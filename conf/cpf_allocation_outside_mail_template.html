<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="color-scheme" content="light dark" />
    <meta name="supported-color-schemes" content="light dark" />
    <title>Document</title>
    <style>
      header {
        position: relative;
      }
      header .water-mark {
        position: absolute;
        right: 0;
        bottom: 0;
        width: 112px;
        height: 92px;
      }
      .header-subtitle {
        position: relative;
        z-index: 9;
      }
      @media (prefers-color-scheme: dark) {
        body {
          background: #23232a;
        }
        .wrap-header {
          padding: 50px;
          display: flex;
          justify-content: center;
        }
        header {
          width: 480px;
          background-color: #f6f6f6;
          border-radius: 12px;
          margin-bottom: 35px;
        }

        header .water-mark--light {
          display: none;
        }

        .header-picture {
          color: #000;
          padding: 21px 30px 0 29px;
          display: flex;
          align-items: center;
        }

        .header-picture div {
          font-size: 17px;
        }

        .header-subtitle {
          display: flex;
          justify-content: space-between;
          align-items: center;
          color: #000;
          padding: 6px 30px 26px 35px;
        }
        .header-subtitle :nth-child(1) {
          color: #000;
          font-size: 25px;
          font-weight: 500;
        }
        .header-subtitle :nth-child(2) {
          color: rgba(0, 0, 0, 0.8);
          font-size: 12px;
        }

        section {
          width: 430px;
          margin: auto;
          font-size: 15px;
        }

        .section-title {
          color: #fff;
          margin-bottom: 18px;
          font-weight: 500;
        }

        .section-text {
          color: rgba(255, 255, 255, 0.7);
          margin-bottom: 13px;
        }

        .section-technical {
          color: rgba(255, 255, 255, 0.7);
          margin-bottom: 47px;
          font-weight: 500;
        }

        .section-sincerely {
          color: rgba(255, 255, 255, 0.7);
          margin-bottom: 5px;
        }

        .section-review {
          width: 150px;
          height: 44px;
          background-color: #f6f6f6;
          border-radius: 25px;
          line-height: 44px;
          text-align: center;
          margin: auto;
          color: #000;
          margin-bottom: 50px;
          font-weight: 500;
        }

        .section-name {
          font-weight: 600;
          color: rgba(255, 255, 255, 0.7);
        }

        .section-background-color {
          color: #fff;
          font-weight: bold;
        }


        footer {
          width: 480px;
          height: 91px;
          background-color: #3a3a41;
          border-radius: 12px;
          display: flex;
          justify-content: center;
          align-items: center;
          margin-bottom: 18px;
        }

        .footer-hint {
          color: rgba(255, 255, 255, 0.7);
          font-size: 15px;
        }
        .footer-hint-text {
          color: #4e78e3;
          text-decoration:none;
        }

        .hint-text {
          color: rgba(255, 255, 255, 0.5);
          text-align: center;
          font-size: 12px;
        }
        .apple-white {
          padding-right: 6px;
          font-size: 20px;
        }

        .apple-black {
          display: none;
        }
      }

      @media (prefers-color-scheme: light) {
        body {
          background: #fff;
        }
        .wrap-header {
          padding: 50px;
          display: flex;
          justify-content: center;
        }
        header {
          width: 480px;
          background-color: black;
          border-radius: 12px;
          margin-bottom: 35px;
        }

        header .water-mark--dark {
          display: none;
        }

        .header-picture {
          color: #fff;
          padding: 21px 30px 0 29px;
          display: flex;
          align-items: center;
        }

        .header-picture div {
          font-size: 17px;
        }

        .header-subtitle {
          display: flex;
          justify-content: space-between;
          align-items: center;
          color: #fff;
          padding: 6px 30px 26px 35px;
        }
        .header-subtitle :nth-child(1) {
          color: #fff;
          font-size: 25px;
          font-weight: 500;
        }
        .header-subtitle :nth-child(2) {
          color: rgba(255, 255, 255, 0.8);
          font-size: 12px;
        }

        section {
          width: 430px;
          margin: auto;
          font-size: 15px;
        }

        .section-title {
          color: #000;
          margin-bottom: 18px;
          font-weight: 500;
        }

        .section-text {
          color: #666666;
          margin-bottom: 13px;
        }

        .section-technical {
          color: #666666;
          margin-bottom: 47px;
          font-weight: 500;
        }

        .section-sincerely {
          color: #666666;
          margin-bottom: 5px;
        }

        .section-review {
          width: 150px;
          height: 44px;
          background-color: #000;
          border-radius: 25px;
          line-height: 44px;
          text-align: center;
          margin: auto;
          color: #fff;
          margin-bottom: 50px;
          font-weight: 500;
        }

        .section-name {
          font-weight: 500;
          color: #666;
        }

        .section-background-color {
          color: #000;
          font-weight: bold;
        }

        footer {
          width: 480px;
          height: 91px;
          background-color: #f2f2f7;
          border-radius: 12px;
          display: flex;
          justify-content: center;
          align-items: center;
          margin-bottom: 18px;
        }

        .footer-hint {
          color: #8e8e93;
          font-size: 15px;
        }
        .footer-hint-text {
          color: #3948b1;
          text-decoration:none;
        }

        .hint-text {
          color: #d1d1d6;
          text-align: center;
          font-size: 12px;
        }
        .apple-black {
          padding-right: 6px;
          font-size: 20px;
        }

        .apple-white {
          display: none;
        }
      }
      @media screen and (max-width: 480px) {
        .wrap-header {
          padding: 2%;
        }
        header {
          margin: 0 auto 35px auto;
          width: 100%;
        }
        section {
          width: 100%;
        }
        footer {
          width: 100%;
        }
      }
    </style>
  </head>
  <body>
    <div class="wrap-header">
      <div>
        <header>
          <div class="header-picture">
            <div class="apple-white"></div>
            <div class="apple-black"></div>
            <div>Expert</div>
            <img src="data:image/png;base64,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" alt="" class="water-mark water-mark--light">
            <img src="data:image/png;base64,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*******************************+mR6evp87DoM4wrq4VrCAPuqKype7X3gXnBlcnJyLFdQBmrYS1hQTVHhEbjJ0qVL18DmUTdvGZD3kjr2CjHU+OquNX369JmYGvzfqFGjuJJ6hVqwJ4SiFhUuB1vDY0UW/GImOODSmj0UOEfMI+Bsi+qLoaa/bdmy5VDv3r25kjJfzZ7PoDMtav1Vq1atePTRR9O8YcbD86tQHxI4pKhYaxgHk4ZbUlJSEvUBD5eSFQRkv/qHDh364K5duw5wJWWl6vTFhyxFxQrugQsXLtzRqlUrvnJEX/rBjLR2X/2TJk0anJGRkY1V9KoaOGAGAc6IRyAgqahQ0oexQWtzTEyMrJbXIyTmTHokAjYVtWPHjgnr1q3Lw9aPUI+UjDPtVQjYUtQIeNXIwYqndl4lLRfGUxG4Yu2VXuurr76ai1VPXEk9tVq9j+89NRR1zpw5Q/r06TOODDDwwBFgBIFFlq/+SPj/yccu0DhGGORscASysFx0hLhF9YGb7H/A9A1XUq4crCBwHYxMIGbELWoUHKcewT5y7qiKlWrSNx8lEL8FWlOjJRlTiwpXK+kYL+VKqm/lYEX6vWCksaCkxJSgqPVgT2gk6/abWEGR86EZAmTKqDsU9M84zIxqGBV12bJlybCaQQ4EeOAIuAMBUtAxUM5gHLutMUCGVP2SkpJGw5y2tXh+jyOgJQL0ih8O5TxnrxBqUYNhsaS7vYQ8niOgAgL0FZ+BozaUkwK94u0qKZXrjwH+ODgo4AuhCQ0elCBADitIEcni9gkcR3Hk4NgKZazAWVH4P/I2BKMStsFmAAAAAElFTkSuQmCC" alt="" class="water-mark water-mark--dark">
          </div>
          <div class="header-subtitle">
            <div>FAST System</div>
            <div>&TIME&</div>
          </div>
        </header>
        <section class="section">
          &CONTENT&
        </section>
      </div>
    </div>
  </body>
  <script>
    const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");
    function darkModeHandler() {
      if (mediaQuery.matches) {
        console.log("现在是深色模式");
      } else {
        console.log("现在是浅色模式");
      }
    }

    // 判断当前模式
    darkModeHandler();
    // 监听模式变化
    mediaQuery.addListener(darkModeHandler);
  </script>
</html>