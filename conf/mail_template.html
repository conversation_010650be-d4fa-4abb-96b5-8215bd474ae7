<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="color-scheme" content="light dark" />
    <meta name="supported-color-schemes" content="light dark" />
    <title>Document</title>
    <style>
      @media (prefers-color-scheme: dark) {
        body {
          background: #23232a;
        }
        .wrap-header {
          padding: 50px;
          display: flex;
          justify-content: center;
        }
        header {
          width: 480px;
          background-color: #f6f6f6;
          border-radius: 12px;
          margin-bottom: 35px;
        }

        .header-picture {
          color: #000;
          padding: 21px 30px 0 29px;
          display: flex;
          align-items: center;
        }
        .header-picture img {
          display: inline-block;
          width: 28px;
          height: 28px;
        }

        .header-picture div {
          font-size: 17px;
        }

        .header-subtitle {
          display: flex;
          justify-content: space-between;
          align-items: center;
          color: #000;
          padding: 6px 30px 26px 35px;
        }
        .header-subtitle :nth-child(1) {
          color: #000;
          font-size: 25px;
          font-weight: 500;
        }
        .header-subtitle :nth-child(2) {
          color: rgba(0, 0, 0, 0.8);
          font-size: 12px;
        }

        section {
          width: 430px;
          margin: auto;
          font-size: 15px;
        }

        .section-title {
          color: #fff;
          margin-bottom: 18px;
          font-weight: 500;
        }

        .section-text {
          color: rgba(255, 255, 255, 0.7);
          margin-bottom: 13px;
        }

        .section-technical {
          color: rgba(255, 255, 255, 0.7);
          margin-bottom: 47px;
          font-weight: 500;
        }

        .section-sincerely {
          color: rgba(255, 255, 255, 0.7);
          margin-bottom: 5px;
        }

        .section-review {
          width: 150px;
          height: 44px;
          background-color: #f6f6f6;
          border-radius: 25px;
          line-height: 44px;
          text-align: center;
          margin: auto;
          color: #000;
          margin-bottom: 50px;
          font-weight: 500;
        }

        .section-name {
          font-weight: 600;
          color: rgba(255, 255, 255, 0.7);
        }

        .section-background-color {
          color: rgba(255, 255, 255, 0.7);
          font-weight: bold;
        }


        footer {
          width: 480px;
          height: 91px;
          background-color: #3a3a41;
          border-radius: 12px;
          display: flex;
          justify-content: center;
          align-items: center;
          margin-bottom: 18px;
        }

        .footer-hint {
          color: rgba(255, 255, 255, 0.7);
          font-size: 15px;
        }
        .footer-hint-text {
          color: #4e78e3;
          text-decoration:none;
        }

        .hint-text {
          color: rgba(255, 255, 255, 0.5);
          text-align: center;
          font-size: 12px;
        }
        .apple-white {
          padding-right: 6px;
          font-size: 20px;
        }

        .apple-black {
          display: none;
        }
      }

      @media (prefers-color-scheme: light) {
        body {
          background: #fff;
        }
        .wrap-header {
          padding: 50px;
          display: flex;
          justify-content: center;
        }
        header {
          width: 480px;
          background-color: black;
          border-radius: 12px;
          margin-bottom: 35px;
        }

        .header-picture {
          color: #fff;
          padding: 21px 30px 0 29px;
          display: flex;
          align-items: center;
        }
        .header-picture img {
          display: inline-block;
          width: 28px;
          height: 28px;
        }

        .header-picture div {
          font-size: 17px;
        }

        .header-subtitle {
          display: flex;
          justify-content: space-between;
          align-items: center;
          color: #fff;
          padding: 6px 30px 26px 35px;
        }
        .header-subtitle :nth-child(1) {
          color: #fff;
          font-size: 25px;
          font-weight: 500;
        }
        .header-subtitle :nth-child(2) {
          color: rgba(255, 255, 255, 0.8);
          font-size: 12px;
        }

        section {
          width: 430px;
          margin: auto;
          font-size: 15px;
        }

        .section-title {
          color: #000;
          margin-bottom: 18px;
          font-weight: 500;
        }

        .section-text {
          color: #666666;
          margin-bottom: 13px;
        }

        .section-technical {
          color: #666666;
          margin-bottom: 47px;
          font-weight: 500;
        }

        .section-sincerely {
          color: #666666;
          margin-bottom: 5px;
        }

        .section-review {
          width: 150px;
          height: 44px;
          background-color: #000;
          border-radius: 25px;
          line-height: 44px;
          text-align: center;
          margin: auto;
          color: #fff;
          margin-bottom: 50px;
          font-weight: 500;
        }

        .section-name {
          font-weight: 500;
          color: #666;
        }

        .section-background-color {
          color: #666;
          font-weight: bold;
        }

        footer {
          width: 480px;
          height: 91px;
          background-color: #f2f2f7;
          border-radius: 12px;
          display: flex;
          justify-content: center;
          align-items: center;
          margin-bottom: 18px;
        }

        .footer-hint {
          color: #8e8e93;
          font-size: 15px;
        }
        .footer-hint-text {
          color: #3948b1;
          text-decoration:none;
        }

        .hint-text {
          color: #d1d1d6;
          text-align: center;
          font-size: 12px;
        }
        .apple-black {
          padding-right: 6px;
          font-size: 20px;
        }

        .apple-white {
          display: none;
        }
      }
      @media screen and (max-width: 480px) {
        .wrap-header {
          padding: 2%;
        }
        header {
          margin: 0 auto 35px auto;
          width: 100%;
        }
        section {
          width: 100%;
        }
        footer {
          width: 100%;
        }
      }
    </style>
  </head>
  <body>
    <div class="wrap-header">
      <div>
        <header>
          <div class="header-picture">
            <div class="apple-white"></div>
            <div class="apple-black"></div>
            <div>Expert</div>
          </div>
          <div class="header-subtitle">
            <div>Technical Support</div>
            <div>&TIME&</div>
          </div>
        </header>
        <section class="section">
          &CONTENT&
        </section>
        <footer>
          <div class="footer-hint">
            <div style="text-align:center">If you have any questions,</div>
            <div style="text-align:center">
              please contact
              <a href="mailto:<EMAIL>" class="footer-hint-text"><EMAIL></a>
            </div>
          </div>
        </footer>
        <div class="hint-text">
          For internal use only. Sent from Expert Technical Support.
        </div>
      </div>
    </div>
  </body>
  <script>
    const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");
    function darkModeHandler() {
      if (mediaQuery.matches) {
        console.log("现在是深色模式");
      } else {
        console.log("现在是浅色模式");
      }
    }

    // 判断当前模式
    darkModeHandler();
    // 监听模式变化
    mediaQuery.addListener(darkModeHandler);
  </script>
</html>
