[loggers]
keys=root, flask, sqlalchemy, gunicorn, matplotlib, icronjob

[handlers]
keys=consolehandler, filehandler, ihandler

[formatters]
keys=consoleformatter, fileformatter

[logger_root]
level=INFO
handlers=filehandler, consolehandler

[logger_sqlalchemy]
level=ERROR
handlers=filehandler, consolehandler
qualname=sqlalchemy
propagate=0

[logger_flask]
level=INFO
handlers=consolehandler
qualname=flask
propagate=0

[logger_gunicorn]
level=INFO
handlers=filehandler,consolehandler
qualname=gunicorn
propagate=0


[logger_matplotlib]
level=INFO
handlers=filehandler,consolehandler
qualname=matplotlib
propagate=0


[logger_icronjob]
level=INFO
handlers=consolehandler, ihandler
qualname=icronjob
propagate=0


[handler_filehandler]
class=handlers.TimedRotatingFileHandler
level=INFO
formatter=fileformatter
args=('./logs/flask.log', 'H', 10, 20)
#(filename, when='h', interval=1, backupCount=0, encoding=None, delay=False, utc=False)

[handler_ihandler]
class=handlers.TimedRotatingFileHandler
level=INFO
formatter=fileformatter
args=('./logs/sync_task.log', 'H', 10, 20)

[handler_consolehandler]
class=logging.StreamHandler
args=(sys.stdout, )
level=DEBUG
formatter=consoleformatter


[formatter_fileformatter]
format=%(asctime)s %(pathname)s %(filename)s %(funcName)s %(lineno)s %(levelname)s - %(message)s

[formatter_consoleformatter]
format=%(asctime)s %(pathname)s %(filename)s %(funcName)s %(lineno)s %(levelname)s - %(message)s