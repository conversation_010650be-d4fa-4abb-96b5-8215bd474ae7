
RTM_CPF = "CP&F"
RTM_OTHER = "Other"

FAST_TWO = "Fast 2.0"
FAST_MONO_EXT = "FAST Mono Ext."
FAST_MONO_INT = "FAST Mono Int."

INSIDE_TABLE = "inside"
OUTSIDE_TABLE = "outside"

PRODUCT_LIST = [FAST_TWO, FAST_MONO_EXT, FAST_MONO_INT]

STEP_VISIT = "Visit"
STEP_CPF_PLANNING_TEAM = "CP&F Planning Team"
STEP_RTM_PLANNERS = "RTM Planners"
STEP_VIEW_FORECAST_DEMAND_RESULT = "View Forecast & Demand Result"
STEP_ADJUSTMENT_OPTIONAL = "Adjustment (optional)"
STEP_ADJUSTMENT_APPROVAL = "Adjustment Approval"
STEP_SOLD_TO_ALLOCATION_VISIT = "Sold-to allocation  - Visit"
STEP_SOLD_TO_ALLOCATION_PUBLISH_ALLOCATION_PLAN = "Sold-to allocation  - Publish Allocation Plan"
STEP_POS_ALLOCATION_VISIT = "POS allocation  - Visit"
STEP_POS_ALLOCATION_PUBLISH_ALLOCATION_PLAN = "POS allocation  - Publish Allocation Plan"

FAST2_VISIT_STEP = ["Forecast & Demand", "Forecast Actual", "Forecast Comparison", "UB Velocity", "Country MPN Mix",
                    "Demand Comparison", "PO Gap", "PO Delinquent"]

FAST_MONO_EXT_VIEW_FORECAST_DEMAND_RESULT = ["advanced_suggestion"]
FAST_MONO_EXT_ADJUSTMENT = ["advanced_forecastadjustment_submit"]

FAST_MONO_INT_VIEW_FORECAST_DEMAND_RESULT_OUTSIDE = ["advanced_suggestion",
                                             "advanced_forecastadjustment_submit"]
FAST_MONO_INT_VIEW_FORECAST_DEMAND_RESULT_INSIDE = ["advanced_suggestion",
                                             "advanced_forecastapproval_startapproval",
                                             "advanced_forecastapproval_approvalsubmit"]
FAST_MONO_INT_ADJUSTMENT_APPROVAL = ["advanced_forecastapproval_startapproval",
                                     "advanced_forecastapproval_approvalsubmit"]
FAST_MONO_INT_SOLD_TO_ALLOCATION_VISIT = ["allocation_list"]
FAST_MONO_INT_SOLD_TO_ALLOCATION_PUBLISH_ALLOCATION_PLAN = ["allocation_list_operation"]
FAST_MONO_INT_POS_ALLOCATION_VISIT = ["posallocation_list"]
FAST_MONO_INT_POS_ALLOCATION_PUBLISH_ALLOCATION_PLAN = ["posallocation_report_publish"]

STEP_EVENT_MAP = {
    STEP_VISIT+INSIDE_TABLE: FAST2_VISIT_STEP,
    STEP_CPF_PLANNING_TEAM+INSIDE_TABLE: FAST2_VISIT_STEP,
    STEP_RTM_PLANNERS+INSIDE_TABLE: FAST2_VISIT_STEP,
    STEP_VIEW_FORECAST_DEMAND_RESULT+OUTSIDE_TABLE: FAST_MONO_INT_VIEW_FORECAST_DEMAND_RESULT_OUTSIDE,
    STEP_ADJUSTMENT_OPTIONAL+OUTSIDE_TABLE: FAST_MONO_EXT_ADJUSTMENT,
    STEP_VIEW_FORECAST_DEMAND_RESULT+INSIDE_TABLE: FAST_MONO_INT_VIEW_FORECAST_DEMAND_RESULT_INSIDE,
    STEP_ADJUSTMENT_APPROVAL+INSIDE_TABLE: FAST_MONO_INT_ADJUSTMENT_APPROVAL,
    STEP_SOLD_TO_ALLOCATION_VISIT+INSIDE_TABLE: FAST_MONO_INT_SOLD_TO_ALLOCATION_VISIT,
    STEP_SOLD_TO_ALLOCATION_PUBLISH_ALLOCATION_PLAN+INSIDE_TABLE: FAST_MONO_INT_SOLD_TO_ALLOCATION_PUBLISH_ALLOCATION_PLAN,
    STEP_POS_ALLOCATION_VISIT+INSIDE_TABLE: FAST_MONO_INT_POS_ALLOCATION_VISIT,
    STEP_POS_ALLOCATION_PUBLISH_ALLOCATION_PLAN+INSIDE_TABLE: FAST_MONO_INT_POS_ALLOCATION_PUBLISH_ALLOCATION_PLAN
}


APP_EXPERT = "Expert"
APP_FAST = "Fast"


class StepInfo:
    def __init__(self, step, table_info: str = None, app_name: str = None, denominator_step: str = None, rtm: str = None,
                 au: float = None, au_key: float = None, au_conversion: float = None, au_key_conversion: float = None,
                 product_name: str = None):
        self.step = step
        self.rtm = rtm
        self.table_info = table_info
        self.app_name = app_name
        self.denominator_step = denominator_step
        self.au = au
        self.au_key = au_key
        self.au_conversion = au_conversion
        self.au_key_conversion = au_key_conversion
        self.product_name = product_name


def get_step_list() -> list[StepInfo]:
    step_list = [StepInfo(step=STEP_VISIT, table_info=INSIDE_TABLE, app_name=APP_EXPERT, product_name=FAST_TWO),
                 StepInfo(step=STEP_CPF_PLANNING_TEAM, rtm=RTM_CPF, table_info=INSIDE_TABLE, app_name=APP_EXPERT, product_name=FAST_TWO),
                 StepInfo(step=STEP_RTM_PLANNERS, rtm=RTM_OTHER, table_info=INSIDE_TABLE, app_name=APP_EXPERT, product_name=FAST_TWO),
                 StepInfo(step=STEP_VIEW_FORECAST_DEMAND_RESULT, table_info=OUTSIDE_TABLE, app_name=APP_FAST, product_name=FAST_MONO_EXT),
                 StepInfo(step=STEP_ADJUSTMENT_OPTIONAL, table_info=OUTSIDE_TABLE, app_name=APP_FAST, denominator_step=STEP_VIEW_FORECAST_DEMAND_RESULT, product_name=FAST_MONO_EXT),
                 StepInfo(step=STEP_VIEW_FORECAST_DEMAND_RESULT, table_info=INSIDE_TABLE, app_name=APP_EXPERT, product_name=FAST_MONO_INT),
                 StepInfo(step=STEP_ADJUSTMENT_APPROVAL, table_info=INSIDE_TABLE, app_name=APP_EXPERT, denominator_step=STEP_VIEW_FORECAST_DEMAND_RESULT, product_name=FAST_MONO_INT),
                 StepInfo(step=STEP_SOLD_TO_ALLOCATION_VISIT, table_info=INSIDE_TABLE, app_name=APP_EXPERT, product_name=FAST_MONO_INT),
                 StepInfo(step=STEP_SOLD_TO_ALLOCATION_PUBLISH_ALLOCATION_PLAN, table_info=INSIDE_TABLE, app_name=APP_EXPERT, denominator_step=STEP_SOLD_TO_ALLOCATION_VISIT, product_name=FAST_MONO_INT),
                 StepInfo(step=STEP_POS_ALLOCATION_VISIT, table_info=INSIDE_TABLE, app_name=APP_EXPERT, product_name=FAST_MONO_INT),
                 StepInfo(step=STEP_POS_ALLOCATION_PUBLISH_ALLOCATION_PLAN, table_info=INSIDE_TABLE, app_name=APP_EXPERT, denominator_step=STEP_POS_ALLOCATION_VISIT, product_name=FAST_MONO_INT)]
    return step_list
