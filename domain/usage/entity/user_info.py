import base64


class UserInfo:

    def __init__(self, rtm, is_key_user, user_name: str = None, product: str = None, email: str = None, person_id: str = None, uuid: str = None, event_times: int = 0):
        self.person_id = person_id
        self.user_name = user_name
        self.email = email
        self.rtm = rtm
        self.is_key_user = is_key_user
        self.product = product
        self.person_id = person_id
        if uuid:
            self.uuid = uuid
        else:
            self.uuid = self.base64_encode()

        self.event_times = event_times

    def base64_decode(self):
        return base64.b64decode(self.uuid).decode('utf-8')

    def base64_encode(self):
        return base64.b64encode(str(self.person_id).encode('utf-8')).decode('utf-8')

