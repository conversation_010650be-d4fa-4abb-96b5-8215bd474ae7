import copy
from datetime import timed<PERSON><PERSON>, datetime

from data.fiscal_year_week import Fiscal<PERSON>ear<PERSON>eek
from data.mono_user_data import FastMonoUser
from data.mysqls.usage.event_tracking import EventTracking
from data.mysqls.usage.event_tracking_fast import EventTrackingFastDaily
from data.mysqls.usage.event_tracking_internal import EventTrackingInternal
from data.mysqls.usage.system_user_list import SystemUserList
from domain.usage.entity.step_info import get_step_list, StepInfo, INSIDE_TABLE, STEP_EVENT_MAP, PRODUCT_LIST, RTM_CPF, \
    RTM_OTHER, OUTSIDE_TABLE

DAU = "DAU"
WAU = "WAU"
MAU = "MAU"

MEASURE_LIST = [DAU, WAU, MAU]


class WriteEventTrackingInfo:
    def __init__(self, measure, step_list: list[StepInfo] = None,
                 period_start_date: str = None, period_end_date: str = None, fiscal_dt: str = None,
                 begin_day_of_week: str = None, end_day_of_week: str = None, calendar_month: int = None):
        self.period_start_date = period_start_date
        self.period_end_date = period_end_date
        self.step_list = step_list
        self.measure = measure
        self._get_user_list()
        self.fiscal_dt = fiscal_dt
        self.begin_day_of_week = begin_day_of_week
        self.end_day_of_week = end_day_of_week
        self.calendar_month = calendar_month
        if self.measure == DAU:
            self.date_type = 1
            if self.period_start_date is None or self.period_end_date is None or self.fiscal_dt is None or self.calendar_month is None:
                self.period_start_date, self.period_end_date, self.fiscal_dt,  self.calendar_month = get_yesterday_period()
            self.fiscal_week = self._get_fiscal_week_by_date(self.fiscal_dt)
        elif self.measure == WAU:
            self.date_type = 2
            if self.period_start_date is None or self.period_end_date is None or self.begin_day_of_week is None or self.end_day_of_week is None or self.calendar_month is None:
                self.period_start_date, self.period_end_date, self.begin_day_of_week, self.end_day_of_week, self.calendar_month = get_week()
            self.fiscal_week = self._get_fiscal_week_by_date(self.begin_day_of_week)
        elif self.measure == MAU:
            self.date_type = 3
            if self.period_start_date is None or self.period_end_date is None or self.calendar_month is None:
                self.period_start_date, self.period_end_date, self.calendar_month = get_month()

    def _get_user_list(self):
        last_fiscal_week = FiscalYearWeek.get_last_week()
        self.inside_user_list = SystemUserList.query_by_fiscal_week(last_fiscal_week, PRODUCT_LIST)
        self.outside_user_list = FastMonoUser.get_all_user_list()

    def _get_fiscal_week_by_date(self, fiscal_dt):
        fiscal_year_week = FiscalYearWeek.get_fiscal_qtr_week_name_by_date(fiscal_dt)
        return fiscal_year_week

    def do_write(self):
        # 根据step获取对应的event_name list
        self.step_list = get_step_list()
        if self.measure == DAU:
            self._write_au()
        elif self.measure == WAU:
            self._write_au()
        elif self.measure == MAU:
            self._write_mau()

    def _write_au(self):
        inside_user_info_map = {}
        outside_user_info_map = {}
        for step_info in self.step_list:
            if step_info.table_info == INSIDE_TABLE:
                event_names = STEP_EVENT_MAP.get(step_info.step+INSIDE_TABLE)
                # 找到user_list与step_info product_name相同的user_list
                user_list = self.filter_inside_user_list(step_info.product_name, step_info.rtm)

                user_info_list = EventTrackingInternal.query_by_date(start_date=self.period_start_date,
                                                                     end_date=self.period_end_date,
                                                                     event_names=event_names,
                                                                     app_name=step_info.app_name,
                                                                     user_list=user_list)
                inside_user_info_map[step_info.step] = user_info_list
            else:
                event_names = STEP_EVENT_MAP.get(step_info.step + OUTSIDE_TABLE)
                # 找到user_list与step_info product_name相同的user_list
                user_list = self.filter_outside_user_list(step_info.rtm)

                user_info_list = EventTrackingInternal.query_by_date(start_date=self.period_start_date,
                                                                     end_date=self.period_end_date,
                                                                     event_names=event_names,
                                                                     app_name=step_info.app_name,
                                                                     user_list=user_list)
                outside_user_info_map[step_info.step] = user_info_list

        self.calculate_au(inside_user_info_map, outside_user_info_map)
        self.cal_conversion()
        self.write_db()

    def filter_inside_user_list(self, product_name, rtm):
        ret = []
        # 使用深拷贝创建一个新的user_list
        user_list_copy = copy.deepcopy(self.inside_user_list)
        # 重新创建一个user_list，避免修改原有user_list
        for user_info in user_list_copy:
            if user_info.product == product_name:
                if rtm is not None and rtm == RTM_CPF:
                    if user_info.rtm == rtm:
                        ret.append(user_info)
                elif rtm is not None and rtm == RTM_OTHER:
                    if user_info.rtm != RTM_CPF:
                        ret.append(user_info)
                else:
                    ret.append(user_info)
        return ret

    def filter_outside_user_list(self, rtm):
        ret = []
        # 使用深拷贝创建一个新的user_list
        user_list_copy = copy.deepcopy(self.outside_user_list)
        # 重新创建一个user_list，避免修改原有user_list
        for user_info in user_list_copy:
            if rtm is not None and rtm == RTM_CPF:
                if user_info.rtm == rtm:
                    ret.append(user_info)
            elif rtm is not None and rtm == RTM_OTHER:
                if user_info.rtm != RTM_CPF:
                    ret.append(user_info)
            else:
                ret.append(user_info)
        return ret

    def _write_mau(self):
        step_info_list = EventTrackingFastDaily.get_week_data(self.period_start_date, self.period_end_date)
        self.calculate_mau(step_info_list)
        self.cal_conversion()
        self.write_db()

    def calculate_mau(self, step_info_list):
        for step_info in self.step_list:
            au = 0
            au_key = 0
            week = 0
            for item in step_info_list:
                if step_info.step == item.step and step_info.product_name == item.product_name:
                    au += item.au
                    au_key += item.au_key
                    week += 1

            step_info.au = round(au / week, 1)
            # 结果保留一位小数
            step_info.au_key = round(au_key / week, 1)
            print(f"Step: {step_info.step}, AU: {au}, AU Key: {au_key}")

    def calculate_au(self, inside_user_info_map, outside_user_info_map):
        # 计算AU，将user_info_list中的event_times相加
        for step_info in self.step_list:
            if step_info.table_info == INSIDE_TABLE:
                user_info_list = inside_user_info_map.get(step_info.step)
            else:
                user_info_list = outside_user_info_map.get(step_info.step)
            au = 0
            au_key = 0
            for user_info in user_info_list:
                au += 1
                if user_info.is_key_user == 1:
                    au_key += 1
            step_info.au = au
            step_info.au_key = au_key
            print(f"Step: {step_info.step}, AU: {au}, AU Key: {au_key}")

    # 计算conversion
    def cal_conversion(self):
        for step_info in self.step_list:
            denominator_step_au = 0
            denominator_step_au_key = 0
            if step_info.denominator_step:
                for item in self.step_list:
                    if item.step == step_info.denominator_step and item.product_name == step_info.product_name:
                        denominator_step_au = item.au
                        denominator_step_au_key = item.au_key
                        break
                step_info.au_conversion = step_info.au / denominator_step_au if denominator_step_au != 0 else None
                if step_info.au_conversion is not None and step_info.au_conversion > 1:
                    step_info.au_conversion = 1
                step_info.au_key_conversion = step_info.au_key / denominator_step_au_key if denominator_step_au_key != 0 else None
                if step_info.au_key_conversion is not None and step_info.au_key_conversion > 1:
                    step_info.au_key_conversion = 1

    def write_db(self):
        data_list = []
        for step_info in self.step_list:
            data = {}
            if self.measure == DAU:
                data = {
                    'date_type': 1,
                    'fiscal_dt': self.fiscal_dt,
                    'fiscal_week': self.fiscal_week
                }
            elif self.measure == WAU:
                data = {
                    'date_type': 2,
                    'begin_day_of_week': self.begin_day_of_week,
                    'end_day_of_week': self.end_day_of_week,
                    'fiscal_week': self.fiscal_week
                }
            elif self.measure == MAU:
                data = {
                    'date_type': 3
                }
            else:
                print("Invalid measure type")
            data['calendar_month'] = self.calendar_month
            data['product_name'] = step_info.product_name
            data['step'] = step_info.step
            data['au'] = step_info.au
            data['au_conversion'] = step_info.au_conversion
            data['au_key'] = step_info.au_key
            data['au_key_conversion'] = step_info.au_key_conversion
            data_list.append(data)
        EventTrackingFastDaily.remove_data(date_type=self.date_type, fiscal_dt=self.fiscal_dt, begin_day_of_week=self.begin_day_of_week, calendar_month=self.calendar_month)
        EventTrackingFastDaily.bulk_insert(data_list)


def get_yesterday_period():
    today = datetime.today()

    yesterday = today - timedelta(days=1)
    calendar_month = yesterday.month
    # 判断yesterday是否为周日
    if yesterday.weekday() != 6:
        # 获取yesterday所在的last sunday
        recent_sunday = yesterday - timedelta(days=(yesterday.weekday() + 1) % 7)
        calendar_month = recent_sunday.month
    fiscal_dt = yesterday.strftime("%Y-%m-%d")
    start_date = yesterday.strftime("%Y-%m-%d 00:00:00")
    end_date = yesterday.strftime("%Y-%m-%d 23:59:59")
    print(f"Start Date: {start_date}, End Date: {end_date}")

    return start_date, end_date, fiscal_dt, calendar_month


def get_week():
    today = datetime.today()
    if today.weekday() == 6:
        today = datetime.today()

        recent_saturday = today - timedelta(days=(today.weekday() + 2) % 7)
        if recent_saturday == today:
            recent_saturday = today - timedelta(days=7)
        start_date = recent_saturday - timedelta(days=6)
        end_date = recent_saturday
        print(f"Start Date: {start_date}, End Date: {end_date}")
    else:
        end_date = datetime.today()
        start_date = end_date - timedelta(days=(end_date.weekday() + 1) % 7)
    calendar_month = start_date.month
    begin_day_of_week = start_date.strftime("%Y-%m-%d")
    end_day_of_week = end_date.strftime("%Y-%m-%d")
    start_date = start_date.strftime("%Y-%m-%d 00:00:00")
    end_date = end_date.strftime("%Y-%m-%d 23:59:59")
    print(f"Start Date: {start_date}, End Date: {end_date}")

    return start_date, end_date, begin_day_of_week, end_day_of_week, calendar_month


def get_month():
    today = datetime.today()
    # first_day_of_this_month = today.replace(day=1)
    # today = first_day_of_this_month - timedelta(days=1)
    # today = last_day_of_last_month.replace(day=1)
    # # today+3天
    # today = today + timedelta(days=3)

    first_day_of_this_month = today.replace(day=1)
    # 找到first_day_of_this_month的周一和周日
    first_day_monday = first_day_of_this_month - timedelta(days=first_day_of_this_month.weekday())
    first_day_sunday = first_day_of_this_month + timedelta(days=6 - first_day_of_this_month.weekday())

    # 如果first_day_monday不在本月，则找到next周日
    if first_day_monday.month != first_day_of_this_month.month:
        # 判断today是否大于first_monday_of_this_month
        if today <= first_day_sunday:
            start_date, end_date = get_last_month(first_day_of_this_month)
        else:
            start_date = first_day_sunday
            end_date = today
    else:
        if today == first_day_sunday:
            start_date, end_date = get_last_month(first_day_of_this_month)
        else:
            start_date = first_day_sunday
            end_date = today
    calendar_month = start_date.month
    start_date = start_date.strftime("%Y-%m-%d 00:00:00")
    end_date = end_date.strftime("%Y-%m-%d 23:59:59")
    print(f"Start Date: {start_date}, End Date: {end_date}")
    return start_date, end_date, calendar_month


def get_last_month(first_day_of_this_month):
    last_day_of_last_month = first_day_of_this_month - timedelta(days=1)
    first_day_of_last_month = last_day_of_last_month.replace(day=1)
    # 判断first_day_of_last_month是否是周日，如果不是，则找到下周日
    if first_day_of_last_month.weekday() != 6:
        first_day_of_last_month = first_day_of_last_month + timedelta(
            days=6 - first_day_of_last_month.weekday())

    # 判断last_day_of_last_month是否为周六，如果不是，则找到下周六
    if last_day_of_last_month.weekday() != 5:
        last_day_of_last_month = last_day_of_last_month + timedelta(days=5 - last_day_of_last_month.weekday())

    return first_day_of_last_month, last_day_of_last_month
