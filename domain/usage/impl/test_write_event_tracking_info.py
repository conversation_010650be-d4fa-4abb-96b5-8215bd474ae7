from datetime import datetime, timedelta

from domain.usage.impl.write_event_tracking_info import WriteEventTrackingInfo, DAU, WAU, MAU, get_last_month


def test_dau_write():
    # day_list = ['2024-09-19','2024-09-20', '2024-09-21']
    day_list = get_last_26_weeks_everyday_list()
    for item in day_list:
        period_start_date, period_end_date, fiscal_dt,  calendar_month = get_day_date_by_date(item)
        wau = WriteEventTrackingInfo(measure=DAU, period_start_date=period_start_date, period_end_date=period_end_date,
                                     fiscal_dt=fiscal_dt, calendar_month=calendar_month)
        wau.do_write()


def test_wau_write():
    begin_day_list = get_last_26_weeks_sunday_list()
    # begin_day_list = ['2024-09-08']
    for item in begin_day_list:
        begin_day_of_week, end_day_of_week, period_start_date, period_end_date, calendar_month =get_week_date_by_begin_date(item)
        wau = WriteEventTrackingInfo(measure=WAU, period_start_date=period_start_date, period_end_date=period_end_date,
                                     begin_day_of_week=begin_day_of_week, end_day_of_week=end_day_of_week, calendar_month=calendar_month)
        wau.do_write()


# 计算月数据前，需要先计算周数据
def test_mau_write():
    month_list = [9, 10]
    for item in month_list:
        period_start_date, period_end_date, calendar_month = get_month_date_by_calendar_month(item)
        mau = WriteEventTrackingInfo(MAU, period_start_date=period_start_date, period_end_date=period_end_date, calendar_month=calendar_month)
        mau.do_write()


# 刷新当天的周数据，日数据，月数据
def test_write():
    dau = WriteEventTrackingInfo(measure=DAU)
    dau.do_write()
    wau = WriteEventTrackingInfo(measure=WAU)
    wau.do_write()
    mau = WriteEventTrackingInfo(measure=MAU)
    mau.do_write()







def test_get_date():
    # begin_day_of_week, end_day_of_week, period_start_date, period_end_date, calendar_month =get_week_date_by_begin_date("2024-07-28")
    # print(begin_day_of_week)
    # print(end_day_of_week)
    # print(period_start_date)
    # print(period_end_date)
    # print(calendar_month)
    period_start_date, period_end_date, calendar_month = get_month_date_by_calendar_month(6)
    print(period_start_date, period_end_date, calendar_month)


def get_last_26_weeks_sunday_list():
    last_26_weeks_sunday_list = []
    today = datetime.now() - timedelta(days=7)
    for i in range(26):
        sunday = today + timedelta(days=6 - today.weekday())
        today = sunday - timedelta(days=7)

        last_26_weeks_sunday_list.append(sunday.strftime("%Y-%m-%d"))

    return last_26_weeks_sunday_list


def get_last_26_weeks_everyday_list():
    last_26_weeks_everyday_list = []
    today = datetime.now() - timedelta(days=1)
    for i in range(26*7):
        last_26_weeks_everyday_list.append(today.strftime("%Y-%m-%d"))
        today = today - timedelta(days=1)

    return last_26_weeks_everyday_list


def get_day_date_by_date(date):
    today = datetime.strptime(date, "%Y-%m-%d")
    period_start_date = today.strftime("%Y-%m-%d") + " 00:00:00"
    period_end_date = today.strftime("%Y-%m-%d") + " 23:59:59"
    fiscal_dt = today.strftime("%Y-%m-%d")
    calendar_month = today.month
    return period_start_date, period_end_date, fiscal_dt, calendar_month



def get_week_date_by_begin_date(begin_day_of_week):
    begin_day_of_week_date = datetime.strptime(begin_day_of_week, "%Y-%m-%d")
    end_day_of_week_date = begin_day_of_week_date + timedelta(days=6)
    end_day_of_week = end_day_of_week_date.strftime("%Y-%m-%d")
    period_start_date = begin_day_of_week_date.strftime("%Y-%m-%d") + " 00:00:00"
    period_end_date = end_day_of_week_date.strftime("%Y-%m-%d") + " 23:59:59"
    calendar_month = begin_day_of_week_date.month
    return begin_day_of_week, end_day_of_week, period_start_date, period_end_date, calendar_month


def get_month_date_by_calendar_month(calendar_month: int):
    # 根据month获取月初和月末
    today = datetime.now()
    year = today.year
    month = calendar_month
    if month == 12:
        next_year = year + 1
        next_month = 1
    else:
        next_month = month + 1
    start_date = datetime(year, month, 1)
    # 判断start_date是不是周日，如果不是，找到next sunday
    if start_date.weekday() != 6:
        start_date = start_date + timedelta(days=6 - start_date.weekday())
    start_date = start_date.strftime("%Y-%m-%d") + " 00:00:00"
    end_date = datetime(year, next_month, 1) + timedelta(days=-1)
    # 判断end_date是不是周六，如果end_date不是周六，判断其是否是周日，如果是，找到下一个周六，如果不是，找到end_date所在周六
    if end_date.weekday() != 5:
        # 如果end_date是周日，找到下一个周六
        if end_date.weekday() == 6:
            end_date = end_date + timedelta(days=6)
        else:
            # 找到end_date所在的周六
            days_to_next_saturday = 5 - end_date.weekday()
            end_date = end_date + timedelta(days=days_to_next_saturday)

    if end_date > today:
        end_date = today

    end_date = end_date.strftime("%Y-%m-%d") + " 23:59:59"

    return start_date, end_date, calendar_month
