from datetime import datetime
from datetime import timedelta
from util.conf import logger
from data.hitl_verification_result import ScanAssistInfoRepository
from data.databend.invalid_scan.hitl_verification_result import HitlVerificationResult


def sync_data(limit=200000, day=1):
    try:
        logger.info("***********START SYNC DATA FROM DATABEND TO MYSQL *****************")
        yesterday = datetime.now() - timedelta(days=int(day))
        yesterday_start_time = yesterday.replace(hour=0, minute=0, second=0, microsecond=0)
        yesterday_end_time = yesterday.replace(hour=23, minute=59, second=59, microsecond=0)
        yesterday_start_str = yesterday_start_time.strftime('%Y-%m-%d %H:%M:%S')
        yesterday_end_str = yesterday_end_time.strftime('%Y-%m-%d %H:%M:%S')
        ori_count = HitlVerificationResult.count_by_scan_time(yesterday_start_str, yesterday_end_str)

        count = ScanAssistInfoRepository.count_by_scan_time(yesterday_start_str, yesterday_end_str)

        if count >= ori_count:
            logger.info("***********HAVE SYNCED DATA FROM DATABEND TO MYSQL *****************")
            return

        random_id = ScanAssistInfoRepository.get_max_random_id_by_scan_time(yesterday_start_str, yesterday_end_str)

        ori_data_list = HitlVerificationResult.query_data_by_scan_time_and_random_id(
            yesterday_start_str,
            random_id, limit)

        has_result_list = []
        no_result_list = []
        for ori_data in ori_data_list:
            if ori_data['ml_percentage'] is not None:
                has_result_list.append(ori_data)
                continue
            no_result_list.append(ori_data)

        has_result_list = sorted(
            has_result_list,
            key=lambda x: (
                x['scan_time'],
                x['pos_id'],
                x['ml_percentage']
            ),
            reverse=False
        )

        no_result_list = sorted(no_result_list, key=lambda x: (x['scan_time'], x['pos_id']), reverse=False)

        records = [
            {
                "ml_percentage": item['ml_percentage'],
                "rtm": item['rtm'],
                "sub_rtm": item['sub_rtm'],
                "disti_id": item['disti_id'],
                "disti_name": item['disti_name'],
                "reseller_id": item['reseller_id'],
                "reseller_name": item['reseller_name'],
                "pos_id": item['pos_id'],
                "pos_name": item['pos_name'],
                "lob": item['lob'],
                "sub_lob": item['sub_lob'],
                "mpn": item['mpn'],
                "original_image_url": item['original_image_url'],
                "source": item['source'],
                "scan_type": item['scan_type'],
                "scan_time": item['scan_time'],
                "random_id": item['random_id'],
                "final_result": item['final_result'],
                "create_time": datetime.now(),
                "update_time": datetime.now(),
                "inventory_status": item['inventory_status'],
                "error_code": item['error_code'],
                "is_kiosk_marked": item['is_kiosk_marked']
            }
            for item in no_result_list + has_result_list
        ]

        if records:
            ScanAssistInfoRepository.bulk_insert_or_update(records)
        logger.info("***********END SYNC DATA FROM DATABEND TO MYSQL *****************")
        return random_id
    except Exception as e:
        logger.error({"sync data from databend to aurora error": e})
        raise e
