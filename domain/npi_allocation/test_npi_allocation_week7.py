from domain.npi_allocation.dto.npi_allocation_dtos import AllocationPosInfoDto, AllocationSupplyConfigDto
from domain.npi_allocation.npi_allocation_week7 import NpiAllocationWeek7ByPos


def test_npi_allocation_week7():
    # Create AllocationPosInfoDto objects from the first table
    pos_info_dtos = [
        AllocationPosInfoDto(
            platform="Online",
            tier="Pro",
            pos_id="1",
            pos_name="",  # Not in image, using default
            rtm="",  # Not in image, using default
            sub_rtm="",  # Not in image, using default
            soldto_id="",  # Not in image, using default
            soldto_name="",  # Not in image, using default
            allocation_mix=20.0,
            bau_mix=0.125
        ),
        AllocationPosInfoDto(
            platform="Online",
            tier="Pro",
            pos_id="2",
            pos_name="",
            rtm="",
            sub_rtm="",
            soldto_id="",
            soldto_name="",
            allocation_mix=10.0,
            bau_mix=0.075
        ),
        AllocationPosInfoDto(
            platform="Online",
            tier="Pro",
            pos_id="3",
            pos_name="",
            rtm="",
            sub_rtm="",
            soldto_id="",
            soldto_name="",
            allocation_mix=5.0,
            bau_mix=0.05
        ),
        AllocationPosInfoDto(
            platform="Online",
            tier="Pro",
            pos_id="4",
            pos_name="",
            rtm="",
            sub_rtm="",
            soldto_id="",
            soldto_name="",
            allocation_mix=20.0,
            bau_mix=0.0416
        ),
        AllocationPosInfoDto(
            platform="Online",
            tier="Pro",
            pos_id="5",
            pos_name="",
            rtm="",
            sub_rtm="",
            soldto_id="",
            soldto_name="",
            allocation_mix=10.0,
            bau_mix=0.1
        ),
        AllocationPosInfoDto(
            platform="Online",
            tier="Pro",
            pos_id="6",
            pos_name="",
            rtm="",
            sub_rtm="",
            soldto_id="",
            soldto_name="",
            allocation_mix=4.0,
            bau_mix=0.0416
        ),
        AllocationPosInfoDto(
            platform="Online",
            tier="Pro",
            pos_id="7",
            pos_name="",
            rtm="",
            sub_rtm="",
            soldto_id="",
            soldto_name="",
            allocation_mix=14.0,
            bau_mix=0.125
        ),
        AllocationPosInfoDto(
            platform="Online",
            tier="Pro",
            pos_id="8",
            pos_name="",
            rtm="",
            sub_rtm="",
            soldto_id="",
            soldto_name="",
            allocation_mix=2.0,
            bau_mix=0.0375
        ),
        AllocationPosInfoDto(
            platform="Online",
            tier="Pro",
            pos_id="9",
            pos_name="",
            rtm="",
            sub_rtm="",
            soldto_id="",
            soldto_name="",
            allocation_mix=10.0,
            bau_mix=0.1
        ),
        AllocationPosInfoDto(
            platform="Online",
            tier="Pro",
            pos_id="10",
            pos_name="",
            rtm="",
            sub_rtm="",
            soldto_id="",
            soldto_name="",
            allocation_mix=5.0,
            bau_mix=0.05
        )
    ]

    # Create AllocationSupplyConfigDto objects from the second table
    supply_config_dtos = [
        AllocationSupplyConfigDto(
            supply_from="China_Total",
            tier="Pro",
            supply_qty=1600,
            rtm_mix=0.0,  # Not in image, using default
            soldto_id="",  # Not in image, using default
            npi_weekly_so_forecast=800,
            woi_cap_adjustment=1  # Not in image for this row
        ),
        AllocationSupplyConfigDto(
            supply_from="Online",
            tier="Pro",
            supply_qty=1600,
            rtm_mix=0.0,
            soldto_id="",
            npi_weekly_so_forecast=None,
            woi_cap_adjustment=None  # Field is empty in image
        )
    ]

    # Call the allocation function with the hardcoded data
    result = NpiAllocationWeek7ByPos(pos_info_dtos, supply_config_dtos).allocation()

    # You might want to add assertions here to verify the result
    # For example:
    # assert result is not None
    # assert len(result) == expected_length

    return result