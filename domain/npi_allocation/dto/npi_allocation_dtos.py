from dataclasses import dataclass, field
from datetime import datetime
from typing import Optional

from dataclasses_json import dataclass_json


@dataclass
class AllocationPosInfoDto:
    """Pure DTO representing allocation_pos_info table"""
    platform: str
    tier: str
    pos_id: str
    pos_name: str
    rtm: str
    sub_rtm: str
    soldto_id: str
    soldto_name: str
    allocation_mix: float
    bau_mix: float
    create_time: str = None
    version: str = None


@dataclass_json
@dataclass
class AllocationSupplyConfigDto:
    """Pure DTO representing allocation_supply_config table"""
    supply_from: str
    tier: str
    supply_qty: int
    rtm_mix: float
    soldto_id: str
    ly_supply: Optional[int] = None
    ly_npi_mix: Optional[float] = None
    bau_mix: Optional[float] = None
    is_calculated: Optional[int] = None
    rtm: Optional[str] = None
    sub_rtm: Optional[str] = None
    npi_weekly_so_forecast: Optional[int] = None
    woi_cap_adjustment: Optional[float] = None
    extra: Optional[str] = None
    remark: Optional[str] = None
    version: Optional[int] = None
    create_time: Optional[datetime] = None
    update_time: Optional[datetime] = None


@dataclass_json
@dataclass
class SoldToMappingDto:
    rtm: str
    sub_rtm: str
    sold_to_id: str
    sold_to_name: str
    short_name: str

@dataclass
class AllocationMpnSupplyDto:
    mpn: str
    sub_lob: str
    lob: str
    supply: float
    fiscal_week: str
    tier: str
    version: str

@dataclass_json
@dataclass
class DashboardNpiSupplyAllocationDto:
    version: Optional[str] = None
    tier: Optional[str] = None
    rtm: Optional[str] = None
    sub_rtm: Optional[str] = None
    sold_to_id: Optional[str] = None
    sold_to_name: Optional[str] = None
    sold_to_name_abbre: Optional[str] = None
    pos_authorized_cnt: Optional[int] = None
    pos_suspension_cnt: Optional[int] = None
    pos_authorized_minus_suspension: Optional[int] = None
    authorized_pos_suspension_pct: Optional[float] = None
    allocation_mix: Optional[float] = None
    allocation_mix_woi_cap: Optional[float] = None
    allocation_mix_woi_cap_vs_reward: Optional[float] = None
    allocation_mix_ly: Optional[float] = None
    allocation_qty: Optional[int] = None
    allocation_qty_ly: Optional[int] = None
    allocation_yoy: Optional[float] = None
    avg_allocation_per_pos: Optional[float] = None
    npi_weekly_runrate: Optional[float] = None
    woi: Optional[float] = None
    tier_bau_mix: Optional[float] = None
    # 新增 id 字段，不参与初始化，由 __post_init__ 设置
    id: str = field(init=False)
    parent_id: str = field(init=False)

    def __post_init__(self):
        self.id = f"{self.tier}_{self.rtm}_{self.sub_rtm}_{self.sold_to_id}"
        if self.sold_to_id != 'All':
            self.parent_id = f"{self.tier}_{self.rtm}_{self.sub_rtm}_All"
            return
        if self.sub_rtm != 'All':
            self.parent_id = f"{self.tier}_{self.rtm}_All_All"
            return
        self.parent_id = f"All"


@dataclass
class AllocationMpnSupplyDto:
    fiscal_qtr_week_name: str
    region: str
    rtm: str
    sub_rtm: str
    sold_to_id: str
    ops_sub_class: str
    mpn: str
    sub_lob: str
    nand: str
    color: str
    shipment_plan_cw: int
    shipment_plan_cw1: int
    shipment_plan_cw2: int
    create_time: datetime
    update_time: datetime
    current_allocation_week_supply: Optional[int]


@dataclass
class AllocationResultDto:
    id: int
    pos_id: str
    soldto_id: str
    rtm: str
    sub_rtm: str
    supply_from: str
    allocation: float
    tier: str
    version: str
    pos_mix: float
    soldto_supply: int
    publish_status: int
    tier_platform_supply: int
    tier_supply: int
    pos_mix_cap_by_woi: float
    remark: str
    create_time: datetime
    update_time: datetime
