from typing import List
from typing import Optional

# from domain.npi_allocation.allocation_by_mix_with_target import Allocation


class Product:
    def __init__(
            self,
            mpn: Optional[str] = None,
            name: Optional[str] = None,
            color: Optional[str] = None,
            sub_lob: Optional[str] = None,
            tier: Optional[str] = None
    ):
        self.mpn_id = mpn
        self.name = name
        self.color = color
        self.sub_lob = sub_lob
        self.tier = tier


class AllocationInput:
    def __init__(self, source_type: str, product: Product, qty: float):
        self.source = source_type
        self.product = product
        self.qty = qty


class Candidate:
    def __init__(self, id: str, mix_percentage: float, extra: str):
        self.id: str = id
        self.extra = extra  # 门店额外信息，分配后方便落表
        self.mix_percentage: float = mix_percentage
        self.target: float = 0
        self.final_allocation: float = 0
        self.adjusted_mix_percentage: float = 0
        self.stop_flag = False
        self.allocation_record: list[AllocationInput] = []
        self.target_record = {}

    def allocate(self, source_type: str, product: Product, qty: float):
        current_allocation = self.get_current_allocation_by_sub_lob(product.sub_lob)
        if current_allocation + qty >= self.target:
            allocated_qty = self.target - current_allocation
            record = AllocationInput(source_type, product, allocated_qty)
            self.allocation_record.append(record)
            self.stop_flag = True
            return allocated_qty
        else:
            record = AllocationInput(source_type, product, qty)
            self.allocation_record.append(record)
            return qty

    def get_current_allocation(self):
        total = 0
        for record in self.allocation_record:
            total += record.qty
        return total

    def get_current_allocation_by_sub_lob(self, sub_lob: str):
        total = 0
        for record in self.allocation_record:
            if record.product.sub_lob == sub_lob:
                total += record.qty
        return total

    def get_allocation_by_mpn(self, mpn: str):
        total = 0
        for record in self.allocation_record:
            if record.product.mpn_id == mpn:
                total += record.qty
        return total

    def is_stop(self):
        return self.stop_flag

    def set_final_allocation(self, final_allocation: float):
        self.final_allocation = final_allocation

    def set_target(self, allocation_source: str, target: float):
        self.target = target
        self.target_record[allocation_source] = target

    def reset_allocation_record(self):
        self.allocation_record = []
