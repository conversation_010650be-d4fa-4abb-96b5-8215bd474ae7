from allocation_by_mix_with_target import Allocation, Candidate
from typing import List

def test_npi_allocation_week7():
    candidates: List[Candidate] = [
        Candidate(pos_id=1, target=300, mix_percentage=0.20, extra=''),
        Candidate(pos_id=2, target=180, mix_percentage=0.10, extra=''),
        Candidate(pos_id=3, target=120, mix_percentage=0.05, extra=''),
        Candidate(pos_id=4, target=100, mix_percentage=0.20, extra=''),
        Candidate(pos_id=5, target=250, mix_percentage=0.10, extra=''),
        Candidate(pos_id=6, target=100, mix_percentage=0.04, extra=''),
        Candidate(pos_id=7, target=300, mix_percentage=0.14, extra=''),
        Candidate(pos_id=8, target=90, mix_percentage=0.02, extra=''),
        Candidate(pos_id=9, target=240, mix_percentage=0.10, extra=''),
        Candidate(pos_id=10, target=120, mix_percentage=0.05, extra=''),
    ]

    total_supply = 1600
    allocator = Allocation(candidates, total_supply)
    final_candidates = allocator.allocate()

    total_allocated = sum(c.current_allocation for c in final_candidates)
    assert round(total_allocated) == round(total_supply), f"Total allocated ({total_allocated}) does not match supply ({total_supply})"

    expected_allocations = {
        1: 299,
        2: 180,
        3: 102,
        4: 100,  # capped
        5: 204,  # capped
        6: 82,
        7: 286,
        8: 41,  # capped
        9: 204,
        10: 102
    }

    for c in final_candidates:
        assert c.current_allocation == expected_allocations[c.pos_id], \
            f"POS {c.pos_id}: expected {expected_allocations[c.pos_id]}, got {c.current_allocation}"
        print(
            f"\n POS {c.pos_id}:\n  Allocated = {c.current_allocation:.2f}\n  Expected = {expected_allocations[c.pos_id]}\n  Target = {c.target:.2f}\n")
