import time
from datetime import datetime
from typing import List
from data.mysqls.npi_allocation.allocation_pos_info_dao import AllocationPosInfoDto
from data.mysqls.npi_allocation.allocation_supply_config_dao import AllocationSupplyConfigDao, AllocationSupplyConfigDto
import json

from domain.npi_allocation.allocation_by_mix_with_target import Allocation
from domain.npi_allocation.candidate import Product, Candidate
from domain.npi_allocation.npi_allocation_utility import NpiAllocationUtility
from util.conf import logger
import concurrent.futures
from functools import partial

ROUNDING_DECIMAL = 5
POS_VERSION = "FY25Q4W6"
JD_VIR_SOLD_TO = '1118511'
JD_SOLD_TO_MIX:dict = {
    "1118510": 0.3,
    "1520447": 0.3,
    "1426316": 0.3,
    "1255322": 0.1,
}


class NpiAllocationWeek7ByPos:
    def __init__(self, pos_infos: List[AllocationPosInfoDto]=None, supply_config: List[AllocationSupplyConfigDto]=None, version=None):
        # supply_from [online/offline/china_total]*[Pro/Consumer]
        self.supply_config = supply_config
        if supply_config is None:
            self.supply_config = AllocationSupplyConfigDao.load_supply_configs()
        self.pos_infos = pos_infos
        if pos_infos is None:
            self.pos_infos:List[AllocationPosInfoDto] = NpiAllocationUtility.load_pos_infos(POS_VERSION)
        self.allocation_result: List = []
        self.now = datetime.now()
        self.version_ts = int(self.now.timestamp()) if version is None else version
        # self.allocation_engine_list: List[Allocation] = self.__build_allocation_list()

    def allocation(self):
        start_total = time.time()

        # Create a list of all allocation functions to run in parallel
        allocation_tasks = [
            partial(self.do_online_offline_allocation, 'Pro', 'Offline'),
            partial(self.do_online_offline_allocation, 'Pro', 'Online'),
            partial(self.do_online_offline_allocation, 'Consumer', 'Offline'),
            partial(self.do_online_offline_allocation, 'Consumer', 'Online'),
            partial(self.do_strategic_allocation, 'Pro'),
            partial(self.do_strategic_allocation, 'Consumer'),
            partial(self.do_vertical_allocation, 'Pro'),
            partial(self.do_vertical_allocation, 'Consumer')
        ]

        # Create thread pool and execute
        with concurrent.futures.ThreadPoolExecutor(max_workers=8) as executor:
            futures = []
            start_times = {}

            # Submit all tasks
            for i, task in enumerate(allocation_tasks):
                start_times[i] = time.time()
                futures.append((i, executor.submit(task)))

            # Process results as they complete
            for future in concurrent.futures.as_completed([f[1] for f in futures]):
                idx = [f[0] for f in futures if f[1] == future][0]
                task_args = allocation_tasks[idx].args
                task_name = task_args[0]  # e.g., "Pro" or "Consumer"
                if not future.result():
                    raise Exception(f"task={task_name} failed")

                # Add "Online/Offline" only if it exists (for do_online_offline_allocation)
                if len(task_args) > 1:
                    task_name += f" {task_args[1]}"  # e.g., "Pro Online"
                elif "strategic" in allocation_tasks[idx].func.__name__:
                    task_name += " Strategic"
                elif "vertical" in allocation_tasks[idx].func.__name__:
                    task_name += " Vertical"

                duration = time.time() - start_times[idx]
                logger.info(f"{task_name} allocation completed in {duration:.2f}s")

        # Save results after all threads complete
        # save_start = time.time()
        # NpiAllocationUtility.save(self.allocation_result)
        # logger.info(f"Save allocation time: {time.time() - save_start:.2f}s")

        logger.info(f"Total allocation time: {time.time() - start_total:.2f}s")

    def do_online_offline_allocation(self, tier: str, platform: str):
        config = NpiAllocationUtility.get_supply_config(self.supply_config, tier, platform)
        if not config:
            return True
        product = Product(tier=tier)
        pos_list = self.get_tier_platform_pos_list(tier, platform)
        tier_supply_qty, china_npi_weekly_so, woi_cap_adjustment, tier_platform_supply = self.get_allocation_data(tier, platform)
        logger.info(f"tier={tier}, platform={platform},"
                    f"tier_supply_qty={tier_supply_qty},"
                    f"china_npi_weekly_so={china_npi_weekly_so},"
                    f"woi_cap_adjustment={woi_cap_adjustment},tier_platform_supply={tier_platform_supply}")
        candidates = self.construct_candidates(tier_supply_qty, china_npi_weekly_so, woi_cap_adjustment, pos_list)
        final_candidates = self.allocate_by_tier_platform(tier_platform_supply, candidates, platform, product)
        allocation_result = NpiAllocationUtility.fill_allocation_result(tier_platform_supply, tier_supply_qty, final_candidates, tier, platform, self.now, self.version_ts)
        NpiAllocationUtility.save(allocation_result)
        return True

    def get_tier_platform_pos_list(self, tier: str, platform: str):
        pos_list = []
        for pos in self.pos_infos:
            if pos.tier == tier and pos.platform == platform:
                pos_list.append(pos)
        return pos_list

    def get_allocation_data(self, tier: str, platform: str):
        china_total_config = NpiAllocationUtility.get_supply_config(self.supply_config, tier, "China_Total")
        tier_platform_config = NpiAllocationUtility.get_supply_config(self.supply_config, tier, platform)
        china_npi_weekly_so = china_total_config.npi_weekly_so_forecast
        woi_cap_adjustment = china_total_config.woi_cap_adjustment
        return china_total_config.supply_qty, china_npi_weekly_so, woi_cap_adjustment, tier_platform_config.supply_qty

    def construct_candidates(self, tier_supply_qty: int, china_npi_weekly_so: int, woi_cap_adjustment: float, pos_list: List[AllocationPosInfoDto]):
        candidates = []
        if china_npi_weekly_so is None or china_npi_weekly_so == 0:
            woi_cap = 0
        else:
            woi_cap = tier_supply_qty / china_npi_weekly_so + woi_cap_adjustment

        for pos in pos_list:
            candidate = Candidate(
                id=pos.pos_id,
                mix_percentage=pos.allocation_mix,
                extra=json.dumps({
                    'platform': pos.platform,
                    'tier': pos.tier,
                    'pos_name': pos.pos_name,
                    'rtm': pos.rtm,
                    'sub_rtm': pos.sub_rtm,
                    'soldto_id': pos.soldto_id,
                    'soldto_name': pos.soldto_name,
                    'bau_mix': pos.bau_mix
                }, ensure_ascii=False)
            )
            candidate.set_target(pos.tier + pos.platform, pos.bau_mix * china_npi_weekly_so * woi_cap)
            candidates.append(candidate)

        return candidates

    def allocate_by_tier_platform(self, supply, candidates: List[Candidate], source_type: str, product: Product):
        allocation_engine = Allocation(
            candidates=candidates
        )
        final_candidates = allocation_engine.allocate(source_type, product, supply)
        return final_candidates


    def do_vertical_allocation(self, tier: str):
        # Get vertical/strategic configs
        vertical_configs = []
        vertical_configs.extend(NpiAllocationUtility.get_strategic_vertical_supply_configs(self.supply_config, tier, "Vertical"))
        tier_total = NpiAllocationUtility.get_supply_config(self.supply_config, tier, "China_Total")
        if tier_total is None:
            return True

        allocation_result = []
        # Create allocation records
        for config in vertical_configs:
            if config.soldto_id != 'Total':
                pos_mix_cap_by_woi = 0
                if tier_total.supply_qty != 0:
                    pos_mix_cap_by_woi = config.supply_qty / tier_total.supply_qty
                allocation_result.append({
                    'version': self.version_ts,
                    'pos_id': 'Total',
                    'soldto_id': config.soldto_id,
                    'rtm': config.rtm,
                    'sub_rtm': config.sub_rtm,
                    'supply_from': config.supply_from,
                    'allocation': config.supply_qty,
                    'tier': config.tier,
                    'create_time': self.now,
                    'update_time': self.now,
                    'pos_mix': 0,
                    'tier_platform_supply': config.supply_qty,
                    'tier_supply': tier_total.supply_qty,
                    'pos_mix_cap_by_woi': pos_mix_cap_by_woi, 'remark': ''
                })
        NpiAllocationUtility.save(allocation_result)
        return True

    def do_strategic_allocation(self, tier: str):
        strategic_sold_to = NpiAllocationUtility.get_strategic_vertical_supply_configs(self.supply_config, tier, 'Strategic')
        if not strategic_sold_to:
            return True

        tier_supply_qty, china_npi_weekly_so, woi_cap_adjustment, tier_platform_supply = self.get_allocation_data(tier,
                                                                                                                  "Strategic")

        allocation_result = []

        # sub_rtm 数据处理
        sub_rtm_configs = NpiAllocationUtility.get_strategic_sub_rtms(self.supply_config, tier,'Strategic')
        for sub_rtm_config in sub_rtm_configs:
            product = Product(tier=tier)
            sub_rtm_supply = sub_rtm_config.supply_qty
            pos_by_sub_rtm = self.get_pos_by_sub_rtm(tier, sub_rtm_config.soldto_id)
            candidates = self.construct_candidates(tier_supply_qty, china_npi_weekly_so, woi_cap_adjustment,
                                                   pos_by_sub_rtm)
            final_candidates = self.allocate_by_tier_platform(sub_rtm_supply, candidates, "Strategic", product)
            allocation_result.extend(
                NpiAllocationUtility.fill_allocation_result(tier_platform_supply, tier_supply_qty, final_candidates,
                                                            tier, 'Strategic', self.now, self.version_ts,sub_rtm_config.remark))

        # 非京东数据处理
        sold_to_without_jd = [sold_to for sold_to in strategic_sold_to if sold_to.soldto_id != JD_VIR_SOLD_TO]
        for sold_to in sold_to_without_jd:
            product = Product(tier=tier)
            sold_to_supply = sold_to.supply_qty
            pos_by_sold_to = self.get_pos_by_sold_to(tier, sold_to.soldto_id)
            candidates = self.construct_candidates(tier_supply_qty, china_npi_weekly_so, woi_cap_adjustment, pos_by_sold_to)
            final_candidates = self.allocate_by_tier_platform(sold_to_supply, candidates, "Strategic", product)
            allocation_result.extend(NpiAllocationUtility.fill_allocation_result(tier_platform_supply, tier_supply_qty, final_candidates, tier, 'Strategic', self.now,self.version_ts,sold_to.remark))

        # jd 数据处理
        jd_sold_to = [sold_to for sold_to in strategic_sold_to if sold_to.soldto_id == JD_VIR_SOLD_TO]
        for sold_to in jd_sold_to:
            # 按照比例拆分为四个sold
            for soldto_id, mix in JD_SOLD_TO_MIX.items():
                pos_mix_cap_by_woi = 0 if tier_supply_qty is None or tier_supply_qty == 0 else (sold_to.supply_qty*mix/tier_supply_qty)
                allocation_result.append({
                    'version': self.version_ts,
                    'pos_id': 'Total',
                    'soldto_id': soldto_id,
                    'rtm': 'Channel Online',
                    'sub_rtm': 'Pure Play',
                    'supply_from': 'Strategic',
                    'allocation': sold_to.supply_qty*mix,
                    'tier': tier,
                    'create_time': self.now,
                    'update_time': self.now,
                    'pos_mix': 0,
                    'tier_platform_supply': tier_platform_supply,
                    'tier_supply': tier_supply_qty,
                    'pos_mix_cap_by_woi': pos_mix_cap_by_woi, 'remark': sold_to.remark
                })
        NpiAllocationUtility.save(allocation_result)
        return True

    def get_pos_by_sold_to(self, tier, sold_to_id):
        result = []
        for pos in self.pos_infos:
            if pos.soldto_id == sold_to_id and pos.tier == tier:
                result.append(pos)
        return result

    def get_pos_by_sub_rtm(self, tier, sub_rtm):
        result = []
        for pos in self.pos_infos:
            if pos.sub_rtm == sub_rtm and pos.tier == tier:
                result.append(pos)

        return result


# class NpiAllocationWeek7BySoldto:
#     def __init__(self, supply_config: List[AllocationSupplyConfigDto]=None):
#         # supply_from [online/offline/china_total]*[Pro/Consumer]
#         self.supply_config = supply_config
#         if supply_config is None:
#             self.supply_config = AllocationSupplyConfigDao.load_supply_configs()
#         # 加载要计算的pos
#         self.pos_infos:List[AllocationPosInfoDto] = NpiAllocationWeek7Utility.load_pos_infos("FY25Q3W7")
#         self.soldto_list: List[Soldto_Info] = self.__build_soldto_list()
#         self.allocation_result: List = []
#
#     def allocation(self):
#         for soldto in self.soldto_list:
#             self.allocation_by_soldto(soldto)
#         NpiAllocationWeek7Utility.save(self.allocation_result)
#
#
#     def allocation_by_soldto(self,soldto: Soldto_Info):
#         # Run allocation algorithm
#         allocation_engine = Allocation(
#             candidates=soldto.soldto_candidates
#         )
#         final_candidates = allocation_engine.allocate(soldto.soldto_allocation)
#
#         # Prepare DB records
#         now = datetime.now()
#         version_ts = int(now.timestamp())
#         for candidate in final_candidates:
#             extra = json.loads(candidate.extra)
#             self.allocation_result.append({
#                 'version': version_ts,
#                 'pos_id': candidate.id,
#                 'soldto_id': soldto.soldto_id,
#                 'rtm': extra.get('rtm'),
#                 'sub_rtm': extra.get('sub_rtm'),
#                 'supply_from': soldto.platform,  # Now using the actual platform
#                 'allocation': candidate.final_allocation,
#                 'tier': soldto.tier,
#                 'create_time': now,
#                 'update_time': now,
#                 'pos_mix': candidate.mix_percentage,
#                 'soldto_supply': soldto.soldto_allocation,
#             })
#
#
#     def __build_soldto_list(self) -> List[Soldto_Info]:
#         result = []
#         soldto_group_map = {}
#         for pos in self.pos_infos:
#             key = (pos.soldto_id, pos.tier, pos.platform)
#             soldto_group_map.setdefault(key, []).append(pos)
#
#         for (soldto_id, tier, platform), group_pos_infos in soldto_group_map.items():
#             # Create Soldto_Info instance with filtered supply configs
#             soldto = Soldto_Info(
#                 soldto_id=soldto_id,
#                 tier=tier,
#                 platform=platform,
#                 pos_infos=group_pos_infos,
#                 npi_weekly_so_forecast=NpiAllocationWeek7Utility.get_supply_config(self.supply_config, tier=tier, platform="china_total").npi_weekly_so_forecast,
#                 total_supply=NpiAllocationWeek7Utility.get_supply_config(self.supply_config, tier=tier, platform=platform).supply_qty,
#             )
#             result.append(soldto)
#         return result
