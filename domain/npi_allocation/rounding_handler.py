# rounding_utils.py
from typing import List, Any, Callable
from decimal import Decimal, ROUND_HALF_UP

from domain.npi_allocation.candidate import Candidate


class RoundingCandidate:
    def __init__(
        self,
        source: Any,
        id_getter: Callable[[Any], str],
        qty_getter: Callable[[Any], float]
    ):
        self.source = source  # Original object (optional to keep)
        self.id = id_getter(source)
        self.ori_qty = qty_getter(source)
        self.adjusted_qty = float(
            Decimal(str(self.ori_qty)).quantize(Decimal('1'), rounding=ROUND_HALF_UP)
        )

    def set_adjusted_qty(self, adjusted_qty: float):
        self.adjusted_qty = adjusted_qty

    def get_qty(self) -> float:
        return self.adjusted_qty

    def set_qty(self, qty: float):
        self.ori_qty = qty
        self.adjusted_qty = qty


class RoundingAllocator:
    def __init__(self, total: float, candidates: List[RoundingCandidate]):
        self.total = int(
            Decimal(str(total)).quantize(Decimal('1'), rounding=ROUND_HALF_UP)
        )
        self.allocated_candidates = candidates

    def allocate(self):
        self.__sort_candidates()
        delta = self.get_total_adjusted_qty() - self.total
        self.__subtract_qty(delta)
        self.__add_qty(delta)
        return self.allocated_candidates

    def __add_qty(self, delta):
        if not self.allocated_candidates:  # Check if empty first
            return
        while delta < 0:
            candidate_list = [candidate for candidate in self.allocated_candidates if candidate.adjusted_qty == 0]
            if len(candidate_list) == 0:
                break
            for candidate in candidate_list:
                if delta == 0:
                    break
                candidate.set_adjusted_qty(candidate.adjusted_qty + 1)
                delta += 1
        while delta < 0:
            for candidate in self.allocated_candidates:
                if delta == 0:
                    break
                candidate.set_adjusted_qty(candidate.adjusted_qty + 1)
                delta += 1

    def __subtract_qty(self, delta):
        if not self.allocated_candidates:  # Check if empty first
            return
        while delta > 0:
            candidate_list = [candidate for candidate in self.allocated_candidates if candidate.adjusted_qty > 1]
            if len(candidate_list) == 0:
                break
            for i in range(len(candidate_list) - 1, -1, -1):
                candidate = candidate_list[i]
                if delta == 0:
                    break
                candidate.set_adjusted_qty(candidate.adjusted_qty - 1)
                delta -= 1
        while delta > 0:
            for i in range(len(self.allocated_candidates) - 1, -1, -1):
                candidate = self.allocated_candidates[i]
                if delta == 0:
                    break
                if candidate.adjusted_qty == 0:
                    continue
                candidate.set_adjusted_qty(candidate.adjusted_qty - 1)
                delta -= 1

    def get_total_adjusted_qty(self):
        total_qty = 0
        for candidate in self.allocated_candidates:
            total_qty += candidate.adjusted_qty
        return total_qty

    def __sort_candidates(self):
        self.allocated_candidates.sort(key=lambda item: item.get_qty(), reverse=False)


