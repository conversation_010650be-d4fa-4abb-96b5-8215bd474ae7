from rounding_handler import RoundingAllocator, RoundingCandidate
from candidate import <PERSON>didate
from typing import List


def test_rounding_allocator():
    # Setup initial candidates with fractional allocations
    candidates: List[Candidate] = [
        Candidate(pos_id=1, target=300, mix_percentage=0.50, extra=''),
        Candidate(pos_id=2, target=240, mix_percentage=0.10, extra=''),
        Candidate(pos_id=3, target=180, mix_percentage=0.05, extra=''),
        Candidate(pos_id=4, target=90, mix_percentage=0.20, extra=''),
        Candidate(pos_id=5, target=150, mix_percentage=0.15, extra=''),
    ]

    # Pretend this is the allocation result before rounding
    allocations = [299.7, 106.3, 52.5, 90.0, 151.5]
    for c, alloc in zip(candidates, allocations):
        c.current_allocation = alloc

    total_original = sum(allocations)
    assert round(total_original) == 700  # sanity check

    # Wrap with RoundingCandidate
    rounding_candidates = [RoundingCandidate(c) for c in candidates]

    # Allocate with rounding logic
    allocator = RoundingAllocator(total=total_original, candidates=rounding_candidates)
    allocator.allocate()

    # Apply adjusted allocations back to source
    for rc in rounding_candidates:
        rc.source.current_allocation = rc.adjusted_qty

    final_allocations = [c.current_allocation for c in candidates]
    expected_allocations = [299, 106, 53, 90, 152]

    assert sum(final_allocations) == round(total_original), \
        f"Total after rounding {sum(final_allocations)} != {round(total_original)}"

    for c in candidates:
        expected = expected_allocations[c.pos_id - 1]
        assert c.current_allocation == expected, f"POS {c.pos_id}: expected {expected}, got {c.current_allocation}"
        print(f"POS {c.pos_id} final allocation: {c.current_allocation}, expected: {expected}")
