from typing import List
from domain.npi_allocation.rounding_handler import RoundingCandidate, RoundingAllocator
from domain.npi_allocation.candidate import Candidate, Product


class Allocation:
    def __init__(self, candidates: List[Candidate]):
        self.candidates: List[Candidate] = candidates


    def allocate(self, source_type: str, product: Product, supply: float) -> List[Candidate]:
        remaining_candidates = self.candidates
        remaining_supply = supply
        # round = 0
        while remaining_supply > 1e-6 and len(remaining_candidates) > 0:
            # round += 1
            # print("Round ", round)
            self.recalculate_mix(remaining_candidates)
            remaining_supply = self.single_round_allocation(remaining_candidates, source_type, product, remaining_supply)
            remaining_candidates = [candidate for candidate in remaining_candidates if not candidate.is_stop()]

        reallocate = self.round_and_reallocate(self.candidates, supply, product)
        return reallocate

    def recalculate_mix(self, candidates: List[Candidate]) -> None:
        total_mix = sum(candidate.mix_percentage for candidate in candidates)
        if total_mix == 0:
            for candidate in candidates:
                candidate.adjusted_mix_percentage = 0
                candidate.stop_flag = True
            return
        for candidate in candidates:
            candidate.adjusted_mix_percentage = candidate.mix_percentage / total_mix
            # print("adjusted mix_percentage", candidate.adjusted_mix_percentage)

    def single_round_allocation(self, candidates: List[Candidate], source_type: str, product: Product, supply: float) -> float:
        next_round_supply = supply
        for candidate in candidates:
            allocation_by_mix = candidate.adjusted_mix_percentage * supply
            allocated_qty = candidate.allocate(source_type, product, allocation_by_mix)
            next_round_supply -= allocated_qty

        return next_round_supply

    @staticmethod
    def round_and_reallocate(candidates: List["Candidate"], original_supply: float, product: Product) -> List["Candidate"]:
        # Define the getters
        id_getter = lambda c: c.id
        qty_getter = lambda c: c.get_allocation_by_mpn(product.mpn_id)

        # Construct decoupled RoundingCandidate instances
        rounding_candidates = [
            RoundingCandidate(source=c, id_getter=id_getter, qty_getter=qty_getter)
            for c in candidates
        ]

        # Run allocation
        allocator = RoundingAllocator(original_supply, rounding_candidates)
        allocator.allocate()

        # Update original candidates with rounded values
        for rc in rounding_candidates:
            rc.source.set_final_allocation(rc.adjusted_qty)

        return [rc.source for rc in rounding_candidates]


