from typing import List
import json

from data.mysqls.npi_allocation.allocation_pos_info_dao import AllocationPosInfoDto
from domain.npi_allocation.candidate import Candidate


class Soldto_Info:
    def __init__(self,
                 soldto_id: int,
                 pos_infos: List[AllocationPosInfoDto],
                 npi_weekly_so_forecast: int,
                 total_supply: float,
                 tier: str,
                 platform: str):
        """
        Initialize with business logic calculations for a soldto.

        Args:
            soldto_id: The soldto identifier
            pos_infos: List of POS DTOs belonging to this soldto
            supply_configs: List of supply configuration DTOs
            platform: The platform filter for supply calculation
        """
        self.soldto_id = soldto_id
        self.tier = tier
        self.platform = platform
        self.pos_infos = pos_infos

        # Calculate derived properties
        self.npi_weekly_so_forecast = npi_weekly_so_forecast
        self.soldto_mix = self._calculate_soldto_mix()
        self.total_supply = total_supply
        self.soldto_allocation = self._calculate_soldto_allocation()
        self.soldto_weekly_so = self._calculate_soldto_weekly_so()
        self.soldto_woi = self._calculate_soldto_woi()
        self.prepare_candidates()

    def _calculate_soldto_mix(self) -> float:
        """Sum of POS allocation_mix values for this soldto"""
        return sum(pos.allocation_mix for pos in self.pos_infos)

    # def _calculate_total_supply(self) -> float:
    #     """Get supply_qty where soldto.tier = tier and platform matches"""
    #     matching_supplies = [
    #         sc.supply_qty for sc in self.supply_configs
    #         if sc.tier == self.tier and sc.platform == self.platform
    #     ]
    #
    #     if not matching_supplies:
    #         raise ValueError(f"No supply found for tier {self.tier} and platform {self.platform}")
    #     if len(matching_supplies) > 1:
    #         raise ValueError(f"Multiple supplies found for tier {self.tier} and platform {self.platform}")
    #
    #     return matching_supplies[0]

    def _calculate_soldto_allocation(self) -> float:
        """Calculate as total_supply * soldto_mix"""
        return self.total_supply * self.soldto_mix

    def _calculate_soldto_weekly_so(self) -> float:
        """Sum of (npi_weekly_so_forecast * pos.bau_mix) for matching tier"""
        return sum(
            self.npi_weekly_so_forecast * pos.bau_mix
            for pos in self.pos_infos
        )

    def _calculate_soldto_woi(self) -> float:
        """Calculate weeks of inventory (soldto_allocation / soldto_weekly_so)"""
        if self.soldto_weekly_so == 0:
            return 0.0
        return self.soldto_allocation / self.soldto_weekly_so

    def prepare_candidates(self) -> None:
        """Prepare candidate instances for this soldto with calculated targets.
        All POS in this soldto share the same tier and thus the same npi_weekly_so_forecast.
        """
        self.soldto_candidates = []

        # Get the single china_total forecast for this soldto's tier
        # china_total_config = next(
        #     (sc for sc in self.supply_configs
        #      if sc.platform == 'china_total' and sc.tier == self.tier),
        #     None
        # )
        #
        # if china_total_config is None:
        #     raise ValueError(f"No china_total supply config found for tier {self.tier}")

        npi_weekly_so_forecast = self.npi_weekly_so_forecast

        for pos in self.pos_infos:
            # Calculate target: woi * (npi_forecast * bau_mix)
            target = (self.soldto_woi + 0.5) * (npi_weekly_so_forecast * pos.bau_mix)

            # Create and add the candidate
            self.soldto_candidates.append(
                Candidate(
                    pos_id=pos.pos_id,
                    target=target,
                    mix_percentage=pos.allocation_mix,
                    extra=json.dumps({
                        'platform': pos.platform,
                        'tier': pos.tier,
                        'pos_name': pos.pos_name,
                        'rtm': pos.rtm,
                        'sub_rtm': pos.sub_rtm,
                        'sold_to_name': pos.sold_to_name,
                        'bau_mix': pos.bau_mix,
                        'create_time': str(pos.create_time) if pos.create_time else None,
                        'update_time': str(pos.update_time) if pos.update_time else None
                    }, ensure_ascii=False)
                )
            )



