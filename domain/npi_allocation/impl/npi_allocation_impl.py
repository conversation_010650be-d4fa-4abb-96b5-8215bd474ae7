import io
from datetime import datetime

from data.mysqls.npi_allocation.allocation_result_dao import AllocationResultDao
from data.mysqls.npi_allocation.allocation_supply_config_dao import AllocationSupplyConfigDao
from data.mysqls.npi_allocation.app_fast_dashboard_npi_supply_allocation_wi_dao import \
    AppFastDashboardNpiSupplyAllocationWiDao
from data.mysqls.npi_allocation.app_fast_npi_cdc_allocation_wi_dao import AppFastNpiCdcAllocationWiDao
from data.mysqls.npi_allocation.app_fast_npi_pos_allocation_wi_dao import AppFastNpiPosAllocationWiDao
from data.mysqls.npi_allocation.eng_allocation_strategic_sold_to_mapping_dao import \
    EngAllocationStrategicSoldToMappingDao
from domain.npi_allocation.dto.npi_allocation_dtos import AllocationSupplyConfigDto, \
    DashboardNpiSupplyAllocationDto, SoldToMappingDto
from util.util import _async


def allocation_supply_config() -> list[AllocationSupplyConfigDto]:
    configs: list[AllocationSupplyConfigDto] = AllocationSupplyConfigDao().load_supply_configs()
    desired_order = ["China_Total", "Online", "Offline", "Vertical", "Strategic"]
    order_mapping = {value: index for index, value in enumerate(desired_order)}
    sorted_configs = sorted(
        configs,
        key=lambda dto: order_mapping.get(dto.supply_from, len(desired_order))
    )
    return sorted_configs


def get_sold_to_mapping() -> list[SoldToMappingDto]:
    return EngAllocationStrategicSoldToMappingDao().find_all()


def get_sold_to_menu(tier) -> list[SoldToMappingDto]:
    return AppFastDashboardNpiSupplyAllocationWiDao().find_distinct_rtm_subrtm_sold_to(tier)


def save_china_total_config_by_max_version(tier, total_china_supply, npi_weekly_so_forecast,woi_cap_adjustment):

    max_version_by_tier = AllocationSupplyConfigDao().get_max_version_by_tier(tier)
    AllocationSupplyConfigDao().update_china_total_config(tier=tier,version=max_version_by_tier,
                                                          forecast=npi_weekly_so_forecast,supply_qty=total_china_supply,
                                                          woi_adjustment=woi_cap_adjustment)
    return


def save_allocation_supply_config(configs: list[AllocationSupplyConfigDto]):
    if not configs:
        return
    now = datetime.now()
    ts = int(now.timestamp())
    for config in configs:
        config.version = ts
        config.create_time = now
        config.update_time = now
    fields_to_insert = ['supply_from', 'tier', 'supply_qty', 'rtm_mix',
                        'ly_supply', 'soldto_id', 'extra', 'remark',
                        'npi_weekly_so_forecast', 'woi_cap_adjustment', 'rtm', 'sub_rtm', 'bau_mix','ly_npi_mix',
                        'is_calculated', 'version', 'create_time', 'update_time']
    dict_configs = [config.to_dict() for config in configs]
    AllocationSupplyConfigDao().batch_insert(objs=dict_configs, fields_to_insert=fields_to_insert)
    return ts


def get_dashboard(tier: str, rtm: str = None, sub_rtm: str = None, sold_to_id: str = None):
    # 约定传上级参数
    # 筛选当前及上级数据
    dtos: list[DashboardNpiSupplyAllocationDto] = AppFastDashboardNpiSupplyAllocationWiDao().find_by_tier(tier)

    menu_result = __get_dashboard_menu(dtos)

    # 过滤掉 EDU & ENT 下的sold to数据
    # dtos = [dto for dto in dtos if dto.rtm!='EDU & ENT' or (dto.rtm=='EDU & ENT' and dto.sold_to_id=='All')]

    def sold_to_sort_key(dto):
        if dto.rtm == 'Carrier':
            # Carrier 专属排序
            return CARRIER_ORDER_INDEX.get(dto.sold_to_name_abbre, len(CARRIER_ORDER))
        else:
            # 非 Carrier 的情况：'All' 最前，其余字母序
            return (0 if dto.sold_to_name_abbre == 'All' else 1, dto.sold_to_name_abbre or '')

    dtos = sorted(
        dtos,
        key=lambda x: (
            RTM_ORDER_INDEX.get(x.rtm, len(RTM_ORDER)),
            SUB_RTM_ORDER_INDEX.get(x.sub_rtm, len(SUB_RTM_ORDER)),
            sold_to_sort_key(x)
        )
    )
    if rtm is not None and rtm.strip() and rtm != 'All':
        dtos = [dto for dto in dtos if dto.rtm == rtm]
    if sub_rtm is not None and sub_rtm.strip() and sub_rtm != 'All':
        dtos = [dto for dto in dtos if dto.sub_rtm == sub_rtm]
    if sold_to_id is not None and sold_to_id.strip() and sold_to_id != 'All':
        dtos = [dto for dto in dtos if dto.sold_to_id == 'All' or dto.sold_to_id == sold_to_id]

    return menu_result, dtos


def __get_dashboard_menu(dtos: list[DashboardNpiSupplyAllocationDto]):
    result = []
    seen = set()

    for dto in dtos:
        if dto.sold_to_id is None or dto.sold_to_id == 'All' or dto.sold_to_id == '':
            continue
        key = (dto.rtm, dto.sub_rtm, dto.sold_to_id)
        if key not in seen:
            seen.add(key)
            result.append(SoldToMappingDto(
                rtm=dto.rtm,
                sub_rtm=dto.sub_rtm,
                sold_to_id=dto.sold_to_id,
                sold_to_name=dto.sold_to_name,
                short_name=dto.sold_to_name_abbre
            ))
    return result

@_async
def delete_allocation_result():
    tier_version_dict: dict[str, list[int]] = AllocationResultDao().get_all_tier_version_dict()
    pro_version_list = tier_version_dict.get("Pro")
    consumer_version_list = tier_version_dict.get("Consumer")
    if pro_version_list is not None and len(pro_version_list) > 2:
        AllocationResultDao().delete_by_version_and_tier(pro_version_list[0], "Pro")
    if consumer_version_list is not None and len(consumer_version_list) > 2:
        AllocationResultDao().delete_by_version_and_tier(consumer_version_list[0], "Consumer")


def get_pos_allocation_mix_file(rtm):
    pos_allocation_data_frame = AppFastNpiPosAllocationWiDao().find_by_rtm(rtm)
    if rtm:
        pos_allocation_data_frame = pos_allocation_data_frame[list(RTM_POS_ALLOCATION_MIX_FILE_COLUMN_MAPPING.values())]
        pos_allocation_data_frame.columns = list(RTM_POS_ALLOCATION_MIX_FILE_COLUMN_MAPPING.keys())
    else:
        pos_allocation_data_frame = pos_allocation_data_frame[list(POS_ALLOCATION_MIX_FILE_COLUMN_MAPPING.values())]
        pos_allocation_data_frame.columns = list(POS_ALLOCATION_MIX_FILE_COLUMN_MAPPING.keys())
    pos_allocation_data_frame['CATEGORY'] = pos_allocation_data_frame['CATEGORY'].replace('Strategic', 'Program')
    pos_allocation_data_frame['Apple ID'] = pos_allocation_data_frame['Apple ID'].replace('Total', '')

    excel_file_bytes = io.BytesIO()
    pos_allocation_data_frame.to_excel(excel_file_bytes, sheet_name='sheet1', index=False)
    excel_file_bytes.seek(0)
    return excel_file_bytes, "POS ALLOCATION MIX RESULT.xlsx"


def get_cdc_allocation_mix_file(rtm):
    cdc_allocation_data_frame = AppFastNpiCdcAllocationWiDao().find_by_rtm(rtm)
    cdc_allocation_data_frame = cdc_allocation_data_frame[list(CDC_ALLOCATION_MIX_FILE_COLUMN_MAPPING.values())]
    cdc_allocation_data_frame.columns = list(CDC_ALLOCATION_MIX_FILE_COLUMN_MAPPING.keys())
    cdc_allocation_data_frame['rtm'] = cdc_allocation_data_frame['rtm'].replace('Strategic', 'Program')
    excel_file_bytes = io.BytesIO()
    cdc_allocation_data_frame.to_excel(excel_file_bytes, sheet_name='sheet1', index=False)
    excel_file_bytes.seek(0)
    return excel_file_bytes, "CDC ALLOCATION MIX RESULT.xlsx"


CARRIER_ORDER = ['CM', 'CT', 'CU', 'CB']
CARRIER_ORDER_INDEX = {val: idx for idx, val in enumerate(CARRIER_ORDER)}
# 排序
RTM_ORDER = ["Grand Total", "Monobrand", "Multibrand", "Carrier", "Channel Online", "EDU & ENT",
             "Online Total","Offline Total", "Vertical", "Program"]
RTM_ORDER_INDEX = {v: i for i, v in enumerate(RTM_ORDER)}

SUB_RTM_ORDER = ["All", "Lifestyle", "Mono", "OTC","Mass Merchant", "Township", "Duty Free",
                 "Pure Play", "Content", "Market Place", "Vertical",
                 "China Offline", "China Online","Carrier Offline", "Carrier Online",
                 "JD Self-run", "Douyin pop","Kuaishou","WeChat","Others","Red","NetEase","Douyin self-run",
                 "Live-streaming", "SN/MN", "Marketplace", "Banking", "Vertical"]
SUB_RTM_ORDER_INDEX = {v: i for i, v in enumerate(SUB_RTM_ORDER)}

POS_ALLOCATION_MIX_FILE_COLUMN_MAPPING = {
    "CATEGORY": "supply_from",
    "RTM": "rtm",
    "Sub-RTM": "sub_rtm",
    "Sold-to ID": "sold_to_id",
    "Sold-to Name": "sold_to_name",
    "Apple ID": "apple_id",
    "POS Name": "pos_name",
    "LOB": "lob",
    "Tier": "tier",
    "Allocation Mix": "allocation_mix_woi_cap",
    "Ship-to ID": "ship_to_id",
    "Update Date": "update_time",
    "Remark": "remark",
}

RTM_POS_ALLOCATION_MIX_FILE_COLUMN_MAPPING = {
    "CATEGORY": "supply_from",
    "RTM": "rtm",
    "Sub-RTM": "sub_rtm",
    "Sold-to ID": "sold_to_id",
    "Sold-to Name": "sold_to_name",
    "Apple ID": "apple_id",
    "POS Name": "pos_name",
    "LOB": "lob",
    "Tier": "tier",
    "Allocation Mix": "allocation_mix_for_rtm",
    "Ship-to ID": "ship_to_id",
    "Update Date": "update_time",
    "Remark": "remark",
}

CDC_ALLOCATION_MIX_FILE_COLUMN_MAPPING = {
    "rtm": "rtm",
    "sub_rtm": "sub_rtm",
    "hq_id": "reseller_id",
    "apple_id": "apple_id",
    "province": "province",
    "city": "city",
    "cdc_city": "cdc_city",
    "ship_to_id": "ship_to_id",
    "ship_to_name": "ship_to_name",
    "ship_to_pos_mix": "ship_to_pos_mix",
    "ship_to_pos_qty": "ship_to_pos_qty",
    "logistics_vendor": "logistics_vendor",
    "pickup_time": "pickup_time",
    "eta": "eta",
}
