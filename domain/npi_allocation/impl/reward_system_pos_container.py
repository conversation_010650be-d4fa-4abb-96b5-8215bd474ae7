from data.mysqls.npi_allocation.allocation_pos_info_dao import AllocationPosInfoDao
from domain.npi_allocation.dto.npi_allocation_dtos import AllocationPosInfoDto
from kit.container.global_var_container import GlobalVarContainer


def loader() -> list[AllocationPosInfoDto] :
    pos_list: list[AllocationPosInfoDto] = AllocationPosInfoDao.find_all()
    return pos_list

# 2min
reward_system_pos_list_container = GlobalVarContainer(loader, 60 * 2)
reward_system_pos_list_container.run()

