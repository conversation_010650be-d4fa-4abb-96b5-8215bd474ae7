from data.mysqls.npi_allocation.allocation_pos_info_dao import AllocationPosInfoDao
from data.mysqls.npi_allocation.allocation_result_dao import AllocationResultDao
from domain.npi_allocation.dto.npi_allocation_dtos import AllocationPosInfoDto, AllocationResultDto
from kit.container.global_var_container import GlobalVarContainer


def loader() -> list[AllocationResultDto] :
    pos_list: list[AllocationResultDto] = AllocationResultDao.load_all()
    return pos_list

# 2min
week7_allocation_result_container = GlobalVarContainer(loader, 60 * 2)
week7_allocation_result_container.run()

