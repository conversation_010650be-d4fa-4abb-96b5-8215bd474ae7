from collections import defaultdict
from datetime import datetime
from typing import List, Dict

from data.mysqls.npi_allocation.allocation_mpn_supply_dao import AllocationMpnSupplyDao
# from data.mysqls.npi_allocation.allocation_pos_info_week11_dao import PosInfoWeek11Dto
# from data.mysqls.npi_allocation.allocation_pos_info_dao import AllocationPosInfoDao
from data.mysqls.npi_allocation.allocation_supply_config_dao import AllocationSupplyConfigDao
from domain.npi_allocation.dto.npi_allocation_dtos import AllocationSupplyConfigDto, AllocationMpnSupplyDto, \
    AllocationPosInfoDto, AllocationResultDto
from allocation_by_mix_with_target import Allocation
import json
from domain.npi_allocation.npi_allocation_utility import NpiAllocationUtility
from candidate import Candidate, Product
from data.mysqls.npi_allocation.allocation_result_week11_dao import AllocationResultWeek11Dao
POS_VERSION = "FY25Q3W11"
CONSUMER_TIER = ['iPhone 16', 'iPhone 16 Plus']
PRO_TIER = ['iPhone 16 Pro', 'iPhone 16 Pro Max']

class NpiAllocationWeek11:
    def __init__(self, mpn_supply: List[AllocationMpnSupplyDto]=None, version=None):

        self.mpn_supply: List[AllocationMpnSupplyDto] = mpn_supply
        if mpn_supply is None:
            self.mpn_supply = NpiAllocationUtility.load_mpn_supplies()
        # Sort pos_infos by allocation_mix (descending order)
        self.pos_infos: List[AllocationResultDto] = NpiAllocationUtility.load_week7_allocation_result_pos_infos()
        self.pos_infos.sort(key=lambda x: x.pos_mix, reverse=True)

        self.allocation_result: List = []
        self.now = datetime.now()
        self.version = int(self.now.timestamp()) if version is None else version
        self.sub_lob_list = NpiAllocationUtility.get_sub_lob_list(self.mpn_supply)
        self.allocation_engines_by_sold_to_pro = self.construct_candidates_by_sold_to_tier('Pro')
        self.allocation_engines_by_sold_to_consumer = self.construct_candidates_by_sold_to_tier('Consumer')

    def allocate(self):
        self.allocate_by_shipment_plan_week('shipment_plan_cw')
        self.allocate_by_shipment_plan_week('shipment_plan_cw1')
        self.allocate_by_shipment_plan_week('shipment_plan_cw2')

        # AllocationResultWeek11Dao.bulk_save(self.allocation_result)

    def allocate_by_shipment_plan_week(self, shipment_plan_week: str):
        NpiAllocationUtility.set_mpn_current_allocation_week_supply(self.mpn_supply, shipment_plan_week)
        self.reset_allocation_record()

        # 按照sub_lob循环，拿到sub_lob下mpn总和
        for sub_lob in self.sub_lob_list:
            tier = 'Pro' if sub_lob in PRO_TIER else 'Consumer'
            total_supply = NpiAllocationUtility.get_total_supply_by_sub_lob(sub_lob, self.mpn_supply)
            if tier == 'Consumer':
                current_allocation_engine = self.allocation_engines_by_sold_to_consumer
            else:
                current_allocation_engine = self.allocation_engines_by_sold_to_pro

            # 再按照soldto循环，拿到soldto占sub_lob下mpn总和的mix，给soldto下pos分配target
            for sold_to_id, allocation_engine in current_allocation_engine.items():
                allocation_result = []
                mpns_by_sub_lob_soldto = NpiAllocationUtility.get_mpn_by_sub_lob_soldto_and_sort(self.mpn_supply, sub_lob, sold_to_id)
                # soldto_supply = NpiAllocationUtility.get_soldto_supply_by_id(mpns_by_sub_lob, sold_to_id)
                NpiAllocationUtility.set_targets(allocation_engine, sub_lob, total_supply)
                # 分mpn
                for mpn in mpns_by_sub_lob_soldto:
                    product = Product(mpn=mpn.mpn, sub_lob=sub_lob)
                    sold_to_mpn_supply = mpn.current_allocation_week_supply
                    allocation_engine.allocate("All", product, sold_to_mpn_supply)

                    allocation_result.extend(
                        self.fill_allocation_result(mpn.mpn, sold_to_mpn_supply, shipment_plan_week, allocation_engine.candidates))
                if len(allocation_result) > 0:
                    AllocationResultWeek11Dao.bulk_save(allocation_result)

    def reset_allocation_record(self, ):
        for sold_to_id, allocation_engine in self.allocation_engines_by_sold_to_pro.items():
            for candidate in allocation_engine.candidates:
                candidate.reset_allocation_record()
        for sold_to_id, allocation_engine in self.allocation_engines_by_sold_to_consumer.items():
            for candidate in allocation_engine.candidates:
                candidate.reset_allocation_record()

    def construct_candidates_by_sold_to_tier(self, tier: str) -> Dict[str, Allocation]:
        sold_to_groups = defaultdict(list)

        for pos in self.pos_infos:
            if pos.tier == tier and pos.pos_id != 'Total':
                candidate = Candidate(
                    id=str(pos.pos_id),
                    mix_percentage=pos.pos_mix,
                    extra=json.dumps({
                        'platform': pos.supply_from,
                        'tier': pos.tier,
                        'rtm': pos.rtm,
                        'sub_rtm': pos.sub_rtm,
                        'soldto_id': pos.soldto_id,
                    }, ensure_ascii=False)
                )
                sold_to_groups[pos.soldto_id].append(candidate)

        # Create Allocation instances for each soldto_id group
        return {
            soldto_id: Allocation(candidates)
            for soldto_id, candidates in sold_to_groups.items()
        }

    def fill_allocation_result(self, mpn: str, total_mpn_supply: float, shipment_cw: str, candidates: List[Candidate]):
        if shipment_cw == 'shipment_plan_cw':
            cw = 'cw'
        elif shipment_cw == 'shipment_plan_cw1':
            cw = 'cw1'
        else:
            cw = 'cw2'
        allocation_result = []
        for candidate in candidates:
            extra = json.loads(candidate.extra)
            allocation_result.append({
                'version': self.version,
                'pos_id': candidate.id,
                'soldto_id': extra.get('soldto_id'),
                'fiscal_week': 'FY25Q4W3',
                'rtm': extra.get('rtm'),
                'sub_rtm': extra.get('sub_rtm'),
                'mpn': mpn,
                'allocation': candidate.final_allocation,
                'tier': extra.get('tier'),
                'cw': cw,
                'create_time': self.now,
                'update_time': self.now,
                'total_mpn_supply': total_mpn_supply,
                'pos_mix_cap_by_woi': candidate.mix_percentage,
            })
        return allocation_result
