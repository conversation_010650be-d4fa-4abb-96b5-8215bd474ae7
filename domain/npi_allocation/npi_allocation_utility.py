import json
from decimal import Decimal, ROUND_HALF_UP
from typing import List, Set

from data.mysqls.npi_allocation.allocation_mpn_supply_dao import AllocationMpnSupplyDao
from data.mysqls.npi_allocation.allocation_pos_info_week11_dao import PosInfoWeek11Dao
# from data.mysqls.npi_allocation.allocation_mpn_supply_dao import AllocationMpnSupplyDao
# from data.mysqls.npi_allocation.allocation_pos_info_dao import AllocationPosInfoDao
from data.mysqls.npi_allocation.allocation_result_dao import AllocationResultDao
from data.mysqls.npi_allocation.allocation_supply_config_dao import AllocationSupplyConfigDao
from domain.npi_allocation.allocation_by_mix_with_target import Allocation
from domain.npi_allocation.candidate import Candidate
from domain.npi_allocation.dto.npi_allocation_dtos import AllocationPosInfoDto, AllocationSupplyConfigDto, \
    AllocationMpnSupplyDto, AllocationResultDto
from domain.npi_allocation.impl.reward_system_pos_container import reward_system_pos_list_container
from domain.npi_allocation.impl.week7_allocation_result_container import week7_allocation_result_container
from util.conf import logger

SUB_RTMS = {
    'China Unicom', 'China Telecom', 'China Mobile', 'China Broadcast',
    'Lifestyle', 'Mono AAR+',
    'OTC', 'Township', 'Duty Free', 'Mass Merchant', 'Pure Play', 'Vertical', 'Content', 'Marketplace',
    'Enterprise', 'Education',
}

class NpiAllocationUtility:
    @staticmethod
    def load_pos_infos(version: str) -> List[AllocationPosInfoDto]:
        """Load all POS information (shared by both strategies)"""
        pos_list: List[AllocationPosInfoDto] = reward_system_pos_list_container.load()
        result = [dto for dto in pos_list if dto.version == version]
        return result

    @staticmethod
    def load_pos_infos_week11():
        return AllocationResultDao.load_all()

    @staticmethod
    def load_mpn_supplies():
        return AllocationMpnSupplyDao.load_all()

    @staticmethod
    def load_supply_configs() -> List[AllocationSupplyConfigDto]:
        """Load all supply configurations (shared by both strategies)"""
        return AllocationSupplyConfigDao.load_supply_configs()

    @staticmethod
    def get_supply_config(supply_configs: List[AllocationSupplyConfigDto],
                          tier: str,
                          platform: str) -> AllocationSupplyConfigDto:
        """Find specific supply config (shared logic)"""
        for config in supply_configs:
            if config.tier == tier and config.supply_from == platform:
                return config
        return None

    @staticmethod
    def get_strategic_vertical_supply_configs(supply_configs: List[AllocationSupplyConfigDto], tier: str,
                                              platform: str):
        result = [config for config in supply_configs if
                  config.supply_from == platform and config.tier == tier and config.soldto_id != "Total"]
        return result

    @staticmethod
    def get_strategic_sub_rtms(supply_configs: List[AllocationSupplyConfigDto], tier: str, platform: str) -> List[AllocationSupplyConfigDto]:
        supply_configs_as_sub_rtm = []
        for supply_config in supply_configs:
            sub_rtm_name = supply_config.soldto_id
            debug1 = sub_rtm_name in SUB_RTMS
            if supply_config.tier == tier and supply_config.supply_from == platform and supply_config.soldto_id in SUB_RTMS:
                supply_configs_as_sub_rtm.append(supply_config)
        return supply_configs_as_sub_rtm


    @staticmethod
    # @take_time
    def save(allocation_result: List):
        # Save all results to DB
        if allocation_result:
            AllocationResultDao.bulk_save(allocation_result)
            logger.info(f"Successfully saved {len(allocation_result)} allocation results")

    @staticmethod
    def rounding(num: float, num_decimals: int) -> float:
        # Convert to string first to avoid float precision issues
        decimal_num = Decimal(str(num))

        # Create quantize string based on desired decimals
        quantize_str = '1.' + '0' * num_decimals if num_decimals > 0 else '1'

        # Perform the rounding and convert to float
        rounded = float(
            decimal_num.quantize(
                Decimal(quantize_str),
                rounding=ROUND_HALF_UP
            )
        )

        return rounded

    @staticmethod
    def get_sub_lob_list(mpn_supplies: List[AllocationMpnSupplyDto]):
        # Using set comprehension for distinct values
        unique_sub_lobs: Set[str] = {item.sub_lob for item in mpn_supplies if item.sub_lob}

        return sorted(unique_sub_lobs)

    @staticmethod
    def get_mpn_by_sub_lob_soldto_and_sort(mpn_supplies: List[AllocationMpnSupplyDto], sub_lob: str, soldto_id: str) -> List[AllocationMpnSupplyDto]:
        filtered_mpns = [
            item for item in mpn_supplies
            if item.sub_lob == sub_lob and item.sold_to_id == soldto_id
        ]

        # Step 2: Sort the filtered MPNs by supply (ascending order)
        filtered_mpns.sort(key=lambda x: x.current_allocation_week_supply, reverse=False)

        return filtered_mpns

    # @staticmethod
    # def get_fiscal_week_list(mpn_supply: List[AllocationMpnSupplyDto]):
    #     if not mpn_supply:
    #         return []
    #
    #     weeks = set()
    #     for item in mpn_supply:
    #         if item.fiscal_week is not None:
    #             weeks.add(item.fiscal_week)

        # return sorted(weeks)

    @staticmethod
    def get_soldto_supply_by_id(mpn_supplies: List[AllocationMpnSupplyDto], soldto_id: str) -> List[AllocationSupplyConfigDto]:
        soldto_mpn_supplies = []
        for mpn_supply in mpn_supplies:
            # Check if this MPN supply belongs to the requested sold-to
            if mpn_supply.sold_to_id == soldto_id:
                soldto_mpn_supplies.append(mpn_supply)

        return soldto_mpn_supplies

    @staticmethod
    def fill_allocation_result(tier_platform_supply: int, tier_supply: int, final_candidates: List[Candidate],
                               tier: str, supply_from: str, timestamp, allocation_version, remark=''):
        allocation_result = []
        for candidate in final_candidates:
            pos_mix_cap_by_woi = 0
            if tier_supply != 0:
                pos_mix_cap_by_woi = candidate.final_allocation / tier_supply
            extra = json.loads(candidate.extra)
            allocation_result.append({
                'version': allocation_version,
                'pos_id': candidate.id,
                'soldto_id': extra.get('soldto_id'),
                'rtm': extra.get('rtm'),
                'sub_rtm': extra.get('sub_rtm'),
                'supply_from': supply_from,
                'allocation': candidate.final_allocation,
                'tier': tier,
                'create_time': timestamp,
                'update_time': timestamp,
                'pos_mix': candidate.mix_percentage,
                'tier_platform_supply': tier_platform_supply,
                'tier_supply': tier_supply,
                'pos_mix_cap_by_woi': pos_mix_cap_by_woi,
                'remark': remark
            })
        return allocation_result

    @staticmethod
    def set_mpn_current_allocation_week_supply(mpns: List[AllocationMpnSupplyDto], shipment_plan_week: str):
        for mpn in mpns:
            if shipment_plan_week == 'shipment_plan_cw':
                mpn.current_allocation_week_supply = mpn.shipment_plan_cw
            elif shipment_plan_week == 'shipment_plan_cw1':
                mpn.current_allocation_week_supply = mpn.shipment_plan_cw1
            elif shipment_plan_week == 'shipment_plan_cw2':
                mpn.current_allocation_week_supply = mpn.shipment_plan_cw2

    @staticmethod
    def get_total_mix_by_sold_to(allocation_engine: Allocation) -> float:
        return sum(candidate.mix_percentage for candidate in allocation_engine.candidates)

    @staticmethod
    def get_total_supply_by_sub_lob(sub_lob: str, mpns: List[AllocationMpnSupplyDto]) -> int:
        return sum(mpn.current_allocation_week_supply for mpn in mpns if mpn.sub_lob == sub_lob)

    @staticmethod
    def set_targets(allocation_engine: Allocation, sub_lob: str, total_supply: int) -> None:
        for candidate in allocation_engine.candidates:
            candidate.set_target(sub_lob, total_supply * candidate.mix_percentage)

    @staticmethod
    def get_pos_mix_by_tier(pos_infos: List[AllocationResultDto], pos_id: str, tier: str) -> float:
        for pos_info in pos_infos:
            if pos_info.pos_id == pos_id and pos_info.tier == tier and (pos_info.supply_from == 'Online' or pos_info.supply_from == 'Offline'):
                return pos_info.pos_mix
        return 0

    @staticmethod
    def load_week7_allocation_result_pos_infos() -> List[AllocationResultDto]:
        """Load all POS information (shared by both strategies)"""
        pos_list: List[AllocationResultDto] = week7_allocation_result_container.load()
        return pos_list