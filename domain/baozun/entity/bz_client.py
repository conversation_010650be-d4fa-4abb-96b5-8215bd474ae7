import os
import time
import uuid
import urllib.request
import http.client
import json
import requests
from util.conf import logger
from util.cpf_util import save_file, create_path
from concurrent.futures import ThreadPoolExecutor

class BzClient:
    CLIENT_ID = 'omo-appledma'
    CLIENT_SECRET = 'jY6vTK2iRXgkDqg0QyNLmCGxuQLcPtBl'
    GRANT_TYPE = 'client_credentials'
    PAYLOAD = f'client_id={CLIENT_ID}&client_secret={CLIENT_SECRET}&grant_type={GRANT_TYPE}'
    HOST = 'open-gateway.baozun.com'
    TOKEN_URL = '/auth/realms/open-gateway/protocol/openid-connect/token'
    IMAGE_URL = '/omowm/api/device/getimg'
    ACCESS_TOKEN = ""
    MAX_WORKERS = 400
    TIMEOUT = 30

    def __init__(self):
        pass

    @classmethod
    def get_access_token(cls):
        conn = http.client.HTTPSConnection(cls.HOST)
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded',
        }
        conn.request("POST", "/auth/realms/open-gateway/protocol/openid-connect/token", cls.PAYLOAD, headers)
        res = conn.getresponse()
        data = res.read()
        res = data.decode("utf-8")
        return json.loads(res)['access_token']

    @classmethod
    def get_image_url(cls, img_url, access_token):
        """
        {
            "code": "0",
            "msg": "Success",
            "data": {
                "signUrl": "https://bztic-pri-file-prod.oss-cn-shanghai.aliyuncs.com/prod/xpos-wm-pro/20241015/147574786/SLJY2TYWKJK%7C2410151833124936145.png?Expires=1729255604&OSSAccessKeyId=LTAI5tF75piKdnZeU8KXqys8&Signature=9DRFqQ3WKIcRzLcBmTgIsWdQD3U%3D"
            },
            "errors": null,
            "errorData": null,
            "message": "Success",
            "success": "true",
            "isSuccess": true
        }
        """
        json_data = {}
        try:
            conn = http.client.HTTPSConnection(cls.HOST, timeout=30)
            payload = json.dumps({"imgUrl": img_url})
            headers = {
                'Auth_Authorization': f'Bearer {access_token}',
                'Auth_ClientId': cls.CLIENT_ID,
                'Content-Type': 'application/json'
            }
            conn.request("POST", cls.IMAGE_URL, payload, headers)
            res = conn.getresponse()
            data = res.read()
            json_data = json.loads(data.decode("utf-8"))
        except Exception as e:
            logger.error({"get image url error": e})
        return json_data