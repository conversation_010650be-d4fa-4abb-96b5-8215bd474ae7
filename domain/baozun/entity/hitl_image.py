import os
import time
import uuid
import json
import requests
from util.conf import logger
from datetime import date
from util.cpf_util import create_path
from util.util import env_dev
from concurrent.futures import ThreadPoolExecutor

class HitlImg:
    HOST = 'open-gateway.baozun.com'
    TIMEOUT = 30
    TOKEN_URL = '/auth/realms/open-gateway/protocol/openid-connect/token'
    IMAGE_URL = '/omowm/api/device/getimg'
    CLIENT_ID = 'omo-appledma'
    GRANT_TYPE = 'client_credentials'
    MAX_WORKERS = 400
    ENV = "-dev" if env_dev() else ""
    EFS_IMG_URL = "https://gcsales.expert{}.apple.com/static/display/fast-lite/hitl/".format(ENV)+"{}"
    ACCESS_TOKEN = ""
    CLIENT_SECRET = 'jY6vTK2iRXgkDqg0QyNLmCGxuQLcPtBl'
    SOURCE_BZ = "BZ"
    SOURCE_GBI = "GBI"
    EFS_SAVE_PATH = "/uploads/hitl/{}"

    @classmethod
    def init_access_token(cls):
        try:
            start = time.time()
            headers = {
                'Content-Type': 'application/x-www-form-urlencoded',
            }
            params = {
                "client_id": cls.CLIENT_ID,
                "client_secret": cls.CLIENT_SECRET,
                "grant_type": cls.GRANT_TYPE,
            }
            host = "https://{}{}".format(cls.HOST, cls.TOKEN_URL)
            response = requests.post(host, data=params, headers=headers, timeout=cls.TIMEOUT)
            resp_json = response.json()
            cls.ACCESS_TOKEN = resp_json.get('access_token', "")
            # cls.execute_time_record("init access token",  start, {"access token": cls.ACCESS_TOKEN})
        except Exception as e:
            logger.error({"init baozun access token error": e})

    @classmethod
    def execute_time_record(cls, msg, start, data=None):
        try:
            logger.info("_____{} during: {}".format(msg, time.time() - start))
            if data:
                logger.info(data)
        except Exception as e:
            logger.error(e)


    @classmethod
    def get_image_sign_url(cls, bz_url: str) -> str:
        ret = ""
        try:
            start = time.time()
            headers = {
                'Auth_Authorization': f'Bearer {cls.ACCESS_TOKEN}',
                'Auth_ClientId': cls.CLIENT_ID,
                'Content-Type': 'application/json'
            }
            params = json.dumps({"imgUrl": bz_url})
            host_url = "https://{}{}".format(cls.HOST, cls.IMAGE_URL)
            response = requests.post(host_url, data=params, headers=headers, timeout=30)
            cls.execute_time_record("get baozun image url", start, {"baozun url response": response})

            url_info = response.json()
            logger.info({"baozun url info": url_info})

            image_url_data = url_info.get("data")
            logger.info({"baozun image url data": image_url_data})
            if type(image_url_data) == dict and "signUrl" in image_url_data:
                ret = image_url_data.get("signUrl")
        except Exception as e:
            print("get image url error: ", e)
        return ret

    @classmethod
    def request_data_by_url(cls, efs_img_url: str):
        response = None
        try:
            start = time.time()
            response = requests.get(efs_img_url, timeout=cls.TIMEOUT)
            cls.execute_time_record("get/download image during", start, {"url": efs_img_url, "response": response})
        except Exception as err:
            logger.error({"check url can access except error": err})
        return response

    @classmethod
    def download_image_by_curl(cls, image_url: str, cookie: str, save_path: str):
        try:
            cmd = ("curl '{}' \
              -H 'accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7' \
              -H 'accept-language: zh-CN,zh;q=0.9' \
              -H 'cache-control: max-age=0' \
              -H 'cookie: {}' \
              -H 'priority: u=0, i' \
              -H 'sec-ch-ua: \"Chromium\";v=\"130\", \"Google Chrome\";v=\"130\", \"Not?A_Brand\";v=\"99\"' \
              -H 'sec-ch-ua-mobile: ?0' \
              -H 'sec-ch-ua-platform: \"macOS\"' \
              -H 'sec-fetch-dest: document' \
              -H 'sec-fetch-mode: navigate' \
              -H 'sec-fetch-site: none' \
              -H 'sec-fetch-user: ?1' \
              -H 'upgrade-insecure-requests: 1' \
              -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36' \
              -o {}".format(image_url, cookie, save_path))
            res = os.popen(cmd)
            res = res.read()
            logger.info({"run curl res": res.splitlines()})
            for line in res.splitlines():
                logger.info(line)
        except Exception as e:
            logger.error({"get GBI image by curl error": e})

    @classmethod
    def hitl_image_process_pool(cls, func, param_list) -> list:
        ret = []
        try:
            if len(param_list) == 0:
                return ret
            
            work_results = []
            # 不要改动worker数量！！！宝尊限流每分钟500个请求，定义最大400个线程防止超限。
            image_work_pool = ThreadPoolExecutor(max_workers=cls.MAX_WORKERS)
            # 将所有任务提交到线程池的任务队列，当某个线程完成当前任务时，会自动取出任务队列中的下一个任务继续执行。
            for param in param_list:
                work_results.append(image_work_pool.submit(func, *param))
            
            for future in work_results:
                item = future.result()
                if item: ret.append(item)
            image_work_pool.shutdown()
        except Exception as e:
            logger.error({"image pool error": e})
        return ret
    
    @classmethod
    def hitl_image_process_pool_gbi(cls, func, param_list, token) -> list:
        ret = []
        try:
            if len(param_list) == 0:
                return ret
            
            work_results = []
            image_work_pool = ThreadPoolExecutor(max_workers=cls.MAX_WORKERS)
            for param in param_list:
                work_results.append(image_work_pool.submit(func, *param, token=token))
            
            for future in work_results:
                item = future.result()
                if item: ret.append(item)
            image_work_pool.shutdown()
        except Exception as e:
            logger.error({"image pool error": e})
        return ret

    @classmethod
    def get_img_efs_save_path(cls, image_sign_url: str, source: str) -> tuple:
        save_file_name, save_file_url = "", ""
        try:
            start = time.time()
            subfix = ""
            if source == cls.SOURCE_BZ:
                filename_str = image_sign_url.split('?')[0].split('/')[-1]
                if filename_str:
                    subfix = filename_str.split('.')[-1]
            if not subfix: subfix = "png"
            filename = "{}.{}".format(str(uuid.uuid4()), subfix)

            today = date.today()
            image_save_path = create_path(cls.EFS_SAVE_PATH.format(today))
            save_file_name = os.path.join(image_save_path, filename)

            efs_image_path = cls.EFS_IMG_URL.format(today)
            save_file_url = os.path.join(efs_image_path, filename)

            # cls.execute_time_record("get save path/url", start, {"save path": save_file_name, "save url": save_file_url})
        except Exception as e:
            logger.error({"get_img_efs_save_path error": e})
        return save_file_name, save_file_url