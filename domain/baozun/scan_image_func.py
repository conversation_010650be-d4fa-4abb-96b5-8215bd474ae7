import os
import time
import uuid
import math
import requests
from util.conf import logger
from datetime import date, datetime, timedelta
from util.cpf_util import create_path
from util.pagerduty import send_alert
from util.send_email import async_send_email_by_database
from util.util import remove_file, env_dev
from domain.baozun.entity.hitl_image import HitlImg
from data.hitl_verification_result import ScanAssistInfoRepository as ScanTable
from domain.hitl.apple_token import get_apple_token


TIMEOUT = 30
OFFSET = 0
SCAN_SIZE = 100
MAX_RETRIES = 3
HitlImg.init_access_token()

def upload_file_to_remote(file_path):
    cmd = "curl --location --request POST 'https://gcdmp-eng.corp.apple.com/fastlite/common/upload?file_path=/uploads/hitl/{}' \
        --header 'User-Agent: Apifox/1.0.0 (https://apifox.com)' \
        --header 'Shield-Ds-emailAddress: baogu<PERSON><EMAIL>' \
        --header 'Shield-Ds-Prsid: 2700965151' \
        --header 'Shield-Ds-lastName: Liu' \
        --header 'Shield-Ds-firstName: Baoguo' \
        --header 'Shield-Ds-nickName: Baoguo' \
        --header 'Shield-Ds-employeeId: 8845402' \
        --header 'Shield-Ds-prsTypeCode: 1' \
        --header 'Accept: */*' \
        --header 'Host: gcdmp-eng.corp.apple.com' \
        --header 'Connection: keep-alive' \
        --header 'Content-Type: multipart/form-data; boundary=--------------------------721687655515137038713592' \
        --header 'Cookie: dslang=US-EN; idmsac-int=4edd4be79da0e3d91c0c905ae1a90680' \
        --form 'file=@\"{}\"'".format(date.today(), file_path)
    retValue = os.system(cmd)
    logger.info({"cmd res: ", retValue})

def get_ck_token() -> str:
    ret = ""
    try:
        file_path = os.path.join(create_path(""), "ck.txt")
        logger.info({"ck file: ", file_path})
        if not os.path.exists(file_path):
            logger.error("cookie file is not exist")
            return ret

        with open(file_path, 'r') as f:
            ck = f.read()
            if not ck:
                logger.error({"get cookie error": "cookie is null"})
                return ret
            ret = ck
    except Exception as e:
        logger.error({"get ck token error": e})

    return ret

def get_gbi_image(url, save_path, token):
    ret = 0
    try:
        image_content = download_each_gbi_image(url, token)
        save_image(image_content, save_path)
        ret = 1
    except Exception as e:
        logger.exception({"original_image_url": url, "get GBI image error": e})
    return ret


def download_each_gbi_image(download_url: str, token: str):
    image_content = None
    try:
        headers = {
            'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'accept-language': 'zh-CN,zh;q=0.9' ,
            'cache-control': 'max-age=0' ,
            'cookie': token ,
            'priority': 'u=0, i' ,
            'sec-ch-ua': '"Chromium";v="130", "Google Chrome";v="130", "Not?A_Brand";v="99"' ,
            'sec-ch-ua-mobile': '?0' ,
            'sec-ch-ua-platform': '"macOS"' ,
            'sec-fetch-dest': 'document' ,
            'sec-fetch-mode': 'navigate' ,
            'sec-fetch-site': 'none' ,
            'sec-fetch-user': '?1' ,
            'upgrade-insecure-requests': '1' ,
            'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36'
        }
        response = requests.get(download_url, headers=headers)
        if not response and response.status_code != 200:
            logger.error({"GBI 请求结果报错": response})
            return image_content

        content_type = response.headers.get("Content-Type")
        if not content_type.startswith("image/"):
            logger.error({"get by gbi is not image": content_type})
            return image_content
        
        image_content = response.content
        return image_content
    except Exception as e:
        logger.error({"download_each_gbi_image error": e})
        raise Exception("get GBI image error")


def save_image(image_content, file_path):
    if not image_content:
        raise Exception("no image content")
    with open(file_path, 'wb') as f:
        f.write(image_content)


def scan_image_data(limit=400, offset=0, source=HitlImg.SOURCE_BZ) -> list:
    """下载图片获取图片efs路径存入数据库表"""

    ret = []
    logger.info("*******************START {} IMG SYNC *******************".format(source))
    bz_start = time.time()
    try:
        start = time.time()
        scan_data = ScanTable.get_scan_list(limit, offset, source)
        logger.info({"******select scan data time": time.time() - start})
        logger.info({"{} scan data: ".format(source): scan_data})

        if source == HitlImg.SOURCE_GBI:
            ck_token = get_ck_token()
            if not ck_token:
                logger.error("*****can not get cookie****")
                return ret

        func = process_bz_image if source == HitlImg.SOURCE_BZ else process_gbi_image
        save_list = HitlImg.hitl_image_process_pool(func, scan_data)

        logger.info({"{} save scan list: ".format(source): save_list})
        if save_list:
            start = time.time()
            err = ScanTable.bulk_update_scan_data(save_list)
            logger.info({"******bulk save {} scan data res".format(source): err, "update scan data during": time.time() - start})
            ret = save_list if not err else err
        logger.info({"*****************{} IMG SYNC END ***********************".format(source): time.time() - bz_start})
    except Exception as e:
        logger.error({"scan image data error": e})
    return ret


def handle_gbi_image_data(limit=400, offset=0, source=HitlImg.SOURCE_GBI) -> list:
    ret = []
    try:
        start_time = time.time()
        # 查询需要更新的GBI数据条数
        total = ScanTable.get_gbi_data_count()
        if total > 0:
            logger.info(f"*******************START SYNC {source} IMAGE*******************")
            # 获取token
            token = get_apple_token()
            pages = math.ceil(total / limit)
            for page in range(0, pages):
                scan_data = ScanTable.get_scan_list(limit, offset, source)
                save_list = HitlImg.hitl_image_process_pool_gbi(process_gbi_image, scan_data, token=token)
                if save_list:
                    start_save_time = time.time()
                    # err = ""
                    err = ScanTable.bulk_update_scan_data(save_list)
                    logger.info({f"******bulk save {source} scan data res": err, "update scan data during": time.time() - start_save_time})
                    ret = save_list if not err else err
            logger.info({f"*****************{source} IMG SYNC END*****************": time.time() - start_time})
    except Exception as e:
        logger.error({"handle gbi image errorr": e})
        raise Exception("handle gbi image error")
    return ret


def process_bz_image(id, retry_times, original_image_url):
    ret = {}
    try:
        scan_id = id
        logger.info({"id": scan_id, "retry_times": retry_times, "original_url": original_image_url})
        if not original_image_url.strip():
            ret = {"id": scan_id, "retry_times": retry_times + 1, "update_time": datetime.now()}
            return ret

        image_sign_url = HitlImg.get_image_sign_url(original_image_url)
        logger.debug({"get bz sign url": image_sign_url})
        if not image_sign_url:
            ret = {"id": scan_id, "retry_times": retry_times + 1, "update_time": datetime.now()}
            return ret

        response = HitlImg.request_data_by_url(image_sign_url)
        if response.status_code != 200:
            logger.error({"can not get bz image by url signUrl", image_sign_url})
            ret = {"id": scan_id, "retry_times": retry_times + 1, "update_time": datetime.now()}
            return ret

        start = time.time()
        save_file_name, save_file_url = HitlImg.get_img_efs_save_path(image_sign_url, HitlImg.SOURCE_BZ)
        if not save_file_name or not save_file_url: return ret
        with open(save_file_name, "wb") as f:
            res = f.write(response.content)
            if not res:
                logger.error({"write file error": save_file_name})
                return ret

        if not os.path.exists(save_file_name):
            logger.error({"write file failed, save file path": save_file_name})
            return ret
        HitlImg.execute_time_record("write baozun image time", start)

        # 本地调试
        # upload_file_to_remote(save_file_name)
        # efs_rsp = HitlImg.request_data_by_url(save_file_url)
        # if efs_rsp.status_code != 200:
        #     logger.error({"check_efs_img_url_access response": efs_rsp, "image url": save_file_url})
        #     return ret

        ret = {"id": scan_id, "efs_image_url": save_file_url, "update_time": datetime.now()}

    except Exception as e:
        logger.error({"process baozun image error": e})
    return ret


def process_gbi_image(scan_id: int, retry_times: int, original_image_url: str, token):
    ret = {}
    save_file_name = ""
    try:
        save_file_name, save_file_url = HitlImg.get_img_efs_save_path(original_image_url, HitlImg.SOURCE_GBI)
        gbi_res = get_gbi_image(original_image_url, save_file_name, token)
        if not gbi_res or not os.path.exists(save_file_name):
            retry_times = retry_times + 1
            ret = {"id": scan_id, "retry_times": retry_times, "update_time": datetime.now()}
            return ret
        # logger.info({"save gbi image path": save_file_name, "update_time": datetime.now()})

        # 本地调试
        # upload_file_to_remote(save_file_name)
        # response = HitlImg.request_data_by_url(save_file_url)
        # if response.status_code != 200:
        #     logger.error({"check_efs_img_url_access code": response.status_code, "image url": save_file_url})
        #     return ret

        ret = {"id": scan_id, "efs_image_url": save_file_url, "update_time": datetime.now()}
    except Exception as e:
        logger.error({"gbi image data error": e})
    finally:
        # logger.info({"-------local": os.environ.get("local")})
        if os.environ.get("local") == "t":
            remove_file(save_file_name)
    return ret


def notify_not_sync_yet():
    """当天如果到了指定的时间还有未搬完的图，需要邮件通知开发人员"""
    # 我们需要搬图的数据是t-1的
    try:
        
        yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
        start_scan_time = f"{yesterday} 00:00:00"
        end_scan_time = f"{yesterday} 23:59:59"
        count = ScanTable.count_not_sync_data(start_scan_time, end_scan_time)
        if count > 0:
            if not env_dev():
                text = f'notify_not_sync_yet_for_hitl\n count: {count} \n date: {yesterday}'
                send_alert(text, 'https://github.pie.apple.com/Apple-GC-Sales-Dev/'
                                 'FAST-Lite-Server/domain/baozun/scan_image_func.py', 'hm')
    except Exception as e:
        logger.error(e)
