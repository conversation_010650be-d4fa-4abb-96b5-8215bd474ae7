import uuid
from datetime import datetime
from typing import Union
import pandas as pd
import xlsxwriter
from enum import Enum

from data.databend.esr.gc_ro_ds_esr_data import GcRoDsEsrData
from data.databend.esr.gc_ro_ds_esr_accy_data import GcRoDsEsrAccyData
from data.databend.cto.cto_pod import CTOPod
from enum import Enum

from kit.pd import fill_nan_to_none
from util.file_util import get_absolute_path


# ESR Mono/online 权限LOB对应的数据库查询的LOB
ESR_MONO_ONLINE_PERMISSION_LOB_TO_DB_LOB_MAPPING = {
    "iPhone": "IPHONE",
    "iPad": "IPAD",
    "CPU": "CPU",
    "Watch": "WATCH",
    "Displays": "DISPLAYS",
    "Music Accessory": "MUS_ACC",
}


CTO_START_TIME = '06:00'
CTO_END_TIME = '11:00'


class AutomaticDataStatus(Enum):
    DELAY = "Delay"
    REFRESH = "Refresh"


class AutomaticDataType(Enum):
    ESR = "ESR"
    CTO = "CTO"


Automatic_DB_NAME = "gc_dmp_data"


class DataRefreshVersion(Enum):
    MORNING = "Morning"
    AFTERNOON = "Afternoon"
    COB = "COB"


class KeySubKeyRelation(Enum):
    # 定义 key
    ESR_MAIN_PRODUCTS = "esr_main_products"
    ESR_ACCESSORIES = "esr_accessories"

    CTO_POD = "cto_pod"
    CTO_EXECUTION = "cto_execution"
    CTO_BACKLOG = "cto_backlog"


# ESR channel 权限
ESR_CHANNEL_PERMISSIONS = {
    "Sales Finance": {
        "regions": None,
        "rtms": None,
        "reports": [KeySubKeyRelation.ESR_MAIN_PRODUCTS.value, KeySubKeyRelation.ESR_ACCESSORIES.value],
        "partition": 0,
    },
    "Ecosystem": {
        "regions": None,
        "rtms": None,
        "reports": [KeySubKeyRelation.ESR_ACCESSORIES.value],
        "partition": 1,
    },
    "CP&F": {
        "regions": None,
        "rtms": None,
        "reports": [KeySubKeyRelation.ESR_MAIN_PRODUCTS.value, KeySubKeyRelation.ESR_ACCESSORIES.value],
        "partition": 2,
    },
    "Mono": {
        "regions": ["China mainland"],
        "rtms": ["Monobrand"],
        "reports": [KeySubKeyRelation.ESR_MAIN_PRODUCTS.value, KeySubKeyRelation.ESR_ACCESSORIES.value],
        "partition": 3,
    },
    "Multi": {
        "regions": ["China mainland"],
        "rtms": ["Multibrand"],
        "reports": [KeySubKeyRelation.ESR_MAIN_PRODUCTS.value, KeySubKeyRelation.ESR_ACCESSORIES.value],
        "partition": 4,
    },
    "Online": {
        "regions": ["China mainland"],
        "rtms": ["Channel Online"],
        "reports": [KeySubKeyRelation.ESR_MAIN_PRODUCTS.value, KeySubKeyRelation.ESR_ACCESSORIES.value],
        "partition": 5,
    },
    "Carrier": {
        "regions": ["China mainland"],
        "rtms": ["Carrier"],
        "reports": [KeySubKeyRelation.ESR_MAIN_PRODUCTS.value, KeySubKeyRelation.ESR_ACCESSORIES.value],
        "partition": 6,
    },
    "Education": {
        "regions": ["China mainland"],
        "rtms": ["Education"],
        "reports": [KeySubKeyRelation.ESR_MAIN_PRODUCTS.value, KeySubKeyRelation.ESR_ACCESSORIES.value],
        "partition": 7,
    },
    "Enterprise": {
        "regions": ["China mainland"],
        "rtms": ["Commercial"],
        "reports": [KeySubKeyRelation.ESR_MAIN_PRODUCTS.value, KeySubKeyRelation.ESR_ACCESSORIES.value],
        "partition": 8,
    },
    "HK/TW RP": {
        "regions": ["Hong Kong", "Taiwan"],
        "rtms": ["Monobrand", "Multibrand", "Channel Online", "Beats Channel", "Others"],
        "reports": [KeySubKeyRelation.ESR_MAIN_PRODUCTS.value, KeySubKeyRelation.ESR_ACCESSORIES.value],
        "partition": 9,
    },
    "HK/TW Carrier": {
        "regions": ["Hong Kong", "Taiwan"],
        "rtms": ["Carrier"],
        "reports": [KeySubKeyRelation.ESR_MAIN_PRODUCTS.value, KeySubKeyRelation.ESR_ACCESSORIES.value],
        "partition": 10,
    },
    "HK/TW E&E": {
        "regions": ["Hong Kong", "Taiwan"],
        "rtms": ["Commercial", "Education"],
        "reports": [KeySubKeyRelation.ESR_MAIN_PRODUCTS.value, KeySubKeyRelation.ESR_ACCESSORIES.value],
        "partition": 11,
    }
}


# CTO channel 权限
CTO_CHANNEL_PERMISSIONS = {
    "CP&F": {
        "regions": None,
        "rtms": None,
        "reports": [KeySubKeyRelation.CTO_POD.value, KeySubKeyRelation.CTO_BACKLOG.value, KeySubKeyRelation.CTO_EXECUTION.value],
        "partition": 1,
    },
    "Mono": {
        "regions": ["China mainland"],
        "rtms": ["Monobrand"],
        "reports": [KeySubKeyRelation.CTO_POD.value, KeySubKeyRelation.CTO_BACKLOG.value, KeySubKeyRelation.CTO_EXECUTION.value],
        "partition": 2,
    },
    "Multi": {
        "regions": ["China mainland"],
        "rtms": ["Multibrand"],
        "reports": [KeySubKeyRelation.CTO_POD.value, KeySubKeyRelation.CTO_BACKLOG.value, KeySubKeyRelation.CTO_EXECUTION.value],
        "partition": 3,
    },
    "Online": {
        "regions": ["China mainland"],
        "rtms": ["Channel Online"],
        "reports": [KeySubKeyRelation.CTO_POD.value, KeySubKeyRelation.CTO_BACKLOG.value, KeySubKeyRelation.CTO_EXECUTION.value],
        "partition": 4,
    },
    "Carrier": {
        "regions": ["China mainland"],
        "rtms": ["Carrier"],
        "reports": [KeySubKeyRelation.CTO_POD.value, KeySubKeyRelation.CTO_BACKLOG.value, KeySubKeyRelation.CTO_EXECUTION.value],
        "partition": 5,
    },
    "Education": {
        "regions": ["China mainland"],
        "rtms": ["Education"],
        "reports": [KeySubKeyRelation.CTO_POD.value, KeySubKeyRelation.CTO_BACKLOG.value, KeySubKeyRelation.CTO_EXECUTION.value],
        "partition": 6,
    },
    "Enterprise": {
        "regions": ["China mainland"],
        "rtms": ["Commercial"],
        "reports": [KeySubKeyRelation.CTO_POD.value, KeySubKeyRelation.CTO_BACKLOG.value, KeySubKeyRelation.CTO_EXECUTION.value],
        "partition": 7,
    },
    "HK/TW RP": {
        "regions": ["Hong Kong", "Taiwan", "Macau"],
        "rtms": ["Monobrand", "Multibrand", "Channel Online", "Beats Channel"],
        "reports": [KeySubKeyRelation.CTO_POD.value, KeySubKeyRelation.CTO_BACKLOG.value, KeySubKeyRelation.CTO_EXECUTION.value],
        "partition": 8,
    },
    "HK/TW Carrier": {
        "regions": ["Hong Kong", "Taiwan", "Macau"],
        "rtms": ["Carrier"],
        "reports": [KeySubKeyRelation.CTO_POD.value, KeySubKeyRelation.CTO_BACKLOG.value, KeySubKeyRelation.CTO_EXECUTION.value],
        "partition": 9,
    },
    "HK/TW E&E": {
        "regions": ["Hong Kong", "Taiwan", "Macau"],
        "rtms": ["Commercial", "Education"],
        "reports": [KeySubKeyRelation.CTO_POD.value, KeySubKeyRelation.CTO_BACKLOG.value, KeySubKeyRelation.CTO_EXECUTION.value],
        "partition": 10,
    }
}

# 分LOB权限下载的rtm清单
ESR_LOB_RTM_LIST = ['Mono', 'Online']

# ESR定制化表头
CUSTOM_REPORT_RTM_LIST = ['Mono', 'Online', 'Multi', 'CP&F']

# ESR mono excel下载列
esr_mono_column_mapping = {
        "RTM": "hq_segment_cd",
        "Sales Org": "alt_hier_level_3_desc",
        "Customer Name": "roc_cust_desc",   # 配件无
        "Customer Sold To": "cust_id",
        "LOB": "lob_id",
        "Model": "sub_lob_id",  # 配件无
        "Project Short Desc": "project_cd", # 配件无
        "Revenue/Demo": "type_desc", # 配件无
        "Apple Part #": "prod_id",
        "Lifecycle": "lifecycle", # 配件无
        "Description": "prod_description", # 配件无
        "QTW Shipment Plan": "qtw_shipment_plan", # 配件无
        "QTD Gross Billings": "qtd_gross_billing", # 配件无
        "CW Shipment Plan (Discrete)": "cw_shipment_plan", # 配件无
        "CW Consumed Qty": "cw_consumed_qty", # 配件无
        "GATP Allocation": "gatp_allocation",
        "Delta SNI": "delta_sni",
        "SNI (CW-1)": "sni_cw_minus_1", # 配件无
        "Last Week Open DN": "last_week_open_dn",
        "Rollover": "rollover",
        "CW+1 Shipment Plan (Discrete)": "shipment_plan_cw1",
        "CW+2 Shipment Plan (Discrete)": "shipment_plan_cw2", # 配件无
        "CW+3 Shipment Plan (Discrete)": "shipment_plan_cw3", # 配件无
        "CW Gross Billing Units": "cw_gross_billing",
        "CW Shipped Units": "cw_shipped_units", # 配件无
        "Shipment Attainment %": "shipment_attainment", # 配件无
        "PO Attainment %": "po_attainment", # 配件无
        "CW Backlog Gap": "cw_backlog_gap",
        "CW Supply Gap": "cw_supply_gap",
        "DNs On Credit Hold": "dns_on_credit_hold",
        "DNs Not On Credit Hold": "dns_not_on_credit_hold",
        "Shipped not Invoiced CW": "sni_qty", # 配件无
        "Total Backlog": "tot_backlog", # 配件无
        "Backlog Gap cum CW+1": "backlog_gap_cum_cw1", # 配件无
        "Shippable Backlog": "shippable_backlog", # 配件无
        "Scheduled Backlog": "scheduled_backlog", # 配件无
        "SIF Actualised": "sif_actualised", # 配件无
        "POD": "pod_landed_qty",
        "EOH": "eoh_qty",
        "ST/UB CW-1": "st_ub_qty_cw_minus1", # 配件无
        "ST/UB CW-2": "st_ub_qty_cw_minus2", # 配件无
        "ST/UB CW-3": "st_ub_qty_cw_minus3", # 配件无
        "ST/UB CW-4": "st_ub_qty_cw_minus4", # 配件无
        "ST/UB CW-5": "st_ub_qty_cw_minus5",  # 配件无
        "ST/UB 5 Wk Bwd Avg": "st_ub_5wk_bwd_avg", # 配件无
        "WOI before allocation": "woi_before_allocatn",  # 配件无
        "WOS after allocation": "wos_after_allocation", # 配件无
        "Open Backlog over Published SP": "open_backlog_over_published_sp", # 配件无
        "Open Backlog > 2Wk old over Published SP": "open_backlog_over_published_sp_gt_2w", # 配件无
        "Open Backlog > 3Wk old over Published SP": "open_backlog_over_published_sp_gt_3w",
        "Open Backlog > 9Wk old over Published SP": "open_backlog_over_published_sp_gt_9w",
        "Open Backlog over Published CW+1 SP": "open_backlog_over_published_sp_cw1", # 配件无
        "Cum CW +2 Shipment Plan": "cum_shipment_plan_cw2", # 配件无
        "Backlog Gap cum CW+2": "backlog_gap_cum_cw2",  # 配件无
        "Backlog Gap cum CW+3": "backlog_gap_cum_cw3",  # 配件无
        "Open Backlog over Published CW+2 SP": "open_backlog_over_published_sp_cw2",  # 配件无
        "Open Backlog over Published CW+3 SP": "open_backlog_over_published_sp_cw3",  # 配件无
        "NAND": "storage_size", # 配件无
        "Color": "color_desc", # 配件无
        "CW-1 Billing Gross (Discrete)": "billing_cw_minus1",
        "CW-2 Billing Gross (Discrete)": "billing_cw_minus2",
        "Displaysize Short Desc": "display_size",
        "Color Short Desc": "color_shrt_desc",
        "Wireless Short Desc": "wireless_desc",
        "rtm_new": "rtm_new",
        "rtm_level_3": "rtm_level_3",
}


KEY_TABLE_NAME_MAPPING = {
        KeySubKeyRelation.ESR_MAIN_PRODUCTS.value: "gc_ro_ds_esr_data",
        KeySubKeyRelation.ESR_ACCESSORIES.value: "gc_ro_ds_esr_accy_data",

        KeySubKeyRelation.CTO_POD.value: "gc_dmp_pod",
        KeySubKeyRelation.CTO_BACKLOG.value: "gc_dmp_backlog",
        KeySubKeyRelation.CTO_EXECUTION.value: "gc_dmp_execution",
    }


class EsrTableName(Enum):
    GC_RO_DS_ESR_DATA = "gc_ro_ds_esr_data"
    GC_RO_DS_ESR_ACCY_DATA = "gc_ro_ds_esr_accy_data"


ESR_MORNING_START_TIME = '08:00'
ESR_MORNING_END_TIME = '10:00'
ESR_AFTERNOON_START_TIME = '15:00'
ESR_AFTERNOON_END_TIME = '17:00'
ESR_COB_START_TIME = '04:00'
ESR_COB_END_TIME = '08:30'



class EsrName(Enum):
    # 定义 key
    ESR_MAIN_PRODUCTS = "ESR - Main Products"
    ESR_ACCESSORIES = "ESR - Accessories"


class CTOName:
    CTO_POD = "CTO - POD"
    CTO_EXECTION = "CTO - Execution"
    CTO_BACKLOG = "CTO - Backlog"


KEY_ESR_NAME_MAPPING = {
        KeySubKeyRelation.ESR_MAIN_PRODUCTS.value: EsrName.ESR_MAIN_PRODUCTS.value,
        KeySubKeyRelation.ESR_ACCESSORIES.value: EsrName.ESR_ACCESSORIES.value,

        KeySubKeyRelation.CTO_POD.value: CTOName.CTO_POD,
        KeySubKeyRelation.CTO_BACKLOG.value: CTOName.CTO_BACKLOG,
        KeySubKeyRelation.CTO_EXECUTION.value: CTOName.CTO_EXECTION,
    }


class KeyTableSelector:
    # 配置 key 与表的对应关系
    KEY_TABLE_MAPPING = {
        KeySubKeyRelation.ESR_MAIN_PRODUCTS.value: GcRoDsEsrData,
        KeySubKeyRelation.ESR_ACCESSORIES.value: GcRoDsEsrAccyData,

        KeySubKeyRelation.CTO_POD.value: CTOPod
    }

    @classmethod
    def get_table(cls, key):
        # 根据 key 和 sub_key 获取对应的表
        if key in cls.KEY_TABLE_MAPPING:
            return cls.KEY_TABLE_MAPPING[key]
        raise ValueError(f"No table mapping found for key: {key}")


class AutomaticFileResult:
    def __init__(self, key: str, channel: str, update_time: str):
        self.key = key
        self.channel = channel
        self.update_time = update_time
        # 默认文件存储路径
        self.folder_path = '/uploads/datasource_fastlite_automatic_files/'

    def _column_mapping(self):
        if self.key in [KeySubKeyRelation.ESR_MAIN_PRODUCTS.value] and self.channel in CUSTOM_REPORT_RTM_LIST:
            return esr_mono_column_mapping
        else:
            return {}

    def __get_file_name(self, update_time, channel) -> str:
        if self.key == KeySubKeyRelation.ESR_MAIN_PRODUCTS.value:
            return f"{update_time}_ESR_Main Products_{channel}"

        if self.key == KeySubKeyRelation.ESR_ACCESSORIES.value:
            return f"{update_time}_ESR_Accessories_{channel}"

        if self.key == KeySubKeyRelation.CTO_POD.value:
            return f"{update_time}_CTO-POD_{channel}"

    def file_info(self, folder_path: str = None) -> dict:
        file_name = self.__get_file_name(update_time=self.update_time, channel=self.channel)

        unique_key = uuid.uuid4().hex
        unique_filename = f"{unique_key}.xlsx"

        if folder_path is not None:
            self.folder_path = folder_path
        # 默认文件存储路径(按天区分)
        now = datetime.now()
        current_date = now.strftime('%Y-%m-%d')
        date_folder_path = f"{self.folder_path}{current_date}/"
        absolute_file_path = get_absolute_path(date_folder_path) + unique_filename
        relative_file_path = date_folder_path + unique_filename

        return {
            "bus_key": file_name.lower(),             # 数据库唯一文件标识建
            "absolute_file_path": absolute_file_path,  # 绝对路径
            "relative_file_path": relative_file_path,  # 相对路径
            "file_name":  f"{file_name}.xlsx",  # 数据库中用于前端展示使用的文件名称
            "unique_key": unique_key,
            "unique_filename": unique_filename,
            "folder_path": self.folder_path,
        }

    def gen_excel_file(self, data: Union[list, pd.DataFrame], file_path: str):
        if isinstance(data, list):
            # 如果数据是列表，转换为 DataFrame
            df = pd.DataFrame(data)
        else:
            # 如果数据已经是 DataFrame，直接使用
            df = data
        db_columns = list(self._column_mapping().values())
        display_columns = list(self._column_mapping().keys())
        # 过滤并重命名标题列
        if db_columns and display_columns:
            df = df[db_columns]
            df.columns = display_columns

        df = fill_nan_to_none(df)

        # 创建一个新的工作簿
        workbook = xlsxwriter.Workbook(file_path)
        worksheet = workbook.add_worksheet()
        # 写入列名
        headers = list(df.columns)
        for col_num, header in enumerate(headers):
            worksheet.write(0, col_num, header)

        # 分批写入数据
        chunksize = 20000
        row_num = 1
        for start in range(0, len(df), chunksize):
            chunk = df.iloc[start:start + chunksize]
            for row in chunk.itertuples(index=False, name=None):
                worksheet.write_row(row_num, 0, row)
                row_num += 1

        # 关闭工作簿
        workbook.close()
