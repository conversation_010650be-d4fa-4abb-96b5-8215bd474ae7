from datetime import datetime

from data.databend.esr.app_gbi_table_sync_status_di import AppGbiTableSyncStatusDi
from domain.datasource.impl.automatic import KeySubKeyRelation, AutomaticDataStatus
from task_kit.register_task import TASK_TYPE_ESR_MAIN_PRODUCTS_EMAIL, TASK_TYPE_ESR_ACCESSORIES_EMAIL, \
    AUTOMATIC_ESR_MAIN_PRODUCTS_GEN_FILE, AUTOMATIC_ESR_ACCESSORIES_GEN_FILE
from task_kit.repository.task_repository import Task, TaskStatus, TaskRepository
from util.const import EmailCmd


def generate_esr_task(data_type, db_name, table_name, begin_time, end_time, notice_time):
    now = datetime.now()
    latest_esr = AppGbiTableSyncStatusDi.query_esr_data_by_time_range(data_type=data_type,
                                                                      db_name=db_name,
                                                                      table_name=table_name,
                                                                      begin_time=begin_time,
                                                                      end_time=end_time)
    if begin_time <= now <= end_time:
        if latest_esr:
            generate_esr_success_task(latest_esr, notice_time)
    elif latest_esr is None:
        generate_esr_error_task(notice_time, table_name, end_time)


def generate_esr_success_task(last_esr, notice_time):
    email_cmd = None
    type = None
    if last_esr:
        email_cmd = EmailCmd.EsrMainProductsEmail if last_esr.table_name == "gc_ro_ds_esr_data" else EmailCmd.EsrAccessoriesEmail
        type = KeySubKeyRelation.ESR_MAIN_PRODUCTS.value if last_esr.table_name == "gc_ro_ds_esr_data" else KeySubKeyRelation.ESR_ACCESSORIES.value

    task_name = f"{last_esr.sync_time}-{email_cmd}"
    task_type = TASK_TYPE_ESR_MAIN_PRODUCTS_EMAIL if last_esr.table_name == "gc_ro_ds_esr_data" else TASK_TYPE_ESR_ACCESSORIES_EMAIL
    task_partition = 0
    task_params = (
        f'{{'
        f'"email_cmd":"{email_cmd}","sync_status":"{AutomaticDataStatus.REFRESH.value}",'
        f'"refresh_version":"{notice_time}", "type":"{type}",'
        f'"sync_time":"{last_esr.sync_time}","snapshot_dt":"{last_esr.snapshot_dt.strftime("%Y-%m-%d")}",'
        f'"fiscal_qtr_week_name":"{last_esr.fiscal_qtr_week_name}", "table_name":"{last_esr.table_name}", '
        f'"data_type":"{last_esr.data_type}", "db_name":"{last_esr.db_name}"'
        f'}}'
    )
    task_desc = f"ESR生成成功，发送-{email_cmd}邮件"
    task = Task(
        name=task_name,
        _type=task_type,
        period=last_esr.sync_time,
        partition=task_partition,
        run_at=datetime.strptime(datetime.now().strftime("%Y-%m-%d %H:%M:%S"), "%Y-%m-%d %H:%M:%S"),
        params=task_params,
        desc=task_desc,
        status=TaskStatus.Ready
    )
    TaskRepository.create(task)


def generate_esr_error_task(notice_time, table_name, end_time):
    email_cmd = EmailCmd.EsrMainProductsEmail if table_name == "gc_ro_ds_esr_data" else EmailCmd.EsrAccessoriesEmail
    type = KeySubKeyRelation.ESR_MAIN_PRODUCTS.value if table_name == "gc_ro_ds_esr_data" else KeySubKeyRelation.ESR_ACCESSORIES.value

    task_name = f"{end_time}-{email_cmd}"
    task_type = TASK_TYPE_ESR_MAIN_PRODUCTS_EMAIL if table_name == "gc_ro_ds_esr_data" else TASK_TYPE_ESR_ACCESSORIES_EMAIL
    task_partition = 1
    task_params = (
        f'{{'
        f'"email_cmd":"{email_cmd}","sync_status":"{AutomaticDataStatus.DELAY.value}",'
        f'"refresh_version":"{notice_time}", "type":"{type}",'
        f'"snapshot_dt":"{end_time.strftime("%Y-%m-%d")}"'
        f'}}'
    )
    task_desc = f"ESR生成失败，发送-{email_cmd}邮件"
    task = Task(
        name=task_name,
        _type=task_type,
        period=end_time,
        partition=task_partition,
        run_at=datetime.strptime(datetime.now().strftime("%Y-%m-%d %H:%M:%S"), "%Y-%m-%d %H:%M:%S"),
        params=task_params,
        desc=task_desc,
        status=TaskStatus.Ready
    )
    TaskRepository.create(task)


def get_reports_todo(data_type, db_name, table_name, task_type):
    origin_reports = AppGbiTableSyncStatusDi.query_last_ten_update_data(data_type=data_type,
                                                                        db_name=db_name,
                                                                        table_name=table_name
                                                                        )
    period_list = [str(item.sync_time) for item in origin_reports]
    created_reports = TaskRepository.get_task_periods_for_esr(type=task_type, period_list=period_list)

    reports_todo = []
    for item in origin_reports:
        if str(item.sync_time) not in created_reports:
            reports_todo.append(item)
    return reports_todo


def create_esr_task(generate_dict, item, key_relation):
    for key, value in generate_dict.items():
        if key:
            report_list = value["reports"]
            partition = value["partition"]
            for report in report_list:
                if report != key_relation:
                    continue
                task_name = f"{item.sync_time}-{key}_{report}"
                task_type = AUTOMATIC_ESR_MAIN_PRODUCTS_GEN_FILE if report == KeySubKeyRelation.ESR_MAIN_PRODUCTS.value else AUTOMATIC_ESR_ACCESSORIES_GEN_FILE
                task_partition = partition
                task_params = (
                    f'{{"type":"{report}","snapshot_ts":"{item.snapshot_ts}","snapshot_dt":"{item.snapshot_dt.strftime("%Y-%m-%d")}", '
                    f'"rtm":"{key}", "fiscal_qtr_week_name":"{item.fiscal_qtr_week_name}",'
                    f'"table_name":"{item.table_name}", "data_type":"{item.data_type}",'
                    f'"db_name":"{item.db_name}", "sync_time": "{item.sync_time}"}}')
                task_desc = f"生成-{key}-{report}对应文件"
                task = Task(
                    name=task_name,
                    _type=task_type,
                    period=item.sync_time,
                    partition=task_partition,
                    run_at=datetime.strptime(datetime.now().strftime("%Y-%m-%d %H:%M:%S"), "%Y-%m-%d %H:%M:%S"),
                    params=task_params,
                    desc=task_desc,
                    status=TaskStatus.Ready
                )
                TaskRepository.create(task)