from datetime import datetime, time
from typing import Optional

from data.databend.esr.app_gbi_table_sync_status_di import AppGbiTableSyncStatusDi
from domain.datasource.entity.automatic.gbi_sync_status import GbiSyncStatus
from domain.datasource.impl.automatic import AutomaticDataStatus, KeySubKeyRelation, CTO_START_TIME, CTO_END_TIME
from task_kit.register_task import AUTOMATIC_CTO_POD_GEN_FILE, TAST_TYPE_CTO_NOTIFICATION_EMAIL
from task_kit.repository.task_repository import Task, TaskRepository, TaskStatus
from util.const import EmailCmd


def monitor_cto(data_type, db_name, table_name):
    now = datetime.now()

    notify_time_config = [
        {
            "check_begin_time": CTO_START_TIME,
            "check_end_time": CTO_END_TIME,
            "generate_notify_task_begin_time": time(6, 0, 0),
            "generate_notify_task_end_time":time(10, 30, 0),
        }
    ]

    for time_range in notify_time_config:
        if time_range['check_begin_time'] <= now.strftime('%H:%M') <= time_range['check_end_time']:
            begin_time = datetime.combine(now.date(), time_range['generate_notify_task_begin_time'])
            end_time = datetime.combine(now.date(), time_range['generate_notify_task_end_time'])
            check_cto_data_and_generate_task(data_type, db_name, table_name, begin_time, end_time)


def check_cto_data_and_generate_task(data_type, db_name, table_name, begin_time, end_time):
    now = datetime.now()
    latest_record = AppGbiTableSyncStatusDi.query_esr_data_by_time_range(data_type=data_type,
                                                                         db_name=db_name,
                                                                         table_name=table_name,
                                                                         begin_time=begin_time,
                                                                         end_time=end_time)
    if begin_time <= now <= end_time:
        if latest_record is not None:
            generate_cto_notification_task(sync_status=AutomaticDataStatus.REFRESH.value,
                                           latest_record=latest_record)
    elif latest_record is None:
        generate_cto_notification_task(sync_status=AutomaticDataStatus.DELAY.value,
                                       end_time=end_time)


def generate_cto_notification_task(sync_status: str,
                                   latest_record: Optional[GbiSyncStatus] = None,
                                   end_time: Optional[str] = None):
    email_cmd = EmailCmd.CTONotificationEmail
    type = KeySubKeyRelation.CTO_POD.value
    task_type = TAST_TYPE_CTO_NOTIFICATION_EMAIL

    if sync_status == AutomaticDataStatus.REFRESH.value:
        task_partition = 0
        task_name = f"{latest_record.sync_time}-{email_cmd}"
        period = latest_record.snapshot_dt.strftime("%Y-%m-%d")
        task_params = (
            f'{{'
            f'"email_cmd":"{email_cmd}", "sync_status":"{sync_status}",'
            f'"type":"{type}",'
            f'"sync_time":"{latest_record.sync_time}", "snapshot_dt":"{period}",'
            f'"fiscal_qtr_week_name":"{latest_record.fiscal_qtr_week_name}", "table_name":"{latest_record.table_name}", '
            f'"data_type":"{latest_record.data_type}", "db_name":"{latest_record.db_name}"'
            f'}}'
        )
    else:
        task_partition = 1
        task_name = f"{end_time}-{email_cmd}"
        period = end_time
        task_params = (
            f'{{'
            f'"email_cmd":"{email_cmd}", "sync_status":"{sync_status}",'
            f'"type":"{type}",'
            f'"snapshot_dt":"{period}"'
            f'}}'
        )

    task_desc = f"CTO生成{sync_status}，发送-{email_cmd}邮件"
    task = Task(
        name=task_name,
        _type=task_type,
        period=period,
        partition=task_partition,
        run_at=datetime.strptime(datetime.now().strftime("%Y-%m-%d %H:%M:%S"), "%Y-%m-%d %H:%M:%S"),
        params=task_params,
        desc=task_desc,
        status=TaskStatus.Ready
    )
    TaskRepository.create(task)


def get_created_cto_tasks(data_type, db_name, table_name, task_type):
    records = AppGbiTableSyncStatusDi.query_last_ten_update_data(data_type=data_type,
                                                                db_name=db_name,
                                                                table_name=table_name
                                                                )
    period_list = [str(item.sync_time) for item in records]
    created_tasks = TaskRepository.get_task_periods_for_esr(type=task_type,
                                                           period_list=period_list)
    return created_tasks, records


def create_cto_generate_files_task(permission_dict: dict, sync_status_item: GbiSyncStatus, key_relation):
    for key, value in permission_dict.items():
        if key:
            reports = value["reports"]
            partition = value["partition"]
            for report in reports:
                if report != key_relation:
                    continue
                task_name = f"{sync_status_item.sync_time}-{key}_{report}"
                task_type = AUTOMATIC_CTO_POD_GEN_FILE
                task_partition = partition
                task_params = (
                    f'{{"type":"{report}","snapshot_ts":"{sync_status_item.snapshot_ts}","snapshot_dt":"{sync_status_item.snapshot_dt.strftime("%Y-%m-%d")}", '
                    f'"rtm":"{key}", "fiscal_qtr_week_name":"{sync_status_item.fiscal_qtr_week_name}",'
                    f'"table_name":"{sync_status_item.table_name}", "data_type":"{sync_status_item.data_type}",'
                    f'"db_name":"{sync_status_item.db_name}", "sync_time": "{sync_status_item.sync_time}"}}')
                task_desc = f"生成-{key}-{report}对应文件"
                task = Task(
                    name=task_name,
                    _type=task_type,
                    period=sync_status_item.sync_time,
                    partition=task_partition,
                    run_at=datetime.strptime(datetime.now().strftime("%Y-%m-%d %H:%M:%S"), "%Y-%m-%d %H:%M:%S"),
                    params=task_params,
                    desc=task_desc,
                    status=TaskStatus.Ready
                )
                TaskRepository.create(task)
