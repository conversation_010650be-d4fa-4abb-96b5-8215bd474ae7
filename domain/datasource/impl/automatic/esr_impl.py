import io
import os
from typing import Optional

import pandas as pd

from datetime import datetime
from data.mysqls.automatic.automatic_file_info import AutomaticFileInfo
from domain.datasource.impl.automatic import (CTO_CHANNEL_PERMISSIONS, AutomaticFileResult, KeySubKeyRelation,
                                              ESR_MONO_ONLINE_PERMISSION_LOB_TO_DB_LOB_MAPPING, ESR_LOB_RTM_LIST)
from util.conf import logger
from util.file_util import get_absolute_path
from data.databend.cto.cto_backlog import CTOBacklog
from data.databend.cto.cto_execution import CTOExecution


def get_download_record_file(query_key: str, last_update_time: str, channel: str, lob: Optional[str] = None,
                             query_file_path:  Optional[str] = None):
    # 文件下载、发送邮件
    automatic_file = AutomaticFileResult(key=query_key, channel=channel, update_time=last_update_time)
    file_name = automatic_file.file_info()['file_name']

    # 获取目录部分, 获取文件名部分
    split_path = [os.path.dirname(query_file_path), os.path.basename(query_file_path)]
    absolute_path = get_absolute_path(split_path[0])
    file_absolute_path = f"{absolute_path}{split_path[1]}"

    # mono、online支持按lob下载
    if query_key == KeySubKeyRelation.ESR_MAIN_PRODUCTS.value and channel in ESR_LOB_RTM_LIST and lob:
        df = pd.read_excel(file_absolute_path)
        filter_lob_field = "lob_id"
        if channel in ESR_LOB_RTM_LIST and 'LOB' in df.columns:
            filter_lob_field = 'LOB'

        df = df[df[filter_lob_field] == lob]

        # 获取入参lob
        reversed_esr_lob_dict = {value: key for key, value in ESR_MONO_ONLINE_PERMISSION_LOB_TO_DB_LOB_MAPPING.items()}
        reversed_lob = reversed_esr_lob_dict.get(lob, lob)
        # 返回文件流，不在系统中创建文件
        excel_file_bytes = io.BytesIO()
        df.to_excel(excel_file_bytes, sheet_name='Sheet0', index=False)
        excel_file_bytes.seek(0)
        prefix, suffix = os.path.splitext(file_name)
        new_file_name = f"{prefix}_{reversed_lob}{suffix}"
        return new_file_name, excel_file_bytes

    # 产品要求文件后缀名称要加ALL LOB
    if query_key == KeySubKeyRelation.ESR_MAIN_PRODUCTS.value and channel in ESR_LOB_RTM_LIST and not lob:
        prefix, suffix = os.path.splitext(file_name)
        file_name = f"{prefix}_All LOB{suffix}"
    return file_name, file_absolute_path


KEY_ORM_MAPPING = {
    KeySubKeyRelation.CTO_BACKLOG.value: CTOBacklog,
    KeySubKeyRelation.CTO_EXECUTION.value: CTOExecution
}

class DownloadContentFromDB:
    def __init__(self, query_key: str, last_update_time: str, channel: str ):
        self.query_key = query_key
        self.last_update_time = last_update_time
        self.channel = channel
        self.snapshot_dt = datetime.strptime(last_update_time, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d') if last_update_time else None

    def download(self):
        orm = KEY_ORM_MAPPING.get(self.query_key)
        permission = CTO_CHANNEL_PERMISSIONS.get(self.channel)
        if self.query_key in permission.get('reports'):
            df = orm.query_records(self.snapshot_dt, permission.get("rtms"), permission.get("regions"))
            # 需要排除的列名
            columns_to_exclude = ['create_time', 'update_time']
            # 检查并删除存在的列
            df.drop(columns=columns_to_exclude, inplace=True, errors='ignore')
            if not df.empty:
                df['snapshot_dt'] = df['snapshot_dt'].dt.strftime('%Y-%m-%d')
            # 返回文件流，不在系统中创建文件
            excel_file_bytes = io.BytesIO()
            df.to_excel(excel_file_bytes, sheet_name="Sheet1", index=False)
            excel_file_bytes.seek(0)
            return excel_file_bytes


def download_content_from_db(query_key: str, last_update_time: str, channel: str):
    download_content = DownloadContentFromDB(query_key=query_key, last_update_time=last_update_time, channel=channel)
    return download_content.download()