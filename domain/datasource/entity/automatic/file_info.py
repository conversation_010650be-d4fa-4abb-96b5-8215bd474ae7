from datetime import datetime


class AutomaticFileInfoEntity:
    def __init__(self,
                 bus_key: str,
                 data_type: str,
                 db_name: str,
                 snapshot_dt: datetime,
                 sync_time: datetime,
                 rtm_view: str,
                 fiscal_qtr_week_name: str,
                 table_name: str,
                 snapshot_ts: datetime,
                 file_name: str,
                 file_path: str
                 ) -> None:
        self.bus_key = bus_key
        self.data_type = data_type
        self.db_name = db_name
        self.snapshot_dt = snapshot_dt
        self.sync_time = sync_time
        self.rtm_view = rtm_view
        self.fiscal_qtr_week_name = fiscal_qtr_week_name
        self.table_name = table_name
        self.snapshot_ts = snapshot_ts
        self.file_name = file_name
        self.file_path = file_path

    def as_dict(self) -> dict:
        return {
            "bus_key": self.bus_key,
            "fiscal_qtr_week_name": self.fiscal_qtr_week_name,
            "table_name": self.table_name,
            "snapshot_ts": self.snapshot_ts,
            "file_name": self.file_name,
            "file_path": self.file_path,
        }
