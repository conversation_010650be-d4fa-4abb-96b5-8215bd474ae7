from datetime import datetime


class GbiSyncStatus:
    def __init__(self,
                 data_type: str,
                 db_name: str,
                 table_name: str,
                 snapshot_dt: datetime,
                 snapshot_ts: datetime,
                 fiscal_qtr_week_name: str,
                 sync_time: str
                 ) -> None:
        self.data_type = data_type
        self.db_name = db_name
        self.table_name = table_name
        self.snapshot_dt = snapshot_dt
        self.snapshot_ts = snapshot_ts
        self.fiscal_qtr_week_name = fiscal_qtr_week_name
        self.sync_time = sync_time

    def as_dict(self) -> dict:
        return {
            "data_type": self.data_type,
            "db_name": self.db_name,
            "table_name": self.table_name,
            "snapshot_dt": self.snapshot_dt,
            "snapshot_ts": self.snapshot_ts,
            "fiscal_qtr_week_name": self.fiscal_qtr_week_name,
            "sync_time": self.sync_time
        }
