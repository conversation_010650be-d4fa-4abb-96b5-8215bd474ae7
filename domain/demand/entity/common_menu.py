from typing import Callable, Optional

from kit.custom_sort import CustomSort
from util.const import (
    REGION_CM,
    ALL,
    CARRIER,
    RETAIL_PARTNER,
    REGION_TAIWAN,
    REGION_HK
)
from util.conf import logger


class Menu(object):
    # rtm default display sequence for web UI
    RTM_RETAIL_PARTNER_SORT_RULE = [
        "All",
        "Mono",
        "Mono Brand",
        "Multi",
        "Multi Brand",
        "Online",
        "Channel Online",
        "Carrier",
        "Retail Partner",
        "Enterprise",
        "Education",
    ]
    # region default display sequence for web UI
    REGION_SORT_RULE = [REGION_CM, REGION_HK, REGION_TAIWAN]

    # nand default display sequence for web UI
    NAND_SORT_RULE = ['All', '64GB', '128GB', '256GB', '512GB', '1TB']

    def __init__(self,
                 query_func: Callable,
                 query_kwargs: Optional[dict] = None,
                 sort_rule: list = None,
                 need_retail_partner=False,
                 rtm=None):
        self.query_func = query_func
        self.query_kwargs = query_kwargs
        if self.query_kwargs is None:
            self.query_kwargs = {}
        self.rtm = rtm  # 仅仅是为了兼容获取单个rtm的相关信息
        self.need_retail_partner = need_retail_partner  # 加个开关, 防止后续仍然需要RP结构的数据
        if sort_rule is None:
            self.sort_rule = self.RTM_RETAIL_PARTNER_SORT_RULE
        else:
            self.sort_rule = sort_rule
        self.region_rtm_subrtm = {}
        self.retail_partners_entity = {}
        self.lob_sublob_mpn_or_nand_color = {}
        self.lob_fields = ["lob", "sub_lob", "mpn"]

    @staticmethod
    def ignore_case(word: str):
        return word.lower()

    def __get_sort_region_rtm(self) -> list:
        """排序 region rtm sub_rtm"""
        """
        region_rtm_subrtm {'china mainland': tmp_rtm}
            tmp_rtm {'Mono':tmp_sub_rtm}
                tmp_sub_rtm {'lifeStyle':tmp_sold_to}   {'All':[{'sold_to_id':'All','sold_to_name':'All'}]}
                    tmp_sold_to [{'sold_to_id':'id1','sold_to_name':'name1'},{'sold_to_id':'id2','sold_to_name':'name2'}]
        """
        regions = []
        for region, rtms in self.region_rtm_subrtm.items():
            rtms_tmp = []
            for rtm, sub_rtms in rtms.items():
                # {"Retail Partner","sub_rtms":None}
                tmp_sub_rtms = None
                if "Retail Partner" != rtm:
                    tmp_sub_rtms = list(sub_rtms.keys())
                # sub_rtms = list(sub_rtms) if sub_rtms is not None else sub_rtms
                if ("Retail Partner" == rtm or "Education" == rtm) and "China mainland" != region:
                    tmp_sub_rtms = ['All']
                if ("Enterprise" == rtm or "Education" == rtm) and "China mainland" == region:
                    tmp_sub_rtms = ['All']
                # 所有的sub_rtm有值情况下均按照字母正序排列
                tmp_sub_rtms.sort(reverse=False, key=self.ignore_case) if tmp_sub_rtms else tmp_sub_rtms

                sub_rtm_tmp = []
                # {"Retail Partner","sub_rtms":None}
                if sub_rtms:
                    for sub_rtm, sold_to_list in sub_rtms.items():
                        new_sold_to_list = [{'sold_to_id': sold_to_tuple[0], 'sold_to_name':sold_to_tuple[1]} for sold_to_tuple in sold_to_list]
                        # solo to 排序
                        new_sold_to_list.sort(reverse=False, key=lambda x: x['sold_to_id'] if x['sold_to_id'] != ALL else '-1') if new_sold_to_list else new_sold_to_list
                        sub_rtm_tmp.append({"sub_rtm": sub_rtm, "sold_to": new_sold_to_list})
                # 排序 for sub_rtms_soldto
                sub_rtm_tmp.sort(reverse=False,  key=lambda x: x['sub_rtm'].lower()) if tmp_sub_rtms else tmp_sub_rtms
                rtms_tmp.append({"rtm": rtm, "sub_rtms": tmp_sub_rtms, "sub_rtms_soldto": sub_rtm_tmp})

            try:
                rtms_tmp = sorted(
                    rtms_tmp, key=lambda x: self.RTM_RETAIL_PARTNER_SORT_RULE.index(x["rtm"])
                )
            except Exception as e:
                logger.error(f"sort rtms error: {e}")
            regions.append({"region": region, "rtms": rtms_tmp})
            try:
                regions = sorted(
                    regions, key=lambda x: self.REGION_SORT_RULE.index(x["region"])
                )
            except Exception as e:
                logger.error(f"sort regions error: {e}")

        return regions

    def __get_sort_retail_partner(self) -> list:
        """排序 RP"""
        retail_partner_list = []
        if self.retail_partners_entity.get(RETAIL_PARTNER):
            # Retail Partner在页面菜单展示All
            self.retail_partners_entity[ALL] = self.retail_partners_entity.pop(RETAIL_PARTNER)
        for rtm, sub_rtms in self.retail_partners_entity.items():
            sub_rtms = list(sub_rtms.keys())
            if ("Enterprise" == rtm or "Education" == rtm) :
                sub_rtms = ['All']
            sub_rtms.sort(reverse=False, key=self.ignore_case)
            retail_partner_list.append({"rtm": rtm, "sub_rtms": sub_rtms})
        try:
            retail_partner_list = sorted(
                retail_partner_list,
                key=lambda x: self.RTM_RETAIL_PARTNER_SORT_RULE.index(x["rtm"]),
            )
        except Exception as e:
            logger.error(f"sort retail_partners error: {e}")

        return retail_partner_list

    def __get_sort_lob(self) -> list:
        """
        排序lob和sub lob
        self.lob_sublob_mpn_or_nand_color的结构如下：
        {
             iphone: {
                 "iphone 15 Plus":{
                    "mpns":set("MU2N3CH/A","MU2N3ZA/A"),
                    "nands":set("128G","256GB"),
                    "colors":set("red","blue"),
                 }
             }
         }
        返回结构如下：[
            {
                "lob": "iPhone",
                "sub_lobs": [
                    {
                        "mpns": [
                            "All"
                        ],
                        "sub_lob": "All"
                    },
                    {
                        "mpns": [
                            "All",
                            "MU2N3CH/A",
                            "MU2N3ZA/A",
                        ],
                        "sub_lob": "iPhone 15 Pro Max"
                    }
                ],
            }
        ]
        """
        lobs = []
        for lob, sub_lobs in self.lob_sublob_mpn_or_nand_color.items():
            sub_lobs_tmp = []
            for sub_lob, mpn_nand_colors in sub_lobs.items():
                sub_lob_item = {
                    "sub_lob": sub_lob,
                }
                for k, v in mpn_nand_colors.items():
                    mpn_nand_color_list = list(v)
                    # 字典 升序
                    if k == "nands":
                        try:
                            mpn_nand_color_list = sorted(
                                mpn_nand_color_list, key=lambda x: self.NAND_SORT_RULE.index(x)
                            )
                        except Exception as e:
                            logger.error(f"sort nand error: {e}")
                    else:
                        mpn_nand_color_list.sort(reverse=False, key=self.ignore_case)
                    sub_lob_item[k] = mpn_nand_color_list
                sub_lobs_tmp.append(sub_lob_item)

            sub_lobs_tmp = CustomSort.sort_iphone_sublobs_item(sub_lobs_tmp)
            for item in sub_lobs_tmp:
                if item["sub_lob"] == "iPhone SE (3rd Gen)":
                    sub_lobs_tmp.remove(item)
                    sub_lobs_tmp.append(item)
                if item["sub_lob"] == ALL:
                    sub_lobs_tmp.remove(item)
                    sub_lobs_tmp.insert(0, item)
            lobs.append({"lob": lob, "sub_lobs": sub_lobs_tmp})
        return lobs

    def __build_from_region_to_subrtm(self, region: str, rtm: str, sub_rtm: str, sold_to_id: str, sold_to_name: str) -> None:
        """初始化region、rtm、sub_rtm相关信息"""
        if region is None:
            return
        """
        region_rtm_subrtm {'china mainland': tmp_rtm}
            tmp_rtm {'Mono':tmp_sub_rtm}
                tmp_sub_rtm {'lifeStyle':tmp_sold_to}   {'All':[{'sold_to_id':'All','sold_to_name':'All'}]}
                    tmp_sold_to [{'sold_to_id':'id1','sold_to_name':'name1'},{'sold_to_id':'id2','sold_to_name':'name2'}]
        """
        tmp_rtm = self.region_rtm_subrtm.setdefault(region, {})
        if rtm is None:
            return
        if region == REGION_CM and rtm not in [ALL, CARRIER] and self.need_retail_partner:
            # 特殊处理China Mainland下Retail Partner的按照rtm聚合sub_rtm后结果单独存放到变量retail_partners_tem
            self.retail_partners_entity.setdefault(ALL, {'All': {(ALL, ALL)}})  # 筛选ALL字段处理
            tmp_sub_rtm = self.retail_partners_entity.setdefault(rtm, {})
        else:
            tmp_rtm.setdefault(ALL, {'All': {(ALL, ALL)}})  # 筛选ALL字段处理
            tmp_sub_rtm = tmp_rtm.setdefault(rtm, {})

        if sub_rtm is None:
            return
        tmp_sub_rtm.setdefault(ALL, {(ALL, ALL)})
        tmp_sold_to = tmp_sub_rtm.setdefault(sub_rtm, set())

        if sold_to_id is None:
            return

        tmp_sold_to.add((ALL, ALL))  # 筛选ALL字段处理
        tmp_sold_to.add((sold_to_id,sold_to_name))

    def _build_from_lob_to_mpn(self, item: dict, fields: list) -> None:
        """ 返回结构
        {
            "iphone": {
                "iphone 15": {
                    "mpns":set("MU2N3CH/A","MU2N3ZA/A")
                }
            }
        }
        """
        # fields 元素分别是lob、sub_lob、mpn
        if len(fields) != 3:
            raise Exception("build_from_lob_to_mpn: unsupported lob fields")
        lob = item.get(fields[0], None)
        if lob is None:
            return
        tmp_lobs = self.lob_sublob_mpn_or_nand_color.setdefault(lob, {})
        tmp_lobs.setdefault(ALL, {"mpns": set()})  # 筛选ALL字段处理, 默认是空, mpn有数据再置为all

        sub_lob = item.get(fields[1], None)
        if sub_lob is None:
            return
        tmp_sub_lob = tmp_lobs.setdefault(sub_lob, {
            "mpns": set(),
        })

        mpn = item.get(fields[2], None)
        if mpn is None:
            return
        tmp_lobs[ALL]["mpns"].add(ALL)
        tmp_sub_lob["mpns"].add(ALL)
        tmp_sub_lob["mpns"].add(mpn)

    def _build_from_lob_to_nand_color_mpn(self, item: dict, fields: list) -> None:
        """ 返回结构
        {
            "iphone": {
                "iphone 15": {
                    "mpns":set("MU2N3CH/A","MU2N3ZA/A")
                }
            }
        }
        """
        # fields 元素分别是lob、sub_lob、mpn
        if len(fields) != 5:
            raise Exception("build_from_lob_to_nand_color_mpn: unsupported lob fields")
        lob = item.get(fields[0], None)
        if lob is None:
            return
        tmp_lobs = self.lob_sublob_mpn_or_nand_color.setdefault(lob, {})
        tmp_lobs.setdefault(ALL, {"mpns": set()})  # 筛选ALL字段处理, 默认是空, mpn有数据再置为all

        sub_lob = item.get(fields[1], None)
        if sub_lob is None:
            return
        tmp_sub_lob = tmp_lobs.setdefault(sub_lob, {
            "mpns": set(),
            "nands": set(),
            "colors": set(),
        })

        nand = item.get(fields[2], None)
        color = item.get(fields[3], None)
        if nand is None or color is None:
            return
        tmp_sub_lob["nands"].add(ALL)
        tmp_sub_lob["nands"].add(nand)
        tmp_sub_lob["colors"].add(ALL)
        tmp_sub_lob["colors"].add(color)

        mpn = item.get(fields[4], None)
        if mpn is None:
            return
        tmp_lobs[ALL]["mpns"].add(ALL)
        tmp_sub_lob["mpns"].add(ALL)
        tmp_sub_lob["mpns"].add(mpn)

    def _build_from_lob_to_nand_color(self, item: dict, fields: list) -> None:
        """
        构造的结构:
           {
               "iphone": {
                   "iphone 15":{
                       "nands":set("128G","256GB"),
                       "colors":set("red","blue"),
                   }
               }
           }
        """
        # fields 内元素分别是lob、sub_lob、nand、color
        if len(fields) != 4:
            raise Exception("build_from_lob_to_nand_color: unsupported lob fields")
        lob = item.get(fields[0], None)
        if lob is None:
            return
        tmp_lobs = self.lob_sublob_mpn_or_nand_color.setdefault(lob, {})
        tmp_lobs.setdefault(ALL, {"nands": set(), "colors": set()})  # 筛选sublob=ALL字段处理
        sub_lob = item.get(fields[1], None)
        if sub_lob is None:
            return
        tmp_sub_lob = tmp_lobs.setdefault(sub_lob, {
            "nands": set(),
            "colors": set(),
        })
        nand = item.get(fields[2], None)
        color = item.get(fields[3], None)
        if nand is None or color is None:
            return
        tmp_sub_lob["nands"].add(nand)
        tmp_sub_lob["colors"].add(color)

    def get_menu(self, lob_fields: Optional[list] = None) -> dict:
        # menu_info结构完整版:[(region, rtm, sub_rtm, lob, sub_lob, mpn, nand, color)]
        lob_fields = lob_fields or self.lob_fields
        menu_info = self.query_func(**self.query_kwargs)
        for item in menu_info:
            item = dict(item)  # 兼容一下返回的item是obj类型(sqlalchemy.engine.row.Row)和dict类型
            region = item.get("region", None)
            rtm = item.get("rtm", None)
            sub_rtm = item.get("sub_rtm", None)
            sold_to_id = item.get("sold_to_id", None)
            sold_to_name = item.get("sold_to_name", None)
            # 构造结构, 填充数据
            # 区域级联：region-rtm-subrtm (rp有特殊逻辑)
            self.__build_from_region_to_subrtm(region=region, rtm=rtm, sub_rtm=sub_rtm, sold_to_id=sold_to_id, sold_to_name=sold_to_name)
            # 设备级联,有两类：lob-sublob-mpn/nand、color
            if len(lob_fields) == 5 and lob_fields[len(lob_fields) - 1] == "mpn":
                self._build_from_lob_to_nand_color_mpn(item, lob_fields)
            elif lob_fields[len(lob_fields) - 1] == "mpn":
                self._build_from_lob_to_mpn(item, lob_fields)
            else:
                self._build_from_lob_to_nand_color(item, lob_fields)

        # 如果存在属于retail_partners下的rtms,给region_rtm_tem[REGION_CM]增加{"rtm":"Retail Partner","sub_rtms":None}
        if self.retail_partners_entity and self.need_retail_partner:
            self.region_rtm_subrtm[REGION_CM].update({RETAIL_PARTNER: None})

        # 排序
        res_region_rtm = self.__get_sort_region_rtm()
        res_retail_partners = self.__get_sort_retail_partner()
        res_lobs = self.__get_sort_lob()

        result = {
            "regions": res_region_rtm,
            "lobs": res_lobs,
            "retail_partners": res_retail_partners,
        }
        return result

    def get_result_menu(self, lob_fields: list = None):
        menu_data = self.get_menu(lob_fields)
        if not self.rtm:
            return menu_data

        # 这个主要目的就是为了获取具体某个rtm的结构, 其余rtm都不获取。
        for rtms in menu_data.get("regions", []):
            for rtm in rtms.get("rtms", []):
                if rtm.get("rtm", "") == self.rtm:
                    rtms["rtms"] = [rtm]
                    break
        return menu_data
