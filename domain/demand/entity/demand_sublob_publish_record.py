from datetime import datetime

from domain.demand.entity.demand_publish_status_enum import DemandPublishStatusEnum


class DemandSublobPublishRecord:
    def __init__(self, fiscal_week: str, demand: str, sub_lob: str, publish_time: datetime):
        self.fiscal_week = fiscal_week
        self.demand = demand
        self.sub_lob = sub_lob
        self.publish_time = publish_time


class DemandPublishDetail:
    def __init__(self, fiscal_week: str, demand: str, lob: str, sub_lob: str,
                 publish_status: int, publish_time: datetime,
                 cw1_update_time: datetime, cw2_update_time: datetime):
        self.fiscal_week = fiscal_week
        self.demand = demand
        self.lob = lob
        self.sub_lob = sub_lob
        self.publish_status = publish_status
        self.publish_time = publish_time
        self.cw1_update_time = cw1_update_time
        self.cw2_update_time = cw2_update_time
        self.cw1_is_adjustmented = 1 if cw1_update_time else 0
        self.cw2_is_adjustmented = 1 if cw2_update_time else 0

    def is_publish(self):
        return self.publish_status == DemandPublishStatusEnum.COMPLETED.value

    def to_dict(self):
        return {
            "fiscal_week": self.fiscal_week,
            "demand": self.demand,
            "lob": self.lob,
            "sub_lob": self.sub_lob,
            "publish_status": self.publish_status,
            "publish_time": self.publish_time.strftime("%Y-%m-%d %H:%M:%S") if self.publish_time else '-',
            "cw1_update_time": self.cw1_update_time.strftime("%Y-%m-%d %H:%M:%S") if self.cw1_update_time else '-',
            "cw2_update_time": self.cw2_update_time.strftime("%Y-%m-%d %H:%M:%S") if self.cw2_update_time else '-',
            "cw1_is_adjustmented": self.cw1_is_adjustmented,
            "cw2_is_adjustmented": self.cw2_is_adjustmented
        }
