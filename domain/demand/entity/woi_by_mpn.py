class WoiByMpn:
    def __init__(self, sub_lob: str, mpn: str, nand: str, color: str, woi: list[float],
                 woi_cw2: list[float] = None, woi_cw3: list[float] = None,
                 shipment_plan_woi_cw1: float = None, shipment_plan_woi_cw2: float = None,
                 **kwargs) -> None:
        self.sub_lob = sub_lob
        self.mpn = mpn
        self.nand = nand
        self.color = color
        self.woi = woi
        self.woi_cw2 = woi_cw2
        self.woi_cw3 = woi_cw3
        if woi:
            self.woi_max = woi[1]
            self.woi_min = woi[0]
        if woi_cw2:
            self.woi_max_cw2 = woi_cw2[1]
            self.woi_min_cw2 = woi_cw2[0]
        if woi_cw3:
            self.woi_max_cw3 = woi_cw3[1]
            self.woi_min_cw3 = woi_cw3[0]
        self.shipment_plan_woi_cw1 = shipment_plan_woi_cw1
        self.shipment_plan_woi_cw2 = shipment_plan_woi_cw2
