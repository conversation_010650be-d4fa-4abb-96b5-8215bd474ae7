from enum import Enum

# RTM 列表
RTMS = ["Mono", "Multi", "Online", "Carrier"]
All_RTMS = ["Mono", "Multi", "Online", "Carrier", "Enterprise", "Education", "Retail Partner"]
RTMS_NO_RETAIL = ["Mono", "Multi", "Online", "Carrier", "Enterprise", "Education"]
RTMS_NO_ONLINE = ["Mono", "Multi", "Carrier"]
RTMS_NO_CARRIER = ["Mono", "Multi", "Online"]
RTMS_NO_ML = ["Enterprise", "Education"]

RTM_MONO = "Mono"
RTM_MULTI = "Multi"
RTM_ONLINE = "Online"
RTM_CARRIER = "Carrier"
RTM_ENTERPRISE = "Enterprise"
RTM_EDUCATION = "Education"

NO_MENTION_SOLDTO = ['1116634', '604401', '1567304', '887577']

JD_SOLDTO = {
    '1118510': {'sold_to_name_whole': 'SHENZHOU SHUMA(SHENZHEN) YOUXIAN GONGSI',
                'sold_to_name_abbreviation': 'SHEN MA',
                'rtm': 'Online',
                'sub_rtm': 'JD self-run'},
    '1255322': {'sold_to_name_whole': 'SHENZHENSHI TIANLIAN ZHONGDUAN YOUXIAN GONGSI',
                'sold_to_name_abbreviation': 'TIAN LIAN',
                'rtm': 'Online',
                'sub_rtm': 'JD self-run'},
    '1426316': {'sold_to_name_whole': 'HAINAN HENGSHA KEJI YOUXIAN GONGSI',
                'sold_to_name_abbreviation': 'HENG SHA',
                'rtm': 'Online',
                'sub_rtm': 'JD self-run'},
    '1520447': {'sold_to_name_whole': 'BAOTONG QIANHONG (XIAMEN) KEJI YOUXIAN GONGSI',
                'sold_to_name_abbreviation': ' BAO TONG',
                'rtm': 'Online',
                'sub_rtm': 'JD self-run'}
    }

IDEAL_DEMAND = "ideal_demand"
TOPDOWN_DEMAND = "top_down_demand"
SELL_IN_DEMAND = "sell_in_demand"
NORMALIZED_DEMAND = "normalized_demand"
DELTA_DEMAND = "delta_demand"
FINAL_DEMAND = "final_demand"
DEMANDS = [IDEAL_DEMAND,TOPDOWN_DEMAND,SELL_IN_DEMAND,NORMALIZED_DEMAND,DELTA_DEMAND,FINAL_DEMAND]

THOUSANDS_SEPARATOR_COLUMNS = {
    # IDEAL_DEMAND: ['cw1_ideal_demand', 'cw2_ideal_demand',
    #                'forecast_cw_ml', 'forecast_cw1_ml', 'forecast_cw2_sales',
    #                'forecast_cw3_sales', 'forecast_cw4_sales', 'forecast_cw5_sales', 'forecast_cw6_sales',
    #                'forecast_cw7_sales',
    #                'shipment_plan_cw', 'shipment_plan_cw1', 'shipment_plan_cw2'],
    IDEAL_DEMAND: [],
    # TOPDOWN_DEMAND: ['cw1_ideal_demand', 'cw2_ideal_demand', 'dt_cw1', 'dt_cw2', 'forecast_cw_ml', 'forecast_cw1_ml',
    #                  'forecast_cw2_dfa', 'forecast_cw3_dfa', 'forecast_cw4_dfa', 'forecast_cw5_dfa', 'forecast_cw6_dfa',
    #                  'forecast_cw7_dfa', 'forecast_cw8_dfa',
    #                  'shipment_plan_cw', 'shipment_plan_cw1', 'shipment_plan_cw2'],
    TOPDOWN_DEMAND: [],
    # NORMALIZED_DEMAND: ['dn_cw1', 'dn_cw2',
    #                     'shipment_plan_cw', 'shipment_plan_cw1', 'shipment_plan_cw2',
    #                     'ds_cw1', 'ds_cw2',
    #                     'cw1_ideal_demand', 'cw2_ideal_demand',
    #                     'forecast_cw_ml', 'forecast_cw1_ml',
    #                     'normalized_fcst_cw2', 'normalized_fcst_cw3', 'normalized_fcst_cw4'
    #                     ],
    NORMALIZED_DEMAND: [],
    # DELTA_DEMAND: ['final_dn_cw1', 'final_dn_cw2'],
    DELTA_DEMAND: [],
    # SELL_IN_DEMAND: ['cw1_ideal_demand', 'cw2_ideal_demand', 'ds_cw1', 'ds_cw2',
    #                  'shipment_plan_cw', 'shipment_plan_cw1', 'shipment_plan_cw2'],
    SELL_IN_DEMAND: [],
    # FINAL_DEMAND: ['dn_cw', 'delta_demand_cw', 'final_demand_cw', 'ideal_demand_cw', 'base_demand_cw',
    #                'avg_sales_fcst', 'avg_normalized_fcst', 'avg_ub_1_5', 'shipment_plan_cw', 'ub_eoh']
    FINAL_DEMAND: []
}

ROUND_COLUMNS = {
    SELL_IN_DEMAND: ['ds_cw1_woi', 'ds_cw2_woi']
}

ROUND_INT_COLUMNS = {
    IDEAL_DEMAND: ['cw1_ideal_demand', 'cw2_ideal_demand', 'forecast_cw_ml', 'forecast_cw1_ml', 'forecast_cw2_sales',
                   'forecast_cw3_sales', 'forecast_cw4_sales', 'forecast_cw5_sales', 'forecast_cw6_sales',
                   'forecast_cw7_sales'],
    TOPDOWN_DEMAND: ['cw1_ideal_demand', 'cw2_ideal_demand', 'dt_cw1', 'dt_cw2', 'forecast_cw_ml', 'forecast_cw1_ml',
                     'forecast_cw2_dfa', 'forecast_cw3_dfa', 'forecast_cw4_dfa', 'forecast_cw5_dfa', 'forecast_cw6_dfa',
                     'forecast_cw7_dfa', 'forecast_cw8_dfa'],
    NORMALIZED_DEMAND: ['dn_cw1', 'dn_cw2',
                        'shipment_plan_cw', 'shipment_plan_cw1', 'shipment_plan_cw2',
                        'ds_cw1', 'ds_cw2',
                        'cw1_ideal_demand', 'cw2_ideal_demand',
                        'forecast_cw_ml', 'forecast_cw1_ml',
                        'normalized_fcst_cw2', 'normalized_fcst_cw3', 'normalized_fcst_cw4'
                        ],
    DELTA_DEMAND: ['final_dn_cw1', 'final_dn_cw2'],
    SELL_IN_DEMAND: ['cw1_ideal_demand', 'cw2_ideal_demand', 'ds_cw1', 'ds_cw2'],
    FINAL_DEMAND: ['dn_cw', 'delta_demand_cw', 'final_demand_cw', 'ideal_demand_cw', 'base_demand_cw', 'avg_sales_fcst',
                   'avg_normalized_fcst', 'avg_ub_1_5', 'shipment_plan_cw', 'ub_eoh', 'ml_runrate_cw']
}

CHINA_MAINLAND = 'China mainland'
COLUMN_MPN = 'mpn'
COLUMN_SOLD_TO_ID = 'sold_to_id'
CW_SHIPMENT_PLAN = 'shipment_plan_cw'
CW1_SHIPMENT_PLAN = 'shipment_plan_cw1'
CW2_SHIPMENT_PLAN = 'shipment_plan_cw2'
CW_ML_FORECAST = 'forecast_cw_ml'
CW1_ML_FORECAST = 'forecast_cw1_ml'
UB_EOH = 'ub_eoh'
AVERAGE_SALES_WEEKS_3_5 = 'avg_sales_fcst_3_5'
AVERAGE_SALES_WEEKS_2_4 = 'avg_sales_fcst_2_4'
AVERAGE_DFA_WEEKS_2_4 = 'avg_dfa_fcst_2_4'
AVERAGE_DFA_WEEKS_3_5 = 'avg_dfa_fcst_3_5'
BASE = 'base'
TWOS = 'twos'
TWOS_CW2 = 'twos_cw2'

DI_CW1 = 'cw1_ideal_demand'
DI_CW2 = 'cw2_ideal_demand'

DT_CW1 = 'dt_cw1'
DT_CW2 = 'dt_cw2'

DS_CW1 = 'ds_cw1'
DS_CW2 = 'ds_cw2'
DS_CW1_WOI = 'ds_cw1_woi'
DS_CW2_WOI = 'ds_cw2_woi'

DN_CW1 = 'dn_cw1'
DN_CW2 = 'dn_cw2'

FINAL_DN_CW1 = 'final_dn_cw1'
FINAL_DN_CW2 = 'final_dn_cw2'

BASE_DEMAND_CW1 = 'base_demand_cw1'
BASE_DEMAND_CW2 = 'base_demand_cw2'

DT_CW1_ORIGIN = 'dt_cw1_origin'
DT_CW2_ORIGIN = 'dt_cw2_origin'

DD_CW1 = 'dd_cw1'
DD_CW2 = 'dd_cw2'

DN_CW1_ADJUSTED = 'dn_cw1_adjusted'
DN_CW2_ADJUSTED = 'dn_cw2_adjusted'

DF_CW1_ADJUSTED = 'df_cw1_adjusted'
DF_CW2_ADJUSTED = 'df_cw2_adjusted'

BASE_DEMAND_CW1_ADJUSTED = 'base_demand_cw1_adjusted'
BASE_DEMAND_CW2_ADJUSTED = 'base_demand_cw2_adjusted'

BASE_DEMAND_CW1 = 'base_demand_cw1'
BASE_DEMAND_CW2 = 'base_demand_cw2'


IDEAL_DEMAND_CW1 = 'cw1_ideal_demand'
IDEAL_DEMAND_CW2 = 'cw2_ideal_demand'

DF_CW1 = 'df_cw1'
DF_CW2 = 'df_cw2'

FORECAST_VERSION_CW1 = 'CW+1'
FORECAST_VERSION_CW2 = 'CW+2'

RTM_SORT_RULE = [
    "Mono",
    "Multi",
    "Online",
    "Carrier",
    "Enterprise",
    "Education",
]

# rtm 预测数据上传和下载相关配置信息
RTM_SALES_FORECAST_TEMPLATE_NAME = 'iPhone_{}_Sales_Forecast_Template_{}.xlsx'
RTM_SALES_FORECAST_NAME = 'iPhone_{}_Sales_Forecast_{}.xlsx'
RTM_FORECAST_TEMPLATE_SHEET_NAME = 'Sheet1'
RTM_FORECAST_TEMPLATE_DICT = {
    "Region": "region",
    "RTM": "rtm",
    "Sub-RTM": "sub_rtm",
    "Sold-to Name": "sold_to_name",
    "Sold-to ID": "sold_to_id",
    "LOB": "lob",
    "Sub-LOB": "sub_lob",
    "Nand": "nand",
    "Color": "color",
    "MPN": "mpn",
    "CW": "forecast_cw",
    "CW+1": "forecast_cw1",
    "CW+2": "forecast_cw2",
    "CW+3": "forecast_cw3",
    "CW+4": "forecast_cw4",
    "CW+5": "forecast_cw5",
    "CW+6": "forecast_cw6",
    "Remark": "remark"
}

# twos 默认值
TWOS_DEFAULT = 5

# Po Delinquent
PO_DELINQUENT_FILE_NAME = '{}_PO_Delinquent_Raw_Data.xlsx'
PO_DELINQUENT_SHEET_NAME = 'Sheet1'
PO_DELINQUENT_RTM_SORT_RULE = ['All', "Mono", "Multi", "Online", "Carrier", "Enterprise", "Education", "Retail Partner"]

# Forecast Comparison
FORECAST_COMPARISON_RTM_SORT_RULE = ["Mono", "Multi", "Carrier", "Online"]

# Po GAP
PO_GAP_FILE_NAME = '{}_PO_Gap_Raw_Data.xlsx'
PO_GAP_SHEET_NAME = 'Sheet1'
PO_GAP_RTM_SORT_RULE = ["Total", "Mono", "Multi", "Online", "Carrier", "Enterprise", "Education", "Retail Partner"]
