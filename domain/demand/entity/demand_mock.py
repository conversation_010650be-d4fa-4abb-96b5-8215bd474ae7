class DemandMock:
    fiscal_weeks = {"fiscal_weeks": ["FY24Q3W5", "FY24Q3W4", "FY24Q3W3", "FY24Q3W2"]}

    homepage = {
        "ideal_demand": {
            "status": 0,
            "sub_progress": 0,
            "is_automatic_generate_rtm_file": False,
        },
        "top_down_demand": {"status": 0, "sub_progress": 0},
        "sell_in_demand": {"status": 0, "sub_progress": 0},
        "normalized_demand": {"status": 0, "sub_progress": 0},
        "delta_demand": {"status": 0, "sub_progress": 0},
        "final_demand": {"status": 0, "sub_progress": 0},
    }
    menu_for_x = {
        "lobs": ["iPhone"],
        "status": 1,  # 同上
        "setting_bys": ["Sub-RTM"],
        "regions": ["China mainland"],
        "rtms": ["All"],
        "sub_rtms": ["All"],
    }
    menu_for_y = {
        "regions": [
            "China mainland"
        ],
        "rtms": [
            {
                "rtm": "All",
                "sub_rtms": [
                    "All"
                ]
            },
            {
                "rtm": "Multi",
                "sub_rtms": [
                    "APR",
                    "AAR"
                ]
            }
        ]
    }
    y_setting = [
        {
            "high_low_runner": "HR",
            "rtm": "Mono",
            "sub_rtm": "Lifestyle",
            "region": "China mainland",
            "twos": 5.2,
            "cw_1_y": [0, 0],
            "cw_2_y": [0, 0],
        },
        {
            "high_low_runner": "HR",
            "rtm": "Multi",
            "sub_rtm": "OTC",
            "region": "China mainland",
            "twos": 5.3,
            "cw_1_y": [0.1, 0.2],
            "cw_2_y": [-0.2, 0.2],
        },
    ]
    rtm_sales_fcst = [
        {
            "rtm": "Mono",
            "status": 1,  # 同上，一级状态枚举值
            "type": "UB",  # UB 或者 SO
            "upload_status": 1,  # 0: 未上传 1: 已上传 2: 过期未上传，使用兜底forecast
            "file_name": "iPhone_Mono_Sales_Forecast_FY24Q3W1",
            "file_path": "/file/storage/1231sdfaasdfxcvasfdax.xlsx",
            "uploador": "Baoguo Liu",
            "upload_time": "2024-04-22 15:00:00",
        },
        {
            "rtm": "Multi",
            "status": 1,
            "type": "UB",
            "upload_status": 1,
            "file_name": "iPhone_Multi_Sales_Forecast_FY24Q3W1",
            "file_path": "/file/storage/4567sdfaasdfxcvasfdax.xlsx",
            "uploador": "Baoguo Liu",
            "upload_time": "2024-04-22 16:00:00",
        },
    ]
    query_x = [
        {
            "rtm": "Mono",
            "sub_rtm": "Lifestyle",
            "sold_to": "All",
            "twos": 5.5,
            "cw_1": {"x": 0, "twos_plus_x": 5.5},
            "cw_2": {"x": 0, "twos_plus_x": 5.5},
            "status": 1,  # 同上， 一级状态
        }
    ]
