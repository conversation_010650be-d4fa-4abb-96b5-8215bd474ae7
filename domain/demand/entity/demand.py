class DemandCw1:
    def __init__(self, amount, base, avg_fcst):
        self.base = base
        self.amount = amount
        self.avg_fcst = avg_fcst

    def woi(self) -> float:
        return _round_float((self.amount - self.base) / self.avg_fcst)

    def get_amount_by_woi(self, woi):
        return woi * self.avg_fcst + self.base


class DemandCw2:
    def __init__(self, amount, amount_cw1, base, avg_fcst, cw2_fcst):
        self.base = base
        self.amount = amount
        self.amount_cw1 = amount_cw1
        self.avg_fcst = avg_fcst
        self.cw2_fcst = cw2_fcst

    def woi(self) -> float:
        w = (self.amount - self.base - self.cw2_fcst + self.amount_cw1) / self.avg_fcst
        return _round_float(w)

    def get_amount_by_woi(self, woi):
        return woi * self.avg_fcst + self.base + self.cw2_fcst - self.amount_cw1


def _round_float(v: float) -> float:
    return round(v, 4)
