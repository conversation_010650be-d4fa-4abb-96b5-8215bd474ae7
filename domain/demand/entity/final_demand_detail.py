import math

import numpy as np

from domain.demand.entity.const import FORECAST_VERSION_CW1
from domain.demand.entity.demand import DemandCw1, DemandCw2


def trans_nan_to_none(value: float):
    return None if value and np.isnan(value) else value


class FinalDemandDetail:
    def __init__(self, rtm: str, sub_rtm: str,
                 normalized_demand: float, final_demand: float, ideal_demand: float, base_demand: float,
                 avg_sale_fcst: float, avg_normalized_fcst: float, avg_actual_ub: float,
                 avg_sale_fcst_2_6: float, avg_normalized_fcst_2_6: float,
                 df_adjusted_cw1: float, df_adjusted_cw2: float, base: float, forecast_version: str, normalized_fcst_cw2: float,
                 shipment_plan: float, di_cw1: float, di_cw2: float, sales_fcst_cw2: float):
        self.rtm = rtm
        self.sub_rtm = sub_rtm
        self.forecast_version = forecast_version
        self.normalized_fcst_cw2 = trans_nan_to_none(normalized_fcst_cw2)
        self.normalized_demand = trans_nan_to_none(normalized_demand)
        self.final_demand = trans_nan_to_none(final_demand)
        self.ideal_demand = trans_nan_to_none(ideal_demand)
        self.di_cw1 = trans_nan_to_none(di_cw1)
        self.di_cw2 = trans_nan_to_none(di_cw2)
        self.sales_fcst_cw2 = trans_nan_to_none(sales_fcst_cw2)
        self.base_demand = trans_nan_to_none(base_demand)

        self.delta_demand = self.cal_delta_demand()

        self.df_adjusted_cw1 = trans_nan_to_none(df_adjusted_cw1)
        self.df_adjusted_cw2 = trans_nan_to_none(df_adjusted_cw2)

        self.base = trans_nan_to_none(base)
        self.shipment_plan = trans_nan_to_none(shipment_plan)

        self.avg_sale_fcst = trans_nan_to_none(avg_sale_fcst)
        self.avg_normalized_fcst = trans_nan_to_none(avg_normalized_fcst)

        self.avg_sale_fcst_2_6 = trans_nan_to_none(avg_sale_fcst_2_6)
        self.avg_normalized_fcst_2_6 = trans_nan_to_none(avg_normalized_fcst_2_6)

        self.avg_actual_ub = trans_nan_to_none(avg_actual_ub)
        self.sale_fcst_woi = self.get_woi(self.get_avg_sale_fcst())
        self.normalized_fcst_woi = self.get_woi(self.get_avg_normalized_fcst())
        self.actual_ub_woi = self.get_woi(self.avg_actual_ub)

    def get_avg_sale_fcst(self):
        return self.avg_sale_fcst if self.forecast_version == FORECAST_VERSION_CW1 else self.avg_sale_fcst_2_6

    def get_avg_normalized_fcst(self):
        return self.avg_normalized_fcst if self.forecast_version == FORECAST_VERSION_CW1 else self.avg_normalized_fcst_2_6

    def sum_normalized_demand(self, normalized_demand: float):
        normalized_demand = trans_nan_to_none(normalized_demand)
        if self.normalized_demand is not None and normalized_demand is not None:
            self.normalized_demand += normalized_demand
        elif self.normalized_demand is None:
            self.normalized_demand = normalized_demand
        self.delta_demand = self.cal_delta_demand()

    def sum_final_demand(self, final_demand: float):
        final_demand = trans_nan_to_none(final_demand)
        if self.final_demand is not None and final_demand is not None:
            self.final_demand += final_demand
        elif self.final_demand is None:
            self.final_demand = final_demand
        self.delta_demand = self.cal_delta_demand()

    def sum_ideal_demand(self, ideal_demand: float):
        ideal_demand = trans_nan_to_none(ideal_demand)
        if self.ideal_demand is not None and ideal_demand is not None:
            self.ideal_demand += ideal_demand
        elif self.ideal_demand is None:
            self.ideal_demand = ideal_demand

    def sum_base_demand(self, base_demand: float):
        base_demand = trans_nan_to_none(base_demand)
        if self.base_demand is not None and base_demand is not None:
            self.base_demand += base_demand
        elif self.base_demand is None:
            self.base_demand = base_demand
        self.delta_demand = self.cal_delta_demand()
    
    def sum_shipment_plan(self, shipment_plan: float):
        '''加和shipment_plan数据'''
        shipment_plan = trans_nan_to_none(shipment_plan)
        if self.shipment_plan is not None and shipment_plan is not None:
            self.shipment_plan += shipment_plan
        elif self.shipment_plan is None:
            self.shipment_plan = shipment_plan

    def cal_delta_demand(self) -> float:
        if self.base_demand is not None and self.final_demand is not None:
            return self.final_demand - self.base_demand
        return None

    def sum_avg_sale_fcst(self, avg_sale_fcst: float):
        avg_sale_fcst = trans_nan_to_none(avg_sale_fcst)
        if self.avg_sale_fcst is not None and avg_sale_fcst is not None:
            self.avg_sale_fcst += avg_sale_fcst
        elif self.avg_sale_fcst is None:
            self.avg_sale_fcst = avg_sale_fcst
        self.sale_fcst_woi = self.get_woi(self.get_avg_sale_fcst())

    def sum_avg_normalized_fcst(self, avg_normalized_fcst: float):
        avg_normalized_fcst = trans_nan_to_none(avg_normalized_fcst)
        if self.avg_normalized_fcst is not None and avg_normalized_fcst is not None:
            self.avg_normalized_fcst += avg_normalized_fcst
        elif self.avg_normalized_fcst is None:
            self.avg_normalized_fcst = avg_normalized_fcst
        self.normalized_fcst_woi = self.get_woi(self.get_avg_normalized_fcst())

    def sum_avg_sale_fcst_2_6(self, avg_sale_fcst_2_6: float):
        avg_sale_fcst_2_6 = trans_nan_to_none(avg_sale_fcst_2_6)
        if self.avg_sale_fcst_2_6 is not None and avg_sale_fcst_2_6 is not None:
            self.avg_sale_fcst_2_6 += avg_sale_fcst_2_6
        elif self.avg_sale_fcst_2_6 is None:
            self.avg_sale_fcst_2_6 = avg_sale_fcst_2_6
        self.sale_fcst_woi = self.get_woi(self.get_avg_sale_fcst())

    def sum_avg_normalized_fcst_2_6(self, avg_normalized_fcst_2_6: float):
        avg_normalized_fcst_2_6 = trans_nan_to_none(avg_normalized_fcst_2_6)
        if self.avg_normalized_fcst_2_6 is not None and avg_normalized_fcst_2_6 is not None:
            self.avg_normalized_fcst_2_6 += avg_normalized_fcst_2_6
        elif self.avg_normalized_fcst_2_6 is None:
            self.avg_normalized_fcst_2_6 = avg_normalized_fcst_2_6
        self.normalized_fcst_woi = self.get_woi(self.get_avg_normalized_fcst())

    def sum_avg_actual_ub(self, avg_actual_ub: float):
        avg_actual_ub = trans_nan_to_none(avg_actual_ub)
        if self.avg_actual_ub is not None and avg_actual_ub is not None:
            self.avg_actual_ub += avg_actual_ub
        elif self.avg_actual_ub is None:
            self.avg_actual_ub = avg_actual_ub
        self.actual_ub_woi = self.get_woi(self.avg_actual_ub)

    def sum_df_adjusted_cw1(self, df_adjusted_cw1: float):
        df_adjusted_cw1 = trans_nan_to_none(df_adjusted_cw1)
        if self.df_adjusted_cw1 is not None and df_adjusted_cw1 is not None:
            self.df_adjusted_cw1 += df_adjusted_cw1
        elif self.df_adjusted_cw1 is None:
            self.df_adjusted_cw1 = df_adjusted_cw1
        self.set_woi()

    def sum_df_adjusted_cw2(self, df_adjusted_cw2: float):
        df_adjusted_cw2 = trans_nan_to_none(df_adjusted_cw2)
        if self.df_adjusted_cw2 is not None and df_adjusted_cw2 is not None:
            self.df_adjusted_cw2 += df_adjusted_cw2
        elif self.df_adjusted_cw2 is None:
            self.df_adjusted_cw2 = df_adjusted_cw2
        self.set_woi()

    def sum_di_cw1(self, di_cw1: float):
        di_cw1 = trans_nan_to_none(di_cw1)
        if self.di_cw1 is not None and di_cw1 is not None:
            self.di_cw1 += di_cw1
        elif self.di_cw1 is None:
            self.di_cw1 = di_cw1
        self.set_woi()

    def sum_di_cw2(self, di_cw2: float):
        di_cw2 = trans_nan_to_none(di_cw2)
        if self.di_cw2 is not None and di_cw2 is not None:
            self.di_cw2 += di_cw2
        elif self.di_cw2 is None:
            self.di_cw2 = di_cw2
        self.set_woi()

    def sum_base(self, base: float):
        base = trans_nan_to_none(base)
        if self.base is not None and base is not None:
            self.base += base
        elif self.base is None:
            self.base = base
        self.set_woi()

    def sum_normalized_fcst_cw2(self, normalized_fcst_cw2: float):
        normalized_fcst_cw2 = trans_nan_to_none(normalized_fcst_cw2)
        if self.normalized_fcst_cw2 is not None and normalized_fcst_cw2 is not None:
            self.normalized_fcst_cw2 += normalized_fcst_cw2
        elif self.normalized_fcst_cw2 is None:
            self.normalized_fcst_cw2 = normalized_fcst_cw2
        self.set_woi()
    
    def sum_sales_fcst_cw2(self, sales_fcst_cw2: float):
        sales_fcst_cw2 = trans_nan_to_none(sales_fcst_cw2)
        if self.sales_fcst_cw2 is not None and sales_fcst_cw2 is not None:
            self.sales_fcst_cw2 += sales_fcst_cw2
        elif self.sales_fcst_cw2 is None:
            self.sales_fcst_cw2 = sales_fcst_cw2
        self.set_woi()

    def set_woi(self):
        self.sale_fcst_woi = self.get_woi(self.get_avg_sale_fcst())
        self.normalized_fcst_woi = self.get_woi(self.get_avg_normalized_fcst())
        self.actual_ub_woi = self.get_woi(self.avg_actual_ub)

    def get_woi(self, run_rate: float):
        woi = None
        # fast lite v1.1 只需要计算ideal demand 的woi，不使用final demand
        if self.forecast_version == FORECAST_VERSION_CW1:
            if self.di_cw1 is not None and self.base is not None and run_rate is not None and run_rate != 0:
                demand_cw1 = DemandCw1(amount=self.di_cw1, base=self.base, avg_fcst=run_rate)
                woi = demand_cw1.woi()
        else:
            if (self.di_cw1 is not None and self.di_cw2 is not None
                    and self.sales_fcst_cw2 is not None
                    and self.base is not None and run_rate is not None and run_rate != 0):
                demand_cw2 = DemandCw2(amount=self.di_cw2, amount_cw1=self.di_cw1,
                                       base=self.base, avg_fcst=run_rate, cw2_fcst=self.sales_fcst_cw2)
                woi = demand_cw2.woi()
        return woi

    def to_dict(self):
        return {
            "rtm": self.rtm,
            "sub_rtm": self.sub_rtm,
            "normalized_demand": 0 if self.normalized_demand is None else self.normalized_demand,
            "delta_demand": 0 if self.delta_demand is None or self.delta_demand < 0 else self.delta_demand,
            "final_demand": 0 if self.final_demand is None else self.final_demand,
            "ideal_demand": 0 if self.ideal_demand is None else self.ideal_demand,
            "base_demand": 0 if self.base_demand is None else self.base_demand,
            "sale_fcst_woi": self.sale_fcst_woi,
            "normalized_fcst_woi": self.normalized_fcst_woi,
            "actual_ub_woi": self.actual_ub_woi,
            "avg_sale_fcst": 0 if self.get_avg_sale_fcst() is None else self.get_avg_sale_fcst(),
            "avg_normalized_fcst": 0 if self.get_avg_normalized_fcst() is None else self.get_avg_normalized_fcst(),
            "avg_actual_ub": 0 if self.avg_actual_ub is None else self.avg_actual_ub,
            "shipment_plan": 0 if self.shipment_plan is None else self.shipment_plan,
        }


class FinalDemandUpload:
    def __init__(self, forecast_version: str, sold_to_id: str, mpn: str, final_dn: float, delta_demand: float):
        self.forecast_version = forecast_version
        self.sold_to_id = sold_to_id
        self.mpn = mpn
        self.final_dn = final_dn
        self.delta_demand = delta_demand

    def get_key(self):
        return str(self.sold_to_id) + '-' + self.mpn
