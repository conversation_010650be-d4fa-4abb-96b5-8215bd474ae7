from enum import Enum
from domain.demand.entity.const import *

# 比特位 0001:Mono,0010:Multi,0100:Online,1000:Carrier
RTM_BINARY_MAPPING = {
    RTM_MONO: 1,
    RTM_MULTI: 2,
    RTM_ONLINE: 4,
    RTM_CARRIER: 8,
    RTM_ENTERPRISE: 16,
    RTM_EDUCATION: 32,
}


# 个位用于记录子状态，接口下发给前端的是除以10的结果
class DemandState(Enum):
    NotStarted = 0  # 0 未开始
    # 1: soldto、mpn数据初始化完成
    WaitingToSetup = 10  # 1 等待CP&F设置Y
    WaitingForRTMSetup = 20  # 2 等待RTM上传FCST/设置X
    WaitingForCalculation = 100  # 10 等待计算DI
    # 101 DFA
    CalculationCompleted = 200  # 20 计算完成
    WaitingForAdjust = 210  # 21 等待修改发布final_demand
    PUBLISHED = 300  # 30 所有sub_lob发布

    END = 10000 # 虚拟的最终节点，不会到达

    def ideal_demand_progress(self) -> int:
        m = {
            DemandState.NotStarted.value: 0,
            DemandState.WaitingToSetup.value: 1,
            DemandState.WaitingForRTMSetup.value: 2,
            DemandState.WaitingForCalculation.value: 2,
            DemandState.CalculationCompleted.value: 3,
        }
        return m[self.value]

    def topdown_demand_progress(self) -> int:
        m = {
            DemandState.NotStarted.value: 0,
            DemandState.WaitingToSetup.value: 1,
            DemandState.WaitingForCalculation.value: 1,
            DemandState.CalculationCompleted.value: 2,
        }
        return m[self.value]

    def sellin_demand_progress(self) -> int:
        m = {
            DemandState.NotStarted.value: 0,
            DemandState.WaitingToSetup.value: 1,
            DemandState.WaitingForCalculation.value: 1,
            DemandState.CalculationCompleted.value: 2,
        }
        return m[self.value]

    def default_progress(self) -> int:
        m = {
            DemandState.NotStarted.value: 0,
            DemandState.WaitingForCalculation.value: 0,
            DemandState.CalculationCompleted.value: 1,
        }
        return m[self.value]

    def final_demand_progress(self) -> int:
        m = {
            DemandState.NotStarted.value: 0,
            DemandState.WaitingToSetup.value: 0,
            DemandState.WaitingForCalculation.value: 0,
            DemandState.CalculationCompleted.value: 1,
            DemandState.WaitingForAdjust.value: 1,
            DemandState.PUBLISHED.value: 1,
        }
        return m[self.value]

    def rtm_state(self, rtm_status: int):
        '''
        for RTM 仅三种状态NotStarted, WaitingForRTMSetup, CalculationCompleted
        '''
        m = {
            DemandState.NotStarted.value: DemandState.NotStarted,
            DemandState.WaitingToSetup.value: DemandState.NotStarted,
            DemandState.WaitingForRTMSetup.value: DemandState.CalculationCompleted,
            DemandState.WaitingForCalculation.value: DemandState.CalculationCompleted,
            DemandState.CalculationCompleted.value: DemandState.CalculationCompleted,
        }

        state = m[self.value]
        # 对ent edu 有可能状态机已经是isCompleted 但是 rtm_status=0
        if state.is_completed() and not rtm_status:
            return DemandState.WaitingForRTMSetup
        return state

    def is_waiting_for_calculation(self) -> bool:
        return self.value == DemandState.WaitingForCalculation.value

    def is_completed(self) -> bool:
        return self.value >= DemandState.CalculationCompleted.value

    # 下发给前端的时候移除个位（个位记录的是后台计算相关的子状态）
    def format(self) -> int:
        return int(self.value / 10)
