import os
import sys
from domain.demand.entity.common_menu import Menu


SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(SCRIPT_DIR))


def load():
    return [{
        "lob": "iPhone",
        "sub_lob": "iPhone 15 Pro Max",
        "mpn": "MU2N3CH/A",
        "nand": "128GB",
        "color":"red",
    },
        {
            "lob": "iPhone",
            "sub_lob": "iPhone 15 Pro Max",
            "mpn": "MU2N3CH/A",
            "nand": "256GB",
            "color": "red",
        },
        {
            "lob": "iPhone",
            "sub_lob": "iPhone 15",
            "mpn": "MU2N3CH/A",
            "nand": "128GB",
            "color": "blue",
        },
        {
            "lob": "iPhone",
            "sub_lob": "iPhone 15",
            "mpn": "MU2N3CH/A",
            "nand": "1TB",
            "color": "blue",
        }
    ]


def test_get_result_menu():
    menu = Menu(query_func=load)
    rsp = menu.get_result_menu(lob_fields=["lob", "sub_lob", "nand", "color"])
    print(rsp)


def test_menu__build_from_lob_to_nand_color():
    menu = Menu(load)
    item = {
        "lob": "iPhone",
        "sub_lob": "iPhone 15 Pro Max",
        "mpn": "MU2N3CH/A",
        "nand": "256GB",
        "color": "red"
    }
    fields = ["lob", "sub_lob", "nand", "color"]
    menu._build_from_lob_to_nand_color(item, fields)

    item2 = {
        "lob": "iPhone",
        "sub_lob": "iPhone 15 Pro Max",
        "mpn": "MU2N3CH/A",
        "nand": "128GB",
        "color": "blue"
    }
    menu._build_from_lob_to_nand_color(item2,fields)
    print(menu.lob_sublob_mpn_or_nand_color)