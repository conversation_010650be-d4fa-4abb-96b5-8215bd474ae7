from domain.demand.entity.const import JD_SOLDTO


class DemandDataConfig:
    def __init__(self, fiscal_week: str, sold_to_id: str, sub_lob: str, lob: str = None,
                 name_type: int = None, jd_self_run_mix_cw1: float = None, jd_self_run_mix_cw2: float = None,
                 adjusted_demand_cw1: str = None, adjusted_demand_cw2: str = None):
        self.sold_to_name = None
        self.rtm = None
        self.sub_rtm = None
        self.fiscal_week = fiscal_week
        self.sold_to_id = sold_to_id
        self.lob = lob
        self.sub_lob = sub_lob
        self.jd_self_run_mix_cw1 = jd_self_run_mix_cw1
        self.jd_self_run_mix_cw2 = jd_self_run_mix_cw2
        self.adjusted_demand_cw1 = adjusted_demand_cw1
        self.adjusted_demand_cw2 = adjusted_demand_cw2
        self.name_type = name_type  # 1: 全称 2: 简称
        self.get_sold_to_info()

    def to_dict(self):
        return {
            "rtm": self.rtm,
            "sub_rtm": self.sub_rtm,
            "fiscal_week": self.fiscal_week,
            "sold_to_id": self.sold_to_id,
            "sold_to_name": self.sold_to_name,
            "lob": self.lob,
            "sub_lob": self.sub_lob,
            "jd_self_run_mix_cw1": self.jd_self_run_mix_cw1,
            "jd_self_run_mix_cw2": self.jd_self_run_mix_cw2
        }

    def get_sold_to_info(self):
        if self.sold_to_id in JD_SOLDTO:
            sold_to_info = JD_SOLDTO[self.sold_to_id]
            self.rtm = sold_to_info.get('rtm')
            self.sub_rtm = sold_to_info.get('sub_rtm')
            sold_to_name_whole = sold_to_info.get('sold_to_name_whole')
            sold_to_name_abbreviation = sold_to_info.get('sold_to_name_abbreviation')
            if self.name_type == 1:
                self.sold_to_name = sold_to_name_whole
            else:
                self.sold_to_name = sold_to_name_abbreviation


