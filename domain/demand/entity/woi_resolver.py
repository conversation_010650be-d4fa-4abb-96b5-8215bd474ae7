from typing import Union, Any

import pandas
import pandas as pd

from data.databend.end_to_end.forecast_feedback_by_region import ForecastFeedbackRegion
from data.mysqls.demand.demand_by_soldto_pool import DemandBySoldtoPool
from data.mysqls.demand.sellin_demand_woi_default_setting import SellinDemandWoiDefaultSetting
from domain.demand.entity.const import CHINA_MAINLAND, RTMS
from domain.demand.entity.woi_by_mpn import WoiByMpn
from domain.demand.entity.woi_by_sublob import WoiBySublob
from domain.demand.impl.soldto_mpn_cartesian_product import CartesianProduct
from domain.end_to_end.entity.demand_feedback_query import get_region_feedback_list, get_pool_list
from domain.supply.entity import Lob
from util.const import WOITypes
from util.util import common_sort_df


def get_feedback_mpn_woi_dict(demand_feedback_df: pd.DataFrame) -> list[dict[str, Union[list[Any], Any]]]:
    sort_key = ["nand", "color", 'mpn']
    sort_df = common_sort_df(demand_feedback_df, sort_key)
    demand_feedback_list = get_region_feedback_list(sort_df)

    return [
        {
            "sub_lob": item.sub_lob,
            "mpn": item.mpn,
            "nand": item.nand,
            "color": item.color,
            "woi": [item.feedback_woi_cw1, item.feedback_woi_cw1],
            "woi_cw2": [item.feedback_woi_cw2, item.feedback_woi_cw2],
            # "default_woi": [item.get_feedback_woi_cw1(), item.get_feedback_woi_cw1()],
            # "default_woi_cw2": [item.get_feedback_woi_cw2(), item.get_feedback_woi_cw2()],
            "shipment_plan_woi_cw1": item.shipment_plan_woi_cw1,
            "shipment_plan_woi_cw2": item.shipment_plan_woi_cw2,
        } for item in demand_feedback_list
    ]


def get_feedback_sublob_woi_dict(demand_feedback_df: pd.DataFrame) -> list[dict[str, Union[list[Any], Any]]]:
    sort_key = ["sub_lob"]
    sort_df = common_sort_df(demand_feedback_df, sort_key)
    demand_feedback_list = get_region_feedback_list(sort_df, group_columns=['sub_lob'], is_group=True)
    return [
        {
            "sub_lob": item.sub_lob,
            "woi_max": item.feedback_woi_cw1,
            "woi_max_cw2": item.feedback_woi_cw2,
            # "default_woi_max": item.get_feedback_woi_cw1(),
            # "default_woi_max_cw2": item.get_feedback_woi_cw2(),
            "shipment_plan_woi_cw1": item.shipment_plan_woi_cw1,
            "shipment_plan_woi_cw2": item.shipment_plan_woi_cw2,
        } for item in demand_feedback_list
    ]


def get_pool_mpn_woi_dict(default_setting_df: pd.DataFrame, sold_to_pool_df: pd.DataFrame) -> pd.DataFrame:
    default_mpn_setting_df = default_setting_df[default_setting_df['setting_by'] == 'mpn']
    new_df = default_mpn_setting_df.copy()
    new_df = new_df.rename(columns={'mpn_or_sublob': 'mpn'})
    mpn_shipment_list = get_pool_list(sold_to_pool_df, group_columns=['mpn'], is_group=True)
    mpn_shipment_df = pandas.DataFrame([item.__dict__ for item in mpn_shipment_list])
    default_df = pandas.merge(new_df, mpn_shipment_df, on=['mpn'], how='left')
    default_df = default_df[['mpn', 'default_woi_min', 'default_woi_max',
                             'default_woi_min_cw2', 'default_woi_max_cw2',
                             'default_woi_min_cw3', 'default_woi_max_cw3',
                             'shipment_plan_woi_cw1', 'shipment_plan_woi_cw2']]
    default_df = default_df.astype(object).where(pd.notnull(default_df), None)
    return default_df


def get_pool_sublob_woi_dict(default_setting_df: pd.DataFrame, sold_to_pool_df: pd.DataFrame) -> pd.DataFrame:
    default_sublob_setting_df = default_setting_df[default_setting_df['setting_by'] == 'sublob']
    new_df = default_sublob_setting_df.copy()
    new_df = new_df.rename(columns={'mpn_or_sublob': 'sub_lob'})
    mpn_shipment_list = get_pool_list(sold_to_pool_df, group_columns=['sub_lob'], is_group=True)
    mpn_shipment_df = pandas.DataFrame([item.__dict__ for item in mpn_shipment_list])
    default_df = pandas.merge(new_df, mpn_shipment_df, on=['sub_lob'], how='left')
    default_df = default_df[['sub_lob', 'default_woi_min', 'default_woi_max',
                             'default_woi_min_cw2', 'default_woi_max_cw2',
                             'default_woi_min_cw3', 'default_woi_max_cw3',
                             'shipment_plan_woi_cw1', 'shipment_plan_woi_cw2']]
    default_df = default_df.astype(object).where(pd.notnull(default_df), None)
    return default_df


class WoiResolver:
    def __init__(self, fiscal_week: str, woi_type: str, woi_by_mpns: list[WoiByMpn],
                 woi_by_sublobs: list[WoiBySublob]) -> None:
        self.fiscal_week = fiscal_week
        self.woi_by_mpns = woi_by_mpns
        self.woi_by_sublobs = woi_by_sublobs
        self.cartesian_product = None
        self.woi_type = woi_type
        if WOITypes.FinalDemand.value == woi_type:
            default_setting_df = SellinDemandWoiDefaultSetting.query_all()
            sold_to_pool_df = DemandBySoldtoPool.get_by_fiscal_week(self.fiscal_week)
            self.default_mpn_setting = get_pool_mpn_woi_dict(default_setting_df, sold_to_pool_df)
            self.default_sublob_setting = get_pool_sublob_woi_dict(default_setting_df, sold_to_pool_df)
        else:
            demand_feedback_df = ForecastFeedbackRegion.get_default_woi(self.fiscal_week, "iPhone")
            # 将demand_feedback_df中的nan转成None
            demand_feedback_df = demand_feedback_df.astype(object).where(pd.notnull(demand_feedback_df), None)
            self.default_mpn_woi = get_feedback_mpn_woi_dict(demand_feedback_df)
            self.default_sublob_woi = get_feedback_sublob_woi_dict(demand_feedback_df)

    def as_dict(self) -> dict:
        if WOITypes.FinalDemand.value == self.woi_type:
            return {
                # 根据query_sub_lob获取woi_by_sublobs
                "sublob_settings": self.__get_sublob_setting(),
                "mpn_settings": self.__get_mpn_setting(),
            }
        else:
            return {
                # 根据query_sub_lob获取woi_by_sublobs
                "sublob_settings": self.__get_feedback_sublob_setting(),
                "mpn_settings": self.__get_feedback_mpn_setting(),
            }

    def __get_sublob_setting(self) -> list[dict]:
        # self.woi_by_sublobs转df
        woi_by_sublob = pd.DataFrame([woi.__dict__ for woi in self.woi_by_sublobs])
        woi_by_sublob = woi_by_sublob.astype(object).where(pd.notnull(woi_by_sublob), None)
        # 将defult_sublob_setting和woi_by_sublobs按照setting_by字段进行合并
        if woi_by_sublob.empty:
            if self.cartesian_product is None:
                self.cartesian_product = CartesianProduct(self.fiscal_week, CHINA_MAINLAND, Lob.IPHONE.value,
                                                          RTMS).get_result()
            key = ['sub_lob']
            woi_by_sublob = self.cartesian_product.drop_duplicates(subset=key)[key]
        # 去除woi_by_sublob中的shipment_plan_woi_cw1字段和shipment_plan_woi_cw2字段
        columns_to_drop = ['shipment_plan_woi_cw1', 'shipment_plan_woi_cw2']
        woi_by_sublob = woi_by_sublob.drop(columns=[col for col in columns_to_drop if col in woi_by_sublob.columns])

        self.default_sublob_setting.rename(columns={'mpn_or_sublob': 'sub_lob'}, inplace=True)
        sublobs = pandas.merge(woi_by_sublob, self.default_sublob_setting, on=['sub_lob'], how='left')
        return [
            {
                "sub_lob": row["sub_lob"],
                "woi_max": row["woi_max"] if "woi_max" in sublobs.columns else row["default_woi_max"],
                "woi_max_cw2": row["woi_max_cw2"] if "woi_max_cw2" in sublobs.columns else row[
                    "default_woi_max_cw2"],
                "woi_max_cw3": row["woi_max_cw3"] if "woi_max_cw3" in sublobs.columns else row[
                    "default_woi_max_cw3"],
                "default_woi_max": row["default_woi_max"],
                "default_woi_max_cw2": row["default_woi_max_cw2"],
                "default_woi_max_cw3": row["default_woi_max_cw3"],
                "shipment_plan_woi_cw1": getattr(row, 'shipment_plan_woi_cw1', None),
                "shipment_plan_woi_cw2": getattr(row, 'shipment_plan_woi_cw2', None)
            }
            for index, row in sublobs.iterrows()
        ]

    def __get_mpn_setting(self) -> list[dict]:
        woi_by_mpn = pd.DataFrame([woi.__dict__ for woi in self.woi_by_mpns])
        woi_by_mpn = woi_by_mpn.astype(object).where(pd.notnull(woi_by_mpn), None)
        if woi_by_mpn.empty:
            if self.cartesian_product is None:
                self.cartesian_product = CartesianProduct(self.fiscal_week, CHINA_MAINLAND, Lob.IPHONE.value,
                                                          RTMS).get_result()
            key = ['sub_lob', "mpn", "nand", "color"]
            woi_by_mpn = self.cartesian_product.drop_duplicates(subset=key)[key]
        # 去除woi_by_mpn中的shipment_plan_woi_cw1字段和shipment_plan_woi_cw2字段
        columns_to_drop = ['shipment_plan_woi_cw1', 'shipment_plan_woi_cw2']
        woi_by_mpn = woi_by_mpn.drop(columns=[col for col in columns_to_drop if col in woi_by_mpn.columns])

        self.default_mpn_setting.rename(columns={'mpn_or_sublob': 'mpn'}, inplace=True)
        mpns = pandas.merge(woi_by_mpn, self.default_mpn_setting, on=['mpn'], how='left')
        return [
            {
                "sub_lob": row["sub_lob"],
                "mpn": row["mpn"],
                "nand": row["nand"],
                "color": row["color"],
                "woi": row["woi"] if "woi" in mpns.columns else [row["default_woi_min"], row["default_woi_max"]],
                "woi_cw2": row["woi_cw2"] if "woi_cw2" in mpns.columns else [row["default_woi_min_cw2"],
                                                                             row["default_woi_max_cw2"]],
                "woi_cw3": row["woi_cw3"] if "woi_cw3" in mpns.columns else [row["default_woi_min_cw3"],
                                                                             row["default_woi_max_cw3"]],
                "default_woi": [row["default_woi_min"], row["default_woi_max"]],
                "default_woi_cw2": [row["default_woi_min_cw2"], row["default_woi_max_cw2"]],
                "default_woi_cw3": [row["default_woi_min_cw3"], row["default_woi_max_cw3"]],
                "shipment_plan_woi_cw1": getattr(row, 'shipment_plan_woi_cw1', None),
                "shipment_plan_woi_cw2": getattr(row, 'shipment_plan_woi_cw2', None)
            }
            for index, row in mpns.iterrows()
        ]

    def __get_feedback_sublob_setting(self) -> list[dict]:
        # self.woi_by_sublobs转df
        woi_by_sublob = pd.DataFrame([woi.__dict__ for woi in self.woi_by_sublobs])
        woi_by_sublob = woi_by_sublob.astype(object).where(pd.notnull(woi_by_sublob), None)
        # 将defult_sublob_setting和woi_by_sublobs按照setting_by字段进行合并
        if woi_by_sublob.empty:
            return self.default_sublob_woi
        else:
            return [
                {
                    "sub_lob": row["sub_lob"],
                    "woi_max": row["woi_max"] if "woi_max" in woi_by_sublob.columns else row["woi_max"],
                    "woi_max_cw2": row["woi_max_cw2"] if "woi_max_cw2" in woi_by_sublob.columns else row[
                        "woi_max_cw2"],
                    "shipment_plan_woi_cw1": row["shipment_plan_woi_cw1"],
                    "shipment_plan_woi_cw2": row["shipment_plan_woi_cw2"]
                }
                for index, row in woi_by_sublob.iterrows()
            ]

    def __get_feedback_mpn_setting(self) -> list[dict]:
        woi_by_mpn = pd.DataFrame([woi.__dict__ for woi in self.woi_by_mpns])
        woi_by_mpn = woi_by_mpn.astype(object).where(pd.notnull(woi_by_mpn), None)
        if woi_by_mpn.empty:
            return self.default_mpn_woi
        else:
            return [
                {
                    "sub_lob": row["sub_lob"],
                    "mpn": row["mpn"],
                    "nand": row["nand"],
                    "color": row["color"],
                    "woi": row["woi"] if "woi" in woi_by_mpn.columns else [row["default_woi_min"], row["default_woi_max"]],
                    "woi_cw2": row["woi_cw2"] if "woi_cw2" in woi_by_mpn.columns else [row["default_woi_min_cw2"],
                                                                                 row["default_woi_max_cw2"]],
                    "shipment_plan_woi_cw1": row["shipment_plan_woi_cw1"],
                    "shipment_plan_woi_cw2": row["shipment_plan_woi_cw2"]
                }
                for index, row in woi_by_mpn.iterrows()
            ]
