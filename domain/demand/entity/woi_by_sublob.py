class WoiBySublob:
    def __init__(self, sub_lob: str, woi_max: float, woi_max_cw2: float = None, woi_max_cw3: float = None,
                 shipment_plan_woi_cw1: float = None, shipment_plan_woi_cw2: float = None,
                 **kwargs) -> None:
        self.sub_lob = sub_lob
        self.woi_max = woi_max
        self.woi_max_cw2 = woi_max_cw2
        self.woi_max_cw3 = woi_max_cw3
        self.shipment_plan_woi_cw1 = shipment_plan_woi_cw1
        self.shipment_plan_woi_cw2 = shipment_plan_woi_cw2