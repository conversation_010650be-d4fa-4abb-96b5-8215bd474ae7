from enum import Enum


class ModuleSwitchEnum(Enum):
    MPN_MIX_UPLOAD = "MPN_MIX_UPLOAD"
    UPDATE_THRESHOLD = "UPDATE_THRESHOLD"
    BEFORE_CALCULATION = "BEFORE_CALCULATION"
    Y_VALUE_DEAD_LINE = "Y_VALUE_DEAD_LINE"
    SALES_FORECAST_DEADLINE = "X_VALUE_DEAD_LINE"
    RTM_SALES_FORECAST_AUTO_GENERATE = "RTM_SALES_FORECAST_AUTO_GENERATE"
    WOI_SETTING_AUTO_PUBLISH = "WOI_SETTING_AUTO_PUBLISH"
    DFA_OPERATE_DEADLINE = "DFA_OPERATE_DEADLINE"
    SALES_FORECAST_OPERATE_DEADLINE = "SALES_FORECAST_OPERATE_DEADLINE"
    RTM_UPLOAD_SALES_FORECAST_REMINDER = "RTM_UPLOAD_SALES_FORECAST_REMINDER"
    Y_PUBLISH_DDL = "Y_PUBLISH_DDL"
    FAST_E2E_DEMAND_FEEDBACK_REVIEW = "FAST_E2E_DEMAND_FEEDBACK_REVIEW"
    FAST_E2E_FORECAST_PREPARATION = "FAST_E2E_FORECAST_PREPARATION"
    FAST_E2E_DEMAND_FINALIZATION = "FAST_E2E_DEMAND_FINALIZATION"
    FAST_E2E_QUANTILE = "FAST_E2E_QUANTILE"
    FAST_E2E_FEEDBACK_SETTING = "FAST_E2E_FEEDBACK_SETTING"
    FAST_E2E_FEEDBACK_REVIEW = "FAST_E2E_FEEDBACK_REVIEW"
    FAST_E2E_FIRST_REJECT = "FAST_E2E_FIRST_REJECT"
    FAST_E2E_SECOND_REJECT = "FAST_E2E_SECOND_REJECT"
    FAST_E2E_THIRD_REJECT = "FAST_E2E_THIRD_REJECT"
    FAST_E2E_JD_CONFIGURATION = "FAST_E2E_JD_CONFIGURATION"
    UB_VELOCITY_UPDATE_THRESHOLD = "UB_VELOCITY_UPDATE_THRESHOLD"
