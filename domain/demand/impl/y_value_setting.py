import copy
import logging

from pandas import DataFrame

from data.mysqls.demand.demand_by_soldto_pool import DemandBySoldtoPool
from data.mysqls.demand.demand_state import TblDemandState
from data.mysqls.demand.demand_y_setting import IdealDemandYValueSetting
from domain.demand.entity.const import TWOS_DEFAULT, DEMANDS
from domain.demand.entity.module_switch_enum import ModuleSwitchEnum
from domain.demand.impl.state_machine import StateProxy
from domain.end_to_end.entity.demand_feedback_query import get_pool_list
from kit.custom_date.custom_week_date import CustomWeekDate
from util.util import _async


def query_y_setting(fiscal_week: str, region: str, high_low_runner: str,
                    rtm: str, sub_rtm: str,
                    sold_to_id, nand, color) -> list:
    y_setting_list = IdealDemandYValueSetting.query_ideal_demand_y_setting(fiscal_week, region,
                                                                           high_low_runner, rtm, sub_rtm,
                                                                           sold_to_id, nand, color)
    # 2025-0313 添加reference woi字段
    sold_to_pool_df = DemandBySoldtoPool.get_by_fiscal_week(fiscal_week)
    mpn_shipment_list = get_pool_list(sold_to_pool_df, is_group=False)
    # 将y_setting_list中的mpn_shipment字段填充
    # 创建一个字典用于快速查找
    mpn_shipment_dict = {(mpn_shipment.sold_to_id, mpn_shipment.mpn): mpn_shipment
                         for mpn_shipment in mpn_shipment_list}

    # 遍历 y_setting_list，根据键查找对应的值
    for y_setting in y_setting_list:
        key = (y_setting['sold_to_id'], y_setting['mpn'])
        if key in mpn_shipment_dict:
            mpn_shipment = mpn_shipment_dict[key]
            y_setting['shipment_plan_woi_cw1'] = mpn_shipment.shipment_plan_woi_cw1
            y_setting['shipment_plan_woi_cw2'] = mpn_shipment.shipment_plan_woi_cw2
        else:
            y_setting['shipment_plan_woi_cw1'] = 0
            y_setting['shipment_plan_woi_cw2'] = 0

    return y_setting_list


def get_demand_fiscal_weeks(demand: str) -> list:
    return TblDemandState.query_distinct_fiscal_weeks_by_demand(demand)


def update_y_setting(fiscal_week: str, settings: dict):
    update_list = []
    fields_to_update = ['cw1_y', 'cw2_y', 'is_published', 'update_time']
    for setting in settings:
        # fiscal_week, hr_lr_type, sub_lob, rtm, sub_rtm, sold_to_id, mpn
        y_setting = copy.copy(setting)
        y_setting['fiscal_week'] = fiscal_week
        y_setting['is_published'] = False
        update_list.append(y_setting)
    IdealDemandYValueSetting.batch_update(update_list, fields_to_update)


def get_final_menu_for_y(fiscal_week: str, menu_y: dict) -> dict:
    # system state
    state_proxy = StateProxy(fiscal_week, "ideal_demand")
    current_state = state_proxy.current_state()
    publish_status = IdealDemandYValueSetting.check_is_published(fiscal_week)
    publish_on = CustomWeekDate(ModuleSwitchEnum.Y_PUBLISH_DDL.value).is_before_ddl()
    res = {
        "lob": "iPhone",
        "status": current_state.format(),
        "setting_by": "Sub-RTM",
        "publish_status": publish_status,
        "publish_on": publish_on
    }
    # 特殊处理 all all 时把所有数据放出来
    all_sold_to = menu_y['regions'][0]['rtms'][0]['sub_rtms_soldto'][0]['sold_to']
    distinct_sold_to = IdealDemandYValueSetting.query_distinct_sold_to(fiscal_week)
    for sold_to in distinct_sold_to:
        all_sold_to.append({
            'sold_to_id': sold_to[0],
            'sold_to_name': sold_to[1]
        })
    # menu_y 在其他地方都有使用，但是都没有添加All，不知道是否是前端或者prd要求，但是此处需要加All
    sub_lobs = menu_y['lobs'][0]['sub_lobs']
    sub_lobs.pop(0)
    for item in sub_lobs:
        item['colors'] = ['All'] + item['colors']
        item['nands'] = ['All'] + item['nands']
    res.update(menu_y)
    return res


def get_y_menu(fiscal_week: str, region: str, rtm: str) -> list:
    return IdealDemandYValueSetting.get_region_rtm(fiscal_week=fiscal_week, region=region, rtm=rtm)


def get_soldto_mpn_twos_dict(fiscal_week: str, region: str) -> dict[str:[]]:
    logging.info(f"y setting query fiscal_week: {fiscal_week}, region: {region}")
    y_setting_list = IdealDemandYValueSetting.query_ideal_demand_y_setting(fiscal_week, region, None, None, None)
    logging.info(f"y_setting_list: {len(y_setting_list)}")
    soldto_mpn_twos = {}
    for y_setting in y_setting_list:
        key = y_setting['sold_to_id']+"-"+y_setting['mpn'] + "-" + y_setting['hr_lr_type']
        if key not in soldto_mpn_twos:
            soldto_mpn_twos[key] = [y_setting['cw1_y']+y_setting['twos'], y_setting['cw2_y']+y_setting['twos']]
    return soldto_mpn_twos


def get_twos_by_dataframe(fiscal_week: str, region: str, dataframe: DataFrame) -> DataFrame:
    """
    计算时使用demand_by_sold_to_pool 表，直接通过sold to + mpn 拼接y setting表的cw1 cw2作为twos使用，如果没有找到默认twos
    """
    soldto_mpn_twos = get_soldto_mpn_twos_dict(fiscal_week, region)
    return set_twos_to_dataframe(dataframe, soldto_mpn_twos)


def set_twos_to_dataframe(dataframe, soldto_mpn_twos):
    dataframe['twos'] = None
    dataframe['twos_cw2'] = None
    for index, row in dataframe.iterrows():
        mpn = row['mpn']
        sold_to_id = row['sold_to_id']
        hrlr = row['hr_lr']
        key = sold_to_id + "-" + mpn + "-" + hrlr
        if key in soldto_mpn_twos:
            dataframe.at[index, 'twos'] = soldto_mpn_twos[key][0]
            dataframe.at[index, 'twos_cw2'] = soldto_mpn_twos[key][1]
        else:
            dataframe.at[index, 'twos'] = TWOS_DEFAULT
            dataframe.at[index, 'twos_cw2'] = TWOS_DEFAULT
    return dataframe


@_async
def re_calculate(fiscal_week: str, demands: list[str] = DEMANDS):
    for demand in demands:
        state_proxy = StateProxy(fiscal_week, demand)
        if state_proxy.is_completed():
            state_proxy.re_calculate()
