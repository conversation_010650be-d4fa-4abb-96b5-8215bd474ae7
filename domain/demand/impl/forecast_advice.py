import io
import logging

from data.databend.end_to_end.fast_demand import ForecastDemand


def get_forecast_advice_download_file(fiscal_week_name: str, lob: str, rtm: str):
    try:
        file_name = f'Forecast Advice {fiscal_week_name}.xlsx'
        data_df = ForecastDemand.get_download_data(fiscal_week_name, lob, rtm)
        # 返回文件流，不在系统中创建文件
        excel_file_bytes = io.BytesIO()
        data_df.to_excel(excel_file_bytes, index=False)
        excel_file_bytes.seek(0)
        return file_name, excel_file_bytes
    except Exception as e:
        logging.info(f"gen csv error is {e}.")
        return False
