import io
import pandas as pd
import numpy as np
from datetime import datetime

from data.cpf_data_source import OdsFastCPFActiveSKULob
from data.mysqls.demand.ideal_demand_cpf_upload_dfa import IdealDemandCPFUploadDFA
from domain.demand.entity.module_switch_enum import ModuleSwitchEnum
from kit.custom_date.custom_week_date import CustomWeekDate
from kit.validator.customer_rule import AllowedRule, RangeRule, PositiveIntegerRangeRule
from kit.validator.file import FileValidator
from util.conf import logger
from domain.demand.impl.state_machine import StateProxy
from domain.demand.entity.const import *
from util.const import DateTimeFormat

DFA_COLUMNS_DICT = {
    "Fiscal Week": "fiscal_week",
    "Sub-LOB": "sub_lob",
    "CW": "cw",
    "CW+1": "cw1",
    "CW+2": "cw2",
    "CW+3": "cw3",
    "CW+4": "cw4",
    "CW+5": "cw5",
    "CW+6": "cw6",
    "CW+7": "cw7",
    "CW+8": "cw8",
}
DFA_COLUMNS = list(DFA_COLUMNS_DICT.keys())
DFA_COLUMNS_RAW = list(DFA_COLUMNS_DICT.values())


def get_excle_bytes(df: pd.DataFrame):
    excel_file_bytes = io.BytesIO()
    df.to_excel(excel_file_bytes, index=False)
    excel_file_bytes.seek(0)
    return excel_file_bytes


def get_dfa_template_binary(fiscal_week: str):
    # 查询datasource中最大version的sub_lob数据
    sub_lobs = OdsFastCPFActiveSKULob.list_sub_lobs("China mainland", "iPhone")

    # 生成文件流
    df = pd.DataFrame([], columns=DFA_COLUMNS)
    df["Sub-LOB"] = sub_lobs
    df["Fiscal Week"] = fiscal_week
    file_name = f"iPhone_CP&F_DFA_Forecast_Template_{fiscal_week}.xlsx"
    return file_name, get_excle_bytes(df)


def get_dfa_upload_file(fiscal_week: str):
    df = IdealDemandCPFUploadDFA.query_by_week(fiscal_week)
    df.columns = DFA_COLUMNS
    file_name = f"iPhone_CP&F_DFA_Forecast_{fiscal_week}.xlsx"
    return file_name, get_excle_bytes(df)


def upload_dfa_file(file, fiscal_week: str, uploader: str):
    logger.info(f"{uploader} start upload {fiscal_week} dfa file.")
    df = pd.read_excel(file)
    
    # 文件校验
    sub_lob_list = OdsFastCPFActiveSKULob.list_sub_lobs("China mainland", "iPhone")
    header_rules = {"header": DFA_COLUMNS}
    rules = {
        "Sub-LOB": ["required", AllowedRule(sub_lob_list)],
        "CW": ["required", PositiveIntegerRangeRule(0, None)],
        "CW+1": ["required", PositiveIntegerRangeRule(0, None)],
        "CW+2": ["required", PositiveIntegerRangeRule(0, None)],
        "CW+3": ["required", PositiveIntegerRangeRule(0, None)],
        "CW+4": ["required", PositiveIntegerRangeRule(0, None)],
        "CW+5": ["required", PositiveIntegerRangeRule(0, None)],
        "CW+6": ["required", PositiveIntegerRangeRule(0, None)],
        "CW+7": ["required", PositiveIntegerRangeRule(0, None)],
        "CW+8": ["required", PositiveIntegerRangeRule(0, None)],
    }
    validator = FileValidator(
        df, rules=rules, header_rules=header_rules
    )
    has_pass_validation, row, errors = validator.do_validate()
    # 返回文件校验的错误信息
    priority_validation = ["Sub-LOB", "CW", "CW+1", "CW+2", "CW+3", "CW+4", "CW+5", "CW+6", "CW+7", "CW+8"]
    FileValidator.handle_error_msg(has_pass_validation, errors, priority_validation)
    
    # 替换columns为数据库对应字段以及其他补充字段
    df.columns = DFA_COLUMNS_RAW
    df["uploader"] = uploader
    current_time = datetime.now()
    df["create_time"] = current_time
    df["update_time"] = current_time
    
    # 保存数据到DB中
    df.replace({np.nan: None}, inplace=True)
    IdealDemandCPFUploadDFA.bulk_insert_or_update(df.to_dict("records"))
    logger.info(f"{uploader} uploaded {len(df)} line(s) DFA in {fiscal_week}")
    return {
        "file_name": f"iPhone_CP&F_DFA_Forecast_{fiscal_week}.xlsx",
        "uploader": uploader,
        "upload_time": current_time.strftime('%Y-%m-%d %H:%M:%S'),
    }


def delete_dfa_data(fiscal_week: str, operator: str):
    # 保存数据到DB中
    IdealDemandCPFUploadDFA.delete_by_week(fiscal_week)
    logger.info(f"{operator} delete DFA in {fiscal_week}")
    return "ok"


def query_dfa(fiscal_week: str):
    state_proxy = StateProxy(fiscal_week, TOPDOWN_DEMAND)
    status = state_proxy.current_state()
    upload_status = 0
    file_name = ""
    uploader = ""
    upload_time = ""
    can_reoperate = True
    ret = IdealDemandCPFUploadDFA.query_upload_by_week(fiscal_week)
    if ret:
        upload_status = 1
        file_name = f"iPhone_CP&F_DFA_Forecast_{fiscal_week}.xlsx"
        uploader = ret[0].uploader
        upload_time = ret[0].create_time and ret[0].create_time.strftime(DateTimeFormat)
        if (not CustomWeekDate(ModuleSwitchEnum.DFA_OPERATE_DEADLINE.value).is_before_ddl() and bool(ret[0].is_published)):
            can_reoperate = False

    return {
        "status": status.format(),
        "upload_status": upload_status,
        "template_file_name": f"iPhone_CP&F_DFA_Forecast_Template_{fiscal_week}",
        "file_name": file_name,
        "file_path": "",
        "uploader": uploader,
        "upload_time": upload_time,
        "can_reoperate": can_reoperate
    }


def publish_dfa(fiscal_week: str, operator: str):
    state_proxy = StateProxy(fiscal_week, TOPDOWN_DEMAND)
    result = state_proxy.do_publish_dfa()
    if result:
        # 更新upload_dfa表的is_published
        IdealDemandCPFUploadDFA.publish_by_week(fiscal_week)
    
    logger.info(f'{operator} has published {fiscal_week} dfa at {datetime.now()}')
    
    return result.format()
    
