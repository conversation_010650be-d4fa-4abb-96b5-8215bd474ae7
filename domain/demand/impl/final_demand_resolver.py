import math
import traceback
from collections import defaultdict
from datetime import datetime
from itertools import groupby
from concurrent.futures import ThreadPoolExecutor
import numpy as np
import pandas as pd

from data.cpf_data_source import OdsFastCPFActiveSKULob, OdsFastCPFSoldToMappingIPhone
from data.databend.demand.jd_shipment_plan import JDShipmentPlan
from data.databend.end_to_end.forecast_feedback_by_region import ForecastFeedbackRegion
from data.databend.end_to_end.forecast_feedback_by_soldto import ForecastFeedbackSoldto
from data.fiscal_year_week import FiscalYearWeek
from data.mysqls.demand.demand_by_region_pool import DemandByRegionPool
from data.mysqls.demand.demand_by_soldto_pool import DemandBy<PERSON>oldtoPool
from data.mysqls.demand.demand_publish_record import DemandPublishRecord
from data.mysqls.demand.jd_self_run_actual_sold_to import JdSelfRunActualSoldToRepository
from data.mysqls.demand.weekly_demand_setting import WeeklyDemandSetting
from data.mysqls.end_to_end.feedback_demand_by_region import DemandFeedbackByRegion
from data.mysqls.end_to_end.feedback_demand_by_soldto import DemandFeedbackBySoldTo
from domain.dashboard.entity.fiscal_week import FiscalWeek
from domain.dashboard.impl.fiscal_week_container import FiscalWeekContainer
from domain.demand.entity.const import DN_CW1_ADJUSTED, FINAL_DN_CW1, DF_CW1_ADJUSTED, DF_CW1, DN_CW2_ADJUSTED, \
    DF_CW2_ADJUSTED, DF_CW2, FINAL_DEMAND, RTM_ENTERPRISE, RTM_EDUCATION, RTMS, RTMS_NO_RETAIL, BASE, FINAL_DN_CW2, \
    THOUSANDS_SEPARATOR_COLUMNS, COLUMN_MPN, FORECAST_VERSION_CW1, BASE_DEMAND_CW1_ADJUSTED, BASE_DEMAND_CW1, \
    BASE_DEMAND_CW2, BASE_DEMAND_CW2_ADJUSTED, IDEAL_DEMAND_CW1, IDEAL_DEMAND_CW2, DN_CW2, DN_CW1, JD_SOLDTO
from domain.demand.entity.demand_data_config import DemandDataConfig
from domain.demand.entity.demand_publish_status_enum import DemandPublishStatusEnum
from domain.demand.entity.demand_sublob_publish_record import DemandPublishDetail
from domain.demand.entity.final_demand_detail import FinalDemandDetail, FinalDemandUpload
from domain.demand.impl.carrier_mpn import replace_carrier_mpn_to_rp_mpn
from domain.demand.impl.ideal_demand_result import FINAL_DEMAND_TEMPLATE_COLUMNS, FinalDemandResult
from domain.supply.entity import Lob
from kit.number import is_non_negative_integer
from kit.pd import fill_nan_to_none, replace_none_from_nan
from kit.validator.customer_rule import AllowedRule
from kit.validator.file import FileValidator, raise_upload_error
from util.conf import logger
from util.const import REGION_CM, ErrCode, ErrorExcept, StrRTMCPF
from util.cpf_util import get_rtm_mail_fast_url
from util.template_email_sender import TemplateEmail
from util.util import take_time


class FinalDemandResolver:

    def __init__(self, fiscal_week: str, sub_lobs: list[str], rtms: list[str], forecast_version: str):
        self.forecast_version = forecast_version
        self.rtms = rtms
        self.sub_lobs = sub_lobs
        self.fiscal_week = fiscal_week

    def query_final_demand_detail(self, forecast_version: str) -> list[FinalDemandDetail]:
        # 根据 rtm  sub_lob  fiscal_week 获取soldto表数据
        sub_rtm_group_df = DemandBySoldtoPool.sum_by_sub_rtm(self.fiscal_week, self.rtms, self.sub_lobs)
        fill_nan_to_none(sub_rtm_group_df)
        # df_cw1_adjusted = None
        group_details = self.get_final_demand_detail_list(forecast_version, sub_rtm_group_df)
        #  根据sub lob 查询region表聚合数据
        region_sum = DemandByRegionPool.sum_by_fiscal_week(fiscal_week=self.fiscal_week, sub_lobs=self.sub_lobs)
        detail = self.sum_final_demand_detail(group_details, forecast_version, region_sum)
        return detail

    def get_final_demand_detail_list(self, forecast_version, sold_to_df) -> list[FinalDemandDetail]:
        soldto_mpn_details = []
        for _, row in sold_to_df.iterrows():
            # 如果adjusted的值为None 就用原本的 cw1 cw2数据
            df_cw1_adjusted = row[DF_CW1] if row[DF_CW1_ADJUSTED] is None else row[DF_CW1_ADJUSTED]
            df_cw2_adjusted = row[DF_CW2] if row[DF_CW2_ADJUSTED] is None else row[DF_CW2_ADJUSTED]

            # 8.2: base demand
            base_demand_cw1_adjusted = row[BASE_DEMAND_CW1] if row[BASE_DEMAND_CW1_ADJUSTED] is None else row[BASE_DEMAND_CW1_ADJUSTED]
            base_demand_cw2_adjusted = row[BASE_DEMAND_CW2] if row[BASE_DEMAND_CW2_ADJUSTED] is None else row[BASE_DEMAND_CW2_ADJUSTED]
            detail = FinalDemandDetail(
                rtm=row['rtm'],
                sub_rtm=row['sub_rtm'],
                normalized_demand=row[DN_CW1] if forecast_version == FORECAST_VERSION_CW1 else row[DN_CW2],
                final_demand=df_cw1_adjusted if forecast_version == FORECAST_VERSION_CW1 else df_cw2_adjusted,
                ideal_demand=row[IDEAL_DEMAND_CW1] if forecast_version == FORECAST_VERSION_CW1 else row[IDEAL_DEMAND_CW2],
                base_demand=base_demand_cw1_adjusted if forecast_version == FORECAST_VERSION_CW1 else base_demand_cw2_adjusted,
                avg_sale_fcst=row[
                    ['forecast_cw2_sales', 'forecast_cw3_sales', 'forecast_cw4_sales']].mean(),
                avg_normalized_fcst=row[
                    ['normalized_fcst_cw2', 'normalized_fcst_cw3', 'normalized_fcst_cw4']].mean(),
                avg_sale_fcst_2_6=row[
                    ['forecast_cw3_sales', 'forecast_cw4_sales', 'forecast_cw5_sales']].mean(),
                avg_normalized_fcst_2_6=row[['normalized_fcst_cw3', 'normalized_fcst_cw4', 'normalized_fcst_cw5']].mean(),
                avg_actual_ub=row['avg_ub_1_5'],
                df_adjusted_cw1=df_cw1_adjusted, df_adjusted_cw2=df_cw2_adjusted, base=row[BASE],
                forecast_version=forecast_version, normalized_fcst_cw2=row['normalized_fcst_cw2'],
                shipment_plan=row["shipment_plan_cw1"] if forecast_version == FORECAST_VERSION_CW1 else row["shipment_plan_cw2"],
                di_cw1=row[IDEAL_DEMAND_CW1], di_cw2=row[IDEAL_DEMAND_CW2], sales_fcst_cw2=row['forecast_cw2_sales']
            )
            soldto_mpn_details.append(detail)
        return soldto_mpn_details

    def sum_final_demand_detail(self, group_details: list[FinalDemandDetail], forecast_version: str, region_sum: dict) -> list[FinalDemandDetail]:
        # 解析为  list[FinalDemandDetail]
        # dict[key=rtm,FinalDemandDetail]
        rtm_all_detail_dict = {}
        detail_sum_all = FinalDemandDetail(rtm='All', sub_rtm='All', normalized_demand=None, final_demand=None,
                                           ideal_demand=None, base_demand=None, avg_sale_fcst=None, avg_normalized_fcst=region_sum['avg_dfa_fcst_2_4'],
                                           avg_sale_fcst_2_6=None, avg_normalized_fcst_2_6=region_sum['avg_dfa_fcst_3_5'],
                                           avg_actual_ub=region_sum['avg_ub_1_5'],
                                           df_adjusted_cw1=None, df_adjusted_cw2=None, base=region_sum['base'],
                                           forecast_version=forecast_version, normalized_fcst_cw2=None, shipment_plan=None, di_cw1=None, di_cw2=None, sales_fcst_cw2=None)
        rtm_all_detail_dict['All'] = detail_sum_all
        result_list = [detail_sum_all]
        for detail in group_details:
            rtm = detail.rtm
            if rtm not in rtm_all_detail_dict:
                # 先添加一个初始化的sub_rtm = All 的数据到list
                detail_sum_rtm = FinalDemandDetail(rtm=rtm, sub_rtm='All', normalized_demand=None, final_demand=None,
                                                   ideal_demand=None, base_demand=None, avg_sale_fcst=None, avg_normalized_fcst=None,
                                                   avg_sale_fcst_2_6=None, avg_normalized_fcst_2_6=None,
                                                   avg_actual_ub=None,
                                                   df_adjusted_cw1=None, df_adjusted_cw2=None, base=None,
                                                   forecast_version=forecast_version, normalized_fcst_cw2=None, shipment_plan=None, di_cw1=None, di_cw2=None, sales_fcst_cw2=None)
                rtm_all_detail_dict[rtm] = detail_sum_rtm
                result_list.append(detail_sum_rtm)
            #  如果是ent edu的数据 只有all
            if detail.rtm not in [RTM_ENTERPRISE, RTM_EDUCATION]:
                result_list.append(detail)
            # 对All求和
            self.__sum_detail(detail_sum_all, detail, True)
            self.__sum_detail(rtm_all_detail_dict[rtm], detail)
        return result_list

    def __sum_detail(self, all_detail, detail, is_region: bool = False):
        all_detail.sum_normalized_demand(detail.normalized_demand)
        all_detail.sum_final_demand(detail.final_demand)
        all_detail.sum_ideal_demand(detail.ideal_demand)
        all_detail.sum_base_demand(detail.base_demand)
        all_detail.sum_shipment_plan(detail.shipment_plan)
        all_detail.sum_avg_sale_fcst(detail.avg_sale_fcst)
        all_detail.sum_avg_sale_fcst_2_6(detail.avg_sale_fcst_2_6)
        all_detail.sum_normalized_fcst_cw2(detail.normalized_fcst_cw2)
        all_detail.sum_sales_fcst_cw2(detail.sales_fcst_cw2)
        all_detail.sum_df_adjusted_cw1(detail.df_adjusted_cw1)
        all_detail.sum_df_adjusted_cw2(detail.df_adjusted_cw2)
        # 需要使用ideal demand 计算woi
        all_detail.sum_di_cw1(detail.di_cw1)
        all_detail.sum_di_cw2(detail.di_cw2)
        if not is_region:
            # region的这些数据不能从 sold to/mpn加上来
            all_detail.sum_base(detail.base)
            all_detail.sum_avg_normalized_fcst(detail.avg_normalized_fcst)
            all_detail.sum_avg_normalized_fcst_2_6(detail.avg_normalized_fcst_2_6)
            all_detail.sum_avg_actual_ub(detail.avg_actual_ub)
        # 数据加和后重新计算下woi
        all_detail.set_woi()


def publish(fiscal_week: str, sub_lob: str, operator: str):
    DemandPublishRecord.batch_insert([{
        "fiscal_week": fiscal_week,
        "sub_lob": sub_lob,
        "demand": FINAL_DEMAND,
        "publish_time": datetime.now(),
        "update_time": datetime.now(),
        "operator": operator,
    }])
    try:
        #  publish后 发送邮件
        for rtm in RTMS_NO_RETAIL:
            url = get_rtm_mail_fast_url(rtm)
            TemplateEmail().final_demand_publish_notice(datetime.now().strftime("%Y-%m-%d %H:%M:%S"), sub_lob, rtm, url)
    except Exception as e:
        logger.error(traceback.format_exc())
    return


def upload(fiscal_week: str, file, uploader):
    upload_df = pd.read_excel(file)
    # 将千分位转为数字
    for col in ['Base Demand', 'Delta Demand']:
        upload_df[col] = upload_df[col].apply(lambda x: float(str(x).replace(',', '')) if x else 0)
    # 文件校验
    validate_upload_content(upload_df, fiscal_week)

    # 解析上传的文件，sub_lob forecast_version 分组 key=sub_lob {key = forecast_version,value=list[FinalDemandUpload]}
    sublob_version_upload_dict: dict[str: {str: list[FinalDemandUpload]}] = {}
    grouped = upload_df.groupby('Sub-LOB')
    for sub_lob, group in grouped:
        sublob_version_upload_dict[sub_lob] = {}
        version_grouped = group.groupby('As of Week')
        for version, version_group in version_grouped:
            final_demand_list = [
                FinalDemandUpload(
                    row['As of Week'],
                    row['Sold-to ID'],
                    row['MPN'],
                    row['Base Demand'],
                    row['Delta Demand']
                )
                for _, row in version_group.iterrows()
            ]
            sublob_version_upload_dict[sub_lob][version] = final_demand_list

    # 因为没有上传的数据要设置为0 ，所以需要查出来原本的数据
    sub_lobs = list(sublob_version_upload_dict.keys())
    sold_to_df = DemandBySoldtoPool.get_by_fiscal_week(fiscal_week, RTMS_NO_RETAIL, sub_lobs)
    fill_nan_to_none(sold_to_df)
    soldto_demand_list = sold_to_df.to_dict(orient='records')
    # 按照 sub_lob 进行分组
    soldto_demand_list.sort(key=lambda x: x['sub_lob'])
    grouped_by_sub_lob = {key: list(group) for key, group in groupby(soldto_demand_list, key=lambda x: x['sub_lob'])}
    update_list = __build_update_list(grouped_by_sub_lob, sublob_version_upload_dict)
    if not update_list:
        return
    # 更新前先查询发布状态 对于之前已经发布的数据 再次更新时发送邮件
    sub_lob_publish_details = get_sub_lob_publish_detail(fiscal_week)

    fields_to_update = ['base_demand_cw1_adjusted', 'df_cw1_adjusted', 'base_demand_cw2_adjusted', 'df_cw2_adjusted']
    fill_dict_nan_to_none(fields_to_update, update_list)

    DemandBySoldtoPool.batch_update(update_list, fields_to_update + ['cw1_update_time', 'cw2_update_time'])
    update_region_pool(update_list)
    for publish_detail in sub_lob_publish_details:
        sublob = publish_detail.sub_lob
        if sublob not in sub_lobs:
            continue
        if publish_detail.is_publish():
            try:
                for rtm in RTMS_NO_RETAIL:
                    url = get_rtm_mail_fast_url(rtm)
                    TemplateEmail().final_demand_upload_notice(
                        publish_detail.publish_time.strftime("%Y-%m-%d %H:%M:%S"), sublob, rtm, url)
            except Exception as e:
                logger.error(traceback.format_exc())


def update_region_pool(update_list):
    # 按照region mpn 聚合update_list 的df_cw1_adjusted df_cw2_adjusted 保存到demand_by_region_pool
    update_soldto_df = pd.DataFrame(update_list)[
        ['fiscal_week', 'region', COLUMN_MPN, 'df_cw1_adjusted', 'df_cw2_adjusted']]
    country_df = update_soldto_df.groupby(['fiscal_week', 'region', COLUMN_MPN])[
        'df_cw1_adjusted', 'df_cw2_adjusted'].sum().reset_index()
    country_df = fill_nan_to_none(country_df)
    update_list = []
    if 'df_cw1_adjusted' in country_df.columns:
        update_list.append('df_cw1_adjusted')
    if 'df_cw2_adjusted' in country_df.columns:
        update_list.append('df_cw2_adjusted')
    if update_list:
        # 7.22  产品确认: demand comparison 更新时间要求展示df最后的更新时间
        update_list.append('update_time')
        DemandByRegionPool.batch_update(country_df.to_dict(orient='records'),update_list)


def __build_update_list(grouped_by_sub_lob, sublob_version_upload_dict):
    now = datetime.now()
    update_list = []
    for sub_lob, group in grouped_by_sub_lob.items():
        version_upload = sublob_version_upload_dict[sub_lob]

        adjust_cw_1: bool = 'CW+1' in version_upload
        adjust_cw_2: bool = 'CW+2' in version_upload

        if not adjust_cw_1 and not adjust_cw_2:
            continue

        demand_soldto_cw_1 = {d.get_key(): d for d in version_upload['CW+1']} if adjust_cw_1 else {}
        demand_soldto_cw_2 = {d.get_key(): d for d in version_upload['CW+2']} if adjust_cw_2 else {}

        for demand in group:
            key = str(demand['sold_to_id']) + '-' + demand['mpn']
            if adjust_cw_1 and adjust_cw_2:
                has_cw_1 = key in demand_soldto_cw_1
                has_cw_2 = key in demand_soldto_cw_2
                # 8.1 base demand adjust
                demand['base_demand_cw1_adjusted'] = demand_soldto_cw_1[key].final_dn if has_cw_1 else 0
                demand['df_cw1_adjusted'] = demand_soldto_cw_1[key].final_dn + demand_soldto_cw_1[
                    key].delta_demand if has_cw_1 else 0
                demand['base_demand_cw2_adjusted'] = demand_soldto_cw_2[key].final_dn if has_cw_2 else 0
                demand['df_cw2_adjusted'] = demand_soldto_cw_2[key].final_dn + demand_soldto_cw_2[
                    key].delta_demand if has_cw_2 else 0
                demand['cw1_update_time'] = now
                demand['cw2_update_time'] = now
                update_list.append(demand)
            elif adjust_cw_1:
                has_cw_1 = key in demand_soldto_cw_1
                demand['base_demand_cw1_adjusted'] = demand_soldto_cw_1[key].final_dn if has_cw_1 else 0
                demand['df_cw1_adjusted'] = demand_soldto_cw_1[key].final_dn + demand_soldto_cw_1[
                    key].delta_demand if has_cw_1 else 0
                # 计算CW2  dn  cw2 计算  df cw2 计算
                if (demand[BASE] is not None
                        and demand['normalized_fcst_cw2'] is not None and demand['normalized_fcst_cw3'] is not None
                        and demand['normalized_fcst_cw4'] is not None and demand['normalized_fcst_cw5'] is not None
                        and demand['twos'] is not None and demand['base_demand_cw1_adjusted'] is not None):
                    demand['base_demand_cw2_adjusted'] = (demand[BASE] + demand['normalized_fcst_cw2']
                                                 + np.mean(demand['normalized_fcst_cw3'] + demand['normalized_fcst_cw4'] + demand['normalized_fcst_cw5']) * demand['twos']
                                                 - demand['base_demand_cw1_adjusted'])
                else:
                    demand['base_demand_cw2_adjusted'] = None

                if (demand[BASE] is not None
                        and demand['normalized_fcst_cw2'] is not None and demand['normalized_fcst_cw3'] is not None
                        and demand['normalized_fcst_cw4'] is not None and demand['normalized_fcst_cw5'] is not None
                        and demand['twos'] is not None and demand['df_cw1_adjusted'] is not None):
                    demand['df_cw2_adjusted'] = (demand[BASE] + demand['normalized_fcst_cw2']
                                                 + np.mean(demand['normalized_fcst_cw3'] + demand['normalized_fcst_cw4'] + demand['normalized_fcst_cw5']) * demand['twos']
                                                 - demand['df_cw1_adjusted'])
                else:
                    demand['df_cw2_adjusted'] = None
                demand['cw1_update_time'] = now
                # demand['cw2_update_time'] = now 计算的数据不更新时间
                update_list.append(demand)
            else:
                has_cw_2 = key in demand_soldto_cw_2
                demand['base_demand_cw2_adjusted'] = demand_soldto_cw_2[key].final_dn if has_cw_2 else 0
                demand['df_cw2_adjusted'] = demand_soldto_cw_2[key].final_dn + demand_soldto_cw_2[
                    key].delta_demand if has_cw_2 else 0
                demand['cw2_update_time'] = now
                update_list.append(demand)
    return update_list


def fill_dict_nan_to_none(fields_to_process: list, data_list: list):
    # 遍历字典并替换 NaN 为 None
    for data in data_list:
        for key in fields_to_process:
            data[key] = None if data[key] is None or math.isnan(data[key]) else data[key]


def get_sub_lob_publish_detail(fiscal_week: str) -> list[DemandPublishDetail]:
    result = []
    # 比较当周是否已经结束
    is_end: bool = cw_is_end(fiscal_week)
    # 查询publish数据
    sublob_publish_records = DemandPublishRecord.get_by_fiscal_week(fiscal_week)
    # 按照 key = sub_lob 转为 dict
    sublob_to_record = {}
    for publish_record in sublob_publish_records:
        if publish_record.sub_lob not in sublob_to_record:
            sublob_to_record[publish_record.sub_lob] = publish_record
    # 查询sold to表 cw1 cw2 的 update time
    sublob_adjust_list = DemandBySoldtoPool.get_sublob_adjust_info_by_fiscal_week(fiscal_week, RTMS_NO_RETAIL)
    # 按照sublob_adjust_list 处理数据  根据 cw1 cw2 update_time 是否为null 判断是否修改过
    for sublob_adjust in sublob_adjust_list:
        sub_lob = sublob_adjust['sub_lob']
        publish_time = None
        cw1_update_time = sublob_adjust['cw1_update_time']
        cw2_update_time = sublob_adjust['cw2_update_time']
        if sub_lob not in sublob_to_record:
            # 一次都没有发布过
            publish_status = DemandPublishStatusEnum.NO_FEEDBACK.value if is_end else DemandPublishStatusEnum.WAITING_TO_SETUP.value
        else:
            # 发布过需要比较发布时间和 max（cw1 cw2 修改时间）
            publish_record = sublob_to_record[sub_lob]
            publish_time = publish_record.publish_time
            is_after = publish_is_after_adjust(cw1_update_time, cw2_update_time, publish_record.publish_time)
            if is_after:
                publish_status = DemandPublishStatusEnum.COMPLETED.value
            else:
                publish_status = DemandPublishStatusEnum.NO_FEEDBACK.value if is_end else DemandPublishStatusEnum.WAITING_TO_SETUP.value
        result.append(DemandPublishDetail(fiscal_week=fiscal_week, demand=FINAL_DEMAND, lob='iPhone',
                                          sub_lob=sub_lob, publish_status=publish_status, publish_time=publish_time,
                                          cw1_update_time=cw1_update_time, cw2_update_time=cw2_update_time))
    return result


def publish_is_after_adjust(cw1_update_time, cw2_update_time, publish_time) -> bool:
    # publish_time 肯定不为空， 发布过需要比较发布时间和 max（cw1 cw2 修改时间）
    if cw1_update_time is None and cw2_update_time is None:
        return True
    # 获取cw1_update_time, cw2_update_time 其中的较大值
    latest_update_time = max(filter(None, [cw1_update_time, cw2_update_time]))
    # 与 publish_time 比较
    return publish_time > latest_update_time


def cw_is_end(fiscal_week: str) -> bool:
    # fiscal_week 是否结束
    current_week = FiscalWeekContainer().get_current_week()
    return FiscalWeek(current_week).fiscal_week_int > FiscalWeek(fiscal_week).fiscal_week_int


def is_all_sub_lob_publish(fiscal_week: str) -> bool:
    sub_lob_publish_details = get_sub_lob_publish_detail(fiscal_week)
    if not sub_lob_publish_details:
        return False
    flag = True
    for publish_detail in sub_lob_publish_details:
        if not publish_detail.is_publish():
            flag = False
            break
    return flag


def get_all_final_demand_template_df(fiscal_week):
    delta_demand_result = FinalDemandResult(
        fiscal_week, FINAL_DEMAND, 0, [], StrRTMCPF)
    dataframe = delta_demand_result.get_dataframe()
    # 将千分位转为数字
    for col in ['Base Demand', 'Delta Demand']:
        dataframe[col] = dataframe[col].apply(lambda x: float(str(x).replace(',', '')) if x else 0)
    return dataframe


def validate_upload_content(upload_df: pd.DataFrame, fiscal_week: str):
    # 数据表头，应与数据模板保持完全一致，不可修改。
    if not list(upload_df.columns) == FINAL_DEMAND_TEMPLATE_COLUMNS:
        raise ErrorExcept(ErrCode.FileUploadError,
                          "Invalid data fields in the table, please follow the template and don't not change any field.")

    rules = {
        "As of Week": ["required", AllowedRule(["CW+1", "CW+2"], "The following data row(s) are invalid for wrong fiscal week coverage: ")],
        "Region": ["required", AllowedRule([REGION_CM], "The following data row(s) are invalid for wrong Region: ")],
    }
    validator = FileValidator(upload_df, rules=rules)
    has_pass_validation, row, errors = validator.do_validate()
    # 返回文件校验的错误信息
    priority_validation = ["As of Week", "Region"]
    FileValidator.handle_error_msg(
        has_pass_validation, errors, priority_validation)

    pool = ThreadPoolExecutor(max_workers=3)
    region_rtm_subrtm_pool = pool.submit(
        OdsFastCPFSoldToMappingIPhone.get_region_rtm_subrtm)
    lob_sublob_pool = pool.submit(
        OdsFastCPFActiveSKULob.list_lob_sublob, Lob.IPHONE.value)
    final_demand_template_pool = pool.submit(
        get_all_final_demand_template_df, fiscal_week)
    region_rtm_subrtm_list = region_rtm_subrtm_pool.result()
    lob_sublob_list = lob_sublob_pool.result()
    final_demand_template_df = final_demand_template_pool.result()
    pool.shutdown()

    # Region / RTM / Sub-RTM
    invalid_region_rtm_sub_rtm_condition = ~upload_df[[
        'Region', 'RTM', 'Sub-RTM']].apply(lambda x: tuple(x) in region_rtm_subrtm_list, axis=1)
    invalid_region_rtm_sub_rtm_rows = upload_df.loc[invalid_region_rtm_sub_rtm_condition].index.tolist(
    )
    if invalid_region_rtm_sub_rtm_rows:
        raise_upload_error(
            "The followings row(s) are invalid for wrong Region / RTM / Sub-RTM name or mapping relationship: ",
            invalid_region_rtm_sub_rtm_rows)

    # LOB / Sub-LOB
    invalid_lob_sub_lob_condition = ~upload_df[[
        'LOB', 'Sub-LOB']].apply(lambda x: tuple(x) in lob_sublob_list, axis=1)
    invalid_lob_sub_lob_rows = upload_df.loc[invalid_lob_sub_lob_condition].index.tolist(
    )
    if invalid_lob_sub_lob_rows:
        raise_upload_error(
            "The followings row(s) are invalid for wrong LOB / Sub-LOB name or mapping relationship: ",
            invalid_lob_sub_lob_rows)

    # 数据行范围检查，
    # 以财周 & Sold-to & MPN为最细颗粒度进行比对，用户上传的数据范围不可超出完整模板的数据范围基准。
    # 即相较于模板，用户上传数据范围行可少，但不能超出模板。
    # （完整模板的数据范围基准即全部Sub-LOB下的数据明细行，不限于单个机型）
    upload_df["Sold-to ID"] = upload_df["Sold-to ID"].astype(str)
    merge_columns = ["Sold-to ID", "MPN"]
    merged_df = pd.merge(upload_df[merge_columns], final_demand_template_df[merge_columns],
                         how='left', indicator=True, left_index=True, right_index=True)
    not_in_template = merged_df[merged_df['_merge'] == 'left_only']
    if False and not not_in_template.empty:
        raise_upload_error(
            "The following data row(s) are uncovered by data template: ",
            not_in_template.index.tolist())

    # 在财周 & Sold-to ID & MPN的颗粒度上，不允许出现重复的数据行。
    is_duplicate = upload_df.duplicated(
        subset=['As of Week', 'Sold-to ID', 'MPN'], keep=False)
    duplicate_rows = upload_df.loc[is_duplicate].index.tolist()
    if duplicate_rows:
        raise_upload_error(
            "The following data row(s) are invalid for duplicated values: ", duplicate_rows)

    # Demand数值检查
    # 【补充】校验范围说明：该校验不包括RTM = “Enterprise”、“Education”的数据，仅针对RTM = “Mono”、“Multi”、“Online”、“Carrier”的数据行。
    # 在MPN & 财周 & Region的颗粒度上，Normalized Demand和Delta Demand的总和应与原模板内的总数完全相等。
    # 【07.31更新】在Region & 财周 & MPN的颗粒度上，用户上传的Base Demand和Delta Demand两列的总和应与原模板内的总数完全相等。Ideal Demand和Final Demand两列不校验。
    filtered_upload_df = upload_df[upload_df['RTM'].isin(
        ['Mono', 'Multi', 'Online', 'Carrier'])]
    filtered_template_df = final_demand_template_df[final_demand_template_df['RTM'].isin(
        ['Mono', 'Multi', 'Online', 'Carrier'])]
    overlap_columns = ["As of Week", "MPN", "Region"]
    group_upload_df = (filtered_upload_df
                       .groupby(overlap_columns)
                       .agg({'Base Demand': 'sum', 'Delta Demand': 'sum'})
                       .reset_index())
    # 需要DD+DN的校验，需要在group_upload_df中添加一列，并与group_template_df进行对比
    group_upload_df['Total'] = (group_upload_df["Base Demand"] + group_upload_df["Delta Demand"])
    fill_empty_str_to_nan_columns = ['Base Demand', 'Delta Demand']
    filtered_template_df[fill_empty_str_to_nan_columns] = filtered_template_df[fill_empty_str_to_nan_columns].replace(
        '', np.nan)
    group_template_df = (filtered_template_df
                         .groupby(overlap_columns)
                         .agg({'Base Demand': 'sum', 'Delta Demand': 'sum'})
                         .reset_index())
    group_template_df['Total'] = (group_template_df["Base Demand"] + group_template_df["Delta Demand"])
    group_merge_columns = [*overlap_columns, *fill_empty_str_to_nan_columns, "Total"]
    merge_group_df = pd.merge(group_upload_df[group_merge_columns], group_template_df[group_merge_columns],
                              on=overlap_columns, how='left', indicator=True, suffixes=('_left', '_right'))
    if False and (merge_group_df["Total_left"] != merge_group_df["Total_right"]).any():
        raise ErrorExcept(ErrCode.FileUploadError,
                          "Please make sure the summed Final Demand of each week remains unchanged on  MPN level (ENT/EDU not counted).")

# 调用时机：
# 1. final demand 计算完成后
# 2. 上传final demand文件
def deal_jd_self_run_final_demand(fiscal_week: str):
    '''京东拆分：计算jd self-run 的数据，把拆分之后的内容进行重新分配'''
    # 将字符串周转成数字的周
    fiscal_week_year = FiscalYearWeek.get_fiscal_week_year_by_week_date(fiscal_week)
    
    # 获取4家sold_to各自原始的shipment值
    df = JDShipmentPlan.query_by_week(fiscal_week_year)
    # 下面三个字段的类型数据库中为string，在ORM中写成Float并不能直接转化
    df["shipment_plan_cw"] = df["shipment_plan_cw"].astype(float)
    df["shipment_plan_cw1"] = df["shipment_plan_cw1"].astype(float)
    df["shipment_plan_cw2"] = df["shipment_plan_cw2"].astype(float)
    # 获取mpn白名单
    allow_list_mpn = DemandBySoldtoPool.get_distinct_mpn_by_week(fiscal_week)
    
    # 只取白名单中有的数据
    df = df[df["mpn"].isin(allow_list_mpn)]
    
    # 找到最新的JD self-run的比例
    jd_mixes = WeeklyDemandSetting.get_mix_setting_by_fiscal_week(fiscal_week, name_type=1)
    # 如果当周没有设置最新的mix，则使用默认的比例0.25，按照sub_lob纬度分
    if len(jd_mixes) == 0:
        # 获取本周的sub_lob list
        sub_lob_list = DemandBySoldtoPool.get_distinct_sub_lob_by_week(fiscal_week)
        new_mix = []
        # 生成sold_to sub_lob de 笛卡尔积
        for sold_to_id in JD_SOLDTO.keys():
            for sub_lob in sub_lob_list:
                new_mix.append(
                    DemandDataConfig(fiscal_week=fiscal_week,
                                    sold_to_id=sold_to_id,
                                    lob='iPhone',
                                    sub_lob=sub_lob,
                                    jd_self_run_mix_cw1=0.25,
                                    jd_self_run_mix_cw2=0.25,
                                    adjusted_demand_cw1=0,
                                    adjusted_demand_cw2=0,
                                    name_type=1)
                )
        jd_mixes = new_mix
    jd_mixes_record = [item.to_dict() for item in jd_mixes]
    df_jd_mix = pd.DataFrame(jd_mixes_record)
        
    df = df.merge(df_jd_mix[["sold_to_id", "sub_lob", "jd_self_run_mix_cw1", "jd_self_run_mix_cw2"]],
                  on=["sold_to_id", "sub_lob"])
    
    # 获取总的shipment plan
    df_total_shipment = DemandBySoldtoPool.get_jd_self_run_shipment_by_week(fiscal_week)
    
    # 计算新的final demand
    df = df.merge(df_total_shipment, on=['mpn'])
    df['df_cw1'] = df["shipment_plan_cw1"] + df["jd_self_run_mix_cw1"] * (df["df_cw1_origin"] - df["shipment_plan_cw1_total"])
    df['df_cw2'] = df["shipment_plan_cw2"] + df["jd_self_run_mix_cw2"] * (df["df_cw2_origin"] - df["shipment_plan_cw2_total"])
    df['df_cw1_adjusted'] = df["shipment_plan_cw1"] + df["jd_self_run_mix_cw1"] * (df["df_cw1_adjusted_origin"] - df["shipment_plan_cw1_total"])
    df['df_cw2_adjusted'] = df["shipment_plan_cw2"] + df["jd_self_run_mix_cw2"] * (df["df_cw2_adjusted_origin"] - df["shipment_plan_cw2_total"])
    
    # 需要重新计算 Ideal Demand, Normalized Demand, Base Demand
    # ideal demand
    df['di_cw1'] = df["shipment_plan_cw1"] + df["jd_self_run_mix_cw1"] * (df["di_cw1_origin"] - df["shipment_plan_cw1_total"])
    df['di_cw2'] = df["shipment_plan_cw2"] + df["jd_self_run_mix_cw2"] * (df["di_cw2_origin"] - df["shipment_plan_cw2_total"])
    
    # normalized demand
    # df['dn_cw1'] = df["shipment_plan_cw1"] + df["jd_self_run_mix_cw1"] * (df["dn_cw1_origin"] - df["shipment_plan_cw1_total"])
    # df['dn_cw2'] = df["shipment_plan_cw2"] + df["jd_self_run_mix_cw2"] * (df["dn_cw2_origin"] - df["shipment_plan_cw2_total"])
    # df['dn_cw1_adjusted'] = df["shipment_plan_cw1"] + df["jd_self_run_mix_cw1"] * (df["dn_cw1_adjusted_origin"] - df["shipment_plan_cw1_total"])
    # df['dn_cw2_adjusted'] = df["shipment_plan_cw2"] + df["jd_self_run_mix_cw2"] * (df["dn_cw2_adjusted_origin"] - df["shipment_plan_cw2_total"])
    df['dn_cw1'] = df["jd_self_run_mix_cw1"] * (df["dn_cw1_origin"])
    df['dn_cw2'] = df["jd_self_run_mix_cw2"] * (df["dn_cw2_origin"])
    df['dn_cw1_adjusted'] = df["jd_self_run_mix_cw1"] * (df["dn_cw1_adjusted_origin"])
    df['dn_cw2_adjusted'] = df["jd_self_run_mix_cw2"] * (df["dn_cw2_adjusted_origin"])

    # base demand
    df['base_demand_cw1'] = df["shipment_plan_cw1"] + df["jd_self_run_mix_cw1"] * (df["base_demand_cw1_origin"] - df["shipment_plan_cw1_total"])
    df['base_demand_cw2'] = df["shipment_plan_cw2"] + df["jd_self_run_mix_cw2"] * (df["base_demand_cw2_origin"] - df["shipment_plan_cw2_total"])
    df['base_demand_cw1_adjusted'] = df["shipment_plan_cw1"] + df["jd_self_run_mix_cw1"] * (df["base_demand_cw1_adjusted_origin"] - df["shipment_plan_cw1_total"])
    df['base_demand_cw2_adjusted'] = df["shipment_plan_cw2"] + df["jd_self_run_mix_cw2"] * (df["base_demand_cw2_adjusted_origin"] - df["shipment_plan_cw2_total"])
    
    # 输出数据库结构的数据
    df = df[["sold_to_id", "sold_to_name", "sub_lob", "mpn",
             "df_cw1", "df_cw2", "df_cw1_adjusted", "df_cw2_adjusted",
             "shipment_plan_cw", "shipment_plan_cw1", "shipment_plan_cw2", "eoh_lw",
             "di_cw1", "di_cw2", "dn_cw1", "dn_cw2", "dn_cw1_adjusted", "dn_cw2_adjusted",
             "base_demand_cw1", "base_demand_cw2", "base_demand_cw1_adjusted", "base_demand_cw2_adjusted"]]
    
    df["fiscal_week"] = fiscal_week
    
    # 本地调试
    # df.to_excel(f"/Users/<USER>/Downloads/jd_self_run_final_demand_{fiscal_week}_test.xlsx", index=False)

    # 保存到数据库中
    df = fill_nan_to_none(df)
    update_fields = ["sold_to_name", "sub_lob", "df_cw1", "df_cw2", 
                     "df_cw1_adjusted", "df_cw2_adjusted", 
                     "shipment_plan_cw", "shipment_plan_cw1", "shipment_plan_cw2", "eoh_lw",
                     "di_cw1", "di_cw2", "dn_cw1", "dn_cw2", "dn_cw1_adjusted", "dn_cw2_adjusted",
                    "base_demand_cw1", "base_demand_cw2", 
                    "base_demand_cw1_adjusted", "base_demand_cw2_adjusted"]
    JdSelfRunActualSoldToRepository.batch_update(
        df.to_dict("records"),
        update_fields
    )


def upload_v3(fiscal_week: str, file):

    # 1 获取demand3.0数据，计算过需要拼接
    finalized_demand_list: list[dict] = get_finalized_demand(fiscal_week)
    upload_df = pd.read_excel(file, dtype={'Sold-to ID': str})
    upload_data_list = upload_df.to_dict(orient='records')
    check_columns(upload_data_list)
    # 替换
    replace_carrier_mpn_to_rp_mpn(ori_data_list=upload_data_list, replace_field='MPN', rtm_field='RTM')
    # 文件校验
    validate_finalized_demand_upload_content(upload_data_list, finalized_demand_list)
    # 2 新增字段ori_finalized_demand_v2_cw1,original_demand_v3_cw2 ,将原数据复制到original上
    for finalized_demand in finalized_demand_list:
        finalized_demand['ori_finalized_demand_v2_cw1'] = finalized_demand['finalized_demand_v2_cw1']
        finalized_demand['ori_finalized_demand_v2_cw2'] = finalized_demand['finalized_demand_v2_cw2']
        # 上传数据赋值
        for upload_data in upload_data_list:
            if finalized_demand['sold_to_id'] == upload_data['Sold-to ID'] and finalized_demand['mpn_id'] == upload_data['MPN']:
                finalized_demand['finalized_demand_v2_cw1'] = upload_data['Demand 3.0 CW+1']
                finalized_demand['finalized_demand_v2_cw2'] = upload_data['Demand 3.0 CW+2']

    # 3 保存数据
    fields_to_update = ['finalized_demand_v2_cw1', 'finalized_demand_v2_cw2', 'update_time', 'ori_finalized_demand_v2_cw1', 'ori_finalized_demand_v2_cw2']
    if finalized_demand_list:
        fields_to_insert = [
            'fiscal_week_year', 'reseller_id', 'reseller_name', 'reseller_tier',
            'sold_to_name', 'rtm', 'sub_rtm', 'lob', 'sub_lob', 'nand', 'color',
            'mpn_type', 'mpn_desc', 'mpn_order', 'forecast_feedback_cw', 'forecast_feedback_cw1',
            'forecast_feedback_cw2', 'forecast_feedback_cw3', 'forecast_feedback_cw4', 'forecast_feedback_cw5',
            'forecast_feedback_cw6', 'forecast_feedback_cw7', 'forecast_feedback_cw8', 'forecast_feedback_cw9',
            'forecast_feedback_cw10', 'forecast_feedback_cw11', 'forecast_feedback_cw12', 'trial_demand_cw1',
            'trial_demand_cw2', 'trial_po_needed_cw1', 'trial_po_needed_cw2', 'finalized_demand_v2_cw1',
            'finalized_demand_v2_cw2', 'forecast_cw', 'forecast_cw1', 'forecast_cw2', 'forecast_cw3',
            'forecast_cw4', 'forecast_cw5', 'forecast_cw6', 'forecast_cw7', 'forecast_cw8', 'forecast_cw9',
            'forecast_cw10', 'forecast_cw11', 'forecast_cw12', 'shipment_plan_cw', 'shipment_plan_cw1',
            'shipment_plan_cw2', 'ub_eoh_lw', 'final_demand_cw1', 'final_demand_cw2', 'po_needed_cw1',
            'po_needed_cw2', 'open_backlog_over_published_sp', 'create_time', 'update_time', 'publish_time', 'publish_status',
            'ori_finalized_demand_v2_cw1', 'ori_finalized_demand_v2_cw2'
        ]
        DemandFeedbackBySoldTo.batch_update(objs=finalized_demand_list,
                                              fields_to_insert=fields_to_insert,
                                              fields_to_update=fields_to_update)
    # 4 修改region表数据 finalized_demand_list数据按照mpn汇总 ['finalized_demand_v2_cw1', 'finalized_demand_v2_cw2', 'update_time', 'ori_finalized_demand_v2_cw1', 'ori_finalized_demand_v2_cw2']
    def sum_by_mpn(data):
        result = {}
        for item in data:
            mpn = item["mpn_id"]
            if mpn not in result:
                # 初始化分组
                result[mpn] = {
                    "finalized_demand_v2_cw1": 0,
                    "finalized_demand_v2_cw2": 0,
                    "ori_finalized_demand_v2_cw1": 0,
                    "ori_finalized_demand_v2_cw2": 0,
                }
            # 累加各字段
            result[mpn]["finalized_demand_v2_cw1"] += item["finalized_demand_v2_cw1"] if item["finalized_demand_v2_cw1"] else 0
            result[mpn]["finalized_demand_v2_cw2"] += item["finalized_demand_v2_cw2"] if item["finalized_demand_v2_cw2"] else 0
            result[mpn]["ori_finalized_demand_v2_cw1"] += item["ori_finalized_demand_v2_cw1"] if item["ori_finalized_demand_v2_cw1"] else 0
            result[mpn]["ori_finalized_demand_v2_cw2"] += item["ori_finalized_demand_v2_cw2"] if item["ori_finalized_demand_v2_cw2"] else 0
        return result
    mpn_result = sum_by_mpn(finalized_demand_list)
    region_data_list = ForecastFeedbackRegion.find_by_fiscal_week_name(fiscal_week_name=fiscal_week)
    now = datetime.now()
    for region_data in region_data_list:
        mpn = region_data['mpn']
        mpn_data = mpn_result.get(mpn, {})
        region_data["finalized_demand_v2_cw1"] = mpn_data.get("finalized_demand_v2_cw1")
        region_data["finalized_demand_v2_cw2"] = mpn_data.get("finalized_demand_v2_cw2")
        region_data["ori_finalized_demand_v2_cw1"] = mpn_data.get("ori_finalized_demand_v2_cw1")
        region_data["ori_finalized_demand_v2_cw2"] = mpn_data.get("ori_finalized_demand_v2_cw2")
        region_data['update_time'] = now
    region_insert_fields = [col.name for col in DemandFeedbackByRegion.__mapper__.columns if col.name not in ["id","fiscal_week", "mpn"]]
    DemandFeedbackByRegion.batch_update(region_data_list, fields_to_update=fields_to_update, fields_to_insert=region_insert_fields)


def check_columns(upload_data_list):
    # 至少应包含Sold-to ID、MPN、Demand 3.0 CW+1、Demand 3.0 CW+2四列
    required_columns = ["Sold-to ID", "MPN", "Demand 3.0 CW+1", "Demand 3.0 CW+2"]
    missing_columns = set(required_columns) - set(list(upload_data_list[0].keys()))
    if missing_columns:
        raise ErrorExcept(ErrCode.TemplateError,
                          f"Data validation failed - The following columns are missing: {','.join(sorted(missing_columns))}")


def get_finalized_demand(fiscal_week) -> list[dict]:
    sold_to_data: list[dict] = DemandFeedbackBySoldTo().find_by_fiscal_week_name(fiscal_week_name=fiscal_week)

    # 判断是否计算过
    is_calculated = True if sold_to_data is not None and len(sold_to_data) > 0 else False
    # 判断是否已经发布
    if is_calculated:
        demand3_publish_status = sold_to_data[0]['demand3_publish_status']
        if demand3_publish_status is not None and demand3_publish_status == 1:
            raise ErrorExcept(ErrCode.StatusError, f"Demand has been published")

    now = datetime.now()
    if not is_calculated:
        sold_to_data = ForecastFeedbackSoldto().find_by_fiscal_week_name(fiscal_week_name=fiscal_week)
        # 给sold_to_data添加finalized_demand_v2_cw1,finalized_demand_v2_cw2,create_time和update_time字段
        sold_to_data = [dict(item, finalized_demand_v2_cw1=None, finalized_demand_v2_cw2=None,
                             create_time=now, update_time=now) for item in sold_to_data]
    for item in sold_to_data:
        # 如果finalized_demand_v2为空，则用trail_demand替换
        if item['finalized_demand_v2_cw1'] is None:
            item['finalized_demand_v2_cw1'] = item['trial_demand_cw1']
        if item['finalized_demand_v2_cw2'] is None:
            item['finalized_demand_v2_cw2'] = item['trial_demand_cw2']
        item['update_time'] = now
    return sold_to_data


def validate_finalized_demand_upload_content(upload_data_list: list[dict], finalized_demand_list: list[dict]):

    __find_duplicates(upload_data_list)
    __validate_data_coverage(upload_data_list, finalized_demand_list)
    __validate_demand_int(upload_data_list)


def __find_duplicates(upload_data_list):
    seen = defaultdict(list)
    for index, row in enumerate(upload_data_list):
        key = (row.get("Sold-to ID"), row.get("MPN"))
        seen[key].append(index+2)
    # 筛选出重复的行（出现次数大于1的组合）
    duplicate_rows = []
    for indices in seen.values():
        if len(indices) > 1:
            duplicate_rows.extend(indices)

    if duplicate_rows:
        # rows_str = ", ".join(map(str, sorted(duplicate_rows)))
        raise ErrorExcept(
            ErrCode.FileUploadError,
            {
                "message": f"Data validation failed - The following data row(s) are invalid for duplicated values: ",
                "row_index": duplicate_rows
            }
        )


def __validate_data_coverage(upload_data_list: list[dict], finalized_demand_list: list[dict]) -> None:
    demand_combinations = {
        (item.get("sold_to_id"), item.get("mpn_id"))
        for item in finalized_demand_list
    }
    # 找出不在需求结果中的行
    uncovered_rows = []
    for idx, row in enumerate(upload_data_list):
        key = (row.get("Sold-to ID"), row.get("MPN"))
        if key not in demand_combinations:
            uncovered_rows.append(idx+2)

    # 如果有不匹配的数据，抛出错误
    if uncovered_rows:
        raise ErrorExcept(
            ErrCode.FileUploadError,
            {
                "message": f"Data validation failed - The following data row(s) are uncovered by demand result: ",
                "row_index": uncovered_rows
            }
        )


def __validate_demand_int(upload_data_list: list[dict]) -> None:
    invalid_rows = []
    demand_fields = ["Demand 3.0 CW+1", "Demand 3.0 CW+2"]

    for idx, row in enumerate(upload_data_list):  # 行号从1开始
        for field in demand_fields:
            if not is_non_negative_integer(row.get(field)):
                invalid_rows.append(idx+2)
    if invalid_rows:
        raise ErrorExcept(
            ErrCode.FileUploadError,
            {
                "message": f"Data validation failed - the following rows have invalid Demand values: ",
                "row_index": sorted(set(invalid_rows))
            }
        )
