import pandas
import pandas as pd
from pandas import DataFrame

from data.cpf_data_source import OdsFastCPFActiveSKULob, OdsFastCPFSoldToMappingIPhone
from domain.demand.entity.const import RTM_CARRIER
from util.util import common_sort_df


# 笛卡尔乘积类 用ods_fast_cpf_active_sku_lob表和ods_fast_cpf_sold_to_mapping_iphone表生成笛卡尔乘积
class CartesianProduct:
    def __init__(self, fiscal_week: str, region: str, lob: str, rtms: list[str], is_rp_mapping: bool = True):
        self.fiscal_week = fiscal_week
        self.region = region
        self.lob = lob
        self.rtms = rtms
        self.is_rp_mapping = is_rp_mapping

        self.sku_list_df = None
        self.origin_df = None
        self.soldto_mapping_df = None

    # 获取sku_list_df和soldto_mapping_df
    def __get_sku_list_and_soldto_mapping(self):
        self.soldto_mapping_df = pandas.DataFrame(
            OdsFastCPFSoldToMappingIPhone.list_sold_to_ids_df(self.rtms, self.region))
        if len(self.soldto_mapping_df) == 0:
            raise Exception("soldtoids_df is empty.")

        self.sku_list_df = pandas.DataFrame(
            OdsFastCPFActiveSKULob.get_data_by_lob_and_region(self.lob, self.region))
        if len(self.sku_list_df) == 0:
            raise Exception("hr_lr_mpns is empty.")

    # 合并数据
    def __cartesian_product(self):
        # 合并数据
        # 给sku_list_df和soldto_mapping_df添加key字段
        self.sku_list_df = self.sku_list_df.assign(key=1)
        self.soldto_mapping_df = self.soldto_mapping_df.assign(key=1)
        # 通过key字段合并hr_lr_mpns和soldtoids_df，获取所有sold to id和key值的组合
        self.origin_df = pd.merge(self.sku_list_df.assign(key=1), self.soldto_mapping_df.assign(key=1), on='key').drop('key', axis=1)
        # 去除rtm是carrier，且rp_mpn_mapped_from_carrier为None的数据
        self.origin_df = self.origin_df[~((self.origin_df['rtm'] == RTM_CARRIER) & (self.origin_df['rp_mpn_mapped_from_carrier'].isnull()))]
        # 去除rtm不是carrier，且rp_mpn_mapped_from_carrier不为None且rp_mpn_mapped_from_carrier!= mpn的数据
        self.origin_df = self.origin_df[~((self.origin_df['rtm'] != RTM_CARRIER) & (~self.origin_df['rp_mpn_mapped_from_carrier'].isnull()) & (self.origin_df['rp_mpn_mapped_from_carrier'] != self.origin_df['mpn']))]
        if self.is_rp_mapping:
            self.origin_df.loc[self.origin_df['rtm'] == RTM_CARRIER, 'mpn'] = self.origin_df['rp_mpn_mapped_from_carrier']

        self.origin_df['fiscal_week'] = self.fiscal_week

    # 对结果进行排序
    def __sort_result(self):
        # 将self.origin_df按照sold_to_id进行分组
        sort_key = ["region", "rtm", "sub_rtm", "sold_to_id", "sub_lob", "nand", "color", 'mpn']
        sort_df = common_sort_df(self.origin_df, sort_key)
        return sort_df

    def get_result(self) -> DataFrame:
        self.__get_sku_list_and_soldto_mapping()
        self.__cartesian_product()
        # 返回数据字段：fiscal_week, lob, sub_lob, nand, color, mpn, hr_lr, region, rtm, sub_rtm, sold_to_id, sold_to_name,
        return self.__sort_result()
