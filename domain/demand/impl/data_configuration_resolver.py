from datetime import datetime

from data.mysqls.demand.weekly_demand_setting import WeeklyDemandSetting
from domain.demand.entity.const import JD_SOLDTO
from domain.demand.entity.demand_data_config import DemandDataConfig


class DataConfigurationResolver:

    def __init__(self, fiscal_week: str, name_type: int, sub_lob_list: list[str]):
        self.fiscal_week = fiscal_week
        self.name_type = name_type
        self.sub_lob_list = sub_lob_list

    def get_default_data(self) -> list[DemandDataConfig]:
        res = []
        sold_to_ids = JD_SOLDTO.keys()
        for sold_to_id in sold_to_ids:
            for sub_lob in self.sub_lob_list:
                mix = DemandDataConfig(fiscal_week=self.fiscal_week,
                                       sold_to_id=sold_to_id,
                                       lob="iPhone",
                                       sub_lob=sub_lob,
                                       name_type=self.name_type,
                                       jd_self_run_mix_cw1=0.25,
                                       jd_self_run_mix_cw2=0.25)
                res.append(mix)
        return res

    def get_current_week_data(self) -> tuple[list[DemandDataConfig], datetime]:
        last_mixs, update_time = WeeklyDemandSetting.get_mix_setting(self.name_type)
        current_mixs = self.get_default_data()
        # 比较current_mixs和last_mixs，如果有相同的，取last_mixs的jd_self_run_mix
        for current_mix in current_mixs:
            for last_mix in last_mixs:
                if current_mix.sold_to_id == last_mix.sold_to_id and current_mix.sub_lob == last_mix.sub_lob:
                    current_mix.jd_self_run_mix_cw1 = last_mix.jd_self_run_mix_cw1
                    current_mix.jd_self_run_mix_cw2 = last_mix.jd_self_run_mix_cw2
        return current_mixs, update_time
