import io
from datetime import datetime
from flask import g
import numpy as np
import pandas as pd

from data.mysqls.demand.demand_by_region_pool import DemandByRegionPool
from data.mysqls.demand.demand_by_soldto_pool import DemandBy<PERSON>oldtoPool
from domain.demand.entity.const import THOUSANDS_SEPARATOR_COLUMNS, ROUND_COLUMNS, ROUND_INT_COLUMNS, DF_CW1, \
    DF_CW1_ADJUSTED, DF_CW2, DF_CW2_ADJUSTED, DN_CW1_ADJUSTED, DN_CW2_ADJUSTED, FINAL_DN_CW1, \
    FINAL_DN_CW2, RTMS_NO_RETAIL, BASE_DEMAND_CW1_ADJUSTED, BASE_DEMAND_CW1, BASE_DEMAND_CW2_ADJUSTED, BASE_DEMAND_CW2
from domain.demand.impl.carrier_mpn import replace_rp_mpn_to_carrier_mpn
from domain.permission.impl.permission_impl import filtered_soldto_ids_by_permission
from kit.pd import fill_nan_to_none
from util.const import Str<PERSON><PERSON><PERSON><PERSON>, StrRT<PERSON>arrier
from util.util import traditional_round


# ideal demand result file
IDEAL_DEMAND_RESULT_FILE = 0
SALES_FINAL_FORECAST_FILE = 1

# top down demand result file
TOPDOWN_DEMAND_RESULT_FILE = 0
COUNTRY_COMBINED_FORECAST_FILE = 1

SELL_IN_DEMAND_RESULT_FILE = 0

NORMALIZED_DEMAND_COUNTY_RESULT_FILE = 0
NORMALIZED_DEMAND_RTM_RESULT_FILE = 1
NORMALIZED_DEMAND_FORECAST_FILE = 2

DELTA_DEMAND_RTM_RESULT_FILE = 0

DEMAND_WEEK_RESULT_SHEET_NAME = "Sheet1"
SYSTEM = "System"


class DemandResult:
    def __init__(self, fiscal_week: str, demand_name: str, category: int):
        self.sheet = DEMAND_WEEK_RESULT_SHEET_NAME
        self.category = category
        self.fiscal_week = fiscal_week
        self.demand_name = demand_name

    def _column_mapping(self) -> dict:
        pass

    def _get_download_result(self) -> pd.DataFrame:
        pass

    def file_info(self, update_time: str = None) -> dict:
        pass

    def column_format(self, df):
        columns_to_format = THOUSANDS_SEPARATOR_COLUMNS.get(self.demand_name)
        if columns_to_format is None:
            return df
        # 与column mapping中的column求一下交集，处理交集的数据
        columns_to_format = list(filter(lambda x: x in list(self._column_mapping().values()), columns_to_format))
        for col in columns_to_format:
            # 将df[col]转成千分位数据，并四舍五入至一位小数
            df[col] = df[col].apply(
                lambda x: "{:,.0f}".format(x) if pd.notna(x) and isinstance(x, (int, float)) else '')
        return df

    def column_round(self, df):
        columns_to_format = ROUND_COLUMNS.get(self.demand_name)
        if columns_to_format is not None:
            # 与column mapping中的column求一下交集，处理交集的数据
            columns_to_format = list(filter(lambda x: x in list(self._column_mapping().values()), columns_to_format))
            for col in columns_to_format:
                # 将df[col]四舍五入至一位小数
                df[col] = df[col].apply(lambda x: traditional_round(x, 1) if pd.notna(x) else '')

        columns_to_round_int = ROUND_INT_COLUMNS.get(self.demand_name)
        if columns_to_round_int is not None:
            # 与column mapping中的column求一下交集，处理交集的数据
            columns_to_round_int = list(
                filter(lambda x: x in list(self._column_mapping().values()), columns_to_round_int))
            for col in columns_to_round_int:
                # 台数相关的都为整数
                df[col] = df[col].apply(lambda x: traditional_round(x) if pd.notna(x) else '')
        return df

    def download(self):
        df = self.get_dataframe()

        # 返回文件流，不在系统中创建文件
        excel_file_bytes = io.BytesIO()
        df.to_excel(excel_file_bytes, sheet_name=DEMAND_WEEK_RESULT_SHEET_NAME, index=False)
        excel_file_bytes.seek(0)
        return excel_file_bytes

    def get_dataframe(self):
        db_columns = list(self._column_mapping().values())
        display_columns = list(self._column_mapping().keys())
        df = self._get_download_result()
        # 将demand字段四舍五入至一位小数，并转成千分位数据
        df = self.column_round(df)
        df = self.column_format(df)
        if df.empty:
            # 2025-05-23 支持下载空表头，适配RTM只开Expert权限，不开数据权限的情况。
            df = pd.DataFrame(columns=db_columns)
            # raise FileNotFoundError(f' {self.demand_name} result no data, fiscal week: {self.fiscal_week}, category: {self.category}')
        df = df[db_columns]
        df.columns = display_columns
        return df

    def _get_update_time(self):
        pass


class IdealDemandResult(DemandResult):
    def __init__(self, fiscal_week: str, demand_name: str, category: int):
        super().__init__(fiscal_week, demand_name, category)

    def __is_result(self) -> bool:
        return self.category == IDEAL_DEMAND_RESULT_FILE

    def file_info(self, update_time: str = None) -> dict:
        return {
            "title": "Result" if self.__is_result() else "Reference",
            "type": IDEAL_DEMAND_RESULT_FILE if self.__is_result() else SALES_FINAL_FORECAST_FILE,
            "file_name": f"iPhone_Ideal_Demand_{self.fiscal_week}.xlsx" if self.__is_result() else f"iPhone_FCST_{self.fiscal_week}.xlsx",
            "uploader": SYSTEM,
            "upload_time": update_time
        }

    def _column_mapping(self):
        if self.__is_result():
            return {
                "Fiscal Week": "fiscal_week",
                "Region": "region",
                "RTM": "rtm",
                "Sub-RTM": "sub_rtm",
                "Sold-to Name": "sold_to_name",
                "Sold-to ID": "sold_to_id",
                "LOB": "lob",
                "Sub-LOB": "sub_lob",
                "Nand": "nand",
                "Color": "color",
                "MPN": "mpn",
                "CW Shipment Plan": "shipment_plan_cw",
                "CW+1 Ideal Demand": "cw1_ideal_demand",
                "CW+1 Shipment Plan": "shipment_plan_cw1",
                "CW+2 Ideal Demand": "cw2_ideal_demand",
                "CW+2 Shipment Plan": "shipment_plan_cw2",
            }
        return {
            "Fiscal Week": "fiscal_week",
            "Region": "region",
            "RTM": "rtm",
            "Sub-RTM": "sub_rtm",
            "Sold-to Name": "sold_to_name",
            "Sold-to ID": "sold_to_id",
            "LOB": "lob",
            "Sub-LOB": "sub_lob",
            "Nand": "nand",
            "Color": "color",
            "MPN": "mpn",
            "CW Forecast": "forecast_cw_ml",
            "CW+1 Forecast": "forecast_cw1_ml",
            "CW+2 Forecast": "forecast_cw2_sales",
            "CW+3 Forecast": "forecast_cw3_sales",
            "CW+4 Forecast": "forecast_cw4_sales",
            "CW+5 Forecast": "forecast_cw5_sales",
            "CW+6 Forecast": "forecast_cw6_sales",
        }

    def _get_download_result(self) -> pd.DataFrame:
        return DemandBySoldtoPool.get_dataframe_result(self.fiscal_week)


class TopDownDemandResult(DemandResult):
    def __init__(self, fiscal_week: str, demand_name: str, category: int):
        super().__init__(fiscal_week, demand_name, category)

    def __is_result(self) -> bool:
        return self.category == TOPDOWN_DEMAND_RESULT_FILE

    def file_info(self, update_time: str = None) -> dict:
        return {
            "title": "Result" if self.__is_result() else "Reference",
            "type": TOPDOWN_DEMAND_RESULT_FILE if self.__is_result() else COUNTRY_COMBINED_FORECAST_FILE,
            "file_name": f"iPhone_Top-down_Demand_{self.fiscal_week}.xlsx" if self.__is_result() else f"iPhone_Country_Combined_FCST_{self.fiscal_week}.xlsx",
            "uploader": SYSTEM,
            "upload_time": update_time
        }

    def _column_mapping(self):
        if self.__is_result():
            return {
                "Fiscal Week": "fiscal_week",
                "Region": "region",
                "LOB": "lob",
                "Sub-LOB": "sub_lob",
                "Nand": "nand",
                "Color": "color",
                "MPN": "mpn",
                "CW Shipment Plan": "shipment_plan_cw",
                "CW+1 Top-down Demand": "dt_cw1",
                "CW+1 Ideal Demand": "cw1_ideal_demand",
                "CW+1 Shipment Plan": "shipment_plan_cw1",
                "CW+2 Top-down Demand": "dt_cw2",
                "CW+2 Ideal Demand": "cw2_ideal_demand",
                "CW+2 Shipment Plan": "shipment_plan_cw2",
            }
        return {
            "Fiscal Week": "fiscal_week",
            "Region": "region",
            "LOB": "lob",
            "Sub-LOB": "sub_lob",
            "Nand": "nand",
            "Color": "color",
            "MPN": "mpn",
            "CW National ML FCST": "forecast_cw_ml",
            "CW+1 National ML FCST": "forecast_cw1_ml",
            "CW+2 DFA FCST": "forecast_cw2_dfa",
            "CW+3 DFA FCST": "forecast_cw3_dfa",
            "CW+4 DFA FCST": "forecast_cw4_dfa",
            "CW+5 DFA FCST": "forecast_cw5_dfa",
            "CW+6 DFA FCST": "forecast_cw6_dfa",
            "CW+7 DFA FCST": "forecast_cw7_dfa",
            "CW+8 DFA FCST": "forecast_cw8_dfa",
        }

    def _get_download_result(self) -> pd.DataFrame:
        return DemandByRegionPool.get_dataframe_result(self.fiscal_week)


class SellInDemandResult(DemandResult):
    def __init__(self, fiscal_week: str, demand_name: str, category: int):
        super().__init__(fiscal_week, demand_name, category)

    def file_info(self, update_time: str = None) -> dict:
        return {
            "title": "Result",
            "type": SELL_IN_DEMAND_RESULT_FILE,
            "file_name": f"iPhone_Sell_in_Demand_{self.fiscal_week}.xlsx",
            "uploader": SYSTEM,
            "upload_time": update_time
        }

    def _column_mapping(self):
        return {
            "Fiscal Week": "fiscal_week",
            "Region": "region",
            "LOB": "lob",
            "Sub-LOB": "sub_lob",
            "Nand": "nand",
            "Color": "color",
            "MPN": "mpn",
            "CW Shipment Plan": "shipment_plan_cw",
            "CW+1 Sell_in Demand": "ds_cw1",
            "CW+1 DS WOI": "ds_cw1_woi",
            "CW+1 Ideal Demand": "cw1_ideal_demand",
            "CW+1 Shipment Plan": "shipment_plan_cw1",
            "CW+2 Sell_in Demand": "ds_cw2",
            "CW+2 DS WOI": "ds_cw2_woi",
            "CW+2 Ideal Demand": "cw2_ideal_demand",
            "CW+2 Shipment Plan": "shipment_plan_cw2",
            "MPN Range": "mpn_range",
            "Sub-LOB Range": "woi_by_sublob_max",
        }

    def _get_download_result(self) -> pd.DataFrame:
        #  DS获取数据
        df = DemandByRegionPool.get_by_fiscal_week(self.fiscal_week)
        # fill_nan_to_none()
        df['mpn_range'] = df['woi_by_mpn_min'].astype(str) + "-" + df['woi_by_mpn_max'].astype(str)
        return df

    def _get_update_time(self):
        return DemandByRegionPool.get_update_time_by_fiscal_week(self.fiscal_week)


class NormalizedDemandResult(DemandResult):
    def __init__(self, fiscal_week: str, demand_name: str, category: int):
        super().__init__(fiscal_week, demand_name, category)

    def __get_file_name(self) -> str:
        if self.category == NORMALIZED_DEMAND_COUNTY_RESULT_FILE:
            return f"iPhone_Country_Normalized_Demand_{self.fiscal_week}.xlsx"
        elif self.category == NORMALIZED_DEMAND_RTM_RESULT_FILE:
            return f"iPhone_RTM_Normalized_Demand_{self.fiscal_week}.xlsx"
        elif self.category == NORMALIZED_DEMAND_FORECAST_FILE:
            return f"iPhone_RTM_Normalized_FCST_{self.fiscal_week}.xlsx"
        return ''

    def file_info(self, update_time: str = None) -> dict:
        return {
            "title": "Reference" if self.category == NORMALIZED_DEMAND_FORECAST_FILE else "Result",
            "type": self.category,
            "file_name": self.__get_file_name(),
            "uploader": SYSTEM,
            "upload_time": update_time
        }

    def _column_mapping(self):
        if self.category == NORMALIZED_DEMAND_COUNTY_RESULT_FILE:
            return {
                "Fiscal Week": "fiscal_week",
                "Region": "region",
                "LOB": "lob",
                "Sub-LOB": "sub_lob",
                "Nand": "nand",
                "Color": "color",
                "MPN": "mpn",
                "CW Shipment Plan": "shipment_plan_cw",
                "CW+1 Normalized Demand": "dn_cw1",
                "CW+1 Sell_in Demand": "ds_cw1",
                "CW+1 Ideal Demand": "cw1_ideal_demand",
                "CW+1 Shipment Plan": "shipment_plan_cw1",
                "CW+2 Normalized Demand": "dn_cw2",
                "CW+2 Sell_in Demand": "ds_cw2",
                "CW+2 Ideal Demand": "cw2_ideal_demand",
                "CW+2 Shipment Plan": "shipment_plan_cw2",
            }
        elif self.category == NORMALIZED_DEMAND_RTM_RESULT_FILE:
            return {
                "Fiscal Week": "fiscal_week",
                "Region": "region",
                "RTM": "rtm",
                "Sub-RTM": "sub_rtm",
                "Sold-to Name": "sold_to_name",
                "Sold-to ID": "sold_to_id",
                "LOB": "lob",
                "Sub-LOB": "sub_lob",
                "Nand": "nand",
                "Color": "color",
                "MPN": "mpn",
                "CW Shipment Plan": "shipment_plan_cw",
                "CW+1 Normalized Demand": "dn_cw1",
                "CW+1 Ideal Demand": "cw1_ideal_demand",
                "CW+1 Shipment Plan": "shipment_plan_cw1",
                "CW+2 Normalized Demand": "dn_cw2",
                "CW+2 Ideal Demand": "cw2_ideal_demand",
                "CW+2 Shipment Plan": "shipment_plan_cw2",
            }
        elif self.category == NORMALIZED_DEMAND_FORECAST_FILE:
            return {
                "Fiscal Week": "fiscal_week",
                "Region": "region",
                "RTM": "rtm",
                "Sub-RTM": "sub_rtm",
                "Sold-to Name": "sold_to_name",
                "Sold-to ID": "sold_to_id",
                "LOB": "lob",
                "Sub-LOB": "sub_lob",
                "Nand": "nand",
                "Color": "color",
                "MPN": "mpn",
                "CW ML FCST": "forecast_cw_ml",
                "CW+1 ML FCST": "forecast_cw1_ml",
                "CW+2 RTM Normalized FCST": "normalized_fcst_cw2",
                "CW+3 RTM Normalized FCST": "normalized_fcst_cw3",
                "CW+4 RTM Normalized FCST": "normalized_fcst_cw4",
            }
        else:
            return {}

    def _get_download_result(self) -> pd.DataFrame:
        if self.category == NORMALIZED_DEMAND_COUNTY_RESULT_FILE:
            region_pool_df = DemandByRegionPool.get_by_fiscal_week(self.fiscal_week)
            fill_nan_to_none(region_pool_df)

            #  判断如果avg_ub_1_5 = 0 或者 None 或者 forecast_cw2_dfa为none 设置dn_cw1=0,dn_cw2=0
            def process_row(row):
                if row['avg_ub_1_5'] is None or row['avg_ub_1_5'] == 0 or row['forecast_cw2_dfa'] is None:
                    row['dn_cw1'] = 0
                    row['dn_cw2'] = 0
                return row

            region_pool_df = region_pool_df.apply(process_row, axis=1)

            return region_pool_df
        elif self.category == NORMALIZED_DEMAND_RTM_RESULT_FILE:
            region_pool_df = DemandByRegionPool.get_by_fiscal_week(self.fiscal_week)
            region_pool_df = region_pool_df[['mpn', 'avg_ub_1_5', 'forecast_cw2_dfa']]
            region_pool_df.rename(columns={'avg_ub_1_5': 'region_avg_ub_1_5'}, inplace=True)
            fill_nan_to_none(region_pool_df)
            sold_to_pool_df = DemandBySoldtoPool.get_by_fiscal_week(self.fiscal_week)
            # 将region_pool_df与sold_to_pool_df按mpn左关联
            sold_to_pool_df = pd.merge(sold_to_pool_df, region_pool_df, on='mpn', how='left')

            def process_row(row):
                if row['region_avg_ub_1_5'] is None or row['region_avg_ub_1_5'] == 0 or row['forecast_cw2_dfa'] is None:
                    row['dn_cw1'] = 0
                    row['dn_cw2'] = 0
                return row

            # 按照相同逻辑处理merged_df中的数据
            sold_to_pool_df = sold_to_pool_df.apply(process_row, axis=1)
            return sold_to_pool_df
        elif self.category == NORMALIZED_DEMAND_FORECAST_FILE:
            return DemandBySoldtoPool.get_by_fiscal_week(self.fiscal_week)
        return pd.DataFrame()


class DeltaDemandResult(DemandResult):
    def __init__(self, fiscal_week: str, demand_name: str, category: int):
        super().__init__(fiscal_week, demand_name, category)

    def __get_file_name(self) -> str:
        return f"iPhone_Delta_Demand_{self.fiscal_week}.xlsx"

    def file_info(self, update_time: str = None) -> dict:
        return {
            "title": "Result",
            "type": self.category,
            "file_name": self.__get_file_name(),
            "uploader": SYSTEM,
            "upload_time": update_time
        }

    def _column_mapping(self):
        return {
            "Fiscal Week": "fiscal_week",
            "Region": "region",
            "RTM": "rtm",
            "Sub-RTM": "sub_rtm",
            "Sold-to ID": "sold_to_id",
            "Sold-to Name": "sold_to_name",
            "LOB": "lob",
            "Sub-LOB": "sub_lob",
            "Nand": "nand",
            "Color": "color",
            "MPN": "mpn",
            "CW+1 Adjusted Normalized Demand": "final_dn_cw1",
            "CW+2 Adjusted Normalized Demand": "final_dn_cw2",
            "Update Time": "update_time",
        }

    def _get_download_result(self) -> pd.DataFrame:
        return DemandBySoldtoPool.get_by_fiscal_week(self.fiscal_week)

    def _get_update_time(self):
        return DemandByRegionPool.get_update_time_by_fiscal_week(self.fiscal_week)


class FinalDemandResult(DemandResult):
    def __init__(self, fiscal_week: str, demand_name: str, category: int, sub_lobs: list, channel: str):
        super().__init__(fiscal_week, demand_name, category)
        self.sub_lobs = sub_lobs
        self.channel = channel
        self.rtms = RTMS_NO_RETAIL if channel == StrRTMCPF else [channel]

    def __get_file_name(self) -> str:
        formatted_time = datetime.now().strftime("%Y-%m-%d")
        return f"Suggested Demand_{self.fiscal_week}.xlsx"

    def file_info(self, update_time: str = None) -> dict:
        return {
            "title": "Result",
            "type": self.category,
            "file_name": self.__get_file_name(),
            "uploader": SYSTEM,
            "upload_time": update_time
        }

    def _column_mapping(self):
        if self.channel == StrRTMCPF:
            return FINAL_DEMAND_CPF_TEMPLATE_DICT
        # 8.12 RTM=Carrier,显示 Carrier Mpn列, 否则显示MPN列
        if self.channel == StrRTMCarrier:
            return FINAL_DEMAND_CARRIER_TEMPLATE_DICT

        return FINAL_DEMAND_TEMPLATE_DICT

    def _get_download_result(self) -> pd.DataFrame:
        # 过滤权限
        soldto_ids = filtered_soldto_ids_by_permission(self.channel, g)
        all_version_df = DemandBySoldtoPool.get_by_fiscal_week(self.fiscal_week, self.rtms, self.sub_lobs, soldto_ids)
        all_version_df = fill_nan_to_none(all_version_df)

        for index, row in all_version_df.iterrows():
            if row[DF_CW1_ADJUSTED] is not None:
                all_version_df.at[index, DF_CW1] = row[DF_CW1_ADJUSTED]
            if row[DF_CW2_ADJUSTED] is not None:
                all_version_df.at[index, DF_CW2] = row[DF_CW2_ADJUSTED]
            if row[DN_CW1_ADJUSTED] is not None:
                all_version_df.at[index, FINAL_DN_CW1] = row[DN_CW1_ADJUSTED]
            if row[DN_CW2_ADJUSTED] is not None:
                all_version_df.at[index, FINAL_DN_CW2] = row[DN_CW2_ADJUSTED]

            # 8.2 base demand
            if row[BASE_DEMAND_CW1_ADJUSTED] is not None:
                all_version_df.at[index, BASE_DEMAND_CW1] = row[BASE_DEMAND_CW1_ADJUSTED]
            if row[BASE_DEMAND_CW2_ADJUSTED] is not None:
                all_version_df.at[index, BASE_DEMAND_CW2] = row[BASE_DEMAND_CW2_ADJUSTED]

        all_version_df['delta_demand_cw1'] = all_version_df[DF_CW1] - all_version_df[BASE_DEMAND_CW1]
        all_version_df['delta_demand_cw2'] = all_version_df[DF_CW2] - all_version_df[BASE_DEMAND_CW2]
        all_version_df['avg_sales_fcst'] = all_version_df[
            ['forecast_cw1_sales', 'forecast_cw2_sales', 'forecast_cw3_sales', 'forecast_cw4_sales', 'forecast_cw5_sales']].mean(axis=1)
        all_version_df['avg_normalized_fcst'] = all_version_df[
            ['forecast_cw1_ml', 'normalized_fcst_cw2', 'normalized_fcst_cw3', 'normalized_fcst_cw4', 'normalized_fcst_cw5']].mean(axis=1)

        all_version_df['avg_sales_fcst_2_6'] = all_version_df[
            ['forecast_cw2_sales', 'forecast_cw3_sales', 'forecast_cw4_sales', 'forecast_cw5_sales', 'forecast_cw6_sales']].mean(axis=1)
        all_version_df['avg_normalized_fcst_2_6'] = all_version_df[
            ['normalized_fcst_cw2', 'normalized_fcst_cw3', 'normalized_fcst_cw4', 'normalized_fcst_cw5', 'normalized_fcst_cw6']].mean(axis=1)

        all_version_df['ml_runrate_cw1'] = all_version_df[
            ['forecast_cw2_sales', 'forecast_cw3_sales', 'forecast_cw4_sales']].mean(axis=1)
        all_version_df['ml_runrate_cw2'] = all_version_df[
            ['forecast_cw3_sales', 'forecast_cw4_sales', 'forecast_cw5_sales']].mean(axis=1)

        all_version_df = all_version_df[
            ['fiscal_week', 'region', 'rtm', 'sub_rtm', 'sold_to_id', 'sold_to_name', 'lob', 'sub_lob', 'mpn', 'nand', 'color',
             'final_dn_cw1', 'final_dn_cw2', 'delta_demand_cw1', 'delta_demand_cw2', 'df_cw1', 'df_cw2', 'cw1_ideal_demand', 'cw2_ideal_demand', 'dn_cw1', 'dn_cw2',
             'base_demand_cw1', 'base_demand_cw2', 'avg_sales_fcst', 'avg_normalized_fcst','avg_sales_fcst_2_6','avg_normalized_fcst_2_6', 'avg_ub_1_5',
             'shipment_plan_cw1', 'shipment_plan_cw2', 'ub_eoh', 'ml_runrate_cw1', 'ml_runrate_cw2',
             'forecast_cw_ml', 'forecast_cw1_ml', 'forecast_cw2_sales', 'forecast_cw3_sales', 'forecast_cw4_sales', 'forecast_cw5_sales', 'forecast_cw6_sales']]
        df_cw2 = all_version_df.copy()
        # 对原始的 all_version_df 进行 CW1 相关操作
        all_version_df['forecast_version'] = 'CW+1'
        all_version_df = all_version_df.rename(columns={
            'dn_cw1': 'dn_cw',
            'delta_demand_cw1': 'delta_demand_cw',
            'df_cw1': 'final_demand_cw',
            'cw1_ideal_demand': 'ideal_demand_cw',
            'base_demand_cw1': 'base_demand_cw',
            'shipment_plan_cw1': 'shipment_plan_cw',
            'ml_runrate_cw1': 'ml_runrate_cw',
        })

        # 对复制的 all_version_df_cw2 进行 CW2 相关操作
        df_cw2['forecast_version'] = 'CW+2'
        df_cw2 = df_cw2.rename(columns={
            'dn_cw2': 'dn_cw',
            'delta_demand_cw2': 'delta_demand_cw',
            'df_cw2': 'final_demand_cw',
            'cw2_ideal_demand': 'ideal_demand_cw',
            'base_demand_cw2': 'base_demand_cw',
            'shipment_plan_cw2': 'shipment_plan_cw',
            'ml_runrate_cw2': 'ml_runrate_cw',
        })
        df_cw2['avg_sales_fcst'] = df_cw2['avg_sales_fcst_2_6']
        df_cw2['avg_normalized_fcst'] = df_cw2['avg_normalized_fcst_2_6']
        # 合并两个 DataFrame
        combined_df = pd.concat([all_version_df, df_cw2], ignore_index=True)

        replace_fields = ['base_demand_cw', 'ideal_demand_cw', 'dn_cw', 'final_demand_cw', 'delta_demand_cw', 'avg_sales_fcst',
                          'avg_normalized_fcst', 'avg_ub_1_5', 'shipment_plan_cw', 'ub_eoh', 'ml_runrate_cw']
        combined_df[replace_fields] = combined_df[replace_fields].applymap(lambda x: 0 if x is None or np.isnan(x) or x<0 else x)
        #  如果是CP&F添加 carrier mpn，如果是carrier将rp mpn 转为 carrier mpn
        if self.channel == StrRTMCPF:
            combined_df['carrier_mpn'] = None
            def set_carrier_mpn(_row):
                return _row['mpn'] if _row['rtm'] == StrRTMCarrier else None
            combined_df['carrier_mpn'] = combined_df.apply(set_carrier_mpn, axis=1)
            replace_rp_mpn_to_carrier_mpn(combined_df, 'carrier_mpn')
        if self.channel == StrRTMCarrier:
            replace_rp_mpn_to_carrier_mpn(combined_df, 'mpn')

        return combined_df

# 8.12 RTM=Carrier,显示 Carrier Mpn列, 否则显示MPN列
FINAL_DEMAND_CARRIER_TEMPLATE_DICT = {
    "Current Week": "fiscal_week",
    "As of Week": "forecast_version",
    "Region": "region",
    "RTM": "rtm",
    "Sub-RTM": "sub_rtm",
    "Sold-to ID": "sold_to_id",
    "Sold-to Name": "sold_to_name",
    "LOB": "lob",
    "Sub-LOB": "sub_lob",
    "Nand": "nand",
    "Color": "color",
    "Carrier MPN": "mpn",
    "Demand 1.0": "ideal_demand_cw",
    # "Final Demand": "final_demand_cw",
    # "Shipment Plan": "shipment_plan_cw",
    # "EOH": "ub_eoh"
}

FINAL_DEMAND_TEMPLATE_DICT = {
    "Current Week": "fiscal_week",
    "As of Week": "forecast_version",
    "Region": "region",
    "RTM": "rtm",
    "Sub-RTM": "sub_rtm",
    "Sold-to ID": "sold_to_id",
    "Sold-to Name": "sold_to_name",
    "LOB": "lob",
    "Sub-LOB": "sub_lob",
    "Nand": "nand",
    "Color": "color",
    "MPN": "mpn",
    "Demand 1.0": "ideal_demand_cw",
    # "Final Demand": "final_demand_cw",
    # "Shipment Plan": "shipment_plan_cw",
    # "EOH": "ub_eoh"
}
FINAL_DEMAND_CPF_TEMPLATE_DICT = {
    "Current Week": "fiscal_week",
    "As of Week": "forecast_version",
    "Region": "region",
    "RTM": "rtm",
    "Sub-RTM": "sub_rtm",
    "Sold-to ID": "sold_to_id",
    "Sold-to Name": "sold_to_name",
    "LOB": "lob",
    "Sub-LOB": "sub_lob",
    "Nand": "nand",
    "Color": "color",
    "MPN": "mpn",
    "Carrier MPN": "carrier_mpn",
    "Demand 1.0": "ideal_demand_cw",
    # "Normalized Demand": "dn_cw",
    # "Base Demand": "base_demand_cw",
    # "Delta Demand": "delta_demand_cw",
    # "Final Demand": "final_demand_cw",
    # "Run-Rate (Sales Fcst)": "avg_sales_fcst",
    # "Run-Rate (Normalized Fcst)": "avg_normalized_fcst",
    "Shipment Plan": "shipment_plan_cw",
    "Run-Rate (ML) ": "ml_runrate_cw",
    "Run-Rate (Bwd UB) ": "avg_ub_1_5",
    "EOH": "ub_eoh",
    "Forecast CW": "forecast_cw_ml",
    "Forecast CW+1": "forecast_cw1_ml",
    "Forecast CW+2": "forecast_cw2_sales",
    "Forecast CW+3": "forecast_cw3_sales",
    "Forecast CW+4": "forecast_cw4_sales",
    "Forecast CW+5": "forecast_cw5_sales",
    "Forecast CW+6": "forecast_cw6_sales",
}
FINAL_DEMAND_TEMPLATE_COLUMNS = list(FINAL_DEMAND_CPF_TEMPLATE_DICT.keys())
