import datetime
import numpy as np
import pandas as pd
import io

from data.databend.end_to_end.ml_fcst_quantile import MLFcstQuantile
from data.datasource_data import DataSourceFile
from data.mysqls.demand.demand_by_soldto_pool import DemandBy<PERSON>oldtoPool
from data.mysqls.demand.rtm_sales_forecast_upload import FastLiteRTMSalesForecastUpload
from data.mysqls.demand.rtm_sales_forecast_upload_origin import FastLiteRTMSalesForecastUploadOrigin
from domain.dashboard.entity.fiscal_week import FiscalWeek
from domain.demand.impl.carrier_mpn import replace_rp_mpn_to_carrier_mpn
from domain.demand.impl.state_machine import StateProxy
from domain.demand.entity.const import *
from kit.pd import fill_nan_to_none
from util.conf import logger
from util.const import EmailCmd, ErrorExcept, ErrCode, DataSourceFileType
from domain.supply.entity import Lob
from domain.demand.impl.soldto_mpn_cartesian_product import CartesianProduct
from util.cpf_util import get_list_page_url
from util.send_email import async_send_email_by_database


def get_rtm_sales_forecast_file(rtm: str, fiscal_week: str) -> tuple:
    # file name
    file_name = RTM_SALES_FORECAST_NAME.format(rtm, fiscal_week)

    # report excel
    sheet_name = RTM_FORECAST_TEMPLATE_SHEET_NAME
    report_display_columns = list(RTM_FORECAST_TEMPLATE_DICT.keys())
    df = FastLiteRTMSalesForecastUpload.query_dataframe_by_rtm_fiscal_week(rtm=rtm, fiscal_week=fiscal_week)
    df.replace({np.nan: 0}, inplace=True)
    if df.empty:
        raise FileNotFoundError(f'rtm sales forecast upload no data, fiscal week: {fiscal_week}, rtm: {rtm}')
    df = df[list(RTM_FORECAST_TEMPLATE_DICT.values())]
    df.columns = report_display_columns

    # 返回文件流，不在系统中创建文件
    excel_file_bytes = io.BytesIO()
    df.to_excel(excel_file_bytes, sheet_name=sheet_name, index=False)
    excel_file_bytes.seek(0)
    return file_name, excel_file_bytes


def get_rtm_sales_forecast_template_file(rtm: str, fiscal_week: str) -> tuple:
    """rtm sales forecast 模版文件下载"""
    # file name
    file_name = RTM_SALES_FORECAST_TEMPLATE_NAME.format(rtm, fiscal_week)
    is_rp_mapping = False if rtm == RTM_CARRIER else True
    df_cartesian_product = CartesianProduct(fiscal_week, CHINA_MAINLAND, Lob.IPHONE.value, [rtm],
                                            is_rp_mapping).get_result()
    PREFILL_INDEX = 10
    prefill_raw_columns = list(RTM_FORECAST_TEMPLATE_DICT.values())[:PREFILL_INDEX]
    df_cartesian_product = df_cartesian_product[prefill_raw_columns]
    df_cartesian_product.columns = list(RTM_FORECAST_TEMPLATE_DICT.keys())[:PREFILL_INDEX]

    # report excel
    sheet_name = RTM_FORECAST_TEMPLATE_SHEET_NAME
    report_display_columns = list(RTM_FORECAST_TEMPLATE_DICT.keys())
    df = pd.DataFrame(columns=report_display_columns[PREFILL_INDEX:])
    df = pd.concat([df_cartesian_product, df], axis=1)
    df, has_ml_fcst = check_ml_fcst_exist(df, fiscal_week)

    is_empty = False
    if df.empty:
        is_empty = True

    # 返回文件流，不在系统中创建文件
    excel_file_bytes = io.BytesIO()
    df.to_excel(excel_file_bytes, sheet_name=sheet_name, index=False)
    excel_file_bytes.seek(0)
    return file_name, excel_file_bytes, is_empty


def check_ml_fcst_exist(data_df: pd.DataFrame, fiscal_week: str) -> (pd.DataFrame, bool):
    # 获取所有的ml数据
    forecast_df = MLFcstQuantile.get_data_by_fiscal_week(fiscal_week)
    has_ml_fcst = False
    if forecast_df.empty:
        return data_df, has_ml_fcst
    # 根据sold-to id和MPN去重
    forecast_df = forecast_df.drop_duplicates(subset=['sold-to id', 'MPN'], keep='first')

    # 以防万一，将上传的data_df再转一遍
    replace_rp_mpn_to_carrier_mpn(data_df, 'MPN', "RTM")
    # 将forecast_df中的mpn替换为carrier_mpn
    replace_rp_mpn_to_carrier_mpn(forecast_df, 'MPN', "RTM")
    forecast_df.rename(columns={'sold-to id': 'Sold-to ID'}, inplace=True)
    # 根据Sold-to ID和MPN，判断data_df和forecast_df是否有相同的Sold-to ID和MPN
    merged_df = data_df.merge(forecast_df, on=['RTM', 'Sold-to ID', 'MPN'], how='left', indicator=True)
    # 是否有匹配项（说明 `data_df` 里存在 `forecast_df` 里的 `Sold-to ID` 和 `MPN`）

    if merged_df['_merge'].eq('both').any():
        has_ml_fcst = True
    # 过滤掉 `forecast_df` 中出现的 `Sold-to ID` 和 `MPN`，只保留未匹配的数据
    filtered_df = merged_df[merged_df['_merge'] == 'left_only'].drop(columns=['_merge'])

    return filtered_df, has_ml_fcst


def upload_rtm_sales_fcst_file(upload_df, fiscal_week, uploader, rtm: str):
    # upload_df = pd.read_excel(file)
    if upload_df.empty:
        raise ErrorExcept(ErrCode.Param, "upload file is Empty")
    fiscal_week_year = FiscalWeek(fiscal_week)

    # save data
    now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    upload_df.columns = list(RTM_FORECAST_TEMPLATE_DICT.values())
    upload_df['week_date'] = fiscal_week
    upload_df['fiscal_week_year'] = fiscal_week_year.fiscal_week_int
    upload_df['uploader'] = uploader
    upload_df['create_time'] = now
    upload_df['update_time'] = now
    upload_df['model'] = None
    upload_df.replace({np.nan: None}, inplace=True)
    FastLiteRTMSalesForecastUpload.bulk_insert_or_update(upload_df.to_dict("records"))
    return now


def upload_origin_rtm_sales_fcst_file(upload_df, fiscal_week, uploader):
    # upload_df = pd.read_excel(file)
    if upload_df.empty:
        raise ErrorExcept(ErrCode.Param, "upload file is Empty")
    fiscal_week_year = FiscalWeek(fiscal_week)

    # save data
    now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    upload_df.columns = list(RTM_FORECAST_TEMPLATE_DICT.values())
    upload_df['week_date'] = fiscal_week
    upload_df['fiscal_week_year'] = fiscal_week_year.fiscal_week_int
    upload_df['uploader'] = uploader
    upload_df['create_time'] = now
    upload_df['update_time'] = now
    upload_df['model'] = None
    upload_df.replace({np.nan: None}, inplace=True)
    FastLiteRTMSalesForecastUploadOrigin.bulk_insert_or_update(upload_df.to_dict("records"))
    return now


def delete_rtm_sales_fcst_file(rtm: str, fiscal_week: str) -> bool:
    fiscal_week_year = FiscalWeek(fiscal_week)
    delete_success = FastLiteRTMSalesForecastUpload.delete_by_rtm_week(rtm, fiscal_week_year.fiscal_week_int)
    FastLiteRTMSalesForecastUploadOrigin.delete_by_rtm_week(rtm, fiscal_week_year.fiscal_week_int)
    DataSourceFile.update_status_by_rtm_type_upload_date(rtm, DataSourceFileType.SalesForecast, fiscal_week)
    return delete_success


def rtm_publish_fcst_x(fiscal_week: str, channel: str, operator: str):
    logger.info(f'{channel} {operator} start publish {fiscal_week} fcst & x at {datetime.datetime.now()}')
    state_proxy = StateProxy(fiscal_week, IDEAL_DEMAND)
    state_proxy.do_rtm_publish(channel)
    current_state = state_proxy.current_state()
    rtm_status = state_proxy.get_rtm_state(channel)

    logger.info(
        f'{channel} {operator} has published {fiscal_week} fcst & x at {datetime.datetime.now()}. rtm_status: {rtm_status}')

    return current_state.rtm_state(rtm_status).format()


def auto_generate_rtm_sales_forecast(fiscal_week: str):
    state_proxy = StateProxy(fiscal_week, IDEAL_DEMAND)
    fiscal_week_year = FiscalWeek(fiscal_week)
    for rtm in RTMS:
        # 根据rtm状态判断是否提交数据
        if state_proxy.get_rtm_state(rtm) == 1:
            continue

        # 判断rtm是否上传了数据，没有上传数据就给它自动生成兜底
        rtm_upload_record = FastLiteRTMSalesForecastUpload.query_by_rtm_fiscal_week(rtm=rtm, fiscal_week=fiscal_week)
        if rtm_upload_record is not None:
            # 已经有数据不再生成
            continue

        # 获取sold to pool 表中数据 保存到 fast_lite_rtm_sales_forecast_upload
        soldto_df = DemandBySoldtoPool.get_by_fiscal_week(fiscal_week, [rtm])
        if soldto_df.empty:
            raise Exception(ErrCode.DBQueryNoData, f'{rtm} {fiscal_week} demand_by_soldto_pool is empty')
        # 替换soldto_df 的 rp mpn 为 carrier mpn
        replace_rp_mpn_to_carrier_mpn(soldto_df, 'mpn')
        # save data
        insert_df = pd.DataFrame(columns=list(RTM_FORECAST_TEMPLATE_DICT.values()))
        copy_col = ['region', 'rtm', 'sub_rtm', 'sold_to_name', 'sold_to_id', 'lob', 'sub_lob', 'nand', 'color', 'mpn']
        insert_df[copy_col] = soldto_df[copy_col]
        now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        insert_df['week_date'] = fiscal_week
        insert_df['fiscal_week_year'] = fiscal_week_year.fiscal_week_int
        insert_df['uploader'] = 'System'
        insert_df['create_time'] = now
        insert_df['update_time'] = now
        insert_df['model'] = None
        insert_df['forecast_cw'] = soldto_df['forecast_cw_ml']
        insert_df['forecast_cw1'] = soldto_df['forecast_cw1_ml']
        insert_df['forecast_cw2'] = soldto_df['normalized_fcst_cw2']
        insert_df['forecast_cw3'] = soldto_df['normalized_fcst_cw3']
        insert_df['forecast_cw4'] = soldto_df['normalized_fcst_cw4']
        insert_df['forecast_cw5'] = soldto_df['normalized_fcst_cw5']
        insert_df['forecast_cw6'] = soldto_df['normalized_fcst_cw6']
        insert_df.replace({np.nan: None}, inplace=True)
        FastLiteRTMSalesForecastUpload.bulk_insert_or_update(insert_df.to_dict("records"))


def do_moniter_rtms_upload_forecast(fiscal_week: str):
    # 检测6家RTM是否有上传sales forrcast数据，如果到时间还未上传，则发送邮件进行提醒
    upload_record = FastLiteRTMSalesForecastUpload.query_rtm_records_by_fiscal_week(fiscal_week, RTMS_NO_RETAIL) 
    if upload_record is not None:
        if len(upload_record) > 0:
            has_uploaded_rtms = [record.rtm for record in upload_record]
            no_upload_rtms = list(set(RTMS_NO_RETAIL) - set(has_uploaded_rtms))
            for rtm in no_upload_rtms:
                async_send_email_by_database(
                    f"{rtm}_{EmailCmd.RTMUploadSalesForecastReminderEmail}",
                    params={"system_link": get_list_page_url(f"/fd-e2e/rtms?channel={rtm}")}
                )
        else:
            # 没有查到记录，则需要给全部RTM发送提醒邮件
            for rtm in RTMS_NO_RETAIL:
                async_send_email_by_database(
                    f"{rtm}_{EmailCmd.RTMUploadSalesForecastReminderEmail}",
                    params={"system_link": get_list_page_url(f"/fd-e2e/rtms?channel={rtm}")}
                )


