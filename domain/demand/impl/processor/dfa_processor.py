import copy
import logging

import pandas
import pandas as pd

from data.cpf_data_source import OdsFastCPFActiveSKULob
from data.mysqls.demand.demand_by_region_pool import DemandByRegionPool
from data.mysqls.demand.demand_by_soldto_pool import DemandBy<PERSON>oldtoPool
from domain.demand.entity.const import TOPDOWN_DEMAND
from domain.demand.entity.module_switch_enum import ModuleSwitchEnum
from domain.demand.impl.dfa_upload import DFA_COLUMNS, DFA_COLUMNS_RAW
from domain.demand.impl.processor.const import BASE_PROCESSOR
from domain.demand.impl.processor.processor import Processor, is_all_process
from data.mysqls.demand.ideal_demand_cpf_upload_dfa import IdealDemandCPFUploadDFA
from domain.dashboard.impl.mpn_mix_reslover import MpnMixReslover
from domain.demand.impl.state_machine import StateProxy
from domain.supply.entity import Lob
from kit.custom_date.custom_week_date import CustomWeekDate
from kit.pd import fill_nan_to_none


class DFAProcessor(Processor):

    def __init__(self, processor: str, fiscal_week: str):
        super().__init__(processor, fiscal_week)

    def _can_do(self) -> bool:
        # 添加DN的curve计算，时间点在base_process之后
        base_process_finished = is_all_process(self.fiscal_week, [BASE_PROCESSOR])
        # base_process_finished = True
        # 用户上传完数据提交后，状态机状态变为 WaitingForCalculation = 100，确保此状态后才执行，使用用户提交的数据进行计算
        # 改为wait for calculation此状态之后
        # 用户不上传DFA数据，依然可以跑，使用0来兜底
        state_proxy = StateProxy(self.fiscal_week, TOPDOWN_DEMAND)
        return (
            base_process_finished
            and (state_proxy.is_waiting_for_calculation() or state_proxy.is_waiting_to_setup())
            and (
                not CustomWeekDate(
                    ModuleSwitchEnum.BEFORE_CALCULATION.value
                ).is_before_ddl()
            )
        )

    def _do(self):
        fiscal_week = self.fiscal_week
        lob = Lob.IPHONE.value
        self.calculate_mpn_mix(fiscal_week, lob)
        #  新增计算curve逻辑
        sold_to_df = self.calculate_dn_curve(fiscal_week)
        self.save_normalized_fcst(sold_to_df)

    def calculate_mpn_mix(self, fiscal_week, lob):
        # 1 通过fiscal_week查询ideal_demand_cpf_upload_dfa 数据,最终将此数据分为mpn级别
        # 2 查询app_fast_dashboard_country_mpn_mix_wi的trimmed_ub_mix 和 dashboard_mix_upload的adjusted_mix
        # 3 如果sub_lob 有 adjusted_mix 就使用 adjusted_mix， 都没有则使用ml_mix兜底
        # 4 计算cw cw1-cw8 数据
        # 5 保存数据到ideal_demand_mpn_mixed_dfa_forecast
        cpf_upload_dfa_list = IdealDemandCPFUploadDFA.query_list_by_week(fiscal_week)
        if not cpf_upload_dfa_list:
            cpf_upload_dfa_list = self.get_dfa_template()
        region = 'China mainland'
        result = MpnMixReslover().merge_mpn_mix_and_adjust(fiscal_week, region, lob, '', False, False)
        if not result:
            raise Exception('mpn mix result is empty')
        group_by_sub_lob = {}
        for item in result:
            if not item['mpn']:
                continue
            sub_lob = item["sub_lob"]
            if sub_lob not in group_by_sub_lob:
                group_by_sub_lob[sub_lob] = []
            group_by_sub_lob[sub_lob].append(item)
        update_list = []
        for cpf_upload_dfa in cpf_upload_dfa_list:
            sub_lob = cpf_upload_dfa['sub_lob']
            if sub_lob not in group_by_sub_lob:
                raise Exception('sub_lob {} can not find mpn'.format(sub_lob))
            # 如果当前sub_lob有相应的mpn数据 就保存插入
            for sub_lob_mpn in group_by_sub_lob[sub_lob]:
                # 判断使用adjust 还是 trimmed
                mix = sub_lob_mpn['actual_mix']
                if not mix:
                    # 如果为0 通过检查
                    if mix is not None and mix == 0:
                        pass
                    else:
                        raise Exception(
                            'fiscal_week{},cpf_upload_dfa {},sub_lob_mpn{} can not find mpn mix'.format(fiscal_week,
                                                                                                        cpf_upload_dfa,
                                                                                                        sub_lob_mpn))
                mpn_forecast = copy.deepcopy(cpf_upload_dfa)
                mpn_forecast['mpn'] = sub_lob_mpn['mpn']
                mpn_forecast['region'] = region
                mpn_forecast['forecast_cw2_dfa'] = mpn_forecast['cw2'] * mix
                mpn_forecast['forecast_cw3_dfa'] = mpn_forecast['cw3'] * mix
                mpn_forecast['forecast_cw4_dfa'] = mpn_forecast['cw4'] * mix
                mpn_forecast['forecast_cw5_dfa'] = mpn_forecast['cw5'] * mix
                mpn_forecast['forecast_cw6_dfa'] = mpn_forecast['cw6'] * mix
                mpn_forecast['forecast_cw7_dfa'] = mpn_forecast['cw7'] * mix
                mpn_forecast['forecast_cw8_dfa'] = mpn_forecast['cw8'] * mix
                mpn_forecast['avg_dfa_fcst_2_4'] = (mpn_forecast['forecast_cw2_dfa'] + mpn_forecast[
                    'forecast_cw3_dfa'] + mpn_forecast['forecast_cw4_dfa']) / 3
                mpn_forecast['avg_dfa_fcst_3_5'] = (mpn_forecast['forecast_cw3_dfa'] + mpn_forecast[
                    'forecast_cw4_dfa'] + mpn_forecast['forecast_cw5_dfa']) / 3
                update_list.append(mpn_forecast)
        fields_to_update = ['forecast_cw2_dfa', 'forecast_cw3_dfa', 'forecast_cw4_dfa',
                            'forecast_cw5_dfa', 'forecast_cw6_dfa', 'forecast_cw7_dfa',
                            'forecast_cw8_dfa', 'avg_dfa_fcst_2_4', 'avg_dfa_fcst_3_5', 'update_time']
        DemandByRegionPool.batch_update(update_list, fields_to_update)

    def calculate_dn_curve(self, fiscal_week,
                           region_pool_df: pd.DataFrame = None, sold_to_df: pd.DataFrame = None) -> pd.DataFrame:
        if region_pool_df is None:
            region_pool_df = DemandByRegionPool.get_by_fiscal_week(fiscal_week)

        if sold_to_df is None:
            sold_to_df = DemandBySoldtoPool.get_by_fiscal_week(fiscal_week)

        region_pool_df = region_pool_df[region_pool_df['avg_ub_1_5'].notna() & region_pool_df['avg_ub_1_5'] != 0]

        # region_pool_df = region_pool_df[region_pool_df['fiscal_week'] == 'FY24Q4W3']
        # region_pool_df = region_pool_df[region_pool_df['sub_lob'] == 'iPhone 15']

        needed_cols = ['forecast_cw_ml', 'forecast_cw1_ml', 'forecast_cw2_dfa', 'forecast_cw3_dfa',
                       'forecast_cw4_dfa', 'forecast_cw5_dfa', 'forecast_cw6_dfa']
        region_pool_df['dfa_fcst_cw_list'] = region_pool_df[needed_cols].values.tolist()
        col_n = 7
        region_pool_df['dfa_rate'] = region_pool_df.apply(lambda row: [0] * col_n, axis=1)

        def df_dfa_rate_row_processing(row, start_index, init_field, process_col):
            for i in range(start_index, len(row[process_col])):
                if i == start_index:
                    row[process_col][i] = row['dfa_fcst_cw_list'][i] / row[init_field] - 1
                else:
                    if row['dfa_fcst_cw_list'][i - 1] == 0:
                        row[process_col][i] = 0
                    else:
                        row[process_col][i] = row['dfa_fcst_cw_list'][i] / row['dfa_fcst_cw_list'][i - 1] - 1
            for i in range(0, start_index):
                row[process_col][i] = None
            return row

        region_pool_df = region_pool_df.apply(lambda row: df_dfa_rate_row_processing(row, 2, 'avg_ub_1_5', 'dfa_rate'),
                                              axis=1)

        # Processing sold_to_df with similar logic

        sold_to_df = sold_to_df[sold_to_df['avg_ub_1_5'].notna()]
        sold_to_df = sold_to_df[sold_to_df['avg_ub_1_5'] != 0]

        # sold_to_df = sold_to_df[sold_to_df['fiscal_week'] == 'FY24Q4W3']
        # sold_to_df = sold_to_df[sold_to_df['sub_lob'] == 'iPhone 15']
        # sold_to_df = sold_to_df[sold_to_df['sub_rtm'] == 'JD self-run']

        needed_cols_sold_to = ['forecast_cw_ml', 'forecast_cw1_ml', 'forecast_cw2_sales', 'forecast_cw3_sales',
                               'forecast_cw4_sales', 'forecast_cw5_sales', 'forecast_cw6_sales']

        sold_to_df['sales_rate'] = sold_to_df.apply(lambda row: [0] * col_n, axis=1)
        sold_to_df['sales_fcst_cw_list'] = sold_to_df[needed_cols_sold_to].values.tolist()

        def df_sold_to_rate_row_processing(row, start_index, init_field, process_col):
            for i in range(start_index, len(row[process_col])):
                if i == start_index:
                    row[process_col][i] = row['sales_fcst_cw_list'][i] / row[init_field] - 1
                else:
                    if row[needed_cols_sold_to[i - 1]] == 0:
                        row[process_col][i] = 0
                    else:
                        row[process_col][i] = row['sales_fcst_cw_list'][i] / row['sales_fcst_cw_list'][i - 1] - 1
            for i in range(0, start_index):
                row[process_col][i] = None
            return row

        sold_to_df = sold_to_df.apply(lambda row: df_sold_to_rate_row_processing(row, 2, 'avg_ub_1_5', 'sales_rate'),
                                      axis=1)

        sold_to_df = sold_to_df.merge(region_pool_df[['region', 'mpn', 'dfa_rate']], how='left', on=['region', 'mpn'])

        sold_to_df = sold_to_df[sold_to_df['dfa_rate'].notna()]

        # Create new column 'avg_sales_dfa' to store the average of each sold_to's rate and sales_rate
        def calculate_avg_sales_dfa(row):
            try:
                valid_dfa_rates = [rate for rate in row['dfa_rate']]
                valid_sales_rates = [sales_rate for sales_rate in row['sales_rate']]
                avg_rates = []
                for i in range(len(valid_dfa_rates)):
                    if (valid_dfa_rates[i] != None and valid_sales_rates[i] != None):
                        avg_num = (valid_dfa_rates[i] + valid_sales_rates[i])/2
                        avg_rates.append(avg_num)
                    else:
                        avg_rates.append(None)
                return avg_rates
            except Exception as e:
                logging.error(e)
                raise e

        sold_to_df['avg_sales_dfa_rate'] = sold_to_df.apply(calculate_avg_sales_dfa, axis=1)

        sold_to_df = sold_to_df[sold_to_df['avg_sales_dfa_rate'].notna()]

        sold_to_df['rtm_normalized_fcst'] = sold_to_df.apply(lambda row: [0] * col_n, axis=1)

        def rtm_n_fcst_processing(row, num):
            row['rtm_normalized_fcst'][0] = row['forecast_cw_ml']
            row['rtm_normalized_fcst'][1] = row['forecast_cw1_ml']
            row['rtm_normalized_fcst'][2] = row['avg_ub_1_5'] * (row['dfa_rate'][2] + 1)
            for x in range(num, len(row['rtm_normalized_fcst'])):
                row['rtm_normalized_fcst'][x] = row['rtm_normalized_fcst'][x - 1] * (row['dfa_rate'][x] + 1)
            return row

        sold_to_df = sold_to_df.apply(rtm_n_fcst_processing, num=3, axis=1)

        sold_to_df = sold_to_df[['fiscal_week', 'sold_to_id', 'mpn', 'rtm_normalized_fcst']]

        cw_cols = ['normalized_fcst_cw2', 'normalized_fcst_cw3', 'normalized_fcst_cw4', 'normalized_fcst_cw5',
                   'normalized_fcst_cw6']

        def extract_normalized_fcst(row, fields):
            return pd.Series({fields[i]: row[i + 2] for i in range(len(fields))})

        sold_to_df[cw_cols] = sold_to_df['rtm_normalized_fcst'].apply(lambda x: extract_normalized_fcst(x, cw_cols))
        # 保证不报错
        sold_to_df = fill_nan_to_none(sold_to_df)
        # sold_to_df.to_csv('sold_to_df_old.csv', index=False)
        return sold_to_df

    def save_normalized_fcst(self, sold_to_df):
        # 保存数据到sold to
        fields_to_update = ['normalized_fcst_cw2', 'normalized_fcst_cw3', 'normalized_fcst_cw4',
                            'normalized_fcst_cw5', 'normalized_fcst_cw6']
        DemandBySoldtoPool.batch_update(sold_to_df.to_dict(orient='records'), fields_to_update)

    def get_dfa_template(self) -> list:
        # 查询datasource中最大version的sub_lob数据
        sub_lobs = OdsFastCPFActiveSKULob.list_sub_lobs("China mainland", "iPhone")

        df = pd.DataFrame([], columns=DFA_COLUMNS_RAW)
        df["sub_lob"] = sub_lobs
        df["fiscal_week"] = self.fiscal_week
        df["cw"] = 0
        df["cw1"] = 0
        df["cw2"] = 0
        df["cw3"] = 0
        df["cw4"] = 0
        df["cw5"] = 0
        df["cw6"] = 0
        df["cw7"] = 0
        df["cw8"] = 0
        return df.to_dict(orient='records')
