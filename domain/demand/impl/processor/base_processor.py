import copy
import os
import pandas
import pandas as pd

from data.databend.dashboard.bottom_up_ml_forecast import BottomUpMlForecast
from data.databend.dashboard.country_ml_forecast import CountryMlForecast
from data.databend.dashboard.demand_county_esr_ub_eoh import DemandCountyEsrUbEoh
from data.databend.dashboard.demand_esr_ub_eoh import DemandEsrUbEoh
from data.databend.dashboard.sales_forecast_upload import SalesForecastUpload
from data.email_config import EmailConfigRepository
from data.fiscal_year_week import FiscalYearWeek
from data.mysqls.demand.demand_by_region_pool import DemandByRegionPool
from data.mysqls.demand.demand_by_soldto_pool import Demand<PERSON><PERSON><PERSON>oldtoPool
from domain.demand.entity.const import RTMS, IDEAL_DEMAND, NO_MENTION_SOLDTO, COLUMN_MPN, RTMS_NO_ML, RTMS_NO_RETAIL
from domain.demand.entity.module_switch_enum import ModuleSwitchEnum
from domain.demand.impl.calculator.ideal_demand_calculator import CHIN<PERSON>_MAINLAND
from domain.demand.impl.processor.const import BASE_PROCESSOR, SALES_FORECAST_PROCESSOR
from domain.demand.impl.processor.processor import Processor, is_all_process
from domain.demand.impl.y_value_setting import get_demand_fiscal_weeks
from domain.supply.entity import Lob
from kit.custom_date.custom_week_date import CustomWeekDate
from kit.pd import fill_nan_to_none
from kit.token_bucket import EMAIL_BUCKET_KEY_PREFIX
from util.const import EmailCmd
from util.send_email import set_table_content_email_config, async_rate_limited_send_email_v2


class BaseProcessor(Processor):

    def __init__(self, processor: str, fiscal_week: str):
        super().__init__(processor, fiscal_week)

    def _can_do(self) -> bool:
        sales_process_finished = is_all_process(self.fiscal_week, [SALES_FORECAST_PROCESSOR])
        return sales_process_finished and not CustomWeekDate(ModuleSwitchEnum.BEFORE_CALCULATION.value).is_before_ddl()

    def __ml_forecast_preprocessing(self, df_by_soldto: pd.DataFrame) -> pd.DataFrame:
        """ML数据预处理"""

        # 1. 检测ml数据是否为空, 并发送邮件通知
        self.__notify_ml_forecast(df_by_soldto)

        # 2. ml数据为空数据使用sales fcst数据填充
        filled_df_by_soldto = self.__fill_ml_forecast_by_sales_forecast(df_by_soldto)
        return filled_df_by_soldto

    def __notify_ml_forecast(self, df_by_soldto: pd.DataFrame) -> None:
        """检查ML数据是否为空"""
        email_content_columns = ["mpn", "rtm", "sold_to_id", "sold_to_name"]
        filter_condition = (~df_by_soldto['forecast_cw_ml'].notna()) & (~df_by_soldto['rtm'].isin(RTMS_NO_ML))
        ml_miss_data = df_by_soldto[filter_condition]
        if not ml_miss_data.empty:
            # 发邮件
            ml_miss_data = fill_nan_to_none(ml_miss_data)
            email_config = EmailConfigRepository.query_email_config(EmailCmd.Processor)
            developer_subject_params = {"env": os.environ.get('ENV'), "processor": BASE_PROCESSOR}
            file_paths, email_config = set_table_content_email_config(email_config=copy.deepcopy(email_config),
                                                                      content_data=ml_miss_data.to_dict(orient='records'),
                                                                      table_title=f"week 【{self.fiscal_week}】 处理by soldto、by mpn粒度数据失败: <br>ML数据缺失如下forecast数据:",
                                                                      params=developer_subject_params,
                                                                      email_content_columns=email_content_columns)
            async_rate_limited_send_email_v2(limit_key=EMAIL_BUCKET_KEY_PREFIX + 'ml_miss_data111-' + self.fiscal_week,
                                             capacity=1,
                                             expire_time=1200,
                                             email_config=email_config,
                                             file_paths=file_paths)

    def __fill_ml_forecast_by_sales_forecast(self, df_by_soldto: pd.DataFrame) -> pd.DataFrame:
        """Sales fcst 数据填充, 不区分rtm"""
        suffix_right = '_from_right'
        cw_sales_fcst = f'forecast_cw_sales{suffix_right}'
        cw1_sales_fcst = f'forecast_cw1_sales{suffix_right}'
        cw2_sales_fcst = f'forecast_cw2_sales{suffix_right}'
        cw3_sales_fcst = f'forecast_cw3_sales{suffix_right}'
        cw4_sales_fcst = f'forecast_cw4_sales{suffix_right}'
        cw5_sales_fcst = f'forecast_cw5_sales{suffix_right}'
        cw6_sales_fcst = f'forecast_cw6_sales{suffix_right}'

        # 获取算法组处理后的sale forcast的数据
        handled_sales_fcst = SalesForecastUpload.query_sales_by_region_fiscal_week(region=CHINA_MAINLAND,
                                                                                   fiscal_week=self.fiscal_week,
                                                                                   lob=Lob.IPHONE.value)
        handled_sales_fcst_df = pd.DataFrame(handled_sales_fcst)

        # 为空数据直接返回, 不在进行数据填充处理
        if df_by_soldto.empty or handled_sales_fcst_df.empty:
            return df_by_soldto

        handled_sales_fcst_df['sold_to_id'] = handled_sales_fcst_df['sold_to_id'].astype(str)
        merged_df = pd.merge(df_by_soldto, handled_sales_fcst_df[['fiscal_week', 'sold_to_id', 'mpn',
                                                                  'forecast_cw_sales', 'forecast_cw1_sales',
                                                                  'forecast_cw2_sales', 'forecast_cw3_sales',
                                                                  'forecast_cw4_sales', 'forecast_cw5_sales',
                                                                  'forecast_cw6_sales']],
                             on=['fiscal_week', 'sold_to_id', 'mpn'],
                             how='left',
                             suffixes=('', suffix_right))

        # ML数据只要为None都使用sale fcst去填充，不区分rtm
        filter_condition = ~merged_df['forecast_cw_ml'].notna()
        merged_df.loc[filter_condition, 'forecast_cw_ml'] = merged_df.loc[filter_condition, cw_sales_fcst]
        merged_df.loc[filter_condition, 'forecast_cw1_ml'] = merged_df.loc[filter_condition, cw1_sales_fcst]
        merged_df.loc[filter_condition, 'forecast_cw2_sales'] = merged_df.loc[filter_condition, cw2_sales_fcst]
        merged_df.loc[filter_condition, 'forecast_cw3_sales'] = merged_df.loc[filter_condition, cw3_sales_fcst]
        merged_df.loc[filter_condition, 'forecast_cw4_sales'] = merged_df.loc[filter_condition, cw4_sales_fcst]
        merged_df.loc[filter_condition, 'forecast_cw5_sales'] = merged_df.loc[filter_condition, cw5_sales_fcst]
        merged_df.loc[filter_condition, 'forecast_cw6_sales'] = merged_df.loc[filter_condition, cw6_sales_fcst]

        ignore_columns = [cw_sales_fcst, cw1_sales_fcst, cw2_sales_fcst, cw3_sales_fcst, cw4_sales_fcst, cw5_sales_fcst, cw6_sales_fcst]
        final_columns = [col for col in merged_df.columns if col not in ignore_columns]
        merged_df = merged_df[final_columns]
        return merged_df

    def _do(self):
        # 处理 'forecast_cw_ml', 'forecast_cw1_ml', 'shipment_plan_cw', 'shipment_plan_cw1', 'shipment_plan_cw2', 'ub_eoh', 'base'
        df_by_region, df_by_soldto = self.__process_base()
        # 处理 'ub_1_5', 'avg_ub_1_5'
        df_by_region, df_by_soldto = self.__process_avg_ub(df_by_region, df_by_soldto)
        # 更新pool表，并检查空数据发邮件
        self.__update_bottomup_ml_forecast(df_by_region, df_by_soldto)

    def __update_bottomup_ml_forecast(self, group_df: pd.DataFrame, merge_df: pd.DataFrame):

        update_list = ['forecast_cw_ml', 'forecast_cw1_ml', 'shipment_plan_cw', 'shipment_plan_cw1',
                       'shipment_plan_cw2', 'ub_eoh', 'ub_1_5', 'avg_ub_1_5', 'base', 'update_time',
                       'forecast_cw2_sales', 'forecast_cw3_sales', 'forecast_cw4_sales', 
                       'forecast_cw5_sales', 'forecast_cw6_sales', 
                       'avg_sales_fcst_2_4', 'avg_sales_fcst_3_5']
        region_update_list = ['forecast_cw_ml', 'forecast_cw1_ml', 'shipment_plan_cw', 'shipment_plan_cw1',
                       'shipment_plan_cw2', 'ub_eoh', 'ub_1_5', 'avg_ub_1_5', 'base', 'update_time']

        merge_df = fill_nan_to_none(merge_df)
        group_df = fill_nan_to_none(group_df)
        DemandBySoldtoPool.batch_update(merge_df.to_dict(orient='records'), update_list)
        DemandByRegionPool.batch_update(group_df.to_dict(orient='records'), region_update_list)

        email_config = EmailConfigRepository.query_email_config(EmailCmd.Processor)
        developer_subject_params = {"env": os.environ.get('ENV'), "processor": BASE_PROCESSOR}

        # 使用笛卡尔积去做校验
        eoh_miss_data = DemandBySoldtoPool.get_miss_result(self.fiscal_week, NO_MENTION_SOLDTO, eoh_flag=True)
        if not eoh_miss_data.empty:
            file_paths, email_config = set_table_content_email_config(email_config=copy.deepcopy(email_config),
                                                                      content_data=eoh_miss_data.to_dict(orient='records'),
                                                                      table_title=f"week {self.fiscal_week} Demand {BASE_PROCESSOR} soldto_mpn_pool 数据失败,e: {self.fiscal_week} shipment plan and eoh miss data",
                                                                      params=developer_subject_params)
            async_rate_limited_send_email_v2(limit_key=EMAIL_BUCKET_KEY_PREFIX + 'eoh_miss_data-' + self.fiscal_week,
                                             capacity=1,
                                             expire_time=1200,
                                             email_config=email_config,
                                             file_paths=file_paths)

        zero_list = group_df[group_df['avg_ub_1_5'] == 0].to_dict(orient='records')
        if zero_list:
            file_paths, email_config = set_table_content_email_config(email_config=copy.deepcopy(email_config),
                                                                      content_data=zero_list,
                                                                      table_title=f"week {self.fiscal_week} Demand {BASE_PROCESSOR} soldto_mpn_pool 数据异常: {self.fiscal_week}  avg_ub_1_5 is zero",
                                                                      params=developer_subject_params)
            async_rate_limited_send_email_v2(limit_key=EMAIL_BUCKET_KEY_PREFIX + 'avg_ub_1_5-' + self.fiscal_week,
                                             capacity=1,
                                             expire_time=1200,
                                             email_config=email_config,
                                             file_paths=file_paths)

    def __process_avg_ub(self, df_by_region, df_by_soldto) -> tuple[pd.DataFrame, pd.DataFrame]:
        # 处理avg_ub_1_5 数据  -> avg_ub_1_3
        first_five_weeks = self.__get_next_five_weeks(self.fiscal_week, 3)

        ubs = pandas.DataFrame(DemandEsrUbEoh.query_by_region_rtm_fiscal_weeks(CHINA_MAINLAND, first_five_weeks, RTMS_NO_RETAIL))
        # 先把nan转为none，否则pivot table时会没有nan的列
        ubs = fill_nan_to_none(ubs)
        df_ub_by_soldto = ubs.pivot_table(index=['region', 'sold_to_id', 'mpn'], columns='fiscal_week', values='ub',
                                          fill_value=0)
        df_ub_by_soldto['avg_ub_1_5'] = df_ub_by_soldto[first_five_weeks].mean(axis=1)
        # 拼接ub1到ub5的值为字符串，创建新列ub_1_5
        df_ub_by_soldto['ub_1_5'] = df_ub_by_soldto[first_five_weeks].astype(str).agg(','.join, axis=1)
        df_ub_by_soldto['fiscal_week'] = self.fiscal_week
        # 合并ub的数据到df_by_soldto
        df_by_soldto = pandas.merge(df_by_soldto, df_ub_by_soldto, on=['fiscal_week', 'sold_to_id', 'mpn'], how='left')

        # country数据处理
        county_ubs = pandas.DataFrame(DemandCountyEsrUbEoh.query_by_region_rtm_fiscal_weeks(CHINA_MAINLAND, first_five_weeks))
        county_ubs = fill_nan_to_none(county_ubs)
        df_ub_by_country = county_ubs.pivot_table(index=['region', 'mpn'], columns='fiscal_week', values='ub',
                                                  fill_value=0)
        df_ub_by_country['avg_ub_1_5'] = df_ub_by_country[first_five_weeks].mean(axis=1)
        df_ub_by_country['ub_1_5'] = df_ub_by_country[first_five_weeks].astype(str).agg(','.join, axis=1)
        df_ub_by_country['fiscal_week'] = self.fiscal_week
        df_by_region = pandas.merge(df_by_region, df_ub_by_country, on=['fiscal_week', 'region', 'mpn'], how='left')
        return df_by_region, df_by_soldto

    def __process_base(self) -> tuple[pd.DataFrame, pd.DataFrame]:

        demand_by_sold_to_pool = DemandBySoldtoPool.get_by_fiscal_week(self.fiscal_week)
        demand_by_sold_to_pool = demand_by_sold_to_pool[['fiscal_week', 'sold_to_id', 'sold_to_name', COLUMN_MPN, 'forecast_cw_sales', 'forecast_cw1_sales']]

        ub_eohs = pandas.DataFrame(DemandEsrUbEoh.query_eoh_by_region_rtm_fiscal_week(
            CHINA_MAINLAND, self.fiscal_week, RTMS_NO_RETAIL))
        if len(ub_eohs) == 0:
            raise Exception("app_fast_demand_esr_ub_eoh_wi is empty")

        county_ub_eohs = pandas.DataFrame(DemandCountyEsrUbEoh.query_eoh_by_region_rtm_fiscal_week(CHINA_MAINLAND, self.fiscal_week))
        if len(county_ub_eohs) == 0:
            raise Exception("app_fast_demand_county_esr_ub_eoh_wi is empty")

        bottom_ml_fcst = pandas.DataFrame(
            BottomUpMlForecast.query_all_forecast_by_week_and_lob(self.fiscal_week, Lob.IPHONE.value, CHINA_MAINLAND))
        if len(bottom_ml_fcst) == 0:
            raise Exception("ml_fcst is empty")
        nation_ml_fcst = pandas.DataFrame(
            CountryMlForecast.query_by_week_lob_region(self.fiscal_week, Lob.IPHONE.value, CHINA_MAINLAND))
        if len(nation_ml_fcst) == 0:
            raise Exception("national_fcst is empty")

        # 将bottom_ml_fcst和ub_eohs按照sold to id，mpn进行合并
        df_by_soldto = pandas.merge(ub_eohs, bottom_ml_fcst, on=['fiscal_week', 'sold_to_id', 'mpn'], how='left')
        # 合并得到 'forecast_cw_sales', 'forecast_cw1_sales'(7周的数据)
        df_by_soldto = pandas.merge(demand_by_sold_to_pool, df_by_soldto, on=['fiscal_week', 'sold_to_id', 'mpn'], how='left')

        # 重命名, forecst_ml(cw2~cw6) 赋值到 forecst_sales(cw2~cw6)
        df_by_soldto.rename(columns={'forecast_cw2_ml': 'forecast_cw2_sales',
                                     'forecast_cw3_ml': 'forecast_cw3_sales',
                                     'forecast_cw4_ml': 'forecast_cw4_sales',
                                     'forecast_cw5_ml': 'forecast_cw5_sales',
                                     'forecast_cw6_ml': 'forecast_cw6_sales'}, inplace=True)

        # znh: 通知并检测ML数据并用sales fcst填充
        df_by_soldto = self.__ml_forecast_preprocessing(df_by_soldto)

        # 计算rtm有ML数据的base，公式：base= Sum ML UB FCST (CW, CW+1 )  - CW-1 UB EOH (actual) - CW Shipment Plan
        df_by_soldto.loc[~df_by_soldto["rtm"].isin(RTMS_NO_ML), "base"] = (
            df_by_soldto["forecast_cw_ml"]
            + df_by_soldto["forecast_cw1_ml"]
            - df_by_soldto["ub_eoh"]
            - df_by_soldto["shipment_plan_cw"]
        )

        # 计算rtm无ML数据的base，公式：base= Sum Sales SO FCST (CW, CW+1 ) - CW-1 UB EOH (actual) - CW Shipment Plan
        df_by_soldto.loc[df_by_soldto["rtm"].isin(RTMS_NO_ML), "base"] = (
            df_by_soldto["forecast_cw_sales"]
            + df_by_soldto["forecast_cw1_sales"]
            - df_by_soldto["ub_eoh"]
            - df_by_soldto["shipment_plan_cw"]
        )
        # 特殊处理：转换为float类型，如果类型为object类型时，NaN类型不会被replace方法替换为None
        df_by_soldto['base'] = df_by_soldto['base'].astype(float)

        # 将获取到的nation_ml_fcst和county_ub_eohs按照fiscal_week, region, mpn聚合
        df_by_region = pandas.merge(county_ub_eohs, nation_ml_fcst, on=['fiscal_week', 'region', 'mpn'], how='left')
        df_by_region['base'] = df_by_region['forecast_cw_ml'] + df_by_region['forecast_cw1_ml'] - df_by_region['ub_eoh'] - df_by_region['shipment_plan_cw']
        
        # 更新fcst数据后，需要重新计算sales_fcst的avg_sales_fcst_2_4和avg_sales_fcst_3_5
        df_by_soldto['avg_sales_fcst_2_4'] = (df_by_soldto['forecast_cw2_sales'] + df_by_soldto['forecast_cw3_sales'] + df_by_soldto['forecast_cw4_sales']) / 3.0
        df_by_soldto['avg_sales_fcst_3_5'] = (df_by_soldto['forecast_cw3_sales'] + df_by_soldto['forecast_cw4_sales'] + df_by_soldto['forecast_cw5_sales']) / 3.0

        return df_by_region, df_by_soldto

    def __get_next_five_weeks(self, start_value: str, num):
        fiscal_weeks = get_demand_fiscal_weeks(IDEAL_DEMAND)

        # fiscal_weeks = FiscalYearWeek.get_last_n_week_by_qtr_week_name(start_value, 5)
        # 找到起始值的索引
        if start_value in fiscal_weeks:
            start_index = fiscal_weeks.index(start_value)
        else:
            # 如果起始值不在列表中，返回空列表
            return []

        # 计算从起始值开始后的五个元素
        end_index = start_index + num
        return fiscal_weeks[start_index:end_index]
