from datetime import datetime

from data.mysqls.demand.demand_processor import DemandProcessor
from domain.demand.entity.module_switch_enum import ModuleSwitchEnum
from domain.demand.impl.processor.const import WOI_PROCESSOR
from kit.custom_date.custom_week_date import CustomWeekDate


def is_all_process(fiscal_week: str, check_processors: list) -> bool:
    processors = DemandProcessor.query_by_fiscal_week(fiscal_week)

    if check_processors is None or len(check_processors) == 0:
        # 没有传 check 表明不需要检查，那就返回true
        return True

    if processors is None or len(processors) == 0:
        return False

    process = set(check_processors).issubset(set(processors))
    return process


class Processor:

    def __init__(self, processor: str, fiscal_week: str, max_do_times = 1) -> None:
        self.processor = processor
        self.fiscal_week = fiscal_week
        self.max_do_times = max_do_times  # 一周最多可行次数。-1表示无限制

    def __is_exceed_max_times(self)->bool:
        if self.max_do_times == -1:
            return False
        # 目前库里只有执行一次的记录,后面改造成支持多次执行
        has_record = self.__process_finished()
        if has_record:
            return True
        return False

    def process(self):
        # 1、检查任务状态
        if self.__is_exceed_max_times():
            return

        time_ready = self.__process_time_ready()
        if not time_ready:
            return

        can_do = self._can_do()
        if not can_do:
            return

        # 2、可执行的任务，触发do
        self._do()
        # 3、更新任务状态为成功
        self.__update_status()

    # 留给子类重写，失败要抛异常，会触发后面重试
    def _do(self) -> None:
        pass

    # 子类实现自身是否能执行的条件
    def _can_do(self) -> bool:
        return True

    # 流程是否已经完成
    def __process_finished(self) -> bool:
        return is_all_process(self.fiscal_week, [self.processor])

    # 流程是否已经完成
    def __process_time_ready(self) -> bool:
        return not CustomWeekDate(ModuleSwitchEnum.BEFORE_CALCULATION.value).is_before_ddl()

    def __update_status(self) -> bool:
        insert_obj = {'fiscal_week': self.fiscal_week, 'processor': self.processor}
        now = datetime.now()
        insert_obj['create_time'] = now
        insert_obj['update_time'] = now
        DemandProcessor().bulk_save([insert_obj])
        return True
