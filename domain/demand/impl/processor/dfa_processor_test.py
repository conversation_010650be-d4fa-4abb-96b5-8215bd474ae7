import math

import numpy as np
import pandas as pd
from domain.demand.impl.processor.const import DFA_PROCESSOR
from domain.demand.impl.processor.dfa_processor import DFAProcessor


def test_dn_curve():
    sold_to_df = pd.DataFrame({
        'region': ['China']*3,
        'fiscal_week': ['w1', 'w1', 'w1'],
        'sold_to_id': ['1', '2', '3'],
        'mpn': ['x', 'x', 'x'],
        'avg_ub_1_5': [10, 12, 14],
        'forecast_cw_ml': [20, 20, 20],
        'forecast_cw1_ml': [23, 23, 23],
        'forecast_cw2_sales': [21, 21, 21],
        'forecast_cw3_sales': [24,24, 24],
        'forecast_cw4_sales': [20, 20, 20],
        'forecast_cw5_sales': [22, 22, 22],
        'forecast_cw6_sales': [23, 23, 23],
    })

    region_df = pd.DataFrame({
        'fiscal_week': ['w1'],
        'region': ['China'],
        'mpn': ['x'],
        'avg_ub_1_5': [13],
        'forecast_cw_ml': [14],
        'forecast_cw1_ml': [12],
        'forecast_cw2_dfa': [14],
        'forecast_cw3_dfa': [18],
        'forecast_cw4_dfa': [17],
        'forecast_cw5_dfa': [11],
        'forecast_cw6_dfa': [14],
    })
    fiscal_week = 'w1'
    dfa_processor = DFAProcessor(DFA_PROCESSOR, fiscal_week)
    result_df = dfa_processor.calculate_dn_curve(fiscal_week, region_df, sold_to_df)
    result_df

