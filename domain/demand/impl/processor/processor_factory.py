from domain.demand.impl.processor.base_processor import BaseProcessor
from domain.demand.impl.processor.const import DFA_PROCESSOR, SALES_FORECAST_PROCESSOR, BASE_PROCESSOR
from domain.demand.impl.processor.dfa_processor import DFAProcessor
from domain.demand.impl.processor.sales_forecast_processor import SalesForecastProcessor


class ProcessFactory:

    def __init__(self, fiscal_week) -> None:
        self.fiscal_week = fiscal_week

    def get(self, process: str):
        if process == DFA_PROCESSOR:
            return DFAProcessor(DFA_PROCESSOR, self.fiscal_week)
        elif process == SALES_FORECAST_PROCESSOR:
            return SalesForecastProcessor(SALES_FORECAST_PROCESSOR, self.fiscal_week)
        elif process == BASE_PROCESSOR:
            return BaseProcessor(BASE_PROCESSOR, self.fiscal_week)
        else:
            raise ValueError(f'Unsupported processor: {process}')
