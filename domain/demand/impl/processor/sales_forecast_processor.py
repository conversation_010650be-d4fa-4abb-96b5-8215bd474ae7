import copy
import os

import pandas

from data.cpf_data_source import OdsFastCPFActiveSKULob
from data.databend.dashboard.demand_esr_ub_eoh import DemandEsrUbEoh
from data.databend.dashboard.sales_forecast_upload import SalesForecastUpload
from data.email_config import EmailConfigRepository
from data.mysqls.demand.demand_by_soldto_pool import DemandBy<PERSON>oldtoPool
from data.mysqls.demand.demand_x_setting import IdealDemandXValueSetting
from domain.demand.entity.const import CHINA_MAINLAND, RTMS, IDEAL_DEMAND, NO_MENTION_SOLDTO, RTMS_NO_RETAIL
from domain.demand.impl.processor.const import SALES_FORECAST_PROCESSOR
from domain.demand.impl.processor.processor import Processor
from domain.demand.impl.state_machine import StateProxy
from domain.supply.entity import Lob
from kit.pd import fill_nan_to_none
from kit.token_bucket import EMAIL_BUCKET_KEY_PREFIX
from util.const import EmailCmd
from util.send_email import set_table_content_email_config, async_rate_limited_send_email_v2


class SalesForecastProcessor(Processor):

    def __init__(self, processor: str, fiscal_week: str):
        super().__init__(processor, fiscal_week)

    def _can_do(self) -> bool:
        # 用户上传完数据提交后，状态机状态变为 WaitingForCalculation = 100，确保此状态后才执行，使用用户提交的数据进行计算
        state_proxy = StateProxy(self.fiscal_week, IDEAL_DEMAND)
        return state_proxy.is_waiting_for_calculation()

    def _do(self):
        fiscal_week = self.fiscal_week
        # 上传的 rtm sales forecast 数据
        sales_fcst = pandas.DataFrame(SalesForecastUpload.query_sales_by_region_rtm_fiscal_week(
            CHINA_MAINLAND, fiscal_week, RTMS_NO_RETAIL))
        # lite v1.1版本中如果ML中包含所有的数据，RTM则可以不用上传saless forecast
        if len(sales_fcst) == 0:
            # raise Exception('app_fast_dashboard_rtm_sales_forecast_upload_wi is not ready')
            return

        # 计算sales_fcst的avg_sales_fcst_2_4和avg_sales_fcst_3_5
        sales_fcst['avg_sales_fcst_2_4'] = (sales_fcst['forecast_cw2_sales'] + sales_fcst['forecast_cw3_sales'] + sales_fcst[
            'forecast_cw4_sales']) / 3.0
        sales_fcst['avg_sales_fcst_3_5'] = (sales_fcst['forecast_cw3_sales'] + sales_fcst['forecast_cw4_sales'] + sales_fcst[
            'forecast_cw5_sales']) / 3.0

        fields_to_update = ['forecast_cw_sales', 'forecast_cw1_sales','forecast_cw2_sales', 'forecast_cw3_sales',
                            'forecast_cw4_sales', 'forecast_cw5_sales', 'forecast_cw6_sales',
                            'forecast_cw7_sales', 'forecast_cw8_sales', 'forecast_cw9_sales', 'forecast_cw10_sales',
                            'forecast_cw11_sales', 'forecast_cw12_sales', 
                            'avg_sales_fcst_2_4', 'avg_sales_fcst_3_5',  'update_time']

        sales_fcst = fill_nan_to_none(sales_fcst)
        update_list = list(sales_fcst.to_dict(orient='records'))
        DemandBySoldtoPool.batch_update(update_list, fields_to_update)
