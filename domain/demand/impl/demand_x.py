import copy
import json
import os

from data.datasource_data import DataSourceFile
from data.mysqls.demand.demand_x_setting import IdealDemandXValueSetting
from data.mysqls.demand.demand_y_setting import IdealDemandYValueSetting
from domain.demand.entity.const import RTMS, RTM_SORT_RULE, RTMS_NO_RETAIL
from domain.demand.impl.ideal_demand_result import SYSTEM
from domain.demand.impl.rtm_state_proxy import RtmStateProxy
from data.mysqls.demand.rtm_sales_forecast_upload import FastLiteRTMSalesForecastUpload
from util.conf import logger
from util.const import ErrorExcept, ErrCode, DataSourceFileType
from util.send_email import async_send_email


def get_ideal_demand_x(fiscal_week: str, high_low_runner: str, region: str, rtm: str, sub_rtm: str, channel: str) -> list:
    rtm_status_obj = RtmStateProxy(fiscal_week=fiscal_week)
    # get x value
    x_values = IdealDemandXValueSetting.get_ideal_demand_x_value(fiscal_week, high_low_runner, region, rtm, sub_rtm)

    # 按照自定义顺序进行排序
    try:
        x_values = sorted(x_values, key=lambda x: RTM_SORT_RULE.index(x["rtm"]))
    except Exception as e:
        logger.error(f"sort rtms error: {e}")
    # get y value
    y_values = IdealDemandYValueSetting.query_ideal_demand_y_setting(fiscal_week, region, high_low_runner, rtm, sub_rtm)

    # init result
    result = []
    for x_value in x_values:
        hr_lr_type, rtm, sub_rtm, sub_lob, sold_to, twos, cw1, cw2 = x_value
        cw1_y_min = None
        cw1_y_max = None
        cw2_y_min = None
        cw2_y_max = None
        for y_value in y_values:
            if (rtm == y_value["rtm"] and sub_rtm == y_value["sub_rtm"] and sub_lob == y_value["sub_lob"]
                    and hr_lr_type == y_value["hr_lr_type"]):
                cw1_y_min = y_value["cw1_min"]
                cw1_y_max = y_value["cw1_max"]
                cw2_y_min = y_value["cw2_min"]
                cw2_y_max = y_value["cw2_max"]
                break

        if cw1_y_min is None or cw1_y_max is None or cw2_y_min is None or cw2_y_max is None:
            developer_subject = f"{os.environ.get('ENV')} Demand Query x"
            async_send_email(developer_subject, f"{fiscal_week}-->{rtm}-->{sub_rtm}:Query x not find y value")
        result_item = {
            "rtm": rtm,
            "sub_rtm": sub_rtm,
            "sold_to": sold_to,
            "sub_lob": sub_lob,
            "hr_lr_type": hr_lr_type,
            "twos": twos,
            "cw_1_x": cw1,
            "cw_2_x": cw2,
            "cw_1_y": [cw1_y_min, cw1_y_max],
            "cw_2_y": [cw2_y_min, cw2_y_max],
            "status": rtm_status_obj.get_rtm_status(rtm, channel).format()
        }
        result.append(result_item)
    return result


def get_rtm_sales_forecast_info(fiscal_week: str, channel: str) -> list:
    # system state
    rtm_status_obj = RtmStateProxy(fiscal_week=fiscal_week)

    # init result
    result = []
    query_rtms = RTMS_NO_RETAIL if channel == "CP&F" else [channel]
    for rtm in query_rtms:
        result_item = {
            "rtm": rtm,
            "lob": "iPhone",
        }

        # rtm upload info
        rtm_upload_record = FastLiteRTMSalesForecastUpload.query_by_rtm_fiscal_week(rtm=rtm, fiscal_week=fiscal_week)

        rtm_status = rtm_status_obj.get_rtm_status(rtm=rtm, channel=channel).format()

        file_name = None
        upload_time = None
        is_generated_by_system = None
        upload_by = None
        # CPF current_state>WaitingForRTMSetup or (current_state==WaitingForRTMSetup && rtm_status==1)才下发文件名相关信息
        # rtm 非CPF&F 上传后就可以下发文件名相关信息
        if rtm_upload_record and (channel != "CP&F" or rtm_status_obj.is_rtm_waiting_for_file(rtm)):
            file_name = f"iPhone_{rtm}_Sales_Forecast_{fiscal_week}"
            upload_by = rtm_upload_record[0]
            upload_time = rtm_upload_record[1].strftime('%Y-%m-%d %H:%M:%S')
            is_generated_by_system = 1 if upload_by == SYSTEM else 0

        result_item["file_name"] = file_name
        result_item["upload_time"] = upload_time
        result_item["upload_by"] = upload_by
        result_item["is_generated_by_system"] = is_generated_by_system
        result_item["status"] = rtm_status
        forcast_type = "ST" if rtm == "Online" else "UB"
        result_item["type"] = forcast_type
        result_item["template_file_name"] = f"iPhone_{rtm}_Sales_Forecast_Template_{fiscal_week}"
        result.append(result_item)
    return result


def get_x_menu(fiscal_week: str, region: str, rtm: str) -> list:
    return IdealDemandXValueSetting.get_region_rtm(fiscal_week=fiscal_week, region=region, rtm=rtm)


def update_x_setting(fiscal_week: str, settings: dict, region: str, lob: str) -> list:
    update_list = []
    for setting in settings:
        x_setting = copy.copy(setting)
        x_setting['fiscal_week'] = fiscal_week
        x_setting['cw1'] = setting.pop('cw_1_x')
        x_setting['cw2'] = setting.pop('cw_2_x')
        x_setting['sold_to_id'] = ''
        x_setting['sold_to_name'] = ''
        x_setting['twos'] = 0   # 此字段只是为了保证结构, 不会更新tows
        x_setting['lob'] = lob
        x_setting['region'] = region
        update_list.append(x_setting)
    IdealDemandXValueSetting.bulk_insert_or_update(update_list)
