import datetime
import json

from data.databend.end_to_end.forecast_feedback_by_soldto import ForecastFeedbackSoldto
from data.mysqls.demand.demand_by_region_pool import DemandByRegionPool
from data.mysqls.demand.sellin_demand_woi_setting import SellinDemandWoiSetting
from data.mysqls.end_to_end.feedback_demand_by_soldto import DemandFeedbackBySoldTo
from domain.demand.entity.const import *
from domain.demand.entity.module_switch_enum import ModuleSwitchEnum
from domain.demand.entity.woi_resolver import WoiResolver
from domain.demand.impl.calculator.delta_demand_calculator import DeltaDemandCalculator
from domain.demand.impl.calculator.final_demand.feedback_final_demand_calculator import FeedbackDemandCalculator
from domain.demand.impl.calculator.final_demand.final_demand_calculator import FinalDemandCalculator
from domain.demand.impl.calculator.sellin_demand_calculator import SellInDemandCalculator
from domain.demand.impl.ideal_demand_result import SYSTEM
from domain.demand.impl.processor.const import WOI_PROCESSOR
from domain.demand.impl.processor.woi_processor import WoiProcessor
from domain.demand.impl.state_machine import StateProxy
from kit.custom_date.custom_week_date import CustomWeekDate
from util.conf import logger
from util.const import WOITypes
from util.util import _async


def publish_final_demand_woi(fiscal_week: str, operator: str, woi_type: str):
    logger.info(f'{operator} start publish {fiscal_week} sell-in woi at {datetime.datetime.now()}')

    calculate_date = CustomWeekDate(ModuleSwitchEnum.BEFORE_CALCULATION.value)
    if calculate_date.is_before_ddl():
        logger.info(f'{operator} {fiscal_week} is before ddl, can not publish sell-in demand')
        raise Exception(f'{fiscal_week} needs to be published after {calculate_date.get_current_week_date()}')
    # 必须dt完成才能进行发布
    dt_proxy = StateProxy(fiscal_week, TOPDOWN_DEMAND)
    if not dt_proxy.is_completed():
        logger.info(f'{operator} {fiscal_week} dt is not completed, can not publish sell-in demand')
        return

    state_proxy = StateProxy(fiscal_week, SELL_IN_DEMAND)
    # 将状态改成waiting_for_calculation
    state_proxy.do_sellin_woi_publish()
    # 更新woi publish时间
    SellinDemandWoiSetting.update_publish_timestamp_by_fiscal_week(fiscal_week=fiscal_week, woi_type=woi_type,
                                                                   editor=operator)
    # 将woi存入pool表
    WoiProcessor(processor=WOI_PROCESSOR, fiscal_week=fiscal_week, woi_type=woi_type).process()

    sell_in_demand = SellInDemandCalculator(SELL_IN_DEMAND, fiscal_week, True)
    sell_in_demand.calculate()
    # 如果重新Submit后需要重新计算DD，DF
    recalculate_dd_and_df(fiscal_week, operator)
    # recalculate_df(fiscal_week, operator)

    state_proxy = StateProxy(fiscal_week, SELL_IN_DEMAND)
    logger.info(f'{operator} has published {fiscal_week} sell-in woi at {datetime.datetime.now()}')
    return state_proxy.current_state().format()


def publish_finalized_demand_woi(fiscal_week: str, operator: str, woi_type: str):
    logger.info(f'{operator} start publish {fiscal_week} 重新计算finalized demand woi at {datetime.datetime.now()}')

    # 更新woi publish时间
    SellinDemandWoiSetting.update_publish_timestamp_by_fiscal_week(fiscal_week=fiscal_week, woi_type=woi_type,
                                                                   editor=operator)

    sold_to_data: list[dict] = DemandFeedbackBySoldTo().find_by_fiscal_week_name(fiscal_week_name=fiscal_week)
    now = datetime.datetime.now()
    # 判断是否计算过
    is_calculated = True if sold_to_data is not None and len(sold_to_data) > 0 else False
    if not is_calculated:
        sold_to_data = ForecastFeedbackSoldto().find_by_fiscal_week_name(fiscal_week_name=fiscal_week)
        # 给sold_to_data添加finalized_demand_v2_cw1,finalized_demand_v2_cw2,create_time和update_time字段
        sold_to_data = [dict(item, finalized_demand_v2_cw1=None, finalized_demand_v2_cw2=None,
                             create_time=now, update_time=now) for item in sold_to_data]

    for item in sold_to_data:
        # 如果finalized_demand_v2为空，则用trail_demand替换
        if item['finalized_demand_v2_cw1'] is None:
            item['finalized_demand_v2_cw1'] = item['trial_demand_cw1']
        if item['finalized_demand_v2_cw2'] is None:
            item['finalized_demand_v2_cw2'] = item['trial_demand_cw2']
        item['demand3_publish_status'] = 1
        item['demand3_publish_time'] = now
        item['update_time'] = now
    if sold_to_data:
        fields_to_insert = [
            'fiscal_week_year', 'reseller_id', 'reseller_name', 'reseller_tier',
            'sold_to_name', 'rtm', 'sub_rtm', 'lob', 'sub_lob', 'nand', 'color',
            'mpn_type', 'mpn_desc', 'mpn_order', 'forecast_feedback_cw', 'forecast_feedback_cw1',
            'forecast_feedback_cw2', 'forecast_feedback_cw3', 'forecast_feedback_cw4', 'forecast_feedback_cw5',
            'forecast_feedback_cw6', 'forecast_feedback_cw7', 'forecast_feedback_cw8', 'forecast_feedback_cw9',
            'forecast_feedback_cw10', 'forecast_feedback_cw11', 'forecast_feedback_cw12', 'trial_demand_cw1',
            'trial_demand_cw2', 'trial_po_needed_cw1', 'trial_po_needed_cw2', 'finalized_demand_v2_cw1',
            'finalized_demand_v2_cw2', 'forecast_cw', 'forecast_cw1', 'forecast_cw2', 'forecast_cw3',
            'forecast_cw4', 'forecast_cw5', 'forecast_cw6', 'forecast_cw7', 'forecast_cw8', 'forecast_cw9',
            'forecast_cw10', 'forecast_cw11', 'forecast_cw12', 'shipment_plan_cw', 'shipment_plan_cw1',
            'shipment_plan_cw2', 'ub_eoh_lw', 'final_demand_cw1', 'final_demand_cw2', 'po_needed_cw1',
            'po_needed_cw2', 'open_backlog_over_published_sp', 'create_time', 'update_time', 'publish_time', 'publish_status',
            'demand3_publish_time', 'demand3_publish_status'
        ]
        fields_to_update = ['finalized_demand_v2_cw1', 'finalized_demand_v2_cw2', 'update_time', 'demand3_publish_time', 'demand3_publish_status']
        DemandFeedbackBySoldTo().batch_update(objs=sold_to_data, fields_to_insert=fields_to_insert,
                                              fields_to_update=fields_to_update)

    logger.info(f'{operator} has published {fiscal_week} finalized woi at {datetime.datetime.now()}')


def recalculate_df(fiscal_week: str, operator: str):
    start_time = datetime.datetime.now()
    logger.info(f'{operator} start recalculate {fiscal_week} Final Demand at {start_time}')
    calculator = FinalDemandCalculator(demand=FINAL_DEMAND, fiscal_week=fiscal_week, can_recalculate=True)
    calculator.calculate()
    end_time = datetime.datetime.now()
    logger.info(f'{operator} start recalculate {fiscal_week} Final Demand at {start_time},end at {end_time}')


@_async
def recalculate_dd_and_df(fiscal_week: str, operator: str):
    logger.info(f'{operator} start recalculate {fiscal_week} Delta Demand at {datetime.datetime.now()}')
    calculator = DeltaDemandCalculator(DELTA_DEMAND, fiscal_week, True)
    calculator.calculate()
    recalculate_df(fiscal_week, operator)


def get_woi_menu(fiscal_week: str, lob: str) -> list:
    return DemandByRegionPool.get_woi_menu(fiscal_week=fiscal_week, lob=lob)


def get_submit_flag(fiscal_week: str, woi_type: str) -> bool:
    return SellinDemandWoiSetting.query_publish_flag_by_fiscal_week(fiscal_week, woi_type)


def query_woi_settings(fiscal_week: str, woi_type) -> dict:
    woi_mpn_settings, woi_sublob_settings = SellinDemandWoiSetting.query_by_fiscal_week(fiscal_week, woi_type)
    return WoiResolver(fiscal_week=fiscal_week, woi_type=woi_type, woi_by_mpns=woi_mpn_settings,
                       woi_by_sublobs=woi_sublob_settings).as_dict()


def batch_update_woi_setting(fiscal_week: str, mpn_settings: list, sublob_settings: list, operator: str, woi_type: str):
    mpn_str = json.dumps(mpn_settings, indent=4)
    sublob_str = json.dumps(sublob_settings, indent=4)
    obj = {'woi_by_mpn': mpn_str, 'woi_by_sublob': sublob_str, 'fiscal_week': fiscal_week, 'woi_type': woi_type,
           'creator': operator, 'editor': operator}
    SellinDemandWoiSetting.bulk_insert_or_update(obj)
    if WOITypes.FeedbackDemand.value == woi_type:
        #  重新计算finalized demand
        FeedbackDemandCalculator(fiscal_week=fiscal_week).calculate()


def auto_publish_final_demand_woi(fiscal_week: str, woi_type):
    # 判断是否当周已经发布
    is_publish = SellinDemandWoiSetting.query_publish_flag_by_fiscal_week(fiscal_week, woi_type)
    if is_publish:
        return
    # 判断是否已经发布过，如果没有发布，再判断是否有用户的未发布的setting，如果有使用用户未发布的setting，没有使用默认的
    woi_by_mpns, woi_by_sublobs = SellinDemandWoiSetting.query_publish_woi_by_fiscal_week(fiscal_week, woi_type)

    if woi_by_mpns:
        # 存在 publish_timestamp!=0的数据，不再自动发布
        return
    # 需要发布
    un_published_woi_by_mpns, un_published_woi_by_sublobs = SellinDemandWoiSetting.query_un_publish_woi_by_fiscal_week(
        fiscal_week, woi_type)
    if not un_published_woi_by_sublobs:
        # 不存在未发布woi 查询默认的woi_settings 并保存
        settings = query_woi_settings(fiscal_week, woi_type)
        woi_by_mpn = settings['mpn_settings']
        woi_by_sub_lob = settings['sublob_settings']
        batch_update_woi_setting(fiscal_week, woi_by_mpn, woi_by_sub_lob, SYSTEM, woi_type)
    publish_final_demand_woi(fiscal_week, SYSTEM, woi_type)
