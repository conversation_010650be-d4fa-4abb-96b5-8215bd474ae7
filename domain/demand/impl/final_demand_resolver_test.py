import math
from itertools import groupby

import numpy as np
import pandas as pd

from domain.demand.entity.final_demand_detail import FinalDemandUpload
from domain.demand.impl.final_demand_resolver import FinalDemandResolver


def upload_test():

    upload_df = pd.DataFrame({
        'forecast_version': ['CW+1', 'CW+1', 'CW+1', 'CW+2', 'CW+2'],
        'sold_to_id': ['s1', 's2', 's3', 's4', 's5'],
        'SUB LOB': ['iPhone 14', 'iPhone 14', 'iPhone 15', 'iPhone 14', 'iPhone 13'],
        'mpn': ['m1', 'm1', 'm3', 'm4', 'm5'],
        'Normalized Demand': [9, 9, 9, 9, 9],
        'Delta Demand': [6, 6, 6, 6, 6]
    })
    # 解析上传的文件，sub_lob forecast_version 分组 key=sub_lob {key = forecast_version,value=list[FinalDemandUpload]}
    result: {str: {str: list[FinalDemandUpload]}} = {}
    grouped = upload_df.groupby('SUB LOB')
    for sub_lob, group in grouped:
        result[sub_lob] = {}
        version_grouped = group.groupby('forecast_version')
        for version, version_group in version_grouped:
            final_demand_list = [
                FinalDemandUpload(
                    row['forecast_version'],
                    row['sold_to_id'],
                    row['mpn'],
                    row['Normalized Demand'],
                    row['Delta Demand']
                )
                for _, row in version_group.iterrows()
            ]
            result[sub_lob][version] = final_demand_list
    # 获取sublobs
    sub_lobs = list(result.keys())
    sold_to = pd.DataFrame({
        'sold_to_id': ['s1', 's2', 's3', 's4', 's5'],
        'sub_lob': ['iPhone 14', 'iPhone 14', 'iPhone 15', 'iPhone 14', 'iPhone 13'],
        'mpn': ['m1', 'm1', 'm3', 'm4', 'm5'],
        'dn_cw1_adjusted': [1, 2, 3, 4, 5],
        'dn_cw2_adjusted': [1, 2, 3, 4, 5],
        'df_cw1_adjusted': [3, 2, 9, 2, 5],
        'df_cw2_adjusted': [4, 6, 8, 6, 5]
    })
    soldto_demand_list = sold_to.to_dict(orient='records')
    # 按照 sub_lob 进行分组
    soldto_demand_list.sort(key=lambda x: x['sub_lob'])
    grouped_by_sub_lob = {key: list(group) for key, group in groupby(soldto_demand_list, key=lambda x: x['sub_lob'])}

    for sub_lob, group in grouped_by_sub_lob.items():
        version_upload = result[sub_lob]

        adjust_cw_1: bool = 'CW+1' in version_upload
        adjust_cw_2: bool = 'CW+2' in version_upload

        if not adjust_cw_1 and not adjust_cw_2:
            continue

        demand_soldto_cw_1 = {d.sold_to_id + '-' + d.mpn: d for d in version_upload['CW+1']} if adjust_cw_1 else {}
        demand_soldto_cw_2 = {d.sold_to_id + '-' + d.mpn: d for d in version_upload['CW+2']} if adjust_cw_2 else {}

        for demand in group:
            key = demand['sold_to_id'] + '-' + demand['mpn']
            if adjust_cw_1 and adjust_cw_2:
                has_cw_1 = key in demand_soldto_cw_1
                has_cw_2 = key in demand_soldto_cw_2
                demand['dn_cw1_adjusted'] = demand_soldto_cw_1[key].final_dn if has_cw_1 else 0
                demand['df_cw1_adjusted'] = demand_soldto_cw_1[key].final_dn + demand_soldto_cw_1[key].delta_demand if has_cw_1 else 0
                demand['dn_cw2_adjusted'] = demand_soldto_cw_2[key].final_dn if has_cw_2 else 0
                demand['df_cw2_adjusted'] = demand_soldto_cw_2[key].final_dn + demand_soldto_cw_2[key].delta_demand if has_cw_2 else 0
            elif adjust_cw_1:
                has_cw_1 = key in demand_soldto_cw_1
                demand['dn_cw1_adjusted'] = demand_soldto_cw_1[key].final_dn if has_cw_1 else 0
                demand['df_cw1_adjusted'] = demand_soldto_cw_1[key].final_dn + demand_soldto_cw_1[key].delta_demand if has_cw_1 else 0
                # TODO 计算CW2  dn  cw2 计算  df cw2 计算


            else:
                has_cw_2 = key in demand_soldto_cw_2
                demand['dn_cw2_adjusted'] = demand_soldto_cw_2[key].final_dn if has_cw_2 else 0
                demand['df_cw2_adjusted'] = demand_soldto_cw_2[key].final_dn + demand_soldto_cw_2[key].delta_demand if has_cw_2 else 0
        print(f"{sub_lob}:")
        for item in group:
            print(f"  {item}")

    assert result


def sum_final_demand_detail_test():
    result = []
    # 根据 rtm  sub_lob  fiscal_week 获取soldto表数据
    soldto_df = pd.DataFrame({
        'fiscal_week': ['FY24Q3W10', 'FY24Q3W10', 'FY24Q3W10', 'FY24Q3W10'],
        'region': ['China mainland', 'China mainland', 'China mainland', 'China mainland'],
        'rtm': ['Mono', 'Mono', 'Muti', 'Online'],
        'sub_rtm': ['Lifestyle', '123', 'AAA', 'BBB'],
        'sold_to_id': ['531599', '531599', '531599', '531599'],
        'sold_to_name': ['s', 's', 's', 's'],
        'lob': ['iPhone', 'iPhone', 'iPhone', 'iPhone'],
        'sub_lob': ['iPhone 14', 'iPhone 14', 'iPhone 14', 'iPhone 14'],
        'nand': ['512GB', '512GB', '512GB', '512GB'],
        'color': ['YELLOW', 'YELLOW', 'YELLOW', 'YELLOW'],
        'mpn': ['mpn1', 'mpn2', 'mpn3', 'mpn4'],
        'hr_lr': ['HR', 'HR', 'LR', 'LR'],
        'shipment_plan_cw': [1, 2, 3, 4],
        'shipment_plan_cw1': [1, 2, 3, 4],
        'shipment_plan_cw2': [1, 2, 3, 4],
        'avg_dfa_fcst_2_4': [1, 2, 3, 4],
        'avg_dfa_fcst_3_5': [1, 2, 3, 4],
        'ub_eoh': [1, 2, 3, 4],
        'forecast_cw_ml': [1, 2, 3, 4],
        'forecast_cw1_ml': [1, 2, 3, 4],
        'forecast_cw_sales': [1, 2, 3, 4],
        'forecast_cw1_sales': [1, 2, 3, 4],
        'forecast_cw2_sales': [1, 2, 3, 4],
        'forecast_cw3_sales': [1, 2, 3, 4],
        'forecast_cw5_sales': [1, 2, 3, 4],
        'forecast_cw6_sales': [1, 2, 3, 4],
        'forecast_cw7_sales': [1, 2, 3, 4],
        'forecast_cw4_sales': [1, 2, 3, 4],
        'forecast_cw7_dfa': [1, 2, 3, 4],
        'forecast_cw8_dfa': [1, 2, 3, 4],
        'cw1_x': [1, 2, 3, 4],
        'cw2_x': [1, 2, 3, 4],
        'twos': [1, 2, 3, 4],
        'base': [1, 2, 3, 4],
        'avg_ub_1_5': [1, 2, 3, 4],
        'normalized_fcst_cw2': [1, 2, 3, 4],
        'normalized_fcst_cw3': [1, 2, 3, 4],
        'normalized_fcst_cw4': [1, 2, 3, 4],
        'normalized_fcst_cw5': [1, 2, 3, 4],
        'normalized_fcst_cw6': [1, 2, 3, 4],
        'ds_cw1': [1, 2, 3, 4],
        'ds_cw2': [1, 2, 3, 4],
        'dn_cw1': [1, 2, 3, 4],
        'dn_cw2': [1, 2, 3, 4],
        'final_dn_cw1': [1, 2, 3, 4],
        'final_dn_cw2': [1, 2, 3, 4],
        'df_cw1': [1, 2, 3, 4],
        'df_cw2': [1, 2, 3, 4],
    })

    soldto_df = pd.DataFrame({
        'region': ['China mainland', 'China mainland', 'China mainland', 'China mainland'],
        'rtm': ['Mono', 'Mono', 'Muti', 'Online'],
        'cw1': [1, 2, 3, 4],
        'cw2': [4, 2, 3, 4],
    })
    list_sum_all = FinalDemandResolver('', [], [], 'CW+1').sum_final_demand_detail(soldto_df)
    print(list_sum_all)

    assert len(list_sum_all) == 8 and list_sum_all[0].avg_actual_ub == 10
