from datetime import datetime
import pandas as pd

from data.mysqls.demand.demand_by_region_pool import DemandByRegionPool
from domain.demand.entity.const import BASE, DT_CW1, DT_CW2, DT_CW1_ORIGIN, DT_CW2_ORIGIN
from domain.demand.impl.calculator.calculator import Calculator, DemandResult
from kit.pd import replace_none_from_nan
from util.conf import logger


class TopdownDemandCalculator(Calculator):
    def __init__(self, demand: str, fiscal_week: str = None) -> None:
        super().__init__(demand, fiscal_week)

    """
    * Step 1: 将 DFA FCST 从 FPH3 Level break down 到 MPN Level
        * 计算公式：按 Final MPN Mix中各个mpn的比例, 将比例与fph3相乘, 可得到每个mpn的具体数值
        * 完成每周的 DFA FCST从by Country by FPH3 拆至by Country by MPN, 形成 by Country by MPN 的 DFA FCST
    * Step 2: 将DFA FCST与National ML FCST拼接组成Country Combined Forecast(by Country by MPN)
        * CW/CW+1 采用National ML FCST
        * CW+2~CW+8采用DFA FCST
    
    calculate 是做第三步的内容
    组合完数据之后的计算公式为: 
    dt_cw1 = Base + avg_dfa_fcst_2_4 * 5.5
    cw2的计算依赖cw1计算结果
    dt_cw2 = Base + forecast_cw2_dfa + avg_dfa_fcst_3_5 * 5.5 - dt_cw1
    
    * CW+1 周 by Country by MPN 级的 Top-down Demand：CW+1 Top-down Demand=Sum National ML FCST (CW , CW+1 ) +Average DFA FCST(CW+2 ~CW+4)*(TWOS) - CW-1 UB EOH (actual) - CW Shipment Plan
    * CW+2 周 by Country by MPN级的 Top-down Demand：CW+2 Top-down Demand=Sum National ML  FCST (CW , CW+1 ) + CW+2 DFA FCST + Average DFA FCST(CW+3 ~CW+5)*(TWOS) - CW-1 UB EOH (actual) - CW Shipment Plan - CW+1 Top-down Demand
    
    * Step3: 根据Country Combined Forecast (by Country by MPN), 计算Top-down Demand (DT)
        * CW+1 周 by Country by MPN 级的 Top-down Demand: CW+1 Top-down Demand = Base + Average DFA FCST(CW+2 ~ CW+4)*(TWOS)
        * CW+2 周 by Country by MPN级的 Top-down Demand: CW+2 Top-down Demand= Base + CW+2 DFA FCST + Average DFA FCST(CW+3 ~CW+5)*(TWOS) - CW+1 Top-down Demand
        * TWOS=5.5
        * CW-1 UB EOH/Shipment Plan 需聚合到 country  ???
    """

    def _calculate_cw1(self, origin_df: pd.DataFrame) -> pd.DataFrame:
        logger.debug(f"Top-down_demand start calculate cw+1 at {datetime.now()}")

        dt_twos = 5.5
        df = origin_df.copy()

        # 如果计算之后的dt 小于对应周的shipment plan，则取shipment plan的值
        df[DT_CW1] = df[BASE] + df['avg_dfa_fcst_2_4'] * dt_twos
        df[DT_CW1_ORIGIN] = df[DT_CW1]

        logger.debug(f"Top-down_demand complete cw1 calculation at {datetime.now()}")
        return df

    def _calculate_cw2(self, origin_df: pd.DataFrame) -> DemandResult:
        logger.info(f"Top-down_demand start calculate cw+2 at {datetime.now()}")

        dt_twos = 5.5
        df = origin_df.copy()

        df[DT_CW2] = df[BASE] + df['forecast_cw2_dfa'] + df['avg_dfa_fcst_3_5'] * dt_twos - df[DT_CW1]
        df[DT_CW2_ORIGIN] = df[DT_CW2]

        logger.info(f"Top-down_demand complete cw1 calculation at {datetime.now()}")
        return DemandResult(self.demand, df, [DT_CW1, DT_CW2], True)

    def _save(self, demand_by_country: pd.DataFrame, demand_by_soldto: pd.DataFrame):
        # 将数据插入数据库
        update_list = [DT_CW1, DT_CW2, DT_CW1_ORIGIN, DT_CW2_ORIGIN, 'update_time']
        to_dict = demand_by_country.to_dict(orient='records')
        replace_none_from_nan(to_dict, [DT_CW1, DT_CW2, DT_CW1_ORIGIN, DT_CW2_ORIGIN])
        DemandByRegionPool.batch_update(to_dict, update_list)

    def _get_calculate_df(self, week: str) -> pd.DataFrame:
        return DemandByRegionPool.get_by_fiscal_week(week)
