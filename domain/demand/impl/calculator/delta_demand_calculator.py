import math

import numpy as np
import pandas as pd

from data.mysqls.demand.demand_by_region_pool import DemandByRegionPool
from data.mysqls.demand.demand_by_soldto_pool import DemandBySoldtoPool
from domain.demand.entity.const import BASE, DN_CW1, DS_CW1, DS_CW2, FINAL_DN_CW1, FINAL_DN_CW2, DN_CW1_ADJUSTED, \
    DN_CW2_ADJUSTED, BASE_DEMAND_CW1, BASE_DEMAND_CW2, TWOS, CHINA_MAINLAND, TWOS_CW2
from domain.demand.entity.demand import DemandCw1, DemandCw2
from domain.demand.impl.calculator.calculator import Calculator, DemandR<PERSON>ult
from domain.demand.impl.y_value_setting import get_twos_by_dataframe
from kit.pd import replace_none_from_nan
from util.conf import logger


class DeltaDemandCalculator(Calculator):

    def __init__(self, demand: str, fiscal_week=None, can_recalculate: bool=False) -> None:
        super().__init__(demand, fiscal_week, can_recalculate)
        # self.mpn_ds = {}
        self.woi_decr = 0.2
        self.region_df = pd.DataFrame()
        self.reduce_demand = 1

    def _get_calculate_df(self, week: str) -> pd.DataFrame:
        region_df = DemandByRegionPool.get_by_fiscal_week(week)
        # self.mpn_ds = region_df.set_index('mpn').T.to_dict()
        self.region_df = region_df
        dataframe = DemandBySoldtoPool.get_by_fiscal_week(week)
        dataframe = get_twos_by_dataframe(week, CHINA_MAINLAND, dataframe)
        return dataframe

    def _calculate_cw1(self, df: pd.DataFrame) -> pd.DataFrame:
        # key = mpn value = [dealers]
        mpn_dealers: dict[str, list[DeltaDemandDealer]] = {}
        for index, row in df.iterrows():
            row = row.to_frame().T.reset_index(drop=True)
            mpn = row['mpn'][0]
            soldto_id = row['sold_to_id'][0]
            delta_demand_dealer = DeltaDemandDealer(soldto_id, mpn, row[BASE][0],
                                                    row['normalized_fcst_cw2'][0], row['normalized_fcst_cw3'][0],
                                                    row['normalized_fcst_cw4'][0], row['normalized_fcst_cw5'][0],
                                                    row[TWOS][0], row[DN_CW1][0],
                                                    row['cw1_ideal_demand'][0], row['cw2_ideal_demand'][0],
                                                    row['shipment_plan_cw1'][0], row['shipment_plan_cw2'][0],
                                                    is_cw1=True)
            # 初始化时可能因为计算demand=0直接跳过，导致保存数据到df时应该是0的数据存为了null
            # if delta_demand_dealer.is_stop():
            #     continue
            if mpn not in mpn_dealers:
                mpn_dealers[mpn] = []
            mpn_dealers[mpn].append(delta_demand_dealer)

        # 按照region mpn级别进行比较
        num = 1
        for mpn, dealers in mpn_dealers.items():
            num += 1
            logger.info(f"delta_demand cw1 mpn={mpn} num={num}")
            # 计算 dn ds和，并比较
            while True:
                region_ds = self.get_field_by_mpn_from_region(mpn, DS_CW1)
                region_dn = sum_demand(dealers)
                if region_ds >= region_dn:
                    break
                # 对 normalized_demand 进行调减，twos - 0.01
                for dealer in dealers:
                    if region_ds == 0:
                        dealer.stop()
                        dealer.set_demand(0)
                    if dealer.is_stop():
                        continue
                    # 调减的woi设置最少削减的demand数量
                    _woi = dealer.woi_by_demand(dealer.demand - self.reduce_demand)
                    cur_woi = dealer.woi() - self.woi_decr
                    dealer.reduce_woi(min(_woi, cur_woi))

        # 计算结果重新覆盖到dn上
        df[FINAL_DN_CW1] = np.nan
        df[BASE_DEMAND_CW1] = np.nan
        for mpn, dealers in mpn_dealers.items():
            for dealer in dealers:
                # df.loc[(df['mpn'] == mpn) & (df['soldto_id'] == dealer.soldto_id), 'delta_ds_cw1'] = dealer.delta_dn
                # 赋值final_dn_cw1时取di和dn_cw1的最小值，令country维度dd + dn = ds
                min_di_final_dn = min(dealer.demand, dealer.cw1_ideal_demand)
                df.loc[(df['mpn'] == mpn) & (df['sold_to_id'] == dealer.soldto_id), DN_CW1] = dealer.demand  # 用于之后计算
                df.loc[(df['mpn'] == mpn) & (
                        df['sold_to_id'] == dealer.soldto_id), FINAL_DN_CW1] = dealer.demand  # 保存到soldto
                df.loc[(df['mpn'] == mpn) & (
                        df['sold_to_id'] == dealer.soldto_id), BASE_DEMAND_CW1] = min_di_final_dn  # 保存到soldto
        return df

    def _calculate_cw2(self, df: pd.DataFrame) -> DemandResult:
        # key = mpn value = [dealers]
        mpn_dealers: dict[str, list[DeltaDemandDealer]] = {}
        for index, row in df.iterrows():
            row = row.to_frame().T.reset_index(drop=True)
            mpn = row['mpn'][0]
            soldto_id = row['sold_to_id'][0]
            dealer = DeltaDemandDealer(soldto_id, mpn, row[BASE][0],
                                       row['normalized_fcst_cw2'][0], row['normalized_fcst_cw3'][0],
                                       row['normalized_fcst_cw4'][0], row['normalized_fcst_cw5'][0],
                                       row[TWOS_CW2][0], row[DN_CW1][0], row['cw1_ideal_demand'][0],
                                       row['cw2_ideal_demand'][0],
                                       row['shipment_plan_cw1'][0], row['shipment_plan_cw2'][0],
                                       is_cw1=False)
            # 为了保证保存数据时mpn_dealers数据完整，初始化时不再跳过
            # if dealer.is_stop():
            #     continue
            if mpn not in mpn_dealers:
                mpn_dealers[mpn] = []
            mpn_dealers[mpn].append(dealer)
        # 按照region mpn级别进行比较
        num = 1
        for mpn, dealers in mpn_dealers.items():
            num += 1
            logger.info(f"delta_demand cw2 mpn={mpn} num={num}")
            # 计算 dn ds和，并比较
            while True:
                region_ds = self.get_field_by_mpn_from_region(mpn, DS_CW2)
                region_dn = sum_demand(dealers)
                if region_ds >= region_dn:
                    break
                # 对 normalized_demand 进行调减，twos - 0.01
                for dealer in dealers:
                    if region_ds == 0:
                        dealer.stop()
                        dealer.set_demand(0)
                    if dealer.is_stop():
                        continue
                    # woi 计算调用 DemandCw1
                    # 调减的woi设置最少削减的demand数量
                    _woi = dealer.woi_by_demand(dealer.demand - self.reduce_demand)
                    cur_woi = dealer.woi() - self.woi_decr
                    dealer.reduce_woi(min(_woi, cur_woi))

        # 将结果写到到df上
        df[FINAL_DN_CW2] = np.nan
        df[BASE_DEMAND_CW2] = np.nan
        for mpn, dealers in mpn_dealers.items():
            for dealer in dealers:
                # 赋值final_dn_cw1时取di和dn_cw1的最小值，令country维度dd + dn = ds
                df.loc[(df['mpn'] == mpn) & (df['sold_to_id'] == dealer.soldto_id), FINAL_DN_CW2] = dealer.demand
                df.loc[(df['mpn'] == mpn) & (df['sold_to_id'] == dealer.soldto_id), BASE_DEMAND_CW2] = min(dealer.demand, dealer.cw2_ideal_demand)

        return DemandResult(self.demand, df, [FINAL_DN_CW1, FINAL_DN_CW2, BASE_DEMAND_CW1, BASE_DEMAND_CW2], is_region=False)

    def _save(self, demand_by_country: pd.DataFrame, demand_by_soldto: pd.DataFrame):
        # demand_by_soldto[DN_CW1_ADJUSTED] = demand_by_soldto[FINAL_DN_CW1]
        # demand_by_soldto[DN_CW2_ADJUSTED] = demand_by_soldto[FINAL_DN_CW2]
        soldto_to_dict = demand_by_soldto.to_dict(orient='records')
        replace_none_from_nan(soldto_to_dict, [FINAL_DN_CW1, FINAL_DN_CW2, BASE_DEMAND_CW1, BASE_DEMAND_CW2])
        DemandBySoldtoPool.batch_update(soldto_to_dict,
                                        [FINAL_DN_CW1, FINAL_DN_CW2, BASE_DEMAND_CW1, BASE_DEMAND_CW2, 'update_time'])
        # region 的数据保存  因为soldto上没有ds的数据，所以还没有计算，需要实时计算一下dd
        to_dict = demand_by_country.to_dict(orient='records')
        replace_none_from_nan(to_dict, [FINAL_DN_CW1, FINAL_DN_CW2, BASE_DEMAND_CW1, BASE_DEMAND_CW2])
        DemandByRegionPool.batch_update(to_dict, [FINAL_DN_CW1, FINAL_DN_CW2, BASE_DEMAND_CW1, BASE_DEMAND_CW2, 'update_time'])

    def get_field_by_mpn_from_region(self, mpn, field):
        # 通过布尔索引筛选出符合mpn的行
        field_value = self.region_df.loc[self.region_df['mpn'] == mpn, field]
        # 如果没有找到对应的mpn，返回None
        if field_value.empty:
            return None
        else:
            return field_value.values[0]


class DeltaDemandDealer:
    def __init__(self, soldto_id: str,
                 mpn: str, base, normalized_fcst_cw2,
                 normalized_fcst_cw3,
                 normalized_fcst_cw4,
                 normalized_fcst_cw5, twos, dn_cw1, cw1_ideal_demand, cw2_ideal_demand, shipment_plan_cw1, shipment_plan_cw2, is_cw1: bool) -> None:

        self.soldto_id = soldto_id
        self.mpn = mpn
        self.base = base
        self.flag = False

        normalized_fcst_cw2 = 0 if normalized_fcst_cw2 is None or np.isnan(normalized_fcst_cw2) else normalized_fcst_cw2
        normalized_fcst_cw3 = 0 if normalized_fcst_cw3 is None or np.isnan(normalized_fcst_cw3) else normalized_fcst_cw3
        normalized_fcst_cw4 = 0 if normalized_fcst_cw4 is None or np.isnan(normalized_fcst_cw4) else normalized_fcst_cw4
        normalized_fcst_cw5 = 0 if normalized_fcst_cw5 is None or np.isnan(normalized_fcst_cw5) else normalized_fcst_cw5

        self.avg_normalized_2_4 = np.mean([normalized_fcst_cw2, normalized_fcst_cw3, normalized_fcst_cw4])
        self.avg_normalized_3_5 = np.mean([normalized_fcst_cw3, normalized_fcst_cw4, normalized_fcst_cw5])
        self.normalized_fcst_cw2 = normalized_fcst_cw2

        self.dn_cw1 = dn_cw1
        self.cw1_ideal_demand = 0 if cw1_ideal_demand is None or np.isnan(cw1_ideal_demand) else cw1_ideal_demand
        self.cw2_ideal_demand = 0 if cw2_ideal_demand is None or np.isnan(cw2_ideal_demand) else cw2_ideal_demand

        self.is_cw1 = is_cw1
        # 如果初始化为None 会导致此列 存在 None 0, 这样在fill_nan_to_none时会把None 转化为nan。
        self.demand = np.nan
        if self.is_cw1:
            self.shipment_plan = shipment_plan_cw1
        else:
            self.shipment_plan = shipment_plan_cw2
        self.reduce_woi(twos)

    def is_stop(self):
        return self.flag

    def stop(self):
        self.flag = True

    def set_demand(self, demand):
        self.demand = demand
        if self.demand < self.shipment_plan:
            self.demand = self.shipment_plan
            self.stop()
        if self.demand <= 0:
            self.demand = 0
            self.stop()

    def reduce_woi(self, woi: float):
        if woi is None or self.base is None:
            self.stop()
            return

        if math.isnan(woi) or math.isnan(self.base):
            self.stop()
            return
        # 需要调减woi，公式中如果avg_normalized_2_4 或 avg_normalized_3_5为0，则无法向下调减，无法计算
        if self.avg_normalized_2_4 == 0 or self.avg_normalized_3_5 == 0:
            self.stop()
            return

        demand = self.base + self.avg_normalized_2_4 * woi
        if not self.is_cw1:
            if self.dn_cw1 is None or np.isnan(self.dn_cw1):
                self.stop()
                return
            demand = self.base + self.normalized_fcst_cw2 + self.avg_normalized_3_5 * woi - self.dn_cw1
        self.set_demand(demand)

    def woi(self) -> float:
        if self.is_cw1:
            demand_cw1 = DemandCw1(self.demand, self.base, self.avg_normalized_2_4)
            return demand_cw1.woi()
        demand_cw2 = DemandCw2(self.demand, self.dn_cw1, self.base, self.avg_normalized_3_5, self.normalized_fcst_cw2)
        return demand_cw2.woi()

    def woi_by_demand(self, demand) -> float:
        if self.is_cw1:
            demand_cw1 = DemandCw1(demand, self.base, self.avg_normalized_2_4)
            return demand_cw1.woi()
        demand_cw2 = DemandCw2(demand, self.dn_cw1, self.base, self.avg_normalized_3_5, self.normalized_fcst_cw2)
        return demand_cw2.woi()


def sum_demand(dealers: list[DeltaDemandDealer]) -> float:
    region_dn = 0.0
    # region_ds = 0.0
    for dealer in dealers:
        if dealer.is_stop():
            continue
        if dealer.demand is not None and not np.isnan(dealer.demand):
            region_dn += dealer.demand
        # region_ds += dealer.ds
    return region_dn
