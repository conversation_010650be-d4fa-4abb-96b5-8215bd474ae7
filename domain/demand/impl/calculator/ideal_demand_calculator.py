import pandas
import pandas as pd

from data.mysqls.demand.demand_by_region_pool import DemandByRegionPool
from data.mysqls.demand.demand_by_soldto_pool import DemandBySoldtoPool
from domain.demand.entity.const import *
from domain.demand.impl.calculator.calculator import Calculator, DemandResult
from domain.demand.impl.y_value_setting import get_twos_by_dataframe
from kit.pd import replace_none_from_nan


def filter_by_mpn_and_soldto(
        df: pandas.DataFrame, mpns_to_filter: list[str], soldtoids_to_filter: list[str],
) -> pandas.DataFrame:
    filtered_df = df[df[COLUMN_MPN].isin(mpns_to_filter) & df[COLUMN_SOLD_TO_ID].isin(soldtoids_to_filter)]
    return filtered_df


class IdealDemandCalculator(Calculator):

    def __init__(self, demand: str, fiscal_week: str = None) -> None:
        super().__init__(demand, fiscal_week)

    def _calculate_cw1(self, origin_df: pd.DataFrame) -> pd.DataFrame:
        # 保留需要的字段
        df = origin_df[['fiscal_week', 'rtm', 'sub_rtm', 'sold_to_id', 'sold_to_name', 'lob',
                                         'sub_lob', 'region', COLUMN_MPN, 'forecast_cw_ml', 'forecast_cw1_ml',
                                         'forecast_cw_sales', 'forecast_cw1_sales', 'forecast_cw2_sales',
                                         'forecast_cw3_sales', 'forecast_cw4_sales', 'forecast_cw5_sales',
                                         AVERAGE_SALES_WEEKS_2_4, AVERAGE_SALES_WEEKS_3_5, UB_EOH, CW_SHIPMENT_PLAN,
                                         CW1_SHIPMENT_PLAN, CW2_SHIPMENT_PLAN, 'base', 'nand', 'color', TWOS, TWOS_CW2]] # 这是的TWOS是使用了twos + cw1_y的数据

        # CW+1 Ideal Demand=Average Sales UB FCST(CW+2 ~CW+6)*(TWOS+X) + Base Data
        df = calculate_cw1(df)
        return df

    def _calculate_cw2(self, origin_df: pd.DataFrame) -> DemandResult:
        # CW+2 Ideal Demand=CW+2 Sales UB FCST + Average Sales UB FCST(CW+3 ~CW+7)*(TWOS+X) -CW+1 Demand + Base Data
        df = calculate_cw2(origin_df)
        return DemandResult(self.demand, df, [DI_CW1, DI_CW2])

    def _save(self, demand_by_country: pd.DataFrame, demand_by_soldto: pd.DataFrame):
        # 将数据插入数据库
        soldto_update_list = [DI_CW1, DI_CW2, 'cw1_ideal_demand_origin', 'cw2_ideal_demand_origin', 'update_time']
        region_update_list = [DI_CW1, DI_CW2, 'update_time']

        soldto_to_dict = demand_by_soldto.to_dict(orient='records')
        replace_none_from_nan(soldto_to_dict, [DI_CW1, DI_CW2, 'cw1_ideal_demand_origin', 'cw2_ideal_demand_origin'])
        DemandBySoldtoPool.batch_update(soldto_to_dict, soldto_update_list)

        country_to_dict = demand_by_country.to_dict(orient='records')
        replace_none_from_nan(country_to_dict, [DI_CW1, DI_CW2])
        DemandByRegionPool.batch_update(country_to_dict, region_update_list)

    def _get_calculate_df(self, week: str) -> pd.DataFrame:
        dataframe = DemandBySoldtoPool.get_by_fiscal_week(week)
        dataframe = get_twos_by_dataframe(week, CHINA_MAINLAND, dataframe)
        return dataframe


def calculate_cw1(new_df):
    # Mono(有ML UB FCST 后)/Multi/Carrier:  CW+1 RTM Ideal Demand = Sum ML UB FCST (CW, CW+1 ) + Average Sales UB FCST(CW+2～CW+4)*(TWOS+X) - CW-1 UB EOH (actual) - CW Shipment Plan
    # Online: RTM CW+1 Ideal Demand= Sum Sales SO FCST (CW, CW+1 ) + Average Sales SO FCST (CW+2～CW+4)*(TWOS+X) - CW-1 SO EOH (actual) - CW Shipment Plan
    new_df[DI_CW1] = new_df['base'] + new_df[AVERAGE_SALES_WEEKS_2_4] * (new_df[TWOS])
    new_df['cw1_ideal_demand_origin'] = new_df[DI_CW1]
    return new_df.copy()


def calculate_cw2(new_df):
    # Mono(有ML UB FCST 后)/Multi/Carrier:  CW+2 RTM Ideal Demand=Sum ML UB FCST (CW , CW+1 ) + CW+2 Sales UB FCST + Average Sales UB FCST(CW+3～CW+5）*(TWOS+X) - CW-1 UB EOH (actual) - CW Shipment Plan - CW+1 RTM Ideal Demand
    # Online: CW+1 RTM Ideal Demand= Sum Sales SO FCST (CW, CW+1, CW+2) + Average Sales SO FCST (CW+3～CW+5)*(TWOS+X) - CW-1 SO EOH (actual) - CW Shipment Plan - CW+1 RTM Ideal Demand
    new_df[DI_CW2] = new_df['base'] + new_df['forecast_cw2_sales'] + new_df[AVERAGE_SALES_WEEKS_3_5] * (new_df[TWOS_CW2]) - new_df[DI_CW1]
    new_df['cw2_ideal_demand_origin'] = new_df[DI_CW2]
    return new_df



