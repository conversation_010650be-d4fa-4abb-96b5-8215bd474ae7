import math

import pandas as pd

from data.mysqls.demand.demand_by_region_pool import DemandByRegionPool
from data.mysqls.demand.demand_by_soldto_pool import DemandBySoldtoPool
from domain.demand.entity.const import DN_CW1, DN_CW2, BASE, COLUMN_MPN, TWOS, TWOS_CW2, CHINA_MAINLAND
from domain.demand.impl.calculator.calculator import Calculator, DemandResult
from domain.demand.impl.y_value_setting import get_twos_by_dataframe
from kit.pd import replace_none_from_nan


class NormalizedDemandCalculator(Calculator):
    def __init__(self, demand: str, fiscal_week: str = None) -> None:
        super().__init__(demand, fiscal_week)

    # CW+1 DN =Sum ML UB FCST (CW , CW+1 ) + Average RTM Normalized FCST (CW+2 ~CW+4)*(TWOS+X) - CW-1 UB EOH (actual) - CW Shipment Plan
    def _calculate_cw1(self, df: pd.DataFrame) -> pd.DataFrame:
        # df[DN_CW1] = df[BASE] + (df['normalized_fcst_cw2'] + df['normalized_fcst_cw3'] + df['normalized_fcst_cw4']) * \
        #              df['twos'] / 3
        df[DN_CW1] = df[BASE] + df[['normalized_fcst_cw2', 'normalized_fcst_cw3', 'normalized_fcst_cw4']].mean(axis=1) * df[TWOS]

        return df

    # CW+2 DN=Sum ML UB FCST (CW , CW+1 ) + CW+2  RTM Normalized FCST + Average RTM Normalized FCST(CW+3 ~CW+5)*(TWOS+X) - CW-1 UB EOH (actual) - CW Shipment Plan - CW+1 DN
    def _calculate_cw2(self, df: pd.DataFrame) -> DemandResult:
        df[DN_CW2] = df[BASE] + df['normalized_fcst_cw2'] + df[['normalized_fcst_cw3', 'normalized_fcst_cw4', 'normalized_fcst_cw5']].mean(axis=1) * df[TWOS_CW2] - df[DN_CW1]
        return DemandResult(self.demand, df, [DN_CW1, DN_CW2])

    def _save(self, demand_by_country: pd.DataFrame, demand_by_soldto: pd.DataFrame):
        # 保存  dn_cw1 dn_cw2 到sold to 表，by fiscal_week,sold_id,mpn
        fields_to_update = [DN_CW1, DN_CW2]
        demand_dict = demand_by_soldto.to_dict(orient='records')
        replace_none_from_nan(demand_dict, fields_to_update)
        DemandBySoldtoPool.batch_update(demand_dict, fields_to_update)

        # 保存聚合后的 rtm_dn_cw1 rtm_dn_cw2 到 region pool  by region,mpn
        counry_dict = demand_by_country.to_dict(orient='records')
        replace_none_from_nan(counry_dict, fields_to_update)
        DemandByRegionPool.batch_update(counry_dict, fields_to_update)

    def _get_calculate_df(self, week: str) -> pd.DataFrame:
        dataframe = DemandBySoldtoPool.get_by_fiscal_week(week)
        dataframe = get_twos_by_dataframe(week, CHINA_MAINLAND, dataframe)
        return dataframe

