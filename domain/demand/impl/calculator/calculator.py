import os
import traceback

import pandas as pd

from data.mysqls.demand.demand_state import TblDemandState
from domain.demand.entity.const import *
from domain.demand.entity.module_switch_enum import ModuleSwitchEnum
from domain.demand.entity.state import DemandState
from domain.demand.impl.processor.const import BASE_PROCESSOR, SALES_FORECAST_PROCESSOR, DFA_PROCESSOR, WOI_PROCESSOR
from domain.demand.impl.processor.processor import is_all_process
from domain.demand.impl.state_machine import StateProxy
from kit.custom_date.custom_week_date import CustomWeekDate
from kit.pd import set_target_if_less, set_target_if_less_by_column, set_target_by_none_column, fill_nan_to_none
from kit.token_bucket import EMAIL_BUCKET_KEY_PREFIX
from util.conf import logger
from util.const import ErrorExcept
from util.send_email import async_rate_limited_send_email


class DemandResult:
    def __init__(self, demand_name: str, df: pd.DataFrame, columns: list[str], is_region: bool = False):
        self.df = df
        self.demand_name = demand_name
        self.columns = columns
        self.is_region = is_region

    def get_df(self) -> pd.DataFrame:
        return self.df

    def group_by_region(self) -> pd.DataFrame:
        if self.is_region:
            return self.df

        # 将数据按照sales_org和mpn聚合
        country_df = self.df.groupby(['fiscal_week', 'region', COLUMN_MPN])[self.columns].sum().reset_index()
        country_df = fill_nan_to_none(country_df)
        return country_df

    def cw1_group_by_sublob(self) -> pd.DataFrame:
        # 将by Country by MPN的DS’ 汇总至by Country by sublob
        sublob_df = self.df.groupby(['region', 'sub_lob'])[BASE, 'forecast_cw2_dfa', AVERAGE_DFA_WEEKS_2_4, AVERAGE_DFA_WEEKS_3_5, CW1_SHIPMENT_PLAN, CW2_SHIPMENT_PLAN, DI_CW1, DI_CW2].sum().reset_index()
        key = ['region', 'sub_lob', 'woi_by_sublob_max']
        woi_df = self.df.drop_duplicates(subset=key)
        woi_df = woi_df[key]
        sublob_df = pd.merge(sublob_df, woi_df, on=['region', 'sub_lob'], how='left')
        return sublob_df

    def cw2_group_by_sublob(self) -> pd.DataFrame:
        # 将by Country by MPN的DS’ 汇总至by Country by sublob
        sublob_df = self.df.groupby(['region', 'sub_lob'])[BASE, 'forecast_cw2_dfa', AVERAGE_DFA_WEEKS_2_4, AVERAGE_DFA_WEEKS_3_5, CW1_SHIPMENT_PLAN, CW2_SHIPMENT_PLAN, DI_CW1, DI_CW2, DS_CW1].sum().reset_index()
        key = ['region', 'sub_lob', 'woi_by_sublob_max_cw2']
        woi_df = self.df.drop_duplicates(subset=key)[key]
        sublob_df = pd.merge(sublob_df, woi_df, on=['region', 'sub_lob'], how='left')
        return sublob_df

    def modify(self):
        self.df = set_target_if_less(self.df, self.columns[1], 0)

        #  和shipment plan的比较：DI、DT、DS、DF需要，DN、DD不需要
        if self.demand_name != NORMALIZED_DEMAND and self.demand_name != DELTA_DEMAND and self.demand_name != FINAL_DEMAND:
            self.df = set_target_if_less_by_column(self.df, self.columns[0], CW1_SHIPMENT_PLAN)
            self.df = set_target_if_less_by_column(self.df, self.columns[1], CW2_SHIPMENT_PLAN)
            # 添加兜底逻辑，将DI_CW1和DI_CW2为None的数据设置CW1_SHIPMENT_PLAN和CW2_SHIPMENT_PLAN
            self.df = set_target_by_none_column(self.df, self.columns[0], CW1_SHIPMENT_PLAN)
            self.df = set_target_by_none_column(self.df, self.columns[1], CW2_SHIPMENT_PLAN)

        # 3、把所有列中出现的nan，转为none
        df = fill_nan_to_none(self.df)

        return df


class Calculator:
    def __init__(self, demand: str, fiscal_week=None, can_recalculate: bool = False) -> None:
        self.demand = demand
        self.fiscal_week = fiscal_week
        self.can_recalculate = can_recalculate

        columns_of_result = {
            IDEAL_DEMAND: [DI_CW1, DI_CW2],
            TOPDOWN_DEMAND: [DT_CW1, DT_CW2],
            SELL_IN_DEMAND: [DS_CW1, DS_CW2],
            NORMALIZED_DEMAND: [DN_CW1, DN_CW2],
            DELTA_DEMAND: [FINAL_DN_CW1, FINAL_DN_CW2],
            FINAL_DEMAND: [DF_CW1,DF_CW2],
        }
        self.columns = columns_of_result[demand]

    def calculate(self):
        if self.fiscal_week:
            previous_five_weeks = [self.fiscal_week]
        else:
            # 以ideal demand的财年周列表为准
            fiscal_weeks = TblDemandState.query_distinct_fiscal_weeks_by_demand(IDEAL_DEMAND)
            previous_five_weeks = fiscal_weeks[:5]
        res = {}
        for week in previous_five_weeks:
            try:
                state_proxy = StateProxy(week, self.demand)
                if not self.__can_do(week, state_proxy):
                    continue
                logger.info(f"start calculate: fiscal_week:{week} demand:{self.demand}")
                result = self.calculate_each_week(week)
                if result.is_region:
                    self._save(result.get_df(), None)
                else:
                    self._save(result.group_by_region(), result.get_df())

                # 计算成功后，扭转状态机状态
                state_proxy.do_calculate()
                logger.info(f"end calculate: fiscal_week:{week} demand:{self.demand}")
            except ErrorExcept as e:
                developer_subject = f"{os.environ.get('ENV')}  {self.demand}  - calculate {self.demand} fail."
                async_rate_limited_send_email(EMAIL_BUCKET_KEY_PREFIX + f'{self.demand}-calculate-' + week, 2, 1800,
                                              developer_subject,
                                              f"计算week:{week}  {self.demand}失败,e:{traceback.format_exc()} errorExcept: {e}")
                raise e
            except Exception as e:
                developer_subject = f"{os.environ.get('ENV')}  {self.demand}  - calculate {self.demand} fail."
                async_rate_limited_send_email(EMAIL_BUCKET_KEY_PREFIX + f'{self.demand}-calculate-' + week, 2, 1800,
                                              developer_subject,
                                              f"计算week:{week}  {self.demand}失败,e:{traceback.format_exc()} errorExcept: {e}")
                raise e
        return res

    #  留给子类重写写库操作，失败要抛异常，会触发后面重试
    def _save(self, demand_by_country: pd.DataFrame, demand_by_soldto: pd.DataFrame):
        pass

    def calculate_each_week(self, week: str) -> DemandResult:
        # 定时任务中不会初始化fiscal_week, 在这里统一赋值
        self.fiscal_week = week
        # 1、获取依赖数据
        df = self._get_calculate_df(week)

        # 2、执行计算
        df = self._calculate_cw1(df)
        # 小于0，则为0
        # 小于shipment plan, 则为shipment plan
        df = set_target_if_less(df, self.columns[0], 0)
        # 和shipment plan的比较：DI、DT、DS、DF需要，DN不需要 DD不需要
        if self.demand != NORMALIZED_DEMAND and self.demand != DELTA_DEMAND and self.demand != FINAL_DEMAND:
            df = set_target_if_less_by_column(df, self.columns[0], CW1_SHIPMENT_PLAN)
        result = self._calculate_cw2(df)

        # 3、后置逻辑 在这里做shipment的兜底比较
        result.modify()
        return result

    def __modify_result(self, df: pd.DataFrame) -> pd.DataFrame:

        df = set_target_if_less(df, self.columns[1], 0)

        # 和shipment plan的比较：DI、DT、DS、DF需要，DN、DD不需要
        if self.demand != NORMALIZED_DEMAND:
            df = set_target_if_less_by_column(df, self.columns[0], CW1_SHIPMENT_PLAN)
            df = set_target_if_less_by_column(df, self.columns[1], CW2_SHIPMENT_PLAN)

            # 添加兜底逻辑，将DI_CW1和DI_CW2为None的数据设置CW1_SHIPMENT_PLAN和CW2_SHIPMENT_PLAN
            df = set_target_by_none_column(df, self.columns[0], CW1_SHIPMENT_PLAN)
            df = set_target_by_none_column(df, self.columns[1], CW2_SHIPMENT_PLAN)

        # 3、把所有列中出现的nan，转为none
        df = fill_nan_to_none(df)

        return df

    # 留给子类重写，失败要抛异常，会触发后面重试
    def _calculate_cw1(self, df: pd.DataFrame) -> pd.DataFrame:
        return pd.DataFrame()

    # 留给子类重写，失败要抛异常，会触发后面重试
    def _calculate_cw2(self, df: pd.DataFrame) -> DemandResult:
        return None

    def __processors_depended(self) -> list[str]:
        # 注意来这里登记demand对应的依赖processor
        m = {
            IDEAL_DEMAND: [BASE_PROCESSOR],
            TOPDOWN_DEMAND: [DFA_PROCESSOR],
            # NORMALIZED_DEMAND: [BASE_PROCESSOR, DFA_PROCESSOR],
            SELL_IN_DEMAND: [WOI_PROCESSOR]
        }
        return m.get(self.demand, [])

    def __calculator_depended(self) -> list[str]:
        # 注意来这里登记demand对应的依赖processor
        m = {
            NORMALIZED_DEMAND: [TOPDOWN_DEMAND],
            SELL_IN_DEMAND: [IDEAL_DEMAND, TOPDOWN_DEMAND],
            DELTA_DEMAND: [SELL_IN_DEMAND, NORMALIZED_DEMAND],
            FINAL_DEMAND: [DELTA_DEMAND]
        }
        return m.get(self.demand, [])

    def __can_do(self, week, state_proxy) -> bool:
        # 检查是否是可执行状态
        # 如果是sell in demand，则直接返回True
        # 前置的数据必须先准备好，之后才判断状态
        if not is_all_process(week, self.__processors_depended()):
            return False
        # 手动设置Calculator是否可重新计算
        if self.can_recalculate and state_proxy.current_state().is_completed():
            return True
        # 新增加DT，可以在waiting for setup状态时，进行计算
        if self.demand == TOPDOWN_DEMAND and state_proxy.is_waiting_to_setup():
            return True
        if not state_proxy.is_waiting_for_calculation():
            return False
        if not is_all_calculator_process(week, self.__calculator_depended()):
            return False
        # 判断时间是否是配置的开始时间之后
        if CustomWeekDate(ModuleSwitchEnum.BEFORE_CALCULATION.value).is_before_ddl():
            return False
        return True

    def _get_calculate_df(self, week: str) -> pd.DataFrame:
        pass


def is_all_calculator_process(week, demand_list: list[str]) -> bool:
    if not demand_list:
        return True
    for demand in demand_list:
        is_completed = StateProxy(week, demand).is_completed()
        if not is_completed:
            return False
    return True
