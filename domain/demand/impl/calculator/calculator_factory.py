from domain.demand.entity.const import IDEAL_DEMAND, TOPDOWN_DEMAND, NORMALIZED_DEMAND, DELTA_DEMAND, \
    FINAL_DEMAND, SELL_IN_DEMAND
from domain.demand.impl.calculator.delta_demand_calculator import DeltaDemandCalculator
from domain.demand.impl.calculator.final_demand.final_demand_calculator import FinalDemandCalculator
from domain.demand.impl.calculator.ideal_demand_calculator import IdealDemandCalculator
from domain.demand.impl.calculator.normalized_demand_calculator import NormalizedDemandCalculator
from domain.demand.impl.calculator.sellin_demand_calculator import SellInDemandCalculator
from domain.demand.impl.calculator.topdown_demand_calculator import TopdownDemandCalculator


def calculator_factory(demand_name: str, fiscal_week: str = None):
    if demand_name == IDEAL_DEMAND:
        return IdealDemandCalculator(demand=IDEAL_DEMAND, fiscal_week=fiscal_week)
    elif demand_name == TOPDOWN_DEMAND:
        return TopdownDemandCalculator(demand=TOPDOWN_DEMAND, fiscal_week=fiscal_week)
    elif demand_name == SELL_IN_DEMAND:
        return SellInDemandCalculator(demand=SELL_IN_DEMAND, fiscal_week=fiscal_week)
    elif demand_name == NORMALIZED_DEMAND:
        return NormalizedDemandCalculator(demand=NORMALIZED_DEMAND, fiscal_week=fiscal_week)
    elif demand_name == DELTA_DEMAND:
        return DeltaDemandCalculator(demand=DELTA_DEMAND, fiscal_week=fiscal_week)
    elif demand_name == FINAL_DEMAND:
        return FinalDemandCalculator(demand=FINAL_DEMAND, fiscal_week=fiscal_week)
    else:
        raise ValueError(f'Unsupported Calculator: {demand_name}')
