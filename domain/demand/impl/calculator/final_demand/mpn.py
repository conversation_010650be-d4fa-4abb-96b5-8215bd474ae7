from domain.demand.impl.calculator.final_demand.dealer import FinalDemandDealer
from domain.demand.impl.calculator.final_demand.final_demand_strategies import Candidate


class Mpn:
    def __init__(self, mpn):
        self.mpn = mpn
        self.dn_lt_di_candidates = []
        self.di_lte_dn_candidates = []
        self.delta_demand = 0

        self.supply = 0 # 随着计算一直变化的量

    def set_delta_demand(self,dd):
        self.delta_demand = dd
        self.supply = dd

    def allocate(self, dec:int):
        if self.supply <= 0:
            return

        self.supply -= dec
        if self.supply <= 0:
            self.supply = 0

    def add_candidate(self, dealer: FinalDemandDealer):
        # 注意下面两个分支candidate的source和target是相反的！
        if dealer.dn.amount < dealer.di.amount:
            candidate = Candidate(dealer.dealer_id(),
                                  dealer.dn.woi_div_twos(), dealer.di.woi_div_twos(),
                                  dealer.twos * dealer.avg_dfa, dealer.extra_shipment, dealer.base_demand.amount)
            self.dn_lt_di_candidates.append(candidate)
            return

        candidate = Candidate(dealer.dealer_id(),
                              dealer.di.woi_div_twos(), dealer.dn.woi_div_twos(),
                              dealer.twos * dealer.avg_dfa, dealer.extra_shipment, dealer.base_demand.amount)
        self.di_lte_dn_candidates.append(candidate)

    def candidates(self) -> list[Candidate]:
        candidates = []
        candidates.extend(self.dn_lt_di_candidates)
        candidates.extend(self.di_lte_dn_candidates)
        return candidates

    def allocate_by_threshold(self):
        candidates = self.candidates()

        for candidate in candidates:
            ori_amount = candidate.start_amount
            candidate.satisfy_lowest_threshold()
            self.allocate(candidate.start_amount - ori_amount)