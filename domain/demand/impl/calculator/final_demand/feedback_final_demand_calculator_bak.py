# """
# * Finalized Demand数据计算和更新
#     * 在by week & country & mpn粒度上进行比较和试算
#         * 当原始的Finalized WOI（per feedback forecast） 整体低于当前预设的WOI 下限，通过以下循环计算过程调整并更新
#             * 在sold-to 层级上，逐个计算sold-to & mpn的WOI并排列。
#             * 降低队列中WOI最高的sold-to对应的demand，至队列中次高WOI的水平。
#             * 重新计算country & mpn层级的WOI，如果更新后的WOI在设定的Range区间内，则停止循环。
#             * 对于保护Shipment Plan的sold-to，如果调减WOI将导致其Demand降至Shipment Plan以下，则该步骤不继续、该sold-to直接退出队列。
#         * 当原始的Finalized WOI（per feedback forecast） 整体高于当前预设的WOI 下限，通过以下循环计算过程调整并更新
#             * 在sold-to 层级上，逐个计算sold-to & mpn的WOI并排列。
#             * 抬高队列中WOI最低的sold-to对应的demand，至队列中次低WOI的水平。
#             * 重新计算country & mpn层级的WOI，如果更新后的WOI在设定的Range区间内，则停止循环。
#         * 当原始的Finalized WOI（per feedback forecast） 整体位于当前预设的WOI 区间范围内，则结果均保持不变
#
#
#     按照sub_lob 的 woi max 作为 country/sublob 级别的上限
#     如果 从 soldto mpn上 汇总的 country_sublob_woi 小于 sub_lob_max_woi -- 直接结束
#     否则
#         按照 sold to mpn --  向下调减到设置的 woi 最小值， 每轮调减后都要计算 country sublob woi是否小于汇总值
#
#
# """
#
# from data.databend.end_to_end.forecast_feedback_by_region import ForecastFeedbackRegion
# from data.databend.end_to_end.forecast_feedback_by_soldto import ForecastFeedbackSoldto
# from data.mysqls.demand.sellin_demand_woi_setting import SellinDemandWoiSetting
# from data.mysqls.end_to_end.feedback_demand_by_region import DemandFeedbackByRegion
# from data.mysqls.end_to_end.feedback_demand_by_soldto import DemandFeedbackBySoldTo
# from domain.demand.entity.demand import DemandCw1, DemandCw2
# from util.conf import logger
# from util.const import WOITypes
# import concurrent.futures
#
# # 1 降低soldto的woi 会导致country一起降低，还是低于下限？
# # 2 如果woi相同，则同步下降到下一个woi？直到结束或所有woi都相同  按照delta=0.1
# # a=10 b=11 c=12 --》 a=11 b=11 c=12 --》 a=12 b=12 c=12
# # 3 确认 打开df shipmentplan 保护
#
# """
# * Finalized Demand WOI (per Feedback Forecast）
# * CW+1 WOI = [CW+1 Finalized Demand + CW-1 EOH + CW Shipment Plan - Sum (CW ~ CW+1 FCST)]  ÷ Avg. (CW+2 ~ CW+4 FCST)
# * CW+2 WOI= [CW+2 Finalized Demand + CW-1 EOH + CW Shipment Plan + CW+1 Finalized Demand - Sum (CW ~ CW+2 FCST)] ÷ Avg. (CW+3 ~ CW+5 FCST)
# """
#
# WOI_DECR = 0.1
# REDUCE_DEMAND = 1
#
#
# class FeedbackDemandDealer:
#     def __init__(self, soldto_id, mpn, sub_lob,
#                  ori_demand_cw1, ori_demand_cw2,
#                  eoh,
#                  cw_shipment_plan, cw1_shipment_plan, cw2_shipment_plan,
#                  fcst: list[int]):
#         self.soldto_id = soldto_id
#         self.mpn = mpn
#         self.sub_lob = sub_lob
#         self.ori_demand_cw1 = ori_demand_cw1
#         self.ori_demand_cw2 = ori_demand_cw2
#         self.eoh = eoh
#         self.cw_shipment_plan = cw_shipment_plan
#         self.cw1_shipment_plan = cw1_shipment_plan
#         self.cw2_shipment_plan = cw2_shipment_plan
#         self.fcst = [x if x is not None else 0 for x in fcst]
#         self.avg_fcst_2_4 = sum(self.fcst[2:5]) / 3.0
#         self.avg_fcst_3_5 = sum(self.fcst[3:6]) / 3.0
#         self.cw1_stop = False
#         self.cw2_stop = False
#         self.cw1_flag = True
#         # 初始数据计算
#         self.base = None
#         if self.eoh is not None and self.cw_shipment_plan is not None:
#             self.base = sum(self.fcst[0:2]) - self.eoh - self.cw_shipment_plan
#         self.demand_cw1: DemandCw1 = DemandCw1(amount=self.ori_demand_cw1,
#                                                base=self.base,
#                                                avg_fcst=self.avg_fcst_2_4)
#
#         self.demand_cw2: DemandCw2 = DemandCw2(amount=self.ori_demand_cw2,
#                                                amount_cw1=self.ori_demand_cw1,
#                                                base=self.base,
#                                                avg_fcst=self.avg_fcst_3_5,
#                                                cw2_fcst=self.fcst[2])
#
#     def __str__(self):
#         return (f"("
#                 f"  soldto_id={self.soldto_id},"
#                 f"  mpn={self.mpn},"
#                 f"  ori_demand_cw1={self.ori_demand_cw1},"
#                 f"  ori_demand_cw2={self.ori_demand_cw2},"
#                 f"  eoh={self.eoh},"
#                 f"  cw_shipment_plan={self.cw_shipment_plan},"
#                 f"  base={self.base},"
#                 f"  avg_fcst_2_4={self.avg_fcst_2_4},"
#                 f"  avg_fcst_3_5={self.avg_fcst_3_5},"
#                 f"  demand_cw1={self.demand_cw1.amount},"
#                 f"  demand_cw2={self.demand_cw2.amount},"
#                 f")")
#
#     def check_param(self) -> bool:
#         if self.eoh is None or self.cw_shipment_plan is None:
#             self.stop()
#             return False
#         if self.is_cw1() and (self.ori_demand_cw1 is None or self.avg_fcst_2_4 is None or self.avg_fcst_2_4 == 0):
#             self.stop()
#             return False
#         if not self.is_cw1() and (self.ori_demand_cw2 is None or self.avg_fcst_3_5 is None or self.avg_fcst_3_5 == 0):
#             self.stop()
#             return False
#         return True
#
#     def start_cw2_flag(self):
#         self.cw1_flag = False
#
#     def is_cw1(self):
#         return self.cw1_flag
#
#     def stop(self):
#         if self.is_cw1():
#             self.cw1_stop = True
#             return
#         self.cw2_stop = True
#
#     def is_stop(self):
#         if self.is_cw1():
#             return self.cw1_stop
#         return self.cw2_stop
#
#     def get_amount(self):
#         if self.is_cw1():
#             return self.demand_cw1.amount
#         return self.demand_cw2.amount
#
#     def __set_demand(self, demand: int):
#         if demand < 0:
#             self.demand = 0
#             self.stop()
#             return
#         if self.is_cw1():
#             self.__set_cw1_demand(demand)
#             return
#         self.__set_cw2_demand(demand)
#
#     def __set_cw1_demand(self, demand: int):
#         if demand <= self.cw1_shipment_plan:
#             self.stop()
#             return
#         self.demand_cw1.amount = demand
#         self.demand_cw2.amount_cw1 = demand
#
#     def __set_cw2_demand(self, demand: int):
#         # 判断sp大小
#         if demand <= self.cw2_shipment_plan:
#             self.stop()
#             return
#         self.demand_cw2.amount = demand
#
#     def woi(self):
#         if self.is_cw1():
#             return self.demand_cw1.woi()
#         return self.demand_cw2.woi()
#
#     def woi_by_demand(self, demand):
#         if self.is_cw1():
#             demand_cw1 = DemandCw1(demand, self.base, self.avg_fcst_2_4)
#             return demand_cw1.woi()
#         demand_cw2 = DemandCw2(demand, self.demand_cw1.amount, self.base, self.avg_fcst_3_5, self.fcst[2])
#         return demand_cw2.woi()
#
#     def adjust_woi(self, target_woi):
#         if self.is_cw1():
#             demand_cw1 = self.demand_cw1.get_amount_by_woi(target_woi)
#             self.__set_demand(demand_cw1)
#             return
#         demand_cw2 = self.demand_cw2.get_amount_by_woi(target_woi)
#         self.__set_demand(demand_cw2)
#
#
# class FeedbackDemandCalculator:
#     def __init__(self, fiscal_week=None, mpn: str = None) -> None:
#         self.soldto_df: list[dict] = None
#         self.region_dataframe: list[dict] = None
#         self.fiscal_week = fiscal_week
#         woi_by_mpns, woi_by_sublobs = SellinDemandWoiSetting().query_woi_by_fiscal_week(self.fiscal_week,
#                                                                                                 WOITypes.FeedbackDemand.value)
#         # woi_by_mpns, woi_by_sublobs = SellinDemandWoiSetting().query_publish_woi_by_fiscal_week(self.fiscal_week,'houkun')
#         self.mpn_woi_dict = {item.mpn: item for item in woi_by_mpns}
#
#         self.soldto_dealers = self.build_soldto_dealers()
#         self.region_dealer_dict = self.build_region_dealers()
#         self.soldto_dealer_dict = {(soldto_dealer.soldto_id, soldto_dealer.mpn): soldto_dealer for soldto_dealer in
#                                    self.soldto_dealers}
#         if mpn:  # 只处理mpn
#             self.soldto_dealers = [dealer for dealer in self.soldto_dealers if dealer.mpn == mpn]
#
#     def build_soldto_dealers(self):
#         dealers = []
#         self.soldto_df: list[dict] = DemandFeedbackBySoldTo().find_by_fiscal_week_name(
#             fiscal_week_name=self.fiscal_week)
#         # 判断是否计算过
#         is_calculated = True if self.soldto_df is not None and len(self.soldto_df) > 0 else False
#         if not is_calculated:
#             self.soldto_df = ForecastFeedbackSoldto().find_by_fiscal_week_name(fiscal_week_name=self.fiscal_week)
#
#         for item in self.soldto_df:
#             mpn, feedback_demand_dealer = self.__get_feedback_demand_dealer(item, is_calculated)
#             dealers.append(feedback_demand_dealer)
#
#         return dealers
#
#     def build_region_dealers(self):
#         region_dealers = {}
#         self.region_dataframe = ForecastFeedbackRegion().find_by_fiscal_week_name(fiscal_week_name=self.fiscal_week)
#         for item in self.region_dataframe:
#             mpn, feedback_demand_dealer = self.__get_region_feedback_demand_dealer(item)  #region会汇总处理
#             region_dealers[mpn] = feedback_demand_dealer
#         return region_dealers
#
#     # trial_demand_cw1 初始会使用 final_demand填充
#     def __get_feedback_demand_dealer(self, item, is_calculated):
#         mpn = item['mpn_id']
#         sold_to_id = item['sold_to_id']
#
#         fcst = [item['forecast_feedback_cw'], item['forecast_feedback_cw1'],
#                 item['forecast_feedback_cw2'], item['forecast_feedback_cw3'],
#                 item['forecast_feedback_cw4'], item['forecast_feedback_cw5'],
#                 item['forecast_feedback_cw6'], item['forecast_feedback_cw7']]
#         feedback_demand_dealer = FeedbackDemandDealer(soldto_id=sold_to_id, mpn=mpn, sub_lob=item['sub_lob'],
#                                                       ori_demand_cw1=item['finalized_demand_v2_cw1'] if is_calculated else
#                                                       item['trial_demand_cw1'],
#                                                       ori_demand_cw2=item['finalized_demand_v2_cw2'] if is_calculated else
#                                                       item['trial_demand_cw2'],
#                                                       eoh=item['ub_eoh_lw'],
#                                                       cw_shipment_plan=item['shipment_plan_cw'],
#                                                       cw1_shipment_plan=item['shipment_plan_cw1'],
#                                                       cw2_shipment_plan=item['shipment_plan_cw2'],
#                                                       fcst=fcst)
#         return mpn, feedback_demand_dealer
#
#     def __get_region_feedback_demand_dealer(self, item):
#         mpn = item['mpn']
#         fcst = [item['forecast_feedback_cw'], item['forecast_feedback_cw1'],
#                 item['forecast_feedback_cw2'], item['forecast_feedback_cw3'],
#                 item['forecast_feedback_cw4'], item['forecast_feedback_cw5'],
#                 item['forecast_feedback_cw6'], item['forecast_feedback_cw7']]
#         # demand数据是从sold to上汇总来的，此处无意义
#         feedback_demand_dealer = FeedbackDemandDealer(soldto_id='', mpn=mpn, sub_lob=item['sub_lob'],
#                                                       ori_demand_cw1=item['trial_demand_cw1'],
#                                                       ori_demand_cw2=item['trial_demand_cw2'],
#                                                       eoh=item['ub_eoh'],
#                                                       cw_shipment_plan=item['shipment_plan_cw'],
#                                                       cw1_shipment_plan=item['shipment_plan_cw1'],
#                                                       cw2_shipment_plan=item['shipment_plan_cw2'],
#                                                       fcst=fcst)
#         return mpn, feedback_demand_dealer
#
#     def calculate(self):
#         self.__calculate_cw1()
#         self.__calculate_cw2()
#         self.__save()
#
#     def __calculate_cw1(self):
#         self.do_calculate(self.soldto_dealers, self.region_dealer_dict, True)
#
#     def __calculate_cw2(self):
#         self.do_calculate(self.soldto_dealers, self.region_dealer_dict, False)
#
#     def __save(self):
#         # save soldto
#         for item in self.soldto_df:
#             key = (item['sold_to_id'], item['mpn_id'])  # 构造字典的键
#             soldto_dealer = self.soldto_dealer_dict[key]
#             item['finalized_demand_v2_cw1'] = soldto_dealer.demand_cw1.amount
#             item['finalized_demand_v2_cw2'] = soldto_dealer.demand_cw2.amount
#         DemandFeedbackBySoldTo().batch_insert(self.soldto_df)
#         # save region
#         # 循环处理region_dataframe_list， 从 soldto_to_list中按照mpn取出 所有数据的feedback_demand_cw1的和 保存到对象中
#         for region_item in self.region_dataframe:
#             mpn = region_item['mpn']
#             # 计算 feedback_demand_cw1 和 feedback_demand_cw2 的总和
#             feedback_demand_cw1_sum = sum(
#                 item['finalized_demand_v2_cw1'] if item['finalized_demand_v2_cw1'] else 0 for item in self.soldto_df if
#                 item['mpn_id'] == mpn
#             )
#             feedback_demand_cw2_sum = sum(
#                 item['finalized_demand_v2_cw2'] if item['finalized_demand_v2_cw2'] else 0 for item in self.soldto_df if
#                 item['mpn_id'] == mpn
#             )
#             # 将总和保存到 region_item 中
#             region_item['finalized_demand_v2_cw1'] = feedback_demand_cw1_sum
#             region_item['finalized_demand_v2_cw2'] = feedback_demand_cw2_sum
#         # 批量插入 region 数据（假设有一个类似的类 RegionFeedback）
#         DemandFeedbackByRegion().batch_insert(self.region_dataframe)
#
#     def get_country_dealer(self, dealers: list[FeedbackDemandDealer],
#                            region_dealer: FeedbackDemandDealer) -> FeedbackDemandDealer:
#         # 汇总所有sold-to的数值
#         sum_ori_demand_cw1 = sum(dealer.demand_cw1.amount if dealer.demand_cw1.amount else 0 for dealer in dealers)
#         sum_ori_demand_cw2 = sum(dealer.demand_cw2.amount if dealer.demand_cw2.amount else 0 for dealer in dealers)
#         country_demand_dealer = FeedbackDemandDealer(soldto_id='', mpn=region_dealer.mpn,
#                                                      ori_demand_cw1=sum_ori_demand_cw1,
#                                                      ori_demand_cw2=sum_ori_demand_cw2, eoh=region_dealer.eoh,
#                                                      cw_shipment_plan=region_dealer.cw_shipment_plan,
#                                                      cw1_shipment_plan=region_dealer.cw1_shipment_plan,
#                                                      cw2_shipment_plan=region_dealer.cw2_shipment_plan,
#                                                      fcst=region_dealer.fcst)
#         if not dealers[0].is_cw1():
#             country_demand_dealer.start_cw2_flag()
#         return country_demand_dealer
#
#     # def do_calculate(self, dealers: list[FeedbackDemandDealer], region_dealer_dict, is_cw1):
#     #     if not is_cw1:
#     #         for dealer in dealers:
#     #             dealer.start_cw2_flag()
#     #     # 0 按照mpn循环计算
#     #     mpns = list(set(dealer.mpn for dealer in dealers))
#     #     for mpn in mpns:
#     #         # 获取此mpn的dealer
#     #         mpn_dealers = [dealer for dealer in dealers if dealer.mpn == mpn]
#     #         if mpn not in self.mpn_woi_dict: # 没有woi range 不处理
#     #             continue
#     #
#     #         woi_range = [self.mpn_woi_dict[mpn].woi_min if is_cw1 else self.mpn_woi_dict[mpn].woi_min_cw2,
#     #                      self.mpn_woi_dict[mpn].woi_max if is_cw1 else self.mpn_woi_dict[mpn].woi_max_cw2]
#     #         # 1 计算 country+mpn 级别woi，判断是否在区间内
#     #         self.calculate_by_mpn(mpn=mpn, dealers=mpn_dealers, region_dealer=region_dealer_dict[mpn],woi_range=woi_range)
#
#     def do_calculate(self, dealers: list[FeedbackDemandDealer], region_dealer_dict, is_cw1):
#         if not is_cw1:
#             for dealer in dealers:
#                 dealer.start_cw2_flag()
#         # 获取所有唯一的MPN
#         mpns = list({dealer.mpn for dealer in dealers})
#
#         # 创建线程池
#         with concurrent.futures.ThreadPoolExecutor() as executor:
#             futures = []
#             for mpn in mpns:
#                 if mpn not in self.mpn_woi_dict:
#                     continue
#                 mpn_dealers = [dealer for dealer in dealers if dealer.mpn == mpn]
#                 woi_config = self.mpn_woi_dict[mpn]
#                 woi_range = [
#                     woi_config.woi_min if is_cw1 else woi_config.woi_min_cw2,
#                     woi_config.woi_max if is_cw1 else woi_config.woi_max_cw2
#                 ]
#                 # TODO s
#                 woi_range = self.get_woi_range(mpn, mpn_dealers[0].sub_lob)
#
#                 logger.info(f"Submitting calculation task for MPN: {mpn}")
#                 future = executor.submit(
#                     self.calculate_by_mpn,
#                     mpn=mpn,
#                     dealers=mpn_dealers,
#                     region_dealer=region_dealer_dict[mpn],
#                     woi_range=woi_range
#                 )
#                 futures.append(future)
#
#             for future in concurrent.futures.as_completed(futures):
#                 try:
#                     future.result()
#                 except Exception as e:
#                     logger.error(f"Error processing MPN: {e}", exc_info=True)
#
#     def calculate_by_mpn(self, mpn: str, dealers: list[FeedbackDemandDealer], region_dealer: FeedbackDemandDealer, woi_range):
#         logger.info(f"feedback demand start calculate mpn={mpn}")
#         for dealer in dealers:
#             dealer.check_param()
#
#         min_woi = woi_range[0]
#         max_woi = woi_range[1]
#         while True:
#             if min_woi is None or max_woi is None or min_woi == max_woi:
#                 break
#
#             # * 在sold-to 层级上，逐个计算sold-to & mpn的WOI并排列。
#             active_mpn_dealers = [dealer for dealer in dealers if not dealer.is_stop()]
#             if active_mpn_dealers is None or len(active_mpn_dealers) == 0:
#                 break
#             # 判断当前woi，低于下限需要抬升，高于上限需要下降 （有没有可能本来低于下限，调完高于上限？？）
#             # 汇总dealers为country_dealer,计算country级别的woi
#             country_dealer = self.get_country_dealer(dealers, region_dealer)
#             country_dealer.check_param()
#             if country_dealer.is_stop():
#                 break
#             country_dealer_woi = country_dealer.woi()
#             if min_woi <= country_dealer_woi <= max_woi:
#                 break
#             if country_dealer_woi >= max_woi:
#                 # 减woi 从高到低
#                 active_mpn_dealers.sort(key=lambda x: x.woi(), reverse=True)
#                 # 取最大的向下减woi
#                 cur_dealer = active_mpn_dealers[0]
#                 _woi = cur_dealer.woi_by_demand(cur_dealer.get_amount() - REDUCE_DEMAND)
#                 cur_woi = cur_dealer.woi() - WOI_DECR
#                 cur_dealer.adjust_woi(min(_woi, cur_woi))
#                 # print(cur_dealer.__str__())
#             if country_dealer_woi <= min_woi:
#                 active_mpn_dealers.sort(key=lambda x: x.woi(), reverse=False)
#                 # 取最小的加woi
#                 cur_dealer = active_mpn_dealers[0]
#                 _woi = cur_dealer.woi_by_demand(cur_dealer.get_amount() + REDUCE_DEMAND)
#                 cur_woi = cur_dealer.woi() + WOI_DECR
#                 cur_dealer.adjust_woi(max(_woi, cur_woi))
#                 # print(cur_dealer.__str__())
#
#         logger.info(f"feedback demand end calculate mpn={mpn}")
#
#     def get_woi_range(self, mpn, sub_lob):
#
#
#         pass
#
#
