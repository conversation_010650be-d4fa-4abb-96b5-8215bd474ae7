import math

import numpy as np
import pandas as pd

from util.conf import logger


class CalculationStrategy:

    def __init__(self) -> None:
        pass


class Candidate:
    def __init__(self, id: str, start: float, target: float, weight: float, lowest_threshold: int, start_amount):
        self.id = id
        self.start = start  # woi/twos
        self.target = target  # woi/twos
        self.weight = weight  # twos*avg_dfa
        self.start_amount = start_amount
        self.allocated = 0  # 被分配的量，不包括start值
        self.lowest_threshold = lowest_threshold  # 最小分配值
        self.flag = False

    def get_gap(self):
        return self.target - self.start

    def stop(self):
        self.flag = True

    def is_stop(self) -> bool:
        if self.get_gap() <= 0:
            self.stop()
        return self.flag

    def allocate(self, std, coe):
        if not self.is_stop():
            current_allocation = self.adjust_gap(std, coe)
            self.allocated = current_allocation * self.weight

    def get_final_amount(self):
        return self.start_amount + self.allocated

    def satisfy_lowest_threshold(self):
        self.start = self.start + self.lowest_threshold/self.weight
        self.start_amount += self.lowest_threshold
        # self.allocated = self.allocated + self.lowest_threshold

    def adjust_gap(self, std: float, coe: float)->float:
        current_allocation = 0
        # deviation_count = int(self.get_gap() // std + 1)  # 此sold_to以std为单位的量，向上取整
        deviation_count = math.ceil(float(self.get_gap())/std)
        proportion = 1.0
        for j in range(1, deviation_count + 1):
            proportion *= coe
            unit = min(self.get_gap() - std * (j - 1), std)  # 此std单位可分配量
            current_allocation += unit * proportion
        return current_allocation


class ElasticSpring(CalculationStrategy):
    def __init__(self, candidates: list[Candidate], total_supply: int) -> None:
        super().__init__()
        # self.candidates = [c for c in candidates if c.gap > 0]  # gap大于0才参与分配
        self.candidates = candidates
        self.total_supply = total_supply
        self.PROPORTION_DEC_CONST = 0.99
        self.min_std_threshold = 10000
        self.std = self.__get_std()
        self.coe = 1.0  # 弹性系数

    def __get_std(self):
        # Step 1: Calculate the standard deviation of di.(woi/twos) - dn.(woi/twos)
        gaps = [c.get_gap() for c in self.candidates]
        # 如果gap数据很均衡会导致算出来的std非常小，后续计算 get_gap()/std_dev 很大，导致一直循环
        # 所以此处判断下，如果std很小的时候，get_gap()/std_dev > 10000 时，std_dev = get_gap()/10000
        std_dev = np.std(gaps)
        if gaps:
            min_gap = min(gaps)
            if min_gap / std_dev > self.min_std_threshold:
                std_dev = min_gap/self.min_std_threshold
        return std_dev

    def run(self):
        if len(self.candidates) == 0:
            return self.total_supply
        if len(self.candidates) == 1:
            single_allocation = min(self.candidates[0].get_gap() * self.candidates[0].weight, self.total_supply)
            self.candidates[0].allocated = single_allocation
            return self.total_supply - single_allocation

        proportion = 1.0  #
        remaining_supply = 0
        while True:
            if self.total_supply is None or self.total_supply == 0:
                break
            for candidate in self.candidates:
                candidate.allocate(self.std, proportion)
            total_allocated = 0
            for candidate in self.candidates:
                if candidate.allocated is None:
                    candidate.allocated = 0
                total_allocated += candidate.allocated
            """
            remaining_supply>0 分完了
            向下压的过程proportion<=1，
             如果proportion<1 , 初始allocated会大于supply，remaining_supply<0
                此时是都想要货，所以需要按算法向下减proportion < 1.0，但是有一定误差，所以会压过一个极小的值，导致remaining_supply>0,
                这些剩余的supply应该忽略掉，不参与下一轮分配
             如果proportion = 1 , 初始allocated小于supply，remaining_supply>0
                  大家都不想要，所以剩余很多的supply，需要参与下一轮分配
            """
            remaining_supply = self.total_supply - total_allocated
            if remaining_supply > 0:
                if proportion < 1.0:
                    remaining_supply = 0
                break
            proportion *= self.PROPORTION_DEC_CONST

        return remaining_supply


class Algorithm1(CalculationStrategy):

    def __init__(self) -> None:
        super().__init__()

    def calculate(self, mpn_dealers: dict, dds: dict, df: pd.DataFrame):
        # Implement the second algorithm here
        for mpn_index in dds:
            if mpn_index not in mpn_dealers.keys():
                continue
            if dds[mpn_index] is None or np.isnan(dds[mpn_index]) or dds[mpn_index] == 0:
                continue
            dd_this_round = dds[mpn_index]

            gap = {}
            for dealer in mpn_dealers[mpn_index]:
                diff = (dealer.di.woi_div_twos() - dealer.dn.woi_div_twos()) / dealer.twos
                gap[dealer.sold_to_id] = diff

            num_rounds = 100
            allocated = 0
            increments = dds[mpn_index] / num_rounds  # Ensure increments are integers
            min_increment_threshold = 0.01  # Minimum increment threshold to continue allocation (as an integer)

            while allocated < dds[mpn_index]:
                proportions_woi = {}
                for dealer in mpn_dealers[mpn_index]:
                    proportions_woi[dealer.sold_to_id] = max(
                        ((dealer.di.woi() - dealer.df.woi()) / (dealer.twos * gap[dealer.sold_to_id])), 0)

                proportions_sum = sum(list(proportions_woi.values()))
                if proportions_sum == 0 or increments < min_increment_threshold:
                    break

                mix = {}
                allocated_increments = {}
                for dealer in mpn_dealers[mpn_index]:
                    mix[dealer.sold_to_id] = proportions_woi[dealer.sold_to_id] / proportions_sum
                    allocated_increments[dealer.sold_to_id] = mix[dealer.sold_to_id] * increments

                total_allocated_this_round = 0
                for dealer in mpn_dealers[mpn_index]:
                    max_allocatable = max(dealer.di.amount - dealer.df.amount, 0)
                    actual_increment = min(allocated_increments[dealer.sold_to_id], max_allocatable)
                    dealer.df.amount += actual_increment
                    total_allocated_this_round += actual_increment

                allocated += total_allocated_this_round
                if total_allocated_this_round < min_increment_threshold:
                    break

                # Adjust increments for remaining rounds
                remaining_dd = dds[mpn_index] - allocated
                if remaining_dd < increments:
                    increments = remaining_dd

        for mpn, dealers in mpn_dealers.items():
            for dealer in dealers:
                df.loc[(df['mpn'] == mpn) & (df['sold_to_id'] == dealer.sold_to_id), 'df_cw1'] = dealer.df.amount

        return df


class Algorithm2(CalculationStrategy):

    def __init__(self) -> None:
        super().__init__()

    def calculate(self, mpn_dealers: dict, dds: dict, df: pd.DataFrame):
        # Implement the third algorithm here
        for mpn_index in dds:
            if mpn_index not in mpn_dealers.keys():
                continue
            if dds[mpn_index] is None or np.isnan(dds[mpn_index]) or dds[mpn_index] == 0:
                continue
            dd_this_round = dds[mpn_index]

            gap = {}
            for dealer in mpn_dealers[mpn_index]:
                diff = (dealer.di.woi_div_twos() - dealer.dn.woi_div_twos()) / dealer.twos
                gap[dealer.sold_to_id] = diff

            num_rounds = 100
            allocated = 0
            increments = dds[mpn_index] / num_rounds  # Ensure increments are integers
            min_increment_threshold = 0.01  # Minimum increment threshold to continue allocation (as an integer)

            while allocated < dds[mpn_index]:
                proportions_woi = {}
                for dealer in mpn_dealers[mpn_index]:
                    proportions_woi[dealer.sold_to_id] = max(
                        ((dealer.di.woi() - dealer.df.woi()) / (dealer.twos)), 0)

                proportions_sum = sum(list(proportions_woi.values()))
                if proportions_sum == 0 or increments < min_increment_threshold:
                    break

                mix = {}
                allocated_increments = {}
                for dealer in mpn_dealers[mpn_index]:
                    mix[dealer.sold_to_id] = proportions_woi[dealer.sold_to_id] / proportions_sum
                    allocated_increments[dealer.sold_to_id] = mix[dealer.sold_to_id] * increments

                total_allocated_this_round = 0
                for dealer in mpn_dealers[mpn_index]:
                    max_allocatable = max(dealer.di.amount - dealer.df.amount, 0)
                    actual_increment = min(allocated_increments[dealer.sold_to_id], max_allocatable)
                    dealer.df.amount += actual_increment
                    total_allocated_this_round += actual_increment

                allocated += total_allocated_this_round
                if total_allocated_this_round < min_increment_threshold:
                    break

                # Adjust increments for remaining rounds
                remaining_dd = dds[mpn_index] - allocated
                if remaining_dd < increments:
                    increments = remaining_dd

        for mpn, dealers in mpn_dealers.items():
            for dealer in dealers:
                df.loc[(df['mpn'] == mpn) & (df['sold_to_id'] == dealer.sold_to_id), 'df_cw1'] = dealer.df.amount

        return df
