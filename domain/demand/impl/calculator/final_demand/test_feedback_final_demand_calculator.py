from domain.demand.impl.calculator.final_demand.feedback_final_demand_calculator import Feedback<PERSON><PERSON>d<PERSON>eal<PERSON>, \
    FeedbackDemandCalculator


def test_feedback_final_demand_calculator():
    a_1 = FeedbackDemandDealer(soldto_id=1, mpn='A', ori_demand_cw1=3, ori_demand_cw2=20, eoh=2, cw_shipment_plan=5,
                               cw1_shipment_plan=6, cw2_shipment_plan=7,
                               fcst=[2, 4, 6, 7, 5, 6, 7, 7, 8, 9, 10, 11, 12], woi_range=[5.3, 6])

    a_2 = FeedbackDemandDealer(soldto_id=2, mpn='A', ori_demand_cw1=8, ori_demand_cw2=20, eoh=2, cw_shipment_plan=5,
                               cw1_shipment_plan=6, cw2_shipment_plan=7,
                               fcst=[6, 2, 3, 2, 5, 6, 7, 7, 8, 9, 10, 11, 12], woi_range=[5.3, 6])

    a_3 = FeedbackDemandDealer(soldto_id=3, mpn='A', ori_demand_cw1=5, ori_demand_cw2=20, eoh=2, cw_shipment_plan=5,
                               cw1_shipment_plan=6, cw2_shipment_plan=7,
                               fcst=[3, 2, 3, 4, 5, 6, 7, 7, 8, 9, 10, 11, 12], woi_range=[5.3, 6])

    dealers = [a_1, a_2, a_3]
    # calculate(dealers=dealers, is_cw1=True)
    print('----------------------')
    for dealer in dealers:
        print(dealer.__str__())
    assert True


def test_feedback_demand_calculator():
    FeedbackDemandCalculator(fiscal_week='FY25Q2W9',sub_lob='iPhone 16').calculate()
    assert True
