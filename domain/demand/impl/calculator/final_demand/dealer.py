import numpy as np

class Demand:
    def __init__(self, amount, const_var, avg_dfa, twos, flag):
        self.const_var = const_var
        self.amount = amount
        self.avg_dfa = avg_dfa
        self.twos = twos
        self.flag = flag

        if self.const_var is None or self.amount is None or self.avg_dfa is None or self.twos is None:
            self.flag = True
            return

        if np.isnan(self.const_var) or np.isnan(self.amount) or np.isnan(self.avg_dfa) or np.isnan(self.twos):
            self.flag = True
            return

        if self.avg_dfa == 0:
            self.flag = True
            return

        if self.twos == 0:
            self.flag = True
            return

    def woi(self) -> float:
        # 0410改动，由于dfa用0兜底，导致base demand没算，所以amount都为None
        amount = 0 if self.amount is None else self.amount
        return (amount - self.const_var) / self.avg_dfa

    def cal_amount(self, woi) -> float:
        return woi * self.avg_dfa + self.const_var

    def woi_div_twos(self)->float:
        return self.woi()/ self.twos

    def calculate_new_demand(self, woi_div_twos)->int:
        new_demand = woi_div_twos * self.twos * self.avg_dfa + self.const_var
        if new_demand < 0:
            self.flag = True
            new_demand = 0
        self.amount = int(new_demand)
        return new_demand

class FinalDemandDealer:
    def __init__(self, di, dn, base_demand, const_var, avg_dfa, twos, sold_to_id: str,
                 mpn: str, shipment_plan: int):

        self.flag = False
        self.sold_to_id = sold_to_id
        self.mpn = mpn

        self.avg_dfa = avg_dfa

        self.di = Demand(di, const_var, self.avg_dfa, twos, self.flag)
        self.dn = Demand(dn, const_var, self.avg_dfa, twos, self.flag)
        self.base_demand = Demand(base_demand, const_var, self.avg_dfa, twos, self.flag)
        # 初始df=base
        self.df = Demand(base_demand, const_var, self.avg_dfa, twos, self.flag)

        self.twos = twos
        self.shipment_plan = shipment_plan

        # 0410改动，由于dfa用0兜底，导致base demand没算，所以amount都为None
        amount = 0 if self.base_demand.amount is None else self.base_demand.amount
        self.extra_shipment = 0 if self.shipment_plan < amount else self.shipment_plan -amount

        if self.di.flag or self.dn.flag or self.df.flag:
            self.flag = True
        else:
            self.flag = False

        self.WEIGHT_CONST = 0.01

    def dealer_id(self):
        return self.mpn + '-' + self.sold_to_id

    # 获取当前累计分配的df
    def allocated_amount(self):
        return self.df.amount - self.dn.amount

    def weight(self):
        return self.WEIGHT_CONST * self.avg_dfa * self.twos

    def is_stop(self):
        return self.flag

    def stop(self):
        self.flag = True