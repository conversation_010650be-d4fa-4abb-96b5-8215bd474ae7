from domain.demand.entity.const import BASE_DEMAND_CW1, TWOS, BASE_DEMAND_CW2, TWOS_CW2, DF_CW1, DF_CW2, DS_CW1, DS_CW2
from domain.demand.impl.calculator.final_demand.dealer import FinalDemandDealer
from domain.demand.impl.calculator.final_demand.final_demand_calculator import Mpn


def set_zero_if_null(var):
    return var if var else 0


def avg_normalized_fcst(fcsts:list):
    total = 0
    if len(fcsts) == 0:
        return 0
    for idx,fcst in enumerate(fcsts):
        total += set_zero_if_null(fcst)
    return total/float(len(fcsts))


class CW1Proxy:
    def __init__(self, sold_to_dataframe, region_dataframe):
        self.df = sold_to_dataframe
        self.region_dataframe = region_dataframe
        self.dealer_list = self.convert_dealers()

    def convert_dealers(self):
        dealer_list = []
        for index, row in self.df.iterrows():
            row = row.to_frame().T.reset_index(drop=True)
            dealer = FinalDemandDealer(
                di=row['cw1_ideal_demand'][0],
                dn=row['final_dn_cw1'][0],
                base_demand=row[BASE_DEMAND_CW1][0],
                avg_dfa=avg_normalized_fcst([row['normalized_fcst_cw2'][0], row['normalized_fcst_cw3'][0],row['normalized_fcst_cw4'][0]]),
                const_var=row['base'][0],
                twos=row[TWOS][0],
                sold_to_id=row['sold_to_id'][0],
                mpn=row['mpn'][0],
                shipment_plan=row['shipment_plan_cw1'][0],
            )
            dealer_list.append(dealer)
        return dealer_list

    def get_dealers_by_mpn(self, mpn_id: str) -> list[FinalDemandDealer]:
        dealer_list = []
        for dealer in self.dealer_list:
            if dealer.mpn != mpn_id:
                continue
            dealer_list.append(dealer)
        return dealer_list

    def result_key(self):
        return DF_CW1

    def convert_mpns(self) -> list[Mpn]:
        # 每个mpn下的dd, Calculate dd in region_df
        self.region_dataframe['dd'] = self.region_dataframe[DS_CW1] - self.region_dataframe[BASE_DEMAND_CW1]
        dds = dict(zip(self.region_dataframe['mpn'], self.region_dataframe['dd']))
        mpns = []
        for mpn, dd in dds.items():
            m = Mpn(mpn)
            m.set_delta_demand(dd)
            mpns.append(m)
        return mpns


class CW2Proxy:
    def __init__(self, sold_to_dataframe, region_dataframe):
        self.df = sold_to_dataframe
        self.region_dataframe = region_dataframe
        self.dealer_list = self.convert_dealers()

    def convert_dealers(self):
        dealer_list = []
        for index, row in self.df.iterrows():
            row = row.to_frame().T.reset_index(drop=True)
            dealer = FinalDemandDealer(
                di=row['cw2_ideal_demand'][0],
                dn=row['final_dn_cw2'][0],
                base_demand=row[BASE_DEMAND_CW2][0],
                avg_dfa=avg_normalized_fcst([row['normalized_fcst_cw3'][0], row['normalized_fcst_cw4'][0],row['normalized_fcst_cw5'][0]]),
                const_var=row['base'][0],
                twos=row[TWOS_CW2][0],
                sold_to_id=row['sold_to_id'][0],
                mpn=row['mpn'][0],
                shipment_plan=row['shipment_plan_cw2'][0],
            )
            dealer_list.append(dealer)
        return dealer_list

    def get_dealers_by_mpn(self, mpn_id: str) -> list[FinalDemandDealer]:
        dealer_list = []
        for dealer in self.dealer_list:
            if dealer.mpn != mpn_id:
                continue
            dealer_list.append(dealer)
        return dealer_list

    def result_key(self):
        return DF_CW2

    def convert_mpns(self) -> list[Mpn]:
        # 每个mpn下的dd, Calculate dd in region_df
        self.region_dataframe['dd'] = self.region_dataframe[DS_CW2] - self.region_dataframe[BASE_DEMAND_CW2]
        dds = dict(zip(self.region_dataframe['mpn'], self.region_dataframe['dd']))
        mpns = []
        for mpn, dd in dds.items():
            m = Mpn(mpn)
            m.set_delta_demand(dd)
            mpns.append(m)
        return mpns
