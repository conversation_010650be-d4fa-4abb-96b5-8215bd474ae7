
import pandas as pd

from data.mysqls.demand.demand_by_region_pool import DemandByRegionPool
from data.mysqls.demand.demand_by_soldto_pool import DemandBy<PERSON>oldtoPool
from domain.demand.entity.const import DF_CW2, DF_CW1, CHINA_MAINLAND
from domain.demand.impl.calculator.calculator import <PERSON>culator, DemandR<PERSON>ult
from domain.demand.impl.calculator.final_demand.final_demand_strategies import ElasticSpring, Candidate
from domain.demand.impl.calculator.final_demand.mpn import Mpn
from domain.demand.impl.calculator.final_demand.proxy import CW1Proxy, CW2Proxy
from domain.demand.impl.y_value_setting import get_twos_by_dataframe
from kit.pd import replace_none_from_nan
from util.conf import logger


class FinalDemandCalculator(Calculator):

    def __init__(self, demand: str, fiscal_week=None, can_recalculate: bool = False) -> None:
        super().__init__(demand, fiscal_week, can_recalculate)
        self.woi_decr = 0.1
        self.region_df = None

    def _get_calculate_df(self, week: str) -> pd.DataFrame:
        region_df = DemandByRegionPool.get_by_fiscal_week(week)
        self.region_df = region_df

        dataframe = DemandBySoldtoPool.get_by_fiscal_week(week)
        dataframe = get_twos_by_dataframe(week, CHINA_MAINLAND, dataframe)
        return dataframe

    # cw1计算主入口
    def _calculate_cw1(self, df: pd.DataFrame) -> pd.DataFrame:
        df[DF_CW1] = None # 防止旧版本的脏数据
        proxy = CW1Proxy(df, self.region_df)
        mpns = proxy.convert_mpns()
        for mpn in mpns:
            self._do_calculate(proxy, mpn, df)
            for candidate in mpn.candidates():
                self.update_df(proxy, df, candidate)
        return df

    # cw2计算主入口
    def _calculate_cw2(self, df: pd.DataFrame) -> DemandResult:
        df[DF_CW2] = None # 防止旧版本的脏数据
        proxy = CW2Proxy(df, self.region_df)
        mpns = proxy.convert_mpns()
        for mpn in mpns:
            self._do_calculate(proxy, mpn, df)
            for candidate in mpn.candidates():
                self.update_df(proxy, df, candidate)
        return DemandResult(self.demand, df, [DF_CW1, DF_CW2])

    def update_df(self, proxy, df: pd.DataFrame, candidate: Candidate):
        mpn_sold_to_id = candidate.id.split('-')
        mpn = mpn_sold_to_id[0]
        sold_to_id = mpn_sold_to_id[1]
        # print(mpn,  sold_to_id,'---', candidate.get_start_demand() ,'---', candidate.allocated)
        df.loc[(df['mpn'] == mpn) & (df['sold_to_id'] == sold_to_id), proxy.result_key()] = candidate.get_final_amount()

    def _do_calculate(self, proxy, mpn: Mpn, df: pd.DataFrame) -> pd.DataFrame:
        logger.info(f"final demand calculate cw. mpn:{mpn.mpn}")
        # 1、准备mpn下参与分货的candidates
        dealer_list = proxy.get_dealers_by_mpn(mpn.mpn)
        for dealer in dealer_list:
            if dealer.is_stop():
                continue
            mpn.add_candidate(dealer)
        # 2、shipment plan allocation
        # mpn.allocate_by_threshold()

        # 3、dd allocation after shipment plan allocation
        if mpn.supply > 0:
            spring = ElasticSpring(candidates=mpn.dn_lt_di_candidates, total_supply=mpn.supply)
            mpn.supply = spring.run()

        # 20241113暂时改成不压货,supply可以剩余
        if False:
            if mpn.supply > 0:
                spring = ElasticSpring(candidates=mpn.di_lte_dn_candidates, total_supply=mpn.supply)
                mpn.supply = spring.run()
                # todo What if dd still remains? (mpn.supply > 0)
        return df

    def _save(self, demand_by_country: pd.DataFrame, demand_by_soldto: pd.DataFrame):
        # 保存  dn_cw1 dn_cw2 到sold to 表，by fiscal_week,sold_id,mpn
        columns = [DF_CW1, DF_CW2]
        fields_to_update = columns + ['update_time']
        by_soldto_to_dict = demand_by_soldto.to_dict(orient='records')
        replace_none_from_nan(by_soldto_to_dict, columns)
        DemandBySoldtoPool.batch_update(by_soldto_to_dict, fields_to_update)
        # 保存聚合后的 rtm_dn_cw1 rtm_dn_cw2 到 region pool  by region,mpn
        by_country_to_dict = demand_by_country.to_dict(orient='records')
        replace_none_from_nan(by_country_to_dict, columns)
        DemandByRegionPool.batch_update(by_country_to_dict, fields_to_update)
