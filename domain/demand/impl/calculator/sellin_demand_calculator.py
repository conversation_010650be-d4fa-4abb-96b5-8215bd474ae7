import math

import pandas as pd

from data.mysqls.demand.demand_by_region_pool import DemandByRegionPool
from domain.demand.entity.const import *
from domain.demand.impl.calculator.calculator import Calculator, DemandResult
from kit.pd import replace_none_from_nan


class SellInDemandCalculator(Calculator):
    def __init__(self, demand: str, fiscal_week=None, can_recalculate=True) -> None:
        super().__init__(demand, fiscal_week, can_recalculate)

    def _calculate_cw1(self, df: pd.DataFrame) -> pd.DataFrame:
        # 保留需要的字段
        df = df[['fiscal_week', 'region', 'lob', 'sub_lob', COLUMN_MPN, 'forecast_cw_ml',
                 'forecast_cw1_ml', 'forecast_cw2_dfa', 'forecast_cw3_dfa', 'forecast_cw4_dfa',
                 'forecast_cw5_dfa', AVERAGE_DFA_WEEKS_2_4, AVERAGE_DFA_WEEKS_3_5, UB_EOH,
                 CW_SHIPMENT_PLAN, CW1_SHIPMENT_PLAN, CW2_SHIPMENT_PLAN, BASE,
                 DI_CW1, DI_CW2, 'woi_by_mpn_min', 'woi_by_mpn_max', 'woi_by_sublob_max',
                 'woi_by_mpn_min_cw2', 'woi_by_mpn_max_cw2', 'woi_by_sublob_max_cw2']]
        df = calculate_cw1(df)
        return df

    def _calculate_cw2(self, df: pd.DataFrame) -> DemandResult:
        df = calculate_cw2(df)
        return DemandResult(self.demand, df, [DS_CW1, DS_CW2], is_region=True)

    def _get_calculate_df(self, week: str) -> pd.DataFrame:
        region_pool = DemandByRegionPool.get_by_fiscal_week(week)
        return region_pool

    def _save(self, demand_by_country: pd.DataFrame, demand_by_soldto: pd.DataFrame):
        # 将数据插入数据库
        region_update_list = [DS_CW1, DS_CW2, DS_CW1_WOI, DS_CW2_WOI, 'update_time']
        to_dict = demand_by_country.to_dict(orient='records')
        replace_none_from_nan(to_dict, [DS_CW1, DS_CW2, DS_CW1_WOI, DS_CW2_WOI])
        DemandByRegionPool.batch_update(to_dict, region_update_list)


class SellinDemandDealer:
    def __init__(self, sub_lob: str, mpn: str, demand, avg_fcst,
                 base, shipment_plan, woi_by_mpn_min, woi_by_mpn_max,
                 cw2_dfa_fcst=0, ds_cw1=0) -> None:
        self.sub_lob = sub_lob
        self.mpn = mpn
        self.demand = demand
        self.avg_fcst = avg_fcst # sales fcst 2-4
        self.base = base
        self.flag = False
        self.shipment_plan = shipment_plan
        self.woi_by_mpn_min = self._round_float(woi_by_mpn_min)
        self.woi_by_mpn_max = self._round_float(woi_by_mpn_max)

        # cw2专用数据
        self.cw2_dfa_fcst = self._round_float(cw2_dfa_fcst)
        self.ds_cw1 = ds_cw1

    def _round_float(self, v:float)->float:
        return round(v,4)

    # 判断数据是否为空
    def cw1_is_nan(self):
        if self.demand is None or self.avg_fcst is None or self.base is None:
            self.stop()
            return True
        if math.isnan(self.demand) or math.isnan(self.avg_fcst) or math.isnan(self.base):
            self.stop()
            return True
        if self.avg_fcst == 0:
            self.stop()
            return True
        return False

    def cw2_is_nan(self):
        if self.demand is None or self.avg_fcst is None or self.base is None or self.ds_cw1 is None:
            self.stop()
            return True
        if math.isnan(self.demand) or math.isnan(self.avg_fcst) or math.isnan(self.base) or math.isnan(self.ds_cw1):
            self.stop()
            return True
        if self.avg_fcst == 0:
            self.stop()
            return True
        return False

    def cw1_woi(self) -> float:
        if self.cw1_is_nan():
            return 0
        return self._round_float((self.demand - self.base) / self.avg_fcst)

    def cw2_woi(self) -> float:
        if self.cw2_is_nan():
            return 0
        w = (self.demand - self.base - self.cw2_dfa_fcst + self.ds_cw1) / self.avg_fcst
        return self._round_float(w)

    def __set_demand(self, demand: int):
        if demand < 0:
            self.demand = 0
            self.stop()
            return

        self.demand = demand

    # 调减woi
    def reduce_cw1_woi(self, target_woi: float):
        self.__set_demand(self.base + self.avg_fcst * target_woi)
        self.reduce_woi(target_woi)

    def reduce_cw2_woi(self, target_woi: float):
        self.__set_demand(self.base + self.cw2_dfa_fcst + self.avg_fcst * target_woi - self.ds_cw1)
        self.reduce_woi(target_woi)

    def reduce_woi(self, target_woi: float):
        # 判断target_woi是否为NaN
        if math.isnan(target_woi) or math.isnan(self.demand):
            self.stop()

        if self.demand < self.shipment_plan:
            self.__set_demand(self.shipment_plan)
            self.stop()

        if target_woi <= self.woi_by_mpn_min:
            self.stop()

    def stop(self):
        self.flag = True

    def is_stop(self):
        return self.flag


def max_cw1_demand_by_sublob(df):
    dr = DemandResult(SELL_IN_DEMAND, df, [DS_CW1, DS_CW2])
    df_by_sublob = dr.cw1_group_by_sublob()
    sublob_demands = {}
    for index, row in df_by_sublob.iterrows():
        sub_lob = row['sub_lob']
        # 判断row[BASE]，row[AVERAGE_DFA_WEEKS_2_4]，row['woi_by_sublob_max']是否为None或者nan
        if row[BASE] is None or row[AVERAGE_DFA_WEEKS_2_4] is None or row['woi_by_sublob_max'] is None:
            sublob_demands[sub_lob] = None
            continue
        if math.isnan(row[BASE]) or math.isnan(row[AVERAGE_DFA_WEEKS_2_4]) or math.isnan(row['woi_by_sublob_max']):
            sublob_demands[sub_lob] = None
            continue
        sublob_demands[sub_lob] = row[BASE] + row[AVERAGE_DFA_WEEKS_2_4] * row['woi_by_sublob_max']
    return sublob_demands


def max_cw2_demand_by_sublob(df):
    dr = DemandResult(SELL_IN_DEMAND, df, [DS_CW1, DS_CW2])
    df_by_sublob = dr.cw2_group_by_sublob()
    sublob_demands = {}
    for index, row in df_by_sublob.iterrows():
        sub_lob = row['sub_lob']
        if row[BASE] is None or row[AVERAGE_DFA_WEEKS_2_4] is None or row['woi_by_sublob_max_cw2'] is None:
            sublob_demands[sub_lob] = None
            continue
        if math.isnan(row[BASE]) or math.isnan(row[AVERAGE_DFA_WEEKS_2_4]) or math.isnan(row['woi_by_sublob_max_cw2']) or math.isnan(row['ds_cw1']):
            sublob_demands[sub_lob] = None
            continue
        sublob_demands[sub_lob] = row[BASE] + row['forecast_cw2_dfa'] + row[AVERAGE_DFA_WEEKS_3_5] * row['woi_by_sublob_max_cw2'] - row['ds_cw1']
    return sublob_demands


def calculate_cw1(df):
    dealers = {}
    all_active_mpns = []

    for index, row in df.iterrows():
        row = row.to_frame().T.reset_index(drop=True)
        mpn = row['mpn'][0]
        sub_lob = row['sub_lob'][0]
        sellin_demand_dealer = SellinDemandDealer(sub_lob, mpn, row[DI_CW1][0], row[AVERAGE_DFA_WEEKS_2_4][0],
                                                  row[BASE][0], row[CW1_SHIPMENT_PLAN][0], row['woi_by_mpn_min'][0],
                                                  row['woi_by_mpn_max'][0])
        dealers[mpn] = sellin_demand_dealer

    for dealer in dealers.values():
        # 判断基础数据是否为空，如果空，则不计算
        if dealer.cw1_is_nan():
            continue

        if dealer.cw1_woi() <= dealer.woi_by_mpn_min:
            dealer.reduce_cw1_woi(dealer.woi_by_mpn_min)
            continue

        if dealer.cw1_woi() >= dealer.woi_by_mpn_max:
            dealer.reduce_cw1_woi(dealer.woi_by_mpn_max)

        if not dealer.flag:
            all_active_mpns.append(dealer)
    # 将active_mpns按照sub_lob分组，获取所有可以调减的mpn
    sub_lob_mpns = {}
    for active_mpn in all_active_mpns:
        if active_mpn.sub_lob not in sub_lob_mpns:
            sub_lob_mpns[active_mpn.sub_lob] = []
        sub_lob_mpns[active_mpn.sub_lob].append(active_mpn)

    sublob_max_demands = max_cw1_demand_by_sublob(df)
    for sub_lob in sublob_max_demands:
        if sublob_max_demands.get(sub_lob) is None:
            continue

        def sum_demand(dealers) -> float:
            sum = 0.0
            for dealer in dealers.values():
                if sub_lob == dealer.sub_lob:
                    sum += dealer.demand
            return sum

        active_mpns = sub_lob_mpns.get(sub_lob)
        if active_mpns is None:
            continue
        while len(active_mpns) > 0 and sum_demand(dealers) > sublob_max_demands.get(sub_lob):
            # COUNTRY BY MPN 判断是否小于等于目标
            # sort() 按照woi、avgfcst排序
            active_mpns.sort(key=lambda x: (x.cw1_woi(), x.avg_fcst), reverse=True)
            # 找到最大的woi的mpn
            mpn = active_mpns[0].mpn
            dealers.get(mpn).reduce_cw1_woi(dealers.get(mpn).cw1_woi() - 0.1)
            if active_mpns[0].is_stop():
                active_mpns.remove(active_mpns[0])

    # 将结果写入df
    for mpn in dealers:
        df.loc[df['mpn'] == mpn, 'ds_cw1'] = dealers[mpn].demand
        df.loc[df['mpn'] == mpn, 'ds_cw1_woi'] = dealers[mpn].cw1_woi()
    return df


def calculate_cw2(df):
    dealers = {}
    all_active_mpns = []

    for index, row in df.iterrows():
        row = row.to_frame().T.reset_index(drop=True)
        mpn = row['mpn'][0]
        sub_lob = row['sub_lob'][0]
        sellin_demand_dealer = SellinDemandDealer(sub_lob, mpn, row[DI_CW2][0], row[AVERAGE_DFA_WEEKS_3_5][0],
                                                  row[BASE][0], row[CW2_SHIPMENT_PLAN][0], row['woi_by_mpn_min_cw2'][0],
                                                  row['woi_by_mpn_max_cw2'][0], row['forecast_cw2_dfa'][0], row['ds_cw1'][0])
        dealers[mpn] = sellin_demand_dealer

    for dealer in dealers.values():
        # 判断基础数据是否为空，如果空，则不计算
        if dealer.cw2_is_nan():
            continue

        if dealer.cw2_woi() <= dealer.woi_by_mpn_min:
            dealer.reduce_cw2_woi(dealer.woi_by_mpn_min)
            continue

        if dealer.cw2_woi() >= dealer.woi_by_mpn_max:
            dealer.reduce_cw2_woi(dealer.woi_by_mpn_max)

        if not dealer.flag:
            all_active_mpns.append(dealer)
    # 将active_mpns按照sub_lob分组
    sub_lob_mpns = {}
    for active_mpn in all_active_mpns:
        if active_mpn.sub_lob not in sub_lob_mpns:
            sub_lob_mpns[active_mpn.sub_lob] = []
        sub_lob_mpns[active_mpn.sub_lob].append(active_mpn)

    sublob_max_demands = max_cw2_demand_by_sublob(df)
    for sub_lob in sublob_max_demands:
        if sublob_max_demands.get(sub_lob) is None:
            continue

        def sum_demand(dealers) -> float:
            sum = 0.0
            for dealer in dealers.values():
                if sub_lob == dealer.sub_lob:
                    sum += dealer.demand
            return sum

        active_mpns = sub_lob_mpns.get(sub_lob)
        if active_mpns is None:
            continue
        while len(active_mpns) > 0 and sum_demand(dealers) > sublob_max_demands.get(sub_lob):
            # COUNTRY BY MPN 判断是否小于等于目标
            # 找到最大的woi的mpn
            # sort() 按照woi、avgfcst排序
            active_mpns.sort(key=lambda x: (x.cw2_woi(), x.avg_fcst), reverse=True)
            active_mpns[0].reduce_cw2_woi(active_mpns[0].cw2_woi() - 0.1)
            if active_mpns[0].is_stop():
                active_mpns.remove(active_mpns[0])

    # 将结果写入df
    for mpn in dealers:
        df.loc[df['mpn'] == mpn, 'ds_cw2'] = dealers[mpn].demand
        df.loc[df['mpn'] == mpn, 'ds_cw2_woi'] = dealers[mpn].cw2_woi()
    return df
