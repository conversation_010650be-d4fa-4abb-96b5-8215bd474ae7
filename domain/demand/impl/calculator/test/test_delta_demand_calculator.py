import os
import sys

import pandas as pd

from domain.demand.impl.calculator.delta_demand_calculator import DeltaDemandCalculator

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(SCRIPT_DIR))

from domain.demand.entity.const import DELTA_DEMAND


def test_delta_demand_calculator_cw1():
    # demand = self.base + self.avg_normalized_2_4 * woi
    # if not self.is_cw1:
    #     demand = self.base + self.normalized_fcst_cw2 + self.avg_normalized_3_5 * woi - self.dn_cw1
    soldto_df = pd.DataFrame({
        'fiscal_week': ['FY24Q3W10', 'FY24Q3W10', 'FY24Q3W10', 'FY24Q3W10'],
        'region': ['China mainland', 'China mainland', 'China mainland', 'China mainland'],
        'sold_to_id': ['s1', 's2', 's1', 's2'],
        'mpn': ['m1', 'm1', 'm2', 'm2'],
        'twos': [1, 2, 3, 4],
        'base': [1, 2, 3, 4],
        'normalized_fcst_cw2': [1, 2, 3, 4],
        'normalized_fcst_cw3': [1, 2, 3, 4],
        'normalized_fcst_cw4': [1, 2, 3, 4],
        'normalized_fcst_cw5': [1, 2, 3, 4],
        'normalized_fcst_cw6': [1, 2, 3, 4],
        'dn_cw1': [2, 6, 12, 20],
        'dn_cw2': [1, 2, 3, 4],
    })
    region_df = pd.DataFrame({
        'fiscal_week': ['FY24Q3W10', 'FY24Q3W10', 'FY24Q3W10', 'FY24Q3W10'],
        'region': ['China mainland', 'China mainland', 'China mainland', 'China mainland'],
        'mpn': ['m1', 'm2', 'm3', 'm4'],
        'twos': [1, 2, 3, 4],
        'base': [1, 2, 3, 4],
        'ds_cw1': [5, 6, 7, 8],
        'ds_cw2': [5, 6, 7, 8],
    })
    # region ds m1 5 m2 6   soldto dn m1 2+6=8 m2 12+20=32   avg= 1  2  3 4
    # m1-s1 1  m1-s2 4 , m2-s1 0.6  m2-s2 4.8
    demand_calculator = DeltaDemandCalculator(DELTA_DEMAND)
    demand_calculator.region_df = region_df
    demand_calculator.woi_decr = 0.1
    df = demand_calculator._calculate_cw1(soldto_df)
    final_dn_cw1_result = df['final_dn_cw1'].tolist()
    assert final_dn_cw1_result == [1.0, 4.0, 0.6, 4.8]


def test_delta_demand_calculator_cw2():
    # demand = self.base + self.avg_normalized_2_4 * woi
    # if not self.is_cw1:
    #     demand = self.base + self.normalized_fcst_cw2 + self.avg_normalized_3_5 * woi - self.dn_cw1
    soldto_df = pd.DataFrame({
        'fiscal_week': ['FY24Q3W10', 'FY24Q3W10', 'FY24Q3W10', 'FY24Q3W10'],
        'region': ['China mainland', 'China mainland', 'China mainland', 'China mainland'],
        'sold_to_id': ['s1', 's2', 's1', 's2'],
        'mpn': ['m1', 'm1', 'm2', 'm2'],
        'twos': [1, 2, 3, 4],
        'base': [1, 2, 3, 4],
        'normalized_fcst_cw2': [1, 2, 3, 4],
        'normalized_fcst_cw3': [1, 2, 3, 4],
        'normalized_fcst_cw4': [1, 2, 3, 4],
        'normalized_fcst_cw5': [1, 2, 3, 4],
        'normalized_fcst_cw6': [1, 2, 3, 4],
        'dn_cw1': [2, 6, 12, 20],
        'dn_cw2': [1, 2, 3, 4],
    })
    region_df = pd.DataFrame({
        'fiscal_week': ['FY24Q3W10', 'FY24Q3W10', 'FY24Q3W10', 'FY24Q3W10'],
        'region': ['China mainland', 'China mainland', 'China mainland', 'China mainland'],
        'mpn': ['m1', 'm2', 'm3', 'm4'],
        'twos': [1, 2, 3, 4],
        'base': [1, 2, 3, 4],
        'ds_cw1': [5, 6, 7, 8],
        'ds_cw2': [5, 6, 7, 8],
    })
    # region ds m1 5 m2 6   soldto dn m1 2+6=8 m2 12+20=32   avg= 1  2  3 4
    # m1-s1 1  m1-s2 4 , m2-s1 0.6  m2-s2 4.8
    demand_calculator = DeltaDemandCalculator(DELTA_DEMAND)
    demand_calculator.region_df = region_df
    demand_calculator.woi_decr = 0.1
    df = demand_calculator._calculate_cw1(soldto_df)
    final_dn_cw1_result = df['final_dn_cw1'].tolist()
    demand_result = demand_calculator._calculate_cw2(df)
    assert final_dn_cw1_result == [1.0, 4.0, 0.6, 4.8]


def test_delta_demand_demo():
    soldto_df = pd.DataFrame({
        'soldto_id': ['s1', 's2', 's3', 's1'],
        'mpn': ['mpn1', 'mpn1', 'mpn4', 'mpn4'],
        'dn': [3, 2, 3, 4],
        'ds': [2, 2, 4, 4],
        'avg*twos': [2, 1, 4, 4],
        'd_twos': [0, 0, 0, 0],
    })
    soldto_df['need_cal'] = True
    need_cal = True
    while need_cal:
        soldto_df.loc[soldto_df['need_cal'], 'tem_dn'] = (soldto_df.loc[soldto_df['need_cal'], 'dn']
                                                          - soldto_df.loc[soldto_df['need_cal'], 'avg*twos']
                                                          * soldto_df.loc[soldto_df['need_cal'], 'd_twos'])
        # twos 每次减0.01   a * b -  a * b * 0.99  = a * b * 0.01
        # soldto_df['tem_dn'] = soldto_df['dn'] - soldto_df['avg*twos'] * soldto_df['d_twos']
        soldto_df['tem_dn'] = soldto_df['tem_dn'].clip(lower=0)
        # 聚合到regin后，判断是否需要计算
        region_df = soldto_df.groupby('mpn').sum().reset_index()
        # 判断 tem_dn 是否<= ds ,如果小于 则 need_cal = False
        region_df['need_cal'] = region_df.apply(lambda row: False if row['tem_dn'] <= row['ds'] else True, axis=1)
        # 判断是否region_df 所有need_cal 都为false  则不需要计算
        need_cal = not (region_df['need_cal'] == False).all()
        soldto_df = soldto_df.drop('need_cal', axis=1)
        soldto_df = soldto_df.merge(region_df[['mpn', 'need_cal']], on='mpn', how='left')

        if need_cal:
            soldto_df['d_twos'] = soldto_df['d_twos'] + 0.01
    assert not need_cal
