import pytest

import pandas as pd

from domain.demand.impl.calculator.final_demand.final_demand_calculator import FinalDemandCalculator
from domain.demand.entity.const import FINAL_DEMAND

def test_final_demand_calculator_cw1():
    sold_to_df= pd.DataFrame({
        'normalized_fcst_cw2': [50, 25, 60],  ###
        'normalized_fcst_cw3': [50, 25, 60],
        'normalized_fcst_cw4': [50, 25, 60],
        'twos': [5.5, 5.5, 5.5],  ###
        'base': [200, 200, 200],  ###
        'cw1_ideal_demand': [800, 1000, 800],
        'final_dn_cw1': [475, 337.5, 530],  ###
        'mpn': ['mpn1', 'mpn1', 'mpn1'],
        'sold_to_id': ['1', '2', '3'],
        'df_cw1': [None, None, None],
    })

    region_df = pd.DataFrame({
        'mpn': ['mpn1'],
        'ds_cw1': [2000],
        'final_dn_cw1': [1000],
    })

    # region_df = DemandByRegionPool.get_by_fiscal_week('FY24Q3W11')
    # soldto_df = DemandBySoldtoPool.get_by_fiscal_week('FY24Q3W11')

    demand_calculator = FinalDemandCalculator(FINAL_DEMAND)
    demand_calculator.region_df = region_df

    results = [[900, 642, 800],
               [940, 599, 800],
               [957, 585, 800],
               [966, 576, 800],
               [973, 569, 800],]

    for i in range(5):
        sold_to_df.at[0, 'cw1_ideal_demand'] += 100
        df = demand_calculator._calculate_cw1(sold_to_df)
        final_dn_cw1_result = df['df_cw1'].tolist()
        assert final_dn_cw1_result == pytest.approx(results[i], 1)
