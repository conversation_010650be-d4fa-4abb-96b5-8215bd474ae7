########## must at head ############


import os
import sys

import pandas as pd

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(SCRIPT_DIR))

from domain.demand.entity.const import NORMALIZED_DEMAND
from domain.demand.impl.calculator.normalized_demand_calculator import NormalizedDemandCalculator
from domain.demand.impl.calculator.test.test_calculator_df import test_calculator_df


##########      end     ############

# usage
# ENV=dev pytest -s test/test_esr.py

def test_normalized_demand_calculator_cw1():
    region_df, soldto_df = test_calculator_df()
    normalized_demand_calculator = NormalizedDemandCalculator(NORMALIZED_DEMAND)
    # CW+1 DN =Sum ML UB FCST (CW , CW+1 ) + Average RTM Normalized FCST (CW+2 ~CW+4)*(TWOS+X) - CW-1 UB EOH (actual) - CW Shipment Plan
    # cw+1 dn0 = 1 + (1+1+1) * 1 / 3
    # cw+1 dn1 = 2 + (2+2+2) * 2 / 3
    # cw+1 dn2 = 3 + (3+3+3) * 3 / 3
    # cw+1 dn3 = 4 + (4+4+4) * 4 / 3
    dn_cw1_arr = [1 + (1 + 1 + 1) * 1 / 3, 2 + (2 + 2 + 2) * 2 / 3, 3 + (3 + 3 + 3) * 3 / 3, 4 + (4 + 4 + 4) * 4 / 3]
    df = normalized_demand_calculator._calculate_cw1(soldto_df)
    tolist = df['dn_cw1'].tolist()
    assert tolist == dn_cw1_arr


def test_normalized_demand_calculator_cw2():
    region_df, soldto_df = test_calculator_df()
    normalized_demand_calculator = NormalizedDemandCalculator(NORMALIZED_DEMAND)
    dn_cw1_arr = [2.0, 6.0, 12.0, 20.0]
    df = normalized_demand_calculator._calculate_cw1(soldto_df)
    # CW+2 DN= base + CW+2  RTM Normalized FCST + Average RTM Normalized FCST(CW+3 ~CW+5)*(TWOS+X)  - CW+1 DN
    # cw+2 dn0 = 1 + 1 + (1+1+1)*1/3 - 2
    # cw+2 dn1 = 2 + 2 + (2+2+2)*1/3 - 6
    # cw+2 dn2 = 3 + 3 + (3+3+3)*1/3 - 12
    # cw+2 dn3 = 4 + 4 + (4+4+4)*1/3 - 20
    demand_result = normalized_demand_calculator._calculate_cw2(df)
    dn_cw2_arr = [1 + 1 + (1 + 1 + 1) * 1 / 3 - 2,
                  2 + 2 + (2 + 2 + 2) * 2 / 3 - 6,
                  3 + 3 + (3 + 3 + 3) * 3 / 3 - 12,
                  4 + 4 + (4 + 4 + 4) * 4 / 3 - 20]
    tolist = demand_result.get_df()['dn_cw2'].tolist()
    assert tolist == dn_cw2_arr


def test_normalized_demand_calculator_avg_zero():
    region_df = pd.DataFrame({
        'fiscal_week': ['FY24Q3W10', 'FY24Q3W10', 'FY24Q3W10', 'FY24Q3W10'],
        'region': ['China mainland', 'China mainland', 'China mainland', 'China mainland'],
        'lob': ['iPhone', 'iPhone', 'iPhone', 'iPhone'],
        'sub_lob': ['iPhone 14', 'iPhone 14', 'iPhone 14', 'iPhone 14'],
        'nand': ['512GB', '512GB', '512GB', '512GB'],
        'color': ['YELLOW', 'YELLOW', 'YELLOW', 'YELLOW'],
        'mpn': ['mpn1', 'mpn2', 'mpn3', 'mpn1'],
        'avg_ub_1_5': [0, 1, 3, 0],
    })

    df = region_df.groupby(['mpn']).sum().reset_index()
    zero_list = df[df['avg_ub_1_5'] == 0].to_dict(orient='records')
    print(zero_list)
    assert zero_list
