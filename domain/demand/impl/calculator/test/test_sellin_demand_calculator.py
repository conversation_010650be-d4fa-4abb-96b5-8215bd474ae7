import pandas as pd

from domain.demand.impl.calculator.sellin_demand_calculator import calculate_cw1, max_cw1_demand_by_sublob, \
    calculate_cw2
from domain.demand.impl.calculator.test.test_calculator_df import test_calculator_df


def test_calculate_cw1():
    # 公式说明：
    # CW+1 DS’ = Sum ML National FCST (CW , CW+1 ) + Average DFA UB FCST(CW+2 ~CW+4)*(范围内的最大值) - CW-1 UB EOH (actual) - CW Shipment Plan
    # CW+1 DS’ WOI = [CW+1 DS' + CW-1 UB EOH(actual) + CW Shipment Plan - Sum ML National FCST(CW, CW+1)]/Average DFA FCST(CW+2 ~CW+4)
    # 简化后：
    # CW+1 DS’ = base + Average DFA UB FCST(CW+2 ~CW+4)*(woi)
    # CW+1 DS’ WOI = (CW+1 DS' - base)/Average DFA UB FCST(CW+2 ~CW+4)
    # 调整woi计算demand的时候，有一个前置条件：demand >=shipment_plan_cw1

    region_df = pd.DataFrame({
        'region': ['China mainland', 'China mainland', 'China mainland', 'China mainland'],
        'lob': ['iPhone', 'iPhone', 'iPhone', 'iPhone'],
        'sub_lob': ['iPhone 14', 'iPhone 14', 'iPhone 14', 'iPhone 14'],
        'mpn': ['mpn1', 'mpn2', 'mpn3', 'mpn4'],
        'shipment_plan_cw': [1, 2, 3, 4],
        'shipment_plan_cw1': [1, 2, 3, 4],
        'shipment_plan_cw2': [1, 2, 3, 4],
        'avg_dfa_fcst_2_4': [1, 2, 3, 4],
        'avg_dfa_fcst_3_5': [1, 2, 3, 4],
        'ub_eoh': [1, 2, 3, 4],
        'forecast_cw2_dfa': [1, 2, 3, 4],
        'cw1_ideal_demand': [1, 2, 3, 4],   # 通过cw1_ideal_demand计算各mpn的cw1初始woi：0，0，0，0，用初始woi和woi_by_mpn_min/woi_by_mpn_max进行比较
        'cw2_ideal_demand': [1, 2, 3, 4],
        'base': [1, 2, 3, 4],
        'woi_by_mpn_min': [1, 2, 3, 4],  # mpn cw1 min demand: 2, 6, 12, 20
        'woi_by_mpn_max': [2, 3, 4, 5],  # mpn cw1 max demand：3, 8, 15, 24
        'woi_by_sublob_max': [5, 5, 5, 5]  # sublob cw1 max demand: 60
    })
    df = calculate_cw1(region_df)
    print(df)
    expert_df = pd.DataFrame({
        'region': ['China mainland', 'China mainland', 'China mainland', 'China mainland'],
        'lob': ['iPhone', 'iPhone', 'iPhone', 'iPhone'],
        'sub_lob': ['iPhone 14', 'iPhone 14', 'iPhone 14', 'iPhone 14'],
        'mpn': ['mpn1', 'mpn2', 'mpn3', 'mpn4'],
        'shipment_plan_cw': [1, 2, 3, 4],
        'shipment_plan_cw1': [1, 2, 3, 4],
        'shipment_plan_cw2': [1, 2, 3, 4],
        'avg_dfa_fcst_2_4': [1, 2, 3, 4],
        'avg_dfa_fcst_3_5': [1, 2, 3, 4],
        'ub_eoh': [1, 2, 3, 4],
        'forecast_cw2_dfa': [1, 2, 3, 4],
        'cw1_ideal_demand': [1, 2, 3, 4],
        'cw2_ideal_demand': [1, 2, 3, 4],
        'base': [1, 2, 3, 4],
        'woi_by_mpn_min': [1, 2, 3, 4],
        'woi_by_mpn_max': [2, 3, 4, 5],
        'woi_by_sublob_max': [5, 5, 5, 5],
        'ds_cw1': [2.00000, 6.00000, 12.00000, 20.00000],  # 得出各个mpn最小woi下的demand
        'ds_cw1_woi': [1.00000, 2.00000, 3.00000, 4.00000]  #得出对应的woi
    })
    assert df.equals(expert_df), f"df: {df}, expert_df: {expert_df}"

    # 将 region_df 中的cw1_ideal_demand 列设置为 [10, 20, 30, 40]，将各mpn的cw1初始woi调成9，9，9，9
    region_df['cw1_ideal_demand'] = [10, 20, 30, 40]

    df = calculate_cw1(region_df)
    print(df)
    expert_df = pd.DataFrame({
        'region': ['China mainland', 'China mainland', 'China mainland', 'China mainland'],
        'lob': ['iPhone', 'iPhone', 'iPhone', 'iPhone'],
        'sub_lob': ['iPhone 14', 'iPhone 14', 'iPhone 14', 'iPhone 14'],
        'mpn': ['mpn1', 'mpn2', 'mpn3', 'mpn4'],
        'shipment_plan_cw': [1, 2, 3, 4],
        'shipment_plan_cw1': [1, 2, 3, 4],
        'shipment_plan_cw2': [1, 2, 3, 4],
        'avg_dfa_fcst_2_4': [1, 2, 3, 4],
        'avg_dfa_fcst_3_5': [1, 2, 3, 4],
        'ub_eoh': [1, 2, 3, 4],
        'forecast_cw2_dfa': [1, 2, 3, 4],
        'cw1_ideal_demand': [10, 20, 30, 40],
        'cw2_ideal_demand': [1, 2, 3, 4],
        'base': [1, 2, 3, 4],
        'woi_by_mpn_min': [1, 2, 3, 4],
        'woi_by_mpn_max': [2, 3, 4, 5],
        'woi_by_sublob_max': [5, 5, 5, 5],  # sublob最大demand：60
        'ds_cw1': [3.00000, 8.00000, 15.00000, 24.00000],
        'ds_cw1_woi': [2.00000, 3.00000, 4.00000, 5.00000]
    })
    assert df.equals(expert_df), f"df: {df}, expert_df: {expert_df}"

    # 将'woi_by_sublob_max'设置为 [3, 3, 3, 3]，使得sublob最大demand为40，使各个mpn从最大的woi调减到最小的mpn
    region_df['woi_by_sublob_max'] = [3, 3, 3, 3]
    # 校验各个mpn最大demand之和(40) = sublob最大demand(40) 的情况
    df = calculate_cw1(region_df)
    print(df)
    expert_df = pd.DataFrame({
        'region': ['China mainland', 'China mainland', 'China mainland', 'China mainland'],
        'lob': ['iPhone', 'iPhone', 'iPhone', 'iPhone'],
        'sub_lob': ['iPhone 14', 'iPhone 14', 'iPhone 14', 'iPhone 14'],
        'mpn': ['mpn1', 'mpn2', 'mpn3', 'mpn4'],
        'shipment_plan_cw': [1, 2, 3, 4],
        'shipment_plan_cw1': [1, 2, 3, 4],
        'shipment_plan_cw2': [1, 2, 3, 4],
        'avg_dfa_fcst_2_4': [1, 2, 3, 4],
        'avg_dfa_fcst_3_5': [1, 2, 3, 4],
        'ub_eoh': [1, 2, 3, 4],
        'forecast_cw2_dfa': [1, 2, 3, 4],
        'cw1_ideal_demand': [10, 20, 30, 40],  # woi：9，9，9，9
        'cw2_ideal_demand': [1, 2, 3, 4],
        'base': [1, 2, 3, 4],
        'woi_by_mpn_min': [1, 2, 3, 4],
        'woi_by_mpn_max': [2, 3, 4, 5],  # demand：3，8，15，24
        'woi_by_sublob_max': [3, 3, 3, 3],  # demand：40
        'ds_cw1': [2.00000, 6.00000, 12.00000, 20.0000],
        'ds_cw1_woi': [1.00000, 2.00000, 3.00000, 4.0000]
    })
    assert df.equals(expert_df), f"df: {df}, expert_df: {expert_df}"

    # 将每个mpn的woi_by_mpn_min设置为1，使所有mpn都能进行调减
    # woi_by_sublob_max设置为3.5，使得sublob最大demand为45
    region_df['woi_by_mpn_min'] = [1, 1, 1, 1]
    region_df['woi_by_sublob_max'] = [3.5, 3.5, 3.5, 3.5]
    # 校验各个mpn最大demand之和(40) < sublob最大demand(60) 的调减情况
    df = calculate_cw1(region_df)
    print(df)
    expert_df = pd.DataFrame({
        'region': ['China mainland', 'China mainland', 'China mainland', 'China mainland'],
        'lob': ['iPhone', 'iPhone', 'iPhone', 'iPhone'],
        'sub_lob': ['iPhone 14', 'iPhone 14', 'iPhone 14', 'iPhone 14'],
        'mpn': ['mpn1', 'mpn2', 'mpn3', 'mpn4'],
        'shipment_plan_cw': [1, 2, 3, 4],
        'shipment_plan_cw1': [1, 2, 3, 4],
        'shipment_plan_cw2': [1, 2, 3, 4],
        'avg_dfa_fcst_2_4': [1, 2, 3, 4],
        'avg_dfa_fcst_3_5': [1, 2, 3, 4],
        'ub_eoh': [1, 2, 3, 4],
        'forecast_cw2_dfa': [1, 2, 3, 4],
        'cw1_ideal_demand': [10, 20, 30, 40],
        'cw2_ideal_demand': [1, 2, 3, 4],
        'base': [1, 2, 3, 4],
        'woi_by_mpn_min': [1, 1, 1, 1],
        'woi_by_mpn_max': [2, 3, 4, 5],
        'woi_by_sublob_max': [3.50000, 3.50000, 3.50000, 3.50000],
        'ds_cw1': [3.00000, 8.00000, 14.70000, 19.20000],
        'ds_cw1_woi': [2.00000, 3.00000, 3.90000, 3.80000]
    })
    assert df.equals(expert_df), f"df: {df}, expert_df: {expert_df}"


def test_calculate_cw2():
    # 公式说明：
    # CW+2 DS’ = Sum ML National FCST (CW , CW+1 ) + CW+2 DFA FCST + Average DFA FCST(CW+3 ~CW+5)*(范围内的最大值) - CW-1 UB EOH (actual) - CW Shipment Plan - CW+1 DS’
    #  CW+2 DS’ WOI =  [CW+2 Shipment Plan/0 + CW-1 UB EOU(actual) + CW Shipment Plan + CW+1 DI - Sum ML National FCST(CW, CW+1) - CW+2 DFA FCST]/Average DFA FCST(CW+3 ~CW+5)
    # 简化后：
    # CW+2 DS’ = base + CW+2 DFA FCST - CW+1 DS’ + Average DFA UB FCST(CW+3 ~CW+5)*(woi)
    # CW+2 DS’ WOI = (CW+2 DS' - base - CW+2 DFA FCST + CW+1 DS’)/Average DFA UB FCST(CW+3 ~CW+5)

    # 调整woi计算demand的时候，有一个前置条件：demand >=shipment_plan_cw2

    # 校验初始cw2 woi为1, 2, 3, 4，且mpn最小demand之和(10) < sublob最大demand(30) 的情况
    region_df = pd.DataFrame({
        'region': ['China mainland', 'China mainland', 'China mainland', 'China mainland'],
        'lob': ['iPhone', 'iPhone', 'iPhone', 'iPhone'],
        'sub_lob': ['iPhone 14', 'iPhone 14', 'iPhone 14', 'iPhone 14'],
        'mpn': ['mpn1', 'mpn2', 'mpn3', 'mpn4'],
        'shipment_plan_cw': [1, 2, 3, 4],
        'shipment_plan_cw1': [1, 2, 3, 4],
        'shipment_plan_cw2': [1, 2, 3, 4],
        'avg_dfa_fcst_2_4': [1, 2, 3, 4],
        'avg_dfa_fcst_3_5': [1, 2, 3, 4],
        'ub_eoh': [1, 2, 3, 4],
        'forecast_cw2_dfa': [1, 2, 3, 4],
        'cw1_ideal_demand': [1, 2, 3, 4],
        'cw2_ideal_demand': [1, 2, 3, 4],
        'base': [1, 2, 3, 4],
        'woi_by_mpn_min': [1, 2, 3, 4],  # mpn cw2 min demand: 1, 2, 3, 4， woi: 1, 2, 3, 4
        'woi_by_mpn_max': [2, 3, 4, 5],  # mpn cw2 max demand：2, 4, 6, 8
        'woi_by_sublob_max': [5, 5, 5, 5],  # sub_lob cw2 max demand: 30
        'ds_cw1': [2.00000, 6.00000, 12.00000, 20.00000]
    })
    df = calculate_cw2(region_df)
    print(df)
    expert_df = pd.DataFrame({
        'region': ['China mainland', 'China mainland', 'China mainland', 'China mainland'],
        'lob': ['iPhone', 'iPhone', 'iPhone', 'iPhone'],
        'sub_lob': ['iPhone 14', 'iPhone 14', 'iPhone 14', 'iPhone 14'],
        'mpn': ['mpn1', 'mpn2', 'mpn3', 'mpn4'],
        'shipment_plan_cw': [1, 2, 3, 4],
        'shipment_plan_cw1': [1, 2, 3, 4],
        'shipment_plan_cw2': [1, 2, 3, 4],
        'avg_dfa_fcst_2_4': [1, 2, 3, 4],
        'avg_dfa_fcst_3_5': [1, 2, 3, 4],
        'ub_eoh': [1, 2, 3, 4],
        'forecast_cw2_dfa': [1, 2, 3, 4],
        'cw1_ideal_demand': [1, 2, 3, 4],
        'cw2_ideal_demand': [1, 2, 3, 4],
        'base': [1, 2, 3, 4],
        'woi_by_mpn_min': [1, 2, 3, 4],
        'woi_by_mpn_max': [2, 3, 4, 5],
        'woi_by_sublob_max': [5, 5, 5, 5],
        'ds_cw1': [2.00000, 6.00000, 12.00000, 20.00000],  # 得出各个mpn最小woi下的demand
        'ds_cw2': [1.00000, 2.00000, 3.00000, 4.00000],  # 得出各个mpn最小woi下的demand
        'ds_cw2_woi': [1.00000, 2.00000, 3.00000, 4.00000]  # 得出对应的woi
    })
    assert df.equals(expert_df), f"df: {df}, expert_df: {expert_df}"

    # 将 region_df 中的cw2_ideal_demand 列设置为 [10, 20, 30, 40]，调大初始cw2 woi调成9，9，9，9
    region_df['cw2_ideal_demand'] = [10, 20, 30, 40]
    # 校验各个mpn最大demand之和(20) < sublob最大demand(30) 的情况
    df = calculate_cw2(region_df)
    print(df)
    expert_df = pd.DataFrame({
        'region': ['China mainland', 'China mainland', 'China mainland', 'China mainland'],
        'lob': ['iPhone', 'iPhone', 'iPhone', 'iPhone'],
        'sub_lob': ['iPhone 14', 'iPhone 14', 'iPhone 14', 'iPhone 14'],
        'mpn': ['mpn1', 'mpn2', 'mpn3', 'mpn4'],
        'shipment_plan_cw': [1, 2, 3, 4],
        'shipment_plan_cw1': [1, 2, 3, 4],
        'shipment_plan_cw2': [1, 2, 3, 4],
        'avg_dfa_fcst_2_4': [1, 2, 3, 4],
        'avg_dfa_fcst_3_5': [1, 2, 3, 4],
        'ub_eoh': [1, 2, 3, 4],
        'forecast_cw2_dfa': [1, 2, 3, 4],
        'cw1_ideal_demand': [1, 2, 3, 4],
        'cw2_ideal_demand': [10, 20, 30, 40],
        'base': [1, 2, 3, 4],
        'woi_by_mpn_min': [1, 2, 3, 4],
        'woi_by_mpn_max': [2, 3, 4, 5],
        'woi_by_sublob_max': [5, 5, 5, 5],
        'ds_cw1': [2.00000, 6.00000, 12.00000, 20.00000],
        'ds_cw2': [2.00000, 4.00000, 6.00000, 8.00000],  # 得出各个mpn最大woi下的demand
        'ds_cw2_woi': [2.00000, 3.00000, 4.00000, 5.00000]  # 得出对应的woi
    })
    assert df.equals(expert_df), f"df: {df}, expert_df: {expert_df}"

    # 将'woi_by_sublob_max'设置为 [3, 3, 3, 3]，使得sublob最大demand为10
    region_df['woi_by_sublob_max'] = [3, 3, 3, 3]
    # 校验各个mpn最大demand之和(20) > sublob最大demand(10) 的情况
    df = calculate_cw2(region_df)
    print(df)
    expert_df = pd.DataFrame({
        'region': ['China mainland', 'China mainland', 'China mainland', 'China mainland'],
        'lob': ['iPhone', 'iPhone', 'iPhone', 'iPhone'],
        'sub_lob': ['iPhone 14', 'iPhone 14', 'iPhone 14', 'iPhone 14'],
        'mpn': ['mpn1', 'mpn2', 'mpn3', 'mpn4'],
        'shipment_plan_cw': [1, 2, 3, 4],
        'shipment_plan_cw1': [1, 2, 3, 4],
        'shipment_plan_cw2': [1, 2, 3, 4],
        'avg_dfa_fcst_2_4': [1, 2, 3, 4],
        'avg_dfa_fcst_3_5': [1, 2, 3, 4],
        'ub_eoh': [1, 2, 3, 4],
        'forecast_cw2_dfa': [1, 2, 3, 4],
        'cw1_ideal_demand': [1, 2, 3, 4],
        'cw2_ideal_demand': [10, 20, 30, 40],  # woi：9，9，9，9
        'base': [1, 2, 3, 4],
        'woi_by_mpn_min': [1, 2, 3, 4],
        'woi_by_mpn_max': [2, 3, 4, 5],  # demand：3，8，15，24
        'woi_by_sublob_max': [3, 3, 3, 3],  # demand：40
        'ds_cw1': [2.00000, 6.00000, 12.00000, 20.00000],
        'ds_cw2': [1.00000, 2.00000, 3.00000, 4.00000],  # 得出各个mpn最大woi下的demand
        'ds_cw2_woi': [1.00000, 2.00000, 3.00000, 4.00000]  # 得出对应的woi
    })
    assert df.equals(expert_df), f"df: {df}, expert_df: {expert_df}"

    # 将每个mpn的woi_by_mpn_min设置为1，使所有mpn都能进行调减
    # woi_by_sublob_max设置为3.5，使得sublob最大demand为15
    region_df['woi_by_mpn_min'] = [1, 1, 1, 1]
    region_df['woi_by_sublob_max'] = [3.5, 3.5, 3.5, 3.5]
    # 校验各个mpn最大demand之和(20) > sublob最大demand(15) 的调减情况
    df = calculate_cw2(region_df)
    print(df)
    expert_df = pd.DataFrame({
        'region': ['China mainland', 'China mainland', 'China mainland', 'China mainland'],
        'lob': ['iPhone', 'iPhone', 'iPhone', 'iPhone'],
        'sub_lob': ['iPhone 14', 'iPhone 14', 'iPhone 14', 'iPhone 14'],
        'mpn': ['mpn1', 'mpn2', 'mpn3', 'mpn4'],
        'shipment_plan_cw': [1, 2, 3, 4],
        'shipment_plan_cw1': [1, 2, 3, 4],
        'shipment_plan_cw2': [1, 2, 3, 4],
        'avg_dfa_fcst_2_4': [1, 2, 3, 4],
        'avg_dfa_fcst_3_5': [1, 2, 3, 4],
        'ub_eoh': [1, 2, 3, 4],
        'forecast_cw2_dfa': [1, 2, 3, 4],
        'cw1_ideal_demand': [1, 2, 3, 4],
        'cw2_ideal_demand': [10, 20, 30, 40],
        'base': [1, 2, 3, 4],
        'woi_by_mpn_min': [1, 1, 1, 1],
        'woi_by_mpn_max': [2, 3, 4, 5],
        'woi_by_sublob_max': [3.50000, 3.50000, 3.50000, 3.50000],
        'ds_cw1': [2.00000, 6.00000, 12.00000, 20.00000],
        'ds_cw2': [2.00000, 4.00000, 4.80000, 4.00000],  # 得出各个mpn最大woi下的demand
        'ds_cw2_woi': [2.00000, 3.00000, 3.60000, 4.00000]  # 得出对应的woi
    })
    assert df.equals(expert_df), f"df: {df}, expert_df: {expert_df}"


