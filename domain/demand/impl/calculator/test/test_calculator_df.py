import pandas as pd


def test_calculator_df() -> tuple[pd.DataFrame, pd.DataFrame]:
    region_df = pd.DataFrame({
        'fiscal_week': ['FY24Q3W10', 'FY24Q3W10', 'FY24Q3W10', 'FY24Q3W10'],
        'region': ['China mainland', 'China mainland', 'China mainland', 'China mainland'],
        'lob': ['iPhone', 'iPhone', 'iPhone', 'iPhone'],
        'sub_lob': ['iPhone 14', 'iPhone 14', 'iPhone 14', 'iPhone 14'],
        'nand': ['512GB', '512GB', '512GB', '512GB'],
        'color': ['YELLOW', 'YELLOW', 'YELLOW', 'YELLOW'],
        'mpn': ['mpn1', 'mpn2', 'mpn3', 'mpn4'],
        'hr_lr': ['HR', 'HR', 'LR', 'LR'],
        'shipment_plan_cw': [1, 2, 3, 4],
        'shipment_plan_cw1': [1, 2, 3, 4],
        'shipment_plan_cw2': [1, 2, 3, 4],
        'avg_dfa_fcst_2_4': [1, 2, 3, 4],
        'avg_dfa_fcst_3_5': [1, 2, 3, 4],
        'ub_eoh': [1, 2, 3, 4],
        'forecast_cw_ml': [1, 2, 3, 4],
        'forecast_cw1_ml': [1, 2, 3, 4],
        'forecast_cw2_dfa': [1, 2, 3, 4],
        'forecast_cw3_dfa': [1, 2, 3, 4],
        'forecast_cw4_dfa': [1, 2, 3, 4],
        'forecast_cw5_dfa': [1, 2, 3, 4],
        'forecast_cw6_dfa': [1, 2, 3, 4],
        'forecast_cw7_dfa': [1, 2, 3, 4],
        'forecast_cw8_dfa': [1, 2, 3, 4],
        'cw1_x': [1, 2, 3, 4],
        'cw2_x': [1, 2, 3, 4],
        'twos': [1, 2, 3, 4],
        'base': [1, 2, 3, 4],
        'avg_ub_1_5': [1, 2, 3, 4],
        'woi_by_mpn_min': [1, 2, 3, 4],
        'woi_by_mpn_max': [1, 2, 3, 4],
        'woi_by_sublob_max': [5, 5, 5, 5],
        'ds_cw1': [1, 2, 3, 4],
        'ds_cw2': [1, 2, 3, 4]
    })
    soldto_df = pd.DataFrame({
        'fiscal_week': ['FY24Q3W10', 'FY24Q3W10', 'FY24Q3W10', 'FY24Q3W10'],
        'region': ['China mainland', 'China mainland', 'China mainland', 'China mainland'],
        'rtm': ['Mono', 'Mono', 'Mono', 'Mono'],
        'sub_rtm': ['Lifestyle', 'Lifestyle', 'Lifestyle', 'Lifestyle'],
        'sold_to_id': ['531599', '531599', '531599', '531599'],
        'sold_to_name': ['s', 's', 's', 's'],
        'lob': ['iPhone', 'iPhone', 'iPhone', 'iPhone'],
        'sub_lob': ['iPhone 14', 'iPhone 14', 'iPhone 14', 'iPhone 14'],
        'nand': ['512GB', '512GB', '512GB', '512GB'],
        'color': ['YELLOW', 'YELLOW', 'YELLOW', 'YELLOW'],
        'mpn': ['mpn1', 'mpn2', 'mpn3', 'mpn4'],
        'hr_lr': ['HR', 'HR', 'LR', 'LR'],
        'shipment_plan_cw': [1, 2, 3, 4],
        'shipment_plan_cw1': [1, 2, 3, 4],
        'shipment_plan_cw2': [1, 2, 3, 4],
        'avg_dfa_fcst_2_4': [1, 2, 3, 4],
        'avg_dfa_fcst_3_5': [1, 2, 3, 4],
        'ub_eoh': [1, 2, 3, 4],
        'forecast_cw_ml': [1, 2, 3, 4],
        'forecast_cw1_ml': [1, 2, 3, 4],
        'forecast_cw_sales': [1, 2, 3, 4],
        'forecast_cw1_sales': [1, 2, 3, 4],
        'forecast_cw2_sales': [1, 2, 3, 4],
        'forecast_cw3_sales': [1, 2, 3, 4],
        'forecast_cw5_sales': [1, 2, 3, 4],
        'forecast_cw6_sales': [1, 2, 3, 4],
        'forecast_cw7_sales': [1, 2, 3, 4],
        'forecast_cw8_sales': [1, 2, 3, 4],
        'forecast_cw7_dfa': [1, 2, 3, 4],
        'forecast_cw8_dfa': [1, 2, 3, 4],
        'cw1_x': [1, 2, 3, 4],
        'cw2_x': [1, 2, 3, 4],
        'twos': [1, 2, 3, 4],
        'base': [1, 2, 3, 4],
        'avg_ub_1_5': [1, 2, 3, 4],
        'normalized_fcst_cw2': [1, 2, 3, 4],
        'normalized_fcst_cw3': [1, 2, 3, 4],
        'normalized_fcst_cw4': [1, 2, 3, 4],
        'normalized_fcst_cw5': [1, 2, 3, 4],
        'normalized_fcst_cw6': [1, 2, 3, 4],
        'dn_cw1': [1, 2, 3, 4],
        'dn_cw2': [1, 2, 3, 4],
        'final_dn_cw1': [1, 2, 3, 4],  # ds'
        'final_dn_cw2': [1, 2, 3, 4],
    })
    return region_df, soldto_df
