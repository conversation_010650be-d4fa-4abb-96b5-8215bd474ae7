from domain.demand.entity.state import DemandState
from domain.demand.impl.state_machine import StateProxy


class RtmStateProxy:
    def __init__(self, fiscal_week):
        self.fiscal_week = fiscal_week
        self.state_proxy = StateProxy(fiscal_week, "ideal_demand")

    def is_rtm_waiting_for_calculation(self, rtm: str) -> bool:
        """rtm 是否等待计算"""
        return (self.state_proxy.current_state().value == DemandState.WaitingForRTMSetup.value and
                self.state_proxy.get_rtm_state(rtm) == 1)

    def is_rtm_waiting_for_file(self, rtm: str) -> bool:
        """rtm 是否下发文件名"""
        return ((self.state_proxy.current_state().value > DemandState.WaitingForRTMSetup.value) or
                self.is_rtm_waiting_for_calculation(rtm))

    def get_rtm_status(self, rtm: str, channel: str):
        """
            获取rtm状态
            usage: status = get_rtm_status(rtm).format()
        """
        ideal_demand_state: DemandState = self.state_proxy.current_state()
        if channel != 'CP&F':
            return ideal_demand_state.rtm_state(self.state_proxy.get_rtm_state(rtm))
        else:
            # 状态不能往回改 system current state eq 2: if rtm state 1 set status 10, otherwise set 2
            if self.is_rtm_waiting_for_calculation(rtm):
                ideal_demand_state = DemandState.WaitingForCalculation
            return ideal_demand_state
