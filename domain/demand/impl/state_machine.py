from typing import Union, Optional

from statemachine import StateMachine, State
from datetime import datetime

from data.mysqls.demand.demand_state import TblDemandState
from domain.dashboard.entity.fiscal_week import FiscalWeek
from domain.dashboard.impl.fiscal_week_container import FiscalWeekContainer
from domain.demand.entity.demand_record import DemandR<PERSON>ord
from domain.demand.impl.final_demand_resolver import is_all_sub_lob_publish
from util.conf import logger
from domain.demand.entity.state import DemandState, RTM_BINARY_MAPPING
from domain.demand.entity.const import *


# TODO next() cond
def are_last_four_bits_one(num):
    # 创建一个掩码，只有最低四位是1
    mask = 0b1111
    return (num & mask) == mask


class StateCond:
    def __init__(self, fiscal_week: str, demand: str):
        self.fiscal_week = fiscal_week
        self.demand = demand

    def all_rtm_complete_setup(self):
        # 所有RTM完成设置X后
        state, rtm_state, _ = get_state_from_DB(self.fiscal_week, self.demand)
        return are_last_four_bits_one(rtm_state)


class StateControl(StateMachine):
    not_started = State(name="Not Started", initial=True, value=DemandState.NotStarted)
    waiting_to_setup = State(name="Waiting to Setup", value=DemandState.WaitingToSetup)
    waiting_for_rtm_setup = State(name="Waiting for RTM Setup", value=DemandState.WaitingForRTMSetup)
    waiting_for_calculation = State(value=DemandState.WaitingForCalculation)
    calculation_completed = State(value=DemandState.CalculationCompleted)
    end = State(final=True, value=DemandState.END)

    # 状态转换
    # cond 条件满足true，进行状态转化
    initialize = not_started.to(waiting_to_setup)

    # unless 条件满足false，进行状态转化
    # 如果不加or(|) 的条件，在转换状态时,如果不满足cond条件，报错如下: 
    # statemachine.exceptions.TransitionNotAllowed: Can't rtm_setup when in Waiting to Setup.
    # 在这里可以使用to.itself() 代替 unless
    cpf_setup_y = waiting_to_setup.to(waiting_for_rtm_setup) | waiting_to_setup.to.itself()

    rtm_setup = waiting_for_rtm_setup.to(waiting_for_calculation, cond="all_rtm_complete_setup")
    calculate = waiting_for_calculation.to(calculation_completed)
    re_calculate = calculation_completed.to(waiting_for_calculation)
    calculate_end = calculation_completed.to(end)


class DTStateControl(StateMachine):
    not_started = State(name="Not Started", initial=True, value=DemandState.NotStarted)
    waiting_to_setup = State(name="Waiting To Setup", value=DemandState.WaitingToSetup)
    waiting_for_calculation = State(name="Waiting for Calculation", value=DemandState.WaitingForCalculation)
    calculation_completed = State(name="Calculation Completed", value=DemandState.CalculationCompleted)
    end = State(final=True, value=DemandState.END)
    # 状态转换
    initialize = not_started.to(waiting_to_setup)  # 暂时不会被调用
    publish = waiting_to_setup.to(waiting_for_calculation)
    calculate = waiting_for_calculation.to(calculation_completed) | waiting_to_setup.to(calculation_completed)
    re_calculate = calculation_completed.to(waiting_for_calculation)
    calculate_end = calculation_completed.to(end)


class DSStateControl(StateMachine):
    not_started = State(name="Not Started", initial=True, value=DemandState.NotStarted)
    waiting_to_setup = State(name="Waiting To Setup", value=DemandState.WaitingToSetup)
    waiting_for_calculation = State(name="Waiting for Calculation", value=DemandState.WaitingForCalculation)
    calculation_completed = State(name="Calculation Completed", value=DemandState.CalculationCompleted)
    end = State(final=True, value=DemandState.END)
    # 状态转换
    initialize = not_started.to(waiting_to_setup)
    publish = waiting_to_setup.to(waiting_for_calculation)
    calculate = waiting_for_calculation.to(calculation_completed)
    re_calculate = calculation_completed.to(waiting_for_calculation)
    calculate_end = calculation_completed.to(end)


class DNStateControl(StateMachine):
    not_started = State(name="Not Started", initial=True, value=DemandState.NotStarted)
    waiting_for_calculation = State(name="Waiting for Calculation", value=DemandState.WaitingForCalculation)
    calculation_completed = State(name="Calculation Completed", value=DemandState.CalculationCompleted)
    end = State(final=True, value=DemandState.END)
    # 状态转换
    initialize = not_started.to(waiting_for_calculation)  # 暂时不会被调用
    calculate = waiting_for_calculation.to(calculation_completed)
    re_calculate = calculation_completed.to(waiting_for_calculation)
    calculate_end = calculation_completed.to(end)


class DDStateControl(StateMachine):
    not_started = State(name="Not Started", initial=True, value=DemandState.NotStarted)
    waiting_for_calculation = State(name="Waiting for Calculation", value=DemandState.WaitingForCalculation)
    calculation_completed = State(name="Calculation Completed", value=DemandState.CalculationCompleted)
    end = State(final=True, value=DemandState.END)
    # 状态转换
    initialize = not_started.to(waiting_for_calculation)  # 暂时不会被调用
    calculate = waiting_for_calculation.to(calculation_completed)
    re_calculate = calculation_completed.to(waiting_for_calculation)
    calculate_end = calculation_completed.to(end)


class DFStateControl(StateMachine):
    not_started = State(name="Not Started", initial=True, value=DemandState.NotStarted)
    waiting_for_calculation = State(name="Waiting for Calculation", value=DemandState.WaitingForCalculation)
    calculation_completed = State(name="Calculation Completed", value=DemandState.CalculationCompleted)
    waiting_for_adjust = State(name="Waiting for Adjust", value=DemandState.WaitingForAdjust)
    published = State(name="Published", value=DemandState.PUBLISHED)
    end = State(final=True, value=DemandState.END)
    # 状态转换
    initialize = not_started.to(waiting_for_calculation)  # 暂时不会被调用
    calculate = waiting_for_calculation.to(calculation_completed)
    adjust = calculation_completed.to(waiting_for_adjust)  # 暂时不会被调用
    publish = waiting_for_adjust.to(published)  # 暂时不会被调用
    re_calculate = calculation_completed.to(waiting_for_calculation) | published.to(waiting_for_calculation) | waiting_for_adjust.to(waiting_for_calculation)
    publish_end = published.to(end)
    calculate_end = calculation_completed.to(end)


def get_state_from_DB(fiscal_week: str, demand: str) -> tuple[DemandState, int, Optional[datetime]]:
    ret = TblDemandState.query_state(fiscal_week, demand)
    return (DemandState(ret.state), ret.rtm_state, ret.update_time.strftime('%Y-%m-%d %H:%M:%S')) if ret else (
        DemandState.NotStarted, 0, None)


def get_state_by_fiscal_week(fiscal_week: str) -> dict[str:DemandRecord]:
    records = TblDemandState.query_by_fiscal_week(fiscal_week)
    records_dict = {record.demand: record for record in records}
    ret = {}
    for demand in [
        IDEAL_DEMAND,
        TOPDOWN_DEMAND,
        SELL_IN_DEMAND,
        NORMALIZED_DEMAND,
        DELTA_DEMAND,
        FINAL_DEMAND
    ]:
        if demand not in records_dict:
            ret[demand] = (DemandRecord(fiscal_week, demand, DemandState.NotStarted, 0, None))
    return records_dict


def set_state_to_DB(fiscal_week: str, demand: str, state: Union[int, None] = None,
                    rtm_state: Union[int, None] = None) -> int:
    # TODO insert_or_update insert on duplicate
    demand_state = TblDemandState.query_state(fiscal_week, demand)
    if demand_state:
        update_dict = {}
        if state is not None:
            update_dict["state"] = state
        if rtm_state is not None:
            update_dict["rtm_state"] = rtm_state

        TblDemandState.update_by_id(demand_state.id, update_dict)
        return demand_state.id
    else:
        return TblDemandState(fiscal_week, demand, state).save()


class StateProxy:
    def __init__(self, fiscal_week: str, demand: str):
        self.rtm_state = None
        self.state_time = None
        self.fiscal_week = fiscal_week
        self.demand = demand
        # 从DB获取数据
        current_state_value, self.rtm_state, self.state_time = get_state_from_DB(self.fiscal_week, self.demand)
        # 初始化状态机IdealDemand
        if demand == IDEAL_DEMAND:
            state_cond = StateCond(fiscal_week, demand)
            self.state_control = StateControl(state_cond)
        elif demand == TOPDOWN_DEMAND:
            state_cond = StateCond(fiscal_week, demand)
            self.state_control = DTStateControl(state_cond)
        elif demand == SELL_IN_DEMAND:
            state_cond = StateCond(fiscal_week, demand)
            self.state_control = DSStateControl(state_cond)
        elif demand == NORMALIZED_DEMAND:
            state_cond = StateCond(fiscal_week, demand)
            self.state_control = DNStateControl(state_cond)
        elif demand == DELTA_DEMAND:
            state_cond = StateCond(fiscal_week, demand)
            self.state_control = DDStateControl(state_cond)
        elif demand == FINAL_DEMAND:
            state_cond = StateCond(fiscal_week, demand)
            self.state_control = DFStateControl(state_cond)
        self.state_control.current_state_value = current_state_value

        def adjust_state(status: DemandState) -> DemandState:
            if self.demand == TOPDOWN_DEMAND:
                # di入口放出，dt也放出入口
                di_proxy = StateProxy(self.fiscal_week, IDEAL_DEMAND)
                if di_proxy.after_start() and not self.after_start():
                    return DemandState.WaitingToSetup
                return status

            if self.demand == SELL_IN_DEMAND:
                # di计算完成，ds放出入口  添加dt限制
                dt_proxy = StateProxy(self.fiscal_week, TOPDOWN_DEMAND)
                di_proxy = StateProxy(self.fiscal_week, IDEAL_DEMAND)
                if di_proxy.is_completed() and dt_proxy.is_completed() and not self.after_start():
                    return DemandState.WaitingToSetup
                return status

            if self.demand == NORMALIZED_DEMAND:
                # dt计算完成，dn开始计算
                dt_proxy = StateProxy(self.fiscal_week, TOPDOWN_DEMAND)
                if dt_proxy.is_completed() and not self.after_start():
                    return DemandState.WaitingForCalculation
                return status

            if self.demand == DELTA_DEMAND:
                # ds 计算完成
                ds_proxy = StateProxy(self.fiscal_week, SELL_IN_DEMAND)
                dn_proxy = StateProxy(self.fiscal_week, NORMALIZED_DEMAND)
                if ds_proxy.is_completed() and dn_proxy.is_completed() and not self.after_start():
                    return DemandState.WaitingForCalculation
                return status

            if self.demand == FINAL_DEMAND:
                # df 未开始 ，ds 计算完成
                dd_proxy = StateProxy(self.fiscal_week, DELTA_DEMAND)
                if dd_proxy.is_completed() and not self.after_start():
                    return DemandState.WaitingForCalculation
                # df 计算完成 ，关联publish状态 下发 21 30
                if self.is_completed():
                    # 如果当周已经结束则为published
                    current_week = FiscalWeekContainer().get_current_week()
                    cw_is_end = FiscalWeek(current_week).fiscal_week_int > FiscalWeek(self.fiscal_week).fiscal_week_int
                    if cw_is_end:
                        return DemandState.PUBLISHED
                    is_all_publish = is_all_sub_lob_publish(self.fiscal_week)
                    # 查询publish状态 如果所有sub_lob 已经发布返回 PUBLISHED 否则 WaitingForAdjust
                    return DemandState.PUBLISHED if is_all_publish else DemandState.WaitingForAdjust
                return status

            return status

        self.state_control.current_state_value = adjust_state(current_state_value)

    def save_state(self):
        return set_state_to_DB(self.fiscal_week, self.demand, self.current_state().value)

    def save_rtm_state(self, rtm_state: int):
        return set_state_to_DB(self.fiscal_week, self.demand, rtm_state=rtm_state)

    def current_state(self) -> DemandState:
        return self.state_control.current_state_value

    def do_initialize(self):
        try:
            self.state_control.initialize()
            self.save_state()
            return self.current_state()
        except StateMachine.TransitionNotAllowed as e:
            logger.error(e)

    def do_cpf_publish_y(self):
        try:
            self.state_control.cpf_setup_y()
            self.save_state()
            return self.current_state()
        except StateMachine.TransitionNotAllowed as e:
            logger.error(e)

    # TODO rtm 去操作DB rtm_state, 状态机不再提供
    def do_rtm_publish(self, rtm: str):
        try:
            # RTM publish时需要更新对应的状态
            if DemandState.WaitingForRTMSetup == self.current_state().rtm_state(self.get_rtm_state(rtm)):
                # 只有当状态为WaitingForRTMSetup时，才去更改rtm对应位, 并更改状态机
                rtm_binary = RTM_BINARY_MAPPING.get(rtm)
                self.rtm_state = rtm_binary | self.rtm_state
                self.save_rtm_state(self.rtm_state)

                logger.info(
                    f"{rtm}, do_rtm_publish, save_rtm_state: {self.rtm_state}, current_state: {self.current_state().value}")
                self.state_control.rtm_setup()
                self.save_state()
        except StateMachine.TransitionNotAllowed as e:
            logger.error(e)
        return self.current_state()

    def do_calculate(self):
        try:
            self.state_control.calculate()
            self.save_state()
            return self.current_state()
        except StateMachine.TransitionNotAllowed as e:
            logger.error(e)

    def get_rtm_state(self, rtm: str):
        rtm_binary = RTM_BINARY_MAPPING.get(rtm)
        return int(self.rtm_state & rtm_binary == rtm_binary)

    def after_start(self):
        return self.current_state().value > DemandState.NotStarted.value

    def is_completed(self):
        return self.current_state().value >= DemandState.CalculationCompleted.value

    # 是否可以开始计算demand
    def is_waiting_for_calculation(self):
        return DemandState.WaitingForCalculation.value <= self.current_state().value < DemandState.CalculationCompleted.value

    def is_waiting_to_setup(self):
        return self.current_state().value == DemandState.WaitingToSetup.value

    def is_waiting_for_rtm_setup(self):
        return self.current_state().value == DemandState.WaitingForRTMSetup.value

    def do_publish_dfa(self):
        if self.demand == TOPDOWN_DEMAND:
            try:
                self.state_control.publish()
                self.save_state()
            except StateMachine.TransitionNotAllowed as e:
                logger.error(e)
            return self.current_state()

    def get_state_update_time(self):
        return self.state_time

    def do_sellin_woi_publish(self):
        if self.demand == SELL_IN_DEMAND:
            try:
                self.state_control.publish()
                self.save_state()
            except StateMachine.TransitionNotAllowed as e:
                logger.error(e)
            return self.state_control.current_state

    def re_calculate(self):
        if self.is_completed():
            self.state_control.re_calculate()
            self.save_state()

