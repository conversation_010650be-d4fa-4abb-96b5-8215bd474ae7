import pandas as pd

from domain.demand.impl.y_value_setting import set_twos_to_dataframe


def test_set_twos_to_dataframe():
    soldto_df = pd.DataFrame({
        'fiscal_week': ['FY24Q3W10', 'FY24Q3W10', 'FY24Q3W10', 'FY24Q3W10'],
        'region': ['China mainland', 'China mainland', 'China mainland', 'China mainland'],
        'sold_to_id': ['s1', 's2', 's1', 's2'],
        'mpn': ['m1', 'm1', 'm2', 'm2'],
        'twos': [1, 2, 3, 4],
        'base': [1, 2, 3, 4],
    })
    soldto_mpn_twos = {
        "s1m1": [5, 6],
        "s2m2": [9, 9]
    }
    soldto_df = set_twos_to_dataframe(soldto_df, soldto_mpn_twos)
    a = soldto_df.loc[(soldto_df['mpn'] == 'm2') & (soldto_df['sold_to_id'] == 's2')]
    assert a.to_dict(orient='records')[0]['twos'] == 9
