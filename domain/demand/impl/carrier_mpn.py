import pandas as pd

from data.cpf_data_source import OdsFastCPFActiveSKULob
from domain.demand.entity.const import CHINA_MAINLAND
from domain.supply.entity import Lob
from util.const import ErrCode, StrRTMCarrier


def replace_rp_mpn_to_carrier_mpn(soldto_df: pd.DataFrame, replace_field: str, rtm_field: str = 'rtm'):
    # 空数据无需处理
    if soldto_df.empty:
        return
    sku_list_df = pd.DataFrame(
        OdsFastCPFActiveSKULob.get_data_by_lob_and_region(Lob.IPHONE.value, CHINA_MAINLAND))

    if sku_list_df.empty:
        raise Exception(ErrCode.DBQueryNoData, f'carrier active sku lob is empty')
    sku_list_df = sku_list_df.dropna(subset=['rp_mpn_mapped_from_carrier'])
    mpn_dict = sku_list_df.set_index('rp_mpn_mapped_from_carrier')['mpn'].to_dict()

    def replace_mpn(mpn, rtm):
        if rtm == StrRTMCarrier and mpn is not None and mpn in mpn_dict:
            return mpn_dict[mpn]
        return mpn

    soldto_df[replace_field] = soldto_df.apply(lambda row: replace_mpn(row[replace_field], row[rtm_field]), axis=1)


def replace_carrier_mpn_to_rp_mpn(ori_data_list: list[dict], replace_field: str,rtm_field: str = 'rtm'):
    sku_list_df = pd.DataFrame(
        OdsFastCPFActiveSKULob.get_data_by_lob_and_region(Lob.IPHONE.value, CHINA_MAINLAND))

    if sku_list_df.empty:
        raise Exception(ErrCode.DBQueryNoData, f'carrier active sku lob is empty')
    carrier_sku_list = sku_list_df.to_dict(orient='records')

    # carrier_sku_list
    def get_rp_mpn_from_carrier(carrier_mpn: str):
        for carrier_sku in carrier_sku_list:
            if carrier_mpn == carrier_sku["mpn"]:
                return carrier_sku["rp_mpn_mapped_from_carrier"]

    for ori_data in ori_data_list:
        if ori_data[rtm_field] != "Carrier":
            continue
        rp_mpn = get_rp_mpn_from_carrier(ori_data[replace_field])
        if rp_mpn:
            ori_data[replace_field] = rp_mpn
