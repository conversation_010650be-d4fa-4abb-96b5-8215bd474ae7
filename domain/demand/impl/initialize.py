import copy
import logging
import math

import pandas
from pandas import DataFrame

from data.cpf_data_source import OdsFastCPFTWOSiPhone
from data.mysqls.demand.demand_by_region_pool import DemandByRegionPool
from data.mysqls.demand.demand_by_soldto_pool import DemandBy<PERSON>oldtoPool
from data.mysqls.demand.demand_x_setting import IdealDemandXValueSetting
from data.mysqls.demand.demand_y_setting import IdealDemandYValueSetting
from domain.demand.entity.const import RTMS, CHINA_MAINLAND, TWOS_DEFAULT, All_RTMS, RTMS_NO_RETAIL
from domain.demand.impl.soldto_mpn_cartesian_product import CartesianProduct
from domain.supply.entity import Lob
from kit.pd import fill_nan_to_none, replace_none_from_nan


class DemandSettingInitializer:

    def __init__(self, fiscal_week):
        self.fiscal_week = fiscal_week

    def init(self):
        #  按照sold to mapping 和 sku list 的 笛卡尔积 处理twos
        lob = Lob.IPHONE.value
        region = 'China mainland'
        # 返回数据字段：fiscal_week, lob, sub_lob, nand, color, mpn, hr_lr, region, rtm, sub_rtm, sold_to_id, sold_to_name,
        sub_lob_rtm_hrlr = CartesianProduct(self.fiscal_week, CHINA_MAINLAND, Lob.IPHONE.value,
                                            RTMS_NO_RETAIL).get_result()
        # 按照 lob sub_lob rtm sub_rtm hr_lr 去重处理，保留指定的字段
        key = ['sub_lob', 'rtm', 'sub_rtm', 'hr_lr', 'mpn', 'sold_to_id']
        # sub_lob_rtm_hrlr_unique = sub_lob_rtm_hrlr.drop_duplicates(subset=key)[key]
        sub_lob_rtm_hrlr_unique = sub_lob_rtm_hrlr
        # 按照 key 左关联twos数据cw2_min
        # 查询 ods_fast_cpf_twos_iphone -->  sub_lob rtm sub_rtm hr_lr_type twos
        twos_list = OdsFastCPFTWOSiPhone().query_max_version_twos()
        twos_df = DataFrame(twos_list)
        # 需要初始化的数据集
        init_twos_df = pandas.merge(sub_lob_rtm_hrlr_unique, twos_df, how='left', on=key)
        fill_nan_to_none(init_twos_df)
        init_twos_list = init_twos_df.to_dict(orient='records')
        for init_twos in init_twos_list:
            if not init_twos['twos']:
                init_twos['twos'] = TWOS_DEFAULT
                # 16系列的如果没有先默认给0
                if init_twos['sub_lob'] in ['iPhone 16 Plus', 'iPhone 16', 'iPhone 16 Pro', 'iPhone 16 Pro Max']:
                    init_twos['twos'] = 0
            if not init_twos['hr_lr']:
                init_twos['hr_lr_type'] = 'LR'
            else:
                init_twos['hr_lr_type'] = init_twos['hr_lr']
        # y setting 表中保存的最新的财年周
        weeks = IdealDemandYValueSetting.query_distinct_fiscal_weeks()
        y_setting_list = []
        if weeks and len(weeks) > 0:
            latest_week = weeks[0]
            y_setting_list = IdealDemandYValueSetting.query_by_fiscal_week(latest_week)

        sub_rtm_hr_lr_map = dict(map(lambda x: (x['region'] + x['mpn'] + x['sold_to_id'] + x['hr_lr_type'], x), y_setting_list))

        setting_list = []
        for twos in init_twos_list:
            if twos['rtm'] not in RTMS_NO_RETAIL:
                continue
            twos['region'] = region
            setting_list.append(self.__build_demand_setting(init_setting=twos, lob=lob, last_setting=sub_rtm_hr_lr_map))
        if setting_list:
            IdealDemandYValueSetting.bulk_insert_or_update(setting_list)
            IdealDemandXValueSetting.bulk_insert_or_update(setting_list)

    def __build_demand_setting(self, init_setting: dict, lob: str, last_setting):
        # sub_lob rtm sub_rtm twos
        demand_setting = copy.deepcopy(init_setting)
        demand_setting['fiscal_week'] = self.fiscal_week
        demand_setting['lob'] = lob
        demand_setting['cw1_min'] = 0
        demand_setting['cw1_max'] = 0
        demand_setting['cw2_min'] = 0
        demand_setting['cw2_max'] = 0
        demand_setting['cw1'] = 0
        demand_setting['cw2'] = 0
        demand_setting['cw1_y'] = 0
        demand_setting['cw2_y'] = 0
        key = init_setting['region'] + init_setting['mpn'] + init_setting['sold_to_id'] + init_setting['hr_lr_type']
        if key in last_setting:
            demand_setting['cw1_y'] = last_setting[key]['cw1_y']
            demand_setting['cw2_y'] = last_setting[key]['cw2_y']
            logging.info(f"y setting demand setting:{demand_setting}")
        return demand_setting


class DemandPoolInitializer:

    def __init__(self, fiscal_week):
        self.fiscal_week = fiscal_week

    def init(self):
        soldtos_df = CartesianProduct(self.fiscal_week, CHINA_MAINLAND, Lob.IPHONE.value, RTMS_NO_RETAIL).get_result()
        # 添加twos保存 查询x setting表 twos cw1x  cw2x 数据，按照sub_lob，hr_lr 拼接
        x_values = IdealDemandXValueSetting().get_x_by_week_and_region(self.fiscal_week, CHINA_MAINLAND)
        x_values_df = pandas.DataFrame(x_values)
        # fiscal_week, region, rtm, sub_rtm, sold_to_id, sold_to_name, lob, sub_lob, nand, color, mpn, hr_lr,
        soldtos_df = pandas.merge(soldtos_df,
                                  x_values_df[['lob', 'sub_lob', 'rtm', 'sub_rtm', 'hr_lr', 'twos']],
                                  on=['lob', 'sub_lob', 'rtm', 'sub_rtm', 'hr_lr'], how='left')

        soldtos_df = fill_nan_to_none(soldtos_df)
        soldtos_dict = soldtos_df.to_dict(orient='records')
        DemandBySoldtoPool.batch_insert(soldtos_dict)

        # 将sort_result按照'region', 'mpn', 'fiscal_week', 'lob', 'sub_lob', 'nand', 'color', 'hr_lr'聚合
        region_df = soldtos_df[
            ['region', 'mpn', 'fiscal_week', 'lob', 'sub_lob', 'nand', 'color', 'hr_lr']].drop_duplicates()
        region_dict = region_df.to_dict(orient='records')
        DemandByRegionPool.batch_insert(region_dict)
