from typing import Optional

from util.conf import logger
from kit.format_numer_with_thousands_separator import (
    format_number_with_thousands_separator, round_with_dividing_by_thousand
)
from util.util import traditional_round


def calculate_percentage(dividend, divisor, decimal_places=0):
    """
    计算百分比并返回格式化后的字符串
    :param dividend: 被除数
    :param divisor: 除数
    :param decimal_places: 保留的小数位数，默认为 0
    :return: 格式化后的百分比字符串
    """
    if divisor == 0:
        return "0%"
    # 需要四舍五入, 使用绝对值
    percentage = abs(traditional_round((dividend / divisor) * 100, decimal_places))
    if decimal_places == 0:
        return f"{int(percentage)}%"
    return f"{percentage:.{decimal_places}f}%"


class PoGapChannelViewEntity:
    def __init__(self, rtm, sub_rtm, demand_cw1, open_po_cw1, po_gap_cw1, demand_cw2, open_po_cw2, po_gap_cw2):
        self.rtm = rtm
        self.sub_rtm = sub_rtm
        self.demand_cw1 = round_with_dividing_by_thousand(demand_cw1)
        self.open_po_cw1 = round_with_dividing_by_thousand(open_po_cw1)
        self.po_gap_cw1 = 0 if round_with_dividing_by_thousand(po_gap_cw1) >= 0 else round_with_dividing_by_thousand(po_gap_cw1) # 大于0的数需要处理为0
        self.demand_cw2 = round_with_dividing_by_thousand(demand_cw2)
        self.open_po_cw2 = round_with_dividing_by_thousand(open_po_cw2)
        self.po_gap_cw2 = 0 if round_with_dividing_by_thousand(po_gap_cw2) >= 0 else round_with_dividing_by_thousand(po_gap_cw2) # 大于0的数需要处理为0
        self.rank_po_gap_cw1: Optional[int] = None
        self.rank_po_gap_cw2: Optional[int] = None

    @property
    def gap_percentage_cw1(self):
        return calculate_percentage(self.po_gap_cw1, self.demand_cw1)
    
    @property
    def gap_percentage_cw2(self):
        return calculate_percentage(self.po_gap_cw2, self.demand_cw2)
    
    def set_rank(self, rank_field, new_rank):
        # 指定字段进行排名
        if rank_field == 'po_gap_cw1':
            self.rank_po_gap_cw1 = new_rank
        elif rank_field == 'po_gap_cw2':
            self.rank_po_gap_cw2 = new_rank
    
    def to_dict(self):
        return {
            "rtm": self.rtm,
            "sub_rtm": self.sub_rtm,
            "demand_cw1": format_number_with_thousands_separator(self.demand_cw1),
            "open_po_cw1": format_number_with_thousands_separator(self.open_po_cw1),
            "po_gap_cw1": format_number_with_thousands_separator(self.po_gap_cw1),
            "demand_cw2": format_number_with_thousands_separator(self.demand_cw2),
            "open_po_cw2": format_number_with_thousands_separator(self.open_po_cw2),
            "po_gap_cw2": format_number_with_thousands_separator(self.po_gap_cw2),
            "rank_po_gap_cw1": self.rank_po_gap_cw1,
            "rank_po_gap_cw2": self.rank_po_gap_cw2,
            "gap_percentage_cw1": self.gap_percentage_cw1,
            "gap_percentage_cw2": self.gap_percentage_cw2,
        }


class PoGapSoldtoViewEntity:
    def __init__(self, rtm, sub_rtm, sold_to_id, sold_to_name, demand_cw1, open_po_cw1, po_gap_cw1, demand_cw2, open_po_cw2, po_gap_cw2):
        self.rtm = rtm
        self.sub_rtm = sub_rtm
        self.sold_to_id = sold_to_id
        self.sold_to_name = sold_to_name
        self.demand_cw1 = round_with_dividing_by_thousand(demand_cw1)
        self.open_po_cw1 = round_with_dividing_by_thousand(open_po_cw1)
        self.po_gap_cw1 = 0 if round_with_dividing_by_thousand(po_gap_cw1) >= 0 else round_with_dividing_by_thousand(po_gap_cw1) # 大于0的数需要处理为0
        self.demand_cw2 = round_with_dividing_by_thousand(demand_cw2)
        self.open_po_cw2 = round_with_dividing_by_thousand(open_po_cw2)
        self.po_gap_cw2 = 0 if round_with_dividing_by_thousand(po_gap_cw2) >= 0 else round_with_dividing_by_thousand(po_gap_cw2) # 大于0的数需要处理为0
        self.rank_po_gap_cw1: Optional[int] = None
        self.rank_po_gap_cw2: Optional[int] = None

    @property
    def gap_percentage_cw1(self):
        return calculate_percentage(self.po_gap_cw1, self.demand_cw1)
    
    @property
    def gap_percentage_cw2(self):
        return calculate_percentage(self.po_gap_cw2, self.demand_cw2)
    
    def set_rank(self, rank_field, new_rank):
        # 指定字段进行排名
        if rank_field == 'po_gap_cw1':
            self.rank_po_gap_cw1 = new_rank
        elif rank_field == 'po_gap_cw2':
            self.rank_po_gap_cw2 = new_rank
    
    def to_dict(self):
        return {
            "rtm": self.rtm,
            "sub_rtm": self.sub_rtm,
            "sold_to_id": self.sold_to_id,
            "sold_to_name": self.sold_to_name,
            "demand_cw1": format_number_with_thousands_separator(self.demand_cw1),
            "open_po_cw1": format_number_with_thousands_separator(self.open_po_cw1),
            "po_gap_cw1": format_number_with_thousands_separator(self.po_gap_cw1),
            "demand_cw2": format_number_with_thousands_separator(self.demand_cw2),
            "open_po_cw2": format_number_with_thousands_separator(self.open_po_cw2),
            "po_gap_cw2": format_number_with_thousands_separator(self.po_gap_cw2),
            "rank_po_gap_cw1": self.rank_po_gap_cw1,
            "rank_po_gap_cw2": self.rank_po_gap_cw2,
            "gap_percentage_cw1": self.gap_percentage_cw1,
            "gap_percentage_cw2": self.gap_percentage_cw2,
        }
    

class PoGapProductViewEntity:
    def __init__(self, sub_lob,  demand_cw1, open_po_cw1, po_gap_cw1, demand_cw2, open_po_cw2, po_gap_cw2):
        self.sub_lob: str = sub_lob
        self.demand_cw1: int = round_with_dividing_by_thousand(demand_cw1)
        self.open_po_cw1: int = round_with_dividing_by_thousand(open_po_cw1)
        self.po_gap_cw1: int =0 if round_with_dividing_by_thousand(po_gap_cw1) >= 0 else round_with_dividing_by_thousand(po_gap_cw1) # 大于0的数需要处理为0
        self.demand_cw2: int = round_with_dividing_by_thousand(demand_cw2)
        self.open_po_cw2: int = round_with_dividing_by_thousand(open_po_cw2)
        self.po_gap_cw2: int =  0 if round_with_dividing_by_thousand(po_gap_cw2) >= 0 else round_with_dividing_by_thousand(po_gap_cw2) # 大于0的数需要处理为0
        self.rank_po_gap_cw1: Optional[int] = None
        self.rank_po_gap_cw2: Optional[int] = None
    
    @property
    def gap_percentage_cw1(self):
        return calculate_percentage(self.po_gap_cw1, self.demand_cw1)
    
    @property
    def gap_percentage_cw2(self):
        return calculate_percentage(self.po_gap_cw2, self.demand_cw2)
    
    def set_rank(self, rank_field, new_rank):
        # 指定字段进行排名
        if rank_field == 'po_gap_cw1':
            self.rank_po_gap_cw1 = new_rank
        elif rank_field == 'po_gap_cw2':
            self.rank_po_gap_cw2 = new_rank
    
    def to_dict(self):
        return {
            "sub_lob": self.sub_lob,
            "demand_cw1": format_number_with_thousands_separator(self.demand_cw1),
            "open_po_cw1": format_number_with_thousands_separator(self.open_po_cw1),
            "po_gap_cw1": format_number_with_thousands_separator(self.po_gap_cw1),
            "demand_cw2": format_number_with_thousands_separator(self.demand_cw2),
            "open_po_cw2": format_number_with_thousands_separator(self.open_po_cw2),
            "po_gap_cw2": format_number_with_thousands_separator(self.po_gap_cw2),
            "rank_po_gap_cw1": self.rank_po_gap_cw1,
            "rank_po_gap_cw2": self.rank_po_gap_cw2,
            "gap_percentage_cw1": self.gap_percentage_cw1,
            "gap_percentage_cw2": self.gap_percentage_cw2,
        }


class PoGapEntity:
    def __init__(self, channel_view, product_view, summary, latest_refresh_time):
        self.channel_view: list[PoGapChannelViewEntity] = channel_view
        self.product_view: list[PoGapProductViewEntity] = product_view
        self.summary: list[dict] = summary
        self.latest_refresh_time: str = latest_refresh_time
    
    def set_summary(self, new_summary):
        self.summary = new_summary
    
    def set_latest_refresh_time(self, new_refresh_time):
        self.latest_refresh_time = new_refresh_time
    
    def to_dict(self):
        return {
            "channel_view": [item.to_dict() for item in self.channel_view],
            "product_view": [item.to_dict() for item in self.product_view],
            "summary": self.summary,
            "latest_refresh_time": self.latest_refresh_time
        }


def set_ranks(entities, field_name, ascending=False):
    # 根据指定字段对实体列表进行排序
    sorted_entities = sorted(entities, key=lambda x: getattr(x, field_name), reverse=not ascending)

    current_rank = 1
    prev_value = None
    for i, entity in enumerate(sorted_entities):
        current_value = getattr(entity, field_name)
        if prev_value is not None and current_value != prev_value:
            # 如果当前值与前一个值不同，更新排名为下一个顺序值
            current_rank = i + 1
        entity.set_rank(current_rank)
        prev_value = current_value

    return entities


def rank_special_items(entities, filter_func, rank_field, unique_keys):
    """
    对列表中满足特定条件的元素进行排名，不改变原列表顺序
    :param entities: 包含 PoGapChannelViewEntity 对象的列表
    :param filter_func: 筛选满足条件元素的函数
    :param rank_field: 用于排名的字段名
    :return: 排名后的原列表
    """
    # 筛选出满足条件的元素
    special_entities = [entity for entity in entities if filter_func(entity)]
    # 对筛选出的元素按指定字段排序，使用绝对值进行排序
    sorted_special_entities = sorted(special_entities, key=lambda x: abs(getattr(x, rank_field)), reverse=True)

    rank = 1
    top1 = []
    prev_value = None
    rank_dict = {}
    for i, entity in enumerate(sorted_special_entities):
        current_value = abs(getattr(entity, rank_field))
        # 特殊处理，如果值为0则不进行排名
        if current_value == 0:
            continue
        if prev_value is not None and current_value < prev_value:
            rank = i + 1
        # 构建唯一键
        unique_key = tuple(getattr(entity, key) for key in unique_keys)
        rank_dict[unique_key] = rank
        if rank == 1:
            top1.append(entity)
        prev_value = current_value

    # 为原列表中的元素设置排名
    for entity in entities:
        # 构建唯一键
        unique_key = tuple(getattr(entity, key) for key in unique_keys)
        if unique_key in rank_dict:
            entity.set_rank(rank_field, rank_dict[unique_key])
        else:
            entity.set_rank(rank_field, 0)  # 不满足条件的元素排名设为 0

    # 还需要返回top1的entity list
    return entities, top1


class PoGapRTMEntity:
    def __init__(self, channel_view, product_view, summary, latest_refresh_time):
        self.channel_view: list[PoGapSoldtoViewEntity] = channel_view
        self.product_view: list[PoGapProductViewEntity] = product_view
        self.summary: list[dict] = summary
        self.latest_refresh_time: str = latest_refresh_time
    
    def set_summary(self, new_summary):
        self.summary = new_summary
    
    def set_latest_refresh_time(self, new_refresh_time):
        self.latest_refresh_time = new_refresh_time
    
    def to_dict(self):
        return {
            "channel_view": [item.to_dict() for item in self.channel_view],
            "product_view": [item.to_dict() for item in self.product_view],
            "summary": self.summary,
            "latest_refresh_time": self.latest_refresh_time
        }
