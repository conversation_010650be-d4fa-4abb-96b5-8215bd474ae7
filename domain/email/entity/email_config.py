import threading
from typing import Optional
import json

from util.redis_pool import GetRedis
from util.conf import logger


class LimitFrequency:
    def __init__(self, redis_key: str, 
                 sending_quantity: int,
                 interval_seconds: int):
        # key 能否自动生成防止重复？
        self.redis_key = redis_key # 邮件标识，redis中的唯一key
        self.sending_quantity = sending_quantity # 在间隔时间内容的发送次数
        self.interval_seconds = interval_seconds # 间隔时间，比如每天(86400s)发送1次
        self.lock = threading.Lock()
    
    def consume(self) -> bool:
        redis_cli = GetRedis()
        # 尝试获取现有的令牌数量
        tokens = redis_cli.get(self.redis_key)
        if tokens is None:
            # 如果key不存在，初始化令牌数量并设置过期时间
            redis_cli.set(self.redis_key, self.sending_quantity - 1, ex=self.interval_seconds)
            count = redis_cli.get(self.redis_key)
            logger.info(f'初始化{self.redis_key}的令牌数量为{count}')
            return True
        else:
            tokens = int(tokens)
            if tokens > 0:
                # 如果令牌数量大于0，则消耗一个令牌
                redis_cli.decr(self.redis_key)
                count = redis_cli.get(self.redis_key)
                logger.info(f"正在消耗令牌， 剩余令牌数为 {count}")
                return True
            else:
                # 如果令牌数量不足，返回False
                logger.info(f'令牌数量不足，{self.redis_key}的令牌数量为{tokens}')
                return False
    
    def consume_with_lock(self) -> bool:
        redis_cli = GetRedis()
        with self.lock:
            # 尝试获取现有的令牌数量
            tokens = redis_cli.get(self.redis_key)
            if tokens is None:
                # 如果key不存在，初始化令牌数量并设置过期时间
                redis_cli.set(self.redis_key, self.sending_quantity - 1, ex=self.interval_seconds)
                count = redis_cli.get(self.redis_key)
                logger.info(f'初始化{self.redis_key}的令牌数量为{count}')
                return True
            else:
                tokens = int(tokens)
                if tokens > 0:
                    # 如果令牌数量大于0，则消耗一个令牌
                    redis_cli.decr(self.redis_key)
                    count = redis_cli.get(self.redis_key)
                    logger.info(f"正在消耗令牌， 剩余令牌数为 {count}")
                    return True
                else:
                    # 如果令牌数量不足，返回False
                    logger.info(f'令牌数量不足，{self.redis_key}的令牌数量为{tokens}')
                    return False
        

class EmailConfig:
    def __init__(self, cmd: str,
                 content_type: str, subject: str,
                 content: str, params: str, attachments: str,
                 recipients: str, cc: str, bcc: str,
                 is_base_template: bool, frequency: Optional[str]) -> None:
        self.cmd = cmd
        self.content_type = content_type
        self.subject = subject
        self.content = content
        self.params = params
        self.attachments = attachments
        self.recipients = recipients
        self.cc = cc
        self.bcc = bcc
        self.is_base_template = is_base_template
        self.frequency = frequency
        
    def __repr__(self) -> str:
        return f'cmd: {self.cmd}, content-type:{self.content_type}, subject:{self.subject}, params:{self.params}, attachments:{self.attachments}, recipients:{self.recipients}, cc:{self.cc}, bcc:{self.bcc}, is_base_template:{self.is_base_template}, frequency:{self.frequency}'
    
    def is_limit_frequency(self):
        try:
            ret =json.loads(self.frequency)
            if isinstance(ret, dict):
                return True
            return False
        except Exception:
            return False
    
    def limit_frequency(self):
        if self.is_limit_frequency():
            frequency: dict = json.loads(self.frequency)
            
            return LimitFrequency(
                frequency.get("redis_key"),
                frequency.get("sending_quantity"),
                frequency.get("interval_seconds")
            )
    