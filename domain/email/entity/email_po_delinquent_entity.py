from typing import Optional

from kit.format_numer_with_thousands_separator import (
    format_number_with_thousands_separator, round_with_dividing_by_thousand
)


class PoDelinquentChannelViewEntity:
    def __init__(self, rtm, sub_rtm, demand_next_2_weeks, open_po, open_po_condition1, open_po_condition2, open_po_condition3, open_po_condition4, open_po_condition5):
        self.rtm: str= rtm
        self.sub_rtm: str = sub_rtm
        self.demand_next_2_weeks: int = round_with_dividing_by_thousand(demand_next_2_weeks)
        self.open_po: int = round_with_dividing_by_thousand(open_po)
        self.open_po_condition1: int = round_with_dividing_by_thousand(open_po_condition1)
        self.open_po_condition2: int = round_with_dividing_by_thousand(open_po_condition2)
        self.open_po_condition3: int = round_with_dividing_by_thousand(open_po_condition3)
        self.open_po_condition4: int = round_with_dividing_by_thousand(open_po_condition4)
        self.open_po_condition5: int = round_with_dividing_by_thousand(open_po_condition5)
        self.rank: Optional[int] = None
    
    def set_rank(self, new_rank):
        self.rank = new_rank

    @property
    def rank_data(self):
        return self.open_po_condition4 + self.open_po_condition5
    
    def to_dict(self):
        return {
            "rtm": self.rtm,
            "sub_rtm": self.sub_rtm,
            "demand_next_2_weeks": format_number_with_thousands_separator(self.demand_next_2_weeks),
            "open_po": format_number_with_thousands_separator(self.open_po),
            "open_po_condition1": format_number_with_thousands_separator(self.open_po_condition1),
            "open_po_condition2": format_number_with_thousands_separator(self.open_po_condition2),
            "open_po_condition3": format_number_with_thousands_separator(self.open_po_condition3),
            "open_po_condition4": format_number_with_thousands_separator(self.open_po_condition4),
            "open_po_condition5": format_number_with_thousands_separator(self.open_po_condition5),
            "rank_data": format_number_with_thousands_separator(self.rank_data),
            "rank": self.rank
        }


class PoDelinquentProductViewEntity:
    def __init__(self, sub_lob, demand_next_2_weeks, open_po, open_po_condition1, open_po_condition2, open_po_condition3, open_po_condition4, open_po_condition5):
        self.sub_lob: str = sub_lob
        self.demand_next_2_weeks: int = round_with_dividing_by_thousand(demand_next_2_weeks)
        self.open_po: int = round_with_dividing_by_thousand(open_po)
        self.open_po_condition1: int = round_with_dividing_by_thousand(open_po_condition1)
        self.open_po_condition2: int = round_with_dividing_by_thousand(open_po_condition2)
        self.open_po_condition3: int = round_with_dividing_by_thousand(open_po_condition3)
        self.open_po_condition4: int = round_with_dividing_by_thousand(open_po_condition4)
        self.open_po_condition5: int = round_with_dividing_by_thousand(open_po_condition5)
        self.rank: Optional[int] = None
    
    def set_rank(self, new_rank):
        self.rank = new_rank

    @property
    def rank_data(self):
        return self.open_po_condition4 + self.open_po_condition5
    
    def to_dict(self):
        return {
            "sub_lob": self.sub_lob,
            "demand_next_2_weeks": format_number_with_thousands_separator(self.demand_next_2_weeks),
            "open_po": format_number_with_thousands_separator(self.open_po),
            "open_po_condition1": format_number_with_thousands_separator(self.open_po_condition1),
            "open_po_condition2": format_number_with_thousands_separator(self.open_po_condition2),
            "open_po_condition3": format_number_with_thousands_separator(self.open_po_condition3),
            "open_po_condition4": format_number_with_thousands_separator(self.open_po_condition4),
            "open_po_condition5": format_number_with_thousands_separator(self.open_po_condition5),
            "rank_data": format_number_with_thousands_separator(self.rank_data),
            "rank": self.rank
        }


class PoDelinquentEntity:
    def __init__(self, channel_view, product_view, summary, latest_refresh_time):
        self.channel_view: list[PoDelinquentChannelViewEntity] = channel_view
        self.product_view: list[PoDelinquentProductViewEntity] = product_view
        self.summary: dict = summary
        self.latest_refresh_time: str = latest_refresh_time
    
    def set_summary(self, new_summary):
        self.summary = new_summary
    
    def set_latest_refresh_time(self, new_refresh_time):
        self.latest_refresh_time = new_refresh_time

    def to_dict(self):
        return {
            "channel_view": [item.to_dict() for item in self.channel_view],
            "product_view": [item.to_dict() for item in self.product_view],
            "summary": self.summary,
            "latest_refresh_time": self.latest_refresh_time
        }


def rank_special_items(entities, filter_func, rank_field, unique_keys):
    """
    对列表中满足特定条件的元素进行排名，不改变原列表顺序
    :param entities: 包含 PoGapChannelViewEntity 对象的列表
    :param filter_func: 筛选满足条件元素的函数
    :param rank_field: 用于排名的字段名
    :return: 排名后的原列表
    """
    # 筛选出满足条件的元素
    special_entities = [entity for entity in entities if filter_func(entity)]
    # 对筛选出的元素按指定字段排序
    sorted_special_entities = sorted(special_entities, key=lambda x: getattr(x, rank_field), reverse=True)

    rank = 1
    top1 = []
    prev_value = None
    rank_dict = {}
    for i, entity in enumerate(sorted_special_entities):
        current_value = getattr(entity, rank_field)
        # 特殊处理，如果值为0则不进行排名
        if current_value == 0:
            continue
        if prev_value is not None and current_value < prev_value:
            rank = i + 1
        # 构建唯一键
        unique_key = tuple(getattr(entity, key) for key in unique_keys)
        rank_dict[unique_key] = rank
        if rank == 1:
            top1.append(entity)
        prev_value = current_value

    # 为原列表中的元素设置排名
    for entity in entities:
        # 构建唯一键
        unique_key = tuple(getattr(entity, key) for key in unique_keys)
        if unique_key in rank_dict:
            entity.set_rank(rank_dict[unique_key])
        else:
            entity.set_rank(0)  # 不满足条件的元素排名设为 0

    # 还需要返回top1的entity list
    return entities, top1

# RTM 版本
class PoDelinquentRTMSoldtoViewEntity:
    '''sold_to 对应的信息'''
    def __init__(self, rtm, sub_rtm,sold_to_id, sold_to_name, demand_next_2_weeks, open_po, open_po_condition1, open_po_condition2, open_po_condition3, open_po_condition4, open_po_condition5):
        self.rtm: str= rtm
        self.sub_rtm: str = sub_rtm
        self.sold_to_id: str = sold_to_id
        self.sold_to_name: str = sold_to_name
        self.demand_next_2_weeks: int = round_with_dividing_by_thousand(demand_next_2_weeks)
        self.open_po: int = round_with_dividing_by_thousand(open_po)
        self.open_po_condition1: int = round_with_dividing_by_thousand(open_po_condition1)
        self.open_po_condition2: int = round_with_dividing_by_thousand(open_po_condition2)
        self.open_po_condition3: int = round_with_dividing_by_thousand(open_po_condition3)
        self.open_po_condition4: int = round_with_dividing_by_thousand(open_po_condition4)
        self.open_po_condition5: int = round_with_dividing_by_thousand(open_po_condition5)
        self.rank: Optional[int] = None
    
    def set_rank(self, new_rank):
        self.rank = new_rank

    @property
    def rank_data(self):
        return self.open_po_condition4 + self.open_po_condition5
    
    def to_dict(self):
        return {
            "rtm": self.rtm,
            "sub_rtm": self.sub_rtm,
            "sold_to_id": self.sold_to_id,
            "sold_to_name": self.sold_to_name,
            "demand_next_2_weeks": format_number_with_thousands_separator(self.demand_next_2_weeks),
            "open_po": format_number_with_thousands_separator(self.open_po),
            "open_po_condition1": format_number_with_thousands_separator(self.open_po_condition1),
            "open_po_condition2": format_number_with_thousands_separator(self.open_po_condition2),
            "open_po_condition3": format_number_with_thousands_separator(self.open_po_condition3),
            "open_po_condition4": format_number_with_thousands_separator(self.open_po_condition4),
            "open_po_condition5": format_number_with_thousands_separator(self.open_po_condition5),
            "rank_data": format_number_with_thousands_separator(self.rank_data),
            "rank": self.rank
        }


class PoDelinquentRTMEntity:
    def __init__(self, sold_to_view, product_view, summary, latest_refresh_time):
        self.sold_to_view: list[PoDelinquentRTMSoldtoViewEntity] = sold_to_view
        self.product_view: list[PoDelinquentProductViewEntity] = product_view
        self.summary: dict = summary
        self.latest_refresh_time: str = latest_refresh_time
    
    def set_summary(self, new_summary):
        self.summary = new_summary
    
    def set_latest_refresh_time(self, new_refresh_time):
        self.latest_refresh_time = new_refresh_time

    def to_dict(self):
        return {
            "sold_to_view": [item.to_dict() for item in self.sold_to_view],
            "product_view": [item.to_dict() for item in self.product_view],
            "summary": self.summary,
            "latest_refresh_time": self.latest_refresh_time
        }
