<!doctype html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">

    <head>
        <title>
        </title>
        <!--[if !mso]><!-->
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <!--<![endif]-->
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <style>
            #outlook a {
                padding: 0;
            }

            body {
                margin: 0;
                padding: 0;
                -webkit-text-size-adjust: 100%;
                -ms-text-size-adjust: 100%;
            }

            table,
            td {
                border-collapse: collapse;
                mso-table-lspace: 0pt;
                mso-table-rspace: 0pt;
            }

            img {
                border: 0;
                height: auto;
                line-height: 100%;
                outline: none;
                text-decoration: none;
                -ms-interpolation-mode: bicubic;
            }

            p {
                display: block;
                margin: 13px 0;
            }

        </style>
        <!--[if mso]>
        <noscript>
            <xml>
                <o:OfficeDocumentSettings>
                    <o:AllowPNG/>
                    <o:PixelsPerInch>96</o:PixelsPerInch>
                </o:OfficeDocumentSettings>
            </xml>
        </noscript>
        <![endif]-->
        <!--[if lte mso 11]>
        <style>
            .mj-outlook-group-fix { width:100% !important; }
        </style>
        <![endif]-->
        <style>
            @media only screen and (min-width:480px) {
                .mj-column-per-100 {
                    width: 100% !important;
                    max-width: 100%;
                }
            }
            @media only screen and (min-width:600px) {
                .mj-column-per-100 {
                    width: 100% !important;
                    max-width: 100%;
                }
            }

        </style>
        <style media="screen and (min-width:480px)">
            .moz-text-html .mj-column-per-100 {
                width: 100% !important;
                max-width: 100%;
            }

        </style>
        <style media="screen and (min-width:600px)">
            .moz-text-html .mj-column-per-100 {
                width: 100% !important;
                max-width: 100%;
            }

        </style>
        <style>
        </style>
        <style>
            /*.tableWrapper>table {*/
            /*    padding: 16px;*/
            /*}*/

            .header {
                border-radius: 12px;
                position: relative;
            }

            .header .apple-icon {
                position: absolute;
                right: 0;
                bottom: 0;
                width: 90px;
                height: 60px;
                display: block;
                z-index: 0;
            }

            .sub-title {
                display: flex;
                justify-content: space-between;
                font-size: 12px;
            }

            .sub-title p {
                margin: 6px 0 0;
            }

            .title {
                position: relative;
                left: 2px;
            }

            .date {
                font-size: 10px;
                padding: 0 2px;
                line-height: 12px;
            }

            .timeTip {
                font-size: 10px;
                font-weight: 400;
                line-height: 16px;
                padding-top: 4px;
            }
            .timeTip .timeTipDiv {
                text-indent: -22px;
                margin-left: 22px;
            }
            .timeTip.text {
                padding-left: 22px;
            }
            .timeTip.text.last {
                padding-top: 8px;
            }
            .timeTip.text .timeTipDiv {
                text-indent: -10px;
                margin-left: 10px;
            }
            .timeTip.text .timeTipDiv {
                padding-top: 4px;
            }
            .timeTip svg {
                vertical-align: text-top;
            }
            b,.bold {
                font-weight: 500 !important;
            }
            .red {
                color: #F63F54 !important;
            }
            .yellow {
                color: #ff9500 !important;
            }
            .green {
                color: #34C759 !important;
            }
            .tableTitle {
                font-size: 14px;
                font-weight: 500;
                padding: 24px 8px 8px;
                line-height: 20px;
            }

            .tableTitle.tableOver {
                padding-top: 8px;
            }

            table {
                border-collapse: separate;
            }

            .wrap-box-div {
                padding: 24px 0 0 0;
            }

            .type-btn-title h2 {
                font-weight: 500;
                margin: 0 0 4px;
                font-size: 16px;
                line-height: 20px;
            }

            .type-btn-title p {
                font-size: 12px;
            }

            .qtd-perf th,
            .qtd-perf td {
                font-weight: 400;
                border-bottom-width: 1px;
                border-bottom-style: solid;
            }

            .qtd-perf th {
                font-size: 8px;
                white-space: nowrap;
            }
            .first-tr th:first-child {
                text-align: left;
            }
            .two-tr th {
                text-align: left;
                vertical-align: top;
            }

            .first-tr th {
                border-top-width: 1px;
                border-top-style: solid;
                border-right-width: 1px;
                border-right-style: solid;
            }

            .first-tr th:first-child {
                border-top-left-radius: 12px;
            }

            .first-tr th:last-child {
                border-top-right-radius: 12px;
                border-right-width: 1px;
                border-right-style: solid;
            }

            .qtd-perf .two-tr th:last-child,
            .qtd-perf tr td:last-child {
                border-right-width: 1px;
                border-right-style: solid;
            }

            .qtd-perf .first-tr th:first-child,
            .qtd-perf tr td:first-child {
                border-left-width: 1px;
                border-left-style: solid;
            }

            .qtd-perf tr td {
                vertical-align: top;
            }

            .qtd-perf tr:last-child td {
                border-bottom-width: 1px;
                border-bottom-style: solid;
            }

            .qtd-perf tr:last-child td:first-child {
                border-bottom-left-radius: 12px;
            }

            .qtd-perf tr:last-child td:last-child {
                border-bottom-right-radius: 12px;
            }

            .qtd-perf .two-tr th:nth-child(4),
            .qtd-perf .two-tr th:nth-child(10),
            .qtd-perf tr td:nth-child(1),
            .qtd-perf tr td:nth-child(5),
            .qtd-perf tr td:nth-child(10) {
                border-right-width: 1px;
                border-right-style: solid;
            }

            .qtd-perf .two:not(:has(+ .two)):has(+ .three) td {
                padding-bottom: 0;
            }

            .qtd-perf .three:not(:has(+ .three)):has(+ .two) td {
                border-bottom-width: 1px;
                border-bottom-style: solid;
            }
            .qtd-perf .one td {
                font-weight: 500;
            }
            .qtd-perf .two td,
            .qtd-perf .three td {
                border-bottom: none;
            }

            .qtd-perf .two:first-child td,
            .qtd-perf .three:first-child td {
                border-bottom-width: 1px;
                border-bottom-style: solid;
            }

            .qtd-perf .two:has(+ .two) td {
                border-bottom-width: 1px;
                border-bottom-style: solid;
            }
            /*.qtd-perf .first-tr th:first-child {*/
            /*    width: 22%;*/
            /*}*/
            .tip {
                color: #aeaeb2;
            }
            .footer {
                border-radius: 12px;
            }

            .footer-hint {
                font-size: 10px;
                line-height: 16px;
            }

            .footer-hint-text {
                text-decoration: none;
            }

            .hint-text {
                text-align: center;
                font-size: 10px;
                line-height: 14px;
                font-weight: 400;
            }

            .section-btn {
                display: inline-block;
                width: 140px;
                height: 48px;
                text-align: center;
                border-radius: 24px;
                line-height: 48px;
                font-size: 14px;
                font-weight: 500;
                cursor: pointer;
                margin: 0 auto 32px;
                text-decoration: none;
                overflow: hidden;
                outline: none;
                background-color: rgba(254, 254, 254, 1);
                color: #1C1C1E;
            }
            .section-btn:focus {
                outline: none;
            }

            @media (prefers-color-scheme: dark) {
                .tableWrapper>table {
                    background: #232425;
                }

                .header {
                    background-color: rgba(254, 254, 254, 1);
                    color: #1C1C1E;
                }

                .header .apple-icon {
                    background-image: url(data:image/png;base64,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);
                }

                .apple-icon,
                .title,
                .sub-title {
                    color: #1C1C1E;
                }

                .icon {
                    color: #1C1C1E;
                }

                .date {
                    color: #6E6E73;
                }

                .type-btn-title h2,
                .type-btn-title p {
                    color: #fff;
                }

                .timeTip {
                    color: #aeaeb2;
                }

                .tableTitle {
                    color: #fff;
                }
                b.bold {
                    color: #fff;
                }
                .first-tr,
                .two-tr {
                    background: #313136;
                }

                .qtd-perf th,
                .qtd-perf td {
                    border-color: #6e6e73;
                }
                .qtd-perf th,
                .qtd-perf td,
                .qtd-perf tr:last-child td,
                .qtd-perf.productView .two:last-child td,
                .qtd-perf.channelView .three:last-child td {
                    border-color: #6e6e73;
                }
                .qtd-perf .two-tr th:nth-child(3),
                .qtd-perf .two-tr th:nth-child(4),
                .qtd-perf .two-tr th:nth-last-child(-n+2) {
                    background: #373E46;
                }
                .qtd-perf td:nth-child(4),
                .qtd-perf td:nth-child(5),
                .qtd-perf td:nth-last-child(-n+2) {
                    background: #22272D;
                }
                .qtd-perf td,
                .qtd-perf .two td.bold,
                .qtd-perf .three td.bold {
                    color: #fff;
                }

                .qtd-perf th {
                    color: #aeaeb2;
                    background: #313136;
                }

                .qtd-perf .two td,
                .qtd-perf .three td {
                    color: #aeaeb2;
                    border-color: #6e6e73;
                }

                .footer {
                    background-color: #34343b;
                }

                .footer-hint {
                    color: #d1d1d6;
                }

                .footer-hint-text {
                    color: #4F78E3;
                }

                .hint-text {
                    color: #8e8e93;
                }

                .section-btn {
                    background-color: #fefefe;
                    color: #1C1C1E;
                }
            }

            @media (prefers-color-scheme: light) {
                .header {
                    background-color: #1C1C1E;
                }

                .header .apple-icon {
                    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAHUAAACPCAYAAADEDZTDAAAABGdBTUEAALGPC/xhBQAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAdaADAAQAAAABAAAAjwAAAADDgFiLAAAHZUlEQVR4Ae2cCZPURBiGWTkEwRMURESOktIq//9PsSy1FAS1FE9uudbnmZpMLezMJJPJMUner+rbnZ10+njefJ3uTmePHIk1SmB/f/8o/hb+XqMZb5DZsQ3SJukSAgrI1+fxN/F9/AX+BD/LsXt7e3sv+dypRdQauBHrOKd9ip/AH+E/I95Tfi+MNHv8odCPF1929CGibgB6HpWKafT9hJD/rTndYyfxiLoGUm+HENOIvIor1PeIaRdbxeyOO7dE6hrk8y70M5Io6g+I+WxN8tcPybbzKLUSEfV1KeZ/z7vaa/x5CzHvr0i27mvvu5tcBOvy2uhYRF2CC0Ev8PXb+NcIWnf0epJzHQV3bm90XuKOF4ig3jvfQJDvthC011YmUg/gR9Ab/HkXMf8+8HXdj05pYn0SQNDP8UZWgcjnDH6pz/ZMvmwEuIJ/0BSIeX7OUXuxyd9TEeAc5F/Q5f7VoAKnyK+XQZJtmLSoCGo0nUOA200JSp6uBT9sKr/kswEB4O/hX+FHNzitNCn53cCdo/ZmU45Upy4/EqVVl/xKRUJMF/Bfkmcviw5FBScpKvBd9jsG/Ka7SZcUbxVw+/o9SVGBPYvSJqFzoXh/dsDVa5TapsmJCvzTtPtpC/B3IkonKSqNbhw+F4pz3EdcKM+F2rdNKlKBP5tuAL/uIv0hvcjTpdYL5NnYtOhQIfliNQEEcCmw0fVu8vsSd+C1MzaZSAW8C+xHm+wiyfMT8vQBwCv7k/pWdzKiAtrF+iaevsw0Q9AP+XACQf+YfZEf3RNABLveRlaPyOcCfqX7VlQrsdH7S7Uie0vlg++tV48Q8yItsBu/2VtLSgqeUvdbgqL8MIK6PRQ9d3ukO4lIRYytNoFxvmu613AHRTt/D52EqIjhlGPdxmsOLzcEdQeDS4DfIujW3ffyUpr9diqiGqkbrfYgpsuJl/FfEPNOs9jbzW0qojp2qLSKhJhuDf0Yd+fCNwi6z+9B2VRENUpXrvogpMd8c+0U/gB3e+jgxKTeM5uKqD43vYZ4NlqB7Y4VshDaFaHfEbKX1yQou1Fz6WwSNo9GR7EuQPjMs43HbzvBcjKitkmbC0aORr8XjO7925HyM6K/0r2ctI1ZL6ICwW7/Xdz12LJbgH2mgxbniL13j9Tdx3cOphTRuukKZ7euK6ZcbZcCH1zgMa1d/RPa0toW0s5EBcb7NOYjXLPx/+L/0Dg/r7U5SPfnClQwLswrsp9bNcpWFOtu2Yrnm+P3q9SbdIeM/LyPO+8t7ueK+7DJtrQq6lwMH0951SqEg5GtuyPyFbJPSczrV/JsegPZEcp4h7z12UVEGYrZuFGOAhcX6wPK2XqPUyuiUtFi4m53eYeKlkZjHVqUY/fmVMTynIr8Rlm1Lxry8+KzR7BrvaeTX+u9AeV4EanFGdw6GLm1n9E2KioV84q7jNul+D8RagPm/I2MsgVSRK9dc+XI4lwj0nu8UfIn524dLeRT2+YcvbDs5jdm2JioVOQKlTByblKR3tZIqYd1OIt7gdlD+I6MA5MFnHkaFxoUUvNNcQF2EpWzEkt+UEe1sQdy2+lGA8StRaVw4VzHb1O4g5+dMepmV2YUet9ywFOIptj2Jp11r5RVy+ZtsP6NDqZWVoYCL+LuKDg4bF+ZPgfqEZAv7n9Ra5czBVzFL9SrZs6qQwDeJ1sTloyNTu9bsY4JwP0EvvVtc1FtM8Pd51oMMBbH8qE7AvA/rhaNlEhGvnvp1CHWMwF0cJS/nZHJJdx5YGwHCKCFvebSaHXIX2qc7AtAbrG8W5o4CTohsNWcGkEdcd3opKYppBsCCPoF3u78qJumpBQJIKavF/hEJDYgAisjEDFdUD5N3+0js9gYCCDqdbzSQGoM7R1TG5bOdRDT7Rr7RGmd/3M7Jj7jaQuiup1y6RxoPK0cb0sO3VMR08c8j7eaB42X1zBbhqiX8aXd8jBbNL1avxKpiOnA6DlR2tvOhelJ0HKLEfU87lQmNmACr0Qq7ThOlPa66WrALHem6gtRiVA/t7KVc2daO5GKLESlvc5N3esaGziBg6L6L8Ir75UdeLtHXf2DohbbJ0fd4Ck0bibqfCqTAdJIFC8i1Tew3NwcGwGBQlTnprVfyBkBh1E1oRC10f+uOSpCA2xMIWoGSQMUb1WVI+oqMgP+PqIOWLxVVS9EXby7uSphvh8OAV+RU9iIOhzNSmuqoG5biailqIaTIN3vcLSqXNNEamVUw0lYRGrmqcPRrLSmihpBSzENK0FEHZZelWpbiJporYRrGIl8kTiCDkOryrXMQKkyquEkjKjD0So1DYEQCIEQCIEQCIEQCIEQCIEQCIEQCIEQCIEQCIEQCIEQCIEQCIEQCIEQCIEQCIEQCIEQCIEQCIEQCIEQCIEQCIEQCIEQCIEQCIEQCIEQCIEQCIEQCIEQCIEQCIEQCIEQCIEQCIEQCIEQCIEQCIEQCIEQCIEQCIEQCIEQCIEQCIEQCIEQCIEQCIEQCIEQCIEQCIEQCIEQCIEQCIEQCIEQ6I7A/1o1YBVBEPqUAAAAAElFTkSuQmCC);
                }

                .icon {
                    color: #fff;
                }

                .apple-icon {
                    color: #fff;
                }

                .title {
                    color: #fff;
                }

                .date {
                    color: #6E6E73;
                }

                .apple-icon,
                .title,
                .sub-title {
                    color: #fff;
                }

                .timeTip {
                    color: #6e6e73;
                }
                .type-btn-title h2,
                .type-btn-title p {
                    color: #1C1C1E;
                }

                .first-tr,
                .two-tr {
                    background: #f5f5f7;
                }

                .qtd-perf th,
                .qtd-perf td,
                .qtd-perf tr:last-child td,
                .qtd-perf.productView .two:last-child td,
                .qtd-perf.channelView .three:last-child td {
                    border-color: #E6E6EB;
                }
                .qtd-perf.channelView .three:last-child td, .qtd-perf.channelView .three:not(:has(+ .three)):has(+ .two) td,.two:has(+ .two) td {
                    padding-bottom: 12px;
                    border-color: #E6E6EB;
                }
                .qtd-perf .two-tr th:nth-child(3),
                .qtd-perf .two-tr th:nth-child(4),
                .qtd-perf .two-tr th:nth-last-child(-n+2) {
                    background: #EFF2F6;
                }
                .qtd-perf td:nth-child(4),
                .qtd-perf td:nth-child(5),
                .qtd-perf td:nth-last-child(-n+2) {
                    background: #F9FCFE;
                }
                .qtd-perf th,
                .qtd-perf td {
                    color: #6e6e73;
                }
                b.bold {
                    color: #3a3a3c;
                }
                .qtd-perf .two td.bold,
                .qtd-perf .three td.bold,
                .qtd-perf .one td {
                    color: #3a3a3c;
                }
                .footer {
                    background-color: #f5f5f7;
                }

                .footer-hint {
                    color: #3a3a3c;
                }

                .footer-hint-text {
                    color: #4F78E3;
                }

                .hint-text {
                    color: #6e6e73;
                }

                .section-btn {
                    background-color: #1C1C1E;
                    color: #ffffff;
                }
            }

        </style>
        <style>
            @media screen and (min-width:600px) {
                .tableWrapper{
                    width:100%;
                    max-width: 880px !important;
                }
                .tableWrapper > table{
                    padding:32px 40px !important;
                }
                .tableWrapper .header{
                    padding: 16px 30px !important;
                }
                .tableWrapper .header .icon{
                    font-size: 17px;
                }
                .tableWrapper .sub-title p:first-child{
                    font-size: 16px !important;
                }
                .type-btn-title h2{
                    font-size: 18px;
                }
                .type-btn-title p{
                    font-size: 14px !important;
                }
                .wrap-box-div .timeTip {
                    padding-top:8px;
                    font-size:14px;
                    line-height: 18px;
                }
                .wrap-box-div .timeTip.text:not(.last) {
                    padding-top:0;
                }
                .tableTitle{
                    padding: 20px 0 12px 8px;
                    line-height: 20px;
                    font-size: 16px;
                }
                .tableTitle span{
                    font-size:16px;
                }
                .tableTitle div{
                    font-size:16px;
                }
                .tableTitle .timeTip{
                    font-size:14px;
                }
                .footer-hint{
                    font-size: 14px;
                }
                .hint-text{
                    font-size: 12px;
                }
                .qtd-perf th {
                    font-size: 13px;
                    padding: 12px;
                    line-height: 20px;
                }
                .qtd-perf .one td,.qtd-perf .two td,.qtd-perf .three td  {
                    font-size: 14px;
                    line-height: 16px;
                    padding: 14px;
                }
                .qtd-perf .three td:nth-child(1) {
                    padding-left: 20px;
                }
                .qtd-perf .three td {
                    padding-bottom: 0;
                }
                .qtd-perf.channelView .three:last-child td, .qtd-perf.channelView .three:not(:has(+ .three)):has(+ .two) td,.two:has(+ .two) td,.qtd-perf tr:last-child td {
                    padding-bottom: 14px;
                    border-bottom-width: 1px;
                    border-bottom-style: solid;
                }
                .qtd-perf.productView .two td {
                    padding-bottom: 0;
                    border-bottom: none;
                }
                .qtd-perf.productView .two:last-child td {
                    padding-bottom: 14px;
                    border-bottom-width: 1px;
                    border-bottom-style: solid;
                }
                .qtd-perf.channelView .two:not(:has(+ .two)):has(+ .three) td  {
                    padding-bottom:0;
                }
                .tip {
                    font-size: 10px;
                    line-height: 14px;
                    padding: 4px 0 24px;
                }
            }
            @media screen and (max-width: 599px ) {
                .type-btn-title p{
                    font-size: 12px !important;
                }
                .wrap-box-div .timeTip{
                    padding-top: 6px;
                }
                .wrap-box-div .timeTip.text:not(.last) {
                    padding-top:0;
                }
                .qtd-perf th {
                    font-size: 8px;
                    padding: 6px;
                    line-height: 12px;
                }
                .timeTip svg {
                    width: 14px;
                    height: 14px;
                    vertical-align: text-bottom;
                }
                /*.timeTip .timeTipDiv{*/
                /*    padding-top: 6px;*/
                /*}*/
                .qtd-perf .one td,.qtd-perf .two td,.qtd-perf .three td  {
                    font-size: 10px;
                    padding: 10px;
                    line-height: 12px;
                }
                .qtd-perf .three td:nth-child(1) {
                    padding-left: 17px;
                }
                .qtd-perf .three td {
                    padding-bottom: 0;
                }
                .qtd-perf.channelView .three:last-child td, .qtd-perf.channelView .three:not(:has(+ .three)):has(+ .two) td,.two:has(+ .two) td,.qtd-perf tr:last-child td {
                    padding-bottom: 10px;
                    border-bottom-width: 1px;
                    border-bottom-style: solid;
                }
                .qtd-perf.productView .two td {
                    padding-bottom: 0;
                    border-bottom: none;
                }
                .qtd-perf.productView .two:last-child td {
                    padding-bottom: 10px;
                    border-bottom-width: 1px;
                    border-bottom-style: solid;
                }
                .qtd-perf.channelView .two:not(:has(+ .two)):has(+ .three) td {
                    padding-bottom:0;
                }
                .tip {
                    font-size: 9px;
                    line-height: 12px;
                    padding: 2px 0 18px;
                }
            }
        </style>
        <meta name="color-scheme" content="light dark">
        <meta name="supported-color-schemes" content="light dark">
    </head>

    <body style="word-spacing:normal;">
        <div style="">
            <!--[if mso | IE]><table align="center" border="0" cellpadding="0" cellspacing="0" class="tableWrapper-outlook" role="presentation" style="width:600px;" width="600" ><tr><td style="line-height:0px;font-size:0px;mso-line-height-rule:exactly;"><![endif]-->
            <div class="tableWrapper" style="margin:0px auto;max-width:600px;">
                <table align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="width:100%;padding:16px">
                    <tbody>
                        <tr>
                            <td style="direction:ltr;font-size:0px;padding:0 0 16px 0;text-align:center;">
                                <!--[if mso | IE]><table role="presentation" border="0" cellpadding="0" cellspacing="0"><tr><td class="" style="vertical-align:top;width:600px;" ><![endif]-->
                                <div class="mj-column-per-100 mj-outlook-group-fix" style="font-size:0px;text-align:left;direction:ltr;display:inline-block;vertical-align:top;width:100%;">
                                    <table border="0" cellpadding="0" cellspacing="0" role="presentation" style="vertical-align:top;" width="100%">
                                        <tbody>
                                            <!-- 页眉区域 -->
                                            <tr>
                                                <td align="left" class="header" style="font-size:0px;padding:16px 24px;word-break:break-word;">
                                                    <div style="font-family:'SF Pro','PingFang SC';font-size:13px;line-height:1;text-align:left;color:#000000;">
                                                        <div>
                                                            <div>
                                                                <span class="icon"></span>
                                                                <span class="title">Expert</span>
                                                            </div>
                                                            <div class="sub-title">
                                                                <p style="font-size: 17px; font-weight: 600;">iPhone PO Gap</p>
                                                                <p>{{ send_date }}</p>
                                                            </div>
                                                        </div>
                                                        <div class="apple-icon"></div>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td align="left" style="font-size:0px;padding:0 2px 0 8px;word-break:break-word;">
                                                    <div style="font-family:'SF Pro','PingFang SC';font-size:13px;line-height:1;text-align:left;color:#000000;">
                                                        <div class="wrap-box-div">
                                                            <div class="type-btn-title">
                                                                <h2>{{ customized_data.get("greeting") }}</h2>
                                                                <p style="margin: 0">Here are your highlights of the latest PO gap status:</p>
                                                            </div>
                                                            {% for summary_item in customized_data.get("summary") %}
                                                            {% if summary_item.get('total') == "0" %}
                                                                <div class="timeTip">
                                                                    <div class="timeTipDiv">
                                                                            <span class="green">
                                                                                <svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                                                                                    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                                                                        <rect opacity="0" x="0" y="0" width="16" height="16"></rect>
                                                                                        <g transform="translate(1.2236, 1.0029)" fill="currentColor" fill-rule="nonzero">
                                                                                            <g id="icon/20px/enter">
                                                                                                <path d="M7.06152344,14.1230469 C8.02766927,14.1230469 8.93684896,13.9396159 9.7890625,13.5727539 C10.641276,13.2058919 11.3932292,12.6966146 12.0449219,12.0449219 C12.6966146,11.3932292 13.2070312,10.641276 13.5761719,9.7890625 C13.9453125,8.93684896 14.1298828,8.02766927 14.1298828,7.06152344 C14.1298828,6.0953776 13.9453125,5.18619792 13.5761719,4.33398437 C13.2070312,3.48177083 12.6966146,2.73095703 12.0449219,2.08154297 C11.3932292,1.43212891 10.641276,0.922851562 9.7890625,0.553710937 C8.93684896,0.184570312 8.02539062,0 7.0546875,0 C6.08854167,0 5.1805013,0.184570312 4.33056641,0.553710937 C3.48063151,0.922851562 2.73095703,1.43212891 2.08154297,2.08154297 C1.43212891,2.73095703 0.922851562,3.48177083 0.553710937,4.33398437 C0.184570313,5.18619792 0,6.0953776 0,7.06152344 C0,8.02766927 0.184570313,8.93684896 0.553710937,9.7890625 C0.922851562,10.641276 1.43212891,11.3932292 2.08154297,12.0449219 C2.73095703,12.6966146 3.48177083,13.2058919 4.33398437,13.5727539 C5.18619792,13.9396159 6.0953776,14.1230469 7.06152344,14.1230469 Z M7.06152344,12.7285156 C6.27766927,12.7285156 5.54280599,12.5826823 4.85693359,12.2910156 C4.1710612,11.999349 3.5694987,11.59375 3.05224609,11.0742188 C2.53499349,10.5546875 2.13053385,9.953125 1.83886719,9.26953125 C1.54720052,8.5859375 1.40136719,7.8499349 1.40136719,7.06152344 C1.40136719,6.27766927 1.54720052,5.54280599 1.83886719,4.85693359 C2.13053385,4.1710612 2.53385417,3.56835937 3.04882812,3.04882812 C3.56380208,2.52929687 4.16422526,2.12369792 4.85009766,1.83203125 C5.53597005,1.54036458 6.27083333,1.39453125 7.0546875,1.39453125 C7.84309896,1.39453125 8.58024089,1.54036458 9.26611328,1.83203125 C9.95198568,2.12369792 10.5546875,2.52929687 11.0742188,3.04882812 C11.59375,3.56835937 11.999349,4.1710612 12.2910156,4.85693359 C12.5826823,5.54280599 12.7285156,6.27766927 12.7285156,7.06152344 C12.7285156,7.8499349 12.5826823,8.5859375 12.2910156,9.26953125 C11.999349,9.953125 11.59375,10.5546875 11.0742188,11.0742188 C10.5546875,11.59375 9.953125,11.999349 9.26953125,12.2910156 C8.5859375,12.5826823 7.8499349,12.7285156 7.06152344,12.7285156 Z M7.06152344,8.19628906 C7.44433594,8.19628906 7.64029948,7.99576823 7.64941406,7.59472656 L7.75878906,4.27246094 C7.76790365,4.0764974 7.70410156,3.91357422 7.56738281,3.78369141 C7.43066406,3.65380859 7.25748698,3.58886719 7.04785156,3.58886719 C6.83821615,3.58886719 6.66731771,3.65266927 6.53515625,3.78027344 C6.40299479,3.9078776 6.34147135,4.0719401 6.35058594,4.27246094 L6.44628906,7.59472656 C6.45996094,7.99576823 6.66503906,8.19628906 7.06152344,8.19628906 Z M7.06152344,10.4658203 C7.27571615,10.4658203 7.4625651,10.3951823 7.62207031,10.2539062 C7.78157552,10.1126302 7.86132812,9.93261719 7.86132812,9.71386719 C7.86132812,9.49511719 7.78271484,9.31510417 7.62548828,9.17382812 C7.46826172,9.03255208 7.28027344,8.96191406 7.06152344,8.96191406 C6.83821615,8.96191406 6.64794922,9.03369141 6.49072266,9.17724609 C6.33349609,9.32080078 6.25488281,9.49967448 6.25488281,9.71386719 C6.25488281,9.9280599 6.33349609,10.1069336 6.49072266,10.2504883 C6.64794922,10.394043 6.83821615,10.4658203 7.06152344,10.4658203 Z" id="形状"></path>
                                                                                            </g>
                                                                                        </g>
                                                                                    </g>
                                                                                </svg>
                                                                        </span> Based on <b class="bold">{{ summary_item.get('display_name') }}</b> demand, all orders have been placed with <b class="green">no PO gap</b> remained.<br/>
                                                                    </div>
                                                                </div>
                                                        {% else %}
                                                            <div class="timeTip">
                                                                <div class="timeTipDiv">
                                                                    <span class="red">
                                                                        <svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                                                                            <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                                                                <rect opacity="0" x="0" y="0" width="16" height="16"></rect>
                                                                                <g transform="translate(1.2236, 1.0029)" fill="currentColor" fill-rule="nonzero">
                                                                                    <g id="icon/20px/enter">
                                                                                        <path d="M7.06152344,14.1230469 C8.02766927,14.1230469 8.93684896,13.9396159 9.7890625,13.5727539 C10.641276,13.2058919 11.3932292,12.6966146 12.0449219,12.0449219 C12.6966146,11.3932292 13.2070312,10.641276 13.5761719,9.7890625 C13.9453125,8.93684896 14.1298828,8.02766927 14.1298828,7.06152344 C14.1298828,6.0953776 13.9453125,5.18619792 13.5761719,4.33398437 C13.2070312,3.48177083 12.6966146,2.73095703 12.0449219,2.08154297 C11.3932292,1.43212891 10.641276,0.922851562 9.7890625,0.553710937 C8.93684896,0.184570312 8.02539062,0 7.0546875,0 C6.08854167,0 5.1805013,0.184570312 4.33056641,0.553710937 C3.48063151,0.922851562 2.73095703,1.43212891 2.08154297,2.08154297 C1.43212891,2.73095703 0.922851562,3.48177083 0.553710937,4.33398437 C0.184570313,5.18619792 0,6.0953776 0,7.06152344 C0,8.02766927 0.184570313,8.93684896 0.553710937,9.7890625 C0.922851562,10.641276 1.43212891,11.3932292 2.08154297,12.0449219 C2.73095703,12.6966146 3.48177083,13.2058919 4.33398437,13.5727539 C5.18619792,13.9396159 6.0953776,14.1230469 7.06152344,14.1230469 Z M7.06152344,12.7285156 C6.27766927,12.7285156 5.54280599,12.5826823 4.85693359,12.2910156 C4.1710612,11.999349 3.5694987,11.59375 3.05224609,11.0742188 C2.53499349,10.5546875 2.13053385,9.953125 1.83886719,9.26953125 C1.54720052,8.5859375 1.40136719,7.8499349 1.40136719,7.06152344 C1.40136719,6.27766927 1.54720052,5.54280599 1.83886719,4.85693359 C2.13053385,4.1710612 2.53385417,3.56835937 3.04882812,3.04882812 C3.56380208,2.52929687 4.16422526,2.12369792 4.85009766,1.83203125 C5.53597005,1.54036458 6.27083333,1.39453125 7.0546875,1.39453125 C7.84309896,1.39453125 8.58024089,1.54036458 9.26611328,1.83203125 C9.95198568,2.12369792 10.5546875,2.52929687 11.0742188,3.04882812 C11.59375,3.56835937 11.999349,4.1710612 12.2910156,4.85693359 C12.5826823,5.54280599 12.7285156,6.27766927 12.7285156,7.06152344 C12.7285156,7.8499349 12.5826823,8.5859375 12.2910156,9.26953125 C11.999349,9.953125 11.59375,10.5546875 11.0742188,11.0742188 C10.5546875,11.59375 9.953125,11.999349 9.26953125,12.2910156 C8.5859375,12.5826823 7.8499349,12.7285156 7.06152344,12.7285156 Z M7.06152344,8.19628906 C7.44433594,8.19628906 7.64029948,7.99576823 7.64941406,7.59472656 L7.75878906,4.27246094 C7.76790365,4.0764974 7.70410156,3.91357422 7.56738281,3.78369141 C7.43066406,3.65380859 7.25748698,3.58886719 7.04785156,3.58886719 C6.83821615,3.58886719 6.66731771,3.65266927 6.53515625,3.78027344 C6.40299479,3.9078776 6.34147135,4.0719401 6.35058594,4.27246094 L6.44628906,7.59472656 C6.45996094,7.99576823 6.66503906,8.19628906 7.06152344,8.19628906 Z M7.06152344,10.4658203 C7.27571615,10.4658203 7.4625651,10.3951823 7.62207031,10.2539062 C7.78157552,10.1126302 7.86132812,9.93261719 7.86132812,9.71386719 C7.86132812,9.49511719 7.78271484,9.31510417 7.62548828,9.17382812 C7.46826172,9.03255208 7.28027344,8.96191406 7.06152344,8.96191406 C6.83821615,8.96191406 6.64794922,9.03369141 6.49072266,9.17724609 C6.33349609,9.32080078 6.25488281,9.49967448 6.25488281,9.71386719 C6.25488281,9.9280599 6.33349609,10.1069336 6.49072266,10.2504883 C6.64794922,10.394043 6.83821615,10.4658203 7.06152344,10.4658203 Z" id="形状"></path>
                                                                                    </g>
                                                                                </g>
                                                                            </g>
                                                                        </svg>
                                                                    </span> Based on <b class="bold">{{ summary_item.get('display_name') }}</b> demand, the total PO gap for {{ summary_item.get('display_name') }} is <b class="red">{{ summary_item.get('total') }}K</b> with Gap% of <b class="red">{{ summary_item.get('gap_percentage') }}</b>.<br/>
                                                                </div>
                                                            </div>
                                                            <div class="timeTip text">
                                                                <div class="timeTipDiv">
                                                                    - In terms of models, <b class="bold">{{ summary_item.get('product_view_top1').get("sub_lob") }}</b> has the largest PO gap of <b class="red">{{ summary_item.get('product_view_top1').get("po_gap") }}K</b>{% if ";" not in summary_item.get('product_view_top1').get("sub_lob") %} with Gap% of <b class="red">{{ summary_item.get('product_view_top1').get("po_gap_percentage") }}</b>{% endif %}.
                                                                </div>
                                                                <div class="timeTipDiv">
                                                                    - In terms of channels, <b class="bold">{{ summary_item.get('channel_view_top1').get("sub_rtm") }}</b> has the largest PO gap of <b class="red">{{ summary_item.get('channel_view_top1').get("po_gap") }}K</b>{% if ";" not in summary_item.get('channel_view_top1').get("sub_rtm") %} with Gap% of <b class="red">{{ summary_item.get('channel_view_top1').get("po_gap_percentage") }}</b>{% endif %}.
                                                                </div>
                                                            </div>
                                                        {% endif %}
                                                        {% endfor %}
                                                        <div class="timeTip text last"><div class="timeTipDiv">* PO Gap = Open PO - Demand, with any negative PO Gap value indicating the number of order quantity shortage.</div></div>
                                                        <div class="timeTip text"><div class="timeTipDiv">* The calculation of PO Gap is generated at Sold-to & MPN level firstly, and then aggregated to channel level result.</div></div>
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                            <tr>
                                                <td align="left" style="font-size:0px;padding:0;word-break:break-word;">
                                                    <div style="font-family:'SF Pro','PingFang SC';font-size:13px;line-height:1;text-align:left;color:#000000;">
                                                        <div class="tableTitle">Product View</div>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td align="left" class="qtd-perf productView" style="font-size:0px;padding:0;word-break:break-word;">
                                                    <table cellpadding="0" cellspacing="0" width="100%" border="0" style="color:#000000;font-family:'SF Pro','PingFang SC';font-size:13px;line-height:22px;table-layout:auto;width:100%;border:none;">
                                                        <tr class="first-tr">
                                                            <th rowspan="2">Units (K)</th>
                                                            <th colspan="4">CW+1</th>
                                                            <th colspan="4">CW+2</th>
                                                        </tr>
                                                        <tr class="two-tr">
                                                            <th>FAST<div>Demand</div></th>
                                                            <th>Open PO</th>
                                                            <th>PO Gap</th>
                                                            <th>Gap%</th>
                                                            <th>FAST<div>Demand</div></th>
                                                            <th>Open PO</th>
                                                            <th>PO Gap</th>
                                                            <th>Gap%</th>
                                                        </tr>
                                                        {% for row in customized_data.get("product_view") %}
                                                        {% if row.get("sub_lob") == "Total" %}
                                                            <tr class="one">
                                                                <td>{{ row.get("sub_lob") }}</td>
                                                                <td>{{ row.get("demand_cw1") }}</td>
                                                                <td>{{ row.get("open_po_cw1") }}</td>
                                                                {% if row.get("po_gap_cw1") != "0" %}
                                                                    {% set cw1_high_light_class = "red" %}
                                                                    {% else %}
                                                                    {% set cw1_high_light_class = "" %}
                                                                {% endif  %}
                                                                <td class={{cw1_high_light_class}}>{{ row.get("po_gap_cw1") }}</td>
                                                                <td class={{cw1_high_light_class}}>{{ row.get("gap_percentage_cw1") }}</td>
                                                                
                                                                <td>{{ row.get("demand_cw2") }}</td>
                                                                <td>{{ row.get("open_po_cw2") }}</td>
                                                                {% if row.get("po_gap_cw2") != "0" %}
                                                                    {% set cw2_high_light_class = "red" %}
                                                                    {% else %}
                                                                    {% set cw2_high_light_class = "" %}
                                                                {% endif  %}
                                                                <td class={{cw2_high_light_class}}>{{ row.get("po_gap_cw2") }}</td>
                                                                <td class={{cw2_high_light_class}}>{{ row.get("gap_percentage_cw2") }}</td>
                                                            </tr>
                                                        {% else %}
                                                            <tr class="two">
                                                                {% set cw1_bold_class = "" %}
                                                                {% if row.get("po_gap_cw1") == "0" %}
                                                                    {% set cw1_high_light_class = "" %}
                                                                {% elif row.get("po_gap_cw1") != "0" and row.get("rank_po_gap_cw1") == 1 %}
                                                                    {% set cw1_high_light_class = "red" %}
                                                                    {% set cw1_bold_class = "bold" %}
                                                                {% else %}
                                                                    {% set cw1_high_light_class = "yellow" %}
                                                                {% endif %}
                                                                
                                                                {% set cw2_bold_class = "" %}
                                                                {% if row.get("po_gap_cw2") == "0" %}
                                                                    {% set cw2_high_light_class = "" %}
                                                                {% elif row.get("po_gap_cw2") != "0" and row.get("rank_po_gap_cw2") == 1 %}
                                                                    {% set cw2_high_light_class = "red" %}
                                                                    {% set cw2_bold_class = "bold" %}
                                                                {% else %}
                                                                    {% set cw2_high_light_class = "yellow" %}
                                                                {% endif %}

                                                                <td class="{{cw1_bold_class if cw1_bold_class else cw2_bold_class}}">{{ row.get("sub_lob") }}</td>
                                                                <td>{{ row.get("demand_cw1") }}</td>
                                                                <td>{{ row.get("open_po_cw1") }}</td>
                                                                <td class={{cw1_high_light_class}}>{{ row.get("po_gap_cw1") }}</td>
                                                                <td class={{cw1_high_light_class}}>{{ row.get("gap_percentage_cw1") }}</td>
                                                                <td>{{ row.get("demand_cw2") }}</td>
                                                                <td>{{ row.get("open_po_cw2") }}</td>
                                                                <td class={{cw2_high_light_class}}>{{ row.get("po_gap_cw2") }}</td>
                                                                <td class={{cw2_high_light_class}}>{{ row.get("gap_percentage_cw2") }}</td>
                                                            </tr>
                                                        {% endif %}
                                                        {% endfor %}
                                                    </table>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td align="left" style="font-size:0px;padding:0;word-break:break-word;">
                                                    <div style="font-family:'SF Pro','PingFang SC';font-size:13px;line-height:1;text-align:left;color:#000000;">
                                                        <div class="tableTitle">Channel View</div>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td align="left" class="qtd-perf channelView" style="font-size:0px;padding:0;word-break:break-word;">
                                                    <table cellpadding="0" cellspacing="0" width="100%" border="0" style="color:#000000;font-family:'SF Pro','PingFang SC';font-size:13px;line-height:22px;table-layout:auto;width:100%;border:none;">
                                                        <tr class="first-tr">
                                                            <th rowspan="2">Units (K)</th>
                                                            <th colspan="4">CW+1</th>
                                                            <th colspan="4">CW+2</th>
                                                        </tr>
                                                        <tr class="two-tr">
                                                            <th>FAST<div>Demand</div></th>
                                                            <th>Open PO</th>
                                                            <th>PO Gap</th>
                                                            <th>Gap%</th>
                                                            <th>FAST<div>Demand</div></th>
                                                            <th>Open PO</th>
                                                            <th>PO Gap</th>
                                                            <th>Gap%</th>
                                                        </tr>
                                                        {% for row in customized_data.get("channel_view") %}
                                                        {% if row.get("rtm") == "Total" %}
                                                            <tr class="one">
                                                                <td>{{ row.get("rtm") }}</td>
                                                                <td>{{ row.get("demand_cw1") }}</td>
                                                                <td>{{ row.get("open_po_cw1") }}</td>
                                                                {% if row.get("po_gap_cw1") != "0" %}
                                                                    {% set cw1_high_light_class = "red" %}
                                                                    {% else %}
                                                                    {% set cw1_high_light_class = "" %}
                                                                {% endif  %}
                                                                <td class={{cw1_high_light_class}}>{{ row.get("po_gap_cw1") }}</td>
                                                                <td class={{cw1_high_light_class}}>{{ row.get("gap_percentage_cw1") }}</td>
                                                                
                                                                <td>{{ row.get("demand_cw2") }}</td>
                                                                <td>{{ row.get("open_po_cw2") }}</td>
                                                                {% if row.get("po_gap_cw2") != "0" %}
                                                                    {% set cw2_high_light_class = "red" %}
                                                                    {% else %}
                                                                    {% set cw2_high_light_class = "" %}
                                                                {% endif  %}
                                                                <td class={{cw2_high_light_class}}>{{ row.get("po_gap_cw2") }}</td>
                                                                <td class={{cw2_high_light_class}}>{{ row.get("gap_percentage_cw2") }}</td>
                                                            </tr>
                                                        {% elif row.get("sub_rtm") == "All" %}
                                                            <tr class="two">
                                                                <td>-{{ row.get("rtm") }}</td>
                                                                <td>{{ row.get("demand_cw1") }}</td>
                                                                <td>{{ row.get("open_po_cw1") }}</td>
                                                                {% if row.get("po_gap_cw1") == "0" %}
                                                                    {% set cw1_high_light_class = "" %}
                                                                {% elif row.get("po_gap_cw1") != "0" and row.get("rank_po_gap_cw1") == 1 %}
                                                                    {% set cw1_high_light_class = "red" %}
                                                                {% else %}
                                                                    {% set cw1_high_light_class = "yellow" %}
                                                                {% endif  %}
                                                                <td class={{cw1_high_light_class}}>{{ row.get("po_gap_cw1") }}</td>
                                                                <td class={{cw1_high_light_class}}>{{ row.get("gap_percentage_cw1") }}</td>

                                                                <td>{{ row.get("demand_cw2") }}</td>
                                                                <td>{{ row.get("open_po_cw2") }}</td>
                                                                {% if row.get("po_gap_cw2") == "0" %}
                                                                    {% set cw2_high_light_class = "" %}
                                                                {% elif row.get("po_gap_cw2") != "0" and row.get("rank_po_gap_cw2") == 1 %}
                                                                    {% set cw2_high_light_class = "red" %}
                                                                {% else %}
                                                                    {% set cw2_high_light_class = "yellow" %}
                                                                {% endif  %}
                                                                <td class={{cw2_high_light_class}}>{{ row.get("po_gap_cw2") }}</td>
                                                                <td class={{cw2_high_light_class}}>{{ row.get("gap_percentage_cw2") }}</td>
                                                            </tr>
                                                        {% else %}
                                                            <tr class="three">
                                                                {% set cw1_bold_class = "" %}
                                                                {% if row.get("po_gap_cw1") == "0" %}
                                                                    {% set cw1_high_light_class = "" %}
                                                                {% elif row.get("po_gap_cw1") != "0" and row.get("rank_po_gap_cw1") == 1 %}
                                                                    {% set cw1_high_light_class = "red" %}
                                                                    {% set cw1_bold_class = "bold" %}
                                                                {% else %}
                                                                    {% set cw1_high_light_class = "yellow" %}
                                                                {% endif %}
                                                                
                                                                {% set cw2_bold_class = "" %}
                                                                {% if row.get("po_gap_cw2") == "0" %}
                                                                    {% set cw2_high_light_class = "" %}
                                                                {% elif row.get("po_gap_cw2") != "0" and row.get("rank_po_gap_cw2") == 1 %}
                                                                    {% set cw2_high_light_class = "red" %}
                                                                    {% set cw2_bold_class = "bold" %}
                                                                {% else %}
                                                                    {% set cw2_high_light_class = "yellow" %}
                                                                {% endif %}

                                                                <td class="{{cw1_bold_class if cw1_bold_class else cw2_bold_class}}">{{ row.get("sub_rtm") }}</td>
                                                                <td>{{ row.get("demand_cw1") }}</td>
                                                                <td>{{ row.get("open_po_cw1") }}</td>
                                                                <td class={{cw1_high_light_class}}>{{ row.get("po_gap_cw1") }}</td>
                                                                <td class={{cw1_high_light_class}}>{{ row.get("gap_percentage_cw1") }}</td>
                                                                <td>{{ row.get("demand_cw2") }}</td>
                                                                <td>{{ row.get("open_po_cw2") }}</td>
                                                                <td class={{cw2_high_light_class}}>{{ row.get("po_gap_cw2") }}</td>
                                                                <td class={{cw2_high_light_class}}>{{ row.get("gap_percentage_cw2") }}</td>
                                                            </tr>
                                                        {% endif %}
                                                        {% endfor %}
                                                    </table>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td align="left" style="font-family:'SF Pro','PingFang SC';padding:0">
                                                    <div class="tip">* Data updated to {{ customized_data.get("latest_refresh_time") }}</div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td align="center" class="button">
                                                    <a style="font-family:'SF Pro','PingFang SC'" href="{{ 'https://idmsac.corp.apple.com/IDMSWebAuth/login?appIdKey=2861e834396f78da14e20746b6303b2cac20bf0f6c27550d19ba804deaf49912&path=/%23/dashboard/po-gap?channel=' ~ 'CP&F'|urlencode }}" class="section-btn">
                                                        Details
                                                    </a>
                                                </td>
                                            </tr>

                                            <!-- 页脚区域 -->
                                            <tr>
                                                <td align="left" class="footer" style="font-size:0px;padding:18px;word-break:break-word;">
                                                    <div style="font-family:'SF Pro','PingFang SC';font-size:13px;line-height:1;text-align:left;color:#000000;">
                                                        <div class="footer-hint">
                                                            <div style="text-align:center">If you have any questions, please contact</div>
                                                            <div style="text-align:center">
                                                                <a href="mailto:<EMAIL>" class="footer-hint-text"><EMAIL></a>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td align="left" style="font-size:0px;padding:16px 0 0;word-break:break-word;">
                                                    <div style="font-family:'SF Pro','PingFang SC';font-size:13px;line-height:1;text-align:left;color:#000000;">
                                                        <div class="hint-text"> For internal use only. Sent from Expert Technical Support. </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <!--[if mso | IE]></td></tr></table><![endif]-->
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <!--[if mso | IE]></td></tr></table><![endif]-->
        </div>
    </body>

</html>