# 使用dashboard中的数据，处理成邮件中需要展示的内容
from util.conf import logger
from data.databend.dashboard.dashboard_po_gap import PoGapDi
from data.fiscal_year_week import FiscalYearWeek
from domain.dashboard.impl.po_gap_impl import get_channel_view_data, get_product_view_data, get_sold_to_view_data
from domain.email.entity.email_po_gap_entity import PoGapChannelViewEntity, PoGapEntity, PoGapProductViewEntity, PoGapRTMEntity, PoGapSoldtoViewEntity, rank_special_items
from kit.format_numer_with_thousands_separator import format_number_with_thousands_separator


def extract_first_layer(tree_list):
    first_layer = []
    for tree in tree_list:
        first_layer.append({k: v for k, v in tree.items() if k != 'child'})
    return first_layer


def extract_second_layer(tree_list):
    second_layer = []
    # 检查根节点是否有子节点
    for tree in tree_list:
        if 'child' in tree:
            for child in tree['child']:
                child_info = {k: v for k, v in child.items() if k != 'child'}
                second_layer.append(child_info)
    return second_layer


def extract_third_layer(tree_list):
    third_layer = []
    # 遍历第一层节点
    for tree in tree_list:
        # 检查第一层节点是否有子节点（第二层节点）
        if 'child' in tree:
            for second_child in tree['child']:
                # 检查第二层节点是否有子节点（第三层节点）
                if 'child' in second_child and len(second_child['child']):
                    # 要先展开第二层
                    third_child_info = {k: v for k, v in second_child.items() if k != 'child'}
                    third_layer.append(third_child_info)
                    for third_child in second_child['child']:
                        # 提取第三层节点信息，排除 'child' 键
                        third_child_info = {k: v for k, v in third_child.items() if k != 'child'}
                        third_layer.append(third_child_info)
                else:
                    third_child_info = {k: v for k, v in second_child.items() if k != 'child'}
                    third_layer.append(third_child_info)
    return third_layer


def process_data(data):
    sub_rtm_groups = {}
    for row in data:
        rtm = row["rtm"]
        sub_rtm = row['sub_rtm']
        zero_sum_dict = {} if rtm != 'Mono' or sub_rtm == 'All' else {
                    "rtm": row["rtm"],
                    "sub_rtm": sub_rtm,
                    "sold_to_id": "",
                    "sold_to_name": "其他",
                    "po_needed_cw1": 0, # po_gap
                    "po_needed_cw2": 0, # po_gap
                    "cw1_demand": 0,
                    "cw2_demand": 0,
                    "available_po_cw1": 0,
                    "available_po_cw2": 0,
                    "others": 0,
                }
        if sub_rtm not in sub_rtm_groups:
            sub_rtm_groups[sub_rtm] = {
                "non_zero": [],
                "zero_sum": zero_sum_dict
            }
        po_gap_cw1 = row.get("po_needed_cw1", 0)
        po_gap_cw2 = row.get("po_needed_cw2", 0)
        if rtm != 'Mono' or (po_gap_cw1 != 0 and abs(po_gap_cw1) >= 50) or (po_gap_cw2 != 0 and abs(po_gap_cw2) >= 50) or row["sub_rtm"] == 'All' or (row['rtm'] == row['sub_rtm'] and row["sold_to_name"] == 'All'):
            new_row = row.copy()
            sub_rtm_groups[sub_rtm]["non_zero"].append(new_row)
        else:
            if len(sub_rtm_groups[sub_rtm]["zero_sum"]) > 0:
                sub_rtm_groups[sub_rtm]["zero_sum"]["po_needed_cw1"] += row.get("po_needed_cw1", 0)
                sub_rtm_groups[sub_rtm]["zero_sum"]["po_needed_cw2"] += row.get("po_needed_cw2", 0)
                sub_rtm_groups[sub_rtm]["zero_sum"]["cw1_demand"] += row.get("cw1_demand", 0)
                sub_rtm_groups[sub_rtm]["zero_sum"]["cw2_demand"] += row.get("cw2_demand", 0)
                sub_rtm_groups[sub_rtm]["zero_sum"]["available_po_cw1"] += row.get("available_po_cw1", 0)
                sub_rtm_groups[sub_rtm]["zero_sum"]["available_po_cw2"] += row.get("available_po_cw2", 0)
                sub_rtm_groups[sub_rtm]["zero_sum"]["others"] += 1

    final_result = []
    for sub_rtm, group in sub_rtm_groups.items():
        non_zero_rows = sorted(group["non_zero"], key=lambda x: (x["sold_to_name"] != "All", x["sold_to_name"] is None, x["sold_to_name"] if x["sold_to_name"] not in [None, "All"] else ""))
        final_result.extend(non_zero_rows)
        if len(group["zero_sum"]) > 0 and group["zero_sum"]["others"] > 0:
            other_row = {
                "rtm": group["zero_sum"]["rtm"],
                "sub_rtm":  group["zero_sum"]["sub_rtm"],
                "sold_to_id": "",
                "sold_to_name": "其他",
                "po_needed_cw1": group["zero_sum"]["po_needed_cw1"],
                "po_needed_cw2": group["zero_sum"]["po_needed_cw2"],
                "cw1_demand": group["zero_sum"]["cw1_demand"],
                "cw2_demand": group["zero_sum"]["cw2_demand"],
                "available_po_cw1": group["zero_sum"]["available_po_cw1"],
                "available_po_cw2": group["zero_sum"]["available_po_cw2"],
            }
            final_result.append(other_row)

    return final_result


def get_po_gap_email_data():
    max_fiscal_week = PoGapDi.get_max_fiscal_qtr_week_name()
    # 需要先查询所有的sub_lob,否则会有多余sub_lob=All的数据
    sub_lobs = PoGapDi.query_distinct_sublob("China mainland", "All", "All", "iPhone", fiscal_week=max_fiscal_week.get("fiscal_qtr_week_name"))
    # 获取dashboard中的数据
    latest_refresh_time, channel_view_list = get_channel_view_data(
        region="China mainland", lob='iPhone', sub_lobs=sub_lobs, rtm=None, fiscal_week=max_fiscal_week.get("fiscal_qtr_week_name")
    )
    # 只取第二层数据
    channel_view_list = extract_second_layer(channel_view_list)
    # 转换成channel view entity
    channel_view_entity_list = []
    for item in channel_view_list:
        channel_view_entity_list.append(
            PoGapChannelViewEntity(
                rtm=item['rtm'],
                sub_rtm=item['sub_rtm'], 
                demand_cw1=item['cw1_demand'],
                open_po_cw1=item['available_po_cw1'],
                po_gap_cw1=item['po_needed_cw1'],
                demand_cw2=item['cw2_demand'],
                open_po_cw2=item['available_po_cw2'],
                po_gap_cw2=item['po_needed_cw2']
            )
        )
    # 定义筛选条件，例如 sub_rtm 不为 'Total'
    filter_function = lambda x: x.rtm != 'Total' and x.sub_rtm != 'All'
    # 对 entity 进行排名，保持原有的顺序不变
    channel_view_entity_list, channel_view_cw1_top1 = rank_special_items(channel_view_entity_list, filter_function, 'po_gap_cw1', ['rtm', 'sub_rtm'])
    channel_view_entity_list, channel_view_cw2_top1 = rank_special_items(channel_view_entity_list, filter_function, 'po_gap_cw2', ['rtm', 'sub_rtm'])
    
    # 处理product_view的数据
    latest_refresh_time_p, product_view_list = get_product_view_data(
        region="China mainland", rtm='All', sub_rtm='All',lob='iPhone', sub_lobs=sub_lobs, fiscal_week=max_fiscal_week.get("fiscal_qtr_week_name")
    )
    # 只取第一层的数据
    product_view_list = extract_first_layer(product_view_list)
    
    # 转换成product view entity
    product_view_entity_list = [
        PoGapProductViewEntity(
            sub_lob=item['sub_lob'],
            demand_cw1=item['cw1_demand'],
            open_po_cw1=item['available_po_cw1'],
            po_gap_cw1=item['po_needed_cw1'],
            demand_cw2=item['cw2_demand'],
            open_po_cw2=item['available_po_cw2'],
            po_gap_cw2=item['po_needed_cw2'],
        ) for item in product_view_list
    ]
    filter_function_p = lambda x: x.sub_lob != 'Total'
    # 对 entity 进行排名，保持原有的顺序不变
    product_view_entity_list, product_view_cw1_top1 = rank_special_items(product_view_entity_list, filter_function_p, 'po_gap_cw1', ['sub_lob'])
    product_view_entity_list, product_view_cw2_top1 = rank_special_items(product_view_entity_list, filter_function_p, 'po_gap_cw2', ['sub_lob'])
    
    # 生成summary信息
    '''
    [
        {
            "display_name": "CW+1",
            "total": 0,
            "channel_view_top1": None,
            "product_view_top1": None
        },
        {
            "display_name": "CW+2",
            "total": 0,
            "channel_view_top1": None,
            "product_view_top1": None
        }
    ]
    '''
    summary_list = []
    if channel_view_entity_list and channel_view_entity_list[0].po_gap_cw1 == 0:
        summary_list.append(
            {
                "display_name": "CW+1",
                "total": "0",
            }
        )
    else:
        summary_list.append(
            {
                "display_name": "CW+1",
                "total": format_number_with_thousands_separator(channel_view_entity_list[0].po_gap_cw1),
                "gap_percentage": channel_view_entity_list[0].gap_percentage_cw1,
                "channel_view_top1": {
                    # "rtm": channel_view_cw1_top1.rtm,
                    "sub_rtm": "; ".join(item.sub_rtm for item in channel_view_cw1_top1),
                    "po_gap": format_number_with_thousands_separator(channel_view_cw1_top1[0].po_gap_cw1 if len(channel_view_cw1_top1) else 0),
                    "po_gap_percentage": channel_view_cw1_top1[0].gap_percentage_cw1 if len(channel_view_cw1_top1) else "0%"
                },
                "product_view_top1": {
                    "sub_lob": "; ".join(item.sub_lob for item in product_view_cw1_top1),
                    "po_gap": format_number_with_thousands_separator(product_view_cw1_top1[0].po_gap_cw1 if len(product_view_cw1_top1) else 0),
                    "po_gap_percentage": product_view_cw1_top1[0].gap_percentage_cw1 if len(product_view_cw1_top1) else "0%"
                }
            }
        )
    if channel_view_entity_list and channel_view_entity_list[0].po_gap_cw2 == 0:
        summary_list.append(
            {
                "display_name": "CW+2",
                "total": "0",
            }
        )
    else:
        summary_list.append(
            {
                "display_name": "CW+2",
                "total": format_number_with_thousands_separator(channel_view_entity_list[0].po_gap_cw2),
                "gap_percentage": channel_view_entity_list[0].gap_percentage_cw2,
                "channel_view_top1": {
                    # "rtm": channel_view_cw2_top1.rtm,
                    "sub_rtm": "; ".join(item.sub_rtm for item in channel_view_cw2_top1),
                    "po_gap": format_number_with_thousands_separator(channel_view_cw2_top1[0].po_gap_cw2 if len(channel_view_cw2_top1) else 0),
                    "po_gap_percentage": channel_view_cw2_top1[0].gap_percentage_cw2 if len(channel_view_cw2_top1) else "0%"
                },
                "product_view_top1": {
                    "sub_lob": "; ".join(item.sub_lob for item in product_view_cw2_top1),
                    "po_gap": format_number_with_thousands_separator(product_view_cw2_top1[0].po_gap_cw2 if len(product_view_cw2_top1) else 0),
                    "po_gap_percentage": product_view_cw2_top1[0].gap_percentage_cw2 if len(product_view_cw2_top1) else "0%"
                }
            }
        )
    ret = PoGapEntity(
        channel_view=channel_view_entity_list,
        product_view=product_view_entity_list,
        summary=summary_list,
        latest_refresh_time=latest_refresh_time
    )
    return ret.to_dict()


def get_po_gap_rtm_email_data(rtm: str):
    ''' RTM版本的数据'''
    max_fiscal_week = PoGapDi.get_max_fiscal_qtr_week_name()
    # 需要先查询所有的sub_lob,否则会有多余sub_lob=All的数据
    sub_lobs = PoGapDi.query_distinct_sublob("China mainland", "All", "All", "iPhone", fiscal_week=max_fiscal_week.get("fiscal_qtr_week_name"))
    
    # 获取dashboard中的数据
    latest_refresh_time, sold_to_view_list = get_sold_to_view_data(
        region="China mainland", lob='iPhone', sub_lobs=sub_lobs, rtm=rtm, fiscal_week=max_fiscal_week.get("fiscal_qtr_week_name")
    )
    
    # 平铺开三层数据
    sold_to_view_list = extract_third_layer(sold_to_view_list)
    
    sold_to_view_list = process_data(sold_to_view_list)
        
    # 转换成sold_to view entity
    sold_to_view_entity_list = []
    for item in sold_to_view_list:
        sold_to_view_entity_list.append(
            PoGapSoldtoViewEntity(
                rtm=item['rtm'],
                sub_rtm=item['sub_rtm'],
                sold_to_id=item['sold_to_id'],
                sold_to_name=item['sold_to_name'],
                demand_cw1=item['cw1_demand'],
                open_po_cw1=item['available_po_cw1'],
                po_gap_cw1=item['po_needed_cw1'],
                demand_cw2=item['cw2_demand'],
                open_po_cw2=item['available_po_cw2'],
                po_gap_cw2=item['po_needed_cw2']
            )
        )
    # 定义筛选条件，例如 sub_rtm 不为 'Total'
    filter_function = lambda x: x.sub_rtm != 'All' and x.sold_to_name != "All"
    # 对 entity 进行排名，保持原有的顺序不变
    sold_to_view_entity_list, sold_to_view_cw1_top1 = rank_special_items(sold_to_view_entity_list, filter_function, 'po_gap_cw1', ['rtm', 'sub_rtm', "sold_to_id"])
    sold_to_view_entity_list, sold_to_view_cw2_top1 = rank_special_items(sold_to_view_entity_list, filter_function, 'po_gap_cw2', ['rtm', 'sub_rtm', "sold_to_id"])
    
    # 处理product_view的数据
    latest_refresh_time_p, product_view_list = get_product_view_data(
        region="China mainland", rtm=rtm, sub_rtm='All',lob='iPhone', sub_lobs=sub_lobs, fiscal_week=max_fiscal_week.get("fiscal_qtr_week_name")
    )
    # 只取第一层的数据
    product_view_list = extract_first_layer(product_view_list)
    
    # 转换成product view entity
    product_view_entity_list = [
        PoGapProductViewEntity(
            sub_lob=item['sub_lob'],
            demand_cw1=item['cw1_demand'],
            open_po_cw1=item['available_po_cw1'],
            po_gap_cw1=item['po_needed_cw1'],
            demand_cw2=item['cw2_demand'],
            open_po_cw2=item['available_po_cw2'],
            po_gap_cw2=item['po_needed_cw2'],
        ) for item in product_view_list
    ]
    filter_function_p = lambda x: x.sub_lob != 'Total'
    # 对 entity 进行排名，保持原有的顺序不变
    product_view_entity_list, product_view_cw1_top1 = rank_special_items(product_view_entity_list, filter_function_p, 'po_gap_cw1', ['sub_lob'])
    product_view_entity_list, product_view_cw2_top1 = rank_special_items(product_view_entity_list, filter_function_p, 'po_gap_cw2', ['sub_lob'])
    
    # 生成summary信息
    '''
    [
        {
            "display_name": "CW+1",
            "total": 0,
            "channel_view_top1": None,
            "product_view_top1": None
        },
        {
            "display_name": "CW+2",
            "total": 0,
            "channel_view_top1": None,
            "product_view_top1": None
        }
    ]
    '''
    summary_list = []
    if sold_to_view_entity_list and sold_to_view_entity_list[0].po_gap_cw1 == 0:
        summary_list.append(
            {
                "display_name": "CW+1",
                "total": "0",
            }
        )
    else:
        summary_list.append(
            {
                "display_name": "CW+1",
                "total": format_number_with_thousands_separator(sold_to_view_entity_list[0].po_gap_cw1),
                "gap_percentage": sold_to_view_entity_list[0].gap_percentage_cw1,
                "channel_view_top1": {
                    "sub_rtm": "; ".join(item.sold_to_name for item in sold_to_view_cw1_top1 if item.sold_to_name is not None),
                    "po_gap": format_number_with_thousands_separator(sold_to_view_cw1_top1[0].po_gap_cw1 if len(sold_to_view_cw1_top1) else 0),
                    "po_gap_percentage": sold_to_view_cw1_top1[0].gap_percentage_cw1 if len(sold_to_view_cw1_top1) else "0%"
                },
                "product_view_top1": {
                    "sub_lob": "; ".join(item.sub_lob for item in product_view_cw1_top1),
                    "po_gap": format_number_with_thousands_separator(product_view_cw1_top1[0].po_gap_cw1 if len(product_view_cw1_top1) else 0),
                    "po_gap_percentage": product_view_cw1_top1[0].gap_percentage_cw1 if len(product_view_cw1_top1) else "0%"
                }
            }
        )
    if sold_to_view_entity_list and sold_to_view_entity_list[0].po_gap_cw2 == 0:
        summary_list.append(
            {
                "display_name": "CW+2",
                "total": "0",
            }
        )
    else:
        summary_list.append(
            {
                "display_name": "CW+2",
                "total": format_number_with_thousands_separator(sold_to_view_entity_list[0].po_gap_cw2),
                "gap_percentage": sold_to_view_entity_list[0].gap_percentage_cw2,
                "channel_view_top1": {
                    "sub_rtm": "; ".join(item.sold_to_name for item in sold_to_view_cw2_top1 if item.sold_to_name is not None),
                    "po_gap": format_number_with_thousands_separator(sold_to_view_cw2_top1[0].po_gap_cw2 if len(sold_to_view_cw2_top1) else 0),
                    "po_gap_percentage": sold_to_view_cw2_top1[0].gap_percentage_cw2 if len(sold_to_view_cw2_top1) else "0%"
                },
                "product_view_top1": {
                    "sub_lob": "; ".join(item.sub_lob for item in product_view_cw2_top1),
                    "po_gap": format_number_with_thousands_separator(product_view_cw2_top1[0].po_gap_cw2 if len(product_view_cw2_top1) else 0),
                    "po_gap_percentage": product_view_cw2_top1[0].gap_percentage_cw2 if len(product_view_cw2_top1) else "0%"
                }
            }
        )
    ret = PoGapRTMEntity(
        channel_view=sold_to_view_entity_list,
        product_view=product_view_entity_list,
        summary=summary_list,
        latest_refresh_time=latest_refresh_time
    )
    return ret.to_dict()


def whether_to_send_email_for_po_gap(send_date: str):
    '''
    当周有publish状态的demand数据，才能发送邮件。
    如何判断当周是否有publish的数据？
    查询发送日期所在周，与查询表中最新的发布周相等，则代表当周有publish的数据，即可发送邮件。
    '''
    try:
        max_fiscal_week = PoGapDi.get_max_fiscal_qtr_week_name()
        send_date_week = FiscalYearWeek.get_week_by_date(send_date)
        return send_date_week.get("fiscal_qtr_week_name") == max_fiscal_week.get("fiscal_qtr_week_name")
    except Exception as e:
        logger.error(f"Po Gap 没有publish的demand数据")
        logger.exception(e)
        return False