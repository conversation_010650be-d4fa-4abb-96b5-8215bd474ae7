# 使用dashboard中的数据，处理成邮件中需要展示的内容
from util.conf import logger
from data.databend.dashboard.dashboard_po_delinquent import PoDelinquentDi
from data.fiscal_year_week import FiscalYearWeek
from domain.dashboard.impl.po_delinquent_impl import get_channel_view_data, get_product_view_data, get_sold_to_view_data
from domain.email.entity.email_po_delinquent_entity import (
    PoDelinquentChannelViewEntity,
    PoDelinquentEntity,
    PoDelinquentProductViewEntity,
    PoDelinquentRTMEntity,
    PoDelinquentRTMSoldtoViewEntity,
    rank_special_items
)
from kit.format_numer_with_thousands_separator import format_number_with_thousands_separator


def extract_first_layer(tree_list):
    first_layer = []
    for tree in tree_list:
        first_layer.append({k: v for k, v in tree.items() if k != 'child'})
    return first_layer


def extract_second_layer(tree_list):
    second_layer = []
    # 检查根节点是否有子节点
    for tree in tree_list:
        if 'child' in tree:
            for child in tree['child']:
                child_info = {k: v for k, v in child.items() if k != 'child'}
                second_layer.append(child_info)
    return second_layer

def extract_third_layer(tree_list):
    third_layer = []
    # 遍历第一层节点
    for tree in tree_list:
        # 检查第一层节点是否有子节点（第二层节点）
        if 'child' in tree:
            for second_child in tree['child']:
                # 检查第二层节点是否有子节点（第三层节点）
                if 'child' in second_child and len(second_child['child']):
                    # 要先展开第二层
                    third_child_info = {k: v for k, v in second_child.items() if k != 'child'}
                    third_layer.append(third_child_info)
                    for third_child in second_child['child']:
                        # 提取第三层节点信息，排除 'child' 键
                        third_child_info = {k: v for k, v in third_child.items() if k != 'child'}
                        third_layer.append(third_child_info)
                else:
                    third_child_info = {k: v for k, v in second_child.items() if k != 'child'}
                    third_layer.append(third_child_info)
    return third_layer


def get_po_delinquent_email_data():
    # 获取dashboard中的数据
    latest_refresh_time, channel_view_list = get_channel_view_data(
        region="China mainland", lob='iPhone', sub_lobs=None, rtm=None
    )
    # 只取第二层数据
    channel_view_list = extract_second_layer(channel_view_list)
    # 转换成channel view entity
    channel_view_entity_list = []
    for item in channel_view_list:
        channel_view_entity_list.append(
            PoDelinquentChannelViewEntity(
                rtm=item['rtm'],
                sub_rtm=item['sub_rtm'], 
                demand_next_2_weeks=item["2wks_total_demand"],
                # cum_po=item['cum_po'],
                open_po=item['open_po'],
                open_po_condition1=item['cond_1'],
                open_po_condition2=item['cond_2'],
                open_po_condition3=item['cond_3'],
                open_po_condition4=item['cond_4'],
                open_po_condition5=item['cond_5']
            )
        )
    # 定义筛选条件，例如 sub_rtm 不为 'Total'
    filter_function = lambda x: x.rtm != 'Total' and x.sub_rtm != 'All'
    # 对 entity 进行排名，保持原有的顺序不变
    channel_view_entity_list, channel_view_top1 = rank_special_items(channel_view_entity_list, filter_function, 'rank_data', ['rtm', 'sub_rtm'])
    
    # 处理product_view的数据
    latest_refresh_time_p, product_view_list = get_product_view_data(
        region="China mainland", rtm='All', sub_rtm='All',lob='iPhone', sub_lobs=None
    )
    # 只取第一层的数据
    product_view_list = extract_first_layer(product_view_list)
    
    # 转换成product view entity
    product_view_entity_list = [
        PoDelinquentProductViewEntity(
            sub_lob=item['sub_lob'],
            demand_next_2_weeks=item["2wks_total_demand"],
            # cum_po=item['cum_po'],
            open_po=item['open_po'],
            open_po_condition1=item['cond_1'],
            open_po_condition2=item['cond_2'],
            open_po_condition3=item['cond_3'],
            open_po_condition4=item['cond_4'],
            open_po_condition5=item['cond_5'],
        ) for item in product_view_list
    ]
    filter_function_p = lambda x: x.sub_lob != 'Total'
    # 对 entity 进行排名，保持原有的顺序不变
    product_view_entity_list, product_view_top1 = rank_special_items(product_view_entity_list, filter_function_p, 'rank_data', ['sub_lob'])
    
    # 生成summary信息
    '''
    {
        "open_po": None,
        "open_po_gap": None,
        "rank_data": None,
        "channel_view_top1": None,
        "product_view_top1": None
    }
    '''
    summary_dict = {}
    summary_dict['open_po'] = format_number_with_thousands_separator(channel_view_entity_list[0].open_po)
    summary_dict['open_po_gap'] = format_number_with_thousands_separator(channel_view_entity_list[0].open_po - channel_view_entity_list[0].demand_next_2_weeks)
    summary_dict['rank_data'] =  format_number_with_thousands_separator(channel_view_entity_list[0].rank_data)
    summary_dict['channel_view_top1'] = {
        "sub_rtm": "; ".join(item.sub_rtm for item in channel_view_top1), # 相同排名的渠道，用分号隔开
        "rank_data": format_number_with_thousands_separator(channel_view_top1[0].rank_data if len(channel_view_top1) else 0)
    }
    summary_dict['product_view_top1'] = {
        "sub_lob": "; ".join(item.sub_lob for item in product_view_top1), # 相同排名的产品，用分号隔开
        "rank_data": format_number_with_thousands_separator(product_view_top1[0].rank_data if len(product_view_top1) else 0)
    }
    
    ret = PoDelinquentEntity(
        channel_view=channel_view_entity_list,
        product_view=product_view_entity_list,
        summary=summary_dict,
        latest_refresh_time=latest_refresh_time
    ).to_dict()
    return ret


def process_data(data):
    sub_rtm_groups = {}
    for row in data:
        rtm = row["rtm"]
        sub_rtm = row['sub_rtm']
        zero_sum_dict = {} if rtm != "Mono" or sub_rtm == 'All' else {
                    "lob": row["lob"],
                    "region": row["region"],
                    "rtm": row["rtm"],
                    "sub_rtm": sub_rtm,
                    "sold_to_id": "",
                    "sold_to_name": "其他",
                    "2wks_total_demand": 0,
                    "cond_1": 0,
                    "cond_2": 0,
                    "cond_3": 0,
                    "cond_4": 0,
                    "cond_5": 0,
                    "open_po": 0,
                    "others": 0
                }
        if sub_rtm not in sub_rtm_groups:
            sub_rtm_groups[sub_rtm] = {
                "non_zero": [],
                "zero_sum": zero_sum_dict
            }
        open_po_3_4_wks = row.get("cond_4", 0)
        open_po_over_4_wks = row.get("cond_5", 0)
        if rtm != "Mono" or (open_po_3_4_wks != 0 and open_po_3_4_wks >= 50) or (open_po_over_4_wks != 0 and open_po_over_4_wks >= 50) or row["sub_rtm"] == 'All' or (row['rtm'] == row['sub_rtm'] and row["sold_to_name"] == 'All'):
            new_row = row.copy()
            sub_rtm_groups[sub_rtm]["non_zero"].append(new_row)
        else:
            if len(sub_rtm_groups[sub_rtm]["zero_sum"]) > 0:
                sub_rtm_groups[sub_rtm]["zero_sum"]["2wks_total_demand"] += row.get("2wks_total_demand", 0)
                sub_rtm_groups[sub_rtm]["zero_sum"]["open_po"] += row.get("open_po", 0)
                sub_rtm_groups[sub_rtm]["zero_sum"]["cond_1"] += row.get("cond_1", 0)
                sub_rtm_groups[sub_rtm]["zero_sum"]["cond_2"] += row.get("cond_2", 0)
                sub_rtm_groups[sub_rtm]["zero_sum"]["cond_3"] += row.get("cond_3", 0)
                sub_rtm_groups[sub_rtm]["zero_sum"]["cond_4"] += open_po_3_4_wks
                sub_rtm_groups[sub_rtm]["zero_sum"]["cond_5"] += open_po_over_4_wks
                sub_rtm_groups[sub_rtm]["zero_sum"]["others"] += 1

    final_result = []
    for sub_rtm, group in sub_rtm_groups.items():
        non_zero_rows = sorted(group["non_zero"], key=lambda x: (x["sold_to_name"] != "All", x["sold_to_name"] is None, x["sold_to_name"] if x["sold_to_name"] not in [None, "All"] else ""))
        final_result.extend(non_zero_rows)
        if len(group["zero_sum"]) > 0 and group["zero_sum"]["others"] > 0:
            other_row = {
                "lob": group["zero_sum"]["lob"],
                "region": group["zero_sum"]["region"],
                "rtm": group["zero_sum"]["rtm"],
                "sub_rtm":  group["zero_sum"]["sub_rtm"],
                "sold_to_id": "",
                "sold_to_name": "其他",
                "2wks_total_demand": group["zero_sum"]["2wks_total_demand"],
                "open_po": group["zero_sum"]["open_po"],
                "cond_1": group["zero_sum"]["cond_1"],
                "cond_2": group["zero_sum"]["cond_2"],
                "cond_3": group["zero_sum"]["cond_3"],
                "cond_4": group["zero_sum"]["cond_4"],
                "cond_5": group["zero_sum"]["cond_5"],
            }
            final_result.append(other_row)

    return final_result


def get_po_delinquent_rtm_email_data(rtm: str):
    ''' RTM版本的数据'''
    # 获取dashboard中的数据
    latest_refresh_time, sold_to_view_list = get_sold_to_view_data(
        region="China mainland", lob='iPhone', sub_lobs=None, rtm=rtm
    )
    # 去掉Total的数据
    sold_to_view_list = [item for item in sold_to_view_list if item['rtm'] != 'Total']
    # 平铺开三层数据
    sold_to_view_list = extract_third_layer(sold_to_view_list)
    '''
    特殊处理：同一个sub_rtm下且sold_name!='All'的数据需要进行特殊处理
    * 3-4 wks / >4 wks 任意一周Open PO结果不为0的经销商单独列出一行。
        * 名称按首字母排序。
        * 经销商名称以FAST - CPF Data Source Reseller Mapping下的Abbre.列为准。
    * 3-4 wks / >4 wks 的Open PO均为0的经销商数据合并为“其它”行展示。
    '''
    # 20250310 只有Mono才展示其他
    sold_to_view_list = process_data(sold_to_view_list)
    
    # 转换成channel view entity
    sold_to_view_entity_list = []
    for item in sold_to_view_list:
        sold_to_view_entity_list.append(
            PoDelinquentRTMSoldtoViewEntity(
                rtm=item['rtm'],
                sub_rtm=item['sub_rtm'],
                sold_to_id=item.get('sold_to_id', ""),
                sold_to_name=item['sold_to_name'],
                demand_next_2_weeks=item["2wks_total_demand"],
                open_po=item['open_po'],
                open_po_condition1=item['cond_1'],
                open_po_condition2=item['cond_2'],
                open_po_condition3=item['cond_3'],
                open_po_condition4=item['cond_4'],
                open_po_condition5=item['cond_5']
            )
        )
    # 定义筛选条件，例如 sub_rtm 不为 'Total'
    filter_function = lambda x: x.sub_rtm != 'All' and x.sold_to_name != "All"
    # 对 entity 进行排名，保持原有的顺序不变
    sold_to_view_entity_list, sold_to_view_top1 = rank_special_items(sold_to_view_entity_list, filter_function, 'rank_data', ['rtm', 'sub_rtm', 'sold_to_id'])
    
    # 处理product_view的数据
    latest_refresh_time_p, product_view_list = get_product_view_data(
        region="China mainland", rtm=rtm, sub_rtm='All',lob='iPhone', sub_lobs=None
    )
    # 只取第一层的数据
    product_view_list = extract_first_layer(product_view_list)
    
    # 转换成product view entity
    product_view_entity_list = [
        PoDelinquentProductViewEntity(
            sub_lob=item['sub_lob'],
            demand_next_2_weeks=item["2wks_total_demand"],
            open_po=item['open_po'],
            open_po_condition1=item['cond_1'],
            open_po_condition2=item['cond_2'],
            open_po_condition3=item['cond_3'],
            open_po_condition4=item['cond_4'],
            open_po_condition5=item['cond_5'],
        ) for item in product_view_list
    ]
    filter_function_p = lambda x: x.sub_lob != 'Total'
    # 对 entity 进行排名，保持原有的顺序不变
    product_view_entity_list, product_view_top1 = rank_special_items(product_view_entity_list, filter_function_p, 'rank_data', ['sub_lob'])
    
    # 生成summary信息
    '''
    {
        "open_po": None,
        "open_po_gap": None,
        "rank_data": None,
        "sold_to_view_top1": None,
        "product_view_top1": None
    }
    '''
    summary_dict = {}
    summary_dict['open_po'] = format_number_with_thousands_separator(sold_to_view_entity_list[0].open_po if len(sold_to_view_entity_list) else 0)
    summary_dict['open_po_gap'] = format_number_with_thousands_separator((sold_to_view_entity_list[0].open_po - sold_to_view_entity_list[0].demand_next_2_weeks) if len(sold_to_view_entity_list) else 0)
    summary_dict['rank_data'] =  format_number_with_thousands_separator(sold_to_view_entity_list[0].rank_data if len(sold_to_view_entity_list) else 0)
    summary_dict['sold_to_view_top1'] = {
        "sub_rtm": "; ".join(item.sold_to_name for item in sold_to_view_top1 if item.sold_to_name is not None), # 相同排名的渠道，用分号隔开
        "rank_data": format_number_with_thousands_separator(sold_to_view_top1[0].rank_data if len(sold_to_view_top1) else 0)
    }
    summary_dict['product_view_top1'] = {
        "sub_lob": "; ".join(item.sub_lob for item in product_view_top1), # 相同排名的产品，用分号隔开
        "rank_data": format_number_with_thousands_separator(product_view_top1[0].rank_data if len(product_view_top1) else 0)
    }
    
    ret = PoDelinquentRTMEntity(
        sold_to_view=sold_to_view_entity_list,
        product_view=product_view_entity_list,
        summary=summary_dict,
        latest_refresh_time=latest_refresh_time
    ).to_dict()
    return ret


def whether_to_send_email_for_po_delinquent(send_date: str):
    '''
    当周有publish状态的demand数据，才能发送邮件。
    如何判断当周是否有publish的数据？
    查询发送日期所在周，与查询表中最新的发布日期所在周相等，则代表当周有publish的数据，即可发送邮件。
    '''
    try:
        fiscal_dt = PoDelinquentDi.get_max_fiscal_dt()
        po_delinquent_week = FiscalYearWeek.get_week_by_date(fiscal_dt.get("max_fiscal_dt"))
        send_date_week = FiscalYearWeek.get_week_by_date(send_date)
        return send_date_week == po_delinquent_week
    except Exception as e:
        logger.error(f"Po Delinquent 没有publish的demand数据")
        logger.exception(e)
        return False
