from data.cpf_data_source import OdsFastCPFSoldToMappingIPhone


def get_iphone_soldto_mapping()->dict:
    '''
    获取sold_to_mapping的数据
    @return:
    {
        "sold_to_id": "sold_to_name_abbre"
    }
    '''
    sold_to_mapping_data = OdsFastCPFSoldToMappingIPhone.query_soldto_by_max_version()
    sold_to_mapping_dict = dict([(item["sold_to_id"], item["sold_to_name_abbre"]) for item in sold_to_mapping_data])
    '''2025-03-12 PRD 变更
    * 对于Channel Online的经销商，其经销商名称固定写为(区分经销商id匹配即可)
    * Sold-to ID = 1118510 ：SHEN ZHOU
    * Sold-to ID = 1520447： BAO TONG
    * Sold-to ID = 1255322：TIAN LIAN
    * Sold-to ID = 1426316：HENG SHA
    '''
    channel_online_special_reseller_dict = {
        "1118510": "SHEN ZHOU",
        "1520447": "BAO TONG",
        "1255322": "TIAN LIAN",
        "1426316": "HENG SHA",
    }
    sold_to_mapping_dict.update(channel_online_special_reseller_dict)
    return sold_to_mapping_dict
