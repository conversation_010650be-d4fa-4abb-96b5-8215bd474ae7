########## must at head ############
import sys
import os

import pytest

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(SCRIPT_DIR))
##########      end     ############

from domain.email.impl.email_po_gap_impl import get_po_gap_email_data, get_po_gap_rtm_email_data
from domain.email.impl.email_po_delinquent_impl import get_po_delinquent_email_data, get_po_delinquent_rtm_email_data

@pytest.mark.skipif(True, reason="skip")
def test_get_po_gap_email_data():
    ret = get_po_gap_email_data()
    print(ret)


@pytest.mark.skipif(True, reason="skip")
def test_get_po_delinquent_email_data():
    ret = get_po_delinquent_email_data()
    print(ret)

@pytest.mark.skipif(True, reason="skip")
def test_get_po_gap_rtm_email_data():
    ret = get_po_gap_rtm_email_data('Education')
    print(ret)

@pytest.mark.skipif(False, reason="skip")
def test_get_po_delinquent_rtm_email_data():
    ret = get_po_delinquent_rtm_email_data('Carrier')
    print(ret)
