from domain.dashboard.impl.channel_compliance import *
from datetime import datetime
from domain.dashboard.impl.channel_compliance.ub_tracking_model import get_ub_tracking_node
from util.const import ErrCode, ErrorExcept, ALL
from util.util import calculate_division


def get_ub_tracking_view(snapshot_date: str, rtm: str,
                         all_platform_role_names: bool,
                         permission_rtm_list: list[str],
                         lob: str, sub_lobs: list[str],
                         tab: str, data_type: str):
    tracking_node = get_ub_tracking_node(data_type, tab)
    if not tracking_node:
        raise ErrorExcept(ErrCode.Param, "tab or data type is incorrect.")
    level1_wi_props, level2_wi_props, level3_wi_props = tracking_node.get_wi_props()
    query_rtms = tracking_node.get_rtm_list()
    if rtm != ALL:
        query_rtms = [rtm]
    else:
        query_rtms = list(set(permission_rtm_list) & set(query_rtms))
    tbl_info = tracking_node.get_tbl_cls()

    # dynamic_columns 确认其他属性是否需要处理
    dynamic_columns = tbl_info.get_dynamic_info_by_snapshot_date(snapshot_date)
    ret = {
        "last_updated": dynamic_columns["last_updated"].strftime('%Y-%m-%d %H:%M:%S'),
        "columns": cst_columns(dynamic_columns),
        "is_monday": datetime.strptime(snapshot_date, "%Y-%m-%d").weekday() == 0,
        'detail_data': []
    }

    nd_types = [NdTypeT1, NdTypeNd] if tab == UbTabType.ND_T1.value else None
    db_data = tbl_info.query_tgt_wi_by_snapshot_date(snapshot_date, query_rtms,
                                                     lob, sub_lobs, level3_wi_props, nd_types)


    # 填充数据库缺失的数据
    db_data = fill_missing_data(db_data, query_rtms, tracking_node, level3_wi_props)
    # 过滤不展示的数据
    db_data = tracking_node.filter_data(db_data)
    is_show_total = all_platform_role_names and rtm == ALL and tab == UbTabType.RTM.value
    total_map, level1_map = \
        aggregate_level_data(db_data, level1_wi_props, level2_wi_props, level3_wi_props, tab, is_show_total)

    detail_data = assemble_detail_data(total_map, level1_map, is_show_total)
    # 删除掉一些子集
    detail_data = tracking_node.remove_some_subset(detail_data)
    # 排序 detail_data
    sort_data(detail_data, level1_wi_props, level2_wi_props, level3_wi_props, tab)
    ret['detail_data'] = detail_data
    return ret


def aggregate_level_data(source_data: list, level1_wi_props: list, level2_wi_props: list, level3_wi_props: list,
                         tab: str, is_show_total: bool):
    total_map, level1_map = {KeyItemName: DISPLAY_GC_NAME, 'child_map': {}}, {}
    for item in source_data:
        level1_key = StrikethroughChar.join([item[p] for p in level1_wi_props])
        level2_key = StrikethroughChar.join([item[p] for p in level2_wi_props])
        level3_key = StrikethroughChar.join([item[p] for p in level3_wi_props])
        if PropSublob in item:
            item[PropSublob] = item[PropSublob].lstrip('iPhone ')
        l1_name_props = [PropSubRtm, PropHqName] if tab == UbTabType.ND_T1.value else level1_wi_props[-1:]
        calcu_item_map(level1_map, cst_item_name(item, l1_name_props), level1_key, item)
        calcu_item_map(level1_map[level1_key]['child_map'],
                       cst_item_name(item, level2_wi_props[-1:]), level2_key, item)
        calcu_item_map(level1_map[level1_key]['child_map'][level2_key]['child_map'],
                       cst_item_name(item, level3_wi_props[-1:]),
                       level3_key,
                       item)
        if is_show_total:
            level2_name = item[level2_wi_props[-1]]
            level3_name = item[level3_wi_props[-1]]
            total_level2_key = level2_name
            total_level3_key = f'{level2_name}{StrikethroughChar}{level3_name}'
            sum_total(total_map, item)
            calcu_item_map(total_map['child_map'], level2_name, total_level2_key, item)
            calcu_item_map(total_map['child_map'][total_level2_key]['child_map'], level3_name, total_level3_key, item)
    return total_map, level1_map


def cst_columns(dynamic_columns: dict):
    return {
        "qtd_current_quarter_name": dynamic_columns["qtd_current_quarter_name"],
        "qtd_last_quarter_name": dynamic_columns["qtd_last_quarter_name"],
        "weekly_trend_cw_name": dynamic_columns["weekly_trend_cw_name"],
        "weekly_trend_cw1_name": dynamic_columns["weekly_trend_cw1_name"],
        "weekly_trend_cw2_name": dynamic_columns["weekly_trend_cw2_name"],
        "weekly_trend_cw3_name": dynamic_columns["weekly_trend_cw3_name"],
        "weekly_trend_cw4_name": dynamic_columns["weekly_trend_cw4_name"],
        "last_weekly_trend_cw_name": dynamic_columns["last_weekly_trend_cw_name"] if dynamic_columns[
            "last_weekly_trend_cw_name"] else dynamic_columns["weekly_trend_cw_name"],
        "last_weekly_trend_cw1_name": dynamic_columns["last_weekly_trend_cw1_name"] if dynamic_columns[
            "last_weekly_trend_cw1_name"] else dynamic_columns["weekly_trend_cw1_name"]
    }


def calcu_item_map(data_map, cur_name, cur_key, data_item):
    if cur_key not in data_map:
        data_map[cur_key] = {KeyItemName: cur_name, 'child_map': {}}
    sum_total(data_map[cur_key], data_item)


def cst_item_name(cur_data: dict, name_props: list, separate_char: str = UbSeparateStr):
    names = [cur_data.get(p, '') for p in name_props]
    new_names = []
    for n in names:
        new_names.append(NameReplaceMap.get(n, n))
    return separate_char.join(new_names)


def sum_total(tgt_data: dict, item_data: dict):
    for k, v in item_data.items():
        cur_tgt_v = tgt_data.get(k)
        if v is not None and not isinstance(v, float) and not isinstance(v, int):
            continue
        if cur_tgt_v is not None and not isinstance(cur_tgt_v, float) and not isinstance(cur_tgt_v, int):
            continue
        if v is None:
            if k not in tgt_data:
                tgt_data[k] = None
            continue
        tgt_v = tgt_data.get(k, 0)
        if tgt_v is None:
            tgt_v = 0
        tgt_data[k] = tgt_v + v


def assemble_detail_data(total_map: dict, level1_map: dict, is_all: bool):
    result = []
    if is_all:
        level1_map[DISPLAY_GC_NAME] = total_map
    level1_list = level1_map.values()
    for level1_item in level1_list:
        l1_child_map = level1_item.get('child_map', {})
        l1_sub_list = []
        for l2_k, l2_item in l1_child_map.items():
            l2_child_map = l2_item.get('child_map', {})
            l2_sub_list = []
            for l3_k, l3_item in l2_child_map.items():
                l2_sub_list.append(cst_ubtracking_rate_unit(l3_item))
            l2_rate_unit = cst_ubtracking_rate_unit(l2_item)
            l2_rate_unit[KeyChild] = l2_sub_list
            l1_sub_list.append(l2_rate_unit)
        l1_rate_unit = cst_ubtracking_rate_unit(level1_item)
        l1_rate_unit[KeyChild] = l1_sub_list
        result.append(l1_rate_unit)
    return result


def cst_ubtracking_rate_unit(cur_item: dict):
    return {
        "item_name": cur_item[KeyItemName],
        "qtd_last_quarter_3d_rate": calculate_division(cur_item['ub3_acc_lq'], cur_item['total_so_acc_lq']),
        "qtd_last_quarter_7d_rate": calculate_division(cur_item['ub7_acc_lq'], cur_item['total_so_acc_lq']),
        "qtd_current_quarter_3d_rate": calculate_division(cur_item['ub3_acc_cq'], cur_item['total_so_acc_cq']),
        "qtd_current_quarter_7d_rate": calculate_division(cur_item['ub7_acc_cq'], cur_item['total_so_acc_cq']),
        "weekly_trend_cw_7d_rate": calculate_division(cur_item['ub7_cw'], cur_item['total_so_cw']),
        "weekly_trend_cw1_7d_rate": calculate_division(cur_item['ub7_cw1'], cur_item['total_so_cw1']),
        "weekly_trend_cw2_7d_rate": calculate_division(cur_item['ub7_cw2'], cur_item['total_so_cw2']),
        "weekly_trend_cw3_7d_rate": calculate_division(cur_item['ub7_cw3'], cur_item['total_so_cw3']),
        "weekly_trend_cw4_7d_rate": calculate_division(cur_item['ub7_cw4'], cur_item['total_so_cw4']),
        "last_weekly_trend_cw_7d_rate": calculate_division(cur_item['last_ub7_cw'], cur_item['last_total_so_cw']),
        "last_weekly_trend_cw1_7d_rate": calculate_division(cur_item['last_ub7_cw1'], cur_item['last_total_so_cw1'])
    }


def sort_data(level1_list, level1_wi_props, level2_wi_props, level3_wi_props, tab_type):
    if tab_type == UbTabType.ND_T1.value:
        level1_sort_list = SortMap.get(UbTabType.SUB_RTM.value)
        detail_data = sort_nd_t1_list(level1_list, level1_sort_list)
    else:
        level1_sort_list = SortMap.get(level1_wi_props[-1])
        detail_data = sort_common_list(level1_list, level1_sort_list)
    for level1_item in detail_data:
        level2_list = level1_item.get(KeyChild, [])
        level2_sort_list = SortMap.get(level2_wi_props[-1])
        level2_list = sort_common_list(level2_list, level2_sort_list)
        for level2_item in level2_list:
            level3_list = level2_item.get(KeyChild, [])
            level3_sort_list = SortMap.get(level3_wi_props[-1])
            sort_common_list(level3_list, level3_sort_list)
    return detail_data


def sort_common_list(data_list, sorted_arr):
    if sorted_arr:
        data_list.sort(key=lambda x: sorted_arr.index(x[KeyItemName]) if x[KeyItemName] in sorted_arr else float('inf'))
    return data_list


def sort_nd_t1_list(data_list, sorted_arr):
    def split_item_name(item_name):
        return item_name.split(UbSeparateStr)

    def get_sort_key(x, sorted_list):
        sub_rtm, nd_t1 = split_item_name(x[KeyItemName])
        sort_key_1 = sorted_list.index(sub_rtm) if sub_rtm in sorted_list else float('inf')
        sort_key_2 = nd_t1
        return sort_key_1, sort_key_2

    data_list.sort(key=lambda x: get_sort_key(x, sorted_arr))
    return data_list


def fill_missing_data(db_data: list, query_rtms: list, tracking_node, level3_wi_props) -> list:
    data_cache = {}
    item_name_list = []
    for rtm in query_rtms:
        display_rtm = db_rtm_to_display_rtm_dict[rtm]
        l1_l2_mappings = tracking_node.get_l1_l2_mapping(display_rtm)
        for l1_l2_mapping in l1_l2_mappings:
            for l1_item_name, l2_l3_item_name_dict in l1_l2_mapping.items():
                for l2_item_name, l3_item_list in l2_l3_item_name_dict.items():
                    for l3_item_name in l3_item_list:
                        l1_item_names = tracking_node.get_l1_item_names(rtm, l1_item_name, db_data)
                        for item_names in l1_item_names:
                            item_name_list.append(item_names + [l2_item_name, l3_item_name])
    for values in item_name_list:
        add_data_to_cache(data_cache, values, level3_wi_props)

    missing_data_list = get_missing_data_list(data_cache, db_data, level3_wi_props)
    return db_data + missing_data_list

def get_missing_data_list(data_cache, db_data, level3_wi_props):
    for db_item in db_data:
        data_key = StrikethroughChar.join([db_item[p] for p in level3_wi_props])
        if data_key in data_cache:
            data_cache.pop(data_key)
    return list(data_cache.values())

def add_data_to_cache(data_cache, values, keys):
    data_key = StrikethroughChar.join(values)
    if data_key in data_cache:
        return
    data_item = {}
    for i in range(len(values)):
        data_item[keys[i]] = values[i]
    for numeratorField in numeratorFields:
        data_item[numeratorField] = None
    for denominatorField in denominatorFields:
        data_item[denominatorField] = None
    data_cache[data_key] = data_item