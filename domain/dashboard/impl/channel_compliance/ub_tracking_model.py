from data.databend.dashboard.channel_compliance.partner_overall_ub_tracking import PartnerOverallUbTracking
from data.databend.dashboard.channel_compliance.partner_store_type_ub_tracking import PartnerStoreTypeUbTracking
from data.databend.dashboard.channel_compliance.ub_tracking_online_platform_data import TblOnlinPlatformeUbTracking
from domain.dashboard.impl.channel_compliance import *
from util.const import StrRTMCarrier, CARRIER_SUB_RTM


class UbTrackingNode:

    def get_wi_props(self):
        return None, None, None

    def get_tbl_cls(self):
        return None

    def get_rtm_list(self):
        return None

    def remove_some_subset(self, detail_data):
        return detail_data

    def filter_data(self, db_data):
        return db_data

    def get_l1_l2_mapping(self, rtm:str) -> list:
        return []

    def get_l1_item_names(self, rtm: str, sub_rtm: str, db_data) -> list:
        return []


class OnlinePlatformRtmNode(UbTrackingNode):
    def get_wi_props(self):
        level1_wi_props = [PropRtm]
        level2_wi_props = level1_wi_props + [PropOnlineOffline]
        level3_wi_props = level2_wi_props + [PropPlatform]
        return level1_wi_props, level2_wi_props, level3_wi_props

    def get_tbl_cls(self):
        return TblOnlinPlatformeUbTracking

    def get_rtm_list(self):
        return RTM_PERMISSION_TO_DB_QUERY_RTM_MAPPING.values()

    def remove_some_subset(self, detail_data):
        """
            二级只有online展开
        """
        for l1_item in detail_data:
            l2_list = l1_item.get(KeyChild, [])
            remove_some_item(l2_list, not_online=True)
        return detail_data

    def filter_data(self, db_data):
        """
            Channel Online 只有Online，无Offline
            如果某个RTM，Online下的Platform，除了Others，其他平台数据都为空，则不展示Online Total 行，和Others行；只展示Offline
        """
        level1_wi_props, level2_wi_props, level3_wi_props = self.get_wi_props()
        res = []
        platform_dict = {} # key是RTM，value是Online下的Platform item
        for item in db_data:
            l1_item_name = item.get(level3_wi_props[0])
            l2_item_name = item.get(level3_wi_props[1])
            if l1_item_name == StrChannelOnline and l2_item_name == StrOffline:
                continue
            if l2_item_name == StrOnline:
                if l1_item_name not in platform_dict:
                    platform_dict[l1_item_name] = []
                platform_dict[l1_item_name].append(item)
                continue
            res.append(item)

        for l3_list in platform_dict.values():
            other_item_not_none = False
            for l3 in l3_list:
                l1_item_name = l3.get(level3_wi_props[0])
                if l1_item_name == DB_RTM_EDU:
                    # edu类型特殊处理，不隐藏
                    other_item_not_none = True
                    break
                if l3[level3_wi_props[2]] != StrOthers and not division_is_none(l3):
                    other_item_not_none = True
                    break
            if other_item_not_none:
                res = res + l3_list
        return res


    def get_l1_l2_mapping(self, rtm:str) -> list:
        return [RTM_SUB_RTM_PLATFORM_MAPPING[rtm]]

    def get_l1_item_names(self, rtm: str, sub_rtm: str, db_data) -> list:
        return [[rtm]]


class OnlinePlatformSubRtmNode(UbTrackingNode):
    def get_wi_props(self):
        
        level1_wi_props = [PropRtm, PropSubRtm]
        level2_wi_props = level1_wi_props + [PropOnlineOffline]
        level3_wi_props = level2_wi_props + [PropPlatform]
        return level1_wi_props, level2_wi_props, level3_wi_props

    def get_tbl_cls(self):
        return TblOnlinPlatformeUbTracking

    def get_rtm_list(self):
        return [DB_RTM_MULTI, DB_RTM_Mono, DB_RTM_CARRIER]

    def remove_some_subset(self, detail_data):
        """
            二级只有online展开
        """
        for l1_item in detail_data:
            l2_list = l1_item.get(KeyChild, [])
            remove_some_item(l2_list, not_online=True)
        return detail_data

    def filter_data(self, db_data):
        """
            OTC无Online，仅有Offline
            China BroadNet 无Online，仅有Offline
            Carrier的四个Sub-RTM额外增加Unauthorized，如果某个Sub-RTM Unauthorized 展示的数据均为“-”，则该行隐藏不展示
        """

        level1_wi_props, level2_wi_props, level3_wi_props = self.get_wi_props()
        return filter_list(db_data, level3_wi_props[1], level3_wi_props[2], otc=True, cb=True, carrier_unauthorized=True)

    def get_l1_l2_mapping(self, rtm: str) -> list:
        return [RTM_SUB_RTM_PLATFORM_MAPPING[rtm]]

    def get_l1_item_names(self, rtm: str, sub_rtm: str, db_data) -> list:
        return [[rtm, sub_rtm]]

class OnlinePlatformNdNode(UbTrackingNode):
    def get_wi_props(self):
        level1_wi_props = [PropRtm, PropSubRtm, PropHqId, PropHqName]
        level2_wi_props = level1_wi_props + [PropOnlineOffline]
        level3_wi_props = level2_wi_props + [PropPlatform]
        return level1_wi_props, level2_wi_props, level3_wi_props

    def get_tbl_cls(self):
        return TblOnlinPlatformeUbTracking

    def get_rtm_list(self):
        return [DB_RTM_MULTI, DB_RTM_Mono, DB_RTM_EDU, DB_RTM_ENT]

    def remove_some_subset(self, detail_data):
        """
            二级只有online展开
        """
        for l1_item in detail_data:
            l2_list = l1_item.get(KeyChild, [])
            remove_some_item(l2_list, not_online=True)
        return detail_data

    def filter_data(self, db_data):
        """
            OTC无Online，仅有Offline
        """
        level1_wi_props, level2_wi_props, level3_wi_props = self.get_wi_props()
        return filter_list(db_data, level3_wi_props[1], level3_wi_props[4], otc=True)

    def get_l1_l2_mapping(self, rtm: str) -> list:
        return [RTM_SUB_RTM_PLATFORM_MAPPING[rtm]]

    def get_l1_item_names(self, rtm: str, sub_rtm: str, db_data) -> list:
        return get_nd_t1_l1_item_names(rtm, sub_rtm, db_data)

class SublobRtmNode(UbTrackingNode):
    def get_wi_props(self):
        level1_wi_props = [PropRtm]
        level2_wi_props = level1_wi_props + [PropProductGroup]
        level3_wi_props = level2_wi_props + [PropSublob]
        return level1_wi_props, level2_wi_props, level3_wi_props

    def get_tbl_cls(self):
        return PartnerOverallUbTracking

    def get_rtm_list(self):
        return RTM_PERMISSION_TO_DB_QUERY_RTM_MAPPING.values()

    def remove_some_subset(self, detail_data):
        """
            二级Others不展开
        """
        return remove_others_subset(detail_data)

    def filter_data(self, db_data):
        return db_data

    def get_l1_l2_mapping(self, rtm: str) -> list:
        return get_sub_lob_l1_l2_mapping(rtm)

    def get_l1_item_names(self, rtm: str, sub_rtm: str, db_data) -> list:
        return [[rtm]]


class SublobSubRtmNode(UbTrackingNode):
    def get_wi_props(self):
        level1_wi_props = [PropRtm, PropSubRtm]
        level2_wi_props = level1_wi_props + [PropProductGroup]
        level3_wi_props = level2_wi_props + [PropSublob]
        return level1_wi_props, level2_wi_props, level3_wi_props

    def get_tbl_cls(self):
        return PartnerOverallUbTracking

    def get_rtm_list(self):
        return [DB_RTM_MULTI, DB_RTM_Mono, DB_RTM_CARRIER]

    def remove_some_subset(self, detail_data):
        """
            二级Others不展开
        """
        return remove_others_subset(detail_data)

    def filter_data(self, db_data):
        return db_data

    def get_l1_l2_mapping(self, rtm: str) -> list:
        return get_sub_lob_l1_l2_mapping(rtm)

    def get_l1_item_names(self, rtm: str, sub_rtm: str, db_data) -> list:
        return [[rtm, sub_rtm]]


class SublobNdNode(UbTrackingNode):
    def get_wi_props(self):
        level1_wi_props = [PropRtm, PropSubRtm, PropHqId, PropHqName]
        level2_wi_props = level1_wi_props + [PropProductGroup]
        level3_wi_props = level2_wi_props + [PropSublob]
        return level1_wi_props, level2_wi_props, level3_wi_props

    def get_tbl_cls(self):
        return PartnerOverallUbTracking

    def get_rtm_list(self):
        return [DB_RTM_MULTI, DB_RTM_Mono, DB_RTM_EDU, DB_RTM_ENT]

    def remove_some_subset(self, detail_data):
        """
            二级Others不展开
        """
        return remove_others_subset(detail_data)

    def filter_data(self, db_data):
        return db_data

    def get_l1_l2_mapping(self, rtm: str) -> list:
        return get_sub_lob_l1_l2_mapping(rtm)


    def get_l1_item_names(self, rtm: str, sub_rtm: str, db_data) -> list:
        return get_nd_t1_l1_item_names(rtm, sub_rtm, db_data)

class PartnerOverallNode(UbTrackingNode):
    def get_wi_props(self):
        return None, None, None

    def get_tbl_cls(self):
        return PartnerOverallUbTracking

    def get_rtm_list(self):
        return RTM_PERMISSION_TO_DB_QUERY_RTM_MAPPING.values()

    def remove_some_subset(self, detail_data):
        return detail_data

    def filter_data(self, db_data):
        return db_data

    def get_l1_l2_mapping(self, rtm: str) -> dict:
        return {}

    def get_l1_item_names(self, rtm: str, sub_rtm: str, db_data) -> list:
        return []


class PartnerStoreTypeNode(UbTrackingNode):
    def get_wi_props(self):
        return None, None, None

    def get_tbl_cls(self):
        return PartnerStoreTypeUbTracking

    def get_rtm_list(self):
        return [DB_RTM_MULTI, DB_RTM_CARRIER]

    def remove_some_subset(self, detail_data):
        return detail_data

    def filter_data(self, db_data):
        return db_data

    def get_l1_l2_mapping(self, rtm: str) -> dict:
        return {}

    def get_l1_item_names(self, rtm: str, sub_rtm: str, db_data) -> list:
        return []

def get_ub_tracking_node(data_type: str, tab_type: str):
    ub_tracking_node_dict = {
        f'{DataTypeOnlinePlatform}||{UbTabType.RTM.value}': OnlinePlatformRtmNode(),
        f'{DataTypeOnlinePlatform}||{UbTabType.SUB_RTM.value}': OnlinePlatformSubRtmNode(),
        f'{DataTypeOnlinePlatform}||{UbTabType.ND_T1.value}': OnlinePlatformNdNode(),
        f'{DataTypeSublob}||{UbTabType.RTM.value}': SublobRtmNode(),
        f'{DataTypeSublob}||{UbTabType.SUB_RTM.value}': SublobSubRtmNode(),
        f'{DataTypeSublob}||{UbTabType.ND_T1.value}': SublobNdNode(),
        f'{DataTypePartner}||{PartnerUbViewEnum.OVERALL.value}': PartnerOverallNode(),
        f'{DataTypePartner}||{PartnerUbViewEnum.STORE_TYPE.value}': PartnerStoreTypeNode()
    }

    return ub_tracking_node_dict.get(f'{data_type}||{tab_type}', None)


def remove_others_subset(l1_list):
    for l1_item in l1_list:
        l2_list = l1_item.get(KeyChild, [])
        remove_some_item(l2_list, others=True)
    return l1_list


def remove_some_item(l2_list, not_online=False, others=False):
    for i in range(len(l2_list) - 1, -1, -1):
        l2_item = l2_list[i]
        l2_item_name = l2_item.get(KeyItemName)
        if not_online and l2_item_name in [StrUnauthorized, StrOffline]:
            l2_item[KeyChild] = []
        if others and l2_item_name == StrOthers:
            l2_item[KeyChild] = []

def filter_list(data, l1_key, l2_key, otc=False, cb=False, carrier_unauthorized=False):
    res = []
    for item in data:
        l1_item_name = item.get(l1_key)
        l2_item_name = item.get(l2_key)
        if otc and l1_item_name == StrOTC and l2_item_name == StrOnline:
            continue
        if cb and l1_item_name == StrCB and l2_item_name == StrOnline:
            continue
        if carrier_unauthorized and l1_item_name in CARRIER_SUB_RTM and l2_item_name == StrUnauthorized:
            if division_is_none(item):
                continue
        res.append(item)
    return res

def division_is_none(item: dict) -> bool:
    """
    判断除法结果是否为空
    """
    for i in range(len(numeratorFields)):
        numerator_field = numeratorFields[i]
        denominator_field = denominatorFields[i]
        if item[numerator_field] is not None and item[denominator_field] is not None and item[denominator_field] != 0:
            return False
    return True

def get_sub_lob_l1_l2_mapping(rtm: str) -> list:
    res = []
    sub_rtm_platform_mapping = RTM_SUB_RTM_PLATFORM_MAPPING.get(rtm)
    for sub_rtm in sub_rtm_platform_mapping.keys():
        res.append({sub_rtm: PRODUCT_GROUP_SUB_LOB_MAPPING})
    return res

def get_nd_t1_l1_item_names(rtm: str, sub_rtm: str, db_data) -> list:
    res = []
    for item in db_data:
        if item.get(PropRtm) == rtm and item.get(PropSubRtm) == sub_rtm:
            res.append([rtm, sub_rtm, item.get(PropHqId), item.get(PropHqName)])
    return res