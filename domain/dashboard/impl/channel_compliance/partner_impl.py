import logging
import traceback
from datetime import datetime

import numpy as np
import pandas as pd
from flask import g

from data.databend.dashboard.channel_compliance.partner_overall_ub_tracking import PartnerOverallUbTracking
from data.databend.dashboard.channel_compliance.partner_store_type_ub_tracking import PartnerStoreTypeUbTracking
from domain.dashboard.entity.channel_compliance.partner_entity import PartnerOverallUbItem, PartnerStoreTypeUbItem
from domain.dashboard.impl.channel_compliance import CUSTOM_RTM_SORT_RULE, CUSTOM_SUB_RTM_SORT_RULE, \
    CUSTOM_ND_T1_SORT_RULE, CUSTOM_STORE_TYPE_SORT_RULE, PartnerUbViewEnum, DISPLAY_GC_NAME, DISPLAY_RTM_CARRIER, \
    DISPLAY_RTM_CHANNEL_ONLINE, DISPLAY_RTM_EDU, DISPLAY_RTM_ENT, RTM_PERMISSION_TO_DB_QUERY_RTM_MAPPING, DB_RTM_MULTI, \
    DB_RTM_CARRIER
from domain.dashboard.impl.channel_compliance.ub_tracking_model import get_ub_tracking_node
from domain.demand.entity.common_menu import Menu
from kit.email.sender import CustomizedData, Sender
from util.const import ALL, REGION_CM, ErrorExcept, ErrCode, EmailCmd
from util.util import env_dev


def sort_partner_overall_ub_data(data: list):
    data = sorted(data, key=lambda x: (
        CUSTOM_RTM_SORT_RULE.index(x.rtm) if x.rtm in CUSTOM_RTM_SORT_RULE else float('inf'),
        CUSTOM_SUB_RTM_SORT_RULE.index(x.sub_rtm) if x.sub_rtm in CUSTOM_SUB_RTM_SORT_RULE else float('inf'),
        CUSTOM_ND_T1_SORT_RULE.index(x.nd_type) if x.nd_type in CUSTOM_ND_T1_SORT_RULE and x.rtm == 'Mono Brand' else float('inf'),
        (x.hq_name == "", x.hq_name != ALL, x.hq_name),  # 空字符串放最后，按照A-Z升序
    ))
    return data


def sort_partner_store_type_ub_data(data: list):
    data = sorted(data, key=lambda x: (
        CUSTOM_RTM_SORT_RULE.index(x.rtm) if x.rtm in CUSTOM_RTM_SORT_RULE else float('inf'),
        CUSTOM_SUB_RTM_SORT_RULE.index(x.sub_rtm) if x.sub_rtm in CUSTOM_SUB_RTM_SORT_RULE else float('inf'),
        CUSTOM_STORE_TYPE_SORT_RULE.index(x.store_type) if x.store_type in CUSTOM_STORE_TYPE_SORT_RULE else float('inf')
    ))
    return data


def get_query_permission_rtm(rtm: str, view_tab=PartnerUbViewEnum.OVERALL.value) -> list:
    """获取查询rtm对应的数据权限列表"""
    permission_rtm_list = [
        RTM_PERMISSION_TO_DB_QUERY_RTM_MAPPING.get(role_name, role_name)
        for role_name in g.platform_role_names
    ]
    if view_tab == PartnerUbViewEnum.OVERALL.value:
        rtm_list = permission_rtm_list if rtm == ALL else [rtm]
    else:
        rtm_list = [DB_RTM_MULTI, DB_RTM_CARRIER] if rtm == ALL else [rtm]

    query_permission_rtm_list = list(set(rtm_list) & set(permission_rtm_list))
    if not query_permission_rtm_list:
        raise ErrorExcept(ErrCode.Param, "no permission")
    return query_permission_rtm_list


class PartnerOverallUbAggregate:

    @classmethod
    def group_by_region(cls, ub_data_list: list[PartnerOverallUbItem]) -> list[PartnerOverallUbItem]:
        if not ub_data_list:
            return []
        ub_data_info = [item.as_dict() for item in ub_data_list]
        df = pd.DataFrame(ub_data_info)
        df = df.groupby(['snapshot_date']).agg({
            'snapshot_date': "first",
            'lob': "first",
            'fiscal_qtr_year_name_cq': "first",
            'total_so_acc_cq': np.sum,
            'ub3_acc_cq': np.sum,
            'ub7_acc_cq': np.sum,
            'fiscal_qtr_year_name_lq': "first",
            'total_so_acc_lq': np.sum,
            'ub3_acc_lq': np.sum,
            'ub7_acc_lq': np.sum,
            'fiscal_qtr_week_name_cw': "first",
            'total_so_cw': np.sum,
            'ub7_cw': np.sum,
            'fiscal_qtr_week_name_cw1': "first",
            'total_so_cw1': np.sum,
            'ub7_cw1': np.sum,
            'fiscal_qtr_week_name_cw2': "first",
            'total_so_cw2': np.sum,
            'ub7_cw2': np.sum,
            'fiscal_qtr_week_name_cw3': "first",
            'total_so_cw3': np.sum,
            'ub7_cw3': np.sum,
            'fiscal_qtr_week_name_cw4': "first",
            'total_so_cw4': np.sum,
            'ub7_cw4': np.sum,
            'last_fiscal_qtr_week_name_cw': "first",
            'last_total_so_cw': np.sum,
            'last_ub7_cw': np.sum,
            'last_fiscal_qtr_week_name_cw1': "first",
            'last_total_so_cw1': np.sum,
            'last_ub7_cw1': np.sum
        })
        aggregate_data = df.to_dict(orient='records')
        ret = []
        for item in aggregate_data:
            aggregate_item = PartnerOverallUbItem(
                snapshot_date=item.get('snapshot_date'),
                rtm=ALL,
                sub_rtm=ALL,
                hq_name=ALL,
                nd_type=ALL,
                lob=item.get('lob'),
                sub_lob=ALL
            )
            aggregate_item.set_ub_data(
                fiscal_qtr_year_name_cq=item.get("fiscal_qtr_year_name_cq"),
                total_so_acc_cq=item.get("total_so_acc_cq"),
                ub3_acc_cq=item.get("ub3_acc_cq"),
                ub7_acc_cq=item.get("ub7_acc_cq"),
                fiscal_qtr_year_name_lq=item.get("fiscal_qtr_year_name_lq"),
                total_so_acc_lq=item.get("total_so_acc_lq"),
                ub3_acc_lq=item.get("ub3_acc_lq"),
                ub7_acc_lq=item.get("ub7_acc_lq"),
                fiscal_qtr_week_name_cw=item.get("fiscal_qtr_week_name_cw"),
                total_so_cw=item.get("total_so_cw"),
                ub7_cw=item.get("ub7_cw"),
                fiscal_qtr_week_name_cw1=item.get("fiscal_qtr_week_name_cw1"),
                total_so_cw1=item.get("total_so_cw1"),
                ub7_cw1=item.get("ub7_cw1"),
                fiscal_qtr_week_name_cw2=item.get("fiscal_qtr_week_name_cw2"),
                total_so_cw2=item.get("total_so_cw2"),
                ub7_cw2=item.get("ub7_cw2"),
                fiscal_qtr_week_name_cw3=item.get("fiscal_qtr_week_name_cw3"),
                total_so_cw3=item.get("total_so_cw3"),
                ub7_cw3=item.get("ub7_cw3"),
                fiscal_qtr_week_name_cw4=item.get("fiscal_qtr_week_name_cw4"),
                total_so_cw4=item.get("total_so_cw4"),
                ub7_cw4=item.get("ub7_cw4"),
                last_fiscal_qtr_week_name_cw=item.get("last_fiscal_qtr_week_name_cw"),
                last_total_so_cw=item.get("last_total_so_cw"),
                last_ub7_cw=item.get("last_ub7_cw"),
                last_fiscal_qtr_week_name_cw1=item.get("last_fiscal_qtr_week_name_cw1"),
                last_total_so_cw1=item.get("last_total_so_cw1"),
                last_ub7_cw1=item.get("last_ub7_cw1")
            )
            ret.append(aggregate_item)

        # 排序
        return sort_partner_overall_ub_data(data=ret)

    @classmethod
    def group_by_rtm(cls, ub_data_list: list[PartnerOverallUbItem]) -> list[PartnerOverallUbItem]:
        if not ub_data_list:
            return []
        ub_data_info = [item.as_dict() for item in ub_data_list]
        df = pd.DataFrame(ub_data_info)
        df = df.groupby(['snapshot_date', 'rtm']).agg({
            'snapshot_date': "first",
            'rtm': "first",
            'lob': "first",
            'fiscal_qtr_year_name_cq': "first",
            'total_so_acc_cq': np.sum,
            'ub3_acc_cq': np.sum,
            'ub7_acc_cq': np.sum,
            'fiscal_qtr_year_name_lq': "first",
            'total_so_acc_lq': np.sum,
            'ub3_acc_lq': np.sum,
            'ub7_acc_lq': np.sum,
            'fiscal_qtr_week_name_cw': "first",
            'total_so_cw': np.sum,
            'ub7_cw': np.sum,
            'fiscal_qtr_week_name_cw1': "first",
            'total_so_cw1': np.sum,
            'ub7_cw1': np.sum,
            'fiscal_qtr_week_name_cw2': "first",
            'total_so_cw2': np.sum,
            'ub7_cw2': np.sum,
            'fiscal_qtr_week_name_cw3': "first",
            'total_so_cw3': np.sum,
            'ub7_cw3': np.sum,
            'fiscal_qtr_week_name_cw4': "first",
            'total_so_cw4': np.sum,
            'ub7_cw4': np.sum,
            'last_fiscal_qtr_week_name_cw': "first",
            'last_total_so_cw': np.sum,
            'last_ub7_cw': np.sum,
            'last_fiscal_qtr_week_name_cw1': "first",
            'last_total_so_cw1': np.sum,
            'last_ub7_cw1': np.sum
        })
        aggregate_data = df.to_dict(orient='records')
        ret = []
        for item in aggregate_data:
            aggregate_item = PartnerOverallUbItem(
                snapshot_date=item.get('snapshot_date'),
                rtm=item.get('rtm'),
                sub_rtm=ALL,
                hq_name=ALL,
                nd_type=ALL,
                lob=item.get('lob'),
                sub_lob=ALL
            )
            aggregate_item.set_ub_data(
                fiscal_qtr_year_name_cq=item.get("fiscal_qtr_year_name_cq"),
                total_so_acc_cq=item.get("total_so_acc_cq"),
                ub3_acc_cq=item.get("ub3_acc_cq"),
                ub7_acc_cq=item.get("ub7_acc_cq"),
                fiscal_qtr_year_name_lq=item.get("fiscal_qtr_year_name_lq"),
                total_so_acc_lq=item.get("total_so_acc_lq"),
                ub3_acc_lq=item.get("ub3_acc_lq"),
                ub7_acc_lq=item.get("ub7_acc_lq"),
                fiscal_qtr_week_name_cw=item.get("fiscal_qtr_week_name_cw"),
                total_so_cw=item.get("total_so_cw"),
                ub7_cw=item.get("ub7_cw"),
                fiscal_qtr_week_name_cw1=item.get("fiscal_qtr_week_name_cw1"),
                total_so_cw1=item.get("total_so_cw1"),
                ub7_cw1=item.get("ub7_cw1"),
                fiscal_qtr_week_name_cw2=item.get("fiscal_qtr_week_name_cw2"),
                total_so_cw2=item.get("total_so_cw2"),
                ub7_cw2=item.get("ub7_cw2"),
                fiscal_qtr_week_name_cw3=item.get("fiscal_qtr_week_name_cw3"),
                total_so_cw3=item.get("total_so_cw3"),
                ub7_cw3=item.get("ub7_cw3"),
                fiscal_qtr_week_name_cw4=item.get("fiscal_qtr_week_name_cw4"),
                total_so_cw4=item.get("total_so_cw4"),
                ub7_cw4=item.get("ub7_cw4"),
                last_fiscal_qtr_week_name_cw=item.get("last_fiscal_qtr_week_name_cw"),
                last_total_so_cw=item.get("last_total_so_cw"),
                last_ub7_cw=item.get("last_ub7_cw"),
                last_fiscal_qtr_week_name_cw1=item.get("last_fiscal_qtr_week_name_cw1"),
                last_total_so_cw1=item.get("last_total_so_cw1"),
                last_ub7_cw1=item.get("last_ub7_cw1")
            )
            ret.append(aggregate_item)
        # 排序
        return sort_partner_overall_ub_data(data=ret)

    @classmethod
    def group_by_sub_rtm(cls, ub_data_list: list[PartnerOverallUbItem]) -> list[PartnerOverallUbItem]:
        if not ub_data_list:
            return []
        ub_data_info = [item.as_dict() for item in ub_data_list]
        df = pd.DataFrame(ub_data_info)
        df = df.groupby(['snapshot_date', 'rtm', 'sub_rtm']).agg({
            'snapshot_date': "first",
            'rtm': "first",
            'sub_rtm': "first",
            'lob': "first",
            'fiscal_qtr_year_name_cq': "first",
            'total_so_acc_cq': np.sum,
            'ub3_acc_cq': np.sum,
            'ub7_acc_cq': np.sum,
            'fiscal_qtr_year_name_lq': "first",
            'total_so_acc_lq': np.sum,
            'ub3_acc_lq': np.sum,
            'ub7_acc_lq': np.sum,
            'fiscal_qtr_week_name_cw': "first",
            'total_so_cw': np.sum,
            'ub7_cw': np.sum,
            'fiscal_qtr_week_name_cw1': "first",
            'total_so_cw1': np.sum,
            'ub7_cw1': np.sum,
            'fiscal_qtr_week_name_cw2': "first",
            'total_so_cw2': np.sum,
            'ub7_cw2': np.sum,
            'fiscal_qtr_week_name_cw3': "first",
            'total_so_cw3': np.sum,
            'ub7_cw3': np.sum,
            'fiscal_qtr_week_name_cw4': "first",
            'total_so_cw4': np.sum,
            'ub7_cw4': np.sum,
            'last_fiscal_qtr_week_name_cw': "first",
            'last_total_so_cw': np.sum,
            'last_ub7_cw': np.sum,
            'last_fiscal_qtr_week_name_cw1': "first",
            'last_total_so_cw1': np.sum,
            'last_ub7_cw1': np.sum
        })
        aggregate_data = df.to_dict(orient='records')
        ret = []
        for item in aggregate_data:
            aggregate_item = PartnerOverallUbItem(
                snapshot_date=item.get('snapshot_date'),
                rtm=item.get('rtm'),
                sub_rtm=item.get('sub_rtm'),
                hq_name=ALL,
                nd_type=ALL,
                lob=item.get('lob'),
                sub_lob=ALL
            )
            aggregate_item.set_ub_data(
                fiscal_qtr_year_name_cq=item.get("fiscal_qtr_year_name_cq"),
                total_so_acc_cq=item.get("total_so_acc_cq"),
                ub3_acc_cq=item.get("ub3_acc_cq"),
                ub7_acc_cq=item.get("ub7_acc_cq"),
                fiscal_qtr_year_name_lq=item.get("fiscal_qtr_year_name_lq"),
                total_so_acc_lq=item.get("total_so_acc_lq"),
                ub3_acc_lq=item.get("ub3_acc_lq"),
                ub7_acc_lq=item.get("ub7_acc_lq"),
                fiscal_qtr_week_name_cw=item.get("fiscal_qtr_week_name_cw"),
                total_so_cw=item.get("total_so_cw"),
                ub7_cw=item.get("ub7_cw"),
                fiscal_qtr_week_name_cw1=item.get("fiscal_qtr_week_name_cw1"),
                total_so_cw1=item.get("total_so_cw1"),
                ub7_cw1=item.get("ub7_cw1"),
                fiscal_qtr_week_name_cw2=item.get("fiscal_qtr_week_name_cw2"),
                total_so_cw2=item.get("total_so_cw2"),
                ub7_cw2=item.get("ub7_cw2"),
                fiscal_qtr_week_name_cw3=item.get("fiscal_qtr_week_name_cw3"),
                total_so_cw3=item.get("total_so_cw3"),
                ub7_cw3=item.get("ub7_cw3"),
                fiscal_qtr_week_name_cw4=item.get("fiscal_qtr_week_name_cw4"),
                total_so_cw4=item.get("total_so_cw4"),
                ub7_cw4=item.get("ub7_cw4"),
                last_fiscal_qtr_week_name_cw=item.get("last_fiscal_qtr_week_name_cw"),
                last_total_so_cw=item.get("last_total_so_cw"),
                last_ub7_cw=item.get("last_ub7_cw"),
                last_fiscal_qtr_week_name_cw1=item.get("last_fiscal_qtr_week_name_cw1"),
                last_total_so_cw1=item.get("last_total_so_cw1"),
                last_ub7_cw1=item.get("last_ub7_cw1")
            )
            ret.append(aggregate_item)
        # 排序
        return sort_partner_overall_ub_data(data=ret)

    @classmethod
    def group_by_hq_name(cls, ub_data_list: list[PartnerOverallUbItem]) -> list[PartnerOverallUbItem]:
        if not ub_data_list:
            return []
        ub_data_info = [item.as_dict() for item in ub_data_list]
        df = pd.DataFrame(ub_data_info)
        df = df.groupby(['snapshot_date', 'rtm', 'sub_rtm', 'hq_name', 'nd_type']).agg({
            'snapshot_date': "first",
            'rtm': "first",
            'sub_rtm': "first",
            'hq_name': "first",
            'nd_type': "first",
            'lob': "first",
            'fiscal_qtr_year_name_cq': "first",
            'total_so_acc_cq': np.sum,
            'ub3_acc_cq': np.sum,
            'ub7_acc_cq': np.sum,
            'fiscal_qtr_year_name_lq': "first",
            'total_so_acc_lq': np.sum,
            'ub3_acc_lq': np.sum,
            'ub7_acc_lq': np.sum,
            'fiscal_qtr_week_name_cw': "first",
            'total_so_cw': np.sum,
            'ub7_cw': np.sum,
            'fiscal_qtr_week_name_cw1': "first",
            'total_so_cw1': np.sum,
            'ub7_cw1': np.sum,
            'fiscal_qtr_week_name_cw2': "first",
            'total_so_cw2': np.sum,
            'ub7_cw2': np.sum,
            'fiscal_qtr_week_name_cw3': "first",
            'total_so_cw3': np.sum,
            'ub7_cw3': np.sum,
            'fiscal_qtr_week_name_cw4': "first",
            'total_so_cw4': np.sum,
            'ub7_cw4': np.sum,
            'last_fiscal_qtr_week_name_cw': "first",
            'last_total_so_cw': np.sum,
            'last_ub7_cw': np.sum,
            'last_fiscal_qtr_week_name_cw1': "first",
            'last_total_so_cw1': np.sum,
            'last_ub7_cw1': np.sum
        })
        aggregate_data = df.to_dict(orient='records')
        ret = []
        for item in aggregate_data:
            aggregate_item = PartnerOverallUbItem(
                snapshot_date=item.get('snapshot_date'),
                rtm=item.get('rtm'),
                sub_rtm=item.get('sub_rtm'),
                hq_name=item.get('hq_name'),
                nd_type=item.get('nd_type'),
                lob=item.get('lob'),
                sub_lob=ALL
            )
            aggregate_item.set_ub_data(
                fiscal_qtr_year_name_cq=item.get("fiscal_qtr_year_name_cq"),
                total_so_acc_cq=item.get("total_so_acc_cq"),
                ub3_acc_cq=item.get("ub3_acc_cq"),
                ub7_acc_cq=item.get("ub7_acc_cq"),
                fiscal_qtr_year_name_lq=item.get("fiscal_qtr_year_name_lq"),
                total_so_acc_lq=item.get("total_so_acc_lq"),
                ub3_acc_lq=item.get("ub3_acc_lq"),
                ub7_acc_lq=item.get("ub7_acc_lq"),
                fiscal_qtr_week_name_cw=item.get("fiscal_qtr_week_name_cw"),
                total_so_cw=item.get("total_so_cw"),
                ub7_cw=item.get("ub7_cw"),
                fiscal_qtr_week_name_cw1=item.get("fiscal_qtr_week_name_cw1"),
                total_so_cw1=item.get("total_so_cw1"),
                ub7_cw1=item.get("ub7_cw1"),
                fiscal_qtr_week_name_cw2=item.get("fiscal_qtr_week_name_cw2"),
                total_so_cw2=item.get("total_so_cw2"),
                ub7_cw2=item.get("ub7_cw2"),
                fiscal_qtr_week_name_cw3=item.get("fiscal_qtr_week_name_cw3"),
                total_so_cw3=item.get("total_so_cw3"),
                ub7_cw3=item.get("ub7_cw3"),
                fiscal_qtr_week_name_cw4=item.get("fiscal_qtr_week_name_cw4"),
                total_so_cw4=item.get("total_so_cw4"),
                ub7_cw4=item.get("ub7_cw4"),
                last_fiscal_qtr_week_name_cw=item.get("last_fiscal_qtr_week_name_cw"),
                last_total_so_cw=item.get("last_total_so_cw"),
                last_ub7_cw=item.get("last_ub7_cw"),
                last_fiscal_qtr_week_name_cw1=item.get("last_fiscal_qtr_week_name_cw1"),
                last_total_so_cw1=item.get("last_total_so_cw1"),
                last_ub7_cw1=item.get("last_ub7_cw1")
            )
            ret.append(aggregate_item)
        # 排序
        return sort_partner_overall_ub_data(data=ret)


class PartnerStoreTypeUbAggregate:

    @classmethod
    def group_by_rtm(cls, ub_data_list: list[PartnerStoreTypeUbItem]) -> list[PartnerStoreTypeUbItem]:
        if not ub_data_list:
            return []
        ub_data_info = [item.as_dict() for item in ub_data_list]
        df = pd.DataFrame(ub_data_info)
        df = df.groupby(['snapshot_date', 'rtm']).agg({
            'snapshot_date': "first",
            'rtm': "first",
            'lob': "first",
            'fiscal_qtr_year_name_cq': "first",
            'total_so_acc_cq': np.sum,
            'ub3_acc_cq': np.sum,
            'ub7_acc_cq': np.sum,
            'fiscal_qtr_year_name_lq': "first",
            'total_so_acc_lq': np.sum,
            'ub3_acc_lq': np.sum,
            'ub7_acc_lq': np.sum,
            'fiscal_qtr_week_name_cw': "first",
            'total_so_cw': np.sum,
            'ub7_cw': np.sum,
            'fiscal_qtr_week_name_cw1': "first",
            'total_so_cw1': np.sum,
            'ub7_cw1': np.sum,
            'fiscal_qtr_week_name_cw2': "first",
            'total_so_cw2': np.sum,
            'ub7_cw2': np.sum,
            'fiscal_qtr_week_name_cw3': "first",
            'total_so_cw3': np.sum,
            'ub7_cw3': np.sum,
            'fiscal_qtr_week_name_cw4': "first",
            'total_so_cw4': np.sum,
            'ub7_cw4': np.sum,
            'last_fiscal_qtr_week_name_cw': "first",
            'last_total_so_cw': np.sum,
            'last_ub7_cw': np.sum,
            'last_fiscal_qtr_week_name_cw1': "first",
            'last_total_so_cw1': np.sum,
            'last_ub7_cw1': np.sum
        })
        aggregate_data = df.to_dict(orient='records')
        ret = []
        for item in aggregate_data:
            aggregate_item = PartnerStoreTypeUbItem(
                snapshot_date=item.get('snapshot_date'),
                rtm=item.get('rtm'),
                sub_rtm=ALL,
                store_type=ALL,
                lob=item.get('lob'),
                sub_lob=ALL
            )
            aggregate_item.set_ub_data(
                fiscal_qtr_year_name_cq=item.get("fiscal_qtr_year_name_cq"),
                total_so_acc_cq=item.get("total_so_acc_cq"),
                ub3_acc_cq=item.get("ub3_acc_cq"),
                ub7_acc_cq=item.get("ub7_acc_cq"),
                fiscal_qtr_year_name_lq=item.get("fiscal_qtr_year_name_lq"),
                total_so_acc_lq=item.get("total_so_acc_lq"),
                ub3_acc_lq=item.get("ub3_acc_lq"),
                ub7_acc_lq=item.get("ub7_acc_lq"),
                fiscal_qtr_week_name_cw=item.get("fiscal_qtr_week_name_cw"),
                total_so_cw=item.get("total_so_cw"),
                ub7_cw=item.get("ub7_cw"),
                fiscal_qtr_week_name_cw1=item.get("fiscal_qtr_week_name_cw1"),
                total_so_cw1=item.get("total_so_cw1"),
                ub7_cw1=item.get("ub7_cw1"),
                fiscal_qtr_week_name_cw2=item.get("fiscal_qtr_week_name_cw2"),
                total_so_cw2=item.get("total_so_cw2"),
                ub7_cw2=item.get("ub7_cw2"),
                fiscal_qtr_week_name_cw3=item.get("fiscal_qtr_week_name_cw3"),
                total_so_cw3=item.get("total_so_cw3"),
                ub7_cw3=item.get("ub7_cw3"),
                fiscal_qtr_week_name_cw4=item.get("fiscal_qtr_week_name_cw4"),
                total_so_cw4=item.get("total_so_cw4"),
                ub7_cw4=item.get("ub7_cw4"),
                last_fiscal_qtr_week_name_cw=item.get("last_fiscal_qtr_week_name_cw"),
                last_total_so_cw=item.get("last_total_so_cw"),
                last_ub7_cw=item.get("last_ub7_cw"),
                last_fiscal_qtr_week_name_cw1=item.get("last_fiscal_qtr_week_name_cw1"),
                last_total_so_cw1=item.get("last_total_so_cw1"),
                last_ub7_cw1=item.get("last_ub7_cw1")
            )
            ret.append(aggregate_item)
        # 排序
        return sort_partner_store_type_ub_data(data=ret)

    @classmethod
    def group_by_sub_rtm(cls, ub_data_list: list[PartnerStoreTypeUbItem]) -> list[PartnerStoreTypeUbItem]:
        if not ub_data_list:
            return []
        ub_data_info = [item.as_dict() for item in ub_data_list]
        df = pd.DataFrame(ub_data_info)
        df = df.groupby(['snapshot_date', 'rtm', 'sub_rtm']).agg({
            'snapshot_date': "first",
            'rtm': "first",
            'sub_rtm': "first",
            'lob': "first",
            'fiscal_qtr_year_name_cq': "first",
            'total_so_acc_cq': np.sum,
            'ub3_acc_cq': np.sum,
            'ub7_acc_cq': np.sum,
            'fiscal_qtr_year_name_lq': "first",
            'total_so_acc_lq': np.sum,
            'ub3_acc_lq': np.sum,
            'ub7_acc_lq': np.sum,
            'fiscal_qtr_week_name_cw': "first",
            'total_so_cw': np.sum,
            'ub7_cw': np.sum,
            'fiscal_qtr_week_name_cw1': "first",
            'total_so_cw1': np.sum,
            'ub7_cw1': np.sum,
            'fiscal_qtr_week_name_cw2': "first",
            'total_so_cw2': np.sum,
            'ub7_cw2': np.sum,
            'fiscal_qtr_week_name_cw3': "first",
            'total_so_cw3': np.sum,
            'ub7_cw3': np.sum,
            'fiscal_qtr_week_name_cw4': "first",
            'total_so_cw4': np.sum,
            'ub7_cw4': np.sum,
            'last_fiscal_qtr_week_name_cw': "first",
            'last_total_so_cw': np.sum,
            'last_ub7_cw': np.sum,
            'last_fiscal_qtr_week_name_cw1': "first",
            'last_total_so_cw1': np.sum,
            'last_ub7_cw1': np.sum
        })
        aggregate_data = df.to_dict(orient='records')
        ret = []
        for item in aggregate_data:
            aggregate_item = PartnerStoreTypeUbItem(
                snapshot_date=item.get('snapshot_date'),
                rtm=item.get('rtm'),
                sub_rtm=item.get('sub_rtm'),
                store_type=ALL,
                lob=item.get('lob'),
                sub_lob=ALL
            )
            aggregate_item.set_ub_data(
                fiscal_qtr_year_name_cq=item.get("fiscal_qtr_year_name_cq"),
                total_so_acc_cq=item.get("total_so_acc_cq"),
                ub3_acc_cq=item.get("ub3_acc_cq"),
                ub7_acc_cq=item.get("ub7_acc_cq"),
                fiscal_qtr_year_name_lq=item.get("fiscal_qtr_year_name_lq"),
                total_so_acc_lq=item.get("total_so_acc_lq"),
                ub3_acc_lq=item.get("ub3_acc_lq"),
                ub7_acc_lq=item.get("ub7_acc_lq"),
                fiscal_qtr_week_name_cw=item.get("fiscal_qtr_week_name_cw"),
                total_so_cw=item.get("total_so_cw"),
                ub7_cw=item.get("ub7_cw"),
                fiscal_qtr_week_name_cw1=item.get("fiscal_qtr_week_name_cw1"),
                total_so_cw1=item.get("total_so_cw1"),
                ub7_cw1=item.get("ub7_cw1"),
                fiscal_qtr_week_name_cw2=item.get("fiscal_qtr_week_name_cw2"),
                total_so_cw2=item.get("total_so_cw2"),
                ub7_cw2=item.get("ub7_cw2"),
                fiscal_qtr_week_name_cw3=item.get("fiscal_qtr_week_name_cw3"),
                total_so_cw3=item.get("total_so_cw3"),
                ub7_cw3=item.get("ub7_cw3"),
                fiscal_qtr_week_name_cw4=item.get("fiscal_qtr_week_name_cw4"),
                total_so_cw4=item.get("total_so_cw4"),
                ub7_cw4=item.get("ub7_cw4"),
                last_fiscal_qtr_week_name_cw=item.get("last_fiscal_qtr_week_name_cw"),
                last_total_so_cw=item.get("last_total_so_cw"),
                last_ub7_cw=item.get("last_ub7_cw"),
                last_fiscal_qtr_week_name_cw1=item.get("last_fiscal_qtr_week_name_cw1"),
                last_total_so_cw1=item.get("last_total_so_cw1"),
                last_ub7_cw1=item.get("last_ub7_cw1")
            )
            ret.append(aggregate_item)
        # 排序
        return sort_partner_store_type_ub_data(data=ret)

    @classmethod
    def group_by_store_type(cls, ub_data_list: list[PartnerStoreTypeUbItem]) -> list[PartnerStoreTypeUbItem]:
        if not ub_data_list:
            return []
        ub_data_info = [item.as_dict() for item in ub_data_list]
        df = pd.DataFrame(ub_data_info)
        df = df.groupby(['snapshot_date', 'rtm', 'sub_rtm', 'store_type']).agg({
            'snapshot_date': "first",
            'rtm': "first",
            'sub_rtm': "first",
            'store_type': "first",
            'lob': "first",
            'fiscal_qtr_year_name_cq': "first",
            'total_so_acc_cq': np.sum,
            'ub3_acc_cq': np.sum,
            'ub7_acc_cq': np.sum,
            'fiscal_qtr_year_name_lq': "first",
            'total_so_acc_lq': np.sum,
            'ub3_acc_lq': np.sum,
            'ub7_acc_lq': np.sum,
            'fiscal_qtr_week_name_cw': "first",
            'total_so_cw': np.sum,
            'ub7_cw': np.sum,
            'fiscal_qtr_week_name_cw1': "first",
            'total_so_cw1': np.sum,
            'ub7_cw1': np.sum,
            'fiscal_qtr_week_name_cw2': "first",
            'total_so_cw2': np.sum,
            'ub7_cw2': np.sum,
            'fiscal_qtr_week_name_cw3': "first",
            'total_so_cw3': np.sum,
            'ub7_cw3': np.sum,
            'fiscal_qtr_week_name_cw4': "first",
            'total_so_cw4': np.sum,
            'ub7_cw4': np.sum,
            'last_fiscal_qtr_week_name_cw': "first",
            'last_total_so_cw': np.sum,
            'last_ub7_cw': np.sum,
            'last_fiscal_qtr_week_name_cw1': "first",
            'last_total_so_cw1': np.sum,
            'last_ub7_cw1': np.sum
        })
        aggregate_data = df.to_dict(orient='records')
        ret = []
        for item in aggregate_data:
            aggregate_item = PartnerStoreTypeUbItem(
                snapshot_date=item.get('snapshot_date'),
                rtm=item.get('rtm'),
                sub_rtm=item.get('sub_rtm'),
                store_type=item.get('store_type'),
                lob=item.get('lob'),
                sub_lob=ALL
            )
            aggregate_item.set_ub_data(
                fiscal_qtr_year_name_cq=item.get("fiscal_qtr_year_name_cq"),
                total_so_acc_cq=item.get("total_so_acc_cq"),
                ub3_acc_cq=item.get("ub3_acc_cq"),
                ub7_acc_cq=item.get("ub7_acc_cq"),
                fiscal_qtr_year_name_lq=item.get("fiscal_qtr_year_name_lq"),
                total_so_acc_lq=item.get("total_so_acc_lq"),
                ub3_acc_lq=item.get("ub3_acc_lq"),
                ub7_acc_lq=item.get("ub7_acc_lq"),
                fiscal_qtr_week_name_cw=item.get("fiscal_qtr_week_name_cw"),
                total_so_cw=item.get("total_so_cw"),
                ub7_cw=item.get("ub7_cw"),
                fiscal_qtr_week_name_cw1=item.get("fiscal_qtr_week_name_cw1"),
                total_so_cw1=item.get("total_so_cw1"),
                ub7_cw1=item.get("ub7_cw1"),
                fiscal_qtr_week_name_cw2=item.get("fiscal_qtr_week_name_cw2"),
                total_so_cw2=item.get("total_so_cw2"),
                ub7_cw2=item.get("ub7_cw2"),
                fiscal_qtr_week_name_cw3=item.get("fiscal_qtr_week_name_cw3"),
                total_so_cw3=item.get("total_so_cw3"),
                ub7_cw3=item.get("ub7_cw3"),
                fiscal_qtr_week_name_cw4=item.get("fiscal_qtr_week_name_cw4"),
                total_so_cw4=item.get("total_so_cw4"),
                ub7_cw4=item.get("ub7_cw4"),
                last_fiscal_qtr_week_name_cw=item.get("last_fiscal_qtr_week_name_cw"),
                last_total_so_cw=item.get("last_total_so_cw"),
                last_ub7_cw=item.get("last_ub7_cw"),
                last_fiscal_qtr_week_name_cw1=item.get("last_fiscal_qtr_week_name_cw1"),
                last_total_so_cw1=item.get("last_total_so_cw1"),
                last_ub7_cw1=item.get("last_ub7_cw1")
            )
            ret.append(aggregate_item)
        # 排序
        return sort_partner_store_type_ub_data(data=ret)


def filter_records_by_rtm(data_group_by_sub_rtm: list, rtm: str):
    """
    筛选符合rtm下的汇总信息(sub_rtm纬度)
    参数说明:
        data_group_by_sub_rtm: sub_rtm纬度的汇总信息
        rtm: 具体某一个rtm
    """
    return [item for item in data_group_by_sub_rtm if item.rtm == rtm]


def filter_records_by_sub_rtm(data_group_by_hq_name: list, rtm: str, sub_rtm: str):
    """
    筛选符合sub_rtm下的汇总信息(hq_name纬度)
    参数说明:
        data_group_by_hq_name: sub_rtm纬度的汇总信息
        rtm: 具体某一个rtm
    """
    return [item for item in data_group_by_hq_name if item.rtm == rtm and item.sub_rtm == sub_rtm]


def get_overall_ub_records(snapshot_date: str, query_rtm: str, all_platform_role_names: bool,
                           permission_rtm_list: list[str], lob: str, sub_lobs: list[str]) -> list:
    """
    获取overall ub 数据
    参数说明:
        snapshot_date: 报告日期
        all_platform_role_names:  是否具备all权限
        query_rtm:  查询的rtm
        permission_rtm_list:  数据权限rtm列表
        lob: lob
        sub_lobs: sub_lob列表
    """
    result_list = []
    detail_records = PartnerOverallUbTracking.get_ub_overall_records(snapshot_date=snapshot_date,
                                                                     rtm_list=permission_rtm_list,
                                                                     lob=lob, sub_lobs=sub_lobs)
    if not detail_records:
        return result_list

    # 明细数据分组求和
    overall_ub_china_channel_list = PartnerOverallUbAggregate.group_by_region(ub_data_list=detail_records)
    overall_ub_rtm_list = PartnerOverallUbAggregate.group_by_rtm(ub_data_list=detail_records)
    overall_ub_sub_rtm_list = PartnerOverallUbAggregate.group_by_sub_rtm(ub_data_list=detail_records)
    overall_ub_hq_name_list = PartnerOverallUbAggregate.group_by_hq_name(ub_data_list=detail_records)

    # 构造返回结构
    for china_channel_item in overall_ub_china_channel_list:
        # 具备All权限且筛选的是ALL才展示GC的数据,  否则是对应rtm的数据
        if all_platform_role_names and query_rtm == ALL:
            china_channel_item.rtm = DISPLAY_GC_NAME
            origin_china_channel_item = china_channel_item.as_display_dict()
            origin_china_channel_item.setdefault("child", [])
            result_list.append(origin_china_channel_item)

        # rtm 纬度汇总数据
        for rtm_item in overall_ub_rtm_list:
            rtm_item_origin = rtm_item.as_display_dict()
            rtm_sub_rtm_list = rtm_item_origin.setdefault("child", [])
            result_list.append(rtm_item_origin)

            # Channel Online无三级和四级的Sub rtm、ND view数据
            if rtm_item.rtm in [DISPLAY_RTM_CHANNEL_ONLINE]:
                continue

            # ENT、EDU无三级sub_rtm数据
            if rtm_item.rtm in [DISPLAY_RTM_EDU, DISPLAY_RTM_ENT]:
                # 具体sub_rtm下hq_name纬度汇总数据
                hq_name_items = filter_records_by_sub_rtm(overall_ub_hq_name_list, rtm_item.rtm,
                                                          rtm_item.rtm)
                for hq_name_item in hq_name_items:
                    hq_name_item_origin = hq_name_item.as_display_dict()
                    hq_name_item_origin.setdefault("child", [])
                    rtm_sub_rtm_list.append(hq_name_item_origin)
                continue

            # 具体rtm下sub_rtm纬度汇总数据
            sub_rtm_items = filter_records_by_rtm(overall_ub_sub_rtm_list, rtm_item.rtm)
            for sub_rtm_item in sub_rtm_items:
                sub_rtm_item_origin = sub_rtm_item.as_display_dict()
                sub_rtm_hq_name_list = sub_rtm_item_origin.setdefault("child", [])
                rtm_sub_rtm_list.append(sub_rtm_item_origin)

                # Carrier无四级ND view数据
                if rtm_item.rtm in [DISPLAY_RTM_CARRIER]:
                    continue

                # 具体sub_rtm下hq_name纬度汇总数据
                hq_name_items = filter_records_by_sub_rtm(overall_ub_hq_name_list, sub_rtm_item.rtm, sub_rtm_item.sub_rtm)
                for hq_name_item in hq_name_items:
                    hq_name_item_origin = hq_name_item.as_display_dict()
                    hq_name_item_origin.setdefault("child", [])
                    sub_rtm_hq_name_list.append(hq_name_item_origin)
    return result_list


def get_overall_ub_dynamic_columns(snapshot_date: str) -> dict:
    dynamic_columns = PartnerOverallUbTracking.get_dynamic_info_by_snapshot_date(snapshot_date=snapshot_date)
    if dynamic_columns:
        return {
            "last_updated": dynamic_columns["last_updated"].strftime('%Y-%m-%d %H:%M:%S'),
            "columns": {
                "qtd_current_quarter_name": dynamic_columns["qtd_current_quarter_name"],
                "qtd_last_quarter_name": dynamic_columns["qtd_last_quarter_name"],
                "weekly_trend_cw_name": dynamic_columns["weekly_trend_cw_name"],
                "weekly_trend_cw1_name": dynamic_columns["weekly_trend_cw1_name"],
                "weekly_trend_cw2_name": dynamic_columns["weekly_trend_cw2_name"],
                "weekly_trend_cw3_name": dynamic_columns["weekly_trend_cw3_name"],
                "weekly_trend_cw4_name": dynamic_columns["weekly_trend_cw4_name"],
                "last_weekly_trend_cw_name": dynamic_columns["last_weekly_trend_cw_name"] if dynamic_columns["last_weekly_trend_cw_name"] else dynamic_columns["weekly_trend_cw_name"],
                "last_weekly_trend_cw1_name": dynamic_columns["last_weekly_trend_cw1_name"] if dynamic_columns["last_weekly_trend_cw1_name"] else dynamic_columns["weekly_trend_cw1_name"]
            }
        }
    return dynamic_columns


def get_store_type_ub_records(snapshot_date: str, permission_rtm_list: list[str],
                              lob: str, sub_lobs: list[str]) -> list:
    """
    获取store type ub 数据
    参数说明:
        snapshot_date: 报告日期
        permission_rtm_list: 数据权限查询rtm
        lob: lob
        sub_lobs: sub_lob列表
    """
    result_list = []

    detail_records = PartnerStoreTypeUbTracking.get_ub_store_type_records(snapshot_date=snapshot_date,
                                                                          rtm_list=permission_rtm_list,
                                                                          lob=lob, sub_lobs=sub_lobs)
    if not detail_records:
        return result_list

    # 明细数据分组求和
    overall_ub_rtm_list = PartnerStoreTypeUbAggregate.group_by_rtm(ub_data_list=detail_records)
    overall_ub_sub_rtm_list = PartnerStoreTypeUbAggregate.group_by_sub_rtm(ub_data_list=detail_records)
    overall_ub_store_type_list = PartnerStoreTypeUbAggregate.group_by_store_type(ub_data_list=detail_records)

    # 构造返回结构: rtm 纬度汇总数据
    for rtm_item in overall_ub_rtm_list:
        rtm_item_origin = rtm_item.as_display_dict()
        rtm_sub_rtm_list = rtm_item_origin.setdefault("child", [])
        result_list.append(rtm_item_origin)

        # 具体rtm下sub_rtm纬度汇总数据
        sub_rtm_items = filter_records_by_rtm(overall_ub_sub_rtm_list, rtm_item.rtm)
        for sub_rtm_item in sub_rtm_items:
            sub_rtm_item_origin = sub_rtm_item.as_display_dict()
            sub_rtm_store_type_list = sub_rtm_item_origin.setdefault("child", [])
            rtm_sub_rtm_list.append(sub_rtm_item_origin)

            # 具体sub_rtm下store_type纬度汇总数据
            store_type_items = filter_records_by_sub_rtm(overall_ub_store_type_list, sub_rtm_item.rtm, sub_rtm_item.sub_rtm)
            for store_type_item in store_type_items:
                store_type_item_origin = store_type_item.as_display_dict()
                store_type_item_origin.setdefault("child", [])
                sub_rtm_store_type_list.append(store_type_item_origin)
    return result_list


def get_store_type_ub_dynamic_columns(snapshot_date: str) -> dict:
    dynamic_columns = PartnerStoreTypeUbTracking.get_dynamic_info_by_snapshot_date(snapshot_date=snapshot_date)
    if dynamic_columns:
        return {
            "last_updated": dynamic_columns["last_updated"].strftime('%Y-%m-%d %H:%M:%S'),
            "columns": {
                "qtd_current_quarter_name": dynamic_columns["qtd_current_quarter_name"],
                "qtd_last_quarter_name": dynamic_columns["qtd_last_quarter_name"],
                "weekly_trend_cw_name": dynamic_columns["weekly_trend_cw_name"],
                "weekly_trend_cw1_name": dynamic_columns["weekly_trend_cw1_name"],
                "weekly_trend_cw2_name": dynamic_columns["weekly_trend_cw2_name"],
                "weekly_trend_cw3_name": dynamic_columns["weekly_trend_cw3_name"],
                "weekly_trend_cw4_name": dynamic_columns["weekly_trend_cw4_name"],
                "last_weekly_trend_cw_name": dynamic_columns["last_weekly_trend_cw_name"] if dynamic_columns["last_weekly_trend_cw_name"] else dynamic_columns["weekly_trend_cw_name"],
                "last_weekly_trend_cw1_name": dynamic_columns["last_weekly_trend_cw1_name"] if dynamic_columns["last_weekly_trend_cw1_name"] else dynamic_columns["weekly_trend_cw1_name"]
            }
        }
    return dynamic_columns


def get_partner_menu(view: str, view_tab: str, permission_rtm_list: list[str]):
    # 查询menu数据
    tracking_node = get_ub_tracking_node(view, view_tab)
    if not tracking_node:
        raise ErrorExcept(ErrCode.Param, "view or view_tab type is incorrect.")
    table_cls = tracking_node.get_tbl_cls()
    rtm_list = tracking_node.get_rtm_list()
    permission_rtm_list = list(set(rtm_list) & set(permission_rtm_list))
    query_kwargs = {
        "rtms": permission_rtm_list
    }
    menu_data = Menu(query_func=table_cls.query_menu, query_kwargs=query_kwargs).get_result_menu()
    history_report_date_list = table_cls.query_history_report_date(permission_rtm_list)

    if not menu_data or not history_report_date_list:
        logging.info(f"partner_menu::menu is empty, menu_data: {menu_data},"
                     f"history_report_date_list: {history_report_date_list}")
        return {}

    # 定制化结构: 去掉sub_lob是All的和mpns字段
    for item in menu_data["lobs"]:
        item["sub_lobs"] = [sub_item["sub_lob"] for sub_item in item["sub_lobs"] if sub_item["sub_lob"] != ALL]
    menu_lobs = menu_data["lobs"]

    # 定制化结构: 只保留rtm
    menu_rtm_list = []
    for region_item in menu_data["regions"]:
        for rtm_item in region_item["rtms"]:
            tmp_rtm = rtm_item["rtm"]
            if region_item["region"] != REGION_CM:  # 这里目前只考虑China mainland
                continue
            # 用户拥有2个及以上的RTM的权限，则有All、RTM a、RTM b，默认展示为All, 如果用户仅有1个RTM的权限，则默认展示该RTM
            if tmp_rtm != ALL or len(permission_rtm_list) >= 2:
                menu_rtm_list.append(tmp_rtm)

    # 当天无数据, 报警异常信息
    now = datetime.now()
    current_date = now.strftime('%Y-%m-%d')
    try:
        if current_date not in history_report_date_list:
            render_data = {"env": 'dev' if env_dev() else 'prod',
                           "desc": f'Channel Compliance - POS UB Tracking - Partner - {view_tab}',
                           "period": current_date,
                           "error_info": f"{view_tab} 无当日数据, 请注意排查"
                           }
            sender = Sender(EmailCmd.DashboardAlert)
            sender.send(customized_data=CustomizedData(now, render_data))
    except Exception as e:
        logging.error(f"{view_tab} 无当日({current_date})数据 error: {traceback.format_exc()}")

    result = {
        "report_date_list": history_report_date_list,
        "rtms": menu_rtm_list,
        "lobs": menu_lobs,
    }
    return result
