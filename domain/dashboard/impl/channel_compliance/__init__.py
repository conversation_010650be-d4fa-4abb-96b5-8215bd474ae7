from enum import Enum

from util.const import ALL


# GC 汇总行显示名称
DISPLAY_GC_NAME = 'China Channel'

# 对外显示RTM名称
DISPLAY_RTM_MULTI = "Multi Brand"
DISPLAY_RTM_MONO = "Mono Brand"
DISPLAY_RTM_CARRIER = "Carrier"
DISPLAY_RTM_EDU = "Education"
DISPLAY_RTM_ENT = "Enterprise"
DISPLAY_RTM_CHANNEL_ONLINE = "Channel Online"

DB_RTM_MULTI = "Multi Brand"
DB_RTM_Mono = "Mono Brand"
DB_RTM_CARRIER = "Carrier"
DB_RTM_EDU = "EDU"
DB_RTM_ENT = "ENT"
DB_RTM_CHANNEL_ONLINE = "Channel Online"

# 自定义的RTM排序列表
CUSTOM_RTM_SORT_RULE = [ALL, DISPLAY_RTM_MONO, DISPLAY_RTM_MULTI, DISPLAY_RTM_CHANNEL_ONLINE, DISPLAY_RTM_CARRIER,
                        DISPLAY_RTM_ENT, DISPLAY_RTM_EDU]
CUSTOM_SUB_RTM_SORT_RULE = [ALL, "Lifestyle", "Mono", "OTC", "Township", "MM", "Duty-free", "CM", "CU", "CT", "CB"]
CUSTOM_ND_T1_SORT_RULE = ["All", "T1", "ND"]
CUSTOM_STORE_TYPE_SORT_RULE = [
    "All",  # 汇总行
    "Whitelist", "Suspend List", "Non-DS", "Others",  # Multi Brand - OTC、Township、Mass Merchant排序
    "DS POS", "Non-DS AAR", "Approved Online", "No Approved Online", "Enterprise", "Unauthorized",  # Carrier 排序
]


# 数据库rmt名称对外显示映射关系
db_rtm_to_display_rtm_dict = {
    DB_RTM_MULTI: DISPLAY_RTM_MULTI,
    DB_RTM_Mono: DISPLAY_RTM_MONO,
    DB_RTM_CARRIER: DISPLAY_RTM_CARRIER,
    DB_RTM_CHANNEL_ONLINE: DISPLAY_RTM_CHANNEL_ONLINE,
    DB_RTM_ENT: DISPLAY_RTM_ENT,
    DB_RTM_EDU: DISPLAY_RTM_EDU,
}

# 数据库rmt名称对外显示映射关系
display_rtm_to_db_rtm_dict = {
    DISPLAY_RTM_MULTI: DB_RTM_MULTI,
    DISPLAY_RTM_MONO: DB_RTM_Mono,
    DISPLAY_RTM_CARRIER: DB_RTM_CARRIER,
    DISPLAY_RTM_CHANNEL_ONLINE: DB_RTM_CHANNEL_ONLINE,
    DISPLAY_RTM_ENT: DB_RTM_ENT,
    DISPLAY_RTM_EDU: DB_RTM_EDU,
}

# 数据库sub_rmt名称对外显示映射关系
db_sub_rtm_to_display_sub_rtm_dict = {
    DB_RTM_ENT: DISPLAY_RTM_ENT,
    DB_RTM_EDU: DISPLAY_RTM_EDU,
}

# RTM权限映射数据库RTM查询名称
PERMISSION_RTM_MONO = 'Mono'
PERMISSION_RTM_MULTI = 'Multi'
PERMISSION_RTM_CARRIER = 'Carrier'
PERMISSION_RTM_ONLINE = 'Online'
PERMISSION_RTM_EDUCATION = 'Education'
PERMISSION_RTM_ENTERPRISE = 'Enterprise'

RTM_PERMISSION_TO_DB_QUERY_RTM_MAPPING = {
    PERMISSION_RTM_MONO: DB_RTM_Mono,
    PERMISSION_RTM_MULTI: DB_RTM_MULTI,
    PERMISSION_RTM_CARRIER: DB_RTM_CARRIER,
    PERMISSION_RTM_ONLINE: DB_RTM_CHANNEL_ONLINE,
    PERMISSION_RTM_EDUCATION: DB_RTM_EDU,
    PERMISSION_RTM_ENTERPRISE: DB_RTM_ENT
}


class PartnerUbViewEnum(Enum):
    OVERALL = "overall"
    STORE_TYPE = "store_type"
    
    
class UbTabType(Enum):
    RTM = 'rtm'
    SUB_RTM = 'sub_rtm'
    ND_T1 = 'nd_t1'


StrOnline = 'Online'
StrOffline = 'Offline'
StrUnauthorized = 'Unauthorized'
StrOthers = 'Others'
StrChannelOnline = 'Channel Online'
StrOTC = 'OTC'
StrChinaBroadNet = 'China BroadNet'
StrCB = 'CB'

SortMap = {
    'rtm': ['China Channel', 'Mono Brand', 'Multi Brand', StrChannelOnline, 'Carrier', 'Education', 'Enterprise'],
    'sub_rtm': ["Lifestyle", "Mono", StrOTC, "Township", "Mass Merchant", "China Mobile", "China Unicom",
                "China Telecom", StrChinaBroadNet],
    'online_offline': [StrOnline, StrOffline, StrUnauthorized],
    'platform': ['JD', 'Meituan', 'Wechat', 'Douyin', 'Tmall', 'Eleme', 'Banking', 'Alipay', 'Carrier Own', 'Others'],
    'product_group': ['16 Pro', '16 Cons.', '15 All', StrOthers],
    'sub_lob': ['16 Pro Max', '16 Pro', '16 Plus', '16', '15 Pro Max','15 Pro', '15 Plus', '15']
}
DataTypePartner = 'partner'
DataTypeOnlinePlatform = 'online_platform'
DataTypeSublob = 'sub_lob'
StrikethroughChar = '-'
UbSeparateStr = f' {StrikethroughChar} '
PropRtm = 'rtm'
PropSubRtm = 'sub_rtm'
PropHqId = 'hq_id'
PropHqName = 'hq_name'
PropSublob = 'sub_lob'
PropOnlineOffline = 'online_offline'
PropPlatform = 'platform'
PropProductGroup = 'product_group'
NameReplaceMap = {
    DB_RTM_MULTI: DISPLAY_RTM_MULTI,
    DB_RTM_Mono: DISPLAY_RTM_MONO,
    DB_RTM_CARRIER: DISPLAY_RTM_CARRIER,
    DB_RTM_CHANNEL_ONLINE: DISPLAY_RTM_CHANNEL_ONLINE,
    DB_RTM_ENT: DISPLAY_RTM_ENT,
    DB_RTM_EDU: DISPLAY_RTM_EDU,
    'MM': "Mass Merchant",
    'CM': "China Mobile",
    'CU': "China Unicom",
    'CT': "China Telecom",
    StrCB: StrChinaBroadNet
}

KeyItemName = 'item_name'
KeyChild = 'child'

NdTypeT1 = 'T1'
NdTypeNd = 'ND'

numeratorFields = ['ub3_acc_lq', 'ub7_acc_lq', 'ub3_acc_cq', 'ub7_acc_cq', 'ub7_cw', 'ub7_cw1', 'ub7_cw2', 'ub7_cw3', 'ub7_cw4', 'last_ub7_cw', 'last_ub7_cw1']
denominatorFields = ['total_so_acc_lq', 'total_so_acc_lq', 'total_so_acc_cq', 'total_so_acc_cq', 'total_so_cw', 'total_so_cw1', 'total_so_cw2', 'total_so_cw3', 'total_so_cw4', 'last_total_so_cw', 'last_total_so_cw1']

RTM_SUB_RTM_PLATFORM_MAPPING = {
    "Mono Brand": {
        "Lifestyle": {
            "Online": ['JD', 'Meituan', 'Wechat', 'Douyin', 'Tmall', 'Eleme', 'Others'],
            "Offline": ['']
        },
        "Mono": {
            "Online": ['JD', 'Meituan', 'Wechat', 'Douyin', 'Tmall', 'Eleme', 'Others'],
            "Offline": ['']
        }
    },
    "Multi Brand": {
        "OTC": {
            # "Online": ['JD', 'Meituan', 'Others'], 产品要求: 去掉Online
            "Online": [],
            "Offline": ['']
        },
        "Township": {
            "Online": ['JD', 'Meituan', 'Wechat'],
            "Offline": ['']
        },
        "MM": {
            "Online": ['Others'],
            "Offline": ['']
        }
    },
    "Channel Online": {
        "": {
            "Online": ['JD', 'Douyin', 'Tmall', 'Banking', 'Alipay', 'Others'],
            "Offline": []  # Channel Online 只有online, 没有offline
        }

    },
    "Carrier": {
        "CM": {
            "Online": ['JD', 'Tmall', 'Carrier Own', 'Others'],
            "Offline": [''],
            "Unauthorized": [''],
        },
        "CU": {
            "Online": ['JD', 'Tmall', 'Carrier Own', 'Others'],
            "Offline": [''],
            "Unauthorized": [''],
        },
        "CT": {
            "Online": ['JD', 'Tmall', 'Carrier Own', 'Others'],
            "Offline": [''],
            "Unauthorized": [''],
        },
        "CB": {
            "Online": [],
            "Offline": [''],
            "Unauthorized": [''],
        },
    },
    "Enterprise": {
        "ENT": {
            "Online": ['JD', 'Others'],
            "Offline": ['']
        },
    },
    "Education": {
        "EDU": {
            "Online": ['Others'],
            "Offline": ['']
        }
    }
}

PRODUCT_GROUP_SUB_LOB_MAPPING = {
    "16 Pro": ['iPhone 16 Pro Max', 'iPhone 16 Pro'],
    "16 Cons.": ['iPhone 16 Plus', 'iPhone 16'],
    "15 All": ['iPhone 15 Pro Max', 'iPhone 15 Pro', 'iPhone 15 Plus', 'iPhone 15'],
    "Others": ['']
}
