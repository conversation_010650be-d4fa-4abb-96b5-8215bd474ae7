import io
from typing import Optional
import numpy as np
import pandas as pd

from data.databend.dashboard.dashboard_po_gap import PoGapDi
from domain.demand.entity.const import PO_GAP_FILE_NAME, PO_GAP_SHEET_NAME, PO_GAP_RTM_SORT_RULE
from domain.demand.impl.carrier_mpn import replace_rp_mpn_to_carrier_mpn
from util.conf import logger
from util.const import Enterprise, Education, RETAIL_PARTNER, ALL, CARRIER
from util.util import common_sort_df


def ignore_none_data(data: Optional[int]) -> int:
    if data is None:
        return 0
    return data


def calculate_po_gap_rate(demand, gap):
    # 1. CW+1 Gap% = abs.[(CW+1 PO Gap) / Final Demand]*100%
    # 2. CW+2 Gap% = abs.[(CW+2 PO Gap) / Final Demand]*100%
    # 3. 四舍五入保留百分数后的1位小数。
    # 4. 如实际CW+1 / CW+2 PO Gap的原始计算结果为正数，则PO Gap按0计算Gap%，结果默认为0%。
    if gap >= 0:
        return 0

    if gap < 0 and demand:
        return abs(gap) / demand
    return 0    # 分母为0, 暂时返回0


def build_po_gap_rate(item: dict):
    cw1_demand = ignore_none_data(item["cw1_demand"])
    cw2_demand = ignore_none_data(item["cw2_demand"])
    po_needed_cw1 = ignore_none_data(item["po_needed_cw1"])
    po_needed_cw2 = ignore_none_data(item["po_needed_cw2"])
    item['cw1_po_gap_rate'] = calculate_po_gap_rate(demand=cw1_demand, gap=po_needed_cw1)
    item['cw2_po_gap_rate'] = calculate_po_gap_rate(demand=cw2_demand, gap=po_needed_cw2)


def convert_data_from_nan_to_none(view_data: list) -> list:
    """异常数据处理:
        列表数据在转换成dataframe转换, 某一列同时包含数字、None. 会导致得到的结果包含numpy nan
    """
    fillnan_column = ['cw1_demand', 'available_po_cw1', 'po_needed_cw1', 'cw2_demand', 'available_po_cw2', 'po_needed_cw2']
    df = pd.DataFrame(view_data)
    df_filled = df.copy()  # 复制一份 DataFrame，避免修改原始数据
    if not df.empty:
        df_filled[fillnan_column] = df_filled[fillnan_column].fillna(0)
    return df_filled.to_dict('records')


def update_po_gap_conditions(target, item):
    target["cw1_demand"] += ignore_none_data(item["cw1_demand"])
    target["available_po_cw1"] += ignore_none_data(item["available_po_cw1"])
    target["po_needed_cw1"] += ignore_none_data(item["po_needed_cw1"])
    target["cw2_demand"] += ignore_none_data(item["cw2_demand"])
    target["available_po_cw2"] += ignore_none_data(item["available_po_cw2"])
    target["po_needed_cw2"] += ignore_none_data(item["po_needed_cw2"])
    # 计算po gap rate
    build_po_gap_rate(target)


def sort_po_gap_data(sort_key: list, source: list, reverse=False) -> list:
    sorted_data = common_sort_df(pd.DataFrame(source), sort_key, reverse=reverse).to_dict("records")
    return convert_data_from_nan_to_none(view_data=sorted_data)


def sort_po_gap_rtm_data(source: list) -> list:
    try:
        sorted_rtm = sorted(
            source, key=lambda x: PO_GAP_RTM_SORT_RULE.index(x["rtm"])
        )
    except Exception as e:
        logger.error(f"sort po gap rtm error: {e}")
        raise e
    return sorted_rtm


def init_product_view_schema(product_view_data):
    # 按照各纬度(sub lob->nand->color)分类数据
    tmp_result = {}
    for item in product_view_data:
        # 计算po gap rate
        build_po_gap_rate(item)

        sub_lob = item['sub_lob']
        nand = item['nand']
        color = item['color']
        item["child"] = []
        sub_lob_entry = tmp_result.setdefault(sub_lob, {})
        nand_entry = sub_lob_entry.setdefault(nand, {})
        # 特殊处理: 每个sub lob整体下的汇总行数据，color默认是展示空
        if nand == "All" and color == "All":
            item["color"] = None
        nand_entry.setdefault(color, item)

    # 构造最终的返回结构
    total_info = {
        "sub_lob": "Total",
        "nand": None,
        "color": None,
        "cw1_demand": 0,
        "available_po_cw1": 0,
        "po_needed_cw1": 0,
        "cw2_demand": 0,
        "available_po_cw2": 0,
        "po_needed_cw2": 0,
    }
    result_list = []
    for _, sub_lob_entry in tmp_result.items():
        if not ("All" in sub_lob_entry and "All" in sub_lob_entry["All"]):  # 防错: 防止数据源没有统计汇总数据(即nand和color都是all)
            logger.error("po gap: product view no summary record for nand(all) and color(all)")
            break
        sub_lob_all_entry = sub_lob_entry.pop("All")["All"]  # 每个sub lob下的整体汇总行
        sub_lob_all_entry["child"] = []

        # 一级total汇总行(累加)
        update_po_gap_conditions(total_info, sub_lob_all_entry)
        for _, nand_entry in sub_lob_entry.items():  # 每个sub lob下所有nand的分类数据
            nand_all_entry = nand_entry.pop("All")  # 每个nand 下的整体汇总行
            # color 排序
            nand_all_entry["child"] = sort_po_gap_data(sort_key=["color"], source=list(nand_entry.values()))
            sub_lob_all_entry["child"].append(nand_all_entry)
        # nand排序
        sub_lob_all_entry["child"] = sort_po_gap_data(sort_key=["nand"], source=sub_lob_all_entry["child"])
        result_list.append(sub_lob_all_entry)

    # sub lob排序
    result_list = sort_po_gap_data(sort_key=["sub_lob"], source=result_list)
    if result_list:
        result_list.insert(0, total_info)
    return result_list


def get_product_view_data(region: str, rtm: str, sub_rtm: str, lob: str, sub_lobs: list, fiscal_week: str) -> tuple:
    # get product view data
    product_view_data = PoGapDi.query_product_view(region=region, rtm=rtm, sub_rtm=sub_rtm,
                                                   lob=lob, sub_lobs=sub_lobs, fiscal_week=fiscal_week)
    product_view_data = convert_data_from_nan_to_none(view_data=product_view_data)

    latest_refresh_time = PoGapDi.get_latest_refresh_time()
    result_list = init_product_view_schema(product_view_data)
    return latest_refresh_time, result_list


def get_po_gap_file(rtm: str, fiscal_week: str, channel: str) -> tuple:
    # get po gap data
    po_gap_records = PoGapDi.query_download_po_gap_records(fiscal_week=fiscal_week, rtm=rtm)
    latest_refresh_time = PoGapDi.get_latest_refresh_time()

    # file name
    file_name = PO_GAP_FILE_NAME.format(latest_refresh_time)
    # report excel
    sheet_name = PO_GAP_SHEET_NAME
    df = pd.DataFrame(po_gap_records)
    if df.empty:
        raise FileNotFoundError(f'po gap no data, fiscal_week: {fiscal_week}')

    sort_key = ["region", "rtm", "sub_rtm", "sold_to_id", "sub_lob", "nand", "color", "mpn"]
    df = common_sort_df(df, sort_key)
    df = df.rename(columns={
        'sold_to_id': 'cust_id',
        'sub_rtm': 'business_type',
        'po_needed_cw1': 'po_gap_cw1',
        'po_needed_cw2': 'po_gap_cw2',
        'available_po_cw1': 'open_po_cw1',
        'available_po_cw2': 'open_po_cw2'
    })

    # 20250402 Carrier的MPN要转换前的MPN(cp&f 看的还是rp_mpn)
    if channel == CARRIER:
        replace_rp_mpn_to_carrier_mpn(df, 'mpn', "rtm")

    # 返回文件流，不在系统中创建文件
    excel_file_bytes = io.BytesIO()
    df.to_excel(excel_file_bytes, sheet_name=sheet_name, index=False)
    excel_file_bytes.seek(0)
    return file_name, excel_file_bytes


def get_po_gap_menu(rtm: Optional[str], fiscal_week) -> list:
    po_gap_records = PoGapDi.query_menu_po_gap_records(fiscal_week=fiscal_week, rtm=rtm)
    if not po_gap_records:
        logger.error(f'po gap no menu, fiscal_week: {fiscal_week}, rtm: {rtm}')
    return po_gap_records


def init_channel_view_schema(channel_view_data):
    tmp_result = {}
    # 汇总求和
    for item in channel_view_data:
        # 计算po gap rate
        build_po_gap_rate(item)

        rtm = item['rtm']
        sub_lob = item['sub_lob']
        sub_rtm = item['sub_rtm']

        # 初始化最外层rtm
        if rtm not in tmp_result:
            tmp_result[rtm] = {
                "rtm": rtm,
                "child": {}
            }
        rtm_entry = tmp_result[rtm]

        # Edu和Ent、RP只保留All行, 因为只有一个sub rtm
        if rtm in [RETAIL_PARTNER, Enterprise, Education] and sub_rtm != "All":
            continue

        # 初始化第二层sub_rtm
        if sub_rtm not in rtm_entry["child"]:
            rtm_entry["child"][sub_rtm] = {
                "rtm": rtm,
                "sub_rtm": sub_rtm,
                "sub_lob": "All",
                "cw1_demand": 0,
                "available_po_cw1": 0,
                "po_needed_cw1": 0,
                "cw2_demand": 0,
                "available_po_cw2": 0,
                "po_needed_cw2": 0,
                "child": {}
            }

        sub_rtm_entry = rtm_entry["child"][sub_rtm]
        # sub lob 为all的累加求和计算
        update_po_gap_conditions(sub_rtm_entry, item)

        # 初始化第三层sub_lob
        item_copy = item.copy()
        item_copy["child"] = []
        sub_rtm_entry["child"][sub_lob] = item_copy

    # 构造最终的返回结构
    total_info = {
        "rtm": "Total",
        "sub_rtm": None,
        "sub_lob": None,
        "cw1_demand": 0,
        "available_po_cw1": 0,
        "po_needed_cw1": 0,
        "cw2_demand": 0,
        "available_po_cw2": 0,
        "po_needed_cw2": 0,
        "child": []
    }
    result_list = []
    for _, rtm_entry in tmp_result.items():
        rtm_total_all_entry = rtm_entry["child"]["All"]     # 每个rtm下的整体汇总行
        # 一级total汇总行(累加)
        update_po_gap_conditions(total_info, rtm_total_all_entry)
        # sub rtm 排序
        sorted_sub_rtm = sort_po_gap_data(sort_key=["sub_rtm"], source=list(rtm_entry["child"].values()))
        for tmp_item in sorted_sub_rtm:
            if tmp_item["sub_rtm"] == "All":
                sorted_sub_rtm.remove(tmp_item)
                sorted_sub_rtm.insert(0, tmp_item)
        rtm_entry["child"] = sorted_sub_rtm
        for sub_rtm_entry in rtm_entry["child"]:
            # sub lob 排序
            sub_rtm_entry["child"] = sort_po_gap_data(sort_key=["sub_lob"],
                                                      source=list(sub_rtm_entry["child"].values()))
        result_list.append(rtm_entry)

    # rtm 排序
    result_list = sort_po_gap_rtm_data(source=result_list)
    if result_list:
        result_list.insert(0, {
            "rtm": "Total",
            "child": [total_info]
        })
    return result_list


def get_channel_view_data(region: str, lob: str, sub_lobs: list, rtm: str, fiscal_week: str) -> tuple:
    # get channel view data
    channel_view_data = PoGapDi.query_channel_view(region=region, lob=lob, sub_lobs=sub_lobs,
                                                   fiscal_week=fiscal_week, rtm=rtm)
    channel_view_data = convert_data_from_nan_to_none(view_data=channel_view_data)

    latest_refresh_time = PoGapDi.get_latest_refresh_time()
    result_list = init_channel_view_schema(channel_view_data)

    return latest_refresh_time, result_list


def preprocessing_sold_to_view_data(view_data) -> list:
    new_view_data = []
    # 预处理数据
    for item in view_data:
        # 20250304 产品要求去掉各个rtm下 sub_rtm=all下 sold to != all的数据
        if item['rtm'] not in [ALL, RETAIL_PARTNER] and item['sub_rtm'] == ALL and item['sold_to_id'] != ALL:
            continue

        # 20250306 产品要求ent、edu一级行下，还要展示一个sub_rtm是ent、edu的数据 @jsons
        # 根据数据组的结构来的: Edu和Ent、RP只保留非All行, 因为只有一个sub rtm
        # if item['rtm'] in [RETAIL_PARTNER, Enterprise, Education]:
        #     if item['sub_rtm'] == ALL:
        #         continue
        #     item['sub_rtm'] = ALL

        if item['rtm'] == ALL:
            item['rtm'] = 'Total'

        # 计算po gap rate
        build_po_gap_rate(item)
        new_view_data.append(item)
    return new_view_data


def pack_sold_to_view_data_to_dict(view_data) -> dict:
    sold_to_view_dict = {}
    for item in view_data:
        key = (item['rtm'], item['sub_rtm'], item['sold_to_id'])
        sold_to_view_dict[key] = item
    return sold_to_view_dict


def init_sold_to_view_schema(sold_to_viw_data):
    preprocessing_view_data = preprocessing_sold_to_view_data(sold_to_viw_data)
    sold_to_view_data_dict = pack_sold_to_view_data_to_dict(preprocessing_view_data)

    # 层级结构整理
    tmp_result = {}
    for item in preprocessing_view_data:
        rtm = item['rtm']
        sub_rtm = item['sub_rtm']
        sold_to_id = item['sold_to_id']

        # 初始化最外层rtm
        if rtm not in tmp_result:
            tmp_result[rtm] = {
                "rtm": rtm,
                "child": {}
            }
        rtm_entry = tmp_result[rtm]

        # 初始化第二层sub_rtm(sold_to_id是all的)
        if sub_rtm not in rtm_entry["child"]:
            rtm_entry["child"][sub_rtm] = sold_to_view_data_dict.get((
                rtm, sub_rtm, ALL
            ))
            rtm_entry["child"][sub_rtm]['child'] = {}
        sub_rtm_entry = rtm_entry["child"][sub_rtm]

        # 初始化第三层sold to id(sold_to_id是非all的)
        if sold_to_id == ALL:
            continue
        item_copy = item.copy()
        item_copy["child"] = []
        sub_rtm_entry["child"][sold_to_id] = item_copy

    # 排序
    result_list = []
    for rtm_key, rtm_entry in tmp_result.items():
        # sub rtm 排序
        sorted_sub_rtm = sort_po_gap_data(sort_key=["sub_rtm"], source=list(rtm_entry["child"].values()))
        for tmp_item in sorted_sub_rtm:
            if tmp_item["sub_rtm"] == "All":
                sorted_sub_rtm.remove(tmp_item)
                sorted_sub_rtm.insert(0, tmp_item)
        rtm_entry["child"] = sorted_sub_rtm
        for sub_rtm_entry in rtm_entry["child"]:
            # sold to name 排序
            sub_rtm_entry["child"] = sorted(list(sub_rtm_entry["child"].values()),
                                            key=lambda x: (x['sold_to_name'] is None, x['sold_to_name'] if x['sold_to_name'] is not None else ""))
        # 重命名
        if rtm_key == 'Total':
            rtm_entry['child'][0]['sub_rtm'] = None
            rtm_entry['child'][0]['sold_to_id'] = None
            rtm_entry['child'][0]['sold_to_name'] = None

        result_list.append(rtm_entry)

    # rtm 排序
    result_list = sort_po_gap_rtm_data(source=result_list)
    return result_list


def get_sold_to_view_data(region: str, lob: str, sub_lobs: list, rtm: str, fiscal_week: str) -> tuple:
    # get sold to view data
    sold_to_view = PoGapDi.query_sold_to_view(region=region, lob=lob, sub_lobs=sub_lobs,
                                              fiscal_week=fiscal_week, rtm=rtm)
    sold_to_view_data = convert_data_from_nan_to_none(view_data=sold_to_view)

    latest_refresh_time = PoGapDi.get_latest_refresh_time()
    result_list = init_sold_to_view_schema(sold_to_view_data)

    return latest_refresh_time, result_list


def get_fiscal_weeks():
    # 保留52周
    fiscal_weeks = PoGapDi.get_fiscal_weeks(default_limit=52)
    return fiscal_weeks
