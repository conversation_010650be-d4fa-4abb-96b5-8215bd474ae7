from kit.parallel_executor import AbstractHandler
from data.databend.dashboard.forecast_comparison.forecast_comparison_dfa import ForecastComparisonDFA
from data.databend.dashboard.forecast_comparison.forecast_comparison_rtm_and_ml import ForecastComparisonRTMAndML


class DFAHandler(AbstractHandler):
    def __init__(self, name, region, sub_lob, fiscal_week, **kwargs):
        super().__init__(name)
        self.region = region
        self.sub_lob = sub_lob
        self.fiscal_week = fiscal_week
        self.kwargs = kwargs

    def handle(self):
        return ForecastComparisonDFA.query_comparison_dfa_data(region=self.region,
                                                               sub_lob=self.sub_lob,
                                                               fiscal_week=self.fiscal_week)


class MLHandler(AbstractHandler):
    def __init__(self, name, region, sub_lob, fiscal_week, **kwargs):
        super().__init__(name)
        self.region = region
        self.sub_lob = sub_lob
        self.fiscal_week = fiscal_week
        self.kwargs = kwargs

    def handle(self):
        return ForecastComparisonRTMAndML.query_comparison_data(region=self.region,
                                                                sub_lob=self.sub_lob,
                                                                fiscal_week=self.fiscal_week)


class DFAUpdateTimeHandler(AbstractHandler):
    def __init__(self, name, fiscal_week, **kwargs):
        super().__init__(name)
        self.fiscal_week = fiscal_week
        self.kwargs = kwargs

    def handle(self):
        return ForecastComparisonDFA.get_latest_refresh_time(fiscal_week=self.fiscal_week)


class MLUpdateTimeHandler(AbstractHandler):
    def __init__(self, name, fiscal_week, **kwargs):
        super().__init__(name)
        self.fiscal_week = fiscal_week
        self.kwargs = kwargs

    def handle(self):
        return ForecastComparisonRTMAndML.get_latest_refresh_time(fiscal_week=self.fiscal_week)
