from typing import Union

import numpy as np
import pandas as pd

from domain.dashboard.entity.forecast_type import ForecastType
from domain.dashboard.impl.forecast_comparison.data_handler import *
from domain.demand.entity.const import FORECAST_COMPARISON_RTM_SORT_RULE
from kit.parallel_executor import ParallelExecutor
from util.conf import logger


def calculate_diff_and_percentage(s1: dict, s2: dict, column_names: list[str], keywords: str, need_lw_minus_total=True):
    vs_res = {}
    if not (s1 and s2):
        logger.info(f"calculate diff {keywords}: the of source data is empty. s1: {s1}, s2: {s2}")
        return None

    for column in column_names:
        s1_value = s1[column]
        s2_value = s2[column]
        # 兼容一下数据异常(数据计算结果的依赖项并未全部准确齐全,但已出计算结果，能展示多少数据展示多少)
        if s1_value is None or s2_value is None:
            vs_res[column] = None
            continue
        tmp = vs_res.setdefault(column, {})
        diff = s1_value - s2_value
        tmp["delta"] = diff
        # cw vs lw 列并不是(A-B)/B的百分比,而是cw(%) - lw(%)
        if column == "total_lw_minus_total":
            tmp["variance"] = 0
            continue

        if s2[column] == 0:
            percentage_change = 0
        else:
            percentage_change = diff / s2_value
        tmp["variance"] = percentage_change

    if need_lw_minus_total and vs_res["total_lw_minus_total"] and vs_res["total_lw"] and vs_res["total"]:
        vs_res["total_lw_minus_total"]["variance"] = vs_res["total"]["variance"] - vs_res["total_lw"]["variance"]
    return vs_res


def standardize_comparison_data(dfa_comparison: list, ml_comparison: list) -> list:
    # 拼接模型: ML + DFA
    ml_dfa_forecast_records = []
    if ml_comparison and dfa_comparison:
        ml_df = pd.DataFrame(ml_comparison)
        ml_df = ml_df[(ml_df['data_type'] == ForecastType.ML_NATIONAL.value) & (ml_df['rtm'] == 'All')]
        dfa_df = pd.DataFrame(dfa_comparison)
        tmp_ml_dfa_merge = pd.merge(ml_df, dfa_df,
                                    on=['fiscal_week_year', 'fiscal_qtr_week_name', 'sales_org',
                                        'rtm', 'lob', 'sub_lob'],
                                    suffixes=('_ml', '_dfa'))

        re_columns = {'cw_ml': 'cw', 'cw1_ml': 'cw1', 'cw2_dfa': 'cw2', 'cw3_dfa': 'cw3', 'cw4_dfa': 'cw4', 'cw5_dfa': 'cw5', 'cw6_dfa': 'cw6'}
        filter_columns = ['fiscal_week_year', 'fiscal_qtr_week_name', 'sales_org', 'rtm', 'lob', 'sub_lob',
                          'cw_ml', 'cw1_ml', 'cw2_dfa', 'cw3_dfa', 'cw4_dfa', 'cw5_dfa', 'cw6_dfa']
        ml_dfa_forecast = tmp_ml_dfa_merge[filter_columns].rename(columns=re_columns)
        ml_dfa_forecast['data_type'] = 'ML+DFA'
        ml_dfa_forecast['total'] = 0
        ml_dfa_forecast['total_lw'] = 0
        ml_dfa_forecast['total_lw_minus_total'] = 0
        ml_dfa_forecast_records = ml_dfa_forecast.to_dict('records')

    standard_data = dfa_comparison + ml_comparison + ml_dfa_forecast_records
    for item in standard_data:
        if item["rtm"] == "Online":
            name = 'Online (ST)'
        elif item["rtm"] != 'All':
            name = f'{item["rtm"]} (UB)'
        else:
            name = item["rtm"]
        item['name'] = name
    return standard_data


def fill_missed_rtm(source: list) -> None:
    """
    产品要求: 4个rtm必须都要展示, 无数据时(数据库无记录), 返回null, 前端展示 -
    """
    rtm_keys = {x["rtm"] for x in source}
    overflow_keys = set(FORECAST_COMPARISON_RTM_SORT_RULE) - rtm_keys
    for rtm in overflow_keys:
        if rtm == "Online":
            name = 'Online (ST)'
        elif rtm != 'All':
            name = f'{rtm} (UB)'
        else:
            name = rtm
        source.append({
            "cw": None,
            "cw1": None,
            "cw2": None,
            "cw3": None,
            "cw4": None,
            "rtm": rtm,
            "name": name
        })


def sort_rtm_data(source: list) -> list:
    # 先补全所有4个rtm
    fill_missed_rtm(source)
    try:
        sorted_rtm = sorted(
            source, key=lambda x: FORECAST_COMPARISON_RTM_SORT_RULE.index(x["rtm"])
        )
    except Exception as e:
        logger.error(f"sort rtm error: {e}")
        raise e
    return sorted_rtm


def get_comparison_result_data(region: str, sub_lob: str, fiscal_week: str) -> dict:
    # 加载数据源
    dfa_handler = DFAHandler(name="dfa_forecast", region=region, sub_lob=sub_lob, fiscal_week=fiscal_week)
    ml_handler = MLHandler(name="ml_forecast", region=region, sub_lob=sub_lob, fiscal_week=fiscal_week)
    dfa_latest_refresh_time_handler = DFAUpdateTimeHandler(name="dfa_latest_refresh_time", region=region, sub_lob=sub_lob,
                                                           fiscal_week=fiscal_week)
    ml_latest_refresh_time_handler = MLUpdateTimeHandler(name="ml_latest_refresh_time", region=region, sub_lob=sub_lob,
                                                         fiscal_week=fiscal_week)
    pe = ParallelExecutor(source=[dfa_handler, ml_handler, dfa_latest_refresh_time_handler, ml_latest_refresh_time_handler])
    parallel_result = pe.execute()
    dfa_latest_refresh_time = parallel_result[dfa_latest_refresh_time_handler.name]
    ml_latest_refresh_time = parallel_result[ml_latest_refresh_time_handler.name]
    dfa_comparison_data = parallel_result[dfa_handler.name]
    ml_comparison_data = parallel_result[ml_handler.name]

    # 标准化数据
    standard_data = standardize_comparison_data(dfa_comparison=dfa_comparison_data, ml_comparison=ml_comparison_data)

    def get_forecast_records(data_type: ForecastType,
                             columns: list,
                             need_all_rtm: bool = True) -> Union[None, list, dict]:
        records = [
            i for i in standard_data
            if i['data_type'] == data_type.value and (i['rtm'] == 'All' if need_all_rtm else i['rtm'] != 'All')
        ]

        if not records:
            return None
        df = pd.DataFrame(records)
        # 兼容一下数据异常(数据计算结果的依赖项并未全部准确齐全,但已出计算结果，能展示多少数据展示多少)
        df.replace({np.nan: None}, inplace=True)
        records = df[columns].to_dict('records')
        # 针对4个rtm返回的是列表, 其他返回的都是汇总记录dict
        if not need_all_rtm:
            return sort_rtm_data(records)
        return records[0]

    # 获取指定数据源
    forecast_columns = ['cw', 'cw1', 'cw2', 'cw3', 'cw4', 'cw5', 'cw6', 'total', 'total_lw', 'total_lw_minus_total']
    dfa_forecast_records = get_forecast_records(data_type=ForecastType.DFA, columns=forecast_columns)
    ml_national_forecast_records = get_forecast_records(data_type=ForecastType.ML_NATIONAL, columns=forecast_columns)
    reseller_feedback_forecast_records = get_forecast_records(data_type=ForecastType.RESELLER_FEEDBACK, columns=forecast_columns)
    ml_dfa_forecast_records = get_forecast_records(data_type=ForecastType.ML_PLUS_DFA, columns=forecast_columns)

    reseller_feedback_vs_dfa = calculate_diff_and_percentage(s1=reseller_feedback_forecast_records,
                                                  s2=dfa_forecast_records,
                                                  column_names=['cw', 'cw1', 'cw2', 'cw3', 'cw4', 'cw5', 'cw6',
                                                                'total', 'total_lw', 'total_lw_minus_total'],
                                                  keywords='reseller_feedback_vs_dfa')

    reseller_feedback_vs_ml = calculate_diff_and_percentage(s1=reseller_feedback_forecast_records,
                                                 s2=ml_national_forecast_records,
                                                 column_names=['cw', 'cw1', 'cw2', 'cw3', 'cw4', 'cw5', 'cw6',
                                                               'total', 'total_lw', 'total_lw_minus_total'],
                                                 keywords='reseller_feedback_vs_ml')

    reseller_feedback_vs_ml_dfa = calculate_diff_and_percentage(s1=reseller_feedback_forecast_records,
                                                     s2=ml_dfa_forecast_records,
                                                     column_names=['cw', 'cw1', 'cw2', 'cw3', 'cw4', 'cw5', 'cw6'],
                                                     need_lw_minus_total=False,
                                                     keywords='reseller_feedback_vs_ml_dfa')
    # 构造返回结构
    forecast = {
        "ML National (UB)": ml_national_forecast_records,
        "DFA (UB)": dfa_forecast_records,
        "Reseller Feedback": reseller_feedback_forecast_records,
        "ML National + DFA (UB)": ml_dfa_forecast_records,
    }

    comparison = {
        "Reseller Feedback vs. DFA": reseller_feedback_vs_dfa,
        "Reseller Feedback vs. ML": reseller_feedback_vs_ml,
        "Reseller Feedback vs. ML+DFA": reseller_feedback_vs_ml_dfa,
    }
    result = {
        "latest_refresh_time": dfa_latest_refresh_time if dfa_latest_refresh_time else ml_latest_refresh_time,
        "forecast": forecast,
        "comparison": comparison
    }
    return result
