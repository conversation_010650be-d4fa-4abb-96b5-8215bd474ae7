import logging

import pandas as pd

from data.databend.dashboard.mpn_mix import DashboardMpnMix
from domain.demand.entity.const import BASE, CW2_SHIPMENT_PLAN, CW_ML_FORECAST, CW1_ML_FORECAST, CW_SHIPMENT_PLAN, \
    CW1_SHIPMENT_PLAN, DI_CW1, AVERAGE_SALES_WEEKS_2_4, AVERAGE_SALES_WEEKS_3_5


class CycleReduceStrategy:
    def __init__(self) -> None:
        pass


def cw1_origin_strategy(mpn_sublob_df, row):
    while True:
        # sublob粒度：CW+1 DS’ WOI = [CW+1 DS’ + CW-1 UB EOU(actual) + CW Shipment Plan - Sum ML National FCST(CW, CW+1)]/Average DFA FCST(CW+2 ~CW+6)
        row = calculate_cw1_woi('ds_woi_cw1_tmp', row, 'ds_cw1_tmp')

        # 如果 CW+1 DS’ WOI<=用户所设定的值，则CW+1 DS = CW+1 DS’
        cw1_mpn_df = mpn_sublob_df.loc[mpn_sublob_df['ds_cal_cw1_flag'] != True]

        if row['ds_woi_cw1_tmp'][0] <= row['region_woi_max'][0] or cw1_mpn_df.empty:
            mpn_sublob_df.loc[:, 'ds_cw1'] = mpn_sublob_df['ds_cw1_tmp']
            return mpn_sublob_df
        else:
            mpn_sublob_df = mpn_sublob_df.sort_values(by='ds_woi_cw1_tmp', ascending=False)
            row, mpn_sublob_df = cw1_cycle_cal(mpn_sublob_df)


def cw2_origin_strategy(mpn_sublob_df, row):
    while True:
        mpn_sublob_df.loc[:, 'ds_cw1'] = mpn_sublob_df['ds_cw1_tmp']

        #  sublob粒度：CW+2 DS’ WOI =  [CW+2 DS’ + CW-1 UB EOU(actual) + CW Shipment Plan + CW+1 DS’ - Sum ML National FCST(CW, CW+1) - CW+2 DFA FCST]/Average DFA FCST(CW+3 ~CW+7)
        row = calculate_cw2_woi('ds_woi_cw2_tmp', row, 'ds_cw1_tmp', 'ds_cw2_tmp')

        cw2_mpn_df = mpn_sublob_df.loc[mpn_sublob_df['ds_cal_cw2_flag'] != True]

        if row['ds_woi_cw2_tmp'][0] <= row['region_woi_max'][0] or cw2_mpn_df.empty:
            # 如果CW+2 DS’ WOI<=用户所设定的值，则CW+2 DS = CW+2 DS’
            mpn_sublob_df.loc[:, 'ds_cw2'] = mpn_sublob_df['ds_cw2_tmp']
            break
        else:
            mpn_sublob_df = mpn_sublob_df.sort_values(by='ds_woi_cw2_tmp', ascending=False)
            row, mpn_sublob_df = cw2_cycle_cal(mpn_sublob_df)
    return mpn_sublob_df


def low_first_strategy(mpn_sublob_df, row, region_woi_max, woi_by_mpn_min):
    # 计算sublob粒度ds_woi_cw1_tmp
    row = calculate_cw1_woi('ds_woi_cw1_tmp', row, 'ds_cw1_tmp')

    if row['ds_woi_cw1_tmp'][0] > region_woi_max:
        cal_cw1_low_first(mpn_sublob_df, row, region_woi_max, woi_by_mpn_min)
        row = calculate_cw2_woi('ds_woi_cw2_tmp', row, 'ds_cw1_tmp', 'ds_cw2_tmp')
    if row['ds_woi_cw2_tmp'][0] > region_woi_max:
        cal_cw2_low_first(mpn_sublob_df, row, region_woi_max, woi_by_mpn_min)

    return mpn_sublob_df


# 保留小数点后一位,不四舍五入
def truncate_float(x):
    return int(x * 10) / 10.0


def calculate_base(new_df):
    # base = Sum ML National FCST(CW, CW+1) - CW-1 UB EOU(actual) - CW Shipment Plan
    new_df[BASE] = new_df[CW_ML_FORECAST] + new_df[CW1_ML_FORECAST] - new_df['ub_eoh'] - new_df[CW_SHIPMENT_PLAN]
    new_df['sales_cw3_cw5_avg'] = (new_df['forecast_cw3_sales'] + new_df['forecast_cw4_sales'] + new_df['forecast_cw5_sales']) / 5.0
    new_df['sales_cw2_cw4_avg'] = (new_df['forecast_cw2_sales'] + new_df['forecast_cw3_sales'] + new_df['forecast_cw4_sales']) / 5.0


def calculate_dfa_fcst_avg(new_df):
    new_df[AVERAGE_SALES_WEEKS_2_4] = (new_df['forecast_cw2_dfa'] + new_df['forecast_cw3_dfa'] + new_df['forecast_cw4_dfa'] + new_df['forecast_cw5_dfa'] + new_df['forecast_cw6_dfa']) / 5.0
    new_df[AVERAGE_SALES_WEEKS_3_5] = (new_df['forecast_cw3_dfa'] + new_df['forecast_cw4_dfa'] + new_df['forecast_cw5_dfa'] + new_df['forecast_cw6_dfa'] + new_df['forecast_cw7_dfa']) / 5.0



def set_zero_if_negative(new_df, column_name):
    new_df.loc[new_df[column_name] < 0, column_name] = 0
    return new_df


def calculate_cw1_woi(woi_column, new_df, w1_column):
    # CW+1 DS’ WOI = [CW+1 DS’ + CW-1 UB EOU(actual) + CW Shipment Plan - Sum ML National FCST(CW, CW+1)]/Average DFA FCST(CW+2 ~CW+6)
    new_df.loc[:, woi_column] = (new_df[w1_column] - new_df[BASE]) / new_df[AVERAGE_SALES_WEEKS_2_4]
    return set_zero_if_negative(new_df, woi_column)


def calculate_cw2_woi(woi_column, new_df, w1_column, w2_column):
    # CW+2 DS’ WOI =  [CW+2 DS’ + CW-1 UB EOU(actual) + CW Shipment Plan + CW+1 DS’ - Sum ML National FCST(CW, CW+1) - CW+2 DFA FCST]/Average DFA FCST(CW+3 ~CW+7)
    new_df.loc[:, woi_column] = (new_df[w2_column] - new_df[BASE] + new_df[w1_column] - new_df['forecast_cw2_dfa']) / new_df[AVERAGE_SALES_WEEKS_3_5]
    return set_zero_if_negative(new_df, woi_column)


def ds_cw1_tmp_check(new_df):
    new_df.loc[new_df['ds_cw1_tmp'] <= new_df[CW_SHIPMENT_PLAN], 'ds_cw1_tmp'] = new_df[CW_SHIPMENT_PLAN]
    # CW+1 DS’ WOI = [CW+1 Shipment Plan/0 + CW-1 UB EOH(actual) + CW Shipment Plan - Sum ML National FCST(CW, CW+1)]/Average DFA FCST(CW+2 ~CW+4)
    new_df.loc[new_df['ds_cw1_tmp'] <= new_df[CW_SHIPMENT_PLAN], 'ds_woi_cw1'] = (new_df[CW1_SHIPMENT_PLAN] - new_df[BASE])/new_df[AVERAGE_SALES_WEEKS_2_4]
    new_df.loc[new_df['ds_cw1_tmp'] <= new_df[CW_SHIPMENT_PLAN], 'ds_cal_cw1_flag'] = True


def ds_cw2_tmp_check(new_df):
    new_df.loc[new_df['ds_cw2_tmp'] <= new_df[CW1_SHIPMENT_PLAN], 'ds_cw2_tmp'] = new_df[CW1_SHIPMENT_PLAN]
    # CW+2 DS’ WOI =  [CW+2 Shipment Plan/0 + CW-1 UB EOU(actual) + CW Shipment Plan + CW+1 DI - Sum ML National FCST(CW, CW+1) - CW+2 DFA FCST]/Average DFA FCST(CW+3 ~CW+5)
    new_df.loc[new_df['ds_cw2_tmp'] <= new_df[CW1_SHIPMENT_PLAN], 'ds_woi_cw2'] = (new_df[CW2_SHIPMENT_PLAN] - new_df[BASE] + new_df[DI_CW1] - new_df['forecast_cw2_dfa'])/new_df[AVERAGE_SALES_WEEKS_3_5]
    new_df.loc[new_df['ds_cw2_tmp'] <= new_df[CW1_SHIPMENT_PLAN], 'ds_cal_cw2_flag'] = True


def cw1_cycle_cal(mpn_sublob_df):
    # 判断是否有ds_cal_cw1_flag不为True, 且WOI大于woi_by_mpn_min的MPN
    cal_mpn_df = mpn_sublob_df.loc[(mpn_sublob_df['ds_cal_cw1_flag'] != True) & (mpn_sublob_df['ds_woi_cw1_tmp'] > mpn_sublob_df['woi_by_mpn_min'])]

    if len(cal_mpn_df) > 0:
        # 找到最大的mpn_woi
        mpn_woi = cal_mpn_df.iloc[0]['ds_woi_cw1_tmp']
        logging.info(f"mpn_woi: {mpn_woi}, cw1_or_2: cw1, mpn: {cal_mpn_df.iloc[0]['mpn']}")

        # CW+1 DS' = Sum ML National FCST (CW, CW+1 ) + Average DFA FCST(CW+2 ~CW+6)*(最高WOI-0.1) - CW-1 UB EOH (actual) - CW Shipment Plan
        mpn_sublob_df.loc[(mpn_sublob_df['ds_woi_cw1_tmp'] >= (mpn_woi-0.1))
                          & (mpn_sublob_df['ds_cal_cw1_flag'] != True)
                          & mpn_sublob_df['ds_woi_cw1_tmp'] > mpn_sublob_df['woi_by_mpn_min'], 'ds_cw1_tmp'] = mpn_sublob_df[BASE] + mpn_sublob_df[AVERAGE_SALES_WEEKS_2_4] * (mpn_woi - 0.1)

        mpn_sublob_df.loc[(mpn_sublob_df['ds_woi_cw1_tmp'] >= (mpn_woi-0.1))
                          & (mpn_sublob_df['ds_cal_cw1_flag'] != True)
                          & mpn_sublob_df['ds_woi_cw1_tmp'] > mpn_sublob_df['woi_by_mpn_min'], 'ds_woi_cw1_tmp'] = mpn_woi - 0.1

        ds_cw1_tmp_check(mpn_sublob_df)
    else:
        # 将mpn_sublob_df中的所有ds_cal_flag列设为True
        mpn_sublob_df['ds_cal_cw1_flag'] = True

    row = mpn_sublob_df.groupby(['region', 'sub_lob']).sum().reset_index()
    return row, mpn_sublob_df


def cw2_cycle_cal(mpn_sublob_df):

    # 判断是否有ds_cal_cw1_flag不为True, 且WOI大于4.5的MPN
    mpn_df = mpn_sublob_df.loc[(mpn_sublob_df['ds_cal_cw2_flag'] != True)
                               & (mpn_sublob_df['ds_woi_cw2_tmp'] > mpn_sublob_df['woi_by_mpn_min'])]
    if len(mpn_df) > 0:
        mpn_woi = mpn_df.iloc[0]['ds_woi_cw2_tmp']
        logging.info(f"mpn_woi: {mpn_woi}, cw1_or_2: cw2, mpn: {mpn_df.iloc[0]['mpn']}")
        # CW+2 DS’ = Sum ML National FCST (CW , CW+1 ) + CW+2 DFA FCST + Average DFA FCST(CW+3 ~CW+7)*(范围内的最小值) - CW-1 UB EOH (actual) - CW Shipment Plan - CW+1 DS’
        mpn_sublob_df.loc[(mpn_sublob_df['ds_woi_cw2_tmp'] >= (mpn_woi-0.1))
                          & (mpn_sublob_df['ds_cal_cw2_flag'] != True)
                          & mpn_sublob_df['ds_woi_cw2_tmp'] > mpn_sublob_df['woi_by_mpn_min'], 'ds_cw2_tmp'] = mpn_sublob_df[BASE] + mpn_sublob_df['forecast_cw2_dfa'] + mpn_sublob_df[AVERAGE_SALES_WEEKS_3_5] * (mpn_woi - 0.1) - mpn_sublob_df['ds_cw1_tmp']
        mpn_sublob_df.loc[(mpn_sublob_df['ds_woi_cw2_tmp'] >= (mpn_woi-0.1))
                          & (mpn_sublob_df['ds_cal_cw2_flag'] != True)
                          & mpn_sublob_df['ds_woi_cw2_tmp'] > mpn_sublob_df['woi_by_mpn_min'], 'ds_woi_cw2_tmp'] = mpn_woi - 0.1

        ds_cw2_tmp_check(mpn_sublob_df)
    else:
        # 将mpn_sublob_df中的所有ds_cal_flag列设为True
        mpn_sublob_df['ds_cal_cw2_flag'] = True
    row = mpn_sublob_df.groupby(['region', 'sub_lob']).sum().reset_index()
    return row, mpn_sublob_df


def deal_dfa_forecast(fiscal_week, dfa_fcst, lob):
    mpn_mixs = DashboardMpnMix.get_mpn_mix_by_fiscal_week_and_lob(fiscal_week,lob)
    mpn_mixs = pd.DataFrame(mpn_mixs)
    # 将dfa_fcst中的GEO_LEVEL_4改成region，FPH_LEVEL_3改成sub_lob
    dfa_fcst = dfa_fcst.rename(columns={'GEO_LEVEL_4': 'region', 'FPH_LEVEL_3': 'sub_lob'})
    dfa_fcst_df = pd.DataFrame(dfa_fcst)
    #将dfa_fcst_df与mpn_mixs合并，以region和sub_lob为key，dfa_fcst_df中的cw1，cw2，cw3，cw4，cw5，cw6与mpn_mixs中的trimmed_ub_mix相乘，得到new_df
    new_df = mpn_mixs.merge(dfa_fcst_df, on=['region', 'sub_lob'], how='left')
    new_df['forecast_cw1_dfa'] = new_df['cw1'] * new_df['trimmed_ub_mix']
    new_df['forecast_cw2_dfa'] = new_df['cw2'] * new_df['trimmed_ub_mix']
    new_df['forecast_cw3_dfa'] = new_df['cw3'] * new_df['trimmed_ub_mix']
    new_df['forecast_cw4_dfa'] = new_df['cw4'] * new_df['trimmed_ub_mix']
    new_df['forecast_cw5_dfa'] = new_df['cw5'] * new_df['trimmed_ub_mix']
    new_df['forecast_cw6_dfa'] = new_df['cw6'] * new_df['trimmed_ub_mix']
    new_df['forecast_cw7_dfa'] = new_df['cw7'] * new_df['trimmed_ub_mix']
    new_df = new_df.rename(columns={'mpn_id': 'mpn'})
    return new_df


def cal_cw1_low_first(mpn_sublob_df, row, region_woi_max, woi_by_mpn_min):
    mpn_sublob_df = mpn_sublob_df.sort_values(by='ds_woi_cw1_tmp', ascending=False)

    # 用region_woi_max来反推MAX_DS’
    # MAX_CW+1 DS’ = region_woi_max * Average DFA FCST(CW+2 ~CW+6) + (Sum ML National FCST(CW, CW+1) - CW-1 UB EOU(actual) - CW Shipment Plan )
    # MAX_CW+1 DS’ = region_woi_max * Average DFA FCST(CW+2 ~CW+6) + base
    max_ds_cw1_tmp = region_woi_max * row[AVERAGE_SALES_WEEKS_2_4][0] + row[BASE][0]
    # 判断max_ds_woi_cw1_tmp是否大于0，如果小于0 ，则将所有ds_cal_cw1_flag不为True的mpn粒度的ds_woi_cw1_tmp设置为woi_by_mpn_min，以此计算ds_cw1_tmp
    if max_ds_cw1_tmp < 0:
        mpn_sublob_df.loc[mpn_sublob_df['ds_cal_cw1_flag'] != True, 'ds_cw1_tmp'] = mpn_sublob_df[BASE] + mpn_sublob_df[AVERAGE_SALES_WEEKS_2_4] * woi_by_mpn_min
        mpn_sublob_df.loc[mpn_sublob_df['ds_cal_cw1_flag'] != True, 'ds_woi_cw1_tmp'] = woi_by_mpn_min
        mpn_sublob_df = set_zero_if_negative(mpn_sublob_df, 'ds_cw1_tmp')
        # 将ds_cw1_tmp和cw shipment plan比较
        ds_cw1_tmp_check(mpn_sublob_df)

        mpn_sublob_df['ds_cal_cw1_flag'] = True
        row = mpn_sublob_df.groupby(['region', 'sub_lob']).sum().reset_index()
        return row, mpn_sublob_df

    # 当反推出来的max_ds_cw1_tmp大于0，且小于sublob粒度的ds_cw1_tmp时，需要进行调减
    if max_ds_cw1_tmp < row['ds_cw1_tmp'][0]:
        # 首先计算mpn粒度所有不需要调减的ds_cw1_tmp的和
        ds_no_fix_sum = mpn_sublob_df.loc[mpn_sublob_df['ds_cal_cw1_flag'] == True]['ds_cw1_tmp'].sum()

        # 将所有需要调减的ds_woi_cw1_tmp按照升序排列
        ds_woi_list = mpn_sublob_df.loc[mpn_sublob_df['ds_cal_cw1_flag'] != True]['ds_woi_cw1_tmp'].tolist()
        ds_woi_list.sort()

        fix_sum = max_ds_cw1_tmp - ds_no_fix_sum
        final_ds_woi = 0

        for ds_woi in ds_woi_list:
            mpn_sublob_df.loc[mpn_sublob_df['ds_cal_cw1_flag'] != True, 'ds_cw1_tmp_fix'] = mpn_sublob_df[BASE] + mpn_sublob_df['forecast_cw2_dfa'] + mpn_sublob_df[AVERAGE_SALES_WEEKS_3_5] * ds_woi
            ds_need_fix_sum = mpn_sublob_df.loc[mpn_sublob_df['ds_cal_cw1_flag'] != True]['ds_cw1_tmp_fix'].sum()
            if ds_need_fix_sum > fix_sum:
                break
            else:
                final_ds_woi = ds_woi
                mpn_sublob_df.loc[(mpn_sublob_df['ds_cal_cw1_flag'] != True) & mpn_sublob_df['ds_woi_cw1_tmp'] == ds_woi, 'ds_cal_cw1_flag'] = True
                fix_sum = fix_sum - mpn_sublob_df.loc[(mpn_sublob_df['ds_cal_cw1_flag'] != True) & mpn_sublob_df['ds_woi_cw1_tmp'] == ds_woi]['ds_cw1_tmp_fix'].sum()

        # 循环结束，判断fix_sum是否大于0，如果大于0，则将其平均分给所有ds_cw1_tmp，并重新计算ds_woi_cw1_tmp
        if fix_sum > 0:
            mpn_sublob_df.loc[mpn_sublob_df['ds_cal_cw1_flag'] != True, 'ds_cw1_tmp'] = mpn_sublob_df['ds_cw1_tmp'] + fix_sum / len(mpn_sublob_df.loc[mpn_sublob_df['ds_cal_cw1_flag'] != True])
            mpn_sublob_df.loc[mpn_sublob_df['ds_cal_cw1_flag'] != True, 'ds_woi_cw1_tmp'] = (mpn_sublob_df['ds_cw1_tmp'] - mpn_sublob_df[BASE]) / mpn_sublob_df[AVERAGE_SALES_WEEKS_2_4]
            mpn_sublob_df = set_zero_if_negative(mpn_sublob_df, 'ds_cw1_tmp')
            ds_cw1_tmp_check(mpn_sublob_df)

            mpn_sublob_df['ds_cal_cw1_flag'] = True
            row = mpn_sublob_df.groupby(['region', 'sub_lob']).sum().reset_index()
            return row, mpn_sublob_df


def cal_cw2_low_first(mpn_sublob_df, row, region_woi_max, woi_by_mpn_min):
    mpn_sublob_df = mpn_sublob_df.sort_values(by='ds_woi_cw2_tmp', ascending=False)

    # 用region_woi_max来反推MAX_DS’
    # MAX_CW+2 DS’ = region_woi_max * Average DFA FCST(CW+3 ~CW+7) + (Sum ML National FCST(CW, CW+1) - CW-1 UB EOU(actual) - CW Shipment Plan - CW+1 DS’)
    # MAX_CW+2 DS’ = region_woi_max * Average DFA FCST(CW+3 ~CW+7) + base + CW+1 DS’
    max_ds_cw2_tmp = region_woi_max * row[AVERAGE_SALES_WEEKS_3_5][0] + row[BASE][0] + row['ds_cw1_tmp'][0]
    # 判断max_ds_woi_cw2_tmp是否大于0，如果小于0 ，则将所有ds_cal_cw2_flag不为True的mpn粒度的ds_woi_cw2_tmp设置为woi_by_mpn_min，以此计算ds_cw2_tmp
    if max_ds_cw2_tmp < 0:
        mpn_sublob_df.loc[mpn_sublob_df['ds_cal_cw2_flag'] != True, 'ds_cw2_tmp'] = mpn_sublob_df[BASE] + mpn_sublob_df['forecast_cw2_dfa'] + mpn_sublob_df[AVERAGE_SALES_WEEKS_3_5] * woi_by_mpn_min - mpn_sublob_df['ds_cw1_tmp']
        mpn_sublob_df.loc[mpn_sublob_df['ds_cal_cw2_flag'] != True, 'ds_woi_cw2_tmp'] = woi_by_mpn_min
        mpn_sublob_df = set_zero_if_negative(mpn_sublob_df, 'ds_cw2_tmp')
        ds_cw2_tmp_check(mpn_sublob_df)

        mpn_sublob_df['ds_cal_cw2_flag'] = True
        row = mpn_sublob_df.groupby(['region', 'sub_lob']).sum().reset_index()
        return row, mpn_sublob_df

    # 当反推出来的max_ds_cw2_tmp大于0，且小于sublob粒度的ds_cw2_tmp时，需要进行调减
    if max_ds_cw2_tmp < row['ds_cw2_tmp'][0]:
        # 首先计算mpn粒度所有不需要调减的ds_cw2_tmp的和
        ds_no_fix_sum = mpn_sublob_df.loc[mpn_sublob_df['ds_cal_cw2_flag'] == True]['ds_cw2_tmp'].sum()

        # 将所有需要调减的ds_woi_cw2_tmp按照升序排列
        ds_woi_list = mpn_sublob_df.loc[mpn_sublob_df['ds_cal_cw2_flag'] != True]['ds_woi_cw2_tmp'].tolist()
        ds_woi_list.sort()

        fix_sum = max_ds_cw2_tmp - ds_no_fix_sum
        final_ds_woi = 0

        for ds_woi in ds_woi_list:
            mpn_sublob_df.loc[mpn_sublob_df['ds_cal_cw2_flag'] != True, 'ds_cw2_tmp_fix'] = mpn_sublob_df[BASE] + mpn_sublob_df['forecast_cw2_dfa'] + mpn_sublob_df[AVERAGE_SALES_WEEKS_3_5] * ds_woi - mpn_sublob_df['ds_cw1_tmp']
            ds_need_fix_sum = mpn_sublob_df.loc[mpn_sublob_df['ds_cal_cw2_flag'] != True]['ds_cw2_tmp_fix'].sum()
            if ds_need_fix_sum > fix_sum:
                break
            else:
                final_ds_woi = ds_woi
                mpn_sublob_df.loc[(mpn_sublob_df['ds_cal_cw2_flag'] != True) & mpn_sublob_df[
                    'ds_woi_cw2_tmp'] == ds_woi, 'ds_cal_cw2_flag'] = True
                fix_sum = fix_sum - mpn_sublob_df.loc[(mpn_sublob_df['ds_cal_cw2_flag'] != True) & mpn_sublob_df['ds_woi_cw2_tmp'] == ds_woi]['ds_cw2_tmp_fix'].sum()

        # 循环结束，判断fix_sum是否大于0，如果大于0，则将其平均分给所有ds_cw2_tmp，并重新计算ds_woi_cw2_tmp
        if fix_sum > 0:
            mpn_sublob_df.loc[mpn_sublob_df['ds_cal_cw2_flag'] != True, 'ds_cw2_tmp'] = mpn_sublob_df['ds_cw2_tmp'] + fix_sum / len(mpn_sublob_df.loc[mpn_sublob_df['ds_cal_cw2_flag'] != True])
            mpn_sublob_df.loc[mpn_sublob_df['ds_cal_cw2_flag'] != True, 'ds_woi_cw2_tmp'] = (mpn_sublob_df['ds_cw2_tmp'] - mpn_sublob_df[BASE] + mpn_sublob_df['di_cw1'] - mpn_sublob_df['forecast_cw2_dfa']) / mpn_sublob_df[AVERAGE_SALES_WEEKS_3_5]
            mpn_sublob_df = set_zero_if_negative(mpn_sublob_df, 'ds_cw2_tmp')
            ds_cw2_tmp_check(mpn_sublob_df)

            mpn_sublob_df['ds_cal_cw2_flag'] = True
            row = mpn_sublob_df.groupby(['region', 'sub_lob']).sum().reset_index()
            return row, mpn_sublob_df
