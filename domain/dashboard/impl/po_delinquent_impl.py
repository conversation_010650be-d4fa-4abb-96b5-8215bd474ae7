import datetime
import io
import os
import copy
from typing import Optional
import pandas as pd
import numpy as np


from data.databend.dashboard.dashboard_po_delinquent import PoDelinquentDi
from data.email_config import EmailConfigRepository
from domain.demand.entity.const import PO_DELINQUENT_FILE_NAME, PO_DELINQUENT_SHEET_NAME, PO_DELINQUENT_RTM_SORT_RULE
from domain.demand.impl.carrier_mpn import replace_rp_mpn_to_carrier_mpn
from kit.token_bucket import EMAIL_BUCKET_KEY_PREFIX
from util.conf import logger
from util.const import Enterprise, Education, RETAIL_PARTNER, EmailCmd, REGION_CM, ALL, CARRIER
from util.send_email import async_rate_limited_send_email_v2, del_email_config
from util.util import common_sort_df, custom_round


def update_po_delinquent_conditions(target, item):
    target["open_po"] += item["open_po"]
    target["2wks_total_demand"] += item["2wks_total_demand"]
    target["cond_1"] += item["cond_1"]
    target["cond_2"] += item["cond_2"]
    target["cond_3"] += item["cond_3"]
    target["cond_4"] += item["cond_4"]
    target["cond_5"] += item["cond_5"]
    
    if "cum_po" in target and "cum_po" in item:
        if not np.isnan(item["cum_po"]):
            target["cum_po"] += item["cum_po"]


def sort_po_delinquent_data(sort_key: list, source: list) -> list:
    sorted_data = common_sort_df(pd.DataFrame(source), sort_key).to_dict("records")
    return sorted_data


def sort_po_delinquent_rtm_data(source: list) -> list:
    try:
        sorted_rtm = sorted(
            source, key=lambda x: PO_DELINQUENT_RTM_SORT_RULE.index(x["rtm"])
        )
    except Exception as e:
        logger.error(f"sort po delinquent rtm error: {e}")
        raise e
    return sorted_rtm


def init_product_view_schema(product_view_data):
    # 按照各纬度(sub lob->nand->color)分类数据
    tmp_result = {}
    for item in product_view_data:
        sub_lob = item['sub_lob']
        nand = item['nand']
        color = item['color']
        item["child"] = []
        sub_lob_entry = tmp_result.setdefault(sub_lob, {})
        nand_entry = sub_lob_entry.setdefault(nand, {})
        # 特殊处理: 每个sub lob整体下的汇总行数据，color默认是展示空
        if nand == "All" and color == "All":
            item["color"] = None
        nand_entry.setdefault(color, item)

    # 构造最终的返回结构
    total_info = {
        "sub_lob": "Total",
        "nand": None,
        "color": None,
        "open_po": 0,
        "2wks_total_demand": 0,
        "cond_1": 0,
        "cond_2": 0,
        "cond_3": 0,
        "cond_4": 0,
        "cond_5": 0,
    }
    result_list = []
    for _, sub_lob_entry in tmp_result.items():
        if not ("All" in sub_lob_entry and "All" in sub_lob_entry["All"]):  # 防错: 防止数据源没有统计汇总数据(即nand和color都是all)
            logger.error("po delinquent: product view no summary record for nand(all) and color(all)")
            break
        sub_lob_all_entry = sub_lob_entry.pop("All")["All"]  # 每个sub lob下的整体汇总行
        sub_lob_all_entry["child"] = []

        # 一级total汇总行(累加)
        update_po_delinquent_conditions(total_info, sub_lob_all_entry)
        for _, nand_entry in sub_lob_entry.items():  # 每个sub lob下所有nand的分类数据
            nand_all_entry = nand_entry.pop("All")  # 每个nand 下的整体汇总行
            # color 排序
            nand_all_entry["child"] = sort_po_delinquent_data(sort_key=["color"], source=list(nand_entry.values()))
            sub_lob_all_entry["child"].append(nand_all_entry)
        # nand排序
        sub_lob_all_entry["child"] = sort_po_delinquent_data(sort_key=["nand"], source=sub_lob_all_entry["child"])
        result_list.append(sub_lob_all_entry)

    # sub lob排序
    result_list = sort_po_delinquent_data(sort_key=["sub_lob"], source=result_list)
    # 兼容一下Hong Kong/Taiwan下的Education数据(其实兼容所有), 如果对应筛选条件没有数据, 前端展示No Data
    if result_list:
        result_list.insert(0, total_info)
    return result_list


def get_product_view_data(region: str, rtm: str, sub_rtm: str, lob: str, sub_lobs: list) -> tuple:
    fiscal_dt = PoDelinquentDi.get_max_fiscal_dt()
    # get product view data
    product_view_data = PoDelinquentDi.query_product_view(region=region,
                                                          rtm=rtm,
                                                          sub_rtm=sub_rtm,
                                                          lob=lob,
                                                          sub_lobs=sub_lobs,
                                                          fiscal_dt=fiscal_dt)

    latest_refresh_time = PoDelinquentDi.get_latest_refresh_time(fiscal_dt=fiscal_dt)
    result_list = init_product_view_schema(product_view_data)
    return latest_refresh_time, result_list


def get_po_delinquent_file(region: list, rtm: Optional[str], channel: str) -> tuple:
    fiscal_dt = PoDelinquentDi.get_max_fiscal_dt()
    # get po delinquent data
    po_delinquent_records = PoDelinquentDi.query_download_po_delinquent_records(fiscal_dt=fiscal_dt, region=region, rtm=rtm)
    latest_refresh_time = PoDelinquentDi.get_latest_refresh_time(fiscal_dt=fiscal_dt)

    # file name
    file_name = PO_DELINQUENT_FILE_NAME.format(latest_refresh_time)
    # report excel
    sheet_name = PO_DELINQUENT_SHEET_NAME
    df = pd.DataFrame(po_delinquent_records)
    if df.empty:
        raise FileNotFoundError(f'po delinquent no data, max_fiscal_dt: {fiscal_dt}')

    sort_key = ["region", "rtm", "sub_rtm", "sub_lob"]
    df = common_sort_df(df, sort_key)

    # 20250402 Carrier的MPN要转换前的MPN(cp&f 看的还是rp_mpn)
    if channel == CARRIER:
        replace_rp_mpn_to_carrier_mpn(df, 'mpn', "rtm")

    # 返回文件流，不在系统中创建文件
    excel_file_bytes = io.BytesIO()
    df.to_excel(excel_file_bytes, sheet_name=sheet_name, index=False)
    excel_file_bytes.seek(0)
    return file_name, excel_file_bytes


def get_po_delinquent_menu(region: list, rtm: str) -> list:
    fiscal_dt = PoDelinquentDi.get_max_fiscal_dt()
    po_delinquent_records = PoDelinquentDi.query_menu_po_delinquent_records(fiscal_dt=fiscal_dt, region=region, rtm=rtm)
    if not po_delinquent_records:
        logger.error(f'po delinquent no menu, max_fiscal_week_year: {fiscal_dt}, rtm: {rtm}, region: {region}')
    return po_delinquent_records


def init_channel_view_schema(channel_view_data):
    tmp_result = {}
    # 汇总求和
    for item in channel_view_data:
        region = item['region']
        rtm = item['rtm']
        sub_lob = item['sub_lob']
        sub_rtm = item['sub_rtm']

        # 按照region聚合, 9.6号支持多region筛选
        if region not in tmp_result:
            tmp_result[region] = {
                "region": region,
                "rtms": {}
            }
        region_entry = tmp_result[region]['rtms']

        # 初始化最外层rtm
        if rtm not in region_entry:
            region_entry[rtm] = {
                "rtm": rtm,
                "child": {}
            }
        rtm_entry = region_entry[rtm]
        # Edu和Ent只保留All行, 因为只有一个sub rtm
        if rtm == Enterprise and sub_rtm != "All":
            continue

        if rtm == Education and sub_rtm != "All":
            continue

        if rtm == RETAIL_PARTNER and sub_rtm != "All":
            continue

        # 初始化第二层sub_rtm
        if sub_rtm not in rtm_entry["child"]:
            rtm_entry["child"][sub_rtm] = {
                "rtm": rtm,
                "sub_rtm": sub_rtm,
                "sub_lob": "All",
                "cum_po": 0,
                "open_po": 0,
                "2wks_total_demand": 0,
                "cond_1": 0,
                "cond_2": 0,
                "cond_3": 0,
                "cond_4": 0,
                "cond_5": 0,
                "child": {}
            }

        sub_rtm_entry = rtm_entry["child"][sub_rtm]
        # sub lob 为all的累加求和计算
        update_po_delinquent_conditions(sub_rtm_entry, item)

        # 初始化第三层sub_lob
        item_copy = item.copy()
        item_copy["child"] = []
        sub_rtm_entry["child"][sub_lob] = item_copy

    # 构造最终的返回结构
    result_dict = {}
    for tmp_region, tmp_region_result in tmp_result.items():
        result_list = []
        total_info = {
            "rtm": "Total",
            "sub_rtm": None,
            "sub_lob": None,
            "cum_po": 0,
            "open_po": 0,
            "2wks_total_demand": 0,
            "cond_1": 0,
            "cond_2": 0,
            "cond_3": 0,
            "cond_4": 0,
            "cond_5": 0,
            "child": []
        }
        for tmp_rtm, rtm_entry in tmp_region_result["rtms"].items():
            rtm_total_all_entry = rtm_entry["child"]["All"]     # 每个rtm下的整体汇总行
            # 9.6号支持多region筛选, 会把RP数据也筛选出来, 对于China mainland来讲， rtm加和时需要排除RP
            if not (tmp_region == REGION_CM and tmp_rtm == RETAIL_PARTNER):
                # 一级total汇总行(累加)
                update_po_delinquent_conditions(total_info, rtm_total_all_entry)
            # sub rtm 排序
            sorted_sub_rtm = sort_po_delinquent_data(sort_key=["sub_rtm"], source=list(rtm_entry["child"].values()))
            for tmp_item in sorted_sub_rtm:
                if tmp_item["sub_rtm"] == "All":
                    sorted_sub_rtm.remove(tmp_item)
                    sorted_sub_rtm.insert(0, tmp_item)
            rtm_entry["child"] = sorted_sub_rtm
            for sub_rtm_entry in rtm_entry["child"]:
                # sub lob 排序
                sub_rtm_entry["child"] = sort_po_delinquent_data(sort_key=["sub_lob"],
                                                                 source=list(sub_rtm_entry["child"].values()))
            result_list.append(rtm_entry)
        # rtm 排序
        result_list = sort_po_delinquent_rtm_data(source=result_list)
        if result_list:
            result_list.insert(0, {
                "rtm": "Total",
                "child": [total_info]
            })
        result_dict[tmp_region] = {
            "region": tmp_region,
            "rtms": result_list
        }
    return result_dict


def get_channel_view_data(lob: str,
                          sub_lobs: list,
                          rtm: Optional[str]=None,
                          region: Optional[str] = None,
                          source: str = 'api') -> tuple:
    fiscal_dt = PoDelinquentDi.get_max_fiscal_dt()

    # get channel view data
    channel_view_data = PoDelinquentDi.query_channel_view(region=region,
                                                          lob=lob,
                                                          sub_lobs=sub_lobs,
                                                          fiscal_dt=fiscal_dt,
                                                          rtm=rtm)

    latest_refresh_time = PoDelinquentDi.get_latest_refresh_time(fiscal_dt=fiscal_dt)

    result = init_channel_view_schema(channel_view_data)
    if region is not None:
        region_result = result.get(region, {})
        result = region_result.get('rtms', list())

    # 数据为空的话，邮件告警
    if not result:
        email_config = EmailConfigRepository.query_email_config(EmailCmd.Dashboard)
        params = {"env": os.environ.get('ENV'), "dashboard": 'po delinquent', "source": source}
        file_paths, email_config = del_email_config(email_config=email_config, params=params, read_template=False)

        async_rate_limited_send_email_v2(limit_key=EMAIL_BUCKET_KEY_PREFIX + 'po_delinquent',
                                         capacity=1,
                                         expire_time=1200,
                                         email_config=email_config,
                                         file_paths=file_paths)

    return latest_refresh_time, result


def channel_view_data_for_email(lob: str, sub_lobs: list, source: str = 'email_report') -> tuple:
    sort_region = ["China mainland", "Hong Kong", "Taiwan"]
    sort_rtm = ["Carrier", "Retail Partner", "Mono Brand", "Multi Brand", "Channel Online", "Enterprise", "Education"]
    rtm_db_to_display_mapping = {
        "Mono": "Mono Brand",
        "Multi": "Multi Brand",
        "Carrier": "Carrier",
        "Online": "Channel Online",
        "Retail Partner": "Retail Partner",
        "Enterprise": "Enterprise",
        "Education": "Education",
    }
    # 返回结构说明
    """
    result: {
        "China mainland": [
            "region": "China mainland",
            "rtms":  [
            {
                "child": [
                    {
                        "child": [],
                        "cond_1": 240,
                        "cond_2": 25390,
                        "cond_3": 31550,
                        "cond_4": 285910,
                        "cond_5": 3702130,
                        "open_po": 4045220,
                        "rtm": "Total",
                        "sub_lob": null,
                        "sub_rtm": null
                    }
                ],
                "rtm": "Total"
            },
            {
                "child": [
                    {
                        "child": [
                            {
                                "child": [],
                                "cond_1": 0,
                                "cond_2": 11340,
                                "cond_3": 300,
                                "cond_4": 0,
                                "cond_5": 100,
                                "open_po": 11740,
                                "rtm": "Mono",
                                "sub_lob": "iPhone 15 Pro Max",
                                "sub_rtm": "All"
                            },
                            {
                                "child": [],
                                "cond_1": 0,
                                "cond_2": 0,
                                "cond_3": 11090,
                                "cond_4": 50,
                                "cond_5": 5150,
                                "open_po": 16290,
                                "rtm": "Mono",
                                "sub_lob": "iPhone 15 Pro",
                                "sub_rtm": "All"
                            },
                            {
                                "child": [],
                                "cond_1": 0,
                                "cond_2": 0,
                                "cond_3": 410,
                                "cond_4": 0,
                                "cond_5": 3510,
                                "open_po": 3920,
                                "rtm": "Mono",
                                "sub_lob": "iPhone 15 Plus",
                                "sub_rtm": "All"
                            },
                            {
                                "child": [],
                                "cond_1": 0,
                                "cond_2": 10,
                                "cond_3": 1020,
                                "cond_4": 0,
                                "cond_5": 15680,
                                "open_po": 16710,
                                "rtm": "Mono",
                                "sub_lob": "iPhone 15",
                                "sub_rtm": "All"
                            },
                            {
                                "child": [],
                                "cond_1": 0,
                                "cond_2": 0,
                                "cond_3": 0,
                                "cond_4": 0,
                                "cond_5": 20,
                                "open_po": 20,
                                "rtm": "Mono",
                                "sub_lob": "iPhone 14 Plus",
                                "sub_rtm": "All"
                            },
                            {
                                "child": [],
                                "cond_1": 0,
                                "cond_2": 0,
                                "cond_3": 0,
                                "cond_4": 0,
                                "cond_5": 230,
                                "open_po": 230,
                                "rtm": "Mono",
                                "sub_lob": "iPhone 14",
                                "sub_rtm": "All"
                            },
                            {
                                "child": [],
                                "cond_1": 0,
                                "cond_2": 0,
                                "cond_3": 0,
                                "cond_4": 0,
                                "cond_5": 1020,
                                "open_po": 1020,
                                "rtm": "Mono",
                                "sub_lob": "iPhone 13",
                                "sub_rtm": "All"
                            }
                        ],
                        "cond_1": 0,
                        "cond_2": 11350,
                        "cond_3": 12820,
                        "cond_4": 50,
                        "cond_5": 25710,
                        "open_po": 49930,
                        "rtm": "Mono",
                        "sub_lob": "All",
                        "sub_rtm": "All"
                    },
                    {
                        "child": [
                            {
                                "child": [],
                                "cond_1": 0,
                                "cond_2": 0,
                                "cond_3": 400,
                                "cond_4": 50,
                                "cond_5": 1430,
                                "open_po": 1880,
                                "rtm": "Mono",
                                "sub_lob": "iPhone 15 Pro",
                                "sub_rtm": "Lifestyle"
                            },
                            {
                                "child": [],
                                "cond_1": 0,
                                "cond_2": 0,
                                "cond_3": 0,
                                "cond_4": 0,
                                "cond_5": 340,
                                "open_po": 340,
                                "rtm": "Mono",
                                "sub_lob": "iPhone 15 Plus",
                                "sub_rtm": "Lifestyle"
                            },
                            {
                                "child": [],
                                "cond_1": 0,
                                "cond_2": 10,
                                "cond_3": 0,
                                "cond_4": 0,
                                "cond_5": 210,
                                "open_po": 220,
                                "rtm": "Mono",
                                "sub_lob": "iPhone 15",
                                "sub_rtm": "Lifestyle"
                            },
                            {
                                "child": [],
                                "cond_1": 0,
                                "cond_2": 0,
                                "cond_3": 0,
                                "cond_4": 0,
                                "cond_5": 20,
                                "open_po": 20,
                                "rtm": "Mono",
                                "sub_lob": "iPhone 14 Plus",
                                "sub_rtm": "Lifestyle"
                            },
                            {
                                "child": [],
                                "cond_1": 0,
                                "cond_2": 0,
                                "cond_3": 0,
                                "cond_4": 0,
                                "cond_5": 230,
                                "open_po": 230,
                                "rtm": "Mono",
                                "sub_lob": "iPhone 14",
                                "sub_rtm": "Lifestyle"
                            },
                            {
                                "child": [],
                                "cond_1": 0,
                                "cond_2": 0,
                                "cond_3": 0,
                                "cond_4": 0,
                                "cond_5": 1020,
                                "open_po": 1020,
                                "rtm": "Mono",
                                "sub_lob": "iPhone 13",
                                "sub_rtm": "Lifestyle"
                            }
                        ],
                        "cond_1": 0,
                        "cond_2": 10,
                        "cond_3": 400,
                        "cond_4": 50,
                        "cond_5": 3250,
                        "open_po": 3710,
                        "rtm": "Mono",
                        "sub_lob": "All",
                        "sub_rtm": "Lifestyle"
                    },
                    {
                        "child": [
                            {
                                "child": [],
                                "cond_1": 0,
                                "cond_2": 11340,
                                "cond_3": 300,
                                "cond_4": 0,
                                "cond_5": 100,
                                "open_po": 11740,
                                "rtm": "Mono",
                                "sub_lob": "iPhone 15 Pro Max",
                                "sub_rtm": "Mono AAR+"
                            },
                            {
                                "child": [],
                                "cond_1": 0,
                                "cond_2": 0,
                                "cond_3": 10690,
                                "cond_4": 0,
                                "cond_5": 3720,
                                "open_po": 14410,
                                "rtm": "Mono",
                                "sub_lob": "iPhone 15 Pro",
                                "sub_rtm": "Mono AAR+"
                            },
                            {
                                "child": [],
                                "cond_1": 0,
                                "cond_2": 0,
                                "cond_3": 410,
                                "cond_4": 0,
                                "cond_5": 3170,
                                "open_po": 3580,
                                "rtm": "Mono",
                                "sub_lob": "iPhone 15 Plus",
                                "sub_rtm": "Mono AAR+"
                            },
                            {
                                "child": [],
                                "cond_1": 0,
                                "cond_2": 0,
                                "cond_3": 1020,
                                "cond_4": 0,
                                "cond_5": 15470,
                                "open_po": 16490,
                                "rtm": "Mono",
                                "sub_lob": "iPhone 15",
                                "sub_rtm": "Mono AAR+"
                            }
                        ],
                        "cond_1": 0,
                        "cond_2": 11340,
                        "cond_3": 12420,
                        "cond_4": 0,
                        "cond_5": 22460,
                        "open_po": 46220,
                        "rtm": "Mono",
                        "sub_lob": "All",
                        "sub_rtm": "Mono AAR+"
                    }
                ],
                "rtm": "Mono"
            }
        ]
        
        ]
    
    
    }
    
    """
    # email是多region展示
    latest_refresh_time, channel_view_data = get_channel_view_data(lob=lob, sub_lobs=sub_lobs, source=source)

    total_info = {
        "region": "Greater China",
        "rtm": None,
        "sub_rtm": None,
        "sub_lob": None,
        "cum_po": 0,
        "open_po": 0,
        "2wks_total_demand": 0,
        "cond_1": 0,
        "cond_2": 0,
        "cond_3": 0,
        "cond_4": 0,
        "cond_5": 0,
        "child": []
    }
    result_list = []
    # 每个region的数据
    for item in list(channel_view_data.values()):
        region = item['region']
        tmp_rtm_list = []
        for rtm_detail in item['rtms']:
            rtm = rtm_detail['rtm']
            sub_rtm_details = rtm_detail['child']
            if rtm == 'Total':
                # 每个region下的整体汇总行
                total_item = sub_rtm_details[0]
                # region 级别的汇总行(累加)
                update_po_delinquent_conditions(total_info, total_item)
                # 保持格式一样
                total_item['rtm'] = 'All'
                total_item['sub_lob'] = 'All'
                total_item['sub_rtm'] = 'All'
                tmp_rtm_list.append(total_item)
                continue
            for sub_rtm_detail in sub_rtm_details:
                # 去掉第三层child内容, 减少返回内容
                sub_rtm_detail["child"] = []
                
                sub_rtm = sub_rtm_detail['sub_rtm']
                sub_lob = sub_rtm_detail['sub_lob']
                if sub_rtm == 'All' and sub_lob == 'All':
                    sub_rtm_detail['rtm'] = rtm_db_to_display_mapping[sub_rtm_detail['rtm']]
                    tmp_rtm_list.append(sub_rtm_detail)
        # rtm 排序
        sorted_rtm = sorted(
            tmp_rtm_list, key=lambda x: sort_rtm.index(x["rtm"]) if x["rtm"] in sort_rtm else -1
        )
        tmp_region = {
            "region": region,
            "child": sorted_rtm
        }
        result_list.append(tmp_region)

    # region 排序
    result_list = sorted(
        result_list, key=lambda x: sort_region.index(x["region"])
    )
    if result_list:
        result_list.insert(0, {
            "region": "Greater China",
            "child": [total_info]
        })

    return latest_refresh_time, result_list


class POAgingEmailDataView():
    def __init__(self, region_rtm: str, region: str, cum_po: int,
                 open_po: int, cond1: int, cond2: int,
                 cond3: int, cond4: int, cond5: int) -> None:
        self.region_rtm = region_rtm
        self.region = region
        self.cum_po = cum_po
        self.open_po = open_po
        self.cond1 = cond1
        self.cond2 = cond2
        self.cond3 = cond3
        self.cond4 = cond4
        self.cond5 = cond5
        if np.isnan(self.cum_po):
            self.cum_po = 0
    
    def __str__(self) -> str:
        return f"Region: {self.region_rtm}, Cum PO: {self.cum_po}, Open PO: {self.open_po}, Cond1: {self.cond1}, Cond2: {self.cond2}, Cond3: {self.cond3}, Cond4: {self.cond4}, Cond5: {self.cond5}"
    
    def level(self) -> int:
        if self.region_rtm in ["Greater China", "China mainland", "Hong Kong", "Taiwan"]:
            return 1
        elif (self.region_rtm in ["Carrier", "Retail Partner"] 
              or (self.region in ["Hong Kong", "Taiwan"] 
                  and self.region_rtm == "Education")):
            return 2
        else:
            return 3

    def as_dict(self):
        divisor = 1000
        return {
            "region_rtm": self.region_rtm,
            "cum_po": "{:,.0f}".format(custom_round(self.cum_po / divisor)) if self.cum_po is not None else '-',
            "open_po": "{:,.0f}".format(custom_round(self.open_po / divisor)) if self.open_po is not None else '-',
            "cond1": "{:,.0f}".format(custom_round(self.cond1 / divisor)) if self.cond1 is not None else '-',
            "cond2": "{:,.0f}".format(custom_round(self.cond2 / divisor)) if self.cond2 is not None else '-',
            "cond3": "{:,.0f}".format(custom_round(self.cond3 / divisor)) if self.cond3 is not None else '-',
            "cond4": "{:,.0f}".format(custom_round(self.cond4 / divisor)) if self.cond4 is not None else '-',
            "cond5": "{:,.0f}".format(custom_round(self.cond5 / divisor)) if self.cond5 is not None else '-',
            "level": self.level()
        }


def convert_to_email_data(view_data_for_email: list[dict]) -> list[POAgingEmailDataView]:
    po_aging_list = []
    for region_data in view_data_for_email:
        region_rtm = region_data['region']
        for rtm_data in region_data['child']:
            rtm = rtm_data['rtm']
            if rtm != 'All' and rtm is not None:
                region_rtm = rtm
            po_aging_list.append(
                POAgingEmailDataView(
                    region_rtm, region_data['region'], rtm_data['cum_po'],
                    rtm_data['open_po'], rtm_data['cond_1'],
                    rtm_data['cond_2'], rtm_data['cond_3'],
                    rtm_data['cond_4'], rtm_data['cond_5']
                ).as_dict()
            )
    return po_aging_list


def get_sold_to_view_data(region: str, lob: str, sub_lobs: list, rtm: str) -> tuple:
    fiscal_dt = PoDelinquentDi.get_max_fiscal_dt()
    sold_to_view = PoDelinquentDi.query_sold_to_view(region=region, lob=lob, sub_lobs=sub_lobs,
                                                     fiscal_dt=fiscal_dt, rtm=rtm)
    latest_refresh_time = PoDelinquentDi.get_latest_refresh_time(fiscal_dt=fiscal_dt)
    result = init_sold_to_view_schema(sold_to_view)
    return latest_refresh_time, result


def init_sold_to_view_schema(channel_view_data):
    tmp_result = {}
    channel_view_data_dict = {}
    for data in channel_view_data:
        rtm = data['rtm'] or ALL
        sub_rtm = data['sub_rtm'] or ALL
        sold_to_name = data['sold_to_name'] or ALL
        sold_to_id = data['sold_to_id'] or ALL
        keys = '{}-{}-{}-{}'.format(rtm, sub_rtm, sold_to_name, sold_to_id)
        channel_view_data_dict.update({keys: data})
    # 汇总求和
    for item in channel_view_data:
        region = item['region']
        rtm = item['rtm']
        # sub_lob = item['sub_lob']
        sub_rtm = item['sub_rtm']
        sold_to_name = item['sold_to_name'] or ALL
        sold_to_id = item['sold_to_id'] or ALL
        keys = '{}-{}-{}-{}'.format(rtm, sub_rtm, sold_to_name, sold_to_id)

        # 按照region聚合, 9.6号支持多region筛选
        if region not in tmp_result:
            tmp_result[region] = {
                "region": region,
                "rtms": {}
            }
        region_entry = tmp_result[region]['rtms']

        # 初始化最外层rtm
        if rtm not in region_entry:
            region_entry[rtm] = {
                "rtm": rtm,
                "child": {}
            }
        rtm_entry = region_entry[rtm]
        # Edu和Ent只保留All行, 因为只有一个sub rtm
        # if rtm == Enterprise and sub_rtm != "All":
        #     continue
        #
        # if rtm == Education and sub_rtm != "All":
        #     continue
        #
        # if rtm == RETAIL_PARTNER and sub_rtm != "All":
        #     continue

        # 初始化第二层sub_rtm
        if sub_rtm not in rtm_entry["child"]:
            rtm_entry["child"][sub_rtm] = {
                "rtm": rtm,
                "sub_rtm": sub_rtm,
                "sub_lob": "All",
                "sold_to_name": "All",
                "sold_to_id": "All",
                "cum_po": 0,
                "open_po": 0,
                "2wks_total_demand": 0,
                "cond_1": 0,
                "cond_2": 0,
                "cond_3": 0,
                "cond_4": 0,
                "cond_5": 0,
                "child": {}
            }

        sub_rtm_entry = rtm_entry["child"][sub_rtm]

        # 初始化第三层sold_to_name
        if sold_to_name == ALL:
            continue
        item_copy = item.copy()
        item_copy["child"] = []
        value = channel_view_data_dict.get(keys)
        item_copy.update(value)
        sub_rtm_entry["child"][sold_to_id] = item_copy

    # 构造最终的返回结构
    result_list = []
    for tmp_region, tmp_region_result in tmp_result.items():
        for tmp_rtm, rtm_entry in tmp_region_result["rtms"].items():
            rtm_total_all_entry = rtm_entry["child"]["All"]     # 每个rtm下的整体汇总行

            # 9.6号支持多region筛选, 会把RP数据也筛选出来, 对于China mainland来讲， rtm加和时需要排除RP
            # if not (tmp_region == REGION_CM and tmp_rtm == RETAIL_PARTNER):
            #     # 一级total汇总行(累加)
            # update_po_delinquent_conditions(total_info, rtm_total_all_entry)
            # sub rtm 排序
            sorted_sub_rtm = sort_po_delinquent_data(sort_key=["sub_rtm"], source=list(rtm_entry["child"].values()))
            for tmp_item in sorted_sub_rtm:
                rtm = tmp_item['rtm']
                sub_rtm = tmp_item['sub_rtm']
                sold_to_name = tmp_item['sold_to_name'] or ALL
                sold_to_id = tmp_item['sold_to_id'] or ALL
                keys = '{}-{}-{}-{}'.format(rtm, sub_rtm, sold_to_name, sold_to_id)
                value = channel_view_data_dict.get(keys)
                tmp_item.update(value)
                if tmp_item["sub_rtm"] == "All":
                    sorted_sub_rtm.remove(tmp_item)
                    sorted_sub_rtm.insert(0, tmp_item)
            rtm_entry["child"] = sorted_sub_rtm
            for sub_rtm_entry in rtm_entry["child"]:
                # sold to name 排序
                sub_rtm_entry["child"] = sorted(list(sub_rtm_entry["child"].values()),
                                                key=lambda x: (x['sold_to_name'] is None, x['sold_to_name'] if x['sold_to_name'] is not None else ""))
            result_list.append(rtm_entry)
        
        # rtm 排序
        result_list = sort_po_delinquent_rtm_data(source=result_list)
        if result_list:
            total_data = copy.deepcopy(result_list[0]['child'][0])
            if result_list[0]['rtm'] != ALL:
                result_list.insert(0, {'rtm': 'Total', 'child': [total_data]})
            result_list[0]['rtm'] = 'Total'
            result_list[0]['child'][0]['rtm'] = 'Total'
    return result_list
