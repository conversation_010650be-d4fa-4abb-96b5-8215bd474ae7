from domain.dashboard.entity.anti_fraud.anti_fraud_so_ub import AntiFraudSOUBEmailView


# databend中返回的数据格式是list[dict]
def get_anti_fraud_so_ub():
    # TODO 根据条件进行筛选出不同part的数据，比如overall, sub_lob_view
    
    return []

class ConvertType:
    OVERALL = 'overall'
    SUB_LOB_VIEW = 'sub_lob_view'


class ConvertForEmail():
    def __init__(self, view_data_for_email: list[dict], convert_type: ConvertType) -> None:
        self.view_data = view_data_for_email
        self.convert_type = convert_type
    
    def convert(self) -> list[dict]:
        if self.convert_type == ConvertType.OVERALL:
            return self._convert_overall()
        elif self.convert_type == ConvertType.SUB_LOB_VIEW:
            return self._convert_sub_lob_view()
        else:
            return []
    
    def _table_header(self) -> list[str]:
        return []
    
    def _convert_overall(self) -> list[AntiFraudSOUBEmailView]:
        email_data_list = []
        for region_data in self.view_data:
            region_rtm = region_data['region']
            for rtm_data in region_data['child']:
                rtm = rtm_data['rtm']
                if rtm != 'All' and rtm is not None:
                    region_rtm = rtm
                email_data_list.append(
                    AntiFraudSOUBEmailView(
                        region_rtm, region_data['region'], rtm_data['cum_po'],
                        rtm_data['open_po'], rtm_data['cond_1'],
                        rtm_data['cond_2'], rtm_data['cond_3'],
                        rtm_data['cond_4'], rtm_data['cond_5']
                    ).as_dict()
                )
        return []
    
    def _convert_sub_lob_view(self) -> list[AntiFraudSOUB]:
        return []
