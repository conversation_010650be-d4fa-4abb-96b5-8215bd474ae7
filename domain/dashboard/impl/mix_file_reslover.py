import itertools
import pandas as pd
import io
import numpy as np
import uuid
from datetime import datetime

from data.mysqls.demand.demand_processor import DemandProcessor
from domain.dashboard.entity import fiscal_week
from domain.demand.entity.const import TOPDOWN_DEMAND, SELL_IN_DEMAND, NORMALIZED_DEMAND, DELTA_DEMAND, FINAL_DEMAND
from domain.demand.impl.processor.const import DFA_PROCESSOR
from domain.demand.impl.y_value_setting import re_calculate
from util.const import LOB, DateTimeFormat
from kit.validator.customer_rule import AllowedRule, RangeRule
from kit.validator.file import FileValidator
from util.const import ErrorExcept, ErrCode, ErrMsg
from util.file_util import get_file_path
from domain.dashboard.entity.const import (
    MixTemplateFileHeader,
    MixTemplateFileRawHeader,
    DASHBOARD_FILE_CATEGORY_UPLOAD,
    DASHBOARD_FILE_MODULE_MPN_MIX,
    SUBLOB_NANDS,
    SUBLOB_COLORS,
    SUB_LOB,
    NAND,
    COLOR,
)
from data.mysqls.dashboard.dashboard_file_storage import MixUpload, DemandFileRecord


class MpnMixDataReslover:
    def __init__(self) -> None:
        pass

    def mpn_mix_data_to_file(
        self, fiscal_week: str, result: list, adjust_mix: list
    ) -> tuple:
        """
        input:
        fiscal_week:str,
        result:list[{"Fiscal Week":"","Region":"","LOB":"","Sub-LOB":"","Nand":"","Color":"",""ML Fcst Mix%":"","Sales Fcst Mix%":"","Trimmed UB Mix%":"","Trimmed Weeks":""}],
        adjust_mix: [{"Fiscal Week:"","Adjusted Mix%":""。。。}]
        return tuple(file_name,file_bytes文件内容)
        """
        df_data = pd.DataFrame(result)
        df_adjust_mix = pd.DataFrame(adjust_mix)
        if not df_data.empty and not df_adjust_mix.empty:
            df_result = df_data.merge(
                df_adjust_mix,
                how="left",
                on=["Fiscal Week", "Region", "LOB", "Sub-LOB", "Color", "Nand", "Mpn"]
            )
        if df_data.empty:
            df_result = df_adjust_mix
        if df_adjust_mix.empty:
            df_result = df_data
            df_result["Adjusted Mix%"] = None # 这一列肯定是要在下载文件中出现的，当用户没有上传过数据，直接返回None
        file_name = f"{fiscal_week}_China mainland_iPhone_Country_MPN_Mix%.xlsx"
        file_bytes = io.BytesIO()
        df_result.to_excel(file_bytes, index=False)
        file_bytes.seek(0)
        return file_name, file_bytes


class MpnMixFileReslover:
    def __init__(self) -> None:
        pass

    def template_for_adjuested_mix(self, fiscal_week: str, result: list) -> tuple:
        """
        input:fiscal_week:str, result:list[{"region":region,"lob":lob,"sub_lob":sub_lob,"nand":nand,"color":color}]
        return tuple(file_name,file_bytes文件内容)
        """
        df_data = pd.DataFrame(result)
        df_data["Mix%"] = ""
        df_data.reindex(columns=["Region", "LOB", "Sub-LOB", "Nand", "Color", 'Mpn', "Mix%"])
        file_name = "iPhone_MPN MIX %_Template.xlsx"
        file_bytes = io.BytesIO()
        df_data.to_excel(file_bytes,index=False)
        file_bytes.seek(0)
        return file_name, file_bytes


def upload_mix_file_service(fiscal_week: str, file, uploader: str, nand_color_dict: dict) -> str:
    upload_df = pd.read_excel(file)
    
    validate_upload_content(upload_df, nand_color_dict)

    unique_name, _ = save_unique_file(upload_df)

    file_name_with_version = save_to_database(
        upload_df, fiscal_week, unique_name, uploader
    )

    # 删除dfa processor
    DemandProcessor.delete_by_fiscal_week(fiscal_week=fiscal_week,processor=DFA_PROCESSOR)
    # 触发重新计算
    re_calculate(fiscal_week=fiscal_week, demands=[TOPDOWN_DEMAND,SELL_IN_DEMAND,NORMALIZED_DEMAND,DELTA_DEMAND,FINAL_DEMAND])

    return file_name_with_version


def validate_upload_content(upload_df: pd.DataFrame, custom_dict: dict):
    sub_lob_list = list(set([item[0] for item in custom_dict.get(SUBLOB_NANDS)]))
    header_rules = {"header": MixTemplateFileHeader}
    rules = {
        "Region": ["required", AllowedRule(["China mainland"])],
        "LOB": ["required", AllowedRule(["iPhone"])],
        "Sub-LOB": ["required", AllowedRule(sub_lob_list)],
        "Nand": ["required"],
        "Color": ["required"],
        "Mpn": ["required"],
        "Mix%": ["required", RangeRule(0, 1)],
    }
    
    validator = FileValidator(
        upload_df, rules=rules, header_rules=header_rules, group_rules=None
    )
    has_pass_validation, row, errors = validator.do_validate()
    
    # 先判断header是否正确，如果header不正确在进行特殊处理，会报列不存在，不符合预期
    if not has_pass_validation:
        error_info = errors.get("errors").get("header")
        if error_info:
            for error_msg, idx in error_info[0].items():
                raise ErrorExcept(
                    ErrCode.FileUploadError, { "message": error_msg, "row_index": idx }
                )
    
    # 再判断列是否正确
    # 特殊校验nand color
    for column in [SUBLOB_NANDS, SUBLOB_COLORS]:
        raw_column_name = COLOR if column == SUBLOB_COLORS else NAND
        result = ~upload_df[[SUB_LOB, raw_column_name]].apply(tuple, axis=1).isin(custom_dict.get(column))
        # 获取不在列表中的行号
        invalid_indices = upload_df.loc[result].index.tolist()
        if invalid_indices:
            if has_pass_validation:
                has_pass_validation = False
                errors = {"errors": {}}
            errors["errors"][raw_column_name] = [{
                f"The following data row(s) are invalid for wrong {raw_column_name} values: ": [index+2 for index in invalid_indices]
            }]
    
    # 需要按照列的优先级来返回对应的错误，越靠前越先返回
    priority_validation = ["Region", "LOB", "Sub-LOB", "Nand", "Color", "Mpn", "Mix%"]
    if not has_pass_validation:
        # 处理errors返回格式
        error = {
            "message": "",
            "row_index": []
        }
        error_info = errors.get("errors")
        
        if not isinstance(error_info, dict):
            raise ErrorExcept(ErrCode.FileUploadError, error)
        
        for item in priority_validation:
            error_value = error_info.get(item)
            if isinstance(error_value, list) and len(error_value) > 0:
                message_and_row_index = error_value[0]
                if isinstance(message_and_row_index, dict):
                    message, row_index = next(iter(message_and_row_index.items()))
                    all_strings = all(isinstance(item, str) for item in row_index)
                    if all_strings:
                        error["message"] = message + ", ".join(row_index)
                    else:
                        error["message"], error["row_index"] = next(iter(message_and_row_index.items()))
                    break
        # 限定最多返回行号数量
        row_index_list = error.get("row_index")
        max_length = 500
        if len(row_index_list) > max_length:
            error["row_index"] = row_index_list[:max_length]
        raise ErrorExcept(ErrCode.FileUploadError, error)
    
    # 新修改mix按照sub_lob group by sum 总和范围误差不超过0.01，则通过校验
    MAX_OF_SUM, VALUE_OF_ERROR = 1, 0.01
    group_by_df = upload_df.groupby("Sub-LOB").agg({"Mix%": "sum"}).reset_index()
    out_of_range_list = (group_by_df.loc[~group_by_df["Mix%"]
                        .between(MAX_OF_SUM - VALUE_OF_ERROR, MAX_OF_SUM + VALUE_OF_ERROR), "Sub-LOB"]
                        .tolist())
    if len(out_of_range_list):
        raise ErrorExcept(
            ErrCode.FileUploadError, 
            {"message": ("The Mix% uploaded is incomplete or invalid on the following models: "
                         + ", ".join(out_of_range_list)),
             "row_index": []})


def save_unique_file(df: pd.DataFrame) -> tuple[str, str]:
    # 生成文件
    unique_name = f"{uuid.uuid4().hex}.xlsx"
    file_path = get_file_path(unique_name, "/uploads/allocation")
    df.to_excel(file_path, index=False)
    return unique_name, file_path


def save_to_database(df: pd.DataFrame, fiscal_week: str, unique_name: str,
                     uploader: str) -> str:
    # 保存文件信息 {Fiscal Week}_{Region}_{LOB}_Country_MPN_Mix%
    region = "China_Mainland"
    lob = "iPhone"
    file_name = f"{fiscal_week}_{region}_{lob}_Country_MPN_Mix%.xlsx"
    file_storage = DemandFileRecord(
        fiscal_week,
        DASHBOARD_FILE_CATEGORY_UPLOAD,
        DASHBOARD_FILE_MODULE_MPN_MIX,
        f"/file/storage/{unique_name}",
        file_name,
        unique_name,
        uploader,
    )
    file_name_with_version = file_storage.save()

    # 保存文件内容到数据库
    df.columns = MixTemplateFileRawHeader
    df["fiscal_qtr_week_name"] = fiscal_week
    current_time = datetime.now().strftime(DateTimeFormat)
    df["create_time"] = current_time
    df["update_time"] = current_time
    df.replace({np.nan: None}, inplace=True)
    MixUpload.delete_by_week(fiscal_week)
    MixUpload.bulk_save(df.to_dict("records"))
    
    return file_name_with_version


def get_upload_mix_file_service(fiscal_week: str) -> dict:
    file_list = DemandFileRecord.get_file_by_week_category_module(
        fiscal_week, DASHBOARD_FILE_CATEGORY_UPLOAD, DASHBOARD_FILE_MODULE_MPN_MIX
    )
    file_info = {
        "file_id": 0,
        "file_name": "",
        "file_path": "",
        "upload_by": "",
        "upload_time": "",
    }

    if file_list and file_list[0].file_path:
        file_info["file_id"] = file_list[0].id
        file_info["file_name"] = file_list[0].file_name
        file_info["file_path"] = file_list[0].file_path
        file_info["upload_by"] = file_list[0].operator
        file_info["upload_time"] = (
            file_list[0].update_time.strftime(DateTimeFormat)
            if file_list[0].operator
            else None
        )

    return file_info


def delete_file_service(file_id: int, operator: str) -> int:
    file_info = DemandFileRecord.get_file_by_id(file_id)
    if not file_info:
        raise ErrorExcept(ErrCode.System, f"file does not exist.")

    fiscal_week = file_info.fiscal_qtr_week_name
    DemandFileRecord.update(
        {
            "fiscal_qtr_week_name": fiscal_week,
            "category": DASHBOARD_FILE_CATEGORY_UPLOAD,
            "module": DASHBOARD_FILE_MODULE_MPN_MIX,
            "file_path": "",
            "file_name": "",
            "unique_name": "",
            "operator": operator,
        }
    )
    # 删除上传到数据库中的内容
    MixUpload.delete_by_week(fiscal_week)

    return 0
