from data.databend.dashboard.forecast_actual.forecast_accuracy_rtm_and_ml import ForecastAccuracyRTMAndML
from data.databend.dashboard.forecast_comparison.forecast_comparison_rtm_and_ml import ForecastComparisonRTMAndML
from data.databend.dashboard.mpn_mix import DashboardMpnMix
from data.databend.dashboard.ub_velocity import DashboardUbVelocity
from data.fiscal_year_week import FiscalYearWeek
from data.mysqls.demand.demand_by_region_pool import DemandByRegionPool
from kit.container.global_var_container import GlobalVarContainer


def loader() -> dict:
    current_week = FiscalYearWeek.get_current_week()
    fiscal_weeks_ub_velocity = DashboardUbVelocity.get_fiscal_weeks()
    fiscal_weeks_mpn_mix = DashboardMpnMix.get_fiscal_weeks()
    fiscal_weeks_forecast_comparison = ForecastComparisonRTMAndML.get_fiscal_weeks()
    fiscal_weeks_forecast_actual = ForecastAccuracyRTMAndML.get_fiscal_weeks()
    fiscal_weeks_region_poll = DemandByRegionPool.get_fiscal_weeks()
    last_week = FiscalYearWeek.get_last_week()
    return {
        "current_week": current_week,
        "fiscal_weeks_ub_velocity": fiscal_weeks_ub_velocity,
        "fiscal_weeks_mpn_mix": fiscal_weeks_mpn_mix,
        "fiscal_weeks_forecast_comparison": fiscal_weeks_forecast_comparison,
        "fiscal_weeks_forecast_actual": fiscal_weeks_forecast_actual,
        "fiscal_weeks_region_poll": fiscal_weeks_region_poll,
        "last_week": last_week,
    }


week_container = GlobalVarContainer(loader, 60 * 30)
week_container.run()


# class CurrentWeek:
class FiscalWeekContainer:
    def __init__(self) -> None:
        self.last_week: str = week_container.load().get("last_week")
        self.current_week: str = week_container.load().get("current_week")
        self.fiscal_weeks_ub_velocity: str = week_container.load().get(
            "fiscal_weeks_ub_velocity"
        )
        self.fiscal_weeks_mpn_mix: str = week_container.load().get(
            "fiscal_weeks_mpn_mix"
        )
        self.fiscal_weeks_forecast_comparison: str = week_container.load().get(
            "fiscal_weeks_forecast_comparison"
        )
        self.fiscal_weeks_forecast_actual: str = week_container.load().get(
            "fiscal_weeks_forecast_actual"
        )
        self.fiscal_weeks_region_poll: str = week_container.load().get(
            "fiscal_weeks_region_poll"
        )

    def get_fiscal_weeks_ub_velocity(self) -> list:
        return self.fiscal_weeks_ub_velocity

    def get_fiscal_weeks_mpn_mix(self) -> list:
        return self.fiscal_weeks_mpn_mix

    def get_fiscal_weeks_forecast_comparison(self) -> list:
        return self.fiscal_weeks_forecast_comparison

    def get_fiscal_weeks_forecast_actual(self) -> list:
        return self.fiscal_weeks_forecast_actual

    def get_fiscal_weeks_region_poll(self) -> list:
        return self.fiscal_weeks_region_poll

    def get_current_week(self) -> str:
        return self.current_week

    def get_last_week(self) -> str:
        return self.last_week
