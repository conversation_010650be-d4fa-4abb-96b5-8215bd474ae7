from data.databend.dashboard.forecast_actual.forecast_accuracy_sold_to_rtm_and_ml import ForecastAccuracySoldToRTMAndML
from domain.dashboard.entity.forecast_threshold import ForecastThreshold
from kit.parallel_executor import AbstractHandler
from data.databend.dashboard.forecast_actual.forecast_accuracy_dfa import ForecastAccuracyDFA
from data.databend.dashboard.forecast_actual.forecast_accuracy_rtm_and_ml import ForecastAccuracyRTMAndML


class DFAHandler(AbstractHandler):
    def __init__(self, name, weeks, condition, **kwargs):
        super().__init__(name)
        self.weeks = weeks
        self.condition = condition
        self.kwargs = kwargs

    def handle(self):
        return ForecastAccuracyDFA.query_forcast_and_ub(weeks=self.weeks, data_type=None, condition=self.condition)


class MLHandler(AbstractHandler):
    def __init__(self, name, weeks, condition, **kwargs):
        super().__init__(name)
        self.weeks = weeks
        self.condition = condition
        self.kwargs = kwargs

    def handle(self):
        return ForecastAccuracyRTMAndML.query_forcast_and_ub(weeks=self.weeks, data_type=None, condition=self.condition)


class MLSoldToHandler(AbstractHandler):
    def __init__(self, name, weeks, condition, **kwargs):
        super().__init__(name)
        self.weeks = weeks
        self.condition = condition
        self.kwargs = kwargs

    def handle(self):
        return ForecastAccuracySoldToRTMAndML.query_forcast_and_ub(weeks=self.weeks, data_type=None, condition=self.condition)


class DfaUpdateTimeHandler(AbstractHandler):
    def __init__(self, name, fiscal_week, **kwargs):
        super().__init__(name)
        self.fiscal_week = fiscal_week
        self.kwargs = kwargs

    def handle(self):
        return ForecastAccuracyDFA.get_latest_refresh_time(fiscal_week=self.fiscal_week)


class ThresholdHandler(AbstractHandler):
    def __init__(self, name, condition, **kwargs):
        super().__init__(name)
        self.condition = condition
        self.kwargs = kwargs

    def handle(self):
        thresholds = ForecastThreshold(condition=self.condition)
        return thresholds.get_one_or_none_threshold()


class MLUpdateTimeHandler(AbstractHandler):
    def __init__(self, name, fiscal_week, **kwargs):
        super().__init__(name)
        self.fiscal_week = fiscal_week
        self.kwargs = kwargs

    def handle(self):
        return ForecastAccuracyRTMAndML.get_latest_refresh_time(fiscal_week=self.fiscal_week)


class MLSoldToUpdateTimeHandler(AbstractHandler):
    def __init__(self, name, fiscal_week, **kwargs):
        super().__init__(name)
        self.fiscal_week = fiscal_week
        self.kwargs = kwargs

    def handle(self):
        return ForecastAccuracySoldToRTMAndML.get_latest_refresh_time(fiscal_week=self.fiscal_week)
