from domain.dashboard.entity.forecast_type import ForecastAccuracyViewerType, ForecastType


def get_data_type_by_viewer(viewer: str):
    # feedback review sold to粒度
    if viewer == ForecastAccuracyViewerType.FEEDBACK_SOLD_TO_ACCURACY.value:
        data_type_dict = {
            "All": [
                ForecastType.ML_BOTTOM_UP.value,
                ForecastType.ACTUAL.value,
                ForecastType.RESELLER_FEEDBACK.value,
            ]
        }
        return data_type_dict

    # feedback review region 粒度
    if viewer == ForecastAccuracyViewerType.FEEDBACK_REGION_ACCURACY.value:
        data_type_dict = {
            "All": [
                ForecastType.ML_BOTTOM_UP.value,
                ForecastType.ACTUAL.value,
                ForecastType.RESELLER_FEEDBACK.value,
                ForecastType.RTM_BOTTOM_UP.value,
            ]
        }
        return data_type_dict

    # 不同hr_lr type 对应的datatype列表
    data_type_dict = {
        "All": [
            ForecastType.ML_NATIONAL.value,
            ForecastType.ML_BOTTOM_UP.value,
            ForecastType.DFA.value,
            ForecastType.RTM_BOTTOM_UP.value,
            ForecastType.ACTUAL.value,
            ForecastType.RESELLER_FEEDBACK.value,
        ],
        "HR": [
            ForecastType.ML_BOTTOM_UP.value,
            ForecastType.RTM_BOTTOM_UP.value,
            ForecastType.ACTUAL.value,
            ForecastType.RESELLER_FEEDBACK.value,
        ],
        "LR": [
            ForecastType.ML_BOTTOM_UP.value,
            ForecastType.RTM_BOTTOM_UP.value,
            ForecastType.ACTUAL.value,
            ForecastType.RESELLER_FEEDBACK.value,
        ]
    }
    return data_type_dict


def get_forecast_actual_order(viewer: str):
    # feedback review sold to粒度
    if viewer == ForecastAccuracyViewerType.FEEDBACK_SOLD_TO_ACCURACY.value:
        return [
            ForecastType.ACTUAL.value,
            ForecastType.ML_BOTTOM_UP.value,
            ForecastType.RESELLER_FEEDBACK.value,
        ]

    # feedback review region 粒度
    if viewer == ForecastAccuracyViewerType.FEEDBACK_REGION_ACCURACY.value:
        return [
            ForecastType.ACTUAL.value,
            ForecastType.ML_BOTTOM_UP.value,
            ForecastType.RTM_BOTTOM_UP.value,
            ForecastType.RESELLER_FEEDBACK.value,
        ]

    return [
        ForecastType.ML_NATIONAL.value,
        ForecastType.DFA.value,
        ForecastType.NATIONAL_ACTUAL_UB.value,
        ForecastType.ML_BOTTOM_UP.value,
        ForecastType.RTM_BOTTOM_UP.value,
        ForecastType.RESELLER_FEEDBACK.value,
        ForecastType.ACTUAL.value,
    ]