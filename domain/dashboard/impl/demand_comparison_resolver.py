from decimal import Decimal

from data.mysqls.demand.demand_by_region_pool import DemandByRegionPool
from domain.dashboard.entity.demand_comparison import DemandComparisonDetail, DemandComparisonResult
from domain.demand.entity.demand import DemandCw1, DemandCw2
from util.const import ALL
from util.util import nand_color_sorted

FORECAST_VERSION_CW1 = 'CW+1'
FORECAST_VERSION_CW2 = 'CW+2'


class DemandComparisonResolver:

    def __init__(self,
                 fiscal_week: str,
                 region: str,
                 lob: str,
                 sub_lobs: list,
                 forecast_version: str,
                 hr_lr: str):
        self.fiscal_week = fiscal_week
        self.region = region
        self.lob = lob
        self.sub_lobs = sub_lobs
        self.forecast_version = forecast_version
        self.hr_lr = hr_lr

    @staticmethod
    def __any_none(iterable_data):
        return any(element is None for element in iterable_data)

    def __set_woi(self, detail: DemandComparisonDetail):
        ub_eoh = detail.ub_eoh
        shipment_plan_cw = detail.shipment_plan_cw
        forecast_cw2_dfa = detail.forecast_cw2_dfa
        forecast_cw3_dfa = detail.forecast_cw3_dfa
        forecast_cw4_dfa = detail.forecast_cw4_dfa
        forecast_cw5_dfa = detail.forecast_cw5_dfa
        cw1_di = detail.cw1_ideal_demand
        cw2_di = detail.cw2_ideal_demand
        cw1_dt = detail.dt_cw1
        cw2_dt = detail.dt_cw2
        cw1_df = detail.df_cw1
        cw2_df = detail.df_cw2
        dfa_fcst_2_4_list = [forecast_cw2_dfa, forecast_cw3_dfa, forecast_cw4_dfa]
        dfa_fcst_3_5_list = [forecast_cw3_dfa, forecast_cw4_dfa, forecast_cw5_dfa]
        ml_national_list = [detail.forecast_cw_ml, detail.forecast_cw1_ml]

        def get_cw1_woi(cw1_value):
            element = [cw1_value, ub_eoh, shipment_plan_cw ] + dfa_fcst_2_4_list + ml_national_list
            if self.__any_none(element):
                return None

            avg_dfa_fcst_2_4 = sum(dfa_fcst_2_4_list) / len(dfa_fcst_2_4_list)

            if not avg_dfa_fcst_2_4:
                return 0

            # [CW+1 DI + CW-1 UB EOH(actual) + CW Shipment Plan - Sum ML National FCST(CW, CW+1)]/Average DFA FCST(CW+2 ~CW+4)
            base = -(Decimal(ub_eoh) + Decimal(shipment_plan_cw) - Decimal(sum(ml_national_list)))
            demand_cw1 = DemandCw1(Decimal(cw1_value), Decimal(base), Decimal(avg_dfa_fcst_2_4))
            return float(demand_cw1.woi())

        def get_cw2_woi(cw1_value, cw2_value):
            element = [cw1_value, cw2_value, ub_eoh, shipment_plan_cw, forecast_cw2_dfa] + dfa_fcst_3_5_list + ml_national_list
            if self.__any_none(element):
                return None

            avg_dfa_fcst_3_5 = sum(dfa_fcst_3_5_list) / len(dfa_fcst_3_5_list)

            if not avg_dfa_fcst_3_5:
                return 0

            # [CW+2 DI + CW-1 UB EOU(actual) + CW Shipment Plan + CW+1 DI - Sum ML National FCST(CW, CW+1) - CW+2 DFA FCST]/Average DFA FCST(CW+3 ~CW+5)
            base = -(Decimal(ub_eoh) + Decimal(shipment_plan_cw) - Decimal(sum(ml_national_list)))
            demand_cw2 = DemandCw2(Decimal(cw2_value), Decimal(cw1_value), Decimal(base), Decimal(avg_dfa_fcst_3_5), Decimal(forecast_cw2_dfa))
            return float(demand_cw2.woi())

        detail.cw1_di_woi = get_cw1_woi(cw1_value=cw1_di)
        detail.cw2_di_woi = get_cw2_woi(cw1_value=cw1_di, cw2_value=cw2_di)
        detail.cw1_dt_woi = get_cw1_woi(cw1_value=cw1_dt)
        detail.cw2_dt_woi = get_cw2_woi(cw1_value=cw1_dt, cw2_value=cw2_dt)
        detail.cw1_df_woi = get_cw1_woi(cw1_value=cw1_df)
        detail.cw2_df_woi = get_cw2_woi(cw1_value=cw1_df, cw2_value=cw2_df)

    @staticmethod
    def __sum_detail(all_detail: DemandComparisonDetail, detail: DemandComparisonDetail):
        all_detail.sum(detail)

    def __sum_and_woi_detail(self, nand_flag: bool, color_flag: bool, group_details: list[DemandComparisonDetail]):
        all_detail = {
            "ub_eoh": None,
            "shipment_plan_cw": None,
            "forecast_cw_ml": None,
            "forecast_cw1_ml": None,
            "forecast_cw2_dfa": None,
            "forecast_cw3_dfa": None,
            "forecast_cw4_dfa": None,
            "forecast_cw5_dfa": None,
            "cw1_ideal_demand": None,
            "cw2_ideal_demand": None,
            "dt_cw1": None,
            "dt_cw2": None,
            "df_cw1": None,
            "df_cw2": None
        }
        # 构造整体all汇总数据
        detail_all = DemandComparisonDetail(nand=ALL, color=ALL, **all_detail)
        result_list = [detail_all] if group_details else []

        # 构造具体某一纬度下all汇总数据
        tmp_sub_all_detail_dict = {}
        dim_key = 'nand'
        nand_color_dim = {
            "color": ALL,
            "nand": ALL,
        }
        if nand_flag:
            dim_key = 'nand'
        if color_flag:
            dim_key = 'color'

        for detail in group_details:
            result_list.append(detail)
            # 构造具体某一纬度下all汇总数据
            nand_color_dim_value = getattr(detail, dim_key)
            nand_color_dim[dim_key] = nand_color_dim_value
            if nand_color_dim_value not in tmp_sub_all_detail_dict:
                sub_detail_all =  DemandComparisonDetail(**nand_color_dim, **all_detail)
                tmp_sub_all_detail_dict[nand_color_dim_value] = sub_detail_all
                result_list.append(sub_detail_all)

            # 对All求和
            self.__sum_detail(detail_all, detail)
            self.__sum_detail(tmp_sub_all_detail_dict[nand_color_dim_value], detail)

        for detail in result_list:
            # 计算woi
            self.__set_woi(detail)
        return result_list

    def __get_demand_comparison_result_list(self, data_source: list[DemandComparisonDetail]) -> list[DemandComparisonResult]:
        result = []
        for detail in data_source:
            if self.forecast_version == FORECAST_VERSION_CW1:
                ideal_demand = detail.cw1_ideal_demand
                ideal_demand_woi = detail.cw1_di_woi
                top_down_demand = detail.dt_cw1
                top_down_demand_woi = detail.cw1_dt_woi
                final_demand = detail.df_cw1
                final_demand_woi = detail.cw1_df_woi
            else:
                ideal_demand = detail.cw2_ideal_demand
                ideal_demand_woi = detail.cw2_di_woi
                top_down_demand = detail.dt_cw2
                top_down_demand_woi = detail.cw2_dt_woi
                final_demand = detail.df_cw2
                final_demand_woi = detail.cw2_df_woi

            detail_result = DemandComparisonResult(
                nand=detail.nand,
                color=detail.color,
                ideal_demand=ideal_demand,
                ideal_demand_woi=ideal_demand_woi,
                top_down_demand=top_down_demand,
                top_down_demand_woi=top_down_demand_woi,
                final_demand=final_demand,
                final_demand_woi=final_demand_woi
            )
            result.append(detail_result)

        return result

    @staticmethod
    def __init_view_schema(view_data: list, nand_flag: bool, color_flag: bool, sort_keys: list):
        # 按照各纬度(nand->color) / (color->nand)分类数据
        dim_key = 'nand'
        dim_other_key = 'color'
        if nand_flag:
            dim_key = 'nand'
            dim_other_key = 'color'
        if color_flag:
            dim_key = 'color'
            dim_other_key = 'nand'

        tmp_result = {}
        result_list = []
        # 分类
        for item in view_data:
            item["child"] = []
            dim_dict = tmp_result.setdefault(item[dim_key], {})
            dim_dict[item[dim_other_key]] = item

        # 构造结构
        for key, values_dict in tmp_result.items():
            all_item = values_dict.pop(ALL)  # 每个dim纬度下整体汇总行
            detail_item = list(values_dict.values())
            # 按照二级纬度排序, 取sort_keys的最后一个即可
            sorted_detail_item = nand_color_sorted(data=detail_item, sorted_key=[sort_keys[-1]])
            all_item['child'] = sorted_detail_item
            result_list.append(all_item)

        # 按照一级纬度排序, 取sort_keys的第一个即可
        sorted_result_list = nand_color_sorted(data=result_list, sorted_key=[sort_keys[0]])
        return sorted_result_list

    def get_demand_comparison_data(self, color: bool, nand: bool, sort_keys: list) -> tuple[list, str]:
        # 数据源
        group_details = DemandByRegionPool.get_demand_comparison_detail(fiscal_week=self.fiscal_week,
                                                                        region=self.region,
                                                                        lob=self.lob,
                                                                        sub_lobs=self.sub_lobs,
                                                                        hr_lr=self.hr_lr)
        # 汇总行以及woi计算
        detail_data = self.__sum_and_woi_detail(nand_flag=nand, color_flag=color, group_details=group_details)

        view_data = self.__get_demand_comparison_result_list(data_source=detail_data)
        view_data = [item.as_dict() for item in view_data]
        result = self.__init_view_schema(view_data=view_data, nand_flag=nand, color_flag=color, sort_keys=sort_keys)
        latest_refresh_time = DemandByRegionPool.get_update_time_by_fiscal_week(fiscal_week=self.fiscal_week)
        return result, latest_refresh_time
