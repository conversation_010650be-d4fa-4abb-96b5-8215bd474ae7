import pandas as pd

from data.databend.dashboard.mpn_mix import DashboardMpnMix
from data.mysqls.dashboard.dashboard_file_storage import MixUpload
from kit.pd import fill_nan_to_none
from util.const import ALL


class MpnMixReslover:

    def __init__(self):
        self.sub_lob = "Sub-LOB"
        self.nand = "Nand"
        self.color = "Color"
        self.mpn = "Mpn"
        self.adjust_mix = "Adjusted Mix%"

    def merge_mpn_mix_and_adjust(self, fiscal_week: str, region: str,
                                 lob: str, sub_lob: str, color: bool, nand: bool) -> list:
        mpn_mix = DashboardMpnMix.get_mpn_mix(
            fiscal_week, region, lob, sub_lob, color, nand
        )
        mpn_mix = [item.as_dict() for item in mpn_mix]
        adjust_mix = MixUpload.get_adjust_mix(fiscal_week, region, lob, [sub_lob] if sub_lob else [])
        result = self.do_merge_mpn_mix_and_adjust(mpn_mix, adjust_mix)
        return result

    def do_merge_mpn_mix_and_adjust(self, mpn_mix: list, adjust_mix) -> list:
        df_result = None
        df_adjust_mix = pd.DataFrame(adjust_mix)
        df_adjust_mix = fill_nan_to_none(df_adjust_mix)
        df_mpn_mix = pd.DataFrame(mpn_mix)
        df_mpn_mix = fill_nan_to_none(df_mpn_mix)
        # 都为空
        if df_adjust_mix.empty and df_mpn_mix.empty:
            return []
        # mpn_mix和adjust_mix都有数时merge
        if not df_adjust_mix.empty and not df_mpn_mix.empty:
            df_result = self.do_merge_by_sublob_nand_color(df_mpn_mix, df_adjust_mix)
        # adjust_mix为空给mpn_mix补齐adjusted_mix字段返回给前端
        if df_adjust_mix.empty:
            df_mpn_mix["adjusted_mix"] = ""
            self.fill_actual_mix(df_mpn_mix)
            df_result = df_mpn_mix.fillna("")
        # mpn_mix为空给adjust_mix补齐mpn_mix的字段返回给前端
        if df_mpn_mix.empty:
            cols = [
                "sub_lob",
                "mpn",
                "nand",
                "color",
                "ml_fcst_mix",
                # "sales_fcst_mix",
                "fd_fcst_mix",
                "trimmed_ub_mix",
                "trimmed_week_cnt",
                "actual_mix",
            ]
            for col in cols:
                df_adjust_mix[col] = ""
            df_result = df_adjust_mix.fillna("")
        result = df_result.to_dict(orient="records")
        return result

    #  左关联，以mpn纬度出数  提供actual_mix字段，方法
    def do_merge_by_sublob_nand_color(self, df_mpn_mix: pd.DataFrame, df_adjust_mix: pd.DataFrame) -> pd.DataFrame:
        df_adjust_mix = df_adjust_mix[
            [self.sub_lob, self.nand, self.color, self.mpn, self.adjust_mix]
        ]
        df_adjust_mix = self.groupby_adjust_mix(df_adjust_mix)
        df_adjust_mix.rename(
            columns={
                self.sub_lob: "sub_lob",
                self.nand: "nand",
                self.color: "color",
                self.mpn: 'mpn',
                self.adjust_mix: "adjusted_mix",
            },
            inplace=True,
        )
        df_result = df_mpn_mix.merge(
            df_adjust_mix, how="left", on=['sub_lob', "mpn", "nand", "color"]
        ).fillna("")
        self.fill_actual_mix(df_result)
        return df_result

    def fill_actual_mix(self, df_result):
        # 设置actual_mix
        # 检查每个 sub_lob 是否存在 adjusted_mix
        sub_lob_adjusted_mix_ = df_result.groupby('sub_lob')['adjusted_mix']

        def contains_number(series):
            return any(pd.notna(x) and isinstance(x, (int, float)) for x in series)

        adjust_mix_exists = sub_lob_adjusted_mix_.apply(contains_number).to_dict()
        # 计算 actMix 列
        df_result['actual_mix'] = df_result.apply(self.__calculate_actual_mix, axis=1,
                                                  adjust_mix_exists=adjust_mix_exists)

    def __calculate_actual_mix(self, row, adjust_mix_exists):
        if adjust_mix_exists[row['sub_lob']]:
            return row['adjusted_mix']
        else:
            if row['trimmed_ub_mix']:
                return row['trimmed_ub_mix']
            else:
                return row['ml_fcst_mix']

    def groupby_adjust_mix(self, df_adjust_mix: pd.DataFrame) -> pd.DataFrame:
        # 按照sublob聚合计算nand=All,color=All时的adjust_mix
        df_by_color_by_nand = (
            df_adjust_mix.groupby([self.sub_lob])
            .agg({self.adjust_mix: "sum"})
            .reset_index()
        )
        df_by_color_by_nand[self.color] = ALL
        df_by_color_by_nand[self.nand] = ALL
        # 按照sublob,nand聚合计算color=All时的adjust_mix
        df_by_nand = (
            df_adjust_mix.groupby([self.sub_lob, self.nand])
            .agg({self.adjust_mix: "sum"})
            .reset_index()
        )
        df_by_nand[self.color] = ALL
        # 按照sublob,color聚合计算nand=All时的adjust_mix
        df_by_color = (
            df_adjust_mix.groupby([self.sub_lob, self.color])
            .agg({self.adjust_mix: "sum"})
            .reset_index()
        )
        df_by_color[self.nand] = ALL
        # 联合两种聚合结果
        df_result = pd.concat(
            [df_adjust_mix, df_by_color_by_nand, df_by_nand, df_by_color], axis=0
        )
        return df_result
