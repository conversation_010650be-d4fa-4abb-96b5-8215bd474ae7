class Mock:
    menu = {
        "threshold": 0.2,
        "fiscal_weeks": ["FY24Q2W11", "FY24Q2W10", "FY24Q2W9", "FY24Q2W8"],
        "regions": [
            {
                "region": "Hong Kong",
                "rtms": [
                    {"rtm": "All", "sub_rtms": ["All"]},
                    {"rtm": "Carrier", "sub_rtms": ["All", "APR"]},
                ],
            },
            {
                "region": "china mainland",
                "rtms": [
                    {"rtm": "All", "sub_rtms": ["All"]},
                    {"rtm": "Carrier", "sub_rtms": ["All", "APR"]},
                    {
                        "rtm": "Retail Partner",
                        "sub_rtms": None,
                    },
                ],
            },
            {
                "region": "hong kong",
                "rtms": [
                    {"rtm": "All", "sub_rtms": ["All"]},
                    {"rtm": "Carrier", "sub_rtms": ["All", "APR"]},
                ],
            },
        ],
        "retail_partners": [
            {"rtm": "All", "sub_rtms": ["All"]},
            {"rtm": "Mono", "sub_rtms": ["APR", "PPR"]},
            {"rtm": "Multi", "sub_rtms": ["APR", "PPR"]},
        ],
        "lobs": [
            {
                "lob": "iPhone",
                "sub_lobs": ["iPhone 15", "iPhone 15 Plus", "iPhone 15 Pro"],
            }
        ],
    }
    ub_velocity_menu = {
        "threshold": 0.2,
        "fiscal_weeks": ["FY24Q2W11", "FY24Q2W10", "FY24Q2W9", "FY24Q2W8"],
        "regions": ["China Mainland"],
        "rtms": [
            {"rtm": "All", "sub_rtms": ["All"]},
            {"rtm": "Carrier", "sub_rtms": ["All", "Carrier"]},
            {"rtm": "Retail Partner", "rtm_second_level": {"All": []}},
        ],
        "lobs": [
            {
                "lob": "iPhone",
                "sub_lobs": ["iPhone 15", "iPhone 15 Plus", "iPhone 15 Pro"],
            }
        ],
    }

    # Carrier和Retail Partner

    current_week = {
        "current_week": "FY24Q2W11",
    }

    view = {
        "columns": [
            "Sub-LOB",
            "Nand",
            "Color",
            "Q1W1",
            "Q1W2",
            "Q1W3",
            "Q1W4",
            "Q1W5",
            "Q1W6",
        ],
        "sub_lobs": [
            {
                "sub_lob": "iPhone 15 Plus",
                "nand": "All",
                "color": "All",
                "Q1W1": 0.15,
                "Q1W2": 0.15,
                "Q1W3": 0.15,
                "Q1W4": 0.15,
                "Q1W5": 0.15,
                "Q1W6": 0.15,
            },
            {
                "sub_lob": "iPhone 15 Plus",
                "nand": "128G",
                "color": "Black",
                "Q1W1": 0.15,
                "Q1W2": 0.15,
                "Q1W3": 0.15,
                "Q1W4": 0.15,
                "Q1W5": 0.15,
                "Q1W6": 0.15,
            },
        ],
    }

    mpn_mix_menu = {
        "fiscal_weeks": ["FY24Q2W11", "FY24Q2W10", "FY24Q2W9", "FY24Q2W8"],
        "regions": ["China Mainland"],
        "lobs": [
            {
                "lob": "iPhone",
                "sub_lobs": ["iPhone 15", "iPhone 15 Plus", "iPhone 15 Pro"],
            }
        ],
    }

    mpn_mix_view = {
        "sub_lobs": [
            {
                "sub_lob": "iPhone 15 Plus",
                "nand": "All",
                "color": "All",
                "ml_fcst_mix": 0.15,
                "trimmed_ub_mix": 0.15,
                "adjusted_mix": 0.15,
            },
            {
                "sub_lob": "iPhone 15 Plus",
                "nand": "128G",
                "color": "Black",
                "ml_fcst_mix": 0.15,
                "trimmed_ub_mix": 0.15,
                "adjusted_mix": 0.15,
            },
        ]
    }

    adjusted_mix_file_record = {
        "file_name": "x.xlsx",
        "file_path": "/tmp/x.xlsx",
        "id": 410,
        "template_file_name": "Multi_Demand_iPad_FY24Q3W2.xlsx",
        "upload_time": "2023-07-15 15:31:35",
        "upload_by": "Zhang san",
    }

    mpn_mix_sublobs = {"sublobs": ["iPhone 15", "iPhone 14", "iPhone 13"]}
