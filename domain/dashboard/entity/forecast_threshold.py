
from data.mysqls.dashboard.forecast_accuracy_threshold import ForecastAccuracyThreshold, get_obj_attr_by_str


class ForecastThreshold():
    def __init__(self, condition: dict) -> None:
        self.thresholds = ForecastAccuracyThreshold.get_threshold_by_codition(
            condition)

    def get_each_threshold(cls, condition: dict):
        threshold = 0
        for item in cls.thresholds:
            if all(get_obj_attr_by_str(item, key) == value for key, value in condition.items()):
                threshold = item.threshold
                break
        return threshold

    def get_one_or_none_threshold(self) -> int:
        threshold = 0
        if self.thresholds:
            threshold = self.thresholds[0].threshold
        return threshold
