from domain.dashboard.entity.anti_fraud.anti_fraud_so_ub import RTM_Mono, RTM_MULTI, RTM_CARRIER
from domain.dashboard.entity.anti_fraud.ub_wo_ds_wkly import RTM_EDU
from util.const import ALL


class Nand:
    def __init__(self, rtm, sub_rtm, hq_id, display_name,level):
        self.rtm = rtm
        self.sub_rtm = sub_rtm
        self.hq_id = hq_id

        self.display_name = display_name
        self.level = level


LEVEL_ONE = "one"
LEVEL_TWO = "two"
LEVEL_THREE = "three"


nands = [
    Nand(RTM_Mono, ALL, ALL, RTM_Mono, LEVEL_ONE),

    Nan<PERSON>(RTM_Mono, "Lifestyle", ALL, "Lifestyle", LEVEL_TWO),
    Nan<PERSON>(RTM_Mono, "Lifestyle", "649296", "Ingram", LEVEL_THREE),
    <PERSON><PERSON>(RTM_Mono, "Lifestyle", "4227887", "<PERSON><PERSON><PERSON>", LEVEL_THREE),
    <PERSON><PERSON>(RTM_Mono, "Lifestyle", "3449268", "Ju<PERSON>", LEVEL_THREE),
    <PERSON><PERSON>(RTM_Mono, "Lifestyle", "544151", "Zhongheng", LEVEL_THREE),

    Nand(RTM_Mono, "Mono", ALL, "Mono", LEVEL_TWO),
    Nand(RTM_Mono, "Mono", "3434506", "Ao Yi Tong", LEVEL_THREE),
    Nand(RTM_Mono, "Mono", "4151032", "BJ-CHJH", LEVEL_THREE),
    Nand(RTM_Mono, "Mono", "3415577", "Beichen Zhihui", LEVEL_THREE),
    Nand(RTM_Mono, "Mono", "3489164", "Chengdu Mingxun", LEVEL_THREE),
    Nand(RTM_Mono, "Mono", "3483969", "Guangxi Baidong", LEVEL_THREE),
    Nand(RTM_Mono, "Mono", "3382045", "Haijia Keji", LEVEL_THREE),
    Nand(RTM_Mono, "Mono", "1294441", "Hebei Jincao", LEVEL_THREE),
    Nand(RTM_Mono, "Mono", "3356989", "Henan Xuanming", LEVEL_THREE),
    Nand(RTM_Mono, "Mono", "1798237", "Hengsha", LEVEL_THREE),
    Nand(RTM_Mono, "Mono", "893362", "Ku’ai", LEVEL_THREE),
    Nand(RTM_Mono, "Mono", "3435975", "Maifeng Wangluo", LEVEL_THREE),
    Nand(RTM_Mono, "Mono", "3405858", "Nanjing Longbao", LEVEL_THREE),
    Nand(RTM_Mono, "Mono", "3501451", "Pengtian Shuma", LEVEL_THREE),
    Nand(RTM_Mono, "Mono", "893413", "Tianyin Xinxi", LEVEL_THREE),
    Nand(RTM_Mono, "Mono", "3426057", "Wuxi Zongchi", LEVEL_THREE),
    Nand(RTM_Mono, "Mono", "3389757", "Xin Gao Wei", LEVEL_THREE),
    Nand(RTM_Mono, "Mono", "4252675", "Xin Jia Yue", LEVEL_THREE),
    Nand(RTM_Mono, "Mono", "3412629", "Xinyan Haijia", LEVEL_THREE),
    Nand(RTM_Mono, "Mono", "3495268", "Xizang Xinyu", LEVEL_THREE),
    Nand(RTM_Mono, "Mono", "3355652", "Zhejiang Qiyi", LEVEL_THREE),
    Nand(RTM_Mono, "Mono", "4291673", "Zhongke Xingmai", LEVEL_THREE),

    Nand(RTM_MULTI, ALL, ALL, RTM_MULTI, LEVEL_ONE),

    Nand(RTM_MULTI, "OTC", ALL, "OTC", LEVEL_TWO),
    Nand(RTM_MULTI, "OTC", "290541", "ASD", LEVEL_THREE),
    Nand(RTM_MULTI, "OTC", "523144", "Telling", LEVEL_THREE),
    Nand(RTM_MULTI, "OTC", "1798225", "Yueyang", LEVEL_THREE),

    Nand(RTM_MULTI, "Township", ALL, "Township", LEVEL_TWO),
    Nand(RTM_MULTI, "Township", "3836640", "Hengze", LEVEL_THREE),
    Nand(RTM_MULTI, "Township", "3484362", "Jujia Yuntong", LEVEL_THREE),

    Nand(RTM_MULTI, "MM", ALL, "MM", LEVEL_TWO),
    Nand(RTM_MULTI, "MM", "268563", "Ingram", LEVEL_THREE),
    Nand(RTM_MULTI, "MM", "2933425", "Juran Zhijia", LEVEL_THREE),
    Nand(RTM_MULTI, "MM", "3870416", "Shike Peidai", LEVEL_THREE),
    Nand(RTM_MULTI, "MM", "1650374", "Zarva", LEVEL_THREE),

    # 需求迭代: Carrier版本和Overall版本，需在ND View中拿掉Carrier的部分
    # Nand(RTM_CARRIER, ALL, ALL, RTM_CARRIER, LEVEL_ONE),
    # Nand(RTM_CARRIER, "CM", "899468", "CM", LEVEL_TWO),
    # Nand(RTM_CARRIER, "CU", "385799", "CU", LEVEL_TWO),
    # Nand(RTM_CARRIER, "CT", "629165", "CT", LEVEL_TWO),


    Nand(RTM_EDU, ALL, ALL, RTM_EDU, LEVEL_ONE),
    Nand(RTM_EDU, RTM_EDU, "2407486", "Chuangsen", LEVEL_THREE),
    Nand(RTM_EDU, RTM_EDU, "3305888", "Wanfang", LEVEL_THREE)
]

def get_nand_by_hq_id(hq_id)->Nand:
    for nand in nands:
        if nand.hq_id == hq_id:
            return nand