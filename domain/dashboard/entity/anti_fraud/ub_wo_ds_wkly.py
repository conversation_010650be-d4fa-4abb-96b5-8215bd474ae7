RTM_MULTI = "Multi Brand"
RTM_Mono = "Mono Brand"
RTM_CARRIER = "Carrier"
RTM_ONLINE = "ONLINE"
RTM_EDU = "Education"


# eoh aging mail 不展示sub_rtm层级的rtm列表
NO_DISPLAY_SUB_RTM = [RTM_EDU]


rtms = [RTM_Mono, RTM_MULTI, RTM_CARRIER]

rtm_dict = {
    "Multi": RTM_MULTI,
    "Mono": RTM_Mono,
    "Carrier": RTM_CARRIER,
    "Education": RTM_EDU,
}

SUB_LOB_OTHERS = "Others"
SUB_LOB_15_ALL = "15 All"
SUB_LOB_16_PRO = "16 Pro"
SUB_LOB_16_Cons = "16 Cons."
SUB_LOB_16e = "16e"



sub_lob_dict = {
    "Others": SUB_LOB_OTHERS,
    "iPhone 15 Series": SUB_LOB_15_ALL,
    "iPhone 16 Consumer Tier": SUB_LOB_16_Cons,
    "iPhone 16 Pro Tier": SUB_LOB_16_PRO,
    "iPhone 16e": SUB_LOB_16e,
}

SUB_LOB_RULES = ["All", "16 Pro", "16 Cons.", "16e", "15 All", "Others"]
SUB_LOB_RULES_NORMAL = ["All","iPhone 16 Pro Tier","iPhone 16 Consumer Tier","iPhone 15 Series", "Others"]
RTMS_RULES = ["All", "Mono Brand", "Multi Brand", "Carrier", "Education"]

RTMS_SUB_RTM_MAPPING = {
    "All": ["All"],
    "Mono Brand": ["All", "Lifestyle", "Mono"],
    "Multi Brand": ["All", "OTC", "Township", "MM"],
    "Carrier": ["All", "CM", "CU", "CT"],
    "Education": ["All"],
}


def format_percentage(numerator, denominator):
    """
        * 百分比前保留1位小数，如7.7%
        * 如果数据本身为0，则0%，不用保留1位小数
    """
    if denominator is None or denominator == 0:
        return "-"
    if numerator == 0:
        return "0%"
    percent = (numerator / denominator) * 100
    return f"{round(percent)}%"


class UbWoDsWklyItem:
    def __init__(self,
                 query_date: str = None,
                 week_date: str = None,
                 total_ub_accum: int = None,
                 ub_without_ds_accum: int = None,
                 total_ub_weekly: int = None,
                 ub_without_ds_weekly: int = None,
                 rtm: str = None,
                 sub_rtm: str = None,
                 sub_lob: str = None,
                 total_ub_accum_lq: int = None,
                 ub_without_ds_accum_lq: int = None,
                 fiscal_qtr_year_name: str = None,
                 fiscal_qtr_year_name_lq: str = None,
                 ) -> None:
        self.query_date = query_date
        self.rtm = rtm
        self.sub_rtm = sub_rtm
        self.sub_lob = sub_lob
        self.week_date = week_date
        self.total_ub_accum = total_ub_accum
        self.ub_without_ds_accum = ub_without_ds_accum
        self.total_ub_weekly = total_ub_weekly
        self.ub_without_ds_weekly = ub_without_ds_weekly

        self.total_ub_accum_lq = total_ub_accum_lq
        self.ub_without_ds_accum_lq = ub_without_ds_accum_lq
        self.fiscal_qtr_year_name = fiscal_qtr_year_name

        self.fiscal_qtr_year_name_lq = self.get_previous_quarter(self.fiscal_qtr_year_name)

    @staticmethod
    def get_previous_quarter(current_quarter: str) -> str:
        """
        计算给定季度的上一个季度表示形式。

        Args:
            current_quarter (str): 当前季度的表示形式，例如 "FY24Q4"。

        Returns:
            str: 上个季度的表示形式，例如 "FY24Q3"。
        """

        if not current_quarter:
            return current_quarter

        # 提取年度和季度
        fiscal_year = int(current_quarter[2:4])
        quarter = int(current_quarter[-1])

        # 计算上个季度
        if quarter == 1:
            previous_fiscal_year = fiscal_year - 1
            previous_quarter = 4
        else:
            previous_fiscal_year = fiscal_year
            previous_quarter = quarter - 1

        # 生成上个季度的表示形式
        return f"FY{previous_fiscal_year:02d}Q{previous_quarter}"

    def otd_percentage(self) -> str:
        return format_percentage(self.ub_without_ds_accum, self.total_ub_accum)

    def otd_percentage_lq(self) -> str:
        return format_percentage(self.ub_without_ds_accum_lq, self.total_ub_accum_lq)

    def percentage(self) -> str:
        return format_percentage(self.ub_without_ds_weekly, self.total_ub_weekly)

    def set_total_ub_accum(self, total_ub_accum):
        self.total_ub_accum = total_ub_accum

    def set_ub_without_ds_accum(self, ub_without_ds_accum):
        self.ub_without_ds_accum = ub_without_ds_accum

    def set_total_ub_accum_lq(self, total_ub_accum_lq):
        self.total_ub_accum_lq = total_ub_accum_lq

    def set_ub_without_ds_accum_lq(self, ub_without_ds_accum_lq):
        self.ub_without_ds_accum_lq = ub_without_ds_accum_lq

    def set_total_ub_weekly(self, total_ub_weekly):
        self.total_ub_weekly = total_ub_weekly

    def set_ub_without_ds_weekly(self, ub_without_ds_weekly):
        self.ub_without_ds_weekly = ub_without_ds_weekly

    def set_query_date(self, query_date):
        self.query_date = query_date

    def set_week_date(self, week_date):
        self.week_date = week_date

    def set_fiscal_qtr_year_name(self, fiscal_qtr_year_name):
        self.fiscal_qtr_year_name = fiscal_qtr_year_name

    def set_fiscal_qtr_year_name_lq(self, fiscal_qtr_year_name_lq):
        self.fiscal_qtr_year_name_lq = fiscal_qtr_year_name_lq

    def as_dict(self) -> dict:
        return {
            "query_date": self.query_date,
            "rtm": self.rtm,
            "sub_rtm": self.sub_rtm,
            "sub_lob": self.sub_lob,
            "week_date": self.week_date,
            "ub_without_ds_weekly": "{:,.0f}".format(self.ub_without_ds_weekly),
            "ub_without_ds_accum": "{:,.0f}".format(self.ub_without_ds_accum),
            "otd_percentage": self.otd_percentage(),
            "percentage": self.percentage(),
            "ub_without_ds_accum_lq": "{:,.0f}".format(self.ub_without_ds_accum_lq) if self.ub_without_ds_accum_lq else '0',
            "otd_percentage_lq": self.otd_percentage_lq(),
            "fiscal_qtr_year_name_lq": self.fiscal_qtr_year_name_lq,
            "fiscal_qtr_year_name": self.fiscal_qtr_year_name,
        }

    def __repr__(self):
        return (
            f"UbWoDsWklyItem(query_date={self.query_date}, rtm={self.rtm}, sub_rtm={self.sub_rtm}, sub_lob={self.sub_lob}, week_date={self.week_date}, "
            f"ub_without_ds_weekly={self.ub_without_ds_weekly}, ub_without_ds_accum={self.ub_without_ds_accum}, "
            f"otd_percentage={self.otd_percentage}, percentage={self.percentage})")
