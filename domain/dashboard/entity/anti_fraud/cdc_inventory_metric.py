from domain.dashboard.entity.anti_fraud.common import format_percentage
from kit.compare_data import compare_percentage_strings
from util.const import ALL


class CDCInventoryMetric:
    def __init__(self, rtm: str, sub_rtm: str, sub_lob: str) -> None:
        self.rtm = rtm
        self.sub_rtm = sub_rtm
        self.sub_lob = sub_lob

        self.landing_qtd = None
        self.landing_eoh = None
        self.age_gre35 = None
        self.age_leq35 = None
        self.age_leq1weeks = None
        self.age_1to2weeks = None
        self.age_2to3weeks = None
        self.age_3to4weeks = None
        self.age_4to5weeks = None
        self.age_5to6weeks = None
        self.age_6to7weeks = None
        self.age_7to8weeks = None
        self.age_8to9weeks = None
        self.age_9to10weeks = None
        self.age_gre70 = None

    def set_data(self, landing_qtd, landing_eoh, age_gre35, age_leq35,
                 age_leq1weeks, age_1to2weeks, age_2to3weeks, age_3to4weeks,
                 age_4to5weeks, age_5to6weeks, age_6to7weeks, age_7to8weeks,
                 age_8to9weeks, age_9to10weeks, age_gre70):
        self.landing_qtd = landing_qtd
        self.landing_eoh = landing_eoh
        self.age_gre35 = age_gre35
        self.age_leq35 = age_leq35
        self.age_leq1weeks = age_leq1weeks
        self.age_1to2weeks = age_1to2weeks
        self.age_2to3weeks = age_2to3weeks
        self.age_3to4weeks = age_3to4weeks
        self.age_4to5weeks = age_4to5weeks
        self.age_5to6weeks = age_5to6weeks
        self.age_6to7weeks = age_6to7weeks
        self.age_7to8weeks = age_7to8weeks
        self.age_8to9weeks = age_8to9weeks
        self.age_9to10weeks = age_9to10weeks
        self.age_gre70 = age_gre70

    def __str__(self) -> str:
        return (f"rtm: {self.rtm}, sub_rtm: {self.sub_rtm}, sub_lob: {self.sub_lob},"
                f" landing_qtd: {self.landing_qtd}, landing_eoh: {self.landing_eoh}, age_gre35: {self.age_gre35}, age_leq35: {self.age_leq35}, "
                f"age_leq1weeks: {self.age_leq1weeks}, age_1to2weeks: {self.age_1to2weeks}, age_2to3weeks: {self.age_2to3weeks}, age_3to4weeks: {self.age_3to4weeks}, age_4to5weeks: {self.age_4to5weeks}")

    def level(self, rtm_version="All"):
        if rtm_version != "All":
            if self.sub_rtm == "All" and self.sub_lob == "All":
                return "one"
            if self.sub_lob == "All":
                return "two"
            return "three"

        # China Channel
        if self.rtm == 'All':
            if self.sub_lob == ALL:
                return "one"
            return "three"
        # RTM
        if self.sub_lob == "All" and self.sub_rtm == "All":
            return "one"
        if self.sub_lob != "All":
            return "three"
        # if self.sub_rtm == 'All':
        #     return "two"
        if self.sub_lob == "All":
            return "two"

    def sublob_level(self):
        # China Channel
        if self.rtm == 'All':
            if self.sub_lob == ALL:
                return "one"
            return "three"
        # RTM
        if self.sub_rtm == "All" and self.sub_lob == 'All':
            # mono、multi、carrier, edu
            return "one"

        if self.sub_lob != 'All':
            return "three"
        return "two"    # 每个subrtm

    def first_column(self):
        if self.rtm == 'All' and self.sub_lob == ALL:
            return 'China Channel'
        if self.sub_rtm == 'All' and self.sub_lob == ALL:
            return self.rtm
        if self.sub_lob == 'All' and self.sub_lob == ALL:
            return self.sub_rtm
        return self.sub_lob

    def landing_eoh_percent(self):
        return format_percentage(self.landing_eoh, self.landing_qtd)

    def default_percent(self, aging):
        return format_percentage(aging, self.landing_eoh)
    
    def convert_to_thousands(self, value):
        if value:
            return "{:,.0f}".format(value)
        else:
            return 0
    
    def age_leq35_threshold_class_name(self, aging):
        return "highlightRed" if compare_percentage_strings(self.default_percent(aging), '25%') > 0 else ""
    
    def age_gre35_threshold_class_name(self, aging):
        return "highlightRed" if compare_percentage_strings(self.default_percent(aging), '25%') > 0 else ""

    def as_dict(self) -> dict:
        return {
            "level": self.level(),
            "first_column": self.first_column(),
            "landing_qtd": "{:,.0f}".format(self.landing_qtd),
            "landing_eoh": "{:,.0f}".format(self.landing_eoh),
            "age_gre35": "{:,.0f}".format(self.age_gre35),
            "age_gre35_threshold_class_name": self.age_gre35_threshold_class_name(),
            "age_leq35": "{:,.0f}".format(self.age_leq35),
            "age_leq35_threshold_class_name": self.age_leq35_threshold_class_name(),
            "age_leq1weeks": "{:,.0f}".format(self.age_leq1weeks),
            "age_1to2weeks": "{:,.0f}".format(self.age_1to2weeks),
            "age_2to3weeks": "{:,.0f}".format(self.age_2to3weeks),
            "age_3to4weeks": "{:,.0f}".format(self.age_3to4weeks),
            "age_4to5weeks": "{:,.0f}".format(self.age_4to5weeks),
            "age_5to6weeks": "{:,.0f}".format(self.age_5to6weeks),
            "age_6to7weeks": "{:,.0f}".format(self.age_6to7weeks),
            "age_7to8weeks": "{:,.0f}".format(self.age_7to8weeks),
            "age_8to9weeks": "{:,.0f}".format(self.age_8to9weeks),
            "age_9to10weeks": "{:,.0f}".format(self.age_9to10weeks),
            "age_gre70": "{:,.0f}".format(self.age_gre70),
        }
