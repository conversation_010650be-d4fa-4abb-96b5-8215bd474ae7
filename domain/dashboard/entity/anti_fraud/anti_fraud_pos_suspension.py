from decimal import Decimal, ROUND_HALF_UP

from domain.dashboard.entity.anti_fraud.pos_suspension_nand import get_nand_by_disti_name


def format_percentage(rate):
    """
        * 百分比前保留1位小数，如7.7% - 不保留小数了
        * 如果数据本身为0，则0%，不用保留1位小数
    """
    if rate is None:
        return "-"
    if rate == 0:
        return "0%"
    percent = Decimal(rate) * 100
    return f"{percent.quantize(Decimal('1'), rounding=ROUND_HALF_UP)}%"


# daily表用来计算qtd的数据
class PosSuspensionItem:
    def __init__(self,
                 rtm: str,
                 sub_rtm: str,
                 display_name: str,
                 level: str,
                 disti_name: str,
                 ub7_rate: float = None,
                 no_ub_pos: int = None,
                 bottom_pos: int = None,
                 no_ub_pos_lw: int = None,
                 bottom_pos_lw: int = None,
                 suspend_1_time_pos: int = None,
                 suspend_2_time_pos: int = None,
                 suspend_3_time_pos: int = None,
                 suspend_1_time_pos_lw: int = None,
                 suspend_2_time_pos_lw: int = None,
                 suspend_3_time_pos_lw: int = None
                 ) -> None:
        self.rtm = rtm
        self.sub_rtm = sub_rtm
        self.disti_name = disti_name
        self.nand = get_nand_by_disti_name(disti_name)
        self.ub7_rate = ub7_rate
        self.no_ub_pos = no_ub_pos
        self.bottom_pos = bottom_pos
        self.no_ub_pos_lw = no_ub_pos_lw
        self.bottom_pos_lw = bottom_pos_lw
        self.suspend_1_time_pos = suspend_1_time_pos
        self.suspend_2_time_pos = suspend_2_time_pos
        self.suspend_3_time_pos = suspend_3_time_pos
        self.suspend_1_time_pos_lw = suspend_1_time_pos_lw
        self.suspend_2_time_pos_lw = suspend_2_time_pos_lw
        self.suspend_3_time_pos_lw = suspend_3_time_pos_lw

        self.display_name = display_name
        self.__level = level

    def set_data(self,
                 ub7_rate,
                 no_ub_pos,
                 bottom_pos,
                 no_ub_pos_lw,
                 bottom_pos_lw,
                 suspend_1_time_pos,
                 suspend_2_time_pos,
                 suspend_3_time_pos,
                 suspend_1_time_pos_lw,
                 suspend_2_time_pos_lw,
                 suspend_3_time_pos_lw
                 ):
        self.ub7_rate = ub7_rate if ub7_rate else 0
        self.no_ub_pos = no_ub_pos if no_ub_pos else 0
        self.bottom_pos = bottom_pos if bottom_pos else 0
        self.no_ub_pos_lw = no_ub_pos_lw if no_ub_pos_lw else 0
        self.bottom_pos_lw = bottom_pos_lw if bottom_pos_lw else 0
        self.suspend_1_time_pos = suspend_1_time_pos if suspend_1_time_pos else 0
        self.suspend_2_time_pos = suspend_2_time_pos if suspend_2_time_pos else 0
        self.suspend_3_time_pos = suspend_3_time_pos if suspend_3_time_pos else 0
        self.suspend_1_time_pos_lw = suspend_1_time_pos_lw if suspend_1_time_pos_lw else 0
        self.suspend_2_time_pos_lw = suspend_2_time_pos_lw if suspend_2_time_pos_lw else 0
        self.suspend_3_time_pos_lw = suspend_3_time_pos_lw if suspend_3_time_pos_lw else 0

    def level_by_nand(self, rtm_version="All"):
        # todo sub_rtm=All 特殊处理的是overall模块，未来可以增加表示view的字段
        if rtm_version != "All" and self.sub_rtm == "All":
            return 'one'
        return self.__level

    def convert_to_thousands(self, value):
        if value is None:
            return "-"
        if value:
            return "{:,.0f}".format(value)
        else:
            return "0"

    def first_column(self):
        return self.display_name

    def get_total_pos(self):
        if self.no_ub_pos is not None and self.bottom_pos is not None:
            return self.no_ub_pos + self.bottom_pos
        return None

    def get_total_pos_lw(self):
        if self.no_ub_pos_lw is not None and self.bottom_pos_lw is not None:
            return self.no_ub_pos_lw + self.bottom_pos_lw
        return None

    def get_wow(self, current_pos, last_week_pos):
        if current_pos is not None and last_week_pos is not None:
            if last_week_pos:
                return (current_pos - last_week_pos) / last_week_pos
            else:
                return None
        return None

    def get_total_pos_wow(self):
        return self.get_wow(current_pos=self.get_total_pos(), last_week_pos=self.get_total_pos_lw())

    def get_no_ub_pos_wow(self):
        return self.get_wow(current_pos=self.no_ub_pos, last_week_pos=self.no_ub_pos_lw)

    def get_bottom_pos_wow(self):
        return self.get_wow(current_pos=self.bottom_pos, last_week_pos=self.bottom_pos_lw)

    def get_suspend_1_time_wow(self):
        return self.get_wow(current_pos=self.suspend_1_time_pos, last_week_pos=self.suspend_1_time_pos_lw)

    def get_suspend_2_time_wow(self):
        return self.get_wow(current_pos=self.suspend_2_time_pos, last_week_pos=self.suspend_2_time_pos_lw)

    def get_suspend_3_time_wow(self):
        return self.get_wow(current_pos=self.suspend_3_time_pos, last_week_pos=self.suspend_3_time_pos_lw)

    def default_percent(self, rate):
        return format_percentage(rate)

    def get_pos_wow_symbol(self, wow):
        """用于前端展示wow的正负号"""
        if wow == '-':
            return ""

        # 去掉百分号并转换为float类型
        num1 = float(wow.rstrip('%'))
        # 比较并返回结果
        if num1 <= 0:
            return ""  # 负号本来就有负号
        else:
            return "+"  # 正号

    def get_pos_wow_class_name(self, wow):
        if wow == '-':
            return ""  # 等于0 正常色

        # 去掉百分号并转换为float类型
        num1 = float(wow.rstrip('%'))

        # 比较并返回结果
        if num1 < 0:
            return "highlightGreen"  # 小于0 绿色
        elif num1 == 0:
            return ""  # 等于0 正常色
        else:
            return "highlightRed"  # 大于0 红色

    def is_display_item(self):
        """
            判断是否是不显示的项
            0. 后prd迭代又增加了1 times、2 times、3 times的停店次数, 所以把这些指标也给加进去
            1. 在ND那个层级，如果本周六个数量都为0 那么在这周不显示
            2. 在ND那个层级，如果本周六个数量都为横杠 那么在这周不显示
            3. 在ND那个层级，特殊情况(上周和本周都是0，但是分母为0无法计算wow)，分子为0，如果本周三个原因数量都为0, 并且wow都是横杠的 那么在这周不显示
        """
        total_pos_wow = self.default_percent(self.get_total_pos_wow())
        no_ub_pos_wow = self.default_percent(self.get_no_ub_pos_wow())
        bottom_pos_wow = self.default_percent(self.get_bottom_pos_wow())

        suspend_1_time_wow = self.default_percent(self.get_suspend_1_time_wow())
        suspend_2_time_wow = self.default_percent(self.get_suspend_2_time_wow())
        suspend_3_time_wow = self.default_percent(self.get_suspend_3_time_wow())

        if (
                self.convert_to_thousands(self.get_total_pos()) != '-'
                or self.convert_to_thousands(self.no_ub_pos) != '-'
                or self.convert_to_thousands(self.bottom_pos) != '-'
                or self.convert_to_thousands(self.suspend_1_time_pos) != '-'
                or self.convert_to_thousands(self.suspend_2_time_pos) != '-'
                or self.convert_to_thousands(self.suspend_3_time_pos) != '-'
                or total_pos_wow != '-'
                or no_ub_pos_wow != '-'
                or bottom_pos_wow != '-'
                or suspend_1_time_wow != '-'
                or suspend_2_time_wow != '-'
                or suspend_3_time_wow != '-'

        ) and (
                self.convert_to_thousands(self.get_total_pos()) != '0'
                or self.convert_to_thousands(self.no_ub_pos) != '0'
                or self.convert_to_thousands(self.bottom_pos) != '0'
                or self.convert_to_thousands(self.suspend_1_time_pos) != '0'
                or self.convert_to_thousands(self.suspend_2_time_pos) != '0'
                or self.convert_to_thousands(self.suspend_3_time_pos) != '0'
                or total_pos_wow != '0%'
                or no_ub_pos_wow != '0%'
                or bottom_pos_wow != '0%'
                or suspend_1_time_wow != '0%'
                or suspend_2_time_wow != '0%'
                or suspend_3_time_wow != '0%'
        ) and (
                self.convert_to_thousands(self.get_total_pos()) != '0'
                or self.convert_to_thousands(self.no_ub_pos) != '0'
                or self.convert_to_thousands(self.bottom_pos) != '0'
                or self.convert_to_thousands(self.suspend_1_time_pos) != '0'
                or self.convert_to_thousands(self.suspend_2_time_pos) != '0'
                or self.convert_to_thousands(self.suspend_3_time_pos) != '0'
                or total_pos_wow != '-'
                or no_ub_pos_wow != '-'
                or bottom_pos_wow != '-'
                or suspend_1_time_wow != '-'
                or suspend_2_time_wow != '-'
                or suspend_3_time_wow != '-'
        ):
            return True
        return False

    def as_dict(self) -> dict:
        return {
            "level": self.level_by_nand(),
            "first_column": self.first_column(),
            "rtm": self.rtm,
            "sub_rtm": self.sub_rtm,
            "disti_name": self.disti_name,
            "ub7_rate": self.ub7_rate,
            "no_ub_pos": self.no_ub_pos,
            "bottom_pos": self.bottom_pos,
            "no_ub_pos_lw": self.no_ub_pos_lw,
            "bottom_pos_lw": self.bottom_pos_lw,
            "total_pos": self.get_total_pos(),
        }

    def __repr__(self):
        return (
            f"PosSuspensionItem(rtm={self.rtm}, sub_rtm={self.sub_rtm}, total_pos={self.get_total_pos()}"
            f"disti_name={self.disti_name}, ub7_rate={self.ub7_rate}, no_ub_pos={self.no_ub_pos}, "
            f"bottom_pos={self.bottom_pos}, no_ub_pos_lw={self.no_ub_pos_lw}, bottom_pos_lw={self.bottom_pos_lw})")
