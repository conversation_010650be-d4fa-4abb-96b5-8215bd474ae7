from domain.dashboard.entity.anti_fraud.common import format_percentage
from domain.dashboard.entity.anti_fraud.nand import get_nand_by_hq_id
from kit.compare_data import compare_percentage_strings
from util.const import ALL


class EOHAgingByNand:
    def __init__(self, rtm: str, sub_rtm: str, hq_id: str,display_name:str,level:str,
                 landing_qtd=None,landing_eoh=None,age_gre35=None,age_leq35=None,
                 age_leq1weeks=None,age_1to2weeks=None,age_2to3weeks=None,age_3to4weeks=None,age_4to5weeks=None,
                 age_5to6weeks=None,age_6to7weeks=None,age_7to8weeks=None,age_8to9weeks=None,age_9to10weeks=None,
                 age_gre70=None
                 ) -> None:
        self.rtm = rtm
        self.sub_rtm = sub_rtm
        self.hq_id = hq_id

        self.nand = get_nand_by_hq_id(hq_id)

        self.landing_qtd = landing_qtd
        self.landing_eoh = landing_eoh
        self.age_gre35 = age_gre35
        self.age_leq35 = age_leq35
        self.age_leq1weeks = age_leq1weeks
        self.age_1to2weeks = age_1to2weeks
        self.age_2to3weeks = age_2to3weeks
        self.age_3to4weeks = age_3to4weeks
        self.age_4to5weeks = age_4to5weeks
        self.age_5to6weeks = age_5to6weeks
        self.age_6to7weeks = age_6to7weeks
        self.age_7to8weeks = age_7to8weeks
        self.age_8to9weeks  = age_8to9weeks
        self.age_9to10weeks = age_9to10weeks
        self.age_gre70  = age_gre70

        self.display_name = display_name
        self.level = level

    def first_column(self):
        return self.display_name

    def set_data(self, landing_qtd, landing_eoh, age_gre35, age_leq35,
                 age_leq1weeks, age_1to2weeks, age_2to3weeks, age_3to4weeks, age_4to5weeks,
                 age_5to6weeks, age_6to7weeks, age_7to8weeks, age_8to9weeks, age_9to10weeks,
                 age_gre70):
        self.landing_qtd = landing_qtd
        self.landing_eoh = landing_eoh
        self.age_gre35 = age_gre35
        self.age_leq35 = age_leq35
        self.age_leq1weeks = age_leq1weeks
        self.age_1to2weeks = age_1to2weeks
        self.age_2to3weeks = age_2to3weeks
        self.age_3to4weeks = age_3to4weeks
        self.age_4to5weeks = age_4to5weeks
        self.age_5to6weeks = age_5to6weeks
        self.age_6to7weeks = age_6to7weeks
        self.age_7to8weeks = age_7to8weeks
        self.age_8to9weeks = age_8to9weeks
        self.age_9to10weeks = age_9to10weeks
        self.age_gre70 = age_gre70

    def __str__(self) -> str:
        return (f"rtm: {self.rtm}, sub_rtm: {self.sub_rtm}, hq_id: {self.hq_id},"
                f" landing_qtd: {self.landing_qtd}, landing_eoh: {self.landing_eoh}, age_gre35: {self.age_gre35}, age_leq35: {self.age_leq35}, "
                f"age_leq1weeks: {self.age_leq1weeks}, age_1to2weeks: {self.age_1to2weeks}, age_2to3weeks: {self.age_2to3weeks}, age_3to4weeks: {self.age_3to4weeks}, age_4to5weeks: {self.age_4to5weeks}")

    def level_by_nand(self):
        return self.level

    # def first_column(self):
    #     if self.rtm == 'All' and self.hq_id == ALL:
    #         return 'China Channel'
    #     if self.sub_rtm == 'All' and self.hq_id == ALL:
    #         return self.rtm
    #     if self.hq_id == 'All' and self.hq_id == ALL:
    #         return self.sub_rtm
    #     return self.hq_id

    def landing_eoh_percent(self):
        return format_percentage(self.landing_eoh, self.landing_qtd)

    def default_percent(self, aging):
        return format_percentage(aging, self.landing_eoh)

    def convert_to_thousands(self, value):
        if value:
            return "{:,.0f}".format(value)
        else:
            return 0

    def age_leq35_threshold_class_name(self, aging):
        return "highlightRed" if compare_percentage_strings(self.default_percent(aging), '25%') > 0 else ""
    
    def age_gre35_threshold_class_name(self, aging):
        return "highlightRed" if compare_percentage_strings(self.default_percent(aging), '25%') > 0 else ""

    def as_dict(self) -> dict:
        return {
            "level": self.level_by_nand(),
            "first_column": self.first_column(),
            "landing_qtd": "{:,.0f}".format(self.landing_qtd),
            "landing_eoh": "{:,.0f}".format(self.landing_eoh),
            "age_gre35": "{:,.0f}".format(self.age_gre35),
            "age_gre35_threshold_class_name": self.age_gre35_threshold_class_name(),
            "age_leq35": "{:,.0f}".format(self.age_leq35),
            "age_leq35_threshold_class_name": self.age_leq35_threshold_class_name(),
            "age_leq1weeks": "{:,.0f}".format(self.age_leq1weeks),
            "age_1to2weeks": "{:,.0f}".format(self.age_1to2weeks),
            "age_2to3weeks": "{:,.0f}".format(self.age_2to3weeks),
            "age_3to4weeks": "{:,.0f}".format(self.age_3to4weeks),
            "age_4to5weeks": "{:,.0f}".format(self.age_4to5weeks),
            "age_5to6weeks": "{:,.0f}".format(self.age_5to6weeks),
            "age_6to7weeks": "{:,.0f}".format(self.age_6to7weeks),
            "age_7to8weeks": "{:,.0f}".format(self.age_7to8weeks),
            "age_8to9weeks": "{:,.0f}".format(self.age_8to9weeks),
            "age_9to10weeks": "{:,.0f}".format(self.age_9to10weeks),
            "age_gre70": "{:,.0f}".format(self.age_gre70),
        }
