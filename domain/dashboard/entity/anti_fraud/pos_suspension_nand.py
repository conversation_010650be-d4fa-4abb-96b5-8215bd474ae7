from domain.dashboard.entity.anti_fraud.anti_fraud_so_ub import RTM_Mono, RTM_MULTI, RTM_CARRIER, RTM_EDU
from util.const import ALL


class Nand:
    def __init__(self,
                 rtm,
                 sub_rtm,
                 disti_name,
                 display_name,
                 level):
        self.rtm = rtm
        self.sub_rtm = sub_rtm
        self.disti_name = disti_name

        self.display_name = display_name
        self.level = level


LEVEL_ONE = "one"
LEVEL_TWO = "two"
LEVEL_THREE = "three"


nands = [
    # overall 白名单
    Nand(rtm=ALL, sub_rtm=ALL, disti_name=ALL, display_name="China Channel", level=LEVEL_ONE),
    Nand(rtm=RTM_Mono, sub_rtm=ALL, disti_name=ALL, display_name=RTM_Mono, level=LEVEL_TWO),
    Nand(rtm=RTM_MULTI, sub_rtm=ALL, disti_name=ALL, display_name=RTM_MULTI, level=LEVEL_TWO),
    Nand(rtm=RTM_CARRIER, sub_rtm=ALL, disti_name=ALL, display_name=RTM_CARRIER, level=LEVEL_TWO),
    Nand(rtm=RTM_EDU, sub_rtm=ALL, disti_name=ALL, display_name=RTM_EDU, level=LEVEL_TWO),

    # ----------------------------------上下之间没有level关系, 因为一次性查出所有数据, 不同的view展示---------------
    # ND View 白名单
    Nand(rtm=RTM_Mono, sub_rtm="Lifestyle", disti_name=ALL, display_name="Lifestyle", level=LEVEL_ONE),

    Nand(rtm=RTM_Mono, sub_rtm="Lifestyle", disti_name="Changhang", display_name="Changhang", level=LEVEL_TWO),
    Nand(rtm=RTM_Mono, sub_rtm="Lifestyle", disti_name="Coodoo", display_name="Coodoo", level=LEVEL_TWO),
    Nand(rtm=RTM_Mono, sub_rtm="Lifestyle", disti_name="Dragon Star", display_name="Dragon Star", level=LEVEL_TWO),
    Nand(rtm=RTM_Mono, sub_rtm="Lifestyle", disti_name="Guangxian", display_name="Guangxian", level=LEVEL_TWO),
    Nand(rtm=RTM_Mono, sub_rtm="Lifestyle", disti_name="Hengzhou", display_name="Hengzhou", level=LEVEL_TWO),
    Nand(rtm=RTM_Mono, sub_rtm="Lifestyle", disti_name="Ingram", display_name="Ingram", level=LEVEL_TWO),
    Nand(rtm=RTM_Mono, sub_rtm="Lifestyle", disti_name="JRZJ", display_name="JRZJ", level=LEVEL_TWO),
    Nand(rtm=RTM_Mono, sub_rtm="Lifestyle", disti_name="Jinmai Kefa", display_name="Jinmai Kefa", level=LEVEL_TWO),
    Nand(rtm=RTM_Mono, sub_rtm="Lifestyle", disti_name="Juran Zhijia", display_name="Juran Zhijia", level=LEVEL_TWO),
    Nand(rtm=RTM_Mono, sub_rtm="Lifestyle", disti_name='Jushi', display_name="Jushi", level=LEVEL_TWO),
    Nand(rtm=RTM_Mono, sub_rtm="Lifestyle", disti_name="Kushan Fugang", display_name="Kushan Fugang", level=LEVEL_TWO),
    Nand(rtm=RTM_Mono, sub_rtm="Lifestyle", disti_name="LTHY", display_name="LTHY", level=LEVEL_TWO),
    Nand(rtm=RTM_Mono, sub_rtm="Lifestyle", disti_name="Meicheng", display_name="Meicheng", level=LEVEL_TWO),
    Nand(rtm=RTM_Mono, sub_rtm="Lifestyle", disti_name="Peidai", display_name="Peidai", level=LEVEL_TWO),
    Nand(rtm=RTM_Mono, sub_rtm="Lifestyle", disti_name="Peizhi", display_name="Peizhi", level=LEVEL_TWO),
    Nand(rtm=RTM_Mono, sub_rtm="Lifestyle", disti_name="Pingfeng", display_name="Pingfeng", level=LEVEL_TWO),
    Nand(rtm=RTM_Mono, sub_rtm="Lifestyle", disti_name="SDYX", display_name="SDYX", level=LEVEL_TWO),
    Nand(rtm=RTM_Mono, sub_rtm="Lifestyle", disti_name="SHZH", display_name="SHZH", level=LEVEL_TWO),
    Nand(rtm=RTM_Mono, sub_rtm="Lifestyle", disti_name="SZSM", display_name="SZSM", level=LEVEL_TWO),
    Nand(rtm=RTM_Mono, sub_rtm="Lifestyle", disti_name="Shangpai", display_name="Shangpai", level=LEVEL_TWO),
    Nand(rtm=RTM_Mono, sub_rtm="Lifestyle", disti_name="Shengdayuan", display_name="Shengdayuan", level=LEVEL_TWO),
    Nand(rtm=RTM_Mono, sub_rtm="Lifestyle", disti_name="Shenhui", display_name="Shenhui", level=LEVEL_TWO),
    Nand(rtm=RTM_Mono, sub_rtm="Lifestyle", disti_name="Shidai Yinxiang", display_name="Shidai Yinxiang", level=LEVEL_TWO),
    Nand(rtm=RTM_Mono, sub_rtm="Lifestyle", disti_name="XYCW", display_name="XYCW", level=LEVEL_TWO),
    Nand(rtm=RTM_Mono, sub_rtm="Lifestyle", disti_name="Xinlian", display_name="Xinlian", level=LEVEL_TWO),
    Nand(rtm=RTM_Mono, sub_rtm="Lifestyle", disti_name="Xinsheng", display_name="Xinsheng", level=LEVEL_TWO),
    Nand(rtm=RTM_Mono, sub_rtm="Lifestyle", disti_name="Xinyao Ruida", display_name="Xinyao Ruida", level=LEVEL_TWO),
    Nand(rtm=RTM_Mono, sub_rtm="Lifestyle", disti_name="Yiwei", display_name="Yiwei", level=LEVEL_TWO),
    Nand(rtm=RTM_Mono, sub_rtm="Lifestyle", disti_name="Zhongheng",  display_name="Zhongheng", level=LEVEL_TWO),


    Nand(rtm=RTM_Mono, sub_rtm="Mono", disti_name=ALL, display_name="Mono", level=LEVEL_ONE),

    Nand(rtm=RTM_Mono, sub_rtm="Mono", disti_name="CHJH", display_name="CHJH", level=LEVEL_TWO),
    Nand(rtm=RTM_Mono, sub_rtm="Mono", disti_name="Changhong Jiahua", display_name="Changhong Jiahua", level=LEVEL_TWO),
    Nand(rtm=RTM_Mono, sub_rtm="Mono", disti_name="Hengsha", display_name="Hengsha", level=LEVEL_TWO),
    Nand(rtm=RTM_Mono, sub_rtm="Mono", disti_name="Ku’ai", display_name="Ku’ai", level=LEVEL_TWO),
    Nand(rtm=RTM_Mono, sub_rtm="Mono", disti_name="Tianyin Xinxi", display_name="Tianyin Xinxi", level=LEVEL_TWO),


    Nand(rtm=RTM_MULTI, sub_rtm="OTC", disti_name=ALL, display_name="OTC", level=LEVEL_ONE),

    Nand(rtm=RTM_MULTI, sub_rtm="OTC", disti_name="ASD", display_name="ASD", level=LEVEL_TWO),
    Nand(rtm=RTM_MULTI, sub_rtm="OTC", disti_name="Telling", display_name="Telling", level=LEVEL_TWO),
    Nand(rtm=RTM_MULTI, sub_rtm="OTC", disti_name="Yueyang", display_name="Yueyang", level=LEVEL_TWO),


    Nand(rtm=RTM_MULTI, sub_rtm="Township", disti_name=ALL, display_name="Township", level=LEVEL_ONE),

    Nand(rtm=RTM_MULTI, sub_rtm="Township", disti_name="Hengze", display_name="Hengze", level=LEVEL_TWO),
    Nand(rtm=RTM_MULTI, sub_rtm="Township", disti_name="JJYT", display_name="JJYT", level=LEVEL_TWO),
    Nand(rtm=RTM_MULTI, sub_rtm="Township", disti_name="Jujia Yuntong", display_name="Jujia Yuntong", level=LEVEL_TWO),


    Nand(rtm=RTM_MULTI, sub_rtm="MM", disti_name=ALL, display_name="MM", level=LEVEL_ONE),

    Nand(rtm=RTM_MULTI, sub_rtm="MM", disti_name="CHJH", display_name="CHJH", level=LEVEL_TWO),
    Nand(rtm=RTM_MULTI, sub_rtm="MM", disti_name="Changhong Jiahua", display_name="Changhong Jiahua", level=LEVEL_TWO),
    Nand(rtm=RTM_MULTI, sub_rtm="MM", disti_name="Ingram", display_name="Ingram", level=LEVEL_TWO),
    Nand(rtm=RTM_MULTI, sub_rtm="MM", disti_name="Juran Zhijia", display_name="Juran Zhijia", level=LEVEL_TWO),
    Nand(rtm=RTM_MULTI, sub_rtm="MM", disti_name="Shike Peidai", display_name="Shike Peidai", level=LEVEL_TWO),


    Nand(rtm=RTM_CARRIER, sub_rtm="CM", disti_name=ALL, display_name="CM", level=LEVEL_ONE),
    Nand(rtm=RTM_CARRIER, sub_rtm="CU", disti_name=ALL, display_name="CU", level=LEVEL_ONE),
    Nand(rtm=RTM_CARRIER, sub_rtm="CT", disti_name=ALL, display_name="CT", level=LEVEL_ONE),
    Nand(rtm=RTM_CARRIER, sub_rtm="CB", disti_name=ALL, display_name="CB", level=LEVEL_ONE),


    Nand(rtm=RTM_EDU, sub_rtm=RTM_EDU, disti_name=ALL, display_name=RTM_EDU, level=LEVEL_ONE),

    Nand(rtm=RTM_EDU, sub_rtm=RTM_EDU, disti_name="Beisheng", display_name="Beisheng", level=LEVEL_TWO),
    Nand(rtm=RTM_EDU, sub_rtm=RTM_EDU, disti_name="Chuangsen", display_name="Chuangsen", level=LEVEL_TWO),
    Nand(rtm=RTM_EDU, sub_rtm=RTM_EDU, disti_name="Fenqile", display_name="Fenqile", level=LEVEL_TWO),
    Nand(rtm=RTM_EDU, sub_rtm=RTM_EDU, disti_name="Haxi", display_name="Haxi", level=LEVEL_TWO),
    Nand(rtm=RTM_EDU, sub_rtm=RTM_EDU, disti_name="Huatong", display_name="Huatong", level=LEVEL_TWO),
    Nand(rtm=RTM_EDU, sub_rtm=RTM_EDU, disti_name="Jialian", display_name="Jialian", level=LEVEL_TWO),
    Nand(rtm=RTM_EDU, sub_rtm=RTM_EDU, disti_name="Juran", display_name="Juran", level=LEVEL_TWO),
    Nand(rtm=RTM_EDU, sub_rtm=RTM_EDU, disti_name="Juran Jiaoyu", display_name="Juran Jiaoyu", level=LEVEL_TWO),
    Nand(rtm=RTM_EDU, sub_rtm=RTM_EDU, disti_name="Lexiang", display_name="Lexiang", level=LEVEL_TWO),
    Nand(rtm=RTM_EDU, sub_rtm=RTM_EDU, disti_name="Meicheng", display_name="Meicheng", level=LEVEL_TWO),
    Nand(rtm=RTM_EDU, sub_rtm=RTM_EDU, disti_name="Ruijiao", display_name="Ruijiao", level=LEVEL_TWO),
    Nand(rtm=RTM_EDU, sub_rtm=RTM_EDU, disti_name="SX Titi", display_name="SX Titi", level=LEVEL_TWO),
    Nand(rtm=RTM_EDU, sub_rtm=RTM_EDU, disti_name="TJ Titi", display_name="TJ Titi", level=LEVEL_TWO),
    Nand(rtm=RTM_EDU, sub_rtm=RTM_EDU, disti_name="Taihuyun", display_name="Taihuyun", level=LEVEL_TWO),
    Nand(rtm=RTM_EDU, sub_rtm=RTM_EDU, disti_name="Tianhe", display_name="Tianhe", level=LEVEL_TWO),
    Nand(rtm=RTM_EDU, sub_rtm=RTM_EDU, disti_name="Wanfang", display_name="Wanfang", level=LEVEL_TWO),
    Nand(rtm=RTM_EDU, sub_rtm=RTM_EDU, disti_name="Winsh", display_name="Winsh", level=LEVEL_TWO),
    Nand(rtm=RTM_EDU, sub_rtm=RTM_EDU, disti_name="Xinyuan", display_name="Xinyuan", level=LEVEL_TWO),
    Nand(rtm=RTM_EDU, sub_rtm=RTM_EDU, disti_name="Youli Maike", display_name="Youli Maike", level=LEVEL_TWO),
    Nand(rtm=RTM_EDU, sub_rtm=RTM_EDU, disti_name="Youli Maike", display_name="Youli Maike", level=LEVEL_TWO),
    Nand(rtm=RTM_EDU, sub_rtm=RTM_EDU, disti_name="Zhongdian", display_name="Zhongdian", level=LEVEL_TWO),
    Nand(rtm=RTM_EDU, sub_rtm=RTM_EDU, disti_name="Zhongjiao", display_name="Zhongjiao", level=LEVEL_TWO),
    Nand(rtm=RTM_EDU, sub_rtm=RTM_EDU, disti_name="Ziguang", display_name="Ziguang", level=LEVEL_TWO),
]


def get_nand_by_disti_name(disti_name) -> Nand:
    for nand in nands:
        if nand.disti_name == disti_name:
            return nand
