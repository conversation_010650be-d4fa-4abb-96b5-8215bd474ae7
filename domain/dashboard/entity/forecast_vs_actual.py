from typing import Optional

from domain.dashboard.entity.fiscal_week import FiscalWeek
from util.const import ALL


def get_obj_attr_by_str(obj, attr):
    if obj is None:
        return None
    if attr is None:
        return None
    try:
        ret = getattr(obj, attr)
    except:
        ret = None
    return ret


class Condition:
    def __init__(self, forecast_version= None,rtm=None, sub_rtm=None, lob=None, sub_lob=None, nand=None, color=None, hr_lr=None) -> None:
        self.fields = {
            "forecast_version": forecast_version,
            "rtm": rtm,
            "sub_rtm": sub_rtm,
            "lob": lob,
            "sub_lob": sub_lob,
            "nand": nand,
            "color": color,
            "hr_lr": hr_lr
        }

    def clone(self):
        return Condition(
            forecast_version=self.fields.get("forecast_version"),
            rtm=self.fields.get("rtm"),
            sub_rtm=self.fields.get("sub_rtm"),
            lob=self.fields.get("lob"),
            sub_lob=self.fields.get("sub_lob"),
            nand=self.fields.get("nand"),
            color=self.fields.get("color"),
            hr_lr=self.fields.get("hr_lr"),
        )
    
    def setted_fields(self)->list:
        return [field for field, value in self.fields.items() if value is not None]
       
    def fields_for_threshold(self, view_by: Optional[str] = None) -> dict:
        """没有设置的字段都是ALL"""
        # 根据view_by，重新整合筛选条件
        if view_by and view_by not in ["sub_lob", "sub_rtm", "nand", "color"]:
            raise Exception("unsupported view_by")
        # threshold中没有lob, hr_lr字段, 所以赋值为None,则不会出现在查询条件中"""
        view_by_none_map = {
            "nand": ["nand"],
            "color": ["color"],
            "sub_rtm": ["rtm", "sub_rtm"],
            "sub_lob": ["sub_lob"],
        }
        fields = self.fields.copy()
        omits = ["lob", "hr_lr"]
        view_by_none_omits = view_by_none_map[view_by] if view_by else []
        omits = omits + view_by_none_omits
        for field, value in fields.items():
            if field in omits:
                fields[field] = None
                continue
            if value is not None:
                continue
            fields[field] = ALL
        return fields
    
    def set_view(self, view_by: str) -> None:
        # 根据view_by，重新整合筛选条件
        if view_by not in ["sub_lob", "sub_rtm", "nand", "color"]:
            raise Exception("unsupported view_by")

        # 只在sub_rtm view下，lob和rtm不应该加筛选条件，其他view下只lob不加筛选
        omits = ["lob", "rtm"] if view_by == "sub_rtm" else ["lob"]
        # view_by对应的字段设置为None，没有设置的字段应该全部设置为ALL
        for field, value in self.fields.items():
            if field in omits:
                continue
            if field == view_by:
                self.fields[field] = None
                continue
            if value is not None:
                continue
            
            self.fields[field] = ALL

    def set_sold_to_id(self, sold_to_id):
        self.fields['sold_to_id'] = sold_to_id


class ForecastVSActual:
    def __init__(
        self, fiscal_week: str, data_type: str, forecast_version: str, 
        rtm: str, sub_rtm: str, lob: str, sub_lob: str, tier: str, 
        nand: str, color: str, forecast_cw: float, ub: float,
        delta: float, accuracy: float,
    ) -> None:
        self.fiscal_week = fiscal_week
        self.data_type = data_type
        self.forecast_version = forecast_version
        self.rtm = rtm
        self.sub_rtm = sub_rtm
        self.lob = lob
        self.sub_lob = sub_lob
        self.tier = tier
        self.nand = nand
        self.color = color
        self.forecast_cw = forecast_cw
        self.ub = ub
        self.delta = delta
        self.accuracy = accuracy


ACCURACY = "Accuracy%"
VALUE = "Value"
DELTA = "Delta"


class ForecastVSActualByWeeks:
    def __init__(
        self,
        data_type: str, forecast_version: str,
        rtm: str, sub_rtm: str, sub_lob: str,
        nand: str, color: str,
        forecast_vs_actuals: list[ForecastVSActual],
    ) -> None:
        self.data_type = data_type
        self.forecast_version = forecast_version
        self.rtm = rtm
        self.sub_rtm = sub_rtm
        self.sub_lob = sub_lob
        self.nand = nand
        self.color = color
        self.forecast_vs_actuals = forecast_vs_actuals

    def weeks(self) -> list[str]:
        wks = [w.fiscal_week for w in self.forecast_vs_actuals]
        wks = sorted(wks, key=lambda x: FiscalWeek(x).fiscal_week_int)
        return wks

    def qualification(self, threshold: float) -> float:
        q_cnt = 0
        total_cnt = len(self.forecast_vs_actuals)
        for forecast_vs_actual in self.forecast_vs_actuals:
            # 兼容一下数据为None的情况
            if forecast_vs_actual.accuracy is None:
                total_cnt -= 1
                continue
            # 产品要求: 达标率要先四舍五入后再进行计算
            round_accuracy = round(forecast_vs_actual.accuracy, 2)
            if round_accuracy >= threshold:
                q_cnt += 1
        return float(q_cnt) / total_cnt if total_cnt else 0.0
