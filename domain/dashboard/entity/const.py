DASHBOARD_FILE_CATEGORY_UPLOAD = 1
DASHBOARD_FILE_MODULE_MPN_MIX = "mpn_mix"

MixTemplateHeaderDict = {
    "Region": "region",
    "LOB": "lob",
    "Sub-LOB": "sub_lob",
    "Nand": "nand",
    "Color": "color",
    "Mpn": "mpn",
    "Mix%": "mix",
}

MixTemplateFileRawHeader = list(MixTemplateHeaderDict.values())

MixTemplateFileHeader = list(MixTemplateHeaderDict.keys())

SUBLOB_COLORS = "sublob_colors"
SUBLOB_NANDS = "sublob_nands"
SUB_LOB = "Sub-LOB"
NAND = "Nand"
COLOR = "Color"


BY_NAND = "by_nand"
BY_COLOR = "by_color"
BY_SUB_LOB = "by_sub_lob"
BY_SUB_RTM = "by_sub_rtm"
