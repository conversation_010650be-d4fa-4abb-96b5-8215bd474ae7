import re
from typing import Optional

from domain.supply.entity import Lob


class SeriesTierSublob:
    def __init__(
        self,
        model: str,
        tier: Optional[str],
        series: Optional[str]
    ) -> None:
        self.model = model
        self.tier = tier
        self.series = series
        self.none_tier = ['N-', None]
        self.none_series = ['N-', None]

    def as_dict(self) -> dict:
        return {
            "model": self.model,
            "tier": self.tier,
            "series": self.series
        }

    def get_tier(self):
        tier_str = []
        pattern = re.compile(r'iphone (\d+)(.*)')
        match = pattern.match(self.model.lower())
        if match and match.group(1) and self.tier not in self.none_tier:
            tier_str.append(Lob.IPHONE.value)
            tier_str.append(str(match.group(1)))
            tier_str.append(self.tier.strip())
            if not tier_str[-1].endswith("Tier"):
                tier_str.append('Tier')
        return " ".join(tier_str)

    def get_series(self):
        series = []
        pattern = re.compile(r'iphone (\d+)(.*)')
        match = pattern.match(self.model.lower())
        if match and match.group(1) and self.series not in self.none_series:
            series.append(Lob.IPHONE.value)
            series.append(self.series.strip())
        return " ".join(series)


class SeriesTierSublobConverter:
    def __init__(
        self,
        series_tier_sub_lobs: list[SeriesTierSublob]
    ) -> None:
        self.series_tier_sub_lobs = series_tier_sub_lobs
        self.tiers = {}
        self.series = {}
        for item in self.series_tier_sub_lobs:
            tier_item = item.get_tier()
            series_item = item.get_series()
            tier_set = self.tiers.setdefault(tier_item, set())
            tier_set.add(item.model.lower())
            series_set = self.series.setdefault(series_item, set())
            series_set.add(item.model.lower())

    def get_series_tier_by_sub_lobs(self, sub_lobs: list[str]) -> list:
        series_tiers = set()
        for sub_lob in sub_lobs:
            for tier, tier_sub_lobs in self.tiers.items():
                if sub_lob.lower() in tier_sub_lobs and tier:
                    series_tiers.add(tier)

            for series, series_sub_lobs in self.series.items():
                if sub_lob.lower() in series_sub_lobs and series:
                    series_tiers.add(series)
        return list(series_tiers)

    @staticmethod
    def format_sub_lob_string(sub_lob):
        # 目标单词和它的转换格式
        target_word = "iphone"
        formatted_word = "iPhone"

        # 分割输入字符串为单词列表
        words = sub_lob.split()

        # 处理每个单词
        formatted_words = []
        for word in words:
            # 如果单词是目标单词，转换为特定格式
            if word.lower() == target_word:
                formatted_words.append(formatted_word)
            else:
                # 对于其他单词，首字母大写
                formatted_words.append(word.capitalize())

        # 重新组合成字符串
        result = ' '.join(formatted_words)
        return result

    def get_sub_lobs_by_series_tier(self, series_tier_or_sub_lob_name):
        sub_lobs = []
        series_sub_lobs = list(self.series.get(series_tier_or_sub_lob_name, []))
        tiers_sub_lobs = list(self.tiers.get(series_tier_or_sub_lob_name, []))
        sub_lobs += series_sub_lobs
        sub_lobs += tiers_sub_lobs
        sub_lobs = list(set(sub_lobs))

        format_sub_lobs = []
        for item in sub_lobs:
            format_sub_lobs.append(self.format_sub_lob_string(item))

        return format_sub_lobs


if __name__ == '__main__':
    s = SeriesTierSublob(model='IPHONE 15 PLUS', tier='Consumer', series='Series')
    s1 = SeriesTierSublob(model='IPHONE 15', tier='N+', series='Series')
    print(s.get_tier())
    print(s.get_series())
    sc = SeriesTierSublobConverter(series_tier_sub_lobs=[s, s1])
    print(sc.get_series_tier_by_sub_lobs(["IPHONE 15 PLUS"]))
    print(sc.get_sub_lobs_by_series_tier("iPhone 15 Series"))
