class MockForecastAccuracy:
    MENU = {
        "fiscal_weeks": ["FY24Q3W11", "FY24Q3W10"],
        "region": ["China mainland"],
        "forecast_version": ["CW", "CW+1", "CW+2", "CW+3", "CW+4"],
        "hr_lr": ["All", "High Runner", "Low Runner"],
        "sub_rtms": ["All", "APR", "AAR"],
        "sub_lobs": [
            {"sub_lob": "All", "nands": [], "colors": []},
            {"sub_lob": "iPhone 15 Series", "nands": [], "colors": []},
        ],
    }
    OVERVIEW = {
        "week_columns": [
            "FY24Q2W9",
            "FY24Q2W10",
            "FY24Q2W11",
            "FY24Q2W12",
            "FY24Q2W13",
            "FY24Q3W1",
            "FY24Q3W2",
            "FY24Q3W3",
            "FY24Q3W4",
            "FY24Q3W5",
            "FY24Q3W6",
            "FY24Q3W7",
            "FY24Q3W8",
            "FY24Q3W9",
        ],
        "forecast": [
            {
                "Type": "ML National (UB)",
                "FY24Q2W9": {"Accuracy%": 0.11, "Value": 10001, "Delta": 10000},
                "FY24Q2W10": {"Accuracy%": 0.12, "Value": 10002, "Delta": 10000},
                "FY24Q2W11": {"Accuracy%": 0.13, "Value": 10003, "Delta": 10000},
                "FY24Q2W12": {"Accuracy%": 0.14, "Value": 10004, "Delta": 10000},
                "FY24Q2W13": {"Accuracy%": -0.15, "Value": -10005, "Delta": -10000},
                "FY24Q3W1": {"Accuracy%": 0.16, "Value": 10006, "Delta": 10000},
                "FY24Q3W2": {"Accuracy%": 0.17, "Value": 10007, "Delta": 10000},
                "FY24Q3W3": {"Accuracy%": -0.18, "Value": -10008, "Delta": -10000},
                "FY24Q3W4": {"Accuracy%": 0.19, "Value": 10009, "Delta": 10000},
                "FY24Q3W5": {"Accuracy%": 0.2, "Value": 10010, "Delta": 10000},
                "FY24Q3W6": {"Accuracy%": 0.21, "Value": 10011, "Delta": 10000},
                "FY24Q3W7": {"Accuracy%": -0.22, "Value": -10012, "Delta": -10000},
                "FY24Q3W8": {"Accuracy%": 0.23, "Value": 10013, "Delta": 10000},
                "FY24Q3W9": {"Accuracy%": 0.24, "Value": 10014, "Delta": 10000},
                "Qualification%": 0.8,
            },
            {
                "Type": "DFA (UB)",
                "FY24Q2W9": {"Accuracy%": 0.21, "Value": 20001, "Delta": 20000},
                "FY24Q2W10": {"Accuracy%": 0.22, "Value": 20002, "Delta": 20000},
                "FY24Q2W11": {"Accuracy%": 0.23, "Value": 20003, "Delta": 20000},
                "FY24Q2W12": {"Accuracy%": 0.24, "Value": 20004, "Delta": 20000},
                "FY24Q2W13": {"Accuracy%": 0.25, "Value": 20005, "Delta": 20000},
                "FY24Q3W1": {"Accuracy%": -0.26, "Value": -20006, "Delta": -20000},
                "FY24Q3W2": {"Accuracy%": 0.27, "Value": 20007, "Delta": 20000},
                "FY24Q3W3": {"Accuracy%": 0.28, "Value": 20008, "Delta": 20000},
                "FY24Q3W4": {"Accuracy%": 0.29, "Value": 20009, "Delta": 20000},
                "FY24Q3W5": {"Accuracy%": 0.3, "Value": 30010, "Delta": 20000},
                "FY24Q3W6": {"Accuracy%": -0.31, "Value": -30011, "Delta": -20000},
                "FY24Q3W7": {"Accuracy%": 0.32, "Value": 30012, "Delta": 20000},
                "FY24Q3W8": {"Accuracy%": 0.33, "Value": 30013, "Delta": 20000},
                "FY24Q3W9": {"Accuracy%": 0.34, "Value": 30014, "Delta": 20000},
                "Qualification%": 0.8,
            },
            {
                "Type": "ML Bottom-up (UB)",
                "FY24Q2W9": {"Accuracy%": 0.31, "Value": 30001, "Delta": 10000},
                "FY24Q2W10": {"Accuracy%": 0.32, "Value": 30002, "Delta": 10000},
                "FY24Q2W11": {"Accuracy%": 0.33, "Value": 30003, "Delta": 10000},
                "FY24Q2W12": {"Accuracy%": -0.34, "Value": -30004, "Delta": -10000},
                "FY24Q2W13": {"Accuracy%": 0.35, "Value": 30005, "Delta": 10000},
                "FY24Q3W1": {"Accuracy%": -0.36, "Value": -30006, "Delta": -10000},
                "FY24Q3W2": {"Accuracy%": 0.37, "Value": 30007, "Delta": 10000},
                "FY24Q3W3": {"Accuracy%": 0.38, "Value": 30008, "Delta": 10000},
                "FY24Q3W4": {"Accuracy%": -0.39, "Value": -30009, "Delta": -10000},
                "FY24Q3W5": {"Accuracy%": 0.4, "Value": 40010, "Delta": 10000},
                "FY24Q3W6": {"Accuracy%": 0.41, "Value": 40011, "Delta": 10000},
                "FY24Q3W7": {"Accuracy%": 0.42, "Value": 40012, "Delta": 10000},
                "FY24Q3W8": {"Accuracy%": -0.43, "Value": -40013, "Delta": -10000},
                "FY24Q3W9": {"Accuracy%": 0.44, "Value": 40014, "Delta": 10000},
                "Qualification%": 0.8,
            }
        ],
        "threshold": 0.8,
        "latest_refreshing_time": "2024-06-17 10:00:00",
    }
    DETAIL = {
        "week_columns": [
            "FY24Q2W9",
            "FY24Q2W10",
            "FY24Q2W11",
            "FY24Q2W12",
            "FY24Q2W13",
            "FY24Q3W1",
            "FY24Q3W2",
            "FY24Q3W3",
            "FY24Q3W4",
            "FY24Q3W5",
            "FY24Q3W6",
            "FY24Q3W7",
            "FY24Q3W8",
            "FY24Q3W9",
        ],
        "accuracy": [
            {
                "nand": "All",
                "color": "All",
                "rtm": "All",
                "sub_rtm": "All",
                "sub_lob": "All",
                "tier": "iPhone 15 Series",
                "FY24Q2W9": 0.3,
                "FY24Q2W10": 0.3,
                "FY24Q2W11": 0.3,
                "FY24Q2W12": 0.3,
                "FY24Q2W13": 0.3,
                "FY24Q3W1": 0.3,
                "FY24Q3W2": 0.3,
                "FY24Q3W3": 0.3,
                "FY24Q3W4": 0.3,
                "FY24Q3W5": 0.3,
                "FY24Q3W6": 0.3,
                "FY24Q3W7": 0.3,
                "FY24Q3W8": 0.3,
                "FY24Q3W9": 0.3,
                "threshold": 0.8,
                "qualification": 0.98,
            }
        ],
    }
