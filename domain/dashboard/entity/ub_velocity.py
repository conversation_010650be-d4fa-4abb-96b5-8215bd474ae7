# orm
from domain.dashboard.entity.fiscal_week import FiscalWeek


class UbVelocity:
    def __init__(
        self, fiscal_week: str, sub_lob: str, nand: str, color: str, ub_velocity: float
    ) -> None:
        self.fiscal_week = fiscal_week
        self.sub_lob = sub_lob
        self.nand = nand
        self.color = color
        self.ub_velocity = ub_velocity


class UbVelocityByWeeks:
    def __init__(
        self, sub_lob: str, nand: str, color: str, weeks: list[UbVelocity]
    ) -> None:
        self.sub_lob = sub_lob
        self.nand = nand
        self.color = color
        self.weeks = weeks

    def as_dict(self) -> tuple:
        """
        return {"sub_lob":"","nand":"","color":"","week1":velocity1,"week2":velocity2...}, fiscal_weeks
        """
        ret = {
            "sub_lob": self.sub_lob,
            "nand": self.nand,
            "color": self.color,
        }
        for obj in self.weeks:
            ret[FiscalWeek(obj.fiscal_week).simp_week_name()] = obj.ub_velocity
        return ret
