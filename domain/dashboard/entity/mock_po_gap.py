class MockPoGap:
    MENU = {
        "lobs": [
            {
                "lob": "iPhone",
                "sub_lobs": [
                    "iPhone 15 Pro Max",
                    "iPhone 15 Pro",
                    "iPhone 15 Plus",
                    "iPhone 15",
                    "iPhone 14 Plus",
                    "iPhone 13"
                ]
            }
        ],
        "rtms": [
            {
                "rtm": "All",
                "sub_rtms": [
                    "All"
                ]
            },
            {
                "rtm": "Carrier",
                "sub_rtms": [
                    "All",
                    "China Broadcast",
                    "China Mobile",
                    "China Telecom",
                    "China Unicom"
                ]
            },
            {
                "rtm": "Retail Partner",
                "sub_rtms": None
            }
        ],
        "retail_partners": [
            {
                "rtm": "All",
                "sub_rtms": [
                    "All"
                ]
            },
            {
                "rtm": "Mono",
                "sub_rtms": [
                    "All",
                    "Lifestyle",
                    "Mono AAR+"
                ]
            },
            {
                "rtm": "Multi",
                "sub_rtms": [
                    "All",
                    "Duty Free",
                    "Mass Merchant",
                    "OTC",
                    "Township"
                ]
            },
            {
                "rtm": "Online",
                "sub_rtms": [
                    "All",
                    "Banking",
                    "Dou Yin POP",
                    "Dou Yin Self Run",
                    "JD self-run",
                    "Marketplace",
                    "Suning",
                    "Vertical"
                ]
            },
            {
                "rtm": "Enterprise",
                "sub_rtms": [
                    "All"
                ]
            },
            {
                "rtm": "Education",
                "sub_rtms": [
                    "All"
                ]
            }
        ]
    }
    PRODUCT_VIEW = {
    "latest_refresh_time": "2024-05-23 11:11:11",
    "po_gap_data": [
        {
            "sub_lob": "total",
            "nand": None,
            "color": None,
            "child": [],
            "cw1_demand": 100,
            "available_po_cw1": 10,
            "po_needed_cw1": 20,
            "cw2_demand": 30,
            "available_po_cw2": 40,
            "po_needed_cw2": 50
        },
        {
            "sub_lob": "iPhone 15 Pro Max",
            "nand": "All",
            "child": [
                {
                    "sub_lob": "iPhone 15 Pro Max",
                    "nand": "256G",
                    "color": "All",
                    "child": [
                        {
                            "sub_lob": "iPhone 15 Pro Max",
                            "nand": "256G",
                            "color": "Black Titanium",
                            "child": [],
            "cw1_demand": 100,
            "available_po_cw1": 10,
            "po_needed_cw1": 20,
            "cw2_demand": 30,
            "available_po_cw2": 40,
            "po_needed_cw2": 50
                        }
                    ],
            "cw1_demand": 100,
            "available_po_cw1": 10,
            "po_needed_cw1": 20,
            "cw2_demand": 30,
            "available_po_cw2": 40,
            "po_needed_cw2": 50
                }
            ],
            "color": None,
            "cw1_demand": 100,
            "available_po_cw1": 10,
            "po_needed_cw1": 20,
            "cw2_demand": 30,
            "available_po_cw2": 40,
            "po_needed_cw2": 50
        }
    ]

    }
    CHANNEL_VIEW = {
        "latest_refresh_time": "2024-05-27 15:40:44",
        "po_gap_data": [
            {
                "rtm": "Total",
                "child": [
                    {
                        "rtm": "Total",
                        "sub_lob": None,
                        "sub_rtm": None,
                        "child": [],
             "cw1_demand": 100,
            "available_po_cw1": 10,
            "po_needed_cw1": 20,
            "cw2_demand": 30,
            "available_po_cw2": 40,
            "po_needed_cw2": 50

                    }
                ]
            },
            {
                "rtm": "Carrier",
                "child": [
                    {
                        "rtm": "Carrier",
                        "sub_lob": "All",
                        "sub_rtm": "All",
                        "child": [
                            {
                                "child": [],
                                "rtm": "Carrier",
                                "sub_lob": "iPhone 15 Pro",
                                "sub_rtm": "All",
            "cw1_demand": 100,
            "available_po_cw1": 10,
            "po_needed_cw1": 20,
            "cw2_demand": 30,
            "available_po_cw2": 40,
            "po_needed_cw2": 50
                            },
                            {
                                "child": [],
                                "rtm": "Carrier",
                                "sub_lob": "iPhone 15 Plus",
                                "sub_rtm": "All",
            "cw1_demand": 100,
            "available_po_cw1": 10,
            "po_needed_cw1": 20,
            "cw2_demand": 30,
            "available_po_cw2": 40,
            "po_needed_cw2": 50
                            },
                            {
                                "child": [],
                                "rtm": "Carrier",
                                "sub_lob": "iPhone 13",
                                "sub_rtm": "All",
            "cw1_demand": 100,
            "available_po_cw1": 10,
            "po_needed_cw1": 20,
            "cw2_demand": 30,
            "available_po_cw2": 40,
            "po_needed_cw2": 50
                            }

                        ],
            "cw1_demand": 100,
            "available_po_cw1": 10,
            "po_needed_cw1": 20,
            "cw2_demand": 30,
            "available_po_cw2": 40,
            "po_needed_cw2": 50
                    },
                    {
                        "rtm": "Carrier",
                        "sub_lob": "All",
                        "sub_rtm": "China Mobile",
                        "child": [
                            {
                                "child": [],
                                "rtm": "Carrier",
                                "sub_lob": "iPhone 15 Pro",
                                "sub_rtm": "China Mobile",
            "cw1_demand": 100,
            "available_po_cw1": 10,
            "po_needed_cw1": 20,
            "cw2_demand": 30,
            "available_po_cw2": 40,
            "po_needed_cw2": 50
                            },
                            {
                                "child": [],
                                "rtm": "Carrier",
                                "sub_lob": "iPhone 15 Pro Max",
                                "sub_rtm": "China Mobile",
            "cw1_demand": 100,
            "available_po_cw1": 10,
            "po_needed_cw1": 20,
            "cw2_demand": 30,
            "available_po_cw2": 40,
            "po_needed_cw2": 50
                            },
                            {
                                "child": [],
                                "rtm": "Carrier",
                                "sub_lob": "iPhone 15 Plus",
                                "sub_rtm": "China Mobile",
            "cw1_demand": 100,
            "available_po_cw1": 10,
            "po_needed_cw1": 20,
            "cw2_demand": 30,
            "available_po_cw2": 40,
            "po_needed_cw2": 50
                            },
                            {
                                "child": [],
                                "rtm": "Carrier",
                                "sub_lob": "iPhone 14",
                                "sub_rtm": "China Mobile",
            "cw1_demand": 100,
            "available_po_cw1": 10,
            "po_needed_cw1": 20,
            "cw2_demand": 30,
            "available_po_cw2": 40,
            "po_needed_cw2": 50
                            },
                            {
                                "child": [],
                                "rtm": "Carrier",
                                "sub_lob": "iPhone 15",
                                "sub_rtm": "China Mobile",
            "cw1_demand": 100,
            "available_po_cw1": 10,
            "po_needed_cw1": 20,
            "cw2_demand": 30,
            "available_po_cw2": 40,
            "po_needed_cw2": 50
                            },
                            {
                                "child": [],

                                "rtm": "Carrier",
                                "sub_lob": "iPhone 13",
                                "sub_rtm": "China Mobile",
            "cw1_demand": 100,
            "available_po_cw1": 10,
            "po_needed_cw1": 20,
            "cw2_demand": 30,
            "available_po_cw2": 40,
            "po_needed_cw2": 50
                            },
                            {
                                "child": [],

                                "rtm": "Carrier",
                                "sub_lob": "iPhone 14 Plus",
                                "sub_rtm": "China Mobile",
            "cw1_demand": 100,
            "available_po_cw1": 10,
            "po_needed_cw1": 20,
            "cw2_demand": 30,
            "available_po_cw2": 40,
            "po_needed_cw2": 50
                            }
                        ],
            "cw1_demand": 100,
            "available_po_cw1": 10,
            "po_needed_cw1": 20,
            "cw2_demand": 30,
            "available_po_cw2": 40,
            "po_needed_cw2": 50
                    }
                ]

            }
]
    }
