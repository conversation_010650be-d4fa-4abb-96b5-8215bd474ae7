import pytest
from fiscal_week import FiscalWeek, cut_fiscal_weeks, week_add_one


def test_cut_fiscal_weeks():
    fiscal_weeks1 = ["FY24Q3W3", "FY24Q3W2", "FY24Q3W1"]
    length1 = 2
    result1 = ["FY24Q3W3", "FY24Q3W2"]
    ret1 = cut_fiscal_weeks(fiscal_weeks1, length1)

    fiscal_weeks2 = ["FY24Q3W3", "FY24Q3W2", "FY24Q3W1"]
    length2 = 5
    result2 = ["FY24Q3W3", "FY24Q3W2", "FY24Q3W1"]
    ret2 = cut_fiscal_weeks(fiscal_weeks2, length2)

    assert ret1 == result1
    assert ret2 == result2

def test_week_add_one():
    fiscal1 = "FY24Q3W3"
    ret1 = week_add_one(fiscal1)
    result1 = "FY24Q3W4"
    
    fiscal2 = "FY24Q3W13"
    ret2 = week_add_one(fiscal2)
    result2 = "FY24Q4W1"
    
    assert ret1 == result1
    assert ret2 == result2