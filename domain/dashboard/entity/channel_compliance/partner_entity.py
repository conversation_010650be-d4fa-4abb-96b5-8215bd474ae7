from decimal import Decimal, ROUND_HALF_UP
from typing import Optional


class BasePartnerUbItem:
    def __init__(self,
                 snapshot_date: str,
                 rtm: str,
                 sub_rtm: str,
                 lob: str,
                 sub_lob: str,
    ) -> None:
        self.snapshot_date = snapshot_date
        self.rtm = rtm
        self.sub_rtm = sub_rtm
        self.lob = lob
        self.sub_lob = sub_lob

        self.fiscal_qtr_year_name_cq = None
        self.total_so_acc_cq = None
        self.ub3_acc_cq = None
        self.ub7_acc_cq = None
        self.fiscal_qtr_year_name_lq = None
        self.total_so_acc_lq = None
        self.ub3_acc_lq = None
        self.ub7_acc_lq = None
        self.fiscal_qtr_week_name_cw = None
        self.total_so_cw = None
        self.ub7_cw = None
        self.fiscal_qtr_week_name_cw1 = None
        self.total_so_cw1 = None
        self.ub7_cw1 = None
        self.fiscal_qtr_week_name_cw2 = None
        self.total_so_cw2 = None
        self.ub7_cw2 = None
        self.fiscal_qtr_week_name_cw3 = None
        self.total_so_cw3 = None
        self.ub7_cw3 = None
        self.fiscal_qtr_week_name_cw4 = None
        self.total_so_cw4 = None
        self.ub7_cw4 = None
        self.last_fiscal_qtr_week_name_cw = None
        self.last_total_so_cw = None
        self.last_ub7_cw = None
        self.last_fiscal_qtr_week_name_cw1 = None
        self.last_total_so_cw1 = None
        self.last_ub7_cw1 = None

    def set_ub_data(self,
                    fiscal_qtr_year_name_cq: str,
                    total_so_acc_cq: Optional[int],
                    ub3_acc_cq: Optional[int],
                    ub7_acc_cq: Optional[int],
                    fiscal_qtr_year_name_lq: str,
                    total_so_acc_lq: Optional[int],
                    ub3_acc_lq: Optional[int],
                    ub7_acc_lq: Optional[int],
                    fiscal_qtr_week_name_cw: str,
                    total_so_cw: Optional[int],
                    ub7_cw: Optional[int],
                    fiscal_qtr_week_name_cw1: str,
                    total_so_cw1: Optional[int],
                    ub7_cw1: Optional[int],
                    fiscal_qtr_week_name_cw2: str,
                    total_so_cw2: Optional[int],
                    ub7_cw2: Optional[int],
                    fiscal_qtr_week_name_cw3: str,
                    total_so_cw3: Optional[int],
                    ub7_cw3: Optional[int],
                    fiscal_qtr_week_name_cw4: str,
                    total_so_cw4: Optional[int],
                    ub7_cw4: Optional[int],
                    last_fiscal_qtr_week_name_cw: str,
                    last_total_so_cw: Optional[int],
                    last_ub7_cw: Optional[int],
                    last_fiscal_qtr_week_name_cw1: str,
                    last_total_so_cw1: Optional[int],
                    last_ub7_cw1: Optional[int]
                    ):
        self.fiscal_qtr_year_name_cq = fiscal_qtr_year_name_cq
        self.total_so_acc_cq = total_so_acc_cq
        self.ub3_acc_cq = ub3_acc_cq
        self.ub7_acc_cq = ub7_acc_cq
        self.fiscal_qtr_year_name_lq = fiscal_qtr_year_name_lq
        self.total_so_acc_lq = total_so_acc_lq
        self.ub3_acc_lq = ub3_acc_lq
        self.ub7_acc_lq = ub7_acc_lq
        self.fiscal_qtr_week_name_cw = fiscal_qtr_week_name_cw
        self.total_so_cw = total_so_cw
        self.ub7_cw = ub7_cw
        self.fiscal_qtr_week_name_cw1 = fiscal_qtr_week_name_cw1
        self.total_so_cw1 = total_so_cw1
        self.ub7_cw1 = ub7_cw1
        self.fiscal_qtr_week_name_cw2 = fiscal_qtr_week_name_cw2
        self.total_so_cw2 = total_so_cw2
        self.ub7_cw2 = ub7_cw2
        self.fiscal_qtr_week_name_cw3 = fiscal_qtr_week_name_cw3
        self.total_so_cw3 = total_so_cw3
        self.ub7_cw3 = ub7_cw3
        self.fiscal_qtr_week_name_cw4 = fiscal_qtr_week_name_cw4
        self.total_so_cw4 = total_so_cw4
        self.ub7_cw4 = ub7_cw4
        self.last_fiscal_qtr_week_name_cw = last_fiscal_qtr_week_name_cw
        self.last_total_so_cw = last_total_so_cw
        self.last_ub7_cw = last_ub7_cw
        self.last_fiscal_qtr_week_name_cw1 = last_fiscal_qtr_week_name_cw1
        self.last_total_so_cw1 = last_total_so_cw1
        self.last_ub7_cw1 = last_ub7_cw1

    @staticmethod
    def calculate_ub_rate(numerator: Optional[int], denominator: Optional[int]) -> Optional[float]:
        if denominator is None or denominator == 0:
            return None
        if numerator == 0:
            return 0

        # 将 numerator 和 denominator 转换为 Decimal 对象
        numerator_decimal = Decimal(numerator)
        denominator_decimal = Decimal(denominator)
        # 计算百分比
        percent = (numerator_decimal / denominator_decimal) * 100
        # 四舍五入到整数位
        rounded_percent = percent.quantize(Decimal('1'), rounding=ROUND_HALF_UP)
        return float(rounded_percent)


class PartnerOverallUbItem(BasePartnerUbItem):
    def __init__(self,
                 snapshot_date: str,
                 rtm: str,
                 sub_rtm: str,
                 nd_type: str,
                 hq_name: str,
                 lob: str,
                 sub_lob: str,
    ) -> None:
        # 调用父类的构造函数，初始化父类的属性
        super().__init__(snapshot_date, rtm, sub_rtm, lob, sub_lob)

        # 初始化子类特有的属性
        self.nd_type = nd_type
        self.hq_name = hq_name

    def as_dict(self) -> dict:
        return {
            "snapshot_date": self.snapshot_date,
            "rtm": self.rtm,
            "sub_rtm": self.sub_rtm,
            "lob": self.lob,
            "sub_lob": self.sub_lob,
            "nd_type": self.nd_type,
            "hq_name": self.hq_name,
            "fiscal_qtr_year_name_cq": self.fiscal_qtr_year_name_cq,
            "total_so_acc_cq": self.total_so_acc_cq,
            "ub3_acc_cq": self.ub3_acc_cq,
            "ub7_acc_cq": self.ub7_acc_cq,
            "fiscal_qtr_year_name_lq": self.fiscal_qtr_year_name_lq,
            "total_so_acc_lq": self.total_so_acc_lq,
            "ub3_acc_lq": self.ub3_acc_lq,
            "ub7_acc_lq": self.ub7_acc_lq,
            "fiscal_qtr_week_name_cw": self.fiscal_qtr_week_name_cw,
            "total_so_cw": self.total_so_cw,
            "ub7_cw": self.ub7_cw,
            "fiscal_qtr_week_name_cw1": self.fiscal_qtr_week_name_cw1,
            "total_so_cw1": self.total_so_cw1,
            "ub7_cw1": self.ub7_cw1,
            "fiscal_qtr_week_name_cw2": self.fiscal_qtr_week_name_cw2,
            "total_so_cw2": self.total_so_cw2,
            "ub7_cw2": self.ub7_cw2,
            "fiscal_qtr_week_name_cw3": self.fiscal_qtr_week_name_cw3,
            "total_so_cw3": self.total_so_cw3,
            "ub7_cw3": self.ub7_cw3,
            "fiscal_qtr_week_name_cw4": self.fiscal_qtr_week_name_cw4,
            "total_so_cw4": self.total_so_cw4,
            "ub7_cw4": self.ub7_cw4,
            "last_fiscal_qtr_week_name_cw": self.last_fiscal_qtr_week_name_cw,
            "last_total_so_cw": self.last_total_so_cw,
            "last_ub7_cw": self.last_ub7_cw,
            "last_fiscal_qtr_week_name_cw1": self.last_fiscal_qtr_week_name_cw1,
            "last_total_so_cw1": self.last_total_so_cw1,
            "last_ub7_cw1": self.last_ub7_cw1
        }

    def as_display_dict(self) -> dict:
        return {
            "rtm": self.rtm,
            "sub_rtm": self.sub_rtm,
            "hq_name": self.hq_name,
            "qtd_last_quarter_3d_rate": self.calculate_ub_rate(self.ub3_acc_lq, self.total_so_acc_lq),
            "qtd_last_quarter_7d_rate": self.calculate_ub_rate(self.ub7_acc_lq, self.total_so_acc_lq),
            "qtd_current_quarter_3d_rate": self.calculate_ub_rate(self.ub3_acc_cq, self.total_so_acc_cq),
            "qtd_current_quarter_7d_rate": self.calculate_ub_rate(self.ub7_acc_cq, self.total_so_acc_cq),
            "weekly_trend_cw_7d_rate": self.calculate_ub_rate(self.ub7_cw, self.total_so_cw),
            "weekly_trend_cw1_7d_rate": self.calculate_ub_rate(self.ub7_cw1, self.total_so_cw1),
            "weekly_trend_cw2_7d_rate": self.calculate_ub_rate(self.ub7_cw2, self.total_so_cw2),
            "weekly_trend_cw3_7d_rate": self.calculate_ub_rate(self.ub7_cw3, self.total_so_cw3),
            "weekly_trend_cw4_7d_rate": self.calculate_ub_rate(self.ub7_cw4, self.total_so_cw4),
            "last_weekly_trend_cw_7d_rate": self.calculate_ub_rate(self.last_ub7_cw, self.last_total_so_cw),
            "last_weekly_trend_cw1_7d_rate": self.calculate_ub_rate(self.last_ub7_cw1, self.last_total_so_cw1),
        }


class PartnerStoreTypeUbItem(BasePartnerUbItem):
    def __init__(self,
                 snapshot_date: str,
                 rtm: str,
                 sub_rtm: str,
                 store_type: str,
                 lob: str,
                 sub_lob: str,
    ) -> None:
        # 调用父类的构造函数，初始化父类的属性
        super().__init__(snapshot_date, rtm, sub_rtm, lob, sub_lob)

        # 初始化子类特有的属性
        self.store_type = store_type

    def as_dict(self) -> dict:
        return {
            "snapshot_date": self.snapshot_date,
            "rtm": self.rtm,
            "sub_rtm": self.sub_rtm,
            "lob": self.lob,
            "sub_lob": self.sub_lob,
            "store_type": self.store_type,
            "fiscal_qtr_year_name_cq": self.fiscal_qtr_year_name_cq,
            "total_so_acc_cq": self.total_so_acc_cq,
            "ub3_acc_cq": self.ub3_acc_cq,
            "ub7_acc_cq": self.ub7_acc_cq,
            "fiscal_qtr_year_name_lq": self.fiscal_qtr_year_name_lq,
            "total_so_acc_lq": self.total_so_acc_lq,
            "ub3_acc_lq": self.ub3_acc_lq,
            "ub7_acc_lq": self.ub7_acc_lq,
            "fiscal_qtr_week_name_cw": self.fiscal_qtr_week_name_cw,
            "total_so_cw": self.total_so_cw,
            "ub7_cw": self.ub7_cw,
            "fiscal_qtr_week_name_cw1": self.fiscal_qtr_week_name_cw1,
            "total_so_cw1": self.total_so_cw1,
            "ub7_cw1": self.ub7_cw1,
            "fiscal_qtr_week_name_cw2": self.fiscal_qtr_week_name_cw2,
            "total_so_cw2": self.total_so_cw2,
            "ub7_cw2": self.ub7_cw2,
            "fiscal_qtr_week_name_cw3": self.fiscal_qtr_week_name_cw3,
            "total_so_cw3": self.total_so_cw3,
            "ub7_cw3": self.ub7_cw3,
            "fiscal_qtr_week_name_cw4": self.fiscal_qtr_week_name_cw4,
            "total_so_cw4": self.total_so_cw4,
            "ub7_cw4": self.ub7_cw4,
            "last_fiscal_qtr_week_name_cw": self.last_fiscal_qtr_week_name_cw,
            "last_total_so_cw": self.last_total_so_cw,
            "last_ub7_cw": self.last_ub7_cw,
            "last_fiscal_qtr_week_name_cw1": self.last_fiscal_qtr_week_name_cw1,
            "last_total_so_cw1": self.last_total_so_cw1,
            "last_ub7_cw1": self.last_ub7_cw1
        }

    def as_display_dict(self) -> dict:
        return {
            "rtm": self.rtm,
            "sub_rtm": self.sub_rtm,
            "store_type_name": self.store_type,
            "qtd_last_quarter_3d_rate": self.calculate_ub_rate(self.ub3_acc_lq, self.total_so_acc_lq),
            "qtd_last_quarter_7d_rate": self.calculate_ub_rate(self.ub7_acc_lq, self.total_so_acc_lq),
            "qtd_current_quarter_3d_rate": self.calculate_ub_rate(self.ub3_acc_cq, self.total_so_acc_cq),
            "qtd_current_quarter_7d_rate": self.calculate_ub_rate(self.ub7_acc_cq, self.total_so_acc_cq),
            "weekly_trend_cw_7d_rate": self.calculate_ub_rate(self.ub7_cw, self.total_so_cw),
            "weekly_trend_cw1_7d_rate": self.calculate_ub_rate(self.ub7_cw1, self.total_so_cw1),
            "weekly_trend_cw2_7d_rate": self.calculate_ub_rate(self.ub7_cw2, self.total_so_cw2),
            "weekly_trend_cw3_7d_rate": self.calculate_ub_rate(self.ub7_cw3, self.total_so_cw3),
            "weekly_trend_cw4_7d_rate": self.calculate_ub_rate(self.ub7_cw4, self.total_so_cw4),
            "last_weekly_trend_cw_7d_rate": self.calculate_ub_rate(self.last_ub7_cw, self.last_total_so_cw),
            "last_weekly_trend_cw1_7d_rate": self.calculate_ub_rate(self.last_ub7_cw1, self.last_total_so_cw1),
        }
