from enum import Enum


# 预测数据类型
class ForecastType(Enum):
    DFA = 'DFA'
    ML_NATIONAL = 'ML National'
    ML_PLUS_RTM = 'ML+RTM'
    ML_PLUS_DFA = 'ML+DFA'
    ML_BOTTOM_UP = 'ML Bottom-up'
    RTM_BOTTOM_UP = 'RTM Bottom-up'
    ACTUAL = 'Actual'
    # RTM_FEEDBACK = 'RTM Feedback'
    RESELLER_FEEDBACK = 'Reseller Feedback'
    NATIONAL_ACTUAL_UB = 'National Actual (UB)'


class ForecastAccuracyViewerType(Enum):
    FAST_DASHBOARD_ACCURACY = 'fast_dashboard_accuracy'
    FEEDBACK_SOLD_TO_ACCURACY = 'feedback_sold_to_accuracy'
    FEEDBACK_REGION_ACCURACY = 'feedback_region_accuracy'
