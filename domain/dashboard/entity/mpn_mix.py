class MpnMix:
    def __init__(
        self,
        fiscal_week: str,
        sub_lob: str,
        mpn_id: str,
        nand: str,
        color: str,
        ml_fcst_mix: float,
        sales_fcst_mix: float,
        fd_fcst_mix: float,
        trimmed_ub_mix: float,
        trimmed_week_cnt: int,
    ) -> None:
        self.fiscal_week = fiscal_week
        self.sub_lob = sub_lob
        self.mpn_id = mpn_id
        self.nand = nand
        self.color = color
        self.ml_fcst_mix = ml_fcst_mix
        self.sales_fcst_mix = sales_fcst_mix
        self.fd_fcst_mix = fd_fcst_mix
        self.trimmed_ub_mix = trimmed_ub_mix
        self.trimmed_week_cnt = trimmed_week_cnt

    def as_dict(self) -> dict:
        return {
            "sub_lob": self.sub_lob,
            "mpn": self.mpn_id,
            "nand": self.nand,
            "color": self.color,
            "ml_fcst_mix": self.ml_fcst_mix,
            # "sales_fcst_mix": self.sales_fcst_mix,
            "fd_fcst_mix": self.fd_fcst_mix,
            "trimmed_ub_mix": self.trimmed_ub_mix,
            "trimmed_week_cnt": self.trimmed_week_cnt,
        }


class MpnMixNandColor:
    def __init__(self, nand: str, color: str) -> None:
        self.nand = nand
        self.color = color
