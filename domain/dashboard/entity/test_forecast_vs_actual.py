from domain.dashboard.entity.forecast_threshold import ForecastThreshold
from domain.dashboard.entity.forecast_vs_actual import Condition


def test_Condition_set_view():
    cond = Condition(sub_lob="iphone 15")
    cond.set_view("nand")

    print(cond.fields)

def test_threshold():
    t = ForecastThreshold({
        "forecast_version": "CW",
        "sub_lob": "iPhone 15",
        "nand": None,
        "color": 'All',
        "rtm": "All",
        "sub_rtm": "All"
    })
    print(t.get_each_threshold({
        "forecast_version": "CW",
        "sub_lob": "iPhone 15",
        "nand": "128GB",
        "color": 'All',
        "rtm": "All",
        "sub_rtm": "All"
    }))