from typing import Optional


class DemandComparisonDetail:
    def __init__(
        self,
        nand: str,
        color: str,
        ub_eoh: Optional[int],
        shipment_plan_cw: Optional[int],
        forecast_cw_ml: Optional[float],
        forecast_cw1_ml: Optional[float],
        forecast_cw2_dfa: Optional[float],
        forecast_cw3_dfa: Optional[float],
        forecast_cw4_dfa: Optional[float],
        forecast_cw5_dfa: Optional[float],
        cw1_ideal_demand: Optional[float],
        cw2_ideal_demand: Optional[float],
        dt_cw1: Optional[float],
        dt_cw2: Optional[float],
        df_cw1: Optional[float],
        df_cw2: Optional[float],
        cw1_di_woi: Optional[float] = None,
        cw2_di_woi: Optional[float] = None,
        cw1_dt_woi: Optional[float] = None,
        cw2_dt_woi: Optional[float] = None,
        cw1_df_woi: Optional[float] = None,
        cw2_df_woi: Optional[float] = None
    ) -> None:
        self.nand = nand
        self.color = color
        self.ub_eoh = ub_eoh
        self.shipment_plan_cw = shipment_plan_cw
        self.forecast_cw_ml = forecast_cw_ml
        self.forecast_cw1_ml = forecast_cw1_ml
        self.forecast_cw2_dfa = forecast_cw2_dfa
        self.forecast_cw3_dfa = forecast_cw3_dfa
        self.forecast_cw4_dfa = forecast_cw4_dfa
        self.forecast_cw5_dfa = forecast_cw5_dfa
        self.cw1_ideal_demand = cw1_ideal_demand
        self.cw2_ideal_demand = cw2_ideal_demand
        self.dt_cw1 = dt_cw1
        self.dt_cw2 = dt_cw2
        self.df_cw1 = df_cw1
        self.df_cw2 = df_cw2
        self.cw1_di_woi = cw1_di_woi
        self.cw2_di_woi = cw2_di_woi
        self.cw1_dt_woi = cw1_dt_woi
        self.cw2_dt_woi = cw2_dt_woi
        self.cw1_df_woi = cw1_df_woi
        self.cw2_df_woi = cw2_df_woi

    def as_dict(self) -> dict:
        return {
            "nand": self.nand,
            "color": self.color,
            "ub_eoh": self.ub_eoh,
            "shipment_plan_cw": self.shipment_plan_cw,
            "forecast_cw_ml": self.forecast_cw_ml,
            "forecast_cw1_ml": self.forecast_cw1_ml,
            "forecast_cw2_dfa": self.forecast_cw2_dfa,
            "forecast_cw3_dfa": self.forecast_cw3_dfa,
            "forecast_cw4_dfa": self.forecast_cw4_dfa,
            "forecast_cw5_dfa": self.forecast_cw5_dfa,
            "cw1_ideal_demand": self.cw1_ideal_demand,
            "cw2_ideal_demand": self.cw2_ideal_demand,
            "dt_cw1": self.dt_cw1,
            "dt_cw2": self.dt_cw2,
            "df_cw1": self.df_cw1,
            "cw1_di_woi": self.cw1_di_woi,
            "cw2_di_woi": self.cw2_di_woi,
            "cw1_dt_woi": self.cw1_dt_woi,
            "cw2_dt_woi": self.cw2_dt_woi,
            "cw1_df_woi": self.cw1_df_woi,
            "cw2_df_woi": self.cw2_df_woi
        }

    def sum(self, source):
        def set_sum_value(attr):
            target_value = getattr(self, attr)
            source_value = getattr(source, attr)
            if target_value is not None and source_value is not None:
                setattr(self, attr, target_value + source_value)
            elif target_value is None:
                setattr(self, attr, source_value)

        sum_attr = ["ub_eoh", "shipment_plan_cw", "forecast_cw_ml", "forecast_cw1_ml", "forecast_cw2_dfa",
                    "forecast_cw3_dfa", "forecast_cw4_dfa", "forecast_cw5_dfa", "cw1_ideal_demand", "cw2_ideal_demand",
                    "dt_cw1", "dt_cw2", "df_cw1", "df_cw2"]
        for attr_name in sum_attr:
            set_sum_value(attr_name)


class DemandComparisonResult:
    def __init__(
        self,
        nand: str,
        color: str,
        ideal_demand: Optional[float],
        ideal_demand_woi: Optional[float],
        top_down_demand: Optional[float],
        top_down_demand_woi: Optional[float],
        final_demand: Optional[float],
        final_demand_woi: Optional[float]
    ) -> None:
        self.nand = nand
        self.color = color
        self.ideal_demand = ideal_demand
        self.ideal_demand_woi = ideal_demand_woi
        self.top_down_demand = top_down_demand
        self.top_down_demand_woi = top_down_demand_woi
        self.final_demand = final_demand
        self.final_demand_woi = final_demand_woi
        self.final_vs_ideal_demand_delta = self.set_demand_delta(demand1=self.final_demand, demand2=self.ideal_demand)
        self.final_vs_ideal_woi_delta = self.set_demand_delta(demand1=self.final_demand_woi, demand2=self.ideal_demand_woi)
        self.final_vs_top_down_demand_delta = self.set_demand_delta(demand1=self.final_demand, demand2=self.top_down_demand)
        self.final_vs_top_down_woi_delta = self.set_demand_delta(demand1=self.final_demand_woi, demand2=self.top_down_demand_woi)
        self.top_down_vs_ideal_demand_delta = self.set_demand_delta(demand1=self.top_down_demand, demand2=self.ideal_demand)
        self.top_down_vs_ideal_woi_delta = self.set_demand_delta(demand1=self.top_down_demand_woi, demand2=self.ideal_demand_woi)

    def as_dict(self) -> dict:
        return {
            "nand": self.nand,
            "color": self.color,
            "ideal_demand": self.ideal_demand,
            "ideal_demand_woi": self.ideal_demand_woi,
            "top_down_demand": self.top_down_demand,
            "top_down_demand_woi": self.top_down_demand_woi,
            "final_demand": self.final_demand,
            "final_demand_woi": self.final_demand_woi,
            "final_vs_ideal_demand_delta": self.final_vs_ideal_demand_delta,
            "final_vs_ideal_woi_delta": self.final_vs_ideal_woi_delta,
            "final_vs_top_down_demand_delta": self.final_vs_top_down_demand_delta,
            "final_vs_top_down_woi_delta": self.final_vs_top_down_woi_delta,
            "top_down_vs_ideal_demand_delta": self.top_down_vs_ideal_demand_delta,
            "top_down_vs_ideal_woi_delta": self.top_down_vs_ideal_woi_delta,
        }

    @staticmethod
    def set_demand_delta(demand1, demand2):
        if any([item is None for item in [demand1, demand2]]):
            return None
        return demand1 - demand2
