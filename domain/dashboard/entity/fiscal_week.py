import datetime
import re
import traceback
from typing import Optional

from util.conf import logger


class FiscalWeek:

    def __init__(self, fiscal_week_name: str, fiscal_week_int: int = None) -> None:
        self.fiscal_week_name = fiscal_week_name
        self.fiscal_week_int = fiscal_week_int

        def fiscal_week_to_int() -> int:
            year = int(self.fiscal_week_name[2:4])  # 提取年份部分
            quarter = int(self.fiscal_week_name.split("Q")[1].split("W")[0])  # 季度
            week = int(self.fiscal_week_name[7:])  # 提取周数部分
            self.fiscal_week_int = int(
                year * 1000 + quarter * 100 + week
            )  # 将年份、季度、周数组合成数字
            return self.fiscal_week_int

        if fiscal_week_int is None:
            self.fiscal_week_int = fiscal_week_to_int()

    def simp_week_name(self) -> str:
        return self.fiscal_week_name[4:]

    def week_name(self) -> str:
        return self.fiscal_week_name[6:]

    def quarter_name(self) -> str:
        return self.fiscal_week_name[:6]

    def year_name(self) -> str:
        return self.fiscal_week_name[:4]
    
    def quarter_week_name(self) -> str:
        return self.fiscal_week_name[4:]


def new_fiscalweek_fill_year(fiscal_week: str) -> str:
    """
    入参缺少年,自动补充当前年。Q1W1-->FY24Q1W1
    """
    now = datetime.datetime.now()
    year = now.strftime("%Y-%m-%d")
    fiscal_week = f"FY{year[2:4]}" + fiscal_week
    result = FiscalWeek(fiscal_week)
    return result


def cut_fiscal_weeks(fiscal_weeks: list, length: int, end_fiscal_weeks: Optional[str] = "FY24Q3W1") -> list:
    try:
        if end_fiscal_weeks:
            fiscal_weeks = fiscal_weeks[:fiscal_weeks.index(end_fiscal_weeks)+1]
        if len(fiscal_weeks) > length:
            fiscal_weeks = fiscal_weeks[: len(fiscal_weeks) - length + 1]
    except Exception as e:
        logger.error(f"cut_fiscal_weeks failed error:{traceback.format_exc()}")
    return fiscal_weeks


def week_add_one(fiscal_week):
    # 如果周>=13 给周赋值1 月加1，周<13，直接周加1返回。月 年同理
    year = int(fiscal_week[2:4])  # 提取年份部分
    quarter = int(fiscal_week.split("Q")[1].split("W")[0])  # 季度
    week = int(fiscal_week[7:])  # 提取周数部分
    if week < 13:
        week += 1
        return "FY" + str(year) + "Q" + str(quarter) + "W" + str(week)
    week = 1
    if quarter < 4:
        quarter += 1
        return "FY" + str(year) + "Q" + str(quarter) + "W" + str(week)
    quarter = 1
    year += 1
    return "FY" + str(year) + "Q" + str(quarter) + "W" + str(week)
