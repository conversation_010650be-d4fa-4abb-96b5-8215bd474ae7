import numpy as np
import pandas as pd

from domain.dashboard.entity.anti_fraud.anti_fraud_so_ub import DailyItem, WeeklyItem, rtms, ONLINE, DailyNdItem, WeeklyNdItem
from domain.dashboard.entity.fiscal_week import FiscalWeek
from util.const import ALL


class UbDailyRecall:
    @classmethod
    def group_by_sub_rtm(cls, ub_list: list[DailyItem]) -> list[DailyItem]:
        if not ub_list:
            return []
        df_data = [item.as_dict() for item in ub_list]
        df = pd.DataFrame(df_data)
        df = df.groupby(['fiscal_dt', 'fiscal_qtr_year_name', 'rtm', 'sub_rtm']).agg({
            'fiscal_dt': "first",
            'fiscal_qtr_year_name': "first",
            'rtm': "first",
            'sub_rtm': "first",
            'fiscal_qtr_year_name_lq': "first",
            'total_so': np.sum,
            'lte_3_days_ub': np.sum,
            'lte_7_days_ub': np.sum,
            'total_so_lq': np.sum,
            'lte_3_days_ub_lq': np.sum,
            'lte_7_days_ub_lq': np.sum
        })
        sub_rtm_all_data = df.to_dict(orient='records')
        ret = []
        for i in sub_rtm_all_data:
            ret.append(DailyItem(
                fiscal_dt=i.get('fiscal_dt'),
                fiscal_qtr_year_name=i.get('fiscal_qtr_year_name'),
                rtm=i.get('rtm'),
                sub_lob=ALL,
                sub_rtm=i.get('sub_rtm'),
                online_offline=ALL,
                platform=ALL,
                total_so=i.get('total_so'),
                lte_3_days_ub=i.get('lte_3_days_ub'),
                lte_7_days_ub=i.get('lte_7_days_ub'),
                fiscal_qtr_year_name_lq=i.get('fiscal_qtr_year_name_lq'),
                total_so_lq=i.get('total_so_lq'),
                lte_3_days_ub_lq=i.get('lte_3_days_ub_lq'),
                lte_7_days_ub_lq=i.get('lte_7_days_ub_lq')
            ))
        return ret

    @classmethod
    def group_by_rtm(cls, ub_list: list[DailyItem]) -> list[DailyItem]:
        if not ub_list:
            return []

        df_data = [item.as_dict() for item in ub_list]
        df = pd.DataFrame(df_data)
        df = df.groupby(['fiscal_dt', 'fiscal_qtr_year_name', 'rtm']).agg({
            'fiscal_dt': "first",
            'fiscal_qtr_year_name': "first",
            'rtm': "first",
            'fiscal_qtr_year_name_lq': "first",
            'total_so': np.sum,
            'lte_3_days_ub': np.sum,
            'lte_7_days_ub': np.sum,
            'total_so_lq': np.sum,
            'lte_3_days_ub_lq': np.sum,
            'lte_7_days_ub_lq': np.sum
        })
        rtm_all_data = df.to_dict(orient='records')
        ret = []

        for i in rtm_all_data:
            ret.append(DailyItem(
                fiscal_dt=i.get('fiscal_dt'),
                fiscal_qtr_year_name=i.get('fiscal_qtr_year_name'),
                rtm=i.get('rtm'),
                sub_lob=ALL,
                sub_rtm=ALL,
                online_offline=ALL,
                platform=ALL,
                total_so=i.get('total_so'),
                lte_3_days_ub=i.get('lte_3_days_ub'),
                lte_7_days_ub=i.get('lte_7_days_ub'),
                fiscal_qtr_year_name_lq=i.get('fiscal_qtr_year_name_lq'),
                total_so_lq=i.get('total_so_lq'),
                lte_3_days_ub_lq=i.get('lte_3_days_ub_lq'),
                lte_7_days_ub_lq=i.get('lte_7_days_ub_lq')
            ))
        # 输出前排序
        ret = sorted(ret, key=lambda x: rtms.index(x.rtm))
        return ret

    @classmethod
    def group_by_region(cls, ub_list: list[DailyItem]) -> list[DailyItem]:
        if not ub_list:
            return []
        df_data = [item.as_dict() for item in ub_list]
        df = pd.DataFrame(df_data)
        df = df.groupby(['fiscal_dt', 'fiscal_qtr_year_name']).agg({
            'fiscal_dt': "first",
            'fiscal_qtr_year_name': "first",
            'fiscal_qtr_year_name_lq': "first",
            'total_so': np.sum,
            'lte_3_days_ub': np.sum,
            'lte_7_days_ub': np.sum,
            'total_so_lq': np.sum,
            'lte_3_days_ub_lq': np.sum,
            'lte_7_days_ub_lq': np.sum
        })
        region_all_data = df.to_dict(orient='records')
        ret = []
        for i in region_all_data:
            ret.append(DailyItem(
                fiscal_dt=i.get('fiscal_dt'),
                fiscal_qtr_year_name=i.get('fiscal_qtr_year_name'),
                rtm=ALL,
                sub_lob=ALL,
                sub_rtm=ALL,
                platform=ALL,
                online_offline=ALL,
                total_so=i.get('total_so'),
                lte_3_days_ub=i.get('lte_3_days_ub'),
                lte_7_days_ub=i.get('lte_7_days_ub'),
                fiscal_qtr_year_name_lq=i.get('fiscal_qtr_year_name_lq'),
                total_so_lq=i.get('total_so_lq'),
                lte_3_days_ub_lq=i.get('lte_3_days_ub_lq'),
                lte_7_days_ub_lq=i.get('lte_7_days_ub_lq')
            ))
        return ret

    # 一级china 使用 获取 online offline数据
    @classmethod
    def group_by_region_online_offline(cls, ub_list: list[DailyItem]) -> list[DailyItem]:
        if not ub_list:
            return []
        df_data = [item.as_dict() for item in ub_list]
        df = pd.DataFrame(df_data)
        df = df.groupby(['fiscal_dt', 'online_offline', 'fiscal_qtr_year_name']).agg({
            'fiscal_dt': "first",
            'online_offline': "first",
            'fiscal_qtr_year_name': "first",
            'fiscal_qtr_year_name_lq': "first",
            'total_so': np.sum,
            'lte_3_days_ub': np.sum,
            'lte_7_days_ub': np.sum,
            'total_so_lq': np.sum,
            'lte_3_days_ub_lq': np.sum,
            'lte_7_days_ub_lq': np.sum
        })
        region_all_data = df.to_dict(orient='records')
        ret = []
        for i in region_all_data:
            ret.append(DailyItem(
                fiscal_dt=i.get('fiscal_dt'),
                rtm=ALL,
                sub_lob=ALL,
                sub_rtm=ALL,
                platform=ALL,
                online_offline=i.get('online_offline'),
                total_so=i.get('total_so'),
                lte_3_days_ub=i.get('lte_3_days_ub'),
                lte_7_days_ub=i.get('lte_7_days_ub'),
                fiscal_qtr_year_name=i.get('fiscal_qtr_year_name'),
                fiscal_qtr_year_name_lq=i.get('fiscal_qtr_year_name_lq'),
                total_so_lq=i.get('total_so_lq'),
                lte_3_days_ub_lq=i.get('lte_3_days_ub_lq'),
                lte_7_days_ub_lq=i.get('lte_7_days_ub_lq')
            ))
        return ret

    # 一级china 使用 获取 online 下的 platform聚合数据
    @classmethod
    def group_by_region_platform(cls, ub_list: list[DailyItem]) -> list[DailyItem]:
        if not ub_list:
            return []
        df_data = [item.as_dict() for item in ub_list if item.online_offline == ONLINE]
        df = pd.DataFrame(df_data)
        df = df.groupby(['fiscal_dt', 'platform', 'fiscal_qtr_year_name']).agg({
            'fiscal_dt': "first",
            'platform': "first",
            'fiscal_qtr_year_name': "first",
            'fiscal_qtr_year_name_lq': "first",
            'total_so': np.sum,
            'lte_3_days_ub': np.sum,
            'lte_7_days_ub': np.sum,
            'total_so_lq': np.sum,
            'lte_3_days_ub_lq': np.sum,
            'lte_7_days_ub_lq': np.sum
        })
        region_all_data = df.to_dict(orient='records')
        ret = []
        for i in region_all_data:
            ret.append(DailyItem(
                fiscal_dt=i.get('fiscal_dt'),
                rtm=ALL,
                sub_lob=ALL,
                sub_rtm=ALL,
                platform=i.get('platform'),
                online_offline=ONLINE,
                total_so=i.get('total_so'),
                lte_3_days_ub=i.get('lte_3_days_ub'),
                lte_7_days_ub=i.get('lte_7_days_ub'),
                fiscal_qtr_year_name=i.get('fiscal_qtr_year_name'),
                fiscal_qtr_year_name_lq=i.get('fiscal_qtr_year_name_lq'),
                total_so_lq=i.get('total_so_lq'),
                lte_3_days_ub_lq=i.get('lte_3_days_ub_lq'),
                lte_7_days_ub_lq=i.get('lte_7_days_ub_lq')
            ))
        return ret

    # 四级subrtm下的online offline 取数
    @classmethod
    def group_by_sub_rtm_online_offline(cls, ub_list: list[DailyItem]) -> list[DailyItem]:
        if not ub_list:
            return []
        df_data = [item.as_dict() for item in ub_list]
        df = pd.DataFrame(df_data)
        df = df.groupby(['fiscal_dt', 'rtm', 'sub_rtm', 'online_offline', 'fiscal_qtr_year_name']).agg({
            'fiscal_dt': "first",
            'rtm': "first",
            'sub_rtm': "first",
            'online_offline': "first",
            'fiscal_qtr_year_name': "first",
            'fiscal_qtr_year_name_lq': "first",
            'total_so': np.sum,
            'lte_3_days_ub': np.sum,
            'lte_7_days_ub': np.sum,
            'total_so_lq': np.sum,
            'lte_3_days_ub_lq': np.sum,
            'lte_7_days_ub_lq': np.sum
        })
        region_all_data = df.to_dict(orient='records')
        ret = []
        for i in region_all_data:
            ret.append(DailyItem(
                fiscal_dt=i.get('fiscal_dt'),
                rtm=i.get('rtm'),
                sub_lob=ALL,
                sub_rtm=i.get('sub_rtm'),
                online_offline=i.get('online_offline'),
                platform=ALL,
                total_so=i.get('total_so'),
                lte_3_days_ub=i.get('lte_3_days_ub'),
                lte_7_days_ub=i.get('lte_7_days_ub'),
                fiscal_qtr_year_name=i.get('fiscal_qtr_year_name'),
                fiscal_qtr_year_name_lq=i.get('fiscal_qtr_year_name_lq'),
                total_so_lq=i.get('total_so_lq'),
                lte_3_days_ub_lq=i.get('lte_3_days_ub_lq'),
                lte_7_days_ub_lq=i.get('lte_7_days_ub_lq')
            ))
        return ret

    # 5级的 platform取数
    @classmethod
    def group_by_online_offline_platform(cls, ub_list: list[DailyItem]) -> list[DailyItem]:
        if not ub_list:
            return []
        df_data = [item.as_dict() for item in ub_list]
        df = pd.DataFrame(df_data)
        df = df.groupby(['fiscal_dt', 'rtm', 'sub_rtm', 'online_offline', 'platform', 'fiscal_qtr_year_name']).agg({
            'fiscal_dt': "first",
            'rtm': "first",
            'sub_rtm': "first",
            'online_offline': "first",
            'platform': "first",
            'fiscal_qtr_year_name': "first",
            'fiscal_qtr_year_name_lq': "first",
            'total_so': np.sum,
            'lte_3_days_ub': np.sum,
            'lte_7_days_ub': np.sum,
            'total_so_lq': np.sum,
            'lte_3_days_ub_lq': np.sum,
            'lte_7_days_ub_lq': np.sum
        })
        region_all_data = df.to_dict(orient='records')
        ret = []
        for i in region_all_data:
            ret.append(DailyItem(
                fiscal_dt=i.get('fiscal_dt'),
                rtm=i.get('rtm'),
                sub_lob=ALL,
                sub_rtm=i.get('sub_rtm'),
                online_offline=i.get('online_offline'),
                platform=i.get('platform'),
                total_so=i.get('total_so'),
                lte_3_days_ub=i.get('lte_3_days_ub'),
                lte_7_days_ub=i.get('lte_7_days_ub'),
                fiscal_qtr_year_name = i.get('fiscal_qtr_year_name'),
                fiscal_qtr_year_name_lq = i.get('fiscal_qtr_year_name_lq'),
                total_so_lq = i.get('total_so_lq'),
                lte_3_days_ub_lq = i.get('lte_3_days_ub_lq'),
                lte_7_days_ub_lq = i.get('lte_7_days_ub_lq')
            ))
        return ret


class UbWeekRecall:
    @classmethod
    def group_by_sub_rtm(cls, ub_list: list[WeeklyItem]) -> list[WeeklyItem]:
        if not ub_list:
            return []
        df_data = [item.as_dict() for item in ub_list]
        df = pd.DataFrame(df_data)
        df = df.groupby(['snapshot_date', 'fiscal_qtr_week_name', 'rtm', 'sub_rtm']).agg({
            'snapshot_date': "first",
            'fiscal_qtr_week_name': "first",
            'rtm': "first",
            'sub_rtm': "first",
            'total_so': np.sum,
            'lte_7_days_ub': np.sum,
        })
        sub_rtm_all_data = df.to_dict(orient='records')
        ret = []
        for i in sub_rtm_all_data:
            ret.append(WeeklyItem(
                snapshot_date=i.get('snapshot_date'),
                fiscal_qtr_week_name=i.get('fiscal_qtr_week_name'),
                rtm=i.get('rtm'),
                sub_lob=ALL,
                sub_rtm=i.get('sub_rtm'),
                platform=ALL,
                online_offline=ALL,
                total_so=i.get('total_so'),
                lte_7_days_ub=i.get('lte_7_days_ub')
            ))
        return ret

    @classmethod
    def group_by_rtm(cls, ub_list: list[WeeklyItem]) -> list[WeeklyItem]:
        if not ub_list:
            return []
        df_data = [item.as_dict() for item in ub_list]
        df = pd.DataFrame(df_data)
        df = df.groupby(['snapshot_date', 'fiscal_qtr_week_name', 'rtm']).agg({
            'snapshot_date': "first",
            'fiscal_qtr_week_name': "first",
            'rtm': "first",
            'total_so': np.sum,
            'lte_7_days_ub': np.sum,
        })
        rtm_all_data = df.to_dict(orient='records')
        ret = []
        for i in rtm_all_data:
            ret.append(WeeklyItem(
                snapshot_date=i.get('snapshot_date'),
                fiscal_qtr_week_name=i.get('fiscal_qtr_week_name'),
                rtm=i.get('rtm'),
                sub_lob=ALL,
                sub_rtm=ALL,
                online_offline=ALL,
                platform=ALL,
                total_so=i.get('total_so'),
                lte_7_days_ub=i.get('lte_7_days_ub')
            ))
        # 输出前排序
        ret = sorted(ret, key=lambda x: (rtms.index(x.rtm), FiscalWeek(x.fiscal_qtr_week_name).fiscal_week_int))
        return ret

    @classmethod
    def group_by_region(cls, ub_list: list[WeeklyItem]) -> list[WeeklyItem]:
        if not ub_list:
            return []
        df_data = [item.as_dict() for item in ub_list]
        df = pd.DataFrame(df_data)
        df = df.groupby(['snapshot_date', 'fiscal_qtr_week_name']).agg({
            'snapshot_date': "first",
            'fiscal_qtr_week_name': "first",
            'total_so': np.sum,
            'lte_7_days_ub': np.sum,
        })
        region_all_data = df.to_dict(orient='records')
        ret = []
        for i in region_all_data:
            ret.append(WeeklyItem(
                snapshot_date=i.get('snapshot_date'),
                fiscal_qtr_week_name=i.get('fiscal_qtr_week_name'),
                rtm=ALL,
                sub_lob=ALL,
                sub_rtm=ALL,
                online_offline=ALL,
                platform=ALL,
                total_so=i.get('total_so'),
                lte_7_days_ub=i.get('lte_7_days_ub')
            ))
        # 输出前排序
        ret = sorted(ret, key=lambda x: FiscalWeek(x.fiscal_qtr_week_name).fiscal_week_int)
        return ret

        # 一级china 使用 获取 online offline数据
    @classmethod
    def group_by_region_online_offline(cls, ub_list: list[WeeklyItem]) -> list[WeeklyItem]:
        if not ub_list:
            return []
        df_data = [item.as_dict() for item in ub_list]
        df = pd.DataFrame(df_data)
        df = df.groupby(['snapshot_date', 'fiscal_qtr_week_name', 'online_offline']).agg({
            'snapshot_date': "first",
            'fiscal_qtr_week_name': "first",
            'online_offline': "first",
            'total_so': np.sum,
            'lte_7_days_ub': np.sum,
        })
        region_all_data = df.to_dict(orient='records')
        ret = []
        for i in region_all_data:
            ret.append(WeeklyItem(
                snapshot_date=i.get('snapshot_date'),
                fiscal_qtr_week_name=i.get('fiscal_qtr_week_name'),
                rtm=ALL,
                sub_lob=ALL,
                sub_rtm=ALL,
                platform=ALL,
                online_offline=i.get('online_offline'),
                total_so=i.get('total_so'),
                lte_7_days_ub=i.get('lte_7_days_ub')
            ))
        # 输出前排序
        ret = sorted(ret, key=lambda x: FiscalWeek(x.fiscal_qtr_week_name).fiscal_week_int)
        return ret

    # 一级china 使用 获取 online 下的 platform聚合数据
    @classmethod
    def group_by_region_platform(cls, ub_list: list[WeeklyItem]) -> list[WeeklyItem]:
        if not ub_list:
            return []
        df_data = [item.as_dict() for item in ub_list if item.online_offline == ONLINE]
        df = pd.DataFrame(df_data)
        df = df.groupby(['snapshot_date', 'fiscal_qtr_week_name', 'platform']).agg({
            'snapshot_date': "first",
            'fiscal_qtr_week_name': "first",
            'platform': "first",
            'total_so': np.sum,
            'lte_7_days_ub': np.sum,
        })
        region_all_data = df.to_dict(orient='records')
        ret = []
        for i in region_all_data:
            ret.append(WeeklyItem(
                snapshot_date=i.get('snapshot_date'),
                fiscal_qtr_week_name=i.get('fiscal_qtr_week_name'),
                rtm=ALL,
                sub_lob=ALL,
                sub_rtm=ALL,
                online_offline=ONLINE,
                platform=i.get('platform'),
                total_so=i.get('total_so'),
                lte_7_days_ub=i.get('lte_7_days_ub')
            ))
        # 输出前排序
        ret = sorted(ret, key=lambda x: FiscalWeek(x.fiscal_qtr_week_name).fiscal_week_int)
        return ret

    # 四级subrtm下的online offline 取数
    @classmethod
    def group_by_sub_rtm_online_offline(cls, ub_list: list[WeeklyItem]) -> list[WeeklyItem]:
        if not ub_list:
            return []
        df_data = [item.as_dict() for item in ub_list]
        df = pd.DataFrame(df_data)
        df = df.groupby(['snapshot_date', 'fiscal_qtr_week_name', 'rtm', 'sub_rtm', 'online_offline']).agg({
            'snapshot_date': "first",
            'fiscal_qtr_week_name': "first",
            'rtm': "first",
            'sub_rtm': "first",
            'online_offline': "first",
            'total_so': np.sum,
            'lte_7_days_ub': np.sum,
        })
        region_all_data = df.to_dict(orient='records')
        ret = []
        for i in region_all_data:
            ret.append(WeeklyItem(
                snapshot_date=i.get('snapshot_date'),
                fiscal_qtr_week_name=i.get('fiscal_qtr_week_name'),
                rtm=i.get('rtm'),
                sub_lob=ALL,
                sub_rtm=i.get('sub_rtm'),
                online_offline=i.get('online_offline'),
                platform=ALL,
                total_so=i.get('total_so'),
                lte_7_days_ub=i.get('lte_7_days_ub')
            ))
        # 输出前排序
        ret = sorted(ret, key=lambda x: FiscalWeek(x.fiscal_qtr_week_name).fiscal_week_int)
        return ret

    # 5级的 platform取数
    @classmethod
    def group_by_online_offline_platform(cls, ub_list: list[WeeklyItem]) -> list[WeeklyItem]:
        if not ub_list:
            return []
        df_data = [item.as_dict() for item in ub_list]
        df = pd.DataFrame(df_data)
        df = df.groupby(['snapshot_date', 'fiscal_qtr_week_name',  'rtm', 'sub_rtm', 'online_offline', 'platform']).agg({
            'snapshot_date': "first",
            'fiscal_qtr_week_name': "first",
            'rtm': "first",
            'sub_rtm': "first",
            'online_offline': "first",
            'platform': "first",
            'total_so': np.sum,
            'lte_7_days_ub': np.sum,
        })
        region_all_data = df.to_dict(orient='records')
        ret = []
        for i in region_all_data:
            ret.append(WeeklyItem(
                snapshot_date=i.get('snapshot_date'),
                fiscal_qtr_week_name=i.get('fiscal_qtr_week_name'),
                rtm=i.get('rtm'),
                sub_lob=ALL,
                sub_rtm=i.get('sub_rtm'),
                online_offline=i.get('online_offline'),
                platform=i.get('platform'),
                total_so=i.get('total_so'),
                lte_7_days_ub=i.get('lte_7_days_ub')
            ))
        # 输出前排序
        ret = sorted(ret, key=lambda x: FiscalWeek(x.fiscal_qtr_week_name).fiscal_week_int)
        return ret


class UbNdDailyRecall:
    @classmethod
    def group_by_ndt1(cls, ub_list: list[DailyNdItem]) -> list[DailyNdItem]:
        if not ub_list:
            return []
        df_data = [item.as_dict() for item in ub_list]
        df = pd.DataFrame(df_data)
        # 注意:  'fiscal_qtr_year_name_lq' 这个字段现在因为没到上个季度所以为空，导致分组失效了, 数据组刷了数据后要展示上个季度再加上这个字段
        df = df.groupby(['fiscal_dt', 'fiscal_qtr_year_name', 'rtm', 'sub_rtm', 'hq_name', 'nd_type']).agg({
            'fiscal_dt': "first",
            'fiscal_qtr_year_name': "first",
            'rtm': "first",
            'sub_rtm': "first",
            'hq_name': "first",
            'nd_type': "first",
            'fiscal_qtr_year_name_lq': "first",
            'total_so': np.sum,
            'lte_3_days_ub': np.sum,
            'lte_7_days_ub': np.sum,
            'total_so_lq': np.sum,
            'lte_3_days_ub_lq': np.sum,
            'lte_7_days_ub_lq': np.sum,
        })
        all_data = df.to_dict(orient='records')
        ret = []
        for i in all_data:
            ret.append(DailyNdItem(
                fiscal_dt=i.get('fiscal_dt'),
                fiscal_qtr_year_name=i.get('fiscal_qtr_year_name'),
                rtm=i.get('rtm'),
                sub_rtm=i.get('sub_rtm'),
                hq_name=i.get('hq_name'),
                nd_type=i.get('nd_type'),
                sub_lob=ALL,
                total_so=i.get('total_so'),
                ub3=i.get('lte_3_days_ub'),
                ub7=i.get('lte_7_days_ub'),
                fiscal_qtr_year_name_lq=i.get('fiscal_qtr_year_name_lq'),
                total_so_lq=i.get('total_so_lq'),
                ub3_lq=i.get('lte_3_days_ub_lq'),
                ub7_lq=i.get('lte_7_days_ub_lq')
            ))
        return ret
    
    @classmethod
    def group_by_sub_rtm(cls, ub_list: list[DailyNdItem]) -> list[DailyNdItem]:
        if not ub_list:
            return []
        df_data = [item.as_dict() for item in ub_list]
        df = pd.DataFrame(df_data)
        # 注意:  'fiscal_qtr_year_name_lq' 这个字段现在因为没到上个季度所以为空，导致分组失效了, 数据组刷了数据后要展示上个季度再加上这个字段
        df = df.groupby(['fiscal_dt', 'fiscal_qtr_year_name', 'rtm', 'sub_rtm']).agg({
            'fiscal_dt': "first",
            'fiscal_qtr_year_name': "first",
            'rtm': "first",
            'sub_rtm': "first",
            'fiscal_qtr_year_name_lq': "first",
            'total_so': np.sum,
            'lte_3_days_ub': np.sum,
            'lte_7_days_ub': np.sum,
            'total_so_lq': np.sum,
            'lte_3_days_ub_lq': np.sum,
            'lte_7_days_ub_lq': np.sum,
        })
        all_data = df.to_dict(orient='records')
        ret = []
        for i in all_data:
            ret.append(DailyNdItem(
                fiscal_dt=i.get('fiscal_dt'),
                fiscal_qtr_year_name=i.get('fiscal_qtr_year_name'),
                rtm=i.get('rtm'),
                sub_rtm=i.get('sub_rtm'),
                hq_name=ALL,
                nd_type=ALL,
                sub_lob=ALL,
                total_so=i.get('total_so'),
                ub3=i.get('lte_3_days_ub'),
                ub7=i.get('lte_7_days_ub'),
                fiscal_qtr_year_name_lq=i.get('fiscal_qtr_year_name_lq'),
                total_so_lq=i.get('total_so_lq'),
                ub3_lq=i.get('lte_3_days_ub_lq'),
                ub7_lq=i.get('lte_7_days_ub_lq')
            ))
        return ret

    @classmethod
    def group_by_rtm(cls, ub_list: list[DailyNdItem]) -> list[DailyNdItem]:
        if not ub_list:
            return []

        df_data = [item.as_dict() for item in ub_list]
        df = pd.DataFrame(df_data)
        # 注意:  'fiscal_qtr_year_name_lq' 这个字段现在因为没到上个季度所以为空，导致分组失效了, 数据组刷了数据后要展示上个季度再加上这个字段
        df = df.groupby(['fiscal_dt', 'fiscal_qtr_year_name', 'rtm']).agg({
            'fiscal_dt': "first",
            'fiscal_qtr_year_name': "first",
            'rtm': "first",
            'fiscal_qtr_year_name_lq': "first",
            'total_so': np.sum,
            'lte_3_days_ub': np.sum,
            'lte_7_days_ub': np.sum,
            'total_so_lq': np.sum,
            'lte_3_days_ub_lq': np.sum,
            'lte_7_days_ub_lq': np.sum,
        })
        all_data = df.to_dict(orient='records')
        ret = []
        for i in all_data:
            ret.append(DailyNdItem(
                fiscal_dt=i.get('fiscal_dt'),
                fiscal_qtr_year_name=i.get('fiscal_qtr_year_name'),
                rtm=i.get('rtm'),
                sub_rtm=ALL,
                hq_name=ALL,
                nd_type=ALL,
                sub_lob=ALL,
                total_so=i.get('total_so'),
                ub3=i.get('lte_3_days_ub'),
                ub7=i.get('lte_7_days_ub'),
                fiscal_qtr_year_name_lq=i.get('fiscal_qtr_year_name_lq'),
                total_so_lq=i.get('total_so_lq'),
                ub3_lq=i.get('lte_3_days_ub_lq'),
                ub7_lq=i.get('lte_7_days_ub_lq')
            ))
        return ret


class UbNdWeekRecall:
    @classmethod
    def group_by_ndt1(cls, ub_list: list[WeeklyNdItem]) -> list[WeeklyNdItem]:
        if not ub_list:
            return []
        df_data = [item.as_dict() for item in ub_list]
        df = pd.DataFrame(df_data)
        df = df.groupby(['snapshot_date', 'fiscal_qtr_week_name', 'rtm', 'sub_rtm', 'hq_name', 'nd_type']).agg({
            'snapshot_date': "first",
            'fiscal_qtr_week_name': "first",
            'rtm': "first",
            'sub_rtm': "first",
            'hq_name': "first",
            'nd_type': "first",
            'total_so': np.sum,
            'lte_7_days_ub': np.sum,
        })
        all_data = df.to_dict(orient='records')
        ret = []
        for i in all_data:
            ret.append(WeeklyNdItem(
                snapshot_date=i.get('snapshot_date'),
                fiscal_qtr_week_name=i.get('fiscal_qtr_week_name'),
                rtm=i.get('rtm'),
                sub_rtm=i.get('sub_rtm'),
                hq_name=i.get('hq_name'),
                nd_type=i.get('nd_type'),
                sub_lob=ALL,
                total_so=i.get('total_so'),
                lte_7_days_ub=i.get('lte_7_days_ub')
            ))
        return ret
    
    @classmethod
    def group_by_sub_rtm(cls, ub_list: list[WeeklyNdItem]) -> list[WeeklyNdItem]:
        if not ub_list:
            return []
        df_data = [item.as_dict() for item in ub_list]
        df = pd.DataFrame(df_data)
        df = df.groupby(['snapshot_date', 'fiscal_qtr_week_name', 'rtm', 'sub_rtm']).agg({
            'snapshot_date': "first",
            'fiscal_qtr_week_name': "first",
            'rtm': "first",
            'sub_rtm': "first",
            'total_so': np.sum,
            'lte_7_days_ub': np.sum,
        })
        all_data = df.to_dict(orient='records')
        ret = []
        for i in all_data:
            ret.append(WeeklyNdItem(
                snapshot_date=i.get('snapshot_date'),
                fiscal_qtr_week_name=i.get('fiscal_qtr_week_name'),
                rtm=i.get('rtm'),
                sub_rtm=i.get('sub_rtm'),
                hq_name=ALL,
                nd_type=ALL,
                sub_lob=ALL,
                total_so=i.get('total_so'),
                lte_7_days_ub=i.get('lte_7_days_ub')
            ))
        return ret
    
    @classmethod
    def group_by_rtm(cls, ub_list: list[WeeklyNdItem]) -> list[WeeklyNdItem]:
        if not ub_list:
            return []
        df_data = [item.as_dict() for item in ub_list]
        df = pd.DataFrame(df_data)
        df = df.groupby(['snapshot_date', 'fiscal_qtr_week_name', 'rtm']).agg({
            'snapshot_date': "first",
            'fiscal_qtr_week_name': "first",
            'rtm': "first",
            'total_so': np.sum,
            'lte_7_days_ub': np.sum,
        })
        all_data = df.to_dict(orient='records')
        ret = []
        for i in all_data:
            ret.append(WeeklyNdItem(
                snapshot_date=i.get('snapshot_date'),
                fiscal_qtr_week_name=i.get('fiscal_qtr_week_name'),
                rtm=i.get('rtm'),
                sub_rtm=ALL,
                hq_name=ALL,
                nd_type=ALL,
                sub_lob=ALL,
                total_so=i.get('total_so'),
                lte_7_days_ub=i.get('lte_7_days_ub')
            ))
        return ret
