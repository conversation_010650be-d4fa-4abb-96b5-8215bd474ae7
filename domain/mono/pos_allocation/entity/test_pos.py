from domain.mono.pos_allocation.entity.pos import Pos,sort_pos


def test_sort_pos():
    poses = [
        Pos('862150', '酷爱', 3868483, "太原波导运城万荣社区店", "Hub AAR", "Actived", 12, []),
        <PERSON><PERSON>('862150', '酷爱', 1177268,"联信电子北镇店" , "AAR+","Deleted",12,[]),
        <PERSON><PERSON>('862150', '酷爱', 4076065, "讯湃通讯南阳卧龙金玛特时尚广场店", "APR","Actived",8,[]),
        <PERSON><PERSON>('862150', '酷爱', 949526, "FORTUNE巴中店", "AAR+", "Actived", 8, []),
        <PERSON><PERSON>('862150', '酷爱', 1107939, "宝丰通讯铁岭西丰店", "AAR+", "Actived", 12, []),
    ]
    sorted_poses = sort_pos(poses)
    print("\n")
    for pos in sorted_poses:
        print(pos)
