from domain.supply.entity import Lob


class Mpn:

    def __init__(self, mpn_id:str, lob:Lob, sub_lob:str,is_npp:bool, twoi=0, npp_min_inventory=0, lifestyle_type: str = None, mono_type: str = None):
        self.id = mpn_id
        self.lob = lob
        self.sub_lob = sub_lob
        self.is_npp = is_npp
        self.npp_min_inventory = npp_min_inventory
        self.project_code = None
        self.description = None
        self.lifestyle_type = lifestyle_type
        self.mono_type = mono_type
        self.twoi = twoi

    def is_mac(self)->bool:
        return self.lob == Lob.MAC.value
    
    def set_project_code(self, project_code):
        self.project_code = project_code
        
    def set_description(self, description):
        self.description = description

    def as_dict(self) -> dict:
        return {
            "id": self.id,
            "lob": self.lob,
            "sub_lob": self.sub_lob,
            "is_npp": self.is_npp,
            "npp_min_inventory": self.npp_min_inventory,
            "project_code": self.project_code,
            "description": self.description,
            "lifestyle_type": self.lifestyle_type,
            "mono_type": self.mono_type,
        }
