import math

import pandas as pd

from domain.mono.pos_allocation.entity.allocate_record import AllocateRecord
from domain.mono.pos_allocation.entity.cdc import Cdc, get_cdc_by_ship_to
from domain.mono.pos_allocation.entity.mpn import Mpn
from domain.mono.pos_allocation.entity.mpn_info import MpnInfo
from domain.mono.pos_allocation.entity.shipment_plan_supply import ShipmentPlanSupplier

CATEGORY_ACTIVED = "Actived"
CATEGORY_DELETED = "Deleted"

POS_TYPE_APR = "APR"

LOCATION_TYPE_COMMERCIAL_STREET = "Commercial street"
LOCATION_TYPE_PHONE_STREET = "Phone Street"

CDC_ORDERS = [0, 1, 2, 3, 4, 5]  # cdc顺位顺序，目前最多6个


def _round_float(v: float) -> float:
    return round(v, 4)


LIFESTYLE = 'Lifestyle'
MONO = 'Mono'
T2_RESELLER_TYPE = 'T2'


class Pos:
    def __init__(self, hq_id: str, reseller_name: str, sold_to_id: str, sold_to_name: str, pos_id: int, pos_name: str, pos_type: str, location_type: str, category: str,
                 district_group: int, cdcs: list[str]):
        self.eoh = {}
        self.twoi_fcst = {}

        self.sold_to_id = sold_to_id
        self.hq_id = hq_id
        self.reseller_name = reseller_name
        self.sold_to_name = sold_to_name
        self.pos_id = pos_id
        self.pos_name = pos_name
        self.cdcs = cdcs
        self.category = category  # Actived or Deleted
        self.pos_type = pos_type
        self.location_type = location_type
        self.district_group = district_group  # city tier

        self.allocated_records: dict[str,list[AllocateRecord]] = {}

        # hq info
        self.supplier_name = None
        self.sub_rtm = None
        self.npp_type = None
        self.reseller_simp_name = None
        self.reseller_type = None
        self.npp_supplier_sold_to_id = None
        self.supplier_hq_id = None
        self.customer_sold_to_id = None
        self.reseller_chinese_name = None

        self.stop_sublob_list = None

    def high_low_runner(self, mpn: Mpn):
        # hr_lr
        high_low_runner = None
        if self.sub_rtm == LIFESTYLE:
            high_low_runner = mpn.lifestyle_type
        if self.sub_rtm == MONO:
            high_low_runner = mpn.mono_type
        return high_low_runner

    def npp_reseller(self, mpn: Mpn):
        # npp_reseller
        npp_reseller = None
        if self.high_low_runner(mpn) in ['NPP', 'eCPP'] and self.npp_type == 'NPP reseller':
            npp_reseller = self.npp_supplier_sold_to_id
        return npp_reseller

    def t2_reseller_name(self):
        return self.reseller_simp_name if self.reseller_type == T2_RESELLER_TYPE else None

    def t2_hq_id(self):
        return self.hq_id if self.reseller_type == T2_RESELLER_TYPE else None

    def set_hq_info(self, sub_rtm=None, npp_type=None, reseller_simp_name=None, reseller_type=None, npp_supplier_sold_to_id=None, supplier_hq_id=None, customer_sold_to_id=None, reseller_chinese_name=None, supplier_name=None):
        self.sub_rtm = sub_rtm
        self.npp_type = npp_type
        self.reseller_simp_name = reseller_simp_name
        self.reseller_type = reseller_type
        self.npp_supplier_sold_to_id = npp_supplier_sold_to_id
        self.supplier_hq_id = supplier_hq_id
        self.customer_sold_to_id = customer_sold_to_id
        self.reseller_chinese_name = reseller_chinese_name
        self.supplier_name = supplier_name

    def add_stop_sublob(self, stop_sublob):
        if self.stop_sublob_list is None:
            self.stop_sublob_list = []
        self.stop_sublob_list.append(stop_sublob)

    def __repr__(self):
        record_str = "["
        for mpn_id, records in self.allocated_records.items():
            for record in records:
                record_str += f"Get {record.fix_quantity},{record.instock_quantity},{record.woi_quantity} ({record.mpn.id}) from ({record.supplier_id})\n"
        record_str += "]"
        return (
            f"Pos(sold_to_id={self.sold_to_id},sold_to_name={self.sold_to_name},pos_id={self.pos_id},pos_name={self.pos_name}, category={self.category}, district_group={self.district_group},"
            f"cdcs={self.cdcs},location_type={self.location_type}, pos_type={self.pos_type} \n"
            f"{record_str})")

    def serialize_for_df(self, cdcs: list[Cdc], npp_mpns: list[Mpn]) -> list:
        ret = []
        for mpn in npp_mpns:
            records = self.allocated_records.get(mpn.id, [])
            if len(records) == 0:
                # 不补货也要写入到数据里
                records.append(AllocateRecord(
                    mpn=mpn,
                    supplier_id="",
                    allocate_record_type=0,
                    quantity=0))

            for record in records:
                ship_to_id = self.cdcs[0] if len(self.cdcs) > 0 else ""
                cdc = get_cdc_by_ship_to(cdcs, ship_to_id)
                warehouse_name = ""
                if cdc is not None:
                    warehouse_name = cdc.warehouse_name
                ret.append({
                    "hq_id": self.hq_id,
                    "reseller_name": self.reseller_name,
                    'sold_to_id': self.sold_to_id,
                    'sold_to_name': self.sold_to_name,
                    'pos_id': self.pos_id,
                    'pos_name': self.pos_name,
                    'category': self.category,
                    'district_group': self.district_group,
                    'location_type': self.location_type,
                    'POS type': self.pos_type,
                    'LOB': record.mpn.lob if not record.mpn.is_mac() else 'CPU',
                    'sub_lob': record.mpn.sub_lob,
                    'cdcs': ",".join(self.cdcs),
                    'Apple Part #': record.mpn.id,
                    'project_code': record.mpn.project_code,
                    'description': record.mpn.description,
                    'Ship to ID': ship_to_id, # 取cdc1
                    'Warehouse Name': warehouse_name,
                    'Sustaining/NPI': 'NPP',
                    'eoh': self.get_eoh(mpn.id),
                    'woi': self.woi(mpn),
                    'twoi_fcst': self.twoi_fcst.get(mpn.id),
                    'total_inv': self.cur_eoh(mpn),
                    'npp_min_inventory': mpn.npp_min_inventory,
                    '特殊补货': record.fix_quantity,
                    '0库存补货': record.instock_quantity,
                    'woi补货': record.woi_quantity,
                })
        return ret

    def serialize_for_mono_df(self, npp_mpns: list[Mpn], plan_id: int, mpn_infos: list[MpnInfo]) -> list:
        ret = []
        # 获取当前年月日
        update_date_str = pd.Timestamp.now().strftime('%Y-%m-%d')
        for mpn in npp_mpns:
            records = self.allocated_records.get(mpn.id, [])
            mpn_info = next((x for x in mpn_infos if x.id == mpn.id), None)
            if len(records) == 0:
                # 不补货也要写入到数据里
                records.append(AllocateRecord(
                    mpn=mpn,
                    supplier_id="",
                    allocate_record_type=0,
                    quantity=0))
            for record in records:
                ret.append({
                    'plan_id': plan_id,
                    'pos_status': self.category,
                    'stop_flag': self.get_stop_flag(record.mpn.sub_lob),
                    'rtm': self.sub_rtm,
                    'supplier_name': self.supplier_name,
                    'customer_sold_to_id': str(self.sold_to_id).zfill(10) if self.sold_to_id else self.sold_to_id,
                    'hq_id': self.hq_id,
                    'reseller_name': self.reseller_chinese_name,
                    'pos_id': self.pos_id,
                    'pos_name': self.pos_name,
                    'pos_type': self.pos_type,
                    'lob': record.mpn.lob if not record.mpn.is_mac() else 'CPU',
                    'model': record.mpn.sub_lob,
                    'project_code': mpn_info.project_code if mpn_info is not None else None,
                    'mpn': record.mpn.id,
                    'description': mpn_info.description if mpn_info is not None else None,
                    'mpn_type': 'NPP',
                    'current_stock_num': self.get_eoh(mpn.id),
                    'special_distribute_num': record.fix_quantity,
                    'zero_inventory_replenishment_num': record.instock_quantity,
                    'twoi_distribute_num': record.woi_quantity,
                    'final_num': record.fix_quantity + record.instock_quantity + record.woi_quantity,
                    'update_date_str': update_date_str,
                    'is_online': 0
                })
        return ret

    def is_apr(self) -> bool:
        return self.pos_type == POS_TYPE_APR

    def is_active(self) -> bool:
        return self.category != CATEGORY_DELETED

    def get_stop_flag(self, sub_lob):
        if not self.is_active():
            return 'D'
        if self.stop_sublob_list is not None and str.lower(sub_lob) in self.stop_sublob_list:
            return 'S'
        return None


    def woi(self, mpn:Mpn)->float:
        # (EOH +当期in-stock分货＋当期fix特殊分货＋当期TWOI分货）/ avg.（CW~CW+2 fcst）
        twoi_fcst = self.twoi_fcst.get(mpn.id, None)
        if twoi_fcst is None or twoi_fcst == 0:
            return 0

        return self.cur_eoh(mpn) * mpn.twoi / twoi_fcst

    def set_eoh(self, eoh):
        self.eoh.update(eoh)   # 店面库存

    def set_twoi_fcst(self, twoi_fcst):
        self.twoi_fcst.update(twoi_fcst)  # ml预测数据

    def get_eoh(self, mpn_id: str):
        if mpn_id in self.eoh:
            return self.eoh[mpn_id]
        return 0

    def npp_min_inventory_target(self, mpn: Mpn):
        '''
         对于MaC NPP MPN, POS location type = Commercial street & Phone Street的target均视为0
        '''
        if not mpn.is_npp:
            return 0
        if mpn.is_mac() and self.location_type in [LOCATION_TYPE_COMMERCIAL_STREET, LOCATION_TYPE_PHONE_STREET]:
            return 0
        return mpn.npp_min_inventory

    def cur_eoh(self, mpn: Mpn) -> float:
        allocated = 0
        if mpn.id in self.allocated_records:
            for record in self.allocated_records.get(mpn.id):
                allocated += record.instock_quantity
                allocated += record.fix_quantity
                allocated += record.woi_quantity
        return _round_float(self.get_eoh(mpn.id) + allocated)

    def in_stock_gap(self, mpn: Mpn) -> int:
        return self.npp_min_inventory_target(mpn) - self.cur_eoh(mpn)

    def woi_gap(self, mpn: Mpn) -> float:
        target = math.ceil(self.twoi_fcst.get(mpn.id, 0))
        cur_eoh = self.cur_eoh(mpn)
        print(f"pos:{self.pos_id}, mpn:{mpn.id}, target:{target}, cur_eoh: {cur_eoh}")
        return target - cur_eoh  # 不到就继续补货

    def cdc(self, cdcs: list[Cdc], cdc_order):
        if cdc_order >= len(self.cdcs):
            return None

        cdc_id = self.cdcs[cdc_order]
        for cdc in cdcs:
            if cdc.ship_to == cdc_id:
                return cdc
        return None

    def equal_cdc_order(self, cdc_id, cdc_order) -> bool:
        if cdc_order >= len(self.cdcs):
            return False
        return self.cdcs[cdc_order] == cdc_id

    def allocate_from_shipment_plan(self, supplier: ShipmentPlanSupplier, mpn: Mpn, allocate_type:int, quantity=1):
        actual_quantity = supplier.allocate(mpn, quantity)  # 从supplier中拿货
        if actual_quantity > 0:
            self.__add_allocation_record(mpn, str(supplier.sold_to_id), actual_quantity, allocate_type)

    def allocate_from_cdc(self, cdc: Cdc, mpn: Mpn, allocate_type:int, quantity=1):
        actual_quantity = cdc.allocate(mpn, quantity)  # 从cdc中拿货
        if actual_quantity > 0:
            self.__add_allocation_record(mpn, cdc.ship_to, actual_quantity, allocate_type)

    def __add_allocation_record(self, mpn: Mpn, supplier_id:str, quantity: int, allocate_type:int):
        # 分到本pos上
        def get_by_mpn_supplier(mpn_id, supplier_id):
            if mpn_id not in self.allocated_records:
                self.allocated_records[mpn_id]=[]
                return None
            records = self.allocated_records[mpn_id]
            for record in records:
                if record.supplier_id == supplier_id:
                    return record
            return None

        rc = get_by_mpn_supplier(mpn.id, supplier_id)
        if rc is None:
            rc = AllocateRecord(
                mpn=mpn,
                supplier_id=supplier_id,
                allocate_record_type=allocate_type,
                quantity=quantity,
            )
            self.allocated_records[mpn.id].append(rc)
        else:
            rc.add(allocate_type, quantity)


def sort_pos(pos_list: list):
    """
     district_group(city tier)数字小的优先级高。district_group 为None的优先级最低
     同一District Group中，POS type为APR的门店优先，其余类型门店POSID小的优先。
     同为APR门店时，POSID小的优先
    """
    def sort_key(pos):
        district_group_priority = pos.district_group if pos.district_group is not None else float('inf')
        pos_type_priority = 0 if pos.pos_type == 'APR' else 1
        pos_id_priority = pos.pos_id
        return district_group_priority, pos_type_priority, pos_id_priority
    sorted_list = sorted(pos_list, key=sort_key)
    return sorted_list
