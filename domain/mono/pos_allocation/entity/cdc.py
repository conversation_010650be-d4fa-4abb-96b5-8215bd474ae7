import logging

from domain.mono.pos_allocation.entity.mpn import Mpn
from util.const import ErrorExcept, ErrCode


class Cdc:
    def __init__(self, ship_to: str, sold_to_id: int, warehouse_name: str) -> None:
        self.ship_to = ship_to
        self.sold_to = sold_to_id
        self.warehouse_name = warehouse_name

        self.pod_supply = {}  # key: mpn_id, value: supply_amount
        self.warehouse_supply = {}  # key: mpn_id, value: supply_amount
        self.supply = {}  # 总供应

    def __repr__(self):
        return (
            f"Cdc(ship_to={self.ship_to}, sold_to={self.sold_to}, \n      pod_supply={self.pod_supply},\nwarehouse_supply={self.warehouse_supply},"
            f"\n          supply={self.supply})")

    def serialize_for_df(self, cdcs: list, npp_mpns) -> list:
        ret = []
        for mpn_id, quantity in self.supply.items():
            mpn = None
            for m in npp_mpns:
                if m.id == mpn_id:
                    mpn = m
            ret.append({
                'ship_to': self.ship_to,
                'sold_to': self.sold_to,
                'mpn': mpn_id,
                'lob':mpn.lob if mpn is not None else None,
                'sub_lob':mpn.sub_lob if mpn is not None else None,
                'remaining': quantity,
                'pod_supply': self.pod_supply.get(mpn_id, 0),
                'warehouse_supply': self.warehouse_supply.get(mpn_id, 0),
                'allocated': self.warehouse_supply.get(mpn_id, 0) + self.pod_supply.get(mpn_id, 0) - quantity,
            })
        return ret

    def get_origin_supply(self, mpn: Mpn) -> int:
        quantity = 0
        if mpn.id in self.pod_supply:
            quantity += self.pod_supply[mpn.id]
        if mpn.id in self.warehouse_supply:
            quantity += self.warehouse_supply[mpn.id]
        return quantity

    def get_current_supply(self, mpn: Mpn) -> int:
        quantity = 0
        if mpn.id in self.supply:
            quantity += self.supply[mpn.id]
        return quantity

    def allocate(self, mpn: Mpn, quantity: int):
        remaining = self.get_current_supply(mpn)
        if remaining == 0:
            return 0
        if remaining >= quantity:
            self.supply[mpn.id] -= quantity  # 从supply中减去对应量
            return quantity
        else:
            msg = f"Insufficient quantity {quantity} of {mpn.id} in {self.ship_to}'s stock {self.supply[mpn.id]}."
            logging.error(msg)
            self.supply[mpn.id] = 0
            return remaining

    def has_supply(self, mpn: Mpn) -> bool:
        if mpn.id in self.supply and self.supply[mpn.id] > 0:
            return True
        return False

    def add_pod_supply(self, mpn_id: str, quantity: int):
        if quantity <= 0:
            return
        if mpn_id in self.pod_supply:
            self.pod_supply[mpn_id] += quantity
        else:
            self.pod_supply[mpn_id] = quantity
        self.__update_supply(mpn_id, quantity)

    def add_warehouse_supply(self, mpn_id: str, quantity: int):
        if quantity <= 0:
            return
        if mpn_id in self.warehouse_supply:
            self.warehouse_supply[mpn_id] += quantity
        else:
            self.warehouse_supply[mpn_id] = quantity
        self.__update_supply(mpn_id, quantity)

    def __update_supply(self, mpn_id, quantity: int):
        if mpn_id in self.supply:
            self.supply[mpn_id] += quantity
        else:
            self.supply[mpn_id] = quantity


def get_cdc_by_ship_to(cdcs: list[Cdc], ship_to: int):
    for cdc in cdcs:
        if ship_to == cdc.ship_to:
            return cdc
    return None
