from domain.mono.pos_allocation.entity.cdc import Cdc
from domain.mono.pos_allocation.entity.mpn import Mpn

ALLOCATE_RECORD_TYPE_INSTOCK = 1
ALLOCATE_RECORD_TYPE_WOI = 2
ALLOCATE_RECORD_TYPE_FIX = 3


class AllocateRecord:
    def __init__(self, mpn: Mpn, supplier_id: str, allocate_record_type: int, quantity: int):
        self.mpn = mpn
        self.supplier_id = supplier_id
        self.instock_quantity = 0
        self.fix_quantity = 0
        self.woi_quantity = 0
        self.allocate_record_type = allocate_record_type
        self.add(allocate_record_type, quantity)

    def __repr__(self):
        return f"AllocateRecord(mpn={self.mpn.id}, supplier_id={self.supplier_id}, fix_quantity={self.fix_quantity}, instock_quantity={self.instock_quantity}, woi_quantity={self.woi_quantity}"

    def add(self,allocate_record_type:int, quantity: int):
        if allocate_record_type == ALLOCATE_RECORD_TYPE_INSTOCK:
            self.instock_quantity += quantity
            return
        if allocate_record_type == ALLOCATE_RECORD_TYPE_WOI:
            self.woi_quantity += quantity
            return
        if allocate_record_type == ALLOCATE_RECORD_TYPE_FIX:
            self.fix_quantity += quantity
        return
