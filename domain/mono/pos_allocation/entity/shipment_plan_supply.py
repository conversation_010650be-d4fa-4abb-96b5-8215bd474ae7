import logging

from domain.mono.pos_allocation.entity.mpn import Mpn
from domain.mono.pos_allocation.entity.mpn_info import MpnInfo


class ShipmentPlanSupplier:
    def __init__(self, sold_to_id: int) -> None:
        self.sold_to_id = sold_to_id

        self.origin_supply = {}  # key: mpn_id, value: supply_amount
        self.supply = {}  # 总供应

    def __repr__(self):
        return (
            f"Shipment Plan(sold_to_id={self.sold_to_id}, \n     origin_supply={self.origin_supply},"
            f"\n          supply={self.supply})")

    def serialize_for_df(self, cdcs: list, mpns) -> list:
        ret = []
        for mpn_id, quantity in self.supply.items():
            mpn = None
            for m in mpns:
                if m.id == mpn_id:
                    mpn = m
            ret.append({
                'sold_to': self.sold_to_id,
                'mpn': mpn_id,
                'lob': mpn.lob if mpn is not None else None,
                'sub_lob': mpn.sub_lob if mpn is not None else None,
                'remaining': quantity,
                'origin_supply': self.origin_supply.get(mpn_id, 0),
                'allocated': self.origin_supply.get(mpn_id, 0) - quantity,
            })
        return ret

    def get_origin_supply(self, mpn: Mpn) -> int:
        quantity = 0
        if mpn.id in self.origin_supply:
            quantity += self.origin_supply[mpn.id]
        return quantity

    def get_current_supply(self, mpn: Mpn) -> int:
        quantity = 0
        if mpn.id in self.supply:
            quantity += self.supply[mpn.id]
        return quantity

    def allocate(self, mpn: Mpn, quantity: int):
        remaining = self.get_current_supply(mpn)
        if remaining == 0:
            return 0
        if remaining >= quantity:
            self.supply[mpn.id] -= quantity  # 从supply中减去对应量
            return quantity
        else:
            msg = f"Insufficient quantity {quantity} of {mpn.id} in {self.sold_to}'s stock {self.supply[mpn.id]}."
            logging.error(msg)
            self.supply[mpn.id] = 0
            return remaining

    def has_supply(self, mpn: Mpn) -> bool:
        if mpn.id in self.supply and self.supply[mpn.id] > 0:
            return True
        return False

    def add_supply(self, mpn_id: str, quantity: int):
        if quantity <= 0:
            return
        if mpn_id in self.origin_supply:
            self.origin_supply[mpn_id] += quantity
        else:
            self.origin_supply[mpn_id] = quantity
        self.__update_supply(mpn_id, quantity)

    def __update_supply(self, mpn_id, quantity: int):
        if mpn_id in self.supply:
            self.supply[mpn_id] += quantity
        else:
            self.supply[mpn_id] = quantity

    def serialize_for_mono_df(self, npp_mpns: list[Mpn], hq_info, plan_id: int, mpn_infos: list[MpnInfo]) -> list:
        ret = []
        for mpn in npp_mpns:
            # plan_id, rtm, customer_sold_to_id, customer_sold_to_name, lob, model, project_code, mpn, mpn_description, supply, ttl_pos_allocation, remaining_supply
            mpn_info = next((x for x in mpn_infos if x.id == mpn.id), None)
            ret.append({
                'plan_id': plan_id,
                'rtm': hq_info.sub_rtm,
                'customer_sold_to_id': str(hq_info.sold_to_id).zfill(10) if hq_info is not None else self.sold_to_id,
                'customer_sold_to_name': hq_info.supplier_name,
                'lob': mpn.lob if not mpn.is_mac() else 'CPU',
                'model': mpn.sub_lob,
                'project_code': mpn_info.project_code if mpn_info is not None else None,
                'mpn': mpn.id,
                'mpn_description': mpn_info.description if mpn_info is not None else None,
                'supply': self.origin_supply.get(mpn.id, 0),
                'ttl_pos_allocation': self.origin_supply.get(mpn.id, 0) - self.supply.get(mpn.id, 0),
                'remaining_supply': self.supply.get(mpn.id, 0),
                'mpn_type': 'NPP',
                'is_online': 0
            })
        return ret


def get_supplier_by_sold_to(suppliers: list[ShipmentPlanSupplier], sold_to: int):
    for supplier in suppliers:
        if sold_to == supplier.sold_to_id:
            return supplier
    return None
