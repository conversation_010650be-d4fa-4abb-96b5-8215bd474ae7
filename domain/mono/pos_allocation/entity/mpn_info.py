from domain.supply.entity import Lob


class MpnInfo:

    def __init__(self, mpn_id: str, sold_to_id: str, sold_to_name: str, lob: str, sub_lob: str, project_code: str, mpn_description: str):
        self.id = mpn_id
        self.sold_to_id = sold_to_id
        self.sold_to_name = sold_to_name
        self.lob = lob
        self.sub_lob = sub_lob
        self.project_code = project_code
        self.description = mpn_description

    def is_mac(self)->bool:
        return self.lob == Lob.MAC