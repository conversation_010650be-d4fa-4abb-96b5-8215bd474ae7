class PosEoh:
    def __init__(self, pos_id: str, lob: str, mpn: str, eoh: int=0):
        self.pos_id = pos_id
        self.lob = lob
        self.mpn = mpn

        self.eoh = eoh
        self.ml_fcst = 0
        self.sell_out_five_week_avg = None

    def set_eoh(self, eoh:int):
        self.eoh = eoh
        
    def set_ml_fcst(self, ml_fcst: int):
        self.ml_fcst = ml_fcst

    def set_sell_out_five_week_avg(self, last_five_week_avg: float):
        self.sell_out_five_week_avg = last_five_week_avg
