import logging
import math

from domain.mono.pos_allocation.entity.allocate_record import ALLOCATE_RECORD_TYPE_WOI
from domain.mono.pos_allocation.entity.cdc import Cdc
from domain.mono.pos_allocation.entity.mpn import Mpn
from domain.mono.pos_allocation.entity.shipment_plan_supply import ShipmentPlanSupplier


class WoiAllocation:
    def __init__(self):
        pass

    # 一轮分货
    def woi_allocation(self, active_poses: [], supplier: ShipmentPlanSupplier, mpn: Mpn):
        # 只要supplier下有剩余库存，且有pos还需要补货就继续补
        while supplier.has_supply(mpn) and len(active_poses) > 0:
            # 已达目标，无需分货。注意这里千万不要一边遍历，一边删除
            active_poses = [pos for pos in active_poses if pos.woi_gap(mpn) >0]
            if len(active_poses) == 0:
                break
            # 取这些pos下woi最小的pos
            # for pos in active_poses:
            #     print("pos_list:::::", pos)
            sorted_active_poses = sorted(active_poses, key=lambda x: x.woi(mpn))
            sorted_active_poses[0].allocate_from_shipment_plan(supplier, mpn, ALLOCATE_RECORD_TYPE_WOI, 1)


def get_twoi_fcst(fcsts: list, twoi: float):
    if not fcsts or twoi <= 0:
        return 0  # 无效输入处理
    if len(fcsts) < math.ceil(twoi):
        total = sum(fcsts)
    else:
        # 使用 math.modf 获取小数部分和整数部分
        decimal_part, integer_part = math.modf(twoi)
        integer_part = int(integer_part)
        # 两位小数点的整数部分
        total = sum(fcsts[:integer_part])
        # 有小数点部分
        if decimal_part > 0:
            total += fcsts[integer_part] * decimal_part
    total = round(total, 3)  # 避免浮点数精度问题
    return total



