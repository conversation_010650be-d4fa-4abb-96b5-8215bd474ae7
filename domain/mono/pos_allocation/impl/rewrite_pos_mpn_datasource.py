import datetime
import json
import logging

from data.databend.mono.pos_mpn_datasource import PosMpnDatasource
from data.fiscal_year_week import FiscalYearWeek
from data.mysqls.mono.ds_pos_eoh import DsPosEoh
from data.mysqls.mono.excel_operate_log import ExcelOperateLog
from data.mysqls.mono.high_runner_for_fd import HighRunnerForFd

from data.mysqls.mono.pos_list_all import PosListAll

from domain.mono.pos_allocation.entity.mpn import Mpn
from domain.mono.pos_allocation.entity.pos import Pos
from util.const import ErrorExcept, ErrCode
from util.util import take_time


class RewritePosMpnDatasource:
    def __init__(self, fiscal_week: str, pos_list=None, mpns=None, stocks=None):
        self.fiscal_week = fiscal_week
        # 需要先加载mpn
        if mpns is None:
            self._load_mpns()
            # self.mpns = [mpn for mpn in self.mpns if mpn.id == "MRXQ3CH/A"]
            # self.mpns = self.mpns[:2]
        else:
            self.mpns = mpns

        if pos_list is None:
            self._load_poses()
            # self.pos_list = [pos for pos in self.pos_list if pos.pos_id==3468396]
            # self.pos_list = self.pos_list[:3]
        else:
            self.pos_list = pos_list

        if stocks is None:
            self._load_stocks()
            # self.stocks = self.stocks[:3]
        else:
            self.stocks = stocks

    def rewrite(self):
        create_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        batch_step = 10
        for i in range(0, len(self.mpns), batch_step):
            mpns = self.mpns[i:i+batch_step]
            records = []
            for mpn in mpns:
                logging.info(f"rewrite begin handle mpn:{mpn.id}")
                mpn_pos_records = self._get_mpn_pos_records(mpn, create_time)
                records.extend(mpn_pos_records)
            self._save_to_db(records)
            logging.info(f"rewrite begin handle mpn, successfully!")

    def eoh_list_by_mpn(self, mpn: Mpn) -> list:
        return [item for item in self.stocks if item.mpn == mpn.id]

    @take_time
    def _get_mpn_pos_records(self, mpn: Mpn, create_time: str) -> list:
        mpn_pos_records = []
        if not self.pos_list:
            logging.info(f'_get_mpn_pos_records pos list empty')
            return mpn_pos_records

        pos_eohes = self.eoh_list_by_mpn(mpn)
        def get_eoh(pos_eohes, pos: Pos):
            for item in pos_eohes:
                if pos.pos_id == item.pos_id:
                    return item.eoh, item.sell_out_five_week_avg
            return None, None

        for pos in self.pos_list:
            lw_eoh, sell_out_five_week_avg = get_eoh(pos_eohes, pos)
            tmp_item = {
                "week_date": self.fiscal_week,
                "apple_id": pos.pos_id,
                "prod_id": mpn.id,
                "customer_sold_to_id": pos.customer_sold_to_id,
                "high_low_runner": pos.high_low_runner(mpn),
                "npp_reseller": pos.npp_reseller(mpn),
                "reseller_npp_type": pos.npp_type,
                "customer_sold_to_id_origin": None,
                "create_time": create_time,
                "lw_eoh": lw_eoh,
                "sell_out_five_week_avg": sell_out_five_week_avg
            }

            # tmp_display_info_item = {
            #     "pos_name": pos.pos_name,
            #     "reseller_name": pos.supplier_name,
            #     "t2_reseller_name": pos.t2_reseller_name(),
            #     "reseller_name_origin": None,
            #     "hq_id": pos.supplier_hq_id,
            #     "t2_hq_id": pos.t2_hq_id(),
            # }
            tmp_item['extra'] = None
            tmp_item['display_info'] = "{}" # json.dumps(tmp_display_info_item)
            mpn_pos_records.append(tmp_item)
        return mpn_pos_records

    @staticmethod
    def _save_to_db(mpn_pos_records: list):
        PosMpnDatasource.bulk_save(mpn_pos_records)

    def _load_mpns(self):
        version = ExcelOperateLog.query_max_version("excelHighRunnerForFD")
        if version is None:
            return []  # todo 告警
        npp_mpn_list = HighRunnerForFd.query_all(version_id=version)
        print(npp_mpn_list)
        self.mpns = npp_mpn_list

    def _load_poses(self):
        pos_list_all_version = ExcelOperateLog.query_max_version("excelPosListAll")
        reseller_version = ExcelOperateLog.query_max_version("excelResellerMapping")
        if pos_list_all_version is None or reseller_version is None:
            raise Exception("No pos list all version or reseller version!")
        pos_list = PosListAll.query_all_by_version(pos_list_all_version, reseller_version)
        print(pos_list)
        self.pos_list = pos_list

    def _load_stocks(self):
        # 要拿上一周的fiscal_week_year去查stock
        current_dt = datetime.datetime.now()
        last_week_dt_str = (current_dt - datetime.timedelta(days=7)).strftime('%Y-%m-%d')

        fiscal_obj = FiscalYearWeek.get_fis_by_date(date=last_week_dt_str)
        if fiscal_obj is None:
            raise ErrorExcept(ErrCode.Param, "can't find fiscal day")
        fiscal_week_year = fiscal_obj.fiscal_week_year
        stocks = DsPosEoh.query_stock_by_fiscal_week_year(fiscal_week_year=fiscal_week_year)
        self.stocks = stocks
