import io

import pandas as pd

from data.mysqls.mono.mono_pos_allocation_result import MonoPosAllocationResult

MONO_POS_ALLOCATION_WEEK_RESULT_SHEET_NAME = "Sheet1"


class MonoPosAllocationFileResult:
    def __init__(self, fiscal_week: str):
        self.fiscal_week = fiscal_week

    def __is_result(self) -> bool:
        return True

    def file_info(self) -> dict:
        return {
            "file_name": f"mono_allocation_{self.fiscal_week}_pos_list.xlsx"
        }

    def _column_mapping(self):
        return {
            "sold_to_id": "sold_to_id",
            "sold_to_name": "sold_to_name",
            "pos_id": "pos_id",
            "pos_name": "pos_name",
            "category": "category",
            "district_group": "district_group",
            "location_type": "location_type",
            "POS type": "pos_type",
            "LOB": "lob",
            "cdc1": "cdc1",
            "cdc2": "cdc2",
            "cdc3": "cdc3",
            "cdc4": "cdc4",
            "cdc5": "cdc5",
            "cdc6": "cdc6",
            "Apple Part #": "mpn_id",
            "Ship to ID": "ship_to_id",
            "Sustaining/NPI": "sustaining_or_npi",
            "eoh": "eoh",
            "twoi_fcst": "twoi_fcst",
            "total_inv": "total_inv",
            "npp_min_inventory": "npp_min_inventory",
            "特殊补货": "fix_quantity",
            "0库存补货": "instock_quantity",
            "woi补货": "woi_quantity",
        }

    def _get_download_result(self) -> pd.DataFrame:
        pos_allocation_df = MonoPosAllocationResult.get_dataframe_result(self.fiscal_week)
        return self._spilt_cdcs(pos_allocation_df)

    def _spilt_cdcs(self, pos_allocation_dataframe: pd.DataFrame) -> pd.DataFrame:
        # 使用 str.split 方法将 'cdcs' 列拆分为多个列
        split_tags = pos_allocation_dataframe['cdcs'].str.split(',', expand=True)
        # 给拆分后的列命名（可以根据实际情况调整）
        split_tags.columns = [f'cdc{i + 1}' for i in range(split_tags.shape[1])]
        # 将拆分后的列与原始 DataFrame 合并
        df = pd.concat([pos_allocation_dataframe, split_tags], axis=1)
        # 如果不需要原始的 'cdcs' 列，将其删除
        df = df.drop(columns=['cdcs'])
        return df

    def get_dataframe(self):
        db_columns = list(self._column_mapping().values())
        display_columns = list(self._column_mapping().keys())
        df = self._get_download_result()
        if df.empty:
            raise FileNotFoundError(
                f'mono pos allocation result no data, allocation_week: {self.fiscal_week}')
        df = df[db_columns]
        df.columns = display_columns
        return df

    def download(self):
        df = self.get_dataframe()
        # 返回文件流，不在系统中创建文件
        excel_file_bytes = io.BytesIO()
        df.to_excel(excel_file_bytes, sheet_name=MONO_POS_ALLOCATION_WEEK_RESULT_SHEET_NAME, index=False)
        excel_file_bytes.seek(0)
        return excel_file_bytes
