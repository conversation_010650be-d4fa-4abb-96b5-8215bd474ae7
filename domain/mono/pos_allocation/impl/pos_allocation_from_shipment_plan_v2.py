import logging
import traceback
import uuid
from datetime import datetime
from enum import Enum

import numpy as np
import pandas as pd

from data.databend.mono.ds_cdc_warehouse_inv import DsCdcWarehouseInv
from data.databend.mono.ds_pos_eoh import DsPosEoh
from data.mysqls.mono.excel_operate_log import ExcelOperateLog
from data.mysqls.mono.file_info import FileInfo
from data.mysqls.mono.forecast_origin_data import ForecastOriginData
from data.mysqls.mono.high_runner import HighRunner
from data.mysqls.mono.mono_pos_allocation_result import MonoPosAllocationResult
from data.mysqls.mono.npp_self_supply_stop_list import ExcelNppSelfSupplyStopList
from data.mysqls.mono.pos_allocation_result import PosAllocationResult
from data.mysqls.mono.pos_allocation_result_excel import PosAllocationResultExcel
from data.mysqls.mono.pos_list_all import PosListAll
from data.mysqls.mono.pos_npp_self_setting import PosNppSelfSetting
from data.mysqls.mono.pos_npp_self_special_supply import PosNppSelfSpecialSupply
from data.mysqls.mono.pos_npp_self_supply import PosNppSelfSupply
from data.mysqls.mono.pos_plan import PosPlan
from domain.mono.pos_allocation.entity.allocate_record import ALLOCATE_RECORD_TYPE_INSTOCK, ALLOCATE_RECORD_TYPE_FIX
from domain.mono.pos_allocation.entity.mpn import Mpn
from domain.mono.pos_allocation.entity.pos import Pos
from domain.mono.pos_allocation.impl.woi_allocation import WoiAllocation, get_twoi_fcst
from util.conf import logger
from util.file_util import get_absolute_path


# 文件保存本地后, 数据库中的唯一健
class FileBusKeyEnum(Enum):
    ALLOCATION_POS_LIST_RECORD = "excel_pos_allocation_pos_list_record_file_planId"
    ALLOCATION_SUPPLY_LIST_RECORD = "excel_pos_allocation_supply_list_record_file_planId"
    ALLOCATION_SUPPLY_KUAI_POS_RESULT = "excel_pos_allocation_kuai_pos_result_file_planId"
    ALLOCATION_SUPPLY_TIANYIN_POS_RESULT = "excel_pos_allocation_tianyin_pos_result_file_planId"
    ALLOCATION_SUPPLY_KUAI_RESULT = "excel_pos_allocation_kuai_result_file_planId"
    ALLOCATION_SUPPLY_TIANYIN_RESULT = "excel_pos_allocation_tianyin_result_file_planId"
    ALLOCATION_SUPPLY_POS_RESULT = "excel_pos_allocation_by_pos_by_mpn_by_sold_to_result_file_planId"
    ALLOCATION_SUPPLY_RESULT = "excel_pos_allocation_by_mpn_by_sold_to_result_file_planId"


# 更新状态，通知java去触发定时任务
class PosNppSelfSettingStatusEnum(Enum):
    ON_READY = 0
    READY = 1


# 文件下载的不同纬度(用于同一个接口, 下载不同的文件)
class AllocationResultLatitudeEnum(Enum):
    SHIP_TO_POS_MPN = "ship_to_pos_mpn"
    SHIP_TO_MPN = "ship_to_mpn"


# 用于文件保存本地后, 存入数据库中的文件信息的相关处理
class FileHandler:
    # 默认mono pos分货后的文件存储路径
    folder_path = '/uploads/fast_allocation_mono/'

    @classmethod
    def get_file_info(cls, display_file_name: str = None, folder_path: str = None):
        unique_key = uuid.uuid4().hex
        unique_filename = f"{unique_key}.xlsx"

        if folder_path is not None:
            cls.folder_path = folder_path
        absolute_file_path = get_absolute_path(cls.folder_path) + unique_filename
        relative_file_path = cls.folder_path + unique_filename

        return {
            "absolute_file_path": absolute_file_path,    # 绝对路径
            "relative_file_path": relative_file_path,    # 相对路径
            "display_file_name": display_file_name,     # 数据库中用于前端展示使用的文件名称(暂时用不上)
            "unique_key": unique_key,
            "unique_filename": unique_filename,
            "folder_path": cls.folder_path             # 默认mono pos分货后的文件存储路径
        }

    @classmethod
    def save_excel(cls, data_df: pd.DataFrame, file_path: str):
        data_df.to_excel(file_path, index=False)


class PosAllocationFromShipmentPlan:
    def __init__(self,
                 fiscal_week: str,
                 plan_id: int,
                 pos_list=None,
                 supplies=None,
                 mpns=None,
                 hq_infos=None,
                 mpn_infos=None,
                 pos_special_supply=None):
        self.fiscal_week = fiscal_week
        self.plan_id = plan_id

        # 不需要分货的mpn
        self.no_allocate_mpns = []
        # self.no_allocate_mpns = ["MYTQ3CH/A","MYTR3CH/A","MYTT3CH/A","MYTW3CH/A","MYW13CH/A","MYLN3CH/A","MYLP3CH/A",
        #                          "MYLR3CH/A","MYLW3CH/A","MYLX3CH/A","MYLY3CH/A","MYM23CH/A","MYM73CH/A"]

        # 需要先加载mpn（cdc数据依赖这个）
        if mpns is None:
            self._load_mpns()
            # self.mpns = [mpn for mpn in self.mpns if mpn.id == 'MRXQ3CH/A']
        else:
            self.mpns = mpns

        # if hq_infos is None:
        #     self._load_hq_infos()
        # else:
        #     self.hq_infos = hq_infos
        if supplies is None or mpn_infos is None:
            self._load_shipment_supplies()
        else:
            self.supplies = supplies
            self.mpn_infos = mpn_infos

        if pos_list is None:
            self._load_poses()
            # self.pos_list = [pos for pos in self.pos_list if pos.pos_id==3468396]
        else:
            self.pos_list = pos_list


        # 特殊分货
        if pos_special_supply is None:
            self._load_pos_special_supply()
        else:
            self.pos_special_supply = pos_special_supply

        # by model pos停货
        self.stops = self._load_stops()

        # 加载所有的cdc
        self.cdcs = DsCdcWarehouseInv.query_by_week(self.fiscal_week, self.mpns)

    def is_stop(self, mpn: Mpn, pos: Pos) -> bool:
        for stop_item in self.stops:
            if str(stop_item.pos_id) == str(pos.pos_id) and str.lower(stop_item.sub_lob) == str.lower(mpn.sub_lob):
                pos.add_stop_sublob(str.lower(stop_item.sub_lob))
                return True
        return False

    def __before_allocate(self):
        # 重新计算时先把之前数据情况在计算
        MonoPosAllocationResult.remove_data_by_plan_id(self.plan_id)
        # 更新状态，通知java去触发定时任务, 用于重新触发计算
        PosNppSelfSetting.update_status_by_plan_id(plan_id=self.plan_id, status=PosNppSelfSettingStatusEnum.ON_READY.value)

    def allocate(self):
        self.__before_allocate()
        logging.info(f"npp::allocation, total mpn: {len(self.mpns)}, plan_id: {self.plan_id}")
        for mpn in self.mpns:
            logging.info(f"npp::allocation, begin handle mpn:{mpn.id}")
            # 特殊分货
            self.allocate_fix(mpn)

            # 0库存补货
            self.allocate_instock(mpn)

            # woi补货
            self.allocate_woi(mpn)

            self._save_to_db([mpn])
        logging.info(f"npp::allocation, plan_id: {self.plan_id}, handle mpn successfully!")

        self.save_to_mono_db()

        # 生成最终报告
        self.get_allocation_report_data()

    def allocate_instock(self, mpn):
        # 补完stock=1的pos后补stock=2的
        in_stock_targets = [1, 2]
        for in_stock in in_stock_targets:
            if mpn.npp_min_inventory < in_stock:
                continue
            self.allocate_instock_for_each_mpn(mpn, in_stock)

    def allocate_instock_for_each_mpn(self, mpn: Mpn, in_stock_target: int):
        # todo 后续pos eoh 会按照周来查询, 暂时先写死一周
        # fiscal_week_year = FiscalYearWeek.get_fiscal_week_year_by_fiscal_week(fiscal_week=self.fiscal_week)
        pos_eoh_list = DsPosEoh.query_pos_eoh([mpn], self.pos_list)
        # if len(pos_eoh_list) == 0:
        #     raise Exception(f"no pos eoh: mpn: {mpn.id}")
        for pos in self.pos_list:
            eoh = 0
            eoh_list = [item.eoh for item in pos_eoh_list if
                        item.pos_id == str(pos.pos_id) and item.mpn == mpn.id]
            if len(eoh_list) > 0:
                eoh = eoh_list[0]
            pos.set_eoh({mpn.id: eoh})

        for supplier in self.supplies:
            # 是否有剩余supply
            if not supplier.has_supply(mpn):
                continue
            # 给对应pos补货
            for pos in self.pos_list:
                if str(pos.sold_to_id) != str(supplier.sold_to_id):
                    continue

                # 停店不参与普通分货
                if not pos.is_active() or self.is_stop(mpn, pos):
                    continue

                # 已达目标，无需分货
                cur_eoh = pos.cur_eoh(mpn)
                if (cur_eoh >= pos.npp_min_inventory_target(mpn)  # 街边店不参与0库存补货
                        or cur_eoh >= in_stock_target):
                    continue

                pos.allocate_from_shipment_plan(supplier, mpn, ALLOCATE_RECORD_TYPE_INSTOCK, 1)  # 补stock，一次只补1台

    def allocate_woi(self, mpn: Mpn, ml_fcsts=None):
        ml_fcsts_dict = {}
        if ml_fcsts == None:
            ml_fcsts = ForecastOriginData.query_pos_mpn_fcst([mpn], self.pos_list)
            ml_fcsts_dict = dict([(fcst.pos_id, fcst) for fcst in ml_fcsts])
        for pos in self.pos_list:
            pos_fcst = ml_fcsts_dict.get(pos.pos_id, None)
            if pos_fcst is None:
                logging.error(f"no ml fcst for pos {pos.pos_id} for mpn {mpn.id}")
                continue
            woi_fcst = get_twoi_fcst([getattr(pos_fcst, f"prediction_wk{i}") for i in range(1, 14)], mpn.twoi)
            pos.set_twoi_fcst({mpn.id: woi_fcst})
            # for ml_fcst in ml_fcsts_dict:
            #     if pos.pos_id == ml_fcst.pos_id:
            #         ml_list = [getattr(ml_fcst, f"prediction_wk{i}") for i in range(1, 14)]
            #         woi_fcst = get_twoi_fcst(ml_list, mpn.twoi)
            #         pos.set_twoi_fcst({mpn.id: woi_fcst})
        for supplier in self.supplies:
            # 是否有剩余supply
            if not supplier.has_supply(mpn):
                continue

            active_poses = []
            for pos in self.pos_list:
                # 停店不参与普通分货
                if not pos.is_active() or self.is_stop(mpn, pos):
                    continue
                # 取对应的这些pos
                if pos.sold_to_id != str(supplier.sold_to_id):
                    continue

                # 缺少fcst预测数据的不参与！
                if pos.twoi_fcst.get(mpn.id, None) is None:
                    logging.error(f"no ml fcst for pos {pos.pos_id} for mpn {mpn.id}")
                    continue
                active_poses.append(pos)

            WoiAllocation().woi_allocation(active_poses, supplier, mpn)

    def _save_to_db(self, mpns: list[Mpn]):
        # 将mpn分货结果存储到数据库中
        result = []
        for item in self.pos_list.copy():
            result.extend(item.serialize_for_df(self.cdcs, mpns))
        result_df = pd.DataFrame(result)
        result_df.columns = [
            'hq_id',
            'reseller_name',
            'sold_to_id',
            'sold_to_name',
            'pos_id',
            'pos_name',
            'category',
            'district_group',
            'location_type',
            'pos_type',
            'lob',
            'sub_lob',
            'cdcs',
            'mpn_id',
            'project_code',
            'description',
            'ship_to_id',
            'warehouse_name',
            'sustaining_or_npi',
            'eoh',
            'woi',
            'twoi_fcst',
            'total_inv',
            'npp_min_inventory',
            'fix_quantity',
            'instock_quantity',
            'woi_quantity'
        ]
        result_df["fiscal_week"] = self.fiscal_week
        result_df["plan_id"] = self.plan_id
        # 保存到数据库中
        result_df.replace({np.nan: None}, inplace=True)
        fields_to_update = ['hq_id', 'reseller_name', 'sold_to_id', 'sold_to_name', 'pos_name', 'category', 'district_group', 'location_type',
                            'pos_type', 'lob', 'sub_lob', 'cdcs', 'warehouse_name', 'sustaining_or_npi',
                            'eoh', 'woi', 'twoi_fcst', 'total_inv', 'npp_min_inventory', 'fix_quantity',
                            'instock_quantity', 'woi_quantity', 'project_code', 'description']
        MonoPosAllocationResult.batch_update_with_plan_id(result_df.to_dict("records"), fields_to_update)

    def _load_mpns(self):
        version = ExcelOperateLog.query_max_version("excelHighRunner")
        if version is None:
            return []  # todo 告警
        npp_mpn_list = HighRunner.query_npp(version_id=version)
        # 过滤掉某些不需要分货的mpn
        npp_mpn_list = [mpn for mpn in npp_mpn_list if mpn.id not in self.no_allocate_mpns]
        print(npp_mpn_list)
        self.mpns = npp_mpn_list

    def _load_hq_infos(self):
        pos_list_all_version = ExcelOperateLog.query_max_version("excelPosListAll")
        reseller_version = ExcelOperateLog.query_max_version("excelResellerMapping")
        if pos_list_all_version is None or reseller_version is None:
            raise Exception("No pos list all version or reseller version!")
        hq_infos = PosListAll.query_by_version(pos_list_all_version, reseller_version)
        print(hq_infos)
        self.hq_infos = hq_infos

    def _load_plan_id(self):
        ret = PosPlan.subquery_plan_id_no_session(self.fiscal_week)
        if ret:
            self.plan_id = ret[0].id

    def _load_poses(self):
        pos_list_all_version = ExcelOperateLog.query_max_version("excelPosListAll")
        reseller_version = ExcelOperateLog.query_max_version("excelResellerMapping")
        if pos_list_all_version is None or reseller_version is None:
            raise Exception("No pos list all version or reseller version!")
        pos_list = PosListAll.query_pos_hq_info_by_pos_version_reseller_version(pos_list_all_version, reseller_version)
        if len(pos_list) == 0:
            logging.error(f"Note: No pos list for {self.fiscal_week}, pos_version: {pos_list_all_version}, reseller_version: {reseller_version}!")
        self.pos_list = pos_list

    def _load_shipment_supplies(self):
        shipment_supplies, mpn_infos = PosNppSelfSupply.query_supply_by_plan_id(self.plan_id)
        self.supplies = shipment_supplies
        self.mpn_infos = mpn_infos
        for mpn_info_with_project_code_and_desc in mpn_infos:
            for mpn in self.mpns:
                if mpn.id == mpn_info_with_project_code_and_desc.id:
                    mpn.set_project_code(mpn_info_with_project_code_and_desc.project_code)
                    mpn.set_description(mpn_info_with_project_code_and_desc.description)

    def _standardize_allocation_records(self, data_list: list) -> list:
        data = []
        for item in data_list:
            data.extend(item.serialize_for_df(self.cdcs, self.mpns))
        return data

    def _save_to_excel(self, data: list, bus_key: str, display_file_name: str):
        # 将字典列表转换为Pandas DataFrame
        df = pd.DataFrame(data)
        file_info = FileHandler.get_file_info(display_file_name=display_file_name)
        FileHandler.save_excel(data_df=df, file_path=file_info.get("absolute_file_path"))
        # 保存文件信息到数据库供下载使用
        now = datetime.now()
        db_file_info_dict = {
            "bus_key": bus_key,
            "file_name": file_info.get("display_file_name"),
            "file_path": file_info.get("relative_file_path"),
            "start_time": None,
            "create_time": now,
            "status": None,
        }
        FileInfo.bulk_save([db_file_info_dict])

    def save_to_mono_db(self):
        result = []
        for item in self.supplies:
            # 从hq_infos里找到一个和supplier对应的sold to信息，如果匹配到多个sold to，只取一个
            hq_infos = [pos_info for pos_info in self.pos_list if int(pos_info.sold_to_id) == item.sold_to_id]
            if hq_infos:
                hq_info = hq_infos[0]
            else:
                hq_info = None
            result.extend(item.serialize_for_mono_df(self.mpns, hq_info, self.plan_id, self.mpn_infos))
        result_df = pd.DataFrame(result)
        # 保存到数据库中
        result_df.replace({np.nan: None}, inplace=True)
        PosAllocationResult.remove_offline_npp_data(self.plan_id)
        PosAllocationResult.bulk_save(result_df.to_dict("records"))

        # 保存pos allocation result excel数据
        result = []
        for item in self.pos_list:
            result.extend(item.serialize_for_mono_df(self.mpns, self.plan_id, self.mpn_infos))
        result_df = pd.DataFrame(result)

        # 保存到数据库中
        result_df.replace({np.nan: None}, inplace=True)
        PosAllocationResultExcel.remove_offline_npp_data(self.plan_id)
        PosAllocationResultExcel.bulk_save(result_df.to_dict("records"))

    def is_stop_by_pos_model(self, sub_lob: str, pos_id: str) -> bool:
        for stop_item in self.stops:
            if stop_item.pos_id == str(pos_id) and stop_item.sub_lob == sub_lob:
                return True
        return False

    def _load_stops(self):
        version = ExcelOperateLog.query_max_version("excelNppSelfSupplyStopList")
        if version is None:
            logger.info("npp::allocation, excelNppSelfSupplyStopList no version")
            return []  # todo 告警
        ret = ExcelNppSelfSupplyStopList.query_stop_records(version_id=version, fiscal_week=self.fiscal_week)
        return ret

    def _load_pos_special_supply(self):
        pos_special_supply_list = PosNppSelfSpecialSupply.get_special_supply_record(self.plan_id)
        self.pos_special_supply = pos_special_supply_list

    def allocate_fix(self, mpn):
        for supplier in self.supplies:
            # 是否有剩余supply
            if not supplier.has_supply(mpn):
                continue
            # 给对应pos补货
            for pos in self.pos_list:
                if pos.sold_to_id != str(supplier.sold_to_id):
                    continue

                # 查询对应sold_to、pos、mpn下的特殊分货
                # todo 假如有多个supply对同一个pos、mpn供货, 那这里的判断就有问题了
                pos_special_record = [item for item in self.pos_special_supply if item.pos_id == pos.pos_id and item.mpn == mpn.id]
                # 无特殊分货
                if not pos_special_record:
                    continue

                # 如果存在同一个sold_to、同一个pos、同一个mpn多条件目前取的是第一条
                pos_special_quantity = pos_special_record[0].quantity

                # 直接分货
                pos.allocate_from_shipment_plan(supplier, mpn, ALLOCATE_RECORD_TYPE_FIX, pos_special_quantity)

    def high_runner_and_npp_report(self, supplier_name: str,
                                   pos_ship_to_mapping: pd.DataFrame,
                                   ship_to_warehouse_name_df: pd.DataFrame):

        # 查询Mono high runner 的分货数据
        hr_allocation = PosAllocationResultExcel.get_dataframe_result_by_plan_id(
            self.plan_id, supplier_name)

        # 防止顺序错位(数据为空, left join会把pos_id的顺序放到后面)
        if hr_allocation.empty:
            hr_allocation["ship_to_id"] = None
            hr_allocation["warehouse_name"] = None
        else:
            # 给Mono high runner数据增加ship_to_id
            hr_allocation = hr_allocation.merge(
                pos_ship_to_mapping, on=["pos_id"], how="left")

            # 给Mono high runner数据增加warehouse_name
            hr_allocation = hr_allocation.merge(
                ship_to_warehouse_name_df, on=["ship_to_id"], how="left")

        hr_allocation["customer_sold_to_id"] = hr_allocation["customer_sold_to_id"].astype(int).astype(str)

        # 查询npp分货
        npp_allocation = MonoPosAllocationResult.get_dataframe_by_plan_id_sold_to_name(self.plan_id, supplier_name)

        # 统一成 high runner 的headers
        npp_allocation.columns = hr_allocation.columns

        # group by
        agg_rules = {
            "pos_status": "first",
            "rtm": "first",
            "supplier_name": "first",
            "customer_sold_to_id": "first",
            "hq_id": "first",
            "reseller_name": "first",
            "pos_id": "first",
            "pos_name": "first",
            "pos_type": "first",
            "stop_flag": "first",
            "lob": "first",
            "project_code": "first",
            "model": "first",
            "mpn": "first",
            "description": "first",
            "mpn_type": "first",
            "special_distribute_num": "sum",
            "zero_inventory_replenishment_num": "sum",
            "twoi_distribute_num": "sum",
            "final_num": "sum",
            "ship_to_id": "first",
            "warehouse_name": "first"
        }
        groupby_columns = ["customer_sold_to_id", "ship_to_id", "mpn"]
        group_hr_allocation = hr_allocation.groupby(groupby_columns).agg(agg_rules)
        group_npp_allocation = npp_allocation.groupby(
            groupby_columns).agg(agg_rules)

        # 组合high runner 和 npp 分货数据
        df = pd.concat([hr_allocation, npp_allocation])
        df_by_sold_to_ship_to_mpn = pd.concat([group_hr_allocation, group_npp_allocation])

        # 替换列名并生成对应的excel文件
        excel_headers = [
            "Update Category",
            "RTM",
            "Supplier Name",
            "Customer Sold To ID",
            "HQ ID",
            "Name of Reseller",
            "POS Apple ID",
            "Name of POS",
            "POS type",
            "Stop Flag",
            "LOB",
            "Project Short Desc",
            "Model",
            "Apple Part #",
            "Description",
            "Sustaining/NPI",
            "特殊项目分货",
            "0库存补货",
            "TWOI分货",
            "最终分货",
            "Ship to ID",
            "Warehouse Name",
        ]
        df.columns = excel_headers

        # 这里需要替换下group by之后的headers，可能需要去掉pos相关的列
        df_by_sold_to_ship_to_mpn.columns = excel_headers
        group_columns = [
            "RTM",
            "Supplier Name",
            "Customer Sold To ID",
            "LOB",
            "Project Short Desc",
            "Model",
            "Apple Part #",
            "Description",
            "Sustaining/NPI",
            "特殊项目分货",
            "0库存补货",
            "TWOI分货",
            "最终分货",
            "Ship to ID",
            "Warehouse Name",
        ]
        df_by_sold_to_ship_to_mpn = df_by_sold_to_ship_to_mpn[group_columns]
        return df, df_by_sold_to_ship_to_mpn

    def get_pos_ship_to_mapping(self):
        # 查询pos和ship to mapping关系, 和第一顺位的cdc mapping
        pos_list_all_version = ExcelOperateLog.query_max_version("excelPosListAll")
        pos_ship_to_mapping = PosListAll.query_pos_ship_to_mapping(version_id=pos_list_all_version)

        # 查询warehouse_name
        ship_to_warehouse_name = DsCdcWarehouseInv.query_ship_to_warehouse_name(self.fiscal_week)

        # 判断下ship to 是否有多个warehouse name, 如果有的话，需要报错，基于下面原因，确认后再报吧。
        ''' 竟然有两家
        1382313  天音信息服务郑州配ECPP良品仓
        1382313      天音信息服务郑州配良品仓
        1567094            中恒顺丰云仓
        1567094           中恒金融星展仓
        '''
        # is_duplicated = ship_to_warehouse_name.duplicated(
        #     subset=["ship_to_id"], keep=False)
        # duplicated_rows = ship_to_warehouse_name[is_duplicated]
        # if not duplicated_rows.empty:
        #     raise Exception("Ship to has multiple warehouse name", duplicated_rows)

        return pos_ship_to_mapping, ship_to_warehouse_name

    def get_allocation_report_data(self):
        df_concat, df_group_concat = pd.DataFrame(), pd.DataFrame()
        pos_ship_to_mapping, ship_to_warehouse_name_df = self.get_pos_ship_to_mapping()

        # 获取solo_to_name
        suppler_name_list = ["酷爱", "天音", "恒沙", "海南聚时", "长虹佳华"]
        for suppler_name in suppler_name_list:
            df, df_group = self.high_runner_and_npp_report(suppler_name, pos_ship_to_mapping,
                                                           ship_to_warehouse_name_df)
            df_concat = pd.concat([df_concat, df])
            df_group_concat = pd.concat([df_group_concat, df_group])

        # 保存合并后文件
        file_name = f"POS_NPP_Self-supply_Allocation Details (Ship-to & POS & MPN)_{self.fiscal_week}.xlsx"
        file_name_group = f'POS_NPP_Self-supply_Allocation Details (Ship-to & MPN)_{self.fiscal_week}.xlsx'
        df_concat.replace({np.nan: None}, inplace=True)
        df_group_concat.replace({np.nan: None}, inplace=True)
        # 排序
        df_concat_sorted = df_concat.sort_values(by=['Ship to ID', 'POS Apple ID', 'Apple Part #'])
        df_group_concat_sorted = df_group_concat.sort_values(by=['Ship to ID','Apple Part #'])
        # 保存文件
        self._save_to_excel(data=df_concat_sorted.to_dict("records"), display_file_name=file_name, bus_key=f"{FileBusKeyEnum.ALLOCATION_SUPPLY_POS_RESULT.value}_{str(self.plan_id)}")
        self._save_to_excel(data=df_group_concat_sorted.to_dict("records"), display_file_name=file_name_group, bus_key=f"{FileBusKeyEnum.ALLOCATION_SUPPLY_RESULT.value}_{str(self.plan_id)}")

        # 更新状态，通知java去触发定时任务
        PosNppSelfSetting.update_status_by_plan_id(plan_id=self.plan_id, status=PosNppSelfSettingStatusEnum.READY.value)


def allocate_task_processor(fiscal_week: str, plan_id: int):
    try:
        """
        status=2: 计算中状态，用户提交计算任务，后台正在计算过程中时
        status=3: 待发布状态，计算任务产出，但用户未发布最终报告时
        status=5: error
        """
        logger.info(f'{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}, begin mono allocate, fiscal_week: {fiscal_week}, plan_id: {plan_id}')
        PosPlan.update_status_by_plan_id(plan_id=plan_id, status=2)
        pa = PosAllocationFromShipmentPlan(fiscal_week=fiscal_week, plan_id=plan_id)
        pa.allocate()
        logger.info(f'{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}, mono allocate successfully , fiscal_week: {fiscal_week}, plan_id: {plan_id}')
    except Exception as e:
        PosPlan.update_status_by_plan_id(plan_id=plan_id, status=5)
        logger.error(f'{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}, mono allocate failed, fiscal_week: {fiscal_week}, plan_id: {plan_id}, error: {traceback.format_exc()}')
    finally:
        logger.info(f'{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}, mono allocate exited, fiscal_week: {fiscal_week}, plan_id: {plan_id}')


def get_allocate_file_info(plan_id: int, group_by: str) -> tuple:
    if group_by == AllocationResultLatitudeEnum.SHIP_TO_POS_MPN.value:
        bus_key = f"{FileBusKeyEnum.ALLOCATION_SUPPLY_POS_RESULT.value}_{str(plan_id)}"
    else:
        bus_key = f"{FileBusKeyEnum.ALLOCATION_SUPPLY_RESULT.value}_{str(plan_id)}"

    file_info = FileInfo.query_file_info_by_bus_key(bus_key=bus_key)

    if not file_info:
        logger.error(f'pos allocation::mono::get_allocate_file_info file not found, plan_id: {plan_id}, group_by: {group_by}')
        raise FileNotFoundError(f'pos allocation::mono::get_allocate_file_info file not found, plan_id: {plan_id}, group_by: {group_by}')
    file_name = file_info.file_name

    split_path = file_info.file_path.split(FileHandler.folder_path)
    if len(split_path) == 2:
        absolute_path = get_absolute_path(FileHandler.folder_path)
        file_absolute_path = f"{absolute_path}{split_path[1]}"
    else:
        file_absolute_path = ""
    return file_name, file_absolute_path
