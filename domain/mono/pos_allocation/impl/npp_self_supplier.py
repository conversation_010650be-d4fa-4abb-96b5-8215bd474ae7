import os
from hashlib import md5

import pandas as pd

from service.datasource_service import columns_equals
from util.const import ErrorExcept, NppAllocationSupplyColumns, ErrCode, FileUploadError


# def file_save_multi_demand(file, uploader):
#     path = os.path.dirname(os.path.dirname(os.path.abspath(__file__))) + f'/uploads/'
#     dir_name = os.path.dirname(path)
#     if not os.path.exists(dir_name):
#         os.makedirs(dir_name)
#     if not os.path.exists(path):
#         os.mkdir(path)
#     file_md5 = md5(file.stream.read()).hexdigest()
#     file.stream.seek(0)
#     file.save(f'{path}/{file_md5}.xlsx')
#     # 文件验证
#     df = pd.read_excel(f'{path}/{file_md5}.xlsx')
#     # table header validate
#     if not columns_equals(NppAllocationSupplyColumns, df.columns):
#         raise ErrorExcept(ErrCode.FileUploadError, FileUploadError.NppAllocationSupplyColumnsError)
#     # business type validate
#     invalid_bts = set()
#     for bt in df["business_type"]:
#         if bt not in MultiBusinessTypeOrder:
#             invalid_bts.add(str(bt))
#     if len(invalid_bts) != 0:
#         raise ErrorExcept(ErrCode.FileUploadError, FileUploadError.BusinessTypeError + ", ".join(invalid_bts))
#     # mpn validate
#     all_mpn = DimFastModelSkuMapList.get_mpn_list_by_models(MODEL)
#     invalid_mpn = set()
#     for mpn in df["mpn_id"]:
#         if mpn not in all_mpn:
#             invalid_mpn.add(str(mpn))
#     if len(invalid_mpn) != 0:
#         raise ErrorExcept(ErrCode.FileUploadError, FileUploadError.MPNInvalid + ", ".join(invalid_mpn))
#     # label validate
#     invalid_label = []
#     for label in df["label"]:
#         if label not in ["High-runner", "Low-runner"]:
#             invalid_label.append(label)
#     if len(invalid_label) != 0:
#         raise ErrorExcept(ErrCode.FileUploadError, FileUploadError.WrongLabel)
#     # data validate
#     none_rows = df[df[["business_type", "mpn_id", "label"]].isnull().T.any()]
#     if len(none_rows) != 0:
#         raise ErrorExcept(ErrCode.FileUploadError,
#                           FileUploadError.MultiDemandBlankData + ", ".join(
#                               [str(x + 1) for x in none_rows.T.columns]))
#     duplicated = []
#     for bt in MultiBusinessTypeOrder:
#         bdf = df[df["business_type"] == bt]
#         duplicated.extend(bdf[bdf["mpn_id"].duplicated()].T.columns)
#     if len(duplicated) != 0:
#         raise ErrorExcept(ErrCode.FileUploadError,
#                           FileUploadError.BusinessTypeMPNDuplicate + ", ".join(str(x + 1) for x in duplicated))
#     # 文件信息保存
#     file_info = DataSourceFile()
#     file_info.rtm = StrRTMMulti
#     file_info.datasource_type = DataSourceFileType.Demand
#     file_info.filesize = file.content_length
#     file_info.content_type = file.content_type
#     file_info.upload_date = datetime.now().strftime(DateFormat)
#     file_info.upload_by = uploader
#     file_info.status = DataSourceFileStatus.Draft
#     file_info.url = f'/file/download/{file_md5}'
#     version = DataSourceFile.query_count_by_rtm_and_type(StrRTMMulti, DataSourceFileType.Demand)
#     file_info.version = version
#     today = datetime.now().strftime(DateFormat)
#     today_count = DataSourceFile.query_count_by_rtm_type_date(StrRTMMulti, DataSourceFileType.Demand, today)
#     if today_count == 0:
#         file_info.filename = f"{DataSourceFileType.Demand}_{today}.xlsx"
#     else:
#         file_info.filename = f"{DataSourceFileType.Demand}_{today} {today_count}.xlsx"
#     file_info.status = DataSourceFileStatus.Enabled
#     # file process
#     old_datas = OdsFastMultiDemand.query_by_version(version)
#     if len(old_datas) != 0:
#         raise ErrorExcept(ErrCode.DBInsert, "have same version data")
#     datas = []
#     for index, row in df.iterrows():
#         datas.append({
#             "business_type": row["business_type"],
#             "lob": row["lob"],
#             "sub_lob": row["sub_lob"],
#             "sku": row["sku"],
#             "mpn_id": row["mpn_id"],
#             "label": row["label"],
#             "version": version,
#         })
#     OdsFastMultiDemand.batch_save(datas)
#     file_info.save()
#     return {"file_id": file_md5}
