import logging

import pandas as pd
from data.databend.mono.ds_pos_eoh import DsPosEoh
from data.mysqls.mono.mono_forecast_result import MonoForecastResult
from data.mysqls.mono.mono_pos_allocation_result import MonoPosAllocationResult
from kit.save_as_file import  save_as_excel
from data.databend.mono.ds_cdc_pod_inv import DsCdcPodInv
from data.databend.mono.ds_cdc_warehouse_inv import DsCdcWarehouseInv
from data.databend.mono.ds_pos import DsPos
from data.mysqls.mono.excel_operate_log import ExcelOperateLog
from data.mysqls.mono.high_runner_for_fd import HighRunnerForFd
from domain.mono.pos_allocation.entity.cdc import Cdc, get_cdc_by_ship_to
from domain.mono.pos_allocation.entity.mpn import Mpn
from domain.mono.pos_allocation.entity.pos import Pos, sort_pos, CDC_ORDERS


def handle_each_mpn(pos_list: list[Pos], cdc_list: list[Cdc], mpn: Mpn, in_stock: int, fiscal_week: str):
    # todo 后续pos eoh 会按照周来查询, 暂时先写死一周
    pos_eoh_list = DsPosEoh.query_pos_eoh(202439, [mpn], pos_list)
    for cdc_order in CDC_ORDERS:
        for cdc in cdc_list:
            # cdc是否有剩余supply
            if not cdc.has_supply(mpn):
                continue
            # 给第n顺位为该cdc的pos补货
            for pos in pos_list:
                eoh = 0
                eoh_list = [item.eoh for item in pos_eoh_list if item.pos_id == str(pos.pos_id) and item.mpn == mpn.id]
                if len(eoh_list) > 0:
                    eoh = eoh_list[0]
                pos.set_eoh({mpn.id: eoh})
                if not pos.equal_cdc_order(cdc.ship_to, cdc_order):
                    continue
                # 停店不参与普通分货
                if not pos.is_active():
                    continue
                # 已达目标，无需分货
                if pos.in_stock_gap(mpn) <= 0:
                    continue

                pos.allocate_from_cdc(cdc, mpn, in_stock)  # 补stock=1
    # save_result_to_DB(fiscal_week, pos_list, [mpn])



def merge_pod_to_cdc(cdc_warehouse_list: list[Cdc], pod_list):
    # 合并CDC实际on-hand inventory+周一/周二预定到仓数量(POD)
    for pod in pod_list:
        cdc = get_cdc_by_ship_to(cdc_warehouse_list, pod["ship_to"])
        if cdc is None:
            cdc = Cdc(pod["ship_to"], pod["sold_to"])
            cdc_warehouse_list.append(cdc)
        cdc.add_pod_supply(pod["mpn"], pod["qty"])


def save_to_excel(data_list: list, npp_mpns:list[Mpn], file_name:str):
    data = []
    for item in data_list:
        data.extend(item.serialize_for_df(npp_mpns))
    # 将字典列表转换为Pandas DataFrame
    df = pd.DataFrame(data)
    path = f'/'
    file_name = f'{file_name}.xlsx'
    save_as_excel(df, path, file_name)


def allocate():
    # 加载数据
    fiscal_week = "FY24Q3W13"  # todo

    version = ExcelOperateLog.query_max_version("excelHighRunnerForFD")
    if version is None:
        return  # todo 告警
    npp_mpn_list = HighRunnerForFd.query_npp(version_id=version)

    pod_list = DsCdcPodInv.query_by_week(fiscal_week, npp_mpn_list)  # todo 合并eoh
    if len(pod_list) == 0:
        logging.info("No cdc pod inv for npp.")

    cdc_list = DsCdcWarehouseInv.query_by_week(fiscal_week, npp_mpn_list)
    if len(cdc_list) == 0:
        logging.info("No cdc warehouse inv for npp.")
    cdc_list = sorted(cdc_list, key=lambda x: x.ship_to)
    merge_pod_to_cdc(cdc_list, pod_list)

    pos_list = DsPos.query_by_fiscal_week(fiscal_week)
    sorted_pos_list = sort_pos(pos_list)
    if len(sorted_pos_list) ==0:
        logging.error(f"Note: No pos list for {fiscal_week}!")
    # 补完stock=1的pos后补stock=2的
    # 补stock==1
    in_stock = 1
    for mpn in npp_mpn_list:
        if mpn.npp_min_inventory == 0:
            continue
        if mpn.id != "MTLN3CH/A":
           continue

        handle_each_mpn(sorted_pos_list, cdc_list, mpn, in_stock, fiscal_week)

    in_stock = 2
    for mpn in npp_mpn_list:
        continue
        handle_each_mpn(pos_list, cdc_list, mpn, in_stock)

    save_to_excel(sorted_pos_list, npp_mpn_list,f"mono_allocation_{fiscal_week}_pos_list")
    save_to_excel(cdc_list, npp_mpn_list,f"mono_allocation_{fiscal_week}_cdc_list")

    print("cdc_list::")
    for cdc in cdc_list:
        continue
        print(cdc)
        print("------")

    cnt = 0
    for pos in sorted_pos_list:
        cnt += 1

        print(pos)
#
# woi = 2.3
# #补woi=2.3
# ml_pos_mpn_by_week = [1,2,3,4,5,6,7,8,9,10,11,12]
# #wks_div_result = int(woi)
# #wks_mod = (woi*10)%10
# for pos_id in pos_list:
#     # 是否还按照cdc顺位顺序 一台一台地补？
#     target = woi * (ml_pos_mpn_by_week[0] + ml_pos_mpn_by_week[1] + ml_pos_mpn_by_week[2]*0.3) # 补充到woi
