import os
import re

import pandas as pd
from data.databend.mono.ds_cdc_warehouse_inv import DsCdcWarehouseInv
from data.email_config import EmailConfigRepository
from data.mysqls.mono.pos_allocation_result_excel import PosAllocationResultExcel
from data.mysqls.mono.mono_pos_allocation_result import MonoPosAllocationResult
from data.databend.mono.ds_pos import DsPos
from kit.save_as_file import save_as_excel
from util.const import EmailCmd
from util.send_email import send_email_by_datasource, del_email_config

"""
a. by sold to by ship-to by POS by MPN allocation result; （NPP+ High Runner）
！！a'  by sold to by ship-to by MPN allocation result; （NPP+ High Runner）
b. By POS by MPN allocation result;  （NPP）
c. By sold-to by MPN remaining(此处与之前邮件中发生了变化);  （NPP）
d. By POS by MPN result for MyMessage)  （NPP）
"""

FISCAL_WEEK = "FY24Q4W4"

def get_pos_ship_to_mapping(fiscal_week):
    # 查询pos和ship to mapping关系, 和第一顺位的cdc mapping
    pos_ship_to_mapping = DsPos.query_pos_ship_to_mapping(fiscal_week)
    
    # 查询warehouse_name
    ship_to_warehouse_name = DsCdcWarehouseInv.query_ship_to_warehouse_name(fiscal_week)
    
    # 判断下ship to 是否有多个warehouse name, 如果有的话，需要报错，基于下面原因，确认后再报吧。
    ''' 竟然有两家
    1382313  天音信息服务郑州配ECPP良品仓
    1382313      天音信息服务郑州配良品仓
    1567094            中恒顺丰云仓
    1567094           中恒金融星展仓
    '''
    # is_duplicated = ship_to_warehouse_name.duplicated(
    #     subset=["ship_to_id"], keep=False)
    # duplicated_rows = ship_to_warehouse_name[is_duplicated]
    # if not duplicated_rows.empty:
    #     raise Exception("Ship to has multiple warehouse name", duplicated_rows)
    
    return pos_ship_to_mapping, ship_to_warehouse_name


def high_runner_and_npp_report(fiscal_week: str, supplier_name: str, 
                               pos_ship_to_mapping: pd.DataFrame,
                               ship_to_warehouse_name_df: pd.DataFrame):
    # 查询Mono high runner 的分货数据
    hr_allocation = PosAllocationResultExcel.get_dataframe_result(
        fiscal_week, supplier_name)

    # 给Mono high runner数据增加ship_to_id
    hr_allocation = hr_allocation.merge(
        pos_ship_to_mapping, on=["pos_id"], how="left")
    
    # 给Mono high runner数据增加warehouse_name
    hr_allocation = hr_allocation.merge(
        ship_to_warehouse_name_df, on=["ship_to_id"], how="left")
    
    hr_allocation["customer_sold_to_id"] = hr_allocation["customer_sold_to_id"].astype(int).astype(str)

    # 查询npp分货
    npp_allocation = MonoPosAllocationResult.get_dataframe_by_week_sold_to_name(
        fiscal_week, supplier_name)
    
    # 统一成 high runner 的headers
    npp_allocation.columns = hr_allocation.columns

    # group by
    agg_rules = {
        "pos_status": "first",
        "rtm": "first",
        "supplier_name": "first",
        "customer_sold_to_id": "first",
        "hq_id": "first",
        "reseller_name": "first",
        "pos_id": "first",
        "pos_name": "first",
        "pos_type": "first",
        "stop_flag": "first",
        "lob": "first",
        "project_code": "first",
        "model": "first",
        "mpn": "first",
        "description": "first",
        "mpn_type": "first",
        "special_distribute_num": "sum",
        "zero_inventory_replenishment_num": "sum",
        "twoi_distribute_num": "sum",
        "final_num": "sum",
        "ship_to_id": "first",
        "warehouse_name": "first"
    }
    groupby_columns = ["customer_sold_to_id", "ship_to_id", "mpn"]
    group_hr_allocation = hr_allocation.groupby(groupby_columns).agg(agg_rules)
    group_npp_allocation = npp_allocation.groupby(
        groupby_columns).agg(agg_rules)

    # 组合high runner 和 npp 分货数据
    df = pd.concat([hr_allocation, npp_allocation])
    df_by_sold_to_ship_to_mpn = pd.concat([group_hr_allocation, group_npp_allocation])

    # 替换列名并生成对应的excel文件
    excel_headers = [
        "Update Category",
        "RTM",
        "Supplier Name",
        "Customer Sold To ID",
        "HQ ID",
        "Name of Reseller",
        "POS Apple ID",
        "Name of POS",
        "POS type",
        "Stop Flag",
        "LOB",
        "Project Short Desc",
        "Model",
        "Apple Part #",
        "Description",
        "Sustaining/NPI",
        "特殊项目分货",
        "0库存补货",
        "TWOI分货",
        "最终分货",
        "Ship to ID",
        "Warehouse Name",
    ]
    df.columns = excel_headers
    path = f'/'
    file_name = f'{fiscal_week}_{supplier_name}_by_sold_to_by_ship_to_by_pos_by_mpn.xlsx'
    save_as_excel(df, path, file_name)
    
    # 这里需要替换下group by之后的headers，可能需要去掉pos相关的列
    df_by_sold_to_ship_to_mpn.columns = excel_headers
    group_columns = [
        "RTM",
        "Supplier Name",
        "Customer Sold To ID",
        "LOB",
        "Project Short Desc",
        "Model",
        "Apple Part #",
        "Description",
        "Sustaining/NPI",
        "特殊项目分货",
        "0库存补货",
        "TWOI分货",
        "最终分货",
        "Ship to ID",
        "Warehouse Name",
    ]
    df_by_sold_to_ship_to_mpn = df_by_sold_to_ship_to_mpn[group_columns]
    path = f'/'
    file_name = f'{fiscal_week}_{supplier_name}_by_sold_to_by_ship_to_by_mpn.xlsx'
    save_as_excel(df_by_sold_to_ship_to_mpn, path, file_name)
    
    return df, df_by_sold_to_ship_to_mpn


def test_generate_report():
    fiscal_week = FISCAL_WEEK
    df_concat, df_group_concat = pd.DataFrame(), pd.DataFrame()
    pos_ship_to_mapping, ship_to_warehouse_name_df = get_pos_ship_to_mapping(fiscal_week)
    for suppler_name in ["酷爱", "天音"]:
        df, df_group = high_runner_and_npp_report(
            fiscal_week, suppler_name, pos_ship_to_mapping,
            ship_to_warehouse_name_df
        )
        df_concat = pd.concat([df_concat, df])
        df_group_concat = pd.concat([df_group_concat, df_group])
    
    # 保存合并后文件
    path = f'/'
    file_name = f'{fiscal_week}_by_sold_to_by_ship_to_by_pos_by_mpn.xlsx'
    file_name_group = f'{fiscal_week}_by_sold_to_by_ship_to_by_mpn.xlsx'
    save_as_excel(df_concat, path, file_name)
    save_as_excel(df_group_concat, path, file_name_group)


def test_send_email():
    fiscal_week = FISCAL_WEEK
    send_to = {"酷爱": "<EMAIL>,<EMAIL>,<EMAIL>", "天音": "<EMAIL>,<EMAIL>,<EMAIL>"}
    base_dir = os.path.dirname(os.path.abspath(__file__))
    project_name = "FAST-Lite-Server"
    root_path = base_dir.split(project_name)[0] + project_name + '/'
    for reseller_name in ["酷爱", "天音"]:
        email_config = EmailConfigRepository.query_email_config(EmailCmd.PosNppEmail)
        pos_mpn_name = f'{fiscal_week}_{reseller_name}_by_sold_to_by_ship_to_by_pos_by_mpn.xlsx'
        mpn_name = f'{fiscal_week}_{reseller_name}_by_sold_to_by_ship_to_by_mpn.xlsx'

        params = {"week": fiscal_week, "reseller_name": reseller_name}

        url, email_config = del_email_config(email_config, '', params, read_template=False)
        send_email_by_datasource(email_config, send_to.get(reseller_name), [root_path+pos_mpn_name, root_path+mpn_name])
