import logging

import numpy as np
import pandas as pd

from data.databend.mono.ds_cdc_warehouse_inv import DsCdcWarehouseInv
from data.databend.mono.ds_model_stop import DsModelStop
from data.databend.mono.ds_pos import DsPos
from data.databend.mono.ds_pos_eoh import DsPosEoh
from data.databend.mono.ds_shipment_plan_supply import DsShipmentPlanSupply
from data.mysqls.mono.excel_operate_log import ExcelOperateLog
from data.mysqls.mono.high_runner_for_fd import HighRunnerForFd
from data.mysqls.mono.mono_forecast_result import MonoForecastResult
from data.mysqls.mono.mono_pos_allocation_result import MonoPosAllocationResult
from data.mysqls.mono.pos_allocation_result import PosAllocationResult
from data.mysqls.mono.pos_allocation_result_excel import PosAllocationResultExcel
from data.mysqls.mono.pos_list_all import PosListAll
from data.mysqls.mono.pos_plan import PosPlan
from domain.mono.pos_allocation.entity.allocate_record import ALLOCATE_RECORD_TYPE_INSTOCK
from domain.mono.pos_allocation.entity.mpn import Mpn
from domain.mono.pos_allocation.entity.pos import Pos
from domain.mono.pos_allocation.impl.woi_allocation import WoiAllocation, get_twoi_fcst
from kit.save_as_file import save_as_excel


class PosAllocationFromShipmentPlan:
    def __init__(self, fiscal_week: str, pos_list=None, supplies=None, mpns=None, hq_infos=None, mpn_infos=None):
        self.fiscal_week = fiscal_week
        self._load_plan_id()
        # 需要先加载mpn（cdc数据依赖这个）
        if mpns is None:
            self._load_mpns()
            # self.mpns = [mpn for mpn in self.mpns if mpn.id == "MRXQ3CH/A"]
        else:
            self.mpns = mpns

        if hq_infos is None:
            self._load_hq_infos()
        else:
            self.hq_infos = hq_infos

        if pos_list is None:
            self._load_poses()
            # self.pos_list = [pos for pos in self.pos_list if pos.pos_id==3468396]
        else:
            self.pos_list = pos_list

        if supplies is None or mpn_infos is None:
            self._load_shipment_supplies()
        else:
            self.supplies = supplies
            self.mpn_infos = mpn_infos

        # by model pos停货
        self.stops = DsModelStop.query_by_week(self.fiscal_week)

        # 加载所有的cdc
        self.cdcs = DsCdcWarehouseInv.query_by_week(self.fiscal_week, self.mpns)

    def is_stop(self, mpn: Mpn, pos: Pos) -> bool:
        for stop_item in self.stops:
            if stop_item.pos_id == str(pos.pos_id) and stop_item.sub_lob == mpn.sub_lob:
                return True
        return False

    def allocate(self):
        for mpn in self.mpns:
            logging.info(f"begin handle mpn:{mpn.id}")
            self.allocate_instock(mpn)

            # self.allocate_woi(mpn)

            self._save_to_db([mpn])

        self._save_to_excel(self.pos_list, f"mono_allocation_{self.fiscal_week}_pos_list")
        self._save_to_excel(self.supplies, f"mono_allocation_{self.fiscal_week}_supply_list")
        self.save_to_mono_db()

    def allocate_instock(self, mpn):
        # 补完stock=1的pos后补stock=2的
        in_stock_targets = [1, 2]
        for in_stock in in_stock_targets:
            if mpn.npp_min_inventory < in_stock:
                continue
            self.allocate_instock_for_each_mpn(mpn, in_stock)

    def allocate_instock_for_each_mpn(self, mpn: Mpn, in_stock_target: int):
        # todo 后续pos eoh 会按照周来查询, 暂时先写死一周
        pos_eoh_list = DsPosEoh.query_pos_eoh(202443, [mpn], self.pos_list)
        if len(pos_eoh_list) == 0:
            raise Exception("no pos eoh")
        for pos in self.pos_list:
            eoh = 0
            eoh_list = [item.eoh for item in pos_eoh_list if
                        item.pos_id == str(pos.pos_id) and item.mpn == mpn.id]
            if len(eoh_list) > 0:
                eoh = eoh_list[0]
            pos.set_eoh({mpn.id: eoh})

        for supplier in self.supplies:
            # 是否有剩余supply
            if not supplier.has_supply(mpn):
                continue
            # 给对应pos补货
            for pos in self.pos_list:
                if pos.sold_to_id != str(supplier.sold_to_id):
                    continue

                # 停店不参与普通分货
                if not pos.is_active() or self.is_stop(mpn, pos):
                    continue

                # 已达目标，无需分货
                cur_eoh = pos.cur_eoh(mpn)
                if (cur_eoh >= pos.npp_min_inventory_target(mpn)  # 街边店不参与0库存补货
                        or cur_eoh >= in_stock_target):
                    continue

                pos.allocate_from_shipment_plan(supplier, mpn, ALLOCATE_RECORD_TYPE_INSTOCK, 1)  # 补stock，一次只补1台

    def allocate_woi(self, mpn: Mpn, ml_fcsts=None):
        if ml_fcsts == None:
            ml_fcsts = MonoForecastResult.query_pos_mpn_fcst(self.fiscal_week, [mpn], self.pos_list)
        for pos in self.pos_list:
            for ml_fcst in ml_fcsts:
                if pos.pos_id == ml_fcst.pos_id:
                    woi_fcst = get_twoi_fcst([
                        ml_fcst.prediction_wk1,
                        ml_fcst.prediction_wk2,
                        ml_fcst.prediction_wk3,
                    ])
                    pos.set_twoi_fcst({mpn.id: woi_fcst})
        for supplier in self.supplies:
            # 是否有剩余supply
            if not supplier.has_supply(mpn):
                continue

            active_poses = []
            for pos in self.pos_list:
                # 停店不参与普通分货
                if not pos.is_active() or self.is_stop(mpn, pos):
                    continue
                # 取对应的这些pos
                if pos.sold_to_id != str(supplier.sold_to_id):
                    continue

                # 缺少fcst预测数据的不参与！
                if pos.twoi_fcst.get(mpn.id, None) is None:
                    logging.error(f"no ml fcst for pos {pos.pos_id} for mpn {mpn.id}")
                    continue
                active_poses.append(pos)

            WoiAllocation().woi_allocation(active_poses, supplier, mpn)

    def _save_to_db(self, mpns: list[Mpn]):
        # 将mpn分货结果存储到数据库中
        result = []
        for item in self.pos_list.copy():
            result.extend(item.serialize_for_df(self.cdcs, mpns))
        result_df = pd.DataFrame(result)
        result_df.columns = [
            'hq_id',
            'reseller_name',
            'sold_to_id',
            'sold_to_name',
            'pos_id',
            'pos_name',
            'category',
            'district_group',
            'location_type',
            'pos_type',
            'lob',
            'sub_lob',
            'cdcs',
            'mpn_id',
            'project_code',
            'description',
            'ship_to_id',
            'warehouse_name',
            'sustaining_or_npi',
            'eoh',
            'woi',
            'twoi_fcst',
            'total_inv',
            'npp_min_inventory',
            'fix_quantity',
            'instock_quantity',
            'woi_quantity'
        ]
        result_df["fiscal_week"] = self.fiscal_week
        # 保存到数据库中
        result_df.replace({np.nan: None}, inplace=True)
        fields_to_update = ['hq_id', 'reseller_name', 'sold_to_id', 'sold_to_name', 'pos_name', 'category', 'district_group', 'location_type',
                            'pos_type', 'lob', 'sub_lob', 'cdcs', 'warehouse_name', 'sustaining_or_npi',
                            'eoh', 'woi', 'twoi_fcst', 'total_inv', 'npp_min_inventory', 'fix_quantity',
                            'instock_quantity', 'woi_quantity', 'project_code', 'description']
        MonoPosAllocationResult.batch_update(result_df.to_dict("records"), fields_to_update)

    def _load_mpns(self):
        version = ExcelOperateLog.query_max_version("excelHighRunnerForFD")
        if version is None:
            return []  # todo 告警
        npp_mpn_list = HighRunnerForFd.query_npp(version_id=version)
        print(npp_mpn_list)
        self.mpns = npp_mpn_list

    def _load_hq_infos(self):
        pos_list_all_version = ExcelOperateLog.query_max_version("excelPosListAll")
        reseller_version = ExcelOperateLog.query_max_version("excelResellerMapping")
        if pos_list_all_version is None or reseller_version is None:
            raise Exception("No pos list all version or reseller version!")
        hq_infos = PosListAll.query_by_version(pos_list_all_version, reseller_version)
        print(hq_infos)
        self.hq_infos = hq_infos

    def _load_plan_id(self):
        ret = PosPlan.subquery_plan_id_no_session(self.fiscal_week)
        if ret:
            self.plan_id = ret[0].id


    def _load_poses(self):
        self.pos_list = DsPos.query_by_fiscal_week(self.fiscal_week)
        if len(self.pos_list) == 0:
            logging.error(f"Note: No pos list for {self.fiscal_week}!")

    def _load_shipment_supplies(self):
        shipment_supplies, mpn_infos = DsShipmentPlanSupply.query_by_week(self.fiscal_week)
        self.supplies = shipment_supplies
        self.mpn_infos = mpn_infos
        for mpn_info_with_project_code_and_desc in mpn_infos:
            for mpn in self.mpns:
                if mpn.id == mpn_info_with_project_code_and_desc.id:
                    mpn.set_project_code(mpn_info_with_project_code_and_desc.project_code)
                    mpn.set_description(mpn_info_with_project_code_and_desc.description)

    def _save_to_excel(self, data_list: list, file_name: str):
        data = []
        for item in data_list:
            data.extend(item.serialize_for_df(self.cdcs, self.mpns))
        print(f"data len:::{len(data)}")
        # 将字典列表转换为Pandas DataFrame
        df = pd.DataFrame(data)
        path = f'/'
        file_name = f'{file_name}.xlsx'
        save_as_excel(df, path, file_name)

    def save_to_mono_db(self):
        result = []
        for item in self.supplies:
            # 从hq_infos里找到一个和supplier对应的sold to信息，如果匹配到多个sold to，只取一个
            hq_infos = [hq_info for hq_info in self.hq_infos if int(hq_info.sold_to_id) == item.sold_to_id]
            if hq_infos:
                hq_info = hq_infos[0]
            else:
                hq_info = None
            result.extend(item.serialize_for_mono_df(self.mpns, hq_info, self.plan_id, self.mpn_infos))
        result_df = pd.DataFrame(result)
        # 保存到数据库中
        result_df.replace({np.nan: None}, inplace=True)
        PosAllocationResult.remove_offline_npp_data(self.plan_id)
        PosAllocationResult.bulk_save(result_df.to_dict("records"))

        # 保存pos allocation result excel数据
        result = []
        for item in self.pos_list:
            hq_infos = [hq_info for hq_info in self.hq_infos if int(hq_info.id) == int(item.pos_id)]
            if hq_infos:
                hq_info = hq_infos[0]
            else:
                hq_info = None
            result.extend(item.serialize_for_mono_df(self.mpns, hq_info, self.plan_id, self.mpn_infos))
        result_df = pd.DataFrame(result)
        # 判断result_df是否已停货，如果是，则pos_type为S
        result_df["stop_flag"] = result_df.apply(
            lambda x: "D" if x["pos_status"] == "Deleted" else "S" if self.is_stop_by_pos_model(x["model"], x["pos_id"]) else None,
            axis=1)

        # 保存到数据库中
        result_df.replace({np.nan: None}, inplace=True)
        PosAllocationResultExcel.remove_offline_npp_data(self.plan_id)
        PosAllocationResultExcel.bulk_save(result_df.to_dict("records"))

    def is_stop_by_pos_model(self, sub_lob: str, pos_id: str) -> bool:
        for stop_item in self.stops:
            if stop_item.pos_id == str(pos_id) and stop_item.sub_lob == sub_lob:
                return True
        return False
