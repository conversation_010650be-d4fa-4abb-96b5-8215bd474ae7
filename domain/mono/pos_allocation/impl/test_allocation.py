from domain.mono.pos_allocation.entity.cdc import Cdc
from domain.mono.pos_allocation.entity.mpn import Mpn
from domain.mono.pos_allocation.entity.pos import Pos, sort_pos
from domain.mono.pos_allocation.impl.pos_allocation import PosAllocation
from domain.supply.entity import Lob

pos_list = [
    Pos('862150', '酷爱', 3868483, "太原波导运城万荣社区店", "Hub AAR", "Commercial street", "Actived", 12, [
        "tianjin",
    ]),
    <PERSON><PERSON>('862150', '酷爱', 1177268, "联信电子北镇店", "AAR+", "Phone Street", "Deleted", 12, [
        "chengdu",
    ]),
    Pos('862150', '酷爱', 4076065, "讯湃通讯南阳卧龙金玛特时尚广场店", "Shopping mall", "APR", "Actived", 8, [
        "beijing",
    ]),
    <PERSON><PERSON>('862150', '酷爱', 949526, "FORTUNE巴中店", "AAR+", "Phone Street", "Actived", 8, [
        "chengdu",
    ]),
    <PERSON><PERSON>('862150', '酷爱', 1107939, "宝丰通讯铁岭西丰店", "AAR+", "Phone Street", "Actived", 12, [
        "chengdu",
    ]),
]

tianjin_cdc = Cdc("tianjin", 123)
tianjin_cdc.add_pod_supply("a", 0)
tianjin_cdc.add_pod_supply("b", 1)

beijing_cdc = Cdc("beijing", 123)
beijing_cdc.add_pod_supply("a", 10)
beijing_cdc.add_pod_supply("b", 2)

chengdu_cdc = Cdc("chengdu", 123)
chengdu_cdc.add_pod_supply("a", 3)
chengdu_cdc.add_pod_supply("b", 1)
cdc_list = [
    tianjin_cdc, beijing_cdc, chengdu_cdc
]

sorted_pos_list = sort_pos(pos_list)

mpns = [
    Mpn("a", Lob.MAC, "", True, 1),
    # Mpn("b", Lob.IPHONE, "",True, 1),
]


def test_allocate_instock_for_each_mpn():
    pa = PosAllocation("", sorted_pos_list, cdc_list, mpns)
    for mpn in mpns:
        pa.allocate_instock_for_each_mpn(mpn, 1)

    print("\n")
    for pos in sorted_pos_list:
        print(pos)


def test_handler_each_mpn_woi_allocate():
    pa = PosAllocation("", sorted_pos_list, cdc_list, mpns)
    ml_fcsts = {"a": 3.8, "b": 3.1}
    for mpn in mpns:
        pa.allocate_woi(mpn)
        print(f"=======mpn::{mpn.id}============")
        for pos in sorted_pos_list:
            print(pos, f"woi:{pos.woi(mpn)}, eoh:{pos.cur_eoh(mpn)}, gap={pos.woi_gap(mpn)}")


def test_allocate():
    pa = PosAllocation("FY24Q3W13")
    pa.allocate()

    print("\n")
    for pos in sorted_pos_list:
        for record in pos.allocated_records:
            rc= record.get("MTLN3CH/A")
            if rc.instock_quantity > 1:
                print("instock >1:::",pos,rc)

        #print(pos)

