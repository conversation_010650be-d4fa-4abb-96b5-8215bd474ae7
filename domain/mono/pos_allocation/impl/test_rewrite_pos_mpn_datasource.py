import datetime
import time

from data.fiscal_year_week import FiscalYearWeek
from domain.mono.pos_allocation.impl.rewrite_pos_mpn_datasource import RewritePosMpnDatasource


def test_rewrite():
    t1 = time.time()
    # 当前日期对应财周
    current_dt = datetime.datetime.now().strftime('%Y-%m-%d')
    fiscal_obj = FiscalYearWeek.get_fis_by_date(date=current_dt)
    if fiscal_obj is None:
        print(f"can't find fiscal day, current_dt: {current_dt}")
        return
    fiscal_week = fiscal_obj.fiscal_qtr_week_name
    rpn = RewritePosMpnDatasource(fiscal_week)
    rpn.rewrite()
    t2 = time.time()
    print(f'rewrite pos mpn cost {(t2 - t1) / 3600}h.')
