import logging

import numpy as np
import pandas as pd

from data.databend.mono.ds_model_stop import DsModelStop
from data.databend.mono.ds_pos_eoh import DsPosEoh
from data.mysqls.mono.mono_forecast_result import MonoForecastResult
from data.mysqls.mono.mono_pos_allocation_result import MonoPosAllocationResult
from domain.mono.pos_allocation.entity.allocate_record import ALLOCATE_RECORD_TYPE_INSTOCK
from domain.mono.pos_allocation.impl.woi_allocation import WoiAllocation, get_twoi_fcst
from kit.save_as_file import save_as_excel
from data.databend.mono.ds_cdc_pod_inv import DsCdcPodInv
from data.databend.mono.ds_cdc_warehouse_inv import DsCdcWarehouseInv
from data.databend.mono.ds_pos import DsPos
from data.mysqls.mono.excel_operate_log import ExcelOperateLog
from data.mysqls.mono.high_runner_for_fd import HighRunnerForFd
from domain.mono.pos_allocation.entity.cdc import Cdc, get_cdc_by_ship_to
from domain.mono.pos_allocation.entity.mpn import Mpn
from domain.mono.pos_allocation.entity.pos import Pos, CDC_ORDERS


class PosAllocation:
    def __init__(self, fiscal_week: str, pos_list=None, cdc_list=None, mpns=None):
        self.fiscal_week = fiscal_week

        # 需要先加载mpn（cdc数据依赖这个）
        if mpns is None:
            self._load_mpns()
            self.mpns = [mpn for mpn in self.mpns if mpn.id == "MTLH3CH/A"]
        else:
            self.mpns = mpns


        if pos_list is None:
            self._load_poses()
            self.pos_list = [pos for pos in self.pos_list if  pos.pos_id==4015945]
        else:
            self.pos_list = pos_list

        if cdc_list is None:
            self._load_cdcs()
        else:
            self.cdc_list = cdc_list

        # by model pos停货
        self.stops = DsModelStop.query_by_week(self.fiscal_week)

    def is_stop(self, mpn: Mpn, pos: Pos) -> bool:
        for stop_item in self.stops:
            if stop_item.pos_id == pos.pos_id and stop_item.sub_lob == mpn.sub_lob:
                return True
        return False

    def allocate(self):
        for mpn in self.mpns:
            logging.info(f"begin handle mpn:{mpn.id}")
            self.allocate_instock(mpn)

            self.allocate_woi(mpn)

            #self._save_to_db([mpn])

        self._save_to_excel(self.pos_list, f"mono_allocation_{self.fiscal_week}_pos_list")
        self._save_to_excel(self.cdc_list, f"mono_allocation_{self.fiscal_week}_cdc_list")

    def allocate_instock(self, mpn):
        # 补完stock=1的pos后补stock=2的
        in_stock_targets = [1, 2]
        for in_stock in in_stock_targets:
            if mpn.npp_min_inventory < in_stock:
                continue
            self.allocate_instock_for_each_mpn(mpn, in_stock)

    def allocate_instock_for_each_mpn(self, mpn: Mpn, in_stock_target: int):
        # todo 后续pos eoh 会按照周来查询, 暂时先写死一周
        pos_eoh_list = DsPosEoh.query_pos_eoh(202439, [mpn], self.pos_list)
        for cdc_order in CDC_ORDERS:
            for cdc in self.cdc_list:
                # cdc是否有剩余supply
                if not cdc.has_supply(mpn):
                    continue
                # 给第n顺位为该cdc的pos补货
                for pos in self.pos_list:
                    eoh = 0
                    eoh_list = [item.eoh for item in pos_eoh_list if
                                item.pos_id == str(pos.pos_id) and item.mpn == mpn.id]
                    if len(eoh_list) > 0:
                        eoh = eoh_list[0]
                    pos.set_eoh({mpn.id: eoh})
                    if not pos.equal_cdc_order(cdc.ship_to, cdc_order):
                        continue
                    # 停店不参与普通分货
                    if not pos.is_active() or self.is_stop(mpn, pos):
                        continue

                    # 已达目标，无需分货
                    cur_eoh = pos.cur_eoh(mpn)
                    if (cur_eoh >= pos.npp_min_inventory_target(mpn)  # 街边店不参与0库存补货
                            or cur_eoh >= in_stock_target):
                        continue

                    pos.allocate_from_cdc(cdc, mpn, ALLOCATE_RECORD_TYPE_INSTOCK, 1)  # 补stock，一次只补1台

    def allocate_woi(self, mpn: Mpn, ml_fcsts=None):
        if ml_fcsts == None:
            ml_fcsts = MonoForecastResult.query_pos_mpn_fcst(self.fiscal_week, [mpn], self.pos_list)
        for pos in self.pos_list:
            for ml_fcst in ml_fcsts:
                if pos.pos_id == ml_fcst.pos_id:
                    woi_fcst = get_twoi_fcst([
                        ml_fcst.prediction_wk1,
                        ml_fcst.prediction_wk2,
                        ml_fcst.prediction_wk3,
                    ])
                    pos.set_twoi_fcst({mpn.id: woi_fcst})
        for cdc_order in CDC_ORDERS:
            for cdc in self.cdc_list:
                # cdc是否有剩余supply
                if not cdc.has_supply(mpn):
                    continue

                active_poses = []
                for pos in self.pos_list:
                    # 停店不参与普通分货
                    if not pos.is_active() or self.is_stop(mpn, pos):
                        continue
                    # 取第n顺位为该cdc的这些pos
                    if not pos.equal_cdc_order(cdc.ship_to, cdc_order):
                        continue

                    # 缺少fcst预测数据的不参与！
                    if pos.twoi_fcst.get(mpn.id, None) is None:
                        logging.error(f"no ml fcst for pos {pos.pos_id} for mpn {mpn.id}")
                        continue
                    active_poses.append(pos)

                WoiAllocation().woi_allocation_by_cdc(active_poses, cdc, mpn)

    def _save_to_db(self, mpns: list[Mpn]):
        # 将mpn分货结果存储到数据库中
        result = []
        for item in self.pos_list.copy():
            result.extend(item.serialize_for_df(mpns))
        result_df = pd.DataFrame(result)
        result_df.columns = [
            'sold_to_id',
            'sold_to_name',
            'pos_id',
            'pos_name',
            'category',
            'district_group',
            'location_type',
            'pos_type',
            'lob',
            'sub_lob',
            'cdcs',
            'mpn_id',
            'ship_to_id',
            'sustaining_or_npi',
            'eoh',
            'woi',
            'twoi_fcst',
            'total_inv',
            'npp_min_inventory',
            'fix_quantity',
            'instock_quantity',
            'woi_quantity'
        ]
        result_df["fiscal_week"] = self.fiscal_week
        # 保存到数据库中
        result_df.replace({np.nan: None}, inplace=True)
        MonoPosAllocationResult.bulk_save(result_df.to_dict("records"))

    def _load_mpns(self):
        version = ExcelOperateLog.query_max_version("excelHighRunnerForFD")
        if version is None:
            return []  # todo 告警
        npp_mpn_list = HighRunnerForFd.query_npp(version_id=version)
        print(npp_mpn_list)
        self.mpns = npp_mpn_list
        #self.mpns = [mpn for mpn in npp_mpn_list if mpn.id == "MRXV3CH/A"]

    def _load_poses(self):
        self.pos_list = DsPos.query_by_fiscal_week(self.fiscal_week)
        if len(self.pos_list) == 0:
            logging.error(f"Note: No pos list for {self.fiscal_week}!")

    def _load_cdcs(self):
        # cdc列表，包含pod和warehouse supply数据
        cdc_list = DsCdcWarehouseInv.query_by_week(self.fiscal_week, self.mpns)
        if len(cdc_list) == 0:
            logging.info("No cdc warehouse inv for npp.")
        cdc_list = sorted(cdc_list, key=lambda x: x.ship_to)

        pod_list = DsCdcPodInv.query_by_week(self.fiscal_week, self.mpns)
        if len(pod_list) == 0:
            logging.info("No cdc pod inv for npp.")

        merge_pod_to_cdc(cdc_list, pod_list)
        self.cdc_list = cdc_list

    def _save_to_excel(self, data_list: list, file_name: str):
        data = []
        for item in data_list:
            data.extend(item.serialize_for_df(self.mpns))
        print(f"data len:::{len(data)}")
        # 将字典列表转换为Pandas DataFrame
        df = pd.DataFrame(data)
        path = f'/'
        file_name = f'{file_name}.xlsx'
        save_as_excel(df, path, file_name)


def merge_pod_to_cdc(cdc_warehouse_list: list[Cdc], pod_list):
    # 合并CDC实际on-hand inventory+周一/周二预定到仓数量(POD)
    for pod in pod_list:
        cdc = get_cdc_by_ship_to(cdc_warehouse_list, pod["ship_to"])
        if cdc is None:
            cdc = Cdc(pod["ship_to"], pod["sold_to"], cdc.warehouse_name)
            cdc_warehouse_list.append(cdc)
        cdc.add_pod_supply(pod["mpn"], pod["qty"])
