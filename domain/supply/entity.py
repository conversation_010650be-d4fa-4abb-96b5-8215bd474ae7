import pandas as pd
import re
from datetime import datetime
from enum import Enum
import datetime as dt
from util.const import DateTimeFormat

# 枚举所有的lob
class Lob(Enum):
    IPHONE = 'iPhone'
    IPAD = 'iPad'
    MAC = 'Mac'
    WATCH = 'Watch'
    AIRPODS = 'AirPods'
    ACCESSORIES = 'Accessories'


    @classmethod
    def all(cls) -> list[str]:
        # 遍历枚举类的所有值
        return [lob.value for lob in cls]


class DataSourceRecordStatus:
    Init = 0
    Running = 1
    Done = 2

class ConfirmFlow:
    def __init__(self, fiscal_year, sales_org, ops_line_desc, confirm_at):
        self.fiscal_year = fiscal_year
        self.sales_org = sales_org
        self.ops_line_desc = ops_line_desc
        self.confirmed_at = confirm_at


class SupplyFileName:
    file_name = ""
    fiscal_week = 0
    fiscal_qtr_week_name = ""
    lob = ""
    updated_at = None

    def __init__(self, file_name: str):
        self.file_name = file_name
        # 一级目录week  "databend-dumps/FAST/PHOEBE/iPhone/FY24Q3W1/"
        # 二级文件  "databend-dumps/FAST/PHOEBE/AirPods/FY24Q2W4/2024-01-1607:32:46/data_2258df5b-339d-4775-a17a-88e6491dc3e5_0000_00000000.parquet"

        pattern = r"databend-dumps/FAST/PHOEBE\/(\w+)\/(\w+)\/(.*)"
        match = re.search(pattern, file_name)
        if not match:
            return

        self.lob = match.group(1)
        self.fiscal_qtr_week_name = match.group(2)  # eg: FY24Q1W4
        year = int(self.fiscal_qtr_week_name[2:4])  # 提取年份部分
        quarter = int(self.fiscal_qtr_week_name[5:6])  # 季度
        week = int(self.fiscal_qtr_week_name[7:])  # 提取周数部分
        self.fiscal_week = int(year * 1000 + quarter * 100 + week)  # 将年份、季度、周数组合成数字

        updated_at = match.group(3)
        pattern = r"(\d{4}-\d{2}-\d{2}\d{2}:\d{2}:\d{2})"
        match = re.search(pattern, updated_at)
        if not match:
            return
        self.updated_at = datetime.strptime(match.group(1), "%Y-%m-%d%H:%M:%S")



UPDATE_TS_FORMAT = '%Y-%m-%d %H:%M:%S.%f'


class SupplyDataFile:
    OPS_LINE_DESC = "ops_line_desc"
    UPDATED_TS = "updated_ts"
    CREATED_TS = "created_ts"
    MAX_UPDATED_TS = "max_updated_ts"
    CONFIRM_AT = "status_ts"
    CONFIRM_STATUS = "status"
    ROC_CUST_ID = "roc_cust_id"

    def __init__(self):
        self.column_name_fiscal_year = 'fiscal_week'
        self.column_name_sales_org = 'sales_org'
        self.column_name_ops_line_desc = 'ops_line_desc'
        self.column_updated_ts = 'updated_ts'
        self.column_created_ts = 'created_ts'

        self.column_name_confirm_at = self.CONFIRM_AT
        self.column_name_confirm_status = self.CONFIRM_STATUS
        # 表头重命名
        self.renamed_headers = {
            self.column_name_fiscal_year: 'fiscal week',
            self.column_name_sales_org: 'sales_org',
            self.column_name_ops_line_desc: self.OPS_LINE_DESC,
            self.column_updated_ts: self.UPDATED_TS,
        }

    def filter_by_mpns(self, df: pd.DataFrame, mpns: list[str]) -> pd.DataFrame:
        return df[df['prod_id'].isin(mpns)]
        #return df[df['ops_line_desc'] == 'iPhone 14']


    def process_data_frame(self, df: pd.DataFrame) -> pd.DataFrame:
        # df = df.astype(str) # 所有列都转成字符串，防止出现0开头的内容丢失
        
        df[self.ROC_CUST_ID].fillna('', inplace=True) # 不显示为None
        df[self.ROC_CUST_ID] = df[self.ROC_CUST_ID].astype(str) # 防止前置0被去除

        def convert_datetime(t: str) -> datetime:
            return datetime.strptime(t, UPDATE_TS_FORMAT)

        # 格式化原始文件中的所有时间字段，防止后续时间类型不一致
        df[self.column_updated_ts] = df[self.column_updated_ts].apply(convert_datetime)
        df[self.column_created_ts] = df[self.column_created_ts].apply(convert_datetime)

        # 根据指定列进行分组
        grouped = df.groupby([self.column_name_sales_org, self.column_name_ops_line_desc])
        # 聚合数据中的最大更新时间，添加成新的一列
        df[self.MAX_UPDATED_TS] = grouped[self.column_updated_ts].transform('max')
        return df.rename(columns=self.renamed_headers)

    def merge_confirm_status(self, df: pd.DataFrame, confirm_flows: list[ConfirmFlow]) -> pd.DataFrame:
        def calculate_confirm_at(row) -> (str, str):
            for flow in confirm_flows:
                if row[self.OPS_LINE_DESC] != flow.ops_line_desc or row['sales_org'] != flow.sales_org:
                    continue
                status = ''
                # 匹配上的最新的状态流水
                if row[self.MAX_UPDATED_TS] < flow.confirmed_at:
                    # 确认后没有修改
                    status = 'confirm'
                # if row['sales_org'] == 'China mainland' and row['ops_line_desc'] == 'iPad (10th Gen)':
                #     print(f"matched latest flow:{flow.sales_org, flow.ops_line_desc, flow.confirmed_at}.")
                #     print(f"max_updated_ts: {row[self.MAX_UPDATED_TS]}, status_ts:{flow.confirmed_at}, status:{status}")

                return flow.confirmed_at.strftime(DateTimeFormat), status
            return '', ''

        if df.shape[0] == 0:
            return df

        df[[self.CONFIRM_AT, self.CONFIRM_STATUS]] = df.apply(calculate_confirm_at, axis=1, result_type='expand')
        return df
