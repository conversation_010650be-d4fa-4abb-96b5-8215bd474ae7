from datetime import datetime

import pandas as pd
from domain.supply.entity import SupplyDataFile, ConfirmFlow
from kit.save_as_file import save_as_csv, save_as_zip, save_as_excel
from util.conf import logger
from util.const import EmailCmd
from util.send_email import send_email_by_database


# 加工(合并确认状态、确认时间)
def handle(
        target_file_name: str, origin_file_df: pd.DataFrame, confirm_flows: list[ConfirmFlow], mpns: list[str], is_debug=False,
):
    supply = SupplyDataFile()
    # 预处理1：按照mpn过滤、
    df = supply.filter_by_mpns(origin_file_df, mpns)
    # 预处理2： 转换表头，添加org,ops_line维度的最后修改时间
    df = supply.process_data_frame(df)

    # 获取mpns里有，但是df里没有的数据，发送邮件
    lost_mpns = list(set(mpns) - set(df['prod_id'].tolist()))
    if lost_mpns:
        params = {"mpns": lost_mpns}
        send_email_by_database(EmailCmd.SupplyLostMpnEmail, file_name='', recipients=None, params=params)

    # 合并confirm状态
    new_df = supply.merge_confirm_status(df, confirm_flows)

    # 删除max_update_ts这一列
    if not is_debug:
        new_df = new_df.drop('max_updated_ts', axis=1)

    # 这里处理下三个时间列，把毫秒去掉
    def convert_datetime(t: datetime) -> str:
        if t is None:
            return ''
        try:
            return t.strftime(HMS_TS_FORMAT)
        except Exception as e:
            logger.error(f"convert_datetime {t} fail. {e}")
        return ''
    new_df[SupplyDataFile.UPDATED_TS] = new_df[SupplyDataFile.UPDATED_TS].apply(convert_datetime)
    # confirmat输出的是str格式，无需转换
    #new_df[SupplyDataFile.CONFIRM_AT] = new_df[SupplyDataFile.CONFIRM_AT].apply(convert_datetime)
    new_df[SupplyDataFile.CREATED_TS] = new_df[SupplyDataFile.CREATED_TS].apply(convert_datetime)
    if is_debug:
        new_df[SupplyDataFile.MAX_UPDATED_TS] = new_df[SupplyDataFile.MAX_UPDATED_TS].apply(convert_datetime)

    # 生成csv文件
    path = f'/uploads/datasource/supply/'
    file_name = f'{target_file_name}.xlsx'
    zip_name = f'{target_file_name}.zip'
    file_path, path = save_as_excel(new_df, path, file_name)
    return save_as_zip(file_path, file_name, path, zip_name)


HMS_TS_FORMAT = '%Y-%m-%d %H:%M:%S'


def reread(path):
    df = pd.read_csv(path)
    df.to_csv(path, header=True, index=False, encoding='utf_8_sig', line_terminator="\n")
    return path
