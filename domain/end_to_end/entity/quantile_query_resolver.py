from data.databend.end_to_end.ml_fcst_quantile_view import MLFcstQuantileView
from domain.demand.entity.const import RTMS_NO_RETAIL
from domain.end_to_end.entity.response_data import Resp, QuantileLevelConfig, UserSetting, \
    WeekInfo


class QuantileQueryResolver:
    def __init__(self,
                 user_quantile_settings: list[UserSetting],
                 fcst_quantiles: list[MLFcstQuantileView],
                 week_names: list[str]) -> None:
        self.user_quantile_settings = user_quantile_settings
        self.fcst_quantiles = fcst_quantiles
        self.week_names = week_names
        self.quantile_configs = self.transfer_to_quantile_resp()

    def transfer_to_quantile_resp(self) -> \
            (dict[str, list[QuantileLevelConfig]], dict[str, Resp]):
        quantile_level_config = {}
        week_info = {}
        quantile_resp = {}
        if self.fcst_quantiles is None:
            return quantile_resp
        for item in self.fcst_quantiles:
            key = get_key(item.sub_rtm, item.sub_lob)
            for index, week_name in enumerate(self.week_names):
                week_key = get_week_key(week_name, item.sub_rtm, item.sub_lob)

                if quantile_level_config.get(week_key) is None:
                    quantile_level_config[week_key] = []

                quantile_level_config[week_key].append(
                    QuantileLevelConfig(
                        quantile_level=item.quantile_level,
                        quantile_min=item.quantile_min_cw if index == 0 else getattr(item, f"quantile_min_cw{index}"),
                        quantile_max=item.quantile_max_cw if index == 0 else getattr(item, f"quantile_max_cw{index}"),
                    )
                )
                week_info[week_key] = WeekInfo(
                    fiscal_week_name=week_name,
                    ml_forecast=item.ml_forecast_cw if index == 0 else getattr(item, f"ml_forecast_cw{index}"),
                    quantile_level_configs=quantile_level_config[week_key]
                )

            if quantile_resp.get(key) is None:
                quantile_resp[key] = []
            quantile_resp[key] = Resp(
                rtm=item.rtm,
                sub_rtm=item.sub_rtm,
                sub_lob=item.sub_lob,
                week_info=[week_info[week_key] for week_key in week_info if week_key.endswith(key)]
            )
        return quantile_resp

    def transfer_to_user_setting_dict(self) -> dict[
        str, int]:
        # 返回的数据应如下
        # {"FY24Q1W1_mono_iphone 15": 0.05}
        res = {}
        for item in self.user_quantile_settings:
            for index, week_name in enumerate(self.week_names):
                key = get_week_key(week_name, item.sub_rtm, item.sub_lob)
                if index == 0:
                    res[key] = item.range_level_cw
                else:
                    res[key] = getattr(item, f"range_level_cw{index}")
        return res

    def as_dict(self):
        res = []
        transfer_to_user_setting_dict = self.transfer_to_user_setting_dict()
        for key, quantile_resp in self.quantile_configs.items():
            forecast_quantiles = []
            for week_info in quantile_resp.week_info:
                week_key = get_week_key(week_info.fiscal_week_name, quantile_resp.sub_rtm, quantile_resp.sub_lob)
                forecast_quantile = {
                    "fiscal_week_name": week_info.fiscal_week_name,
                    "ml_forecast": week_info.ml_forecast,
                    "selected_quantile_level": transfer_to_user_setting_dict.get(week_key, None),
                    "quantile_config":
                        [{
                            "quantile_level": item.quantile_level,
                            "quantile_min": item.quantile_min,
                            "quantile_max": item.quantile_max
                        }
                        for item in week_info.quantile_level_configs]
                }
                forecast_quantiles.append(forecast_quantile)
            res.append({
                "rtm": quantile_resp.rtm,
                "sub_rtm": quantile_resp.sub_rtm,
                "sub_lob": quantile_resp.sub_lob,
                "forecast_quantile": forecast_quantiles
            })
        res.sort(key=lambda item: (RTMS_NO_RETAIL.index(item["rtm"]) if item["rtm"] in RTMS_NO_RETAIL else len(RTMS_NO_RETAIL), item["sub_rtm"]))
        return res


def get_week_key(fiscal_week_name: str, sub_rtm: str, sub_lob: str) -> str:
    return f"{fiscal_week_name}_{sub_rtm}_{sub_lob}"


def get_key(sub_rtm: str, sub_lob: str) -> str:
    return f"{sub_rtm}_{sub_lob}"
