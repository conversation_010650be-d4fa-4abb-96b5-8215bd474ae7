from domain.end_to_end.entity.response_data import UserSetting


def transfer_quantile_data_to_update_data(input_data: dict, forecast_weeks: list[str]) -> list[UserSetting]:
    result = []
    for item in input_data:
        rtm = item.get("rtm", "")
        sub_rtm = item.get("sub_rtm", "")
        sub_lob = item.get("sub_lob", "")

        # 创建默认的 quantile_level 映射
        quantile_levels = {
            "cw": None,
            "cw1": None,
            "cw2": None,
            "cw3": None,
            "cw4": None,
        }

        # 提取 forecast_quantile 列表，确保数据对齐
        forecast_quantiles = item.get("forecast_quantile", [])
        # 将forecast_quantiles按照fiscal_week_name来分组
        forecast_quantiles_dict = {item["fiscal_week_name"]: item for item in forecast_quantiles}
        for i, week in enumerate(forecast_weeks):
            week_data = forecast_quantiles_dict.get(week)
            if week_data:
                suffix = f"cw{i}" if 0 < i < 7 else "cw"
                if suffix:
                    quantile_levels[suffix] = week_data.get("selected_quantile_level", None)

        # 创建 QuantileUserSetting 对象并加入结果
        result.append(
            UserSetting(
                fiscal_week_name=item.get("fiscal_week_name", ""),
                rtm=rtm,
                sub_rtm=sub_rtm,
                sub_lob=sub_lob,
                range_level_cw=quantile_levels["cw"],
                range_level_cw1=quantile_levels["cw1"],
                range_level_cw2=quantile_levels["cw2"],
                range_level_cw3=quantile_levels["cw3"],
                range_level_cw4=quantile_levels["cw4"],
                range_level_cw5=quantile_levels["cw5"],
                range_level_cw6=quantile_levels["cw6"],
            )
        )
    return result


def transfer_batch_data_to_update_data(fiscal_week: str,
                                       update_weeks: list[str],
                                       week_names: list[str],
                                       rtms: list[dict],
                                       sub_lobs: list[str],
                                       quantile_level: float,
                                       user_quantile_settings: list[UserSetting]) -> list[UserSetting]:
    # 根据 update_weeks 和 week_names 构造 week_data 映射
    week_data = {f"cw{i}": week for i, week in enumerate(week_names) if week in update_weeks}

    # 将 user_quantile_settings 转换为字典, key 为 fiscal_week_name + sub_rtm + sub_lob
    user_quantile_setting_dict = {
        f"{item.fiscal_week_name}{item.sub_rtm}{item.sub_lob}": item
        for item in user_quantile_settings
    }

    # 构造结果
    result = []
    for item in rtms:
        sub_rtms = item.get("sub_rtms", "")
        rtm = item.get("rtm", "")
        for sub_rtm in sub_rtms:
            for sub_lob in sub_lobs:
                key = f"{fiscal_week}{sub_rtm}{sub_lob}"
                user_quantile_setting = user_quantile_setting_dict.get(key)

                # 默认值列表，分别为 cw, cw1, cw2, cw3, cw4
                quantile_values = [
                    quantile_level if f"cw{i}" in week_data else (
                                user_quantile_setting and getattr(user_quantile_setting, f"range_level_cw{i}" if 0 < i < 7 else "range_level_cw", None))
                    for i in range(7)
                ]

                # 创建 QuantileUserSetting 实例
                result.append(
                    UserSetting(
                        fiscal_week_name=fiscal_week,
                        rtm=rtm,
                        sub_rtm=sub_rtm,
                        sub_lob=sub_lob,
                        range_level_cw=quantile_values[0],
                        range_level_cw1=quantile_values[1],
                        range_level_cw2=quantile_values[2],
                        range_level_cw3=quantile_values[3],
                        range_level_cw4=quantile_values[4],
                        range_level_cw5=quantile_values[5],
                        range_level_cw6=quantile_values[6],
                    )
                )
    return result



