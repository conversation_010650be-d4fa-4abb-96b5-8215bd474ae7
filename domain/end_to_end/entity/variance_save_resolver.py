from domain.end_to_end.entity.response_data import UserSetting


def transfer_variance_data_to_update_data(input_data: dict, forecast_weeks: list[str]) -> list[UserSetting]:
    result = []
    for item in input_data:
        rtm = item.get("rtm", "")
        sub_rtm = item.get("sub_rtm", "")
        sub_lob = item.get("sub_lob", "")

        # 创建默认的 variance_level 映射
        variance_settings = {
            "cw7": None,
            "cw8": None,
            "cw9": None,
            "cw10": None,
            "cw11": None,
            "cw12": None,
        }

        # 提取 forecast_variance 列表，确保数据对齐
        forecast_variances = item.get("forecast_variance", [])
        # 将forecast_variances按照fiscal_week_name来分组
        forecast_variances_dict = {item["fiscal_week_name"]: item for item in forecast_variances}
        for i, week in enumerate(forecast_weeks):
            if i < 5:
                continue
            week_data = forecast_variances_dict.get(week)
            if week_data:
                suffix = f"cw{i}"
                if suffix:
                    variance_settings[suffix] = week_data.get("feedback_setting", None)

        # 创建 UserSetting 对象并加入结果
        result.append(
            UserSetting(
                fiscal_week_name=item.get("fiscal_week_name", ""),
                rtm=rtm,
                sub_rtm=sub_rtm,
                sub_lob=sub_lob,
                range_level_cw7=variance_settings["cw7"],
                range_level_cw8=variance_settings["cw8"],
                range_level_cw9=variance_settings["cw9"],
                range_level_cw10=variance_settings["cw10"],
                range_level_cw11=variance_settings["cw11"],
                range_level_cw12=variance_settings["cw12"]
            )
        )
    return result


def transfer_batch_data_to_update_data(fiscal_week: str,
                                       update_weeks: list[str],
                                       week_names: list[str],
                                       rtms: list[dict],
                                       sub_lobs: list[str],
                                       variance: float,
                                       user_variance_settings: list[UserSetting]) -> list[UserSetting]:
    # 根据 update_weeks 和 week_names 构造 week_data 映射
    week_data = {f"cw{i}": week for i, week in enumerate(week_names) if week in update_weeks}

    # 将 user_variance_settings 转换为字典, key 为 fiscal_week_name + sub_rtm + sub_lob
    user_variance_setting_dict = {
        f"{item.fiscal_week_name}{item.sub_rtm}{item.sub_lob}": item
        for item in user_variance_settings
    }

    # 构造结果
    result = []
    for item in rtms:
        sub_rtms = item.get("sub_rtms", "")
        rtm = item.get("rtm", "")
        for sub_rtm in sub_rtms:
            for sub_lob in sub_lobs:
                key = f"{fiscal_week}{sub_rtm}{sub_lob}"
                user_variance_setting = user_variance_setting_dict.get(key)

                # 判断要更新的数据是否在update_weeks中，如果不在，则使用其原本的值
                variance_values = [
                    variance if f"cw{i}" in week_data else (
                                user_variance_setting and getattr(user_variance_setting, f"range_level_cw{i}", None))
                    for i in range(7, 13)
                ]

                # 创建 varianceUserSetting 实例
                result.append(
                    UserSetting(
                        fiscal_week_name=fiscal_week,
                        rtm=rtm,
                        sub_rtm=sub_rtm,
                        sub_lob=sub_lob,
                        range_level_cw7=variance_values[0],
                        range_level_cw8=variance_values[1],
                        range_level_cw9=variance_values[2],
                        range_level_cw10=variance_values[3],
                        range_level_cw11=variance_values[4],
                        range_level_cw12=variance_values[5]
                    )
                )
    return result



