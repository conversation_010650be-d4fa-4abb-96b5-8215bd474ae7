from decimal import Decimal


class DemandForecastItem:
    def __init__(self, lob:str, sub_lob: str,update_time: str,
                 nand: str, color: str, mpn: str, mpn_order: int,
                 ub_eoh: int, shipment_plan_cw: int,
                 shipment_plan_cw1: int, shipment_plan_cw2: int,
                 demand_origin_cw1: int, demand_target_cw1: int,
                 demand_origin_cw2: int, demand_target_cw2: int,
                 po_needed_cw1: int, target_po_needed_cw1: int,
                 po_needed_cw2: int, target_po_needed_cw2: int,
                 forecast_origin_cw: int, forecast_target_cw: int,
                 forecast_origin_cw1: int, forecast_target_cw1: int,
                 forecast_origin_cw2: int, forecast_target_cw2: int,
                 forecast_origin_cw3: int, forecast_target_cw3: int,
                 forecast_origin_cw4: int, forecast_target_cw4: int,
                 forecast_origin_cw5: int, forecast_target_cw5: int,
                 forecast_origin_cw6: int, forecast_target_cw6: int,
                 forecast_origin_cw7: int, forecast_target_cw7: int, 
                 forecast_origin_cw8: int, forecast_target_cw8: int, 
                 forecast_origin_cw9: int, forecast_target_cw9: int, 
                 forecast_origin_cw10: int, forecast_target_cw10: int, 
                 forecast_origin_cw11: int, forecast_target_cw11: int, 
                 forecast_origin_cw12: int, forecast_target_cw12: int,
                 ):
        self.update_time = update_time  # 更新时间
        self.lob = lob # LOB
        self.sub_lob = sub_lob # SUB_LOB
        self.nand = nand # 闪存
        self.color = color # 颜色
        self.mpn = mpn # MPN
        self.mpn_order = mpn_order # MPN排序
        self.ub_eoh = ub_eoh if ub_eoh is not None else 0
        self.shipment_plan_cw = shipment_plan_cw if shipment_plan_cw is not None else 0
        self.shipment_plan_cw1 = shipment_plan_cw1 if shipment_plan_cw1 is not None else 0
        self.shipment_plan_cw2 = shipment_plan_cw2 if shipment_plan_cw2 is not None else 0
        self.demand_origin_cw1 = demand_origin_cw1 if demand_origin_cw1 is not None else 0 # 原始需求量
        self.demand_target_cw1 = demand_target_cw1 if demand_target_cw1 is not None else 0 # 目标需求量
        self.demand_origin_cw2 = demand_origin_cw2 if demand_origin_cw2 is not None else 0
        self.demand_target_cw2 = demand_target_cw2 if demand_target_cw2 is not None else 0
        self.po_needed_cw1 = po_needed_cw1 if po_needed_cw1 is not None else 0  # PO需求量
        self.target_po_needed_cw1 = target_po_needed_cw1 if target_po_needed_cw1 is not None else 0  # PO需求量
        self.po_needed_cw2 = po_needed_cw2 if po_needed_cw2 is not None else 0
        self.target_po_needed_cw2 = target_po_needed_cw2 if target_po_needed_cw2 is not None else 0
        self.forecast_origin_cw = forecast_origin_cw if forecast_origin_cw is not None else 0
        self.forecast_target_cw = forecast_target_cw if forecast_target_cw is not None else 0
        self.forecast_origin_cw1 = forecast_origin_cw1 if forecast_origin_cw1 is not None else 0
        self.forecast_target_cw1 = forecast_target_cw1 if forecast_target_cw1 is not None else 0
        self.forecast_origin_cw2 = forecast_origin_cw2 if forecast_origin_cw2 is not None else 0
        self.forecast_target_cw2 = forecast_target_cw2 if forecast_target_cw2 is not None else 0
        self.forecast_origin_cw3 = forecast_origin_cw3 if forecast_origin_cw3 is not None else 0
        self.forecast_target_cw3 = forecast_target_cw3 if forecast_target_cw3 is not None else 0
        self.forecast_origin_cw4 = forecast_origin_cw4 if forecast_origin_cw4 is not None else 0
        self.forecast_target_cw4 = forecast_target_cw4 if forecast_target_cw4 is not None else 0
        self.forecast_origin_cw5 = forecast_origin_cw5 if forecast_origin_cw5 is not None else 0
        self.forecast_target_cw5 = forecast_target_cw5 if forecast_target_cw5 is not None else 0
        self.forecast_origin_cw6 = forecast_origin_cw6 if forecast_origin_cw6 is not None else 0
        self.forecast_target_cw6 = forecast_target_cw6 if forecast_target_cw6 is not None else 0
        self.forecast_origin_cw7 = forecast_origin_cw7 if forecast_origin_cw7 is not None else 0
        self.forecast_target_cw7 = forecast_target_cw7 if forecast_target_cw7 is not None else 0
        self.forecast_origin_cw8 = forecast_origin_cw8 if forecast_origin_cw8 is not None else 0
        self.forecast_target_cw8 = forecast_target_cw8 if forecast_target_cw8 is not None else 0
        self.forecast_origin_cw9 = forecast_origin_cw9 if forecast_origin_cw9 is not None else 0
        self.forecast_target_cw9 = forecast_target_cw9 if forecast_target_cw9 is not None else 0
        self.forecast_origin_cw10 = forecast_origin_cw10 if forecast_origin_cw10 is not None else 0
        self.forecast_target_cw10 = forecast_target_cw10 if forecast_target_cw10 is not None else 0
        self.forecast_origin_cw11 = forecast_origin_cw11 if forecast_origin_cw11 is not None else 0
        self.forecast_target_cw11 = forecast_target_cw11 if forecast_target_cw11 is not None else 0
        self.forecast_origin_cw12 = forecast_origin_cw12 if forecast_origin_cw12 is not None else 0
        self.forecast_target_cw12 = forecast_target_cw12 if forecast_target_cw12 is not None else 0
        self.total_demand_cw1 = 0
        self.total_demand_cw2 = 0
    
    def woi_target_cw1(self):
        '''
        CW+1 WOI = [CW+1 Feedback Demand + CW-1 EOH + CW Shipment Plan - Sum (CW ~ CW+1 FCST)] ÷ Avg.(CW+2 ~ CW+4 FCST)
        '''
        dividend = self.demand_target_cw1 + self.ub_eoh + self.shipment_plan_cw - (self.forecast_origin_cw + self.forecast_origin_cw1)
        divisor = (self.forecast_origin_cw2 + self.forecast_origin_cw3 + self.forecast_origin_cw4) / 3 # Avg.(CW+2 ~ CW+4 FCST)
        if divisor == Decimal('0'):
            return 0
        result = float(dividend / divisor) 
        # WOI不能小于0
        return result if result > 0 else 0
    
    def woi_origin_cw1(self):
        '''
        CW+1 WOI = [CW+1 Feedback Demand + CW-1 EOH + CW Shipment Plan - Sum (CW ~ CW+1 FCST)] ÷ Avg.(CW+2 ~ CW+4 FCST)
        '''
        dividend = self.demand_origin_cw1 + self.ub_eoh + self.shipment_plan_cw - (self.forecast_origin_cw + self.forecast_origin_cw1)
        divisor = (self.forecast_origin_cw2 + self.forecast_origin_cw3 + self.forecast_origin_cw4) / 3  # Avg.(CW+2 ~ CW+4 FCST)
        if divisor == Decimal('0'):
            return 0
        result = float(dividend / divisor)
        # WOI不能小于0
        return result if result > 0 else 0
    
    def woi_target_cw2(self):
        '''
        CW+2 WOI= [CW+2 Feedback Demand + CW-1 EOH + CW Shipment Plan + CW+1 Feedback Demand - Sum (CW ~ CW+2 FCST)] ÷ Avg.(CW+3 ~ CW+5 FCST)
        '''
        dividend = self.demand_target_cw2 + self.ub_eoh + self.shipment_plan_cw + self.demand_target_cw1 - (self.forecast_origin_cw + self.forecast_origin_cw1 + self.forecast_origin_cw2)
        divisor = (self.forecast_origin_cw3 + self.forecast_origin_cw4 + self.forecast_origin_cw5) / 3 # Avg.(CW+3 ~ CW+5 FCST)
        if divisor == Decimal('0'):
            return 0
        result = float(dividend / divisor)
        return result if result > 0 else 0
    
    def woi_origin_cw2(self):
        '''
        CW+2 WOI= [CW+2 Feedback Demand + CW-1 EOH + CW Shipment Plan + CW+1 Feedback Demand - Sum (CW ~ CW+2 FCST)] ÷ Avg.(CW+3 ~ CW+5 FCST)
        '''
        dividend = self.demand_origin_cw2 + self.ub_eoh + self.shipment_plan_cw + self.demand_origin_cw1 - (self.forecast_origin_cw + self.forecast_origin_cw1 + self.forecast_origin_cw2)
        divisor = (self.forecast_origin_cw3 + self.forecast_origin_cw4 + self.forecast_origin_cw5) / 3 # Avg.(CW+3 ~ CW+5 FCST)
        if divisor == Decimal('0'):
            return 0
        result = float(dividend / divisor)
        return result if result > 0 else 0
    
    def set_total_demand_cw1(self, total: int):
        self.total_demand_cw1 = total
    
    def set_total_demand_cw2(self, total: int):
        self.total_demand_cw2 = total
    
    def mix_cw1(self):
        if self.total_demand_cw1 == 0:
            return 0
        else:
            return self.demand_target_cw1 / self.total_demand_cw1
    
    def mix_cw2(self):
        if self.total_demand_cw2 == 0:
            return 0
        else:
            return self.demand_target_cw2 / self.total_demand_cw2
    
    def sublob_dict(self) -> dict:
        return {
        "latest_update_time": self.update_time,
        "sub_lob": self.sub_lob,
        "nand": self.nand,
        "color": self.color,
        "mpn": self.mpn,
        "mpn_order": self.mpn_order,
        "cw1": {
          "demand_origin": self.demand_origin_cw1,
          "demand_target": self.demand_target_cw1,
          "delta": (self.demand_target_cw1 - self.demand_origin_cw1) if self.demand_target_cw1 is not None and self.demand_origin_cw1 is not None else None,
          "mix": self.mix_cw1(), # 本行占汇总行百分比
          "woi_origin": self.woi_origin_cw1(),
          "woi_target": self.woi_target_cw1(),
          "po_needed": self.po_needed_cw1,
          "po_needed_target": self.target_po_needed_cw1
        },
        "cw2": {
          "demand_origin": self.demand_origin_cw2,
          "demand_target": self.demand_target_cw2,
          "delta": (self.demand_target_cw1 - self.demand_origin_cw1) if self.demand_target_cw1 is not None and self.demand_origin_cw1 is not None else None,
          "mix": self.mix_cw2(), # 本行占汇总行百分比
          "woi_origin": self.woi_origin_cw2(),
          "woi_target": self.woi_target_cw2(),
          "po_needed": self.po_needed_cw2,
          "po_needed_target": self.target_po_needed_cw2,
        },
        "forecast_origin_cw": self.forecast_origin_cw,
        "forecast_target_cw": self.forecast_target_cw,
        "forecast_origin_cw1": self.forecast_origin_cw1,
        "forecast_target_cw1": self.forecast_target_cw1,
        "forecast_origin_cw2": self.forecast_origin_cw2,
        "forecast_target_cw2": self.forecast_target_cw2,
        "forecast_origin_cw3": self.forecast_origin_cw3,
        "forecast_target_cw3": self.forecast_target_cw3,
        "forecast_origin_cw4": self.forecast_origin_cw4,
        "forecast_target_cw4": self.forecast_target_cw4,
        "forecast_origin_cw5": self.forecast_origin_cw5,
        "forecast_target_cw5": self.forecast_target_cw5,
        "forecast_origin_cw6": self.forecast_origin_cw6,
        "forecast_target_cw6": self.forecast_target_cw6,
        # fast lite v1.1 版本中暂不需要后面几周的数据
        # "forecast_origin_cw7": self.forecast_origin_cw7,
        # "forecast_target_cw7": self.forecast_target_cw7,
        # "forecast_origin_cw8": self.forecast_origin_cw8,
        # "forecast_target_cw8": self.forecast_target_cw8,
        # "forecast_origin_cw9": self.forecast_origin_cw9,
        # "forecast_target_cw9": self.forecast_target_cw9,
        # "forecast_origin_cw10": self.forecast_origin_cw10,
        # "forecast_target_cw10": self.forecast_target_cw10,
        # "forecast_origin_cw11": self.forecast_origin_cw11,
        # "forecast_target_cw11": self.forecast_target_cw11,
        # "forecast_origin_cw12": self.forecast_origin_cw12,
        # "forecast_target_cw12": self.forecast_target_cw12
    }


class DemandForecastSummary():
    def __init__(self, demand_forecasts: list[DemandForecastItem]):
        self._demand_forecasts = demand_forecasts
    
    def group_by_sub_lob(self):
        '''相同sub_lob的数据，组合成一个字典，key为sub_lob, value为具体的数值'''
        # todo 需要设置total
        total_dict = {}
        for item in self._demand_forecasts:
            if item.color == 'All':
               total_dict.setdefault(item.sub_lob, {
                   "total_demand_cw1": item.demand_target_cw1,
                   "total_demand_cw2": item.demand_target_cw2
               })
        for item in self._demand_forecasts:
            # print(item.sub_lob)
            item.set_total_demand_cw1( total_dict.get(item.sub_lob).get("total_demand_cw1"))
            item.set_total_demand_cw2( total_dict.get(item.sub_lob).get("total_demand_cw2"))
        
        result = {}
        for demand_forecast in self._demand_forecasts:
            if demand_forecast.color == 'All' and demand_forecast.nand in ['eCPP机型', '普通机型', 'NPP机型']:
                continue
            result.setdefault(demand_forecast.sub_lob, []).append(demand_forecast.sublob_dict())
    
        # 按照nand,color指定顺序排序排序
        # sort_config = [
        #     ('nand', ['All','256G', '512G', '1T']),
        #     ("color", ['All', "White", "Black", "Titanium", "Gold"]),
        # ]
        # for key in result:
        #     result[key] = sorted(result[key], key=lambda x: self.__custom_sort_key(x, sort_config))
        
        # 改成按照mpn_order排序
        for key in result:
            result[key] = sorted(result[key], key=lambda x: x["mpn_order"])
        return result
    
    def __custom_sort_key(self, item, config):
        result = []
        for key, order in config:
            value = item.get(key)
            if value in order:
                result.append(order.index(value))
            else:
                result.append(len(order))
        return tuple(result)
