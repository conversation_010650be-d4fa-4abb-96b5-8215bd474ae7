from typing import List, Optional
import pandas as pd

from data.cpf_data_source import OdsFastCPFActiveSKULob
from data.mysqls.demand.ideal_demand_cpf_upload_dfa import IdealDemandCPFUploadDFA
from domain.dashboard.impl.mpn_mix_reslover import MpnMixReslover
from domain.demand.impl.dfa_upload import DFA_COLUMNS_RAW
from util.util import traditional_round


def round_value(value, precision=1):
    if value is None:
        return None
    return traditional_round(value, precision)


# 判断入参是否为None，如果None则返回None
def check_if_none(**kwargs):
    """
    仅检查传入的参数，如果某个参数值是 None 或 0（仅针对 feedback_runrate_cw2），则返回 None
    """
    for key, value in kwargs.items():
        if value is None:
            return True
    return False  # 所有已传入的参数都有效


# 判断入参是否为0，如果为0则返回None
def check_if_zero(**kwargs):
    """
    仅检查传入的参数，如果某个参数值是 0，则返回 None
    """
    for key, value in kwargs.items():
        if value == 0:
            return True
    return False  # 所有已传入的参数都有效


def set_default_value(value, default_value):
    return value if value is not None else default_value


# 如果小于0，则返回0
def set_zero(value):
    return value if value > 0 else 0


class DemandFeedBackView:
    def __init__(self,
                 suggestion_demand_cw1: int, feedback_demand_cw1: int, finalized_demand_cw1: int,
                 suggestion_demand_cw2: int, feedback_demand_cw2: int, finalized_demand_cw2: int,
                 cw1_eoh: int, cw_shipment_plan: int, cw1_shipment_plan: int, cw2_shipment_plan: int,
                 feedback_forecast_cw: float, feedback_forecast_cw1: float,
                 feedback_forecast_cw2: float, feedback_forecast_cw3: float,
                 feedback_forecast_cw4: float, feedback_forecast_cw5: float, feedback_forecast_cw6: float,
                 base_forecast_cw: float, base_forecast_cw1: float,
                 base_forecast_cw2: float, base_forecast_cw3: float,
                 base_forecast_cw4: float, base_forecast_cw5: float,
                 rtm: str = None, sub_rtm: str = None, sub_lob: str = None, mpn: str = None, nand: str = None,
                 color: str = None, sold_to_id: str = None, sold_to_name: str = None):
        self.rtm = rtm
        self.sub_rtm = sub_rtm
        self.sub_lob = sub_lob
        self.mpn = mpn
        self.nand = nand
        self.color = color
        self.sold_to_id = sold_to_id
        self.sold_to_name = sold_to_name

        self.suggestion_demand_cw1 = round_value(suggestion_demand_cw1, 0)
        self.feedback_demand_cw1 = round_value(feedback_demand_cw1, 0)
        self.finalized_demand_cw1 = round_value(finalized_demand_cw1, 0)
        self.suggestion_demand_cw2 = round_value(suggestion_demand_cw2, 0)
        self.feedback_demand_cw2 = round_value(feedback_demand_cw2, 0)
        self.finalized_demand_cw2 = round_value(finalized_demand_cw2, 0)
        self.cw1_eoh = set_default_value(cw1_eoh, 0)
        self.cw_shipment_plan = set_default_value(cw_shipment_plan, 0)
        self.cw1_shipment_plan = set_default_value(cw1_shipment_plan, 0)
        self.cw2_shipment_plan = set_default_value(cw2_shipment_plan, 0)
        self.feedback_forecast_cw = set_default_value(feedback_forecast_cw, 0)
        self.feedback_forecast_cw1 = set_default_value(feedback_forecast_cw1, 0)
        self.feedback_forecast_cw2 = set_default_value(feedback_forecast_cw2, 0)
        self.feedback_forecast_cw3 = set_default_value(feedback_forecast_cw3, 0)
        self.feedback_forecast_cw4 = set_default_value(feedback_forecast_cw4, 0)
        self.feedback_forecast_cw5 = set_default_value(feedback_forecast_cw5, 0)
        self.feedback_forecast_cw6 = set_default_value(feedback_forecast_cw6, 0)
        self.base_forecast_cw = set_default_value(base_forecast_cw, 0)
        self.base_forecast_cw1 = set_default_value(base_forecast_cw1, 0)
        self.base_forecast_cw2 = set_default_value(base_forecast_cw2, 0)
        self.base_forecast_cw3 = set_default_value(base_forecast_cw3, 0)
        self.base_forecast_cw4 = set_default_value(base_forecast_cw4, 0)
        self.base_forecast_cw5 = set_default_value(base_forecast_cw5, 0)

        self.suggestion_delta_cw1 = self.get_suggestion_delta_cw1()
        self.suggestion_delta_cw2 = self.get_suggestion_delta_cw2()
        self.feedback_delta_cw1 = self.get_feedback_delta_cw1()
        self.feedback_delta_cw2 = self.get_feedback_delta_cw2()
        self.feedback_runrate_cw1 = round_value(self.get_feedback_runrate_cw1(),0)
        self.feedback_runrate_cw2 = round_value(self.get_feedback_runrate_cw2(),0)
        self.dfa_runrate_cw1 = round_value(self.get_dfa_runrate_cw1(),0)
        self.dfa_runrate_cw2 = round_value(self.get_dfa_runrate_cw2(),0)
        self.feedback_woi_cw1 = self.get_feedback_woi_cw1()
        self.feedback_woi_cw2 = self.get_feedback_woi_cw2()
        self.finalized_woi_cw1 = self.get_finalized_woi_cw1()
        self.finalized_woi_cw2 = self.get_finalized_woi_cw2()
        self.feedback_woi_dfa_cw1 = self.get_feedback_woi_dfa_cw1()
        self.feedback_woi_dfa_cw2 = self.get_feedback_woi_dfa_cw2()
        self.finalized_woi_dfa_cw1 = self.get_finalized_woi_dfa_cw1()
        self.finalized_woi_dfa_cw2 = self.get_finalized_woi_dfa_cw2()
        self.shipment_plan_woi_cw1 = self.get_shipment_plan_woi_cw1()
        self.shipment_plan_woi_cw2 = self.get_shipment_plan_woi_cw2()

    # Delta(Final - Suggestion) = Demand (Finalized) - Demand (Suggestion)
    # lite version 修改为 feedback demand - suggestion demand (Demand 2.0 - Demand 1.0)
    def get_suggestion_delta_cw1(self):
        if check_if_none(feedback_demand_cw1=self.feedback_demand_cw1,
                         suggestion_demand_cw1=self.suggestion_demand_cw1):
            return None
        return self.feedback_demand_cw1 - self.suggestion_demand_cw1

    def get_suggestion_delta_cw2(self):
        if check_if_none(feedback_demand_cw2=self.feedback_demand_cw2,
                         suggestion_demand_cw2=self.suggestion_demand_cw2):
            return None
        return self.feedback_demand_cw2 - self.suggestion_demand_cw2

    # Delta(Final - Feedback) = Demand (Finalized) - Demand (Feedback)
    def get_feedback_delta_cw1(self):
        if check_if_none(finalized_demand_cw1=self.finalized_demand_cw1, feedback_demand_cw1=self.feedback_demand_cw1):
            return None
        return self.finalized_demand_cw1 - self.feedback_demand_cw1

    def get_feedback_delta_cw2(self):
        if check_if_none(finalized_demand_cw2=self.finalized_demand_cw2, feedback_demand_cw2=self.feedback_demand_cw2):
            return None
        return self.finalized_demand_cw2 - self.feedback_demand_cw2

    # * Runrate(Feedback Fcst)
    # * CW+1 Runrate = Avg. (CW+2 ~ CW+4 FCST)
    # * CW+2 Runrate = Avg. (CW+3 ~ CW+5 FCST)
    def get_feedback_runrate_cw1(self):
        if check_if_none(feedback_forecast_cw2=self.feedback_forecast_cw2,
                         feedback_forecast_cw3=self.feedback_forecast_cw3,
                         feedback_forecast_cw4=self.feedback_forecast_cw4):
            return None
        return (self.feedback_forecast_cw2 + self.feedback_forecast_cw3 + self.feedback_forecast_cw4) / 3

    def get_feedback_runrate_cw2(self):
        if check_if_none(feedback_forecast_cw3=self.feedback_forecast_cw3,
                         feedback_forecast_cw4=self.feedback_forecast_cw4,
                         feedback_forecast_cw5=self.feedback_forecast_cw5):
            return None
        return (self.feedback_forecast_cw3 + self.feedback_forecast_cw4 + self.feedback_forecast_cw5) / 3

    # * Runrate(DFA Fcst)
    # * CW+1 Runrate = Avg. (CW+2 ~ CW+4 FCST)
    # * CW+2 Runrate = Avg. (CW+3 ~ CW+5 FCST)
    def get_dfa_runrate_cw1(self):
        if check_if_none(base_forecast_cw2=self.base_forecast_cw2, base_forecast_cw3=self.base_forecast_cw3,
                         base_forecast_cw4=self.base_forecast_cw4):
            return None
        return (self.base_forecast_cw2 + self.base_forecast_cw3 + self.base_forecast_cw4) / 3

    def get_dfa_runrate_cw2(self):
        if check_if_none(base_forecast_cw3=self.base_forecast_cw3, base_forecast_cw4=self.base_forecast_cw4,
                         base_forecast_cw5=self.base_forecast_cw5):
            return None
        return (self.base_forecast_cw3 + self.base_forecast_cw4 + self.base_forecast_cw5) / 3

    # * Feedback Demand WOI (per Feedback Forecast）
    # * CW+1 WOI = [CW+1 Feedback Demand + CW-1 EOH + CW Shipment Plan - Sum (CW ~ CW+1 FCST)]  ÷ Avg. (CW+2 ~ CW+4 FCST)
    # * CW+2 WOI= [CW+2 Feedback Demand + CW-1 EOH + CW Shipment Plan + CW+1 Feedback Demand - Sum (CW ~ CW+2 FCST)] ÷ Avg. (CW+3 ~ CW+5 FCST)
    def get_feedback_woi_cw1(self):
        if check_if_none(feedback_runrate_cw1=self.feedback_runrate_cw1,
                         feedback_demand_cw1=self.feedback_demand_cw1,
                         cw1_eoh=self.cw1_eoh,
                         cw_shipment_plan=self.cw_shipment_plan,
                         feedback_forecast_cw=self.feedback_forecast_cw,
                         feedback_forecast_cw1=self.feedback_forecast_cw1):
            return None
        if check_if_zero(feedback_runrate_cw1=self.feedback_runrate_cw1):
            return 0

        return (self.feedback_demand_cw1 + self.cw1_eoh + self.cw_shipment_plan - (
                self.feedback_forecast_cw + self.feedback_forecast_cw1)) / self.feedback_runrate_cw1

    def get_feedback_woi_cw2(self):
        if check_if_none(feedback_runrate_cw2=self.feedback_runrate_cw2,
                         feedback_demand_cw1=self.feedback_demand_cw1,
                         feedback_demand_cw2=self.feedback_demand_cw2,
                         cw1_eoh=self.cw1_eoh,
                         cw_shipment_plan=self.cw_shipment_plan,
                         feedback_forecast_cw=self.feedback_forecast_cw,
                         feedback_forecast_cw1=self.feedback_forecast_cw1,
                         feedback_forecast_cw2=self.feedback_forecast_cw2):
            return None
        if check_if_zero(feedback_runrate_cw2=self.feedback_runrate_cw2):
            return 0
        return (self.feedback_demand_cw2 + self.cw1_eoh + self.cw_shipment_plan + self.feedback_demand_cw1 - (
                self.feedback_forecast_cw + self.feedback_forecast_cw1 + self.feedback_forecast_cw2)) / self.feedback_runrate_cw2

    # * Finalized Demand WOI (per Feedback Forecast）
    # * CW+1 WOI = [CW+1 Finalized Demand + CW-1 EOH + CW Shipment Plan - Sum (CW ~ CW+1 FCST)]  ÷ Avg. (CW+2 ~ CW+4 FCST)
    # * CW+2 WOI= [CW+2 Finalized Demand + CW-1 EOH + CW Shipment Plan + CW+1 Finalized Demand - Sum (CW ~ CW+2 FCST)] ÷ Avg. (CW+3 ~ CW+5 FCST)
    def get_finalized_woi_cw1(self):
        if check_if_none(feedback_runrate_cw1=self.feedback_runrate_cw1,
                         finalized_demand_cw1=self.finalized_demand_cw1,
                         cw1_eoh=self.cw1_eoh,
                         cw_shipment_plan=self.cw_shipment_plan,
                         feedback_forecast_cw=self.feedback_forecast_cw,
                         feedback_forecast_cw1=self.feedback_forecast_cw1):
            return None
        if check_if_zero(feedback_runrate_cw1=self.feedback_runrate_cw1):
            return None

        return (self.finalized_demand_cw1 + self.cw1_eoh + self.cw_shipment_plan - (
                self.feedback_forecast_cw + self.feedback_forecast_cw1)) / self.feedback_runrate_cw1

    def get_finalized_woi_cw2(self):
        if check_if_none(feedback_runrate_cw2=self.feedback_runrate_cw2,
                         finalized_demand_cw1=self.finalized_demand_cw1,
                         finalized_demand_cw2=self.finalized_demand_cw2,
                         cw1_eoh=self.cw1_eoh,
                         cw_shipment_plan=self.cw_shipment_plan,
                         feedback_forecast_cw=self.feedback_forecast_cw,
                         feedback_forecast_cw1=self.feedback_forecast_cw1,
                         feedback_forecast_cw2=self.feedback_forecast_cw2):
            return None
        if check_if_zero(feedback_runrate_cw2=self.feedback_runrate_cw2):
            return None

        return (self.finalized_demand_cw2 + self.cw1_eoh + self.cw_shipment_plan + self.finalized_demand_cw1 - (
                self.feedback_forecast_cw + self.feedback_forecast_cw1 + self.feedback_forecast_cw2)) / self.feedback_runrate_cw2

    # * Feedback Demand WOI (per DFA Forecast）
    # * CW+1 WOI = [CW+1 Feedback Demand + CW-1 EOH + CW Shipment Plan - Sum (CW ~ CW+1 FCST)] ÷ Avg.(CW+2 ~ CW+4 FCST)
    # * CW+2 WOI= [CW+2 Feedback Demand + CW-1 EOH + CW Shipment Plan + CW+1 Finalized Demand - Sum (CW ~ CW+2 FCST)] ÷ Avg. (CW+3 ~ CW+5 FCST)
    def get_feedback_woi_dfa_cw1(self):
        if check_if_none(dfa_runrate_cw1=self.dfa_runrate_cw1,
                         feedback_demand_cw1=self.feedback_demand_cw1,
                         cw1_eoh=self.cw1_eoh,
                         cw_shipment_plan=self.cw_shipment_plan,
                         base_forecast_cw=self.base_forecast_cw,
                         base_forecast_cw1=self.base_forecast_cw1):
            return None
        if check_if_zero(dfa_runrate_cw1=self.dfa_runrate_cw1):
            return None

        return (self.feedback_demand_cw1 + self.cw1_eoh + self.cw_shipment_plan - (
                self.base_forecast_cw + self.base_forecast_cw1)) / self.dfa_runrate_cw1

    def get_feedback_woi_dfa_cw2(self):
        if check_if_none(dfa_runrate_cw2=self.dfa_runrate_cw2,
                         feedback_demand_cw1=self.feedback_demand_cw1,
                         feedback_demand_cw2=self.feedback_demand_cw2,
                         cw1_eoh=self.cw1_eoh,
                         cw_shipment_plan=self.cw_shipment_plan,
                         base_forecast_cw=self.base_forecast_cw,
                         base_forecast_cw1=self.base_forecast_cw1,
                         base_forecast_cw2=self.base_forecast_cw2):
            return None
        if check_if_zero(dfa_runrate_cw2=self.dfa_runrate_cw2):
            return None

        return (self.feedback_demand_cw2 + self.cw1_eoh + self.cw_shipment_plan + self.feedback_demand_cw1 - (
                self.base_forecast_cw + self.base_forecast_cw1 + self.base_forecast_cw2)) / self.dfa_runrate_cw2

    # * Finalized Demand WOI (per DFA Forecast）
    # * CW+1 WOI = [CW+1 Finalized Demand + CW-1 EOH + CW Shipment Plan - Sum (CW ~ CW+1 FCST)] ÷ Avg.(CW+2 ~ CW+4 FCST)
    # * CW+2 WOI= [CW+2 Finalized Demand + CW-1 EOH + CW Shipment Plan + CW+1 Finalized Demand - Sum (CW ~ CW+2 FCST)] ÷ Avg. (CW+3 ~ CW+5 FCST)
    def get_finalized_woi_dfa_cw1(self):
        if check_if_none(dfa_runrate_cw1=self.dfa_runrate_cw1,
                         finalized_demand_cw1=self.finalized_demand_cw1,
                         cw1_eoh=self.cw1_eoh,
                         cw_shipment_plan=self.cw_shipment_plan,
                         base_forecast_cw=self.base_forecast_cw,
                         base_forecast_cw1=self.base_forecast_cw1):
            return None
        if check_if_zero(dfa_runrate_cw1=self.dfa_runrate_cw1):
            return None

        return (self.finalized_demand_cw1 + self.cw1_eoh + self.cw_shipment_plan - (
                self.base_forecast_cw + self.base_forecast_cw1)) / self.dfa_runrate_cw1

    def get_finalized_woi_dfa_cw2(self):
        if check_if_none(dfa_runrate_cw2=self.dfa_runrate_cw2,
                         finalized_demand_cw1=self.finalized_demand_cw1,
                         finalized_demand_cw2=self.finalized_demand_cw2,
                         cw1_eoh=self.cw1_eoh,
                         cw_shipment_plan=self.cw_shipment_plan,
                         base_forecast_cw=self.base_forecast_cw,
                         base_forecast_cw1=self.base_forecast_cw1,
                         base_forecast_cw2=self.base_forecast_cw2):
            return None
        if check_if_zero(dfa_runrate_cw2=self.dfa_runrate_cw2):
            return None

        return (self.finalized_demand_cw2 + self.cw1_eoh + self.cw_shipment_plan + self.finalized_demand_cw1 - (
                self.base_forecast_cw + self.base_forecast_cw1 + self.base_forecast_cw2)) / self.dfa_runrate_cw2

    #  * CW+1 WOI = [CW-1 EOH + CW Shipment Plan + CW+1 Shipment Plan - Sum (CW ~ CW+1 FCST)]  ÷ Avg. (CW+2 ~ CW+4 FCST)
    #  * CW+2 WOI = [CW-1 EOH + CW Shipment Plan + CW+1 Shipment Plan + CW+2 Shipment Plan - Sum (CW ~ CW+2 FCST)]  ÷ Avg. (CW+3 ~ CW+5 FCST)
    def get_shipment_plan_woi_cw1(self):
        if check_if_none(feedback_runrate_cw1=self.feedback_runrate_cw1,
                         cw_shipment_plan=self.cw_shipment_plan,
                         cw1_shipment_plan=self.cw1_shipment_plan,
                         feedback_forecast_cw=self.feedback_forecast_cw,
                         feedback_forecast_cw1=self.feedback_forecast_cw1,
                         ):
            return None
        if check_if_zero(feedback_runrate_cw1=self.feedback_runrate_cw1):
            return 0

        woi = (self.cw1_eoh + self.cw_shipment_plan + self.cw1_shipment_plan -
               (self.feedback_forecast_cw+self.feedback_forecast_cw1)) / self.feedback_runrate_cw1

        return set_zero(woi)

    def get_shipment_plan_woi_cw2(self):
        if check_if_none(feedback_runrate_cw2=self.feedback_runrate_cw2,
                         cw_shipment_plan=self.cw_shipment_plan,
                         cw1_shipment_plan=self.cw1_shipment_plan,
                         cw2_shipment_plan=self.cw2_shipment_plan,
                         feedback_forecast_cw=self.feedback_forecast_cw,
                         feedback_forecast_cw1=self.feedback_forecast_cw1,
                         feedback_forecast_cw2=self.feedback_forecast_cw2):
            return None
        if check_if_zero(feedback_runrate_cw2=self.feedback_runrate_cw2):
            return 0

        woi = (self.cw1_eoh + self.cw_shipment_plan + self.cw1_shipment_plan + self.cw2_shipment_plan -
               (self.feedback_forecast_cw + self.feedback_forecast_cw1 + self.feedback_forecast_cw2)) / self.feedback_runrate_cw2

        return set_zero(woi)

    def as_region_dict(self) -> dict:
        '''
        | 接口字段          | 原前端展示                          | 前端展示lite version            |
        | ----------------- | ----------------------------------- | ------------------------------- |
        | suggestion_demand | Demand (Suggestion)                 | Demand 1.0((AI ML))             |
        | feedback_demand   | Demand (Feedback)                   | Demand 2.0((Reseller))          |
        | finalized_demand  | Demand (Finalized)                  | Demand 3.0((Approved))          |
        | suggestion_delta  | Delta(Finalized-suggestion)         | delta(2.0-1.0)                  |
        | feedback_delta    | Delta(Finalized-Feedback)           | delta(3.0-2.0)                  |
        | feedback_woi      | Demand WOI（Feedback）              | Demand 2.0 WOI((vs. Reseller))  |
        | finalized_woi     | Demand WOI（Finalized）             | Demand 3.0 WOI((vs. Reseller))  |
        | feedback_woi_dfa  | -                                   | Demand 2.0 WOI （vs. DFA)——新增 |
        | finalized_woi_dfa | Finalized Demand WOI(per DFA Fcst.) | Demand 3.0 WOI （vs. DFA)       |
        | feedback_runrate  | Run-rate（Feedback）                | Reseller Runrate                |
        | dfa_runrate       | Run-rate（DFA Fcst.）               | DFA Runrate                     |
        '''
        return {
            "nand": self.nand,
            "color": self.color,
            "cw+1": {
                "suggestion_demand": self.suggestion_demand_cw1,
                "feedback_demand": self.feedback_demand_cw1,
                "finalized_demand": self.finalized_demand_cw1,
                "suggestion_delta": self.suggestion_delta_cw1,
                "feedback_delta": self.feedback_delta_cw1,
                "feedback_runrate": self.feedback_runrate_cw1,
                "dfa_runrate": self.dfa_runrate_cw1,
                "feedback_woi": round_value(self.feedback_woi_cw1),
                "finalized_woi": round_value(self.finalized_woi_cw1),
                "finalized_woi_dfa": round_value(self.finalized_woi_dfa_cw1),
                "feedback_woi_dfa": round_value(self.feedback_woi_dfa_cw1)
            },
            "cw+2": {
                "suggestion_demand": self.suggestion_demand_cw2,
                "feedback_demand": self.feedback_demand_cw2,
                "finalized_demand": self.finalized_demand_cw2,
                "suggestion_delta": self.suggestion_delta_cw2,
                "feedback_delta": self.feedback_delta_cw2,
                "feedback_runrate": self.feedback_runrate_cw2,
                "dfa_runrate": self.dfa_runrate_cw2,
                "feedback_woi": round_value(self.feedback_woi_cw2),
                "finalized_woi": round_value(self.finalized_woi_cw2),
                "finalized_woi_dfa": round_value(self.finalized_woi_dfa_cw2),
                "feedback_woi_dfa": round_value(self.feedback_woi_dfa_cw2)
            }
        }

    def as_channel_dict(self) -> dict:
        return {
            "rtm": self.rtm,
            "sub_rtm": self.sub_rtm,
            "cw+1": {
                "suggestion_demand": round_value(self.suggestion_demand_cw1),
                "feedback_demand": self.feedback_demand_cw1,
                "finalized_demand": self.finalized_demand_cw1,
                "suggestion_delta": round_value(self.suggestion_delta_cw1),
                "feedback_delta": self.feedback_delta_cw1,
                "feedback_runrate": self.feedback_runrate_cw1,
                "feedback_woi": round_value(self.feedback_woi_cw1),
                "finalized_woi": round_value(self.finalized_woi_cw1),
            },
            "cw+2": {
                "suggestion_demand": round_value(self.suggestion_demand_cw2),
                "feedback_demand": self.feedback_demand_cw2,
                "finalized_demand": self.finalized_demand_cw2,
                "suggestion_delta": round_value(self.suggestion_delta_cw2),
                "feedback_delta": self.feedback_delta_cw2,
                "feedback_runrate": self.feedback_runrate_cw2,
                "feedback_woi": round_value(self.feedback_woi_cw2),
                "finalized_woi": round_value(self.finalized_woi_cw2),
            }
        }


def process_region_demand_feedback(
        demand_feedback_df: pd.DataFrame,
        group_columns: Optional[List[str]] = None,
        is_group: bool = False
) -> List[DemandFeedBackView]:

    # 定义需要汇总的列
    agg_mapping = {
        'df_cw1': 'sum',
        'df_cw2': 'sum',
        'trial_demand_cw1': 'sum',
        'trial_demand_cw2': 'sum',
        'finalized_demand_v2_cw1': 'sum',
        'finalized_demand_v2_cw2': 'sum',
        'ub_eoh': 'sum',
        'shipment_plan_cw': 'sum',
        'shipment_plan_cw1': 'sum',
        'shipment_plan_cw2': 'sum',
        'forecast_feedback_cw': 'sum',
        'forecast_feedback_cw1': 'sum',
        'forecast_feedback_cw2': 'sum',
        'forecast_feedback_cw3': 'sum',
        'forecast_feedback_cw4': 'sum',
        'forecast_feedback_cw5': 'sum',
        'forecast_feedback_cw6': 'sum',
        'forecast_cw_ml': 'sum',
        'forecast_cw1_ml': 'sum',
        'forecast_cw2_dfa': 'sum',
        'forecast_cw3_dfa': 'sum',
        'forecast_cw4_dfa': 'sum',
        'forecast_cw5_dfa': 'sum',
    }

    # 过滤掉 DataFrame 中不存在的列
    agg_mapping = {col: func for col, func in agg_mapping.items() if col in demand_feedback_df.columns}

    # 是否进行分组聚合
    if is_group and group_columns:
        demand_feedback_df = demand_feedback_df.groupby(group_columns).agg(agg_mapping).reset_index()
    elif is_group:
        demand_feedback_df = pd.DataFrame(demand_feedback_df.agg(agg_mapping)).T.reset_index(drop=True)

    # 转换为 DemandFeedBackView 对象
    demand_feedback_list = [
        DemandFeedBackView(
            suggestion_demand_cw1=row.df_cw1,
            feedback_demand_cw1=row.trial_demand_cw1,
            finalized_demand_cw1=row.get('finalized_demand_v2_cw1', row.trial_demand_cw1),
            suggestion_demand_cw2=row.df_cw2,
            feedback_demand_cw2=row.trial_demand_cw2,
            finalized_demand_cw2=row.get('finalized_demand_v2_cw2', row.trial_demand_cw2),
            cw1_eoh=row.ub_eoh,
            cw_shipment_plan=row.shipment_plan_cw,
            cw1_shipment_plan=row.shipment_plan_cw1,
            cw2_shipment_plan=row.shipment_plan_cw2,
            feedback_forecast_cw=row.forecast_feedback_cw,
            feedback_forecast_cw1=row.forecast_feedback_cw1,
            feedback_forecast_cw2=row.forecast_feedback_cw2,
            feedback_forecast_cw3=row.forecast_feedback_cw3,
            feedback_forecast_cw4=row.forecast_feedback_cw4,
            feedback_forecast_cw5=row.forecast_feedback_cw5,
            feedback_forecast_cw6=row.forecast_feedback_cw6,
            base_forecast_cw=row.forecast_cw_ml,
            base_forecast_cw1=row.forecast_cw1_ml,
            base_forecast_cw2=row.forecast_cw2_dfa,
            base_forecast_cw3=row.forecast_cw3_dfa,
            base_forecast_cw4=row.forecast_cw4_dfa,
            base_forecast_cw5=row.forecast_cw5_dfa,
            sub_lob=getattr(row, 'sub_lob', None),
            mpn=getattr(row, 'mpn', None),
            nand="All" if is_group and (group_columns is None or 'nand' not in group_columns) else getattr(row, 'nand', None),
            color="All" if is_group and (group_columns is None or 'color' not in group_columns) else getattr(row, 'color', None)
        )
        for _, row in demand_feedback_df.iterrows()
    ]
    return demand_feedback_list


def get_region_feedback_list(demand_feedback_df: pd.DataFrame, group_columns: list[str]=None, is_group: bool=False) -> List[DemandFeedBackView]:
    return process_region_demand_feedback(demand_feedback_df, group_columns=group_columns, is_group=is_group)


def process_sold_to_demand_feedback(demand_feedback_df: pd.DataFrame, is_group: bool,
                              group_columns: list[str] = None) -> List[DemandFeedBackView]:
    """
    对需求反馈数据进行聚合。
    """
    agg_mapping = {
        'final_demand_cw1': 'sum',
        'final_demand_cw2': 'sum',
        'trial_demand_cw1': 'sum',
        'trial_demand_cw2': 'sum',
        'finalized_demand_v2_cw1': 'sum',
        'finalized_demand_v2_cw2': 'sum',
        'ub_eoh_lw': 'sum',
        'shipment_plan_cw': 'sum',
        'shipment_plan_cw1': 'sum',
        'shipment_plan_cw2': 'sum',
        'forecast_feedback_cw': 'sum',
        'forecast_feedback_cw1': 'sum',
        'forecast_feedback_cw2': 'sum',
        'forecast_feedback_cw3': 'sum',
        'forecast_feedback_cw4': 'sum',
        'forecast_feedback_cw5': 'sum',
        'forecast_feedback_cw6': 'sum',
        'forecast_cw': 'sum',
        'forecast_cw1': 'sum',
        'forecast_cw2': 'sum',
        'forecast_cw3': 'sum',
        'forecast_cw4': 'sum',
        'forecast_cw5': 'sum',
    }

    # 过滤掉不存在的列
    agg_mapping = {col: func for col, func in agg_mapping.items() if col in demand_feedback_df.columns}

    if is_group:
        if group_columns:
            demand_feedback_df = demand_feedback_df.groupby(group_columns).agg(agg_mapping).reset_index()
        else:
            demand_feedback_df = pd.DataFrame(demand_feedback_df.agg(agg_mapping)).T.reset_index(drop=True)

    return [DemandFeedBackView(
        suggestion_demand_cw1=row.final_demand_cw1,
        feedback_demand_cw1=row.trial_demand_cw1,
        finalized_demand_cw1=row.get('finalized_demand_v2_cw1', row.trial_demand_cw1),
        suggestion_demand_cw2=row.final_demand_cw2,
        feedback_demand_cw2=row.trial_demand_cw2,
        finalized_demand_cw2=row.get('finalized_demand_v2_cw2', row.trial_demand_cw2),
        cw1_eoh=row.ub_eoh_lw,
        cw_shipment_plan=row.shipment_plan_cw,
        cw1_shipment_plan=row.shipment_plan_cw1,
        cw2_shipment_plan=row.shipment_plan_cw2,
        feedback_forecast_cw=row.forecast_feedback_cw,
        feedback_forecast_cw1=row.forecast_feedback_cw1,
        feedback_forecast_cw2=row.forecast_feedback_cw2,
        feedback_forecast_cw3=row.forecast_feedback_cw3,
        feedback_forecast_cw4=row.forecast_feedback_cw4,
        feedback_forecast_cw5=row.forecast_feedback_cw5,
        feedback_forecast_cw6=row.forecast_feedback_cw6,
        base_forecast_cw=row.forecast_cw,
        base_forecast_cw1=row.forecast_cw1,
        base_forecast_cw2=row.forecast_cw2,
        base_forecast_cw3=row.forecast_cw3,
        base_forecast_cw4=row.forecast_cw4,
        base_forecast_cw5=row.forecast_cw5,
        rtm="All" if is_group and (not group_columns or 'rtm' not in group_columns) else getattr(row, 'rtm', None),
        sub_rtm="All" if is_group and (not group_columns or 'sub_rtm' not in group_columns) else getattr(row, 'sub_rtm', None),
        sold_to_id=getattr(row, 'sold_to_id', None),
        sold_to_name=getattr(row, 'sold_to_name', None),
        sub_lob=getattr(row, 'sub_lob', None),
        mpn=getattr(row, 'mpn_id', None),
        nand="All" if is_group and (group_columns is None or 'nand' not in group_columns) else getattr(row, 'nand', None),
        color="All" if is_group and (group_columns is None or 'color' not in group_columns) else getattr(row, 'color', None)
    ) for _, row in demand_feedback_df.iterrows()]


def get_channel_feedback_list(demand_feedback_df: pd.DataFrame, is_group: bool,
                              group_columns: list[str] = None) -> list[DemandFeedBackView]:
    return process_sold_to_demand_feedback(demand_feedback_df, is_group, group_columns)


def process_pool_demand_feedback(
        demand_sold_to_df: pd.DataFrame,
        group_columns: Optional[List[str]] = None,
        is_group: bool = False
) -> List[DemandFeedBackView]:

    # 定义需要汇总的列
    agg_mapping = {
        'ub_eoh': 'sum',
        'shipment_plan_cw': 'sum',
        'shipment_plan_cw1': 'sum',
        'shipment_plan_cw2': 'sum',
        'forecast_cw_ml': 'sum',
        'forecast_cw1_ml': 'sum',
        'forecast_cw2_sales': 'sum',
        'forecast_cw3_sales': 'sum',
        'forecast_cw4_sales': 'sum',
        'forecast_cw5_sales': 'sum',
        'forecast_cw6_sales': 'sum',
    }

    # 过滤掉 DataFrame 中不存在的列
    agg_mapping = {col: func for col, func in agg_mapping.items() if col in demand_sold_to_df.columns}

    # 是否进行分组聚合
    if is_group and group_columns:
        demand_sold_to_df = demand_sold_to_df.groupby(group_columns).agg(agg_mapping).reset_index()
    elif is_group:
        demand_sold_to_df = pd.DataFrame(demand_sold_to_df.agg(agg_mapping)).T.reset_index(drop=True)

    # 转换为 DemandFeedBackView 对象
    demand_feedback_list = [
        DemandFeedBackView(
            suggestion_demand_cw1=0,
            feedback_demand_cw1=0,
            finalized_demand_cw1=0,
            suggestion_demand_cw2=0,
            feedback_demand_cw2=0,
            finalized_demand_cw2=0,
            cw1_eoh=row.ub_eoh,
            cw_shipment_plan=row.shipment_plan_cw,
            cw1_shipment_plan=row.shipment_plan_cw1,
            cw2_shipment_plan=row.shipment_plan_cw2,
            feedback_forecast_cw=row.forecast_cw_ml,
            feedback_forecast_cw1=row.forecast_cw1_ml,
            feedback_forecast_cw2=row.forecast_cw2_sales,
            feedback_forecast_cw3=row.forecast_cw3_sales,
            feedback_forecast_cw4=row.forecast_cw4_sales,
            feedback_forecast_cw5=row.forecast_cw5_sales,
            feedback_forecast_cw6=row.forecast_cw6_sales,
            base_forecast_cw=0,
            base_forecast_cw1=0,
            base_forecast_cw2=0,
            base_forecast_cw3=0,
            base_forecast_cw4=0,
            base_forecast_cw5=0,
            sub_lob=getattr(row, 'sub_lob', None),
            mpn=getattr(row, 'mpn', None),
            nand="All" if is_group and (group_columns is None or 'nand' not in group_columns) else getattr(row, 'nand', None),
            color="All" if is_group and (group_columns is None or 'color' not in group_columns) else getattr(row, 'color', None),
            sold_to_id=getattr(row, 'sold_to_id', None)
        )
        for _, row in demand_sold_to_df.iterrows()
    ]
    return demand_feedback_list


def get_pool_list(demand_sold_to_df: pd.DataFrame, is_group: bool,
                             group_columns: list[str] = None) -> list[DemandFeedBackView]:
    return process_pool_demand_feedback(demand_sold_to_df, is_group=is_group, group_columns=group_columns)


def get_dfa_from_user_published(fiscal_week: str) -> list[dict]:
    '''获取用户上传并发布的dfa数据'''
    user_upload_dfa_list = IdealDemandCPFUploadDFA.query_list_by_week(fiscal_week)
    if not user_upload_dfa_list:
        sub_lobs = OdsFastCPFActiveSKULob.list_sub_lobs("China mainland", "iPhone")

        df = pd.DataFrame([], columns=DFA_COLUMNS_RAW)
        df["sub_lob"] = sub_lobs
        df["fiscal_week"] = fiscal_week
        df["cw"] = 0
        df["cw1"] = 0
        df["cw2"] = 0
        df["cw3"] = 0
        df["cw4"] = 0
        df["cw5"] = 0
        df["cw6"] = 0
        df["cw7"] = 0
        df["cw8"] = 0
        user_upload_dfa_list = df.to_dict(orient='records')
    return user_upload_dfa_list


def dfa_with_mix_by_sublob_mpn_df(dfa_data: list[dict], sublob_mpn_mix_data: list[dict]):
    '''将dfa数据与sublob_mpn_mix_data进行合并，得到按照mpn级别的dfa数据'''
    dfa_df = pd.DataFrame(dfa_data)
    sublob_mpn_mix_df = pd.DataFrame(sublob_mpn_mix_data)
    new_df = sublob_mpn_mix_df.merge(dfa_df, on=['sub_lob'], how='left')
    new_df['forecast_cw_dfa'] = new_df['cw'] * new_df['actual_mix']
    new_df['forecast_cw1_dfa'] = new_df['cw1'] * new_df['actual_mix']
    new_df['forecast_cw2_dfa'] = new_df['cw2'] * new_df['actual_mix']
    new_df['forecast_cw3_dfa'] = new_df['cw3'] * new_df['actual_mix']
    new_df['forecast_cw4_dfa'] = new_df['cw4'] * new_df['actual_mix']
    new_df['forecast_cw5_dfa'] = new_df['cw5'] * new_df['actual_mix']
    new_df['forecast_cw6_dfa'] = new_df['cw6'] * new_df['actual_mix']
    new_df['forecast_cw7_dfa'] = new_df['cw7'] * new_df['actual_mix']
    new_df['forecast_cw8_dfa'] = new_df['cw8'] * new_df['actual_mix']
    return new_df


def get_country_mpn_feedback_list(demand_feedback_soldto_df: pd.DataFrame, 
                                  is_group: bool, group_columns: list[str] = None) -> list[DemandFeedBackView]:
    agg_mapping = {
        'final_demand_cw1': 'sum',
        'final_demand_cw2': 'sum',
        'trial_demand_cw1': 'sum',
        'trial_demand_cw2': 'sum',
        'finalized_demand_v2_cw1': 'sum',
        'finalized_demand_v2_cw2': 'sum',
        'ub_eoh_lw': 'sum',
        'shipment_plan_cw': 'sum',
        'shipment_plan_cw1': 'sum',
        'shipment_plan_cw2': 'sum',
        'forecast_feedback_cw': 'sum',
        'forecast_feedback_cw1': 'sum',
        'forecast_feedback_cw2': 'sum',
        'forecast_feedback_cw3': 'sum',
        'forecast_feedback_cw4': 'sum',
        'forecast_feedback_cw5': 'sum',
        'forecast_feedback_cw6': 'sum',
        'forecast_cw': 'sum',
        'forecast_cw1': 'sum',
        'forecast_cw2': 'sum',
        'forecast_cw3': 'sum',
        'forecast_cw4': 'sum',
        'forecast_cw5': 'sum',
    }
     # 0、过滤掉不存在的列
    agg_mapping = {col: func for col, func in agg_mapping.items() if col in demand_feedback_soldto_df.columns}
    # 1、先进行一步汇总
    demand_feedback_soldto_df = demand_feedback_soldto_df.groupby(["fiscal_qtr_week_name",'lob', 'sub_lob', 'nand', 'color', 'mpn_id']).agg(agg_mapping).reset_index()
    # 2、将dfa sub_lob、mpn 的数据替换forecast_cw ~ forecast_cw5
    try:
        fiscal_week =  demand_feedback_soldto_df["fiscal_qtr_week_name"].values[0]
    except IndexError:
        fiscal_week = None
    dfa_data = get_dfa_from_user_published(fiscal_week)
    sublob_mpn_mix_data = MpnMixReslover().merge_mpn_mix_and_adjust(fiscal_week, "China mainland", "iPhone", "", False, False)
    dfa_by_sublob_mpn_df = dfa_with_mix_by_sublob_mpn_df(dfa_data, sublob_mpn_mix_data)
    # 为了方便merge，重命名mpn，并取所需要的字段
    dfa_by_sublob_mpn_df.rename(columns={'mpn': 'mpn_id'}, inplace=True)
    dfa_by_sublob_mpn_df = dfa_by_sublob_mpn_df[['sub_lob', 'mpn_id', 'forecast_cw_dfa', 'forecast_cw1_dfa', 'forecast_cw2_dfa', 'forecast_cw3_dfa', 'forecast_cw4_dfa', 'forecast_cw5_dfa', 'forecast_cw6_dfa', 'forecast_cw7_dfa', 'forecast_cw8_dfa']]
    demand_feedback_soldto_df = demand_feedback_soldto_df.merge(dfa_by_sublob_mpn_df, on=['sub_lob', 'mpn_id'], how='left')
    demand_feedback_soldto_df["forecast_cw"] = demand_feedback_soldto_df["forecast_cw_dfa"]
    demand_feedback_soldto_df["forecast_cw1"] = demand_feedback_soldto_df["forecast_cw1_dfa"]
    demand_feedback_soldto_df["forecast_cw2"] = demand_feedback_soldto_df["forecast_cw2_dfa"]
    demand_feedback_soldto_df["forecast_cw3"] = demand_feedback_soldto_df["forecast_cw3_dfa"]
    demand_feedback_soldto_df["forecast_cw4"] = demand_feedback_soldto_df["forecast_cw4_dfa"]
    demand_feedback_soldto_df["forecast_cw5"] = demand_feedback_soldto_df["forecast_cw5_dfa"]
    return process_sold_to_demand_feedback(demand_feedback_soldto_df, is_group, group_columns)
