class QuantileLevelConfig:
    def __init__(self,
                 quantile_level: float,
                 quantile_min: int,
                 quantile_max: int) -> None:
        self.quantile_level = quantile_level
        self.quantile_min = quantile_min
        self.quantile_max = quantile_max


class WeekInfo:
    def __init__(self,
                 fiscal_week_name: str,
                 ml_forecast: int,
                 quantile_level_configs: list[QuantileLevelConfig] = None,
                 quantile_level: float = None,
                 is_locked: bool = False) -> None:
        self.fiscal_week_name = fiscal_week_name
        self.ml_forecast = ml_forecast
        self.quantile_level = quantile_level  # 用户配置的quantile_level
        self.quantile_level_configs = quantile_level_configs
        self.is_locked = is_locked


class Resp:
    def __init__(self,
                 rtm: str,
                 sub_lob: str,
                 sub_rtm: str,
                 week_info: list[WeekInfo] = None) -> None:
        self.rtm = rtm
        self.sub_rtm = sub_rtm
        self.sub_lob = sub_lob
        self.week_info = week_info


class UserSetting:

    def __init__(self,
                 fiscal_week_name: str,
                 rtm: str,
                 sub_lob: str,
                 sub_rtm: str,
                 range_level_cw: float = None,
                 range_level_cw1: float = None,
                 range_level_cw2: float = None,
                 range_level_cw3: float = None,
                 range_level_cw4: float = None,
                 range_level_cw5: float = None,
                 range_level_cw6: float = None,
                 range_level_cw7: float = None,
                 range_level_cw8: float = None,
                 range_level_cw9: float = None,
                 range_level_cw10: float = None,
                 range_level_cw11: float = None,
                 range_level_cw12: float = None,
                 update_time: str = None) -> None:
        self.fiscal_week_name = fiscal_week_name
        self.rtm = rtm
        self.sub_rtm = sub_rtm
        self.sub_lob = sub_lob
        self.range_level_cw = range_level_cw
        self.range_level_cw1 = range_level_cw1
        self.range_level_cw2 = range_level_cw2
        self.range_level_cw3 = range_level_cw3
        self.range_level_cw4 = range_level_cw4
        self.range_level_cw5 = range_level_cw5
        self.range_level_cw6 = range_level_cw6
        self.range_level_cw7 = range_level_cw7
        self.range_level_cw8 = range_level_cw8
        self.range_level_cw9 = range_level_cw9
        self.range_level_cw10 = range_level_cw10
        self.range_level_cw11 = range_level_cw11
        self.range_level_cw12 = range_level_cw12
        self.update_time = update_time

    def as_dict(self):
        return {
            "fiscal_week_name": self.fiscal_week_name,
            "rtm": self.rtm,
            "sub_lob": self.sub_lob,
            "sub_rtm": self.sub_rtm,
            "range_level_cw": self.range_level_cw,
            "range_level_cw1": self.range_level_cw1,
            "range_level_cw2": self.range_level_cw2,
            "range_level_cw3": self.range_level_cw3,
            "range_level_cw4": self.range_level_cw4,
            "range_level_cw5": self.range_level_cw5,
            "range_level_cw6": self.range_level_cw6,
            "range_level_cw7": self.range_level_cw7,
            "range_level_cw8": self.range_level_cw8,
            "range_level_cw9": self.range_level_cw9,
            "range_level_cw10": self.range_level_cw10,
            "range_level_cw11": self.range_level_cw11,
            "range_level_cw12": self.range_level_cw12
        }

