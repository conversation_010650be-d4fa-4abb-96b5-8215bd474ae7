import copy

from data.mysqls.demand.rtm_sales_forecast_upload import FastLiteRTMSalesForecastUpload
from domain.demand.entity.const import RTMS_NO_RETAIL
from domain.end_to_end.entity.response_data import Resp, WeekInfo, UserSetting
from util.const import Enterprise, Education


class VarianceQueryResolver:
    def __init__(self,
                 user_variance_settings: list[UserSetting],
                 fcst_variances: list[FastLiteRTMSalesForecastUpload],
                 week_names: list[str]) -> None:
        self.user_variance_settings = user_variance_settings
        self.fcst_variances = fcst_variances
        self.week_names = week_names
        self.variance_configs = self.transfer_to_variance_resp()

    def aggregate_fcst_variances(self):
        # 按 (sub_rtm, sub_lob) 聚合 fcst_variances 中的数据，
        aggregated = {}
        num_weeks = len(self.week_names)
        for item in self.fcst_variances:
            key_tuple = (item.sub_rtm, item.sub_lob)
            if key_tuple not in aggregated:
                aggregated[key_tuple] = {
                    'rtm': item.rtm,
                    'sub_rtm': item.sub_rtm,
                    'sub_lob': item.sub_lob,
                    'forecasts': [0] * num_weeks
                }
            # 第一周字段直接用 forecast_cw，其它周用 forecast_cw{i}，若不存在则默认为 0
            aggregated[key_tuple]['forecasts'][0] += item.forecast_cw
            for i in range(1, num_weeks):
                aggregated[key_tuple]['forecasts'][i] += getattr(item, f"forecast_cw{i}", 0)
        return aggregated

    def build_week_info(self, forecasts):
        """
        根据 week_names 和对应的 forecasts 列表构造 WeekInfo 列表。
        前 5 周的 WeekInfo 设置 is_locked 为 True。
        """
        week_info_list = []
        for index, week_name in enumerate(self.week_names):
            week_info_list.append(
                WeekInfo(
                    fiscal_week_name=week_name,
                    ml_forecast=forecasts[index],
                    is_locked=(index < 7)
                )
            )
        return week_info_list

    def transfer_to_variance_resp(self) -> dict[str, Resp]:
        """
        主函数：先对 fcst_variances 数据进行聚合，
        再根据聚合结果和 week_names 构造每个 Resp 对象，
        最后返回 key 为 get_key(sub_rtm, sub_lob) 的字典。
        """
        if not self.fcst_variances:
            return {}

        # 1. 数据聚合
        aggregated = self.aggregate_fcst_variances()

        # 2. 构造返回结果
        variance_resp = {}
        for (sub_rtm, sub_lob), data in aggregated.items():
            week_info_list = self.build_week_info(data['forecasts'])
            key = get_key(sub_rtm, sub_lob)
            variance_resp[key] = Resp(
                rtm=data['rtm'],
                sub_rtm=sub_rtm,
                sub_lob=sub_lob,
                week_info=week_info_list
            )
        return variance_resp

    def transfer_to_user_setting_dict(self) -> dict[
        str, int]:
        # 返回的数据应如下
        # {"FY24Q1W1_mono_iphone 15": 0.05}
        res = {}
        for item in self.user_variance_settings:
            for index, week_name in enumerate(self.week_names):
                key = get_week_key(week_name, item.sub_rtm, item.sub_lob)
                if index == 0:
                    res[key] = item.range_level_cw
                else:
                    res[key] = getattr(item, f"range_level_cw{index}")
        return res

    def as_dict(self):
        res = []
        transfer_to_user_setting_dict = self.transfer_to_user_setting_dict()
        education_enterprise_list = [Enterprise, Education]
        for key, variance_resp in self.variance_configs.items():
            forecast_variances = []
            for week_info in variance_resp.week_info:
                week_key = get_week_key(week_info.fiscal_week_name, variance_resp.sub_rtm, variance_resp.sub_lob)
                forecast_variance = {
                    "fiscal_week_name": week_info.fiscal_week_name,
                    "ml_forecast": week_info.ml_forecast,
                    # 如果没有配置或者为None，则使用默认值0.15
                    "feedback_setting": transfer_to_user_setting_dict.get(week_key) if transfer_to_user_setting_dict.get(week_key) is not None else 0.15,
                    "is_locked": week_info.is_locked
                }
                forecast_variances.append(forecast_variance)
            res.append({
                "rtm": variance_resp.rtm,
                "sub_rtm": variance_resp.sub_rtm if variance_resp.rtm not in education_enterprise_list else 'All',
                "sub_lob": variance_resp.sub_lob,
                "forecast_variance": forecast_variances
            })
        # 先按照 RTMS_NO_RETAIL 的顺序对rtm排序，再对sub rtm排序
        res.sort(key=lambda item: (RTMS_NO_RETAIL.index(item["rtm"]) if item["rtm"] in RTMS_NO_RETAIL else len(RTMS_NO_RETAIL), item["sub_rtm"]))
        return res


def get_week_key(fiscal_week_name: str, sub_rtm: str, sub_lob: str) -> str:
    return f"{fiscal_week_name}_{sub_rtm}_{sub_lob}"


def get_key(sub_rtm: str, sub_lob: str) -> str:
    return f"{sub_rtm}_{sub_lob}"
