import json
from datetime import datetime, timedelta

from data.mysqls.end_to_end.mybusiness_forecast_feedback import MybusinessForecastFeedback
from data.mysqls.end_to_end.mybusiness_forecast_feedback_view import MybusinessForecastFeedbackView
from data.mysqls.end_to_end.mybusiness_forecast_feedback_wi import MybusinessForecastFeedbackWi
from data.mysqls.end_to_end.mybusiness_module_switch import MybusinessModuleSwitch
from domain.demand.entity.module_switch_enum import ModuleSwitchEnum
from domain.end_to_end.entity.demand_forecast_entity import DemandForecastSummary
from kit.custom_date.custom_week_date import CustomWeekDate

mybiz_feedback_module_switch = 'fast_e2e'
delay_minutes = 20  # 数据延迟20分钟切换数据兜底的表(数据相对于三审时间窗是查10分钟后开始跑数据, 再预留10分钟数据兜底)


def java_like_day_of_week(dt=None):
    """
    模拟 java.util.Calendar 的 weekday 输出
    周日 = 1, 周一 = 2, ..., 周六 = 7
    """
    dt = dt or datetime.now()
    py_weekday = dt.weekday()  # Monday=0, Sunday=6
    return 1 if py_weekday == 6 else py_weekday + 2


def is_before_ddl(ddl_end_date):
    """
    是否在配置的截止时间之前
    """
    current_time = datetime.now()
    ten_minutes_ago = current_time - timedelta(minutes=delay_minutes)  # 延迟时间窗35分钟
    current_week, current_hour = java_like_day_of_week(ten_minutes_ago), ten_minutes_ago.hour
    current_minute, current_second = ten_minutes_ago.minute, ten_minutes_ago.second
    ddl_week, ddl_hour = ddl_end_date['day_of_week'], ddl_end_date['hour']
    ddl_minute, ddl_second = ddl_end_date['minute'], ddl_end_date['second']
    if current_week < ddl_week:
        return True
    if current_week == ddl_week:
        if current_hour < ddl_hour:
            return True
        if current_hour == ddl_hour:
            if current_minute < ddl_minute:
                return True
            if current_minute == ddl_minute:
                if current_second < ddl_second:
                    return True
    return False


def get_demand_forecast_feedback_dict(reseller_id: str, fiscal_week: str, rtm:str, version: int, sub_lob: str) -> dict:
    # 经销商纬度使用原始feedback_forecast表, Reference下使用视图
    if reseller_id:
        forecast_feedback_data = (
            MybusinessForecastFeedback.get_sublob_data_by_rollup(fiscal_week=fiscal_week, resellser_id=reseller_id,
                                                                 rtm=rtm, version=version, sub_lob=sub_lob)
        )
    else:
        # 20250512 时间窗之前使用view表数据，时间窗之后使用数据兜底数据
        # week_date_by_module = MybusinessModuleSwitch.query_date_by_module(mybiz_feedback_module_switch)
        # ddl_end_date = json.loads(week_date_by_module.end_date)

        # 20250523 又通知：三审结束后, 兜底数据才有
        current_time = datetime.now()
        delay_minutes_ago = current_time - timedelta(minutes=delay_minutes)  # 延迟时间窗20分钟
        if CustomWeekDate(ModuleSwitchEnum.FAST_E2E_THIRD_REJECT.value).is_before_ddl(default_current_date=delay_minutes_ago):
            db_clazz = MybusinessForecastFeedbackView
        else:
            db_clazz = MybusinessForecastFeedbackWi

        # 获取原始数据库with rollup数据
        forecast_feedback_data = (
            db_clazz.get_sublob_data_by_rollup(fiscal_week=fiscal_week, sub_lob=sub_lob, rtm=rtm)
        )
    # 整合数据
    ret = DemandForecastSummary(forecast_feedback_data).group_by_sub_lob()
    
    return ret
