import io
import logging
from datetime import datetime
from typing import Dict, Any

from data.databend.end_to_end.ml_fcst_quantile import MLFcstQuantile
from data.fiscal_year_week import FiscalYearWeek
from data.mysqls.demand.rtm_sales_forecast_upload import FastLiteRTMSalesForecastUpload
from data.mysqls.end_to_end.range_level_setting import RangeLevelSetting
from domain.demand.entity.const import IDEAL_DEMAND
from domain.demand.entity.state import RTM_BINARY_MAPPING
from domain.demand.impl.state_machine import StateProxy
from domain.end_to_end.entity.response_data import UserSetting
from domain.end_to_end.entity.variance_query_resolver import VarianceQueryResolver
from domain.end_to_end.entity.variance_save_resolver import transfer_variance_data_to_update_data, \
    transfer_batch_data_to_update_data
from kit.custom_sort import CustomSort


def get_menu(fiscal_week_name: str, lob: str) -> Dict[str, Any]:
    # 获取最新发布时间
    latest_publish_time = RangeLevelSetting.get_variance_publish_time(fiscal_week_name, lob)

    # 获取子产品线和预测周
    menu_data = FastLiteRTMSalesForecastUpload.get_menu_data(fiscal_week_name, lob)
    sub_lobs = sorted(set(item.sub_lob for item in menu_data))

    # 提取并去重 fiscal_week 和 fiscal_week_name，按 fiscal_week 排序
    forecast_weeks, fiscal_week_year = get_fiscal_weeks(fiscal_week_name)

    # 构造 rtms 数据
    rtms_dict = {}
    for item in menu_data:
        if item.rtm not in rtms_dict:
            rtms_dict[item.rtm] = set()
        rtms_dict[item.rtm].add(item.sub_rtm)

    rtms = [
        {
            "rtm": rtm,
            "sub_rtms": sorted(list(sub_rtms))
        }
        for rtm, sub_rtms in rtms_dict.items()
    ]

    return {
        "fiscal_week": fiscal_week_year,
        "sublobs": CustomSort.sort_iphone_sublobs(sub_lobs),
        "forecast_weeks": forecast_weeks,
        "latest_publish_time": latest_publish_time.strftime('%Y-%m-%d %H:%M:%S') if latest_publish_time else None,
        "rtms": rtms
    }


def get_fiscal_weeks(fiscal_week_name, n: int = 12):
    ret = []
    res = FiscalYearWeek.get_cw_and_next_n_week_by_qtr_week_name(fiscal_week_name, n)
    for result in res:
        ret.append(result.fiscal_qtr_week_name)
    return ret, res[0].fiscal_week_year


def get_list(fiscal_week: str, lob: str) -> Dict[str, Any]:
    # 根据状态机状态判断该RTM是否已经上传且提交过sales forecast数据
    status_obj = StateProxy(fiscal_week=fiscal_week, demand=IDEAL_DEMAND)
    rtm_status = status_obj.rtm_state
    rtms = []
    for rtm, value in RTM_BINARY_MAPPING.items():
        if value|rtm_status == rtm_status:
            rtms.append(rtm)

    # 获取原始数据
    fcst_variances = FastLiteRTMSalesForecastUpload.query_by_fiscal_week_and_lob(fiscal_week_name=fiscal_week, lob=lob, rtms=rtms)
    weeks, fiscal_week_year = get_fiscal_weeks(fiscal_week, 6)
    # 获取已设置的数据
    user_variance_settings = RangeLevelSetting.get_settings(fiscal_week_name=fiscal_week, lob=lob)

    # 将fcst_variances和user_variance_settings按照sub_lob分组
    fcst_variances_group = {}
    for item in fcst_variances:
        key = item.sub_lob
        if key not in fcst_variances_group:
            fcst_variances_group[key] = []
        fcst_variances_group[key].append(item)
    user_variance_settings_group = {}
    for item in user_variance_settings:
        key = item.sub_lob
        if key not in user_variance_settings_group:
            user_variance_settings_group[key] = []
        user_variance_settings_group[key].append(item)

    # 处理数据
    all_return_data = {}
    for sub_lob in fcst_variances_group:
        return_data = VarianceQueryResolver(user_variance_settings=user_variance_settings_group.get(sub_lob, []),
                                            fcst_variances=fcst_variances_group[sub_lob],
                                            week_names=weeks)
        all_return_data[sub_lob] = return_data.as_dict()
    return all_return_data


def publish(update_list: dict, weeks: list[str], fiscal_week: int, fiscal_week_name: str, lob, operator: str):
    variance_settings = []
    for sub_lob, data in update_list.items():
        variance_settings.extend(transfer_variance_data_to_update_data(data, weeks))
    objs = deal_update_data(variance_settings, fiscal_week, fiscal_week_name, lob, operator, is_published=True)

    return RangeLevelSetting.update_setting(objs=objs, fields_to_insert=fields_to_insert,
                                            fields_to_update=fields_to_update)


def update_setting(update_list: dict, weeks: list[str], fiscal_week: int, fiscal_week_name: str, lob,
                   operator: str) -> None:
    variance_settings = []
    for sub_lob, data in update_list.items():
        variance_settings.extend(transfer_variance_data_to_update_data(data, weeks))
    objs = deal_update_data(variance_settings, fiscal_week, fiscal_week_name, lob, operator, is_published=False)

    RangeLevelSetting.update_setting(objs=objs, fields_to_insert=fields_to_insert,
                                     fields_to_update=fields_to_update)


def batch_update(fiscal_week_name: str, lob: str, update_weeks: list[str], fiscal_week: int, week_names: list[str],
                 rtms: list[dict], sub_lobs: list[str], variance: float, operator: str) -> None:
    # 先获取所有已经配置过的数据
    user_variance_settings = RangeLevelSetting.get_settings(fiscal_week_name=fiscal_week_name, lob=lob)
    # 处理数据
    variance_settings = transfer_batch_data_to_update_data(fiscal_week_name, update_weeks, week_names, rtms, sub_lobs,
                                                           variance, user_variance_settings)
    objs = deal_update_data(variance_settings, fiscal_week, fiscal_week_name, lob, operator, is_published=False)

    RangeLevelSetting.update_setting(objs=objs, fields_to_insert=fields_to_insert,
                                     fields_to_update=fields_to_update)


fields_to_insert = ['fiscal_week', 'lob', 'rtm',
                    'range_level_cw7', 'range_level_cw8', 'range_level_cw9',
                    'range_level_cw10', 'range_level_cw11', 'range_level_cw12',
                    'variance_create_time', 'variance_created_by',
                    'variance_update_time', 'variance_updated_by', 'variance_publish_time', 'variance_is_published']

fields_to_update = ['range_level_cw7', 'range_level_cw8', 'range_level_cw9',
                    'range_level_cw10', 'range_level_cw11', 'range_level_cw12',
                    'variance_update_time', 'variance_updated_by', 'variance_publish_time', 'variance_is_published']


def deal_update_data(update_data: list[UserSetting], fiscal_week: int, fiscal_week_name: str, lob: str,
                     operator: str, is_published: bool):
    # 将update_data转成dict
    objs = [item.as_dict() for item in update_data]
    now = datetime.now()
    for obj in objs:
        obj['fiscal_week'] = fiscal_week
        obj['fiscal_week_name'] = fiscal_week_name
        obj['lob'] = lob
        obj['variance_created_by'] = operator
        obj['variance_create_time'] = now
        obj['variance_updated_by'] = operator
        obj['variance_update_time'] = now
        obj['variance_publish_time'] = now if is_published else None
        obj['variance_is_published'] = is_published
    return objs
