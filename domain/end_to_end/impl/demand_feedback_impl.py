import io
from typing import Dict, Any, Optional
from flask import g
import numpy as np
import pandas as pd

from data.databend.end_to_end.forecast_feedback_by_region import ForecastFeedbackRegion
from data.databend.end_to_end.forecast_feedback_by_soldto import ForecastFeedbackSoldto
from data.mysqls.end_to_end.feedback_demand_by_soldto import DemandFeedbackBySoldTo
from data.mysqls.end_to_end.mybiz_feedback_demand_by_soldto_view import MybizDemandFeedbackBySoldToView
from domain.demand.entity.const import RTMS_NO_RETAIL
from domain.demand.impl.carrier_mpn import replace_rp_mpn_to_carrier_mpn
from domain.end_to_end.entity.demand_feedback_query import get_channel_feedback_list, get_country_mpn_feedback_list
from domain.permission.impl.permission_impl import filtered_t1_disti_ids_by_permission
from kit.custom_sort import CustomSort
from util.const import StrRTMCPF
from util.util import common_sort_df

NAND_SORT_RULE = ['All', '64GB', '128GB', '256GB', '512GB', '1TB']
PUBLISHED_STATUS = 1


def get_menu(fiscal_week_name: str, lob: str) -> Dict[str, Any]:
    # 获取菜单
    menu_data_df = ForecastFeedbackRegion.get_menu_data(fiscal_week_name, lob, PUBLISHED_STATUS)

    sub_lobs = set(menu_data_df['sub_lob']) if set(menu_data_df['sub_lob']) else []
    nand = set(menu_data_df['nand']) if set(menu_data_df['nand']) else []
    color = set(menu_data_df['color']) if set(menu_data_df['color']) else []

    return {
        "sublobs": CustomSort.sort_iphone_sublobs(sub_lobs),
        "nand": sorted(nand, key=lambda x: NAND_SORT_RULE.index(x)),
        "color": sorted(color)
    }


def convert_np_types(obj):
    """递归转换字典或列表中的 numpy 类型为 Python 内置类型"""
    if isinstance(obj, dict):
        return {k: convert_np_types(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [convert_np_types(i) for i in obj]
    elif isinstance(obj, np.integer):
        return int(obj)
    elif isinstance(obj, np.floating):
        return float(obj)
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    else:
        return obj


def get_published_status(fiscal_week_name: str, lob: str):
    is_published = DemandFeedbackBySoldTo.get_publish_status(fiscal_week_name=fiscal_week_name, lob=lob)
    return is_published


def get_region_list(fiscal_week_name: str, lob: str, sub_lobs: list[str], rtm: str = None, nand: list[str] = None,
                    color: list[str] = None):
    # 获取列表
    data_df = DemandFeedbackBySoldTo.get_data_df(fiscal_week_name=fiscal_week_name, lob=lob, sub_lobs=sub_lobs,
                                                 rtm=rtm, nand=nand, color=color)
    # 将list_data_df转为list
    if data_df.empty:
        data_df = ForecastFeedbackSoldto.get_list_data(fiscal_week_name=fiscal_week_name, lob=lob, sub_lobs=sub_lobs,
                                                       rtm=rtm, nand=nand, color=color)
    result_list = []
    if not data_df.empty:
        # 将data_df中nan替换为None
        data_df = data_df.replace({pd.NA: None, np.nan: None})
        mpn_list = get_country_mpn_feedback_list(demand_feedback_soldto_df=data_df, is_group=True, group_columns=['nand', 'color'])
        nand_list = get_country_mpn_feedback_list(demand_feedback_soldto_df=data_df, is_group=True, group_columns=['nand'])
        all_list = get_country_mpn_feedback_list(demand_feedback_soldto_df=data_df, is_group=True)

        all_list.extend(nand_list)
        all_list.extend(mpn_list)
        # 如果nand为All，则放第一，否则按照字符顺序排列
        all_list = sorted(
            all_list,
            key=lambda x: (
                NAND_SORT_RULE.index(x.nand) if x.nand in NAND_SORT_RULE else 999,
                (0, x.color) if x.color == 'All' else (1, x.color)
            )
        )
        result_list = [convert_np_types(item.as_region_dict()) for item in all_list]
    return {"list": result_list}


def get_channel_list(fiscal_week_name: str, lob: str, sub_lobs: list[str], nand: list[str], color: list[str]):
    # 获取列表
    data_df = DemandFeedbackBySoldTo.get_data_df(fiscal_week_name=fiscal_week_name, lob=lob, sub_lobs=sub_lobs,
                                                 nand=nand, color=color, published_status=PUBLISHED_STATUS)
    # 将list_data_df转为list
    if data_df.empty:
        data_df = ForecastFeedbackSoldto.get_list_data(fiscal_week_name=fiscal_week_name, lob=lob, sub_lobs=sub_lobs,
                                                       nand=nand, color=color, publish_status=PUBLISHED_STATUS)

    result_list = []
    if not data_df.empty:
        # 将data_df中NaN替换为None
        data_df = data_df.replace({pd.NA: None, np.nan: None})
        sub_rtm_list = get_channel_feedback_list(demand_feedback_df=data_df, is_group=True, group_columns=['rtm', 'sub_rtm'])
        rtm_list = get_channel_feedback_list(demand_feedback_df=data_df, is_group=True, group_columns=['rtm'])
        all_list = get_channel_feedback_list(demand_feedback_df=data_df, is_group=True)

        all_list.extend(rtm_list)
        all_list.extend(sub_rtm_list)
        # 如果rtm为All，则放第一，如果rtm不在RTMS_NO_RETAIL里，则放最后，若在RTMS_NO_RETAIL里，则按照RTMS_NO_RETAIL的顺序排列
        all_list = sorted(
            all_list,
            key=lambda x: (
                0 if x.rtm == 'All' else (1 if x.rtm in RTMS_NO_RETAIL else 2),
                RTMS_NO_RETAIL.index(x.rtm) if x.rtm in RTMS_NO_RETAIL else 0,
                x.sub_rtm
            )
        )
        result_list = [convert_np_types(item.as_channel_dict()) for item in all_list]
    return {"list": result_list}


def get_channel_download_file(fiscal_week_name: str, lob: str, rtm: str = None, file_type: str = None):
    '''下载Demand 3.0的数据'''
    if rtm == StrRTMCPF:
        file_name = f"iPhone_Final Sold-to Demand_{fiscal_week_name}.xlsx"
    else:
        file_name = f"iPhone_Final Sold-to Demand_{fiscal_week_name}_{rtm}.xlsx"

    demand_feedback_df = get_channel_download_data(fiscal_week_name, lob, rtm, file_type)

    # 返回文件流，不在系统中创建文件
    excel_file_bytes = io.BytesIO()
    demand_feedback_df.to_excel(excel_file_bytes, index=False)
    excel_file_bytes.seek(0)
    return file_name, excel_file_bytes


def get_channel_download_data(fiscal_week_name: str, lob: str, rtm: str = None, file_type: str = None):
    '''
    1、CP&F下载JD合并版：JD Self-run类型下仅包括虚拟经销商（id = 1118511）的数据结果。
    2、RTM和CP&F下载JD拆分版：JD Self-run类型下取用各经销商独立、真实的数据源，且排除虚拟经销商（id = 1118511）的数据结果。
    3、20250522 需要按照RTM的角色和数据权限来过滤reseller_id
    '''
    rtm_filter = rtm
    published_status = PUBLISHED_STATUS
    reseller_ids: Optional[list[str]] = filtered_t1_disti_ids_by_permission(rtm, g)
    excel_header = [
            "RTM", "Sub-RTM", "Sold-to ID", "Sold-to Name", "Sub-LOB", "Nand", "Color", "MPN",
            "Demand 2.0 CW+1", "Demand 3.0 CW+1", "Demand 2.0 CW+2", "Demand 3.0 CW+2"
        ]

    if rtm == StrRTMCPF:
        rtm_filter = None
        excel_header = [
            "RTM", "Sub-RTM", "Sold-to ID", "Sold-to Name", "Sub-LOB", "Nand", "Color", "MPN",
            "Demand 2.0 CW+1", "Demand 3.0 CW+1", "Demand 2.0 CW+2", "Demand 3.0 CW+2",
            "Shipment Plan CW", "Shipment Plan CW+1", "Shipment Plan CW+2", "EOH",
            "Reseller Runrate CW+1", "Reseller Runrate CW+2", "Feedback Forecast CW", "Feedback Forecast CW+1",
            "Feedback Forecast CW+2", "Feedback Forecast CW+3", "Feedback Forecast CW+4", "Feedback Forecast CW+5",
            "Feedback Forecast CW+6"
        ]

    if rtm == StrRTMCPF and file_type == 'combined':
        # 尝试从主表取数据
        data_df = DemandFeedbackBySoldTo.get_data_df(fiscal_week_name=fiscal_week_name, lob=lob,
                                                    rtm=rtm_filter, published_status=published_status,
                                                    reseller_ids=reseller_ids)
        # 如果为空，fallback 到另一个表
        if data_df.empty:
            data_df = ForecastFeedbackSoldto.get_list_data(fiscal_week_name=fiscal_week_name, 
                                                           lob=lob, rtm=rtm_filter,
                                                           publish_status=published_status,
                                                           reseller_ids=reseller_ids)
    else:
        data_df = MybizDemandFeedbackBySoldToView.get_data_df(
            fiscal_week_name=fiscal_week_name, lob=lob,
            rtm=rtm_filter, published_status=published_status, reseller_ids=reseller_ids
        )
        # 补齐所需要的列，这样后面就可以统一处理
        supplemented_columns = [
             'final_demand_cw1', 'final_demand_cw2',
             'ub_eoh_lw', 'shipment_plan_cw', 'shipment_plan_cw1', 'shipment_plan_cw2', 
             'forecast_feedback_cw', 'forecast_feedback_cw1', 'forecast_feedback_cw2', 
             'forecast_feedback_cw3', 'forecast_feedback_cw4', 'forecast_feedback_cw5', 'forecast_feedback_cw6', 
             'forecast_cw', 'forecast_cw1', 'forecast_cw2', 'forecast_cw3', 'forecast_cw4', 'forecast_cw5',
        ]
        data_df[supplemented_columns] = None

    # 转换为 list of dicts → DataFrame
    demand_feedback_list = get_channel_feedback_list(demand_feedback_df=data_df, is_group=False)
    demand_feedback_df = pd.DataFrame([item.__dict__ for item in demand_feedback_list])

    if demand_feedback_df.empty:
        return pd.DataFrame(columns=excel_header)

    # 字段重命名
    rename_columns = {
        "rtm": "RTM", "sub_rtm": "Sub-RTM", "sold_to_id": "Sold-to ID", "sold_to_name": "Sold-to Name",
        "sub_lob": "Sub-LOB", "nand": "Nand", "color": "Color", "mpn": "MPN",
        "feedback_demand_cw1": "Demand 2.0 CW+1", "feedback_demand_cw2": "Demand 2.0 CW+2",
        "finalized_demand_cw1": "Demand 3.0 CW+1", "finalized_demand_cw2": "Demand 3.0 CW+2",
        "cw_shipment_plan": "Shipment Plan CW", "cw1_shipment_plan": "Shipment Plan CW+1",
        "cw2_shipment_plan": "Shipment Plan CW+2", "cw1_eoh": "EOH",
        "feedback_runrate_cw1": "Reseller Runrate CW+1", "feedback_runrate_cw2": "Reseller Runrate CW+2",
        "feedback_forecast_cw": "Feedback Forecast CW", "feedback_forecast_cw1": "Feedback Forecast CW+1",
        "feedback_forecast_cw2": "Feedback Forecast CW+2", "feedback_forecast_cw3": "Feedback Forecast CW+3",
        "feedback_forecast_cw4": "Feedback Forecast CW+4", "feedback_forecast_cw5": "Feedback Forecast CW+5",
        "feedback_forecast_cw6": "Feedback Forecast CW+6"
    }
    demand_feedback_df.rename(columns=rename_columns, inplace=True)

    # demand 2.0 保持空
    # 需要填充 0 的字段统一处理（存在才填充）
    fill_zero_cols = [
        "Demand 3.0 CW+1", "Demand 3.0 CW+2",
        "Shipment Plan CW", "Shipment Plan CW+1", "Shipment Plan CW+2", "EOH",
        "Reseller Runrate CW+1", "Reseller Runrate CW+2", "Feedback Forecast CW", "Feedback Forecast CW+1",
        "Feedback Forecast CW+2", "Feedback Forecast CW+3", "Feedback Forecast CW+4", "Feedback Forecast CW+5",
        "Feedback Forecast CW+6"
    ]
    for col in fill_zero_cols:
        if col in demand_feedback_df.columns:
            demand_feedback_df[col] = demand_feedback_df[col].fillna(0)

    # 只保留指定列并排序
    demand_feedback_df = demand_feedback_df[excel_header]
    sort_key = ["RTM", "Sub-RTM", "Sold-to ID", "Sub-LOB", "Nand", "Color", "MPN"]
    demand_feedback_df = common_sort_df(demand_feedback_df, sort_key)

    if not data_df.empty:
        replace_rp_mpn_to_carrier_mpn(demand_feedback_df, 'MPN', "RTM")

    return demand_feedback_df
