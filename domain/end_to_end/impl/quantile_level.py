
from enum import Enum


class QuantileLevel(Enum):
    NARROW = "Narrow"
    MID = "Mid"
    WIDE = "Wide"


def determine_quantile_level(quantile_value: float) -> str:
    try:
        # 将数值转换为字符串，获取小数部分
        value_str = str(float(quantile_value))
        if '.' in value_str:
            integer_part, decimal_part = value_str.split('.')
        else:
            integer_part, decimal_part = value_str, ''
        
        # 根据小数位数决定百分比精度
        decimal_length = len(decimal_part)
        if decimal_length <= 2:
            percentage = f"{quantile_value * 100:.0f}%"
        else:
            percentage = f"{quantile_value * 100:.{decimal_length - 2}f}%"
        
        if quantile_value <= 0.5:
            return f"{QuantileLevel.NARROW.value}({percentage})"
        elif 0.5 < quantile_value < 0.9:
            return f"{QuantileLevel.MID.value}({percentage})"
        elif quantile_value >= 0.9:
            return f"{QuantileLevel.WIDE.value}({percentage})"
        return None
    except (TypeError, ValueError):
        return None
