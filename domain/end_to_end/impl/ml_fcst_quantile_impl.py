import io
import traceback
from concurrent.futures import Thread<PERSON>oolExecutor
from datetime import datetime
from typing import Dict, Any, List

from flask import g
import pandas as pd

from data.databend.end_to_end.ml_fcst_quantile import MLFcstQuantile
from data.databend.end_to_end.ml_fcst_quantile_view import MLFcstQuantileView
from data.fiscal_year_week import FiscalYearWeek
from data.mysqls.end_to_end.range_level_setting import RangeLevelSetting
from domain.demand.impl.carrier_mpn import replace_rp_mpn_to_carrier_mpn
from domain.end_to_end.entity.response_data import UserSetting
from domain.end_to_end.entity.quantile_query_resolver import QuantileQueryResolver
from domain.end_to_end.entity.quantile_save_resolver import transfer_quantile_data_to_update_data, \
    transfer_batch_data_to_update_data
from domain.end_to_end.impl.quantile_level import determine_quantile_level
from domain.permission.impl.permission_impl import filtered_soldto_ids_by_permission
from kit.custom_sort import CustomSort
from util.conf import logger
from util.util import common_sort_df


def get_menu(fiscal_week_name: str, lob: str) -> Dict[str, Any]:
    # 获取最新发布时间
    latest_publish_time = RangeLevelSetting.get_quantile_publish_time(fiscal_week_name, lob)

    # 获取子产品线和预测周
    menu_data = MLFcstQuantileView.get_menu_data(fiscal_week_name, lob)
    sub_lobs = sorted(set(item.sub_lob for item in menu_data))

    # 提取并去重 fiscal_week 和 fiscal_week_name，按 fiscal_week 排序
    forecast_weeks, fiscal_week_year = get_fiscal_weeks(fiscal_week_name)

    # 构造 rtms 数据
    rtms_dict = {}
    for item in menu_data:
        if item.rtm not in rtms_dict:
            rtms_dict[item.rtm] = set()
        rtms_dict[item.rtm].add(item.sub_rtm)

    rtms = [
        {
            "rtm": rtm,
            "sub_rtms": sorted(list(sub_rtms))
        }
        for rtm, sub_rtms in rtms_dict.items()
    ]

    return {
        "fiscal_week": fiscal_week_year,
        "sublobs": CustomSort.sort_iphone_sublobs(sub_lobs),
        "forecast_weeks": forecast_weeks,
        "latest_publish_time": latest_publish_time.strftime('%Y-%m-%d %H:%M:%S') if latest_publish_time else None,
        "rtms": rtms
    }


def get_fiscal_weeks(fiscal_week_name):
    ret = []
    res = FiscalYearWeek.get_cw_and_next_n_week_by_qtr_week_name(fiscal_week_name, 6)
    for result in res:
        ret.append(result.fiscal_qtr_week_name)
    return ret, res[0].fiscal_week_year


def get_list(fiscal_week: str, lob: str) -> Dict[str, Any]:
    # 获取原始数据
    fcst_quantiles = MLFcstQuantileView.get_list_data(fiscal_week, lob)
    weeks, fiscal_week_year = get_fiscal_weeks(fiscal_week)
    # 获取已设置的数据
    user_quantile_settings = RangeLevelSetting.get_settings(fiscal_week_name=fiscal_week, lob=lob)

    # 将fcst_quantiles和user_quantile_settings按照sub_lob分组
    fcst_quantiles_group = {}
    for item in fcst_quantiles:
        key = item.sub_lob
        if key not in fcst_quantiles_group:
            fcst_quantiles_group[key] = []
        fcst_quantiles_group[key].append(item)
    user_quantile_settings_group = {}
    for item in user_quantile_settings:
        key = item.sub_lob
        if key not in user_quantile_settings_group:
            user_quantile_settings_group[key] = []
        user_quantile_settings_group[key].append(item)

    # 处理数据
    all_return_data = {}
    for sub_lob in fcst_quantiles_group:
        return_data = QuantileQueryResolver(user_quantile_settings=user_quantile_settings_group.get(sub_lob, []),
                                            fcst_quantiles=fcst_quantiles_group[sub_lob],
                                            week_names=weeks)
        all_return_data[sub_lob] = return_data.as_dict()
    return all_return_data


FIELDS_TO_UPDATE = ['range_level_cw', 'range_level_cw1', 'range_level_cw2', 'range_level_cw3', 'range_level_cw4',
                    'range_level_cw5','range_level_cw6',
                    'quantile_update_time', 'quantile_updated_by', 'quantile_publish_time', 'quantile_is_published']
FIELDS_TO_INSERT = ['fiscal_week', 'lob', 'rtm',
                    'range_level_cw', 'range_level_cw1', 'range_level_cw2', 'range_level_cw3', 'range_level_cw4',
                    'range_level_cw5','range_level_cw6',
                    'quantile_create_time', 'quantile_created_by',
                    'quantile_update_time', 'quantile_updated_by', 'quantile_publish_time', 'quantile_is_published']


def publish(update_list: dict, weeks: list[str], fiscal_week: int, fiscal_week_name: str, lob, operator: str):
    quantile_settings = []
    for sub_lob, data in update_list.items():
        quantile_settings.extend(transfer_quantile_data_to_update_data(data, weeks))
    objs = deal_update_data(quantile_settings, fiscal_week, fiscal_week_name, lob, operator, is_published=True)

    return RangeLevelSetting.update_setting(objs=objs, fields_to_insert=FIELDS_TO_INSERT, fields_to_update=FIELDS_TO_UPDATE)


def update_setting(update_list: dict, weeks: list[str], fiscal_week: int, fiscal_week_name: str, lob,
                   operator: str) -> None:
    quantile_settings = []
    for sub_lob, data in update_list.items():
        quantile_settings.extend(transfer_quantile_data_to_update_data(data, weeks))
    objs = deal_update_data(quantile_settings, fiscal_week, fiscal_week_name, lob, operator, is_published=False)
    RangeLevelSetting.update_setting(objs=objs, fields_to_insert=FIELDS_TO_INSERT, fields_to_update=FIELDS_TO_UPDATE)


def batch_update(fiscal_week_name: str, lob: str, update_weeks: list[str], fiscal_week: int, week_names: list[str],
                 rtms: list[dict], sub_lobs: list[str], quantile_level: float, operator: str) -> None:
    # 先获取所有已经配置过的数据
    user_quantile_settings = RangeLevelSetting.get_settings(fiscal_week_name=fiscal_week_name, lob=lob)
    # 处理数据
    quantile_settings = transfer_batch_data_to_update_data(fiscal_week_name, update_weeks, week_names, rtms, sub_lobs,
                                                           quantile_level, user_quantile_settings)
    objs = deal_update_data(quantile_settings, fiscal_week, fiscal_week_name, lob, operator, is_published=False)
    RangeLevelSetting.update_setting(objs=objs, fields_to_insert=FIELDS_TO_INSERT, fields_to_update=FIELDS_TO_UPDATE)


def deal_update_data(update_data: list[UserSetting], fiscal_week: int, fiscal_week_name: str, lob: str,
                     operator: str, is_published: bool):
    # 将update_data转成dict
    objs = [item.as_dict() for item in update_data]
    now = datetime.now()
    for obj in objs:
        obj['fiscal_week'] = fiscal_week
        obj['fiscal_week_name'] = fiscal_week_name
        obj['lob'] = lob
        obj['quantile_created_by'] = operator
        obj['quantile_create_time'] = now
        obj['quantile_updated_by'] = operator
        obj['quantile_update_time'] = now
        obj['quantile_publish_time'] = now if is_published else None
        obj['quantile_is_published'] = is_published
    return objs


def get_download_file(fiscal_week_name: str, lob: str, rtm: str):
    try:
        # 过滤权限
        soldto_ids = filtered_soldto_ids_by_permission(rtm, g)
        
        forecast_df = MLFcstQuantile.get_download_data(fiscal_week_name, lob, rtm, soldto_ids)
        update_time = forecast_df['update time'].max()
        user_variance_settings = RangeLevelSetting.get_settings(fiscal_week_name=fiscal_week_name, lob=lob, quantile_is_published=1)
        if user_variance_settings:
            update_time = user_variance_settings[0].update_time
        # 创建文件名
        file_name = f"Sold-to ML Forecast {fiscal_week_name}.xlsx"

        # 处理数据
        result_list = match_forecast_with_config(forecast_df, user_variance_settings)
        if not result_list:
            # 若没有匹配的数据，给data_df附上表头
            data_df = pd.DataFrame(columns=[
                'Fiscal Week', 'RTM', 'Sub-RTM', 'LOB', 'Sub-LOB', 'sold-to id', 'sold-to name', 'MPN',
                'ml forecast_cw', 'quantile_level_cw', 'quantile_min_cw', 'quantile_max_cw',
                'ml forecast_cw1', 'quantile_level_cw1', 'quantile_min_cw1', 'quantile_max_cw1',
                'ml forecast_cw2', 'quantile_level_cw2', 'quantile_min_cw2', 'quantile_max_cw2',
                'ml forecast_cw3', 'quantile_level_cw3', 'quantile_min_cw3', 'quantile_max_cw3',
                'ml forecast_cw4', 'quantile_level_cw4', 'quantile_min_cw4', 'quantile_max_cw4',
                'ml forecast_cw5', 'quantile_level_cw5', 'quantile_min_cw5', 'quantile_max_cw5',
                'ml forecast_cw6', 'quantile_level_cw6', 'quantile_min_cw6', 'quantile_max_cw6', 'update time'])
        else:
            data_df = pd.DataFrame(result_list)
            data_df['update time'] = update_time

        if not data_df.empty:
            replace_rp_mpn_to_carrier_mpn(data_df, 'MPN', "RTM")
            sort_key = ["RTM", "Sub-RTM", "sold-to id", "Sub-LOB", 'MPN']
            data_df = common_sort_df(data_df, sort_key)
        # 返回文件流，不在系统中创建文件
        excel_file_bytes = io.BytesIO()
        data_df.to_excel(excel_file_bytes, index=False)
        excel_file_bytes.seek(0)
        return file_name, excel_file_bytes
    except Exception as e:
        logger.error(traceback.format_exc())
        return False


class UserSetting:
    def __init__(self, fiscal_week_name, rtm, sub_rtm, sub_lob, range_level_cw, range_level_cw1, range_level_cw2,
                 range_level_cw3, range_level_cw4, range_level_cw5, range_level_cw6):
        self.fiscal_week_name = fiscal_week_name
        self.rtm = rtm
        self.sub_rtm = sub_rtm
        self.sub_lob = sub_lob
        self.range_level_cw = range_level_cw
        self.range_level_cw1 = range_level_cw1
        self.range_level_cw2 = range_level_cw2
        self.range_level_cw3 = range_level_cw3
        self.range_level_cw4 = range_level_cw4
        self.range_level_cw5 = range_level_cw5
        self.range_level_cw6 = range_level_cw6


def process_group(key, group, config_map):
    fiscal_week, rtm, sub_rtm, lob, sub_lob, sold_to_id, sold_to_name, mpn = key
    config = config_map.get((rtm, sub_rtm, sub_lob))

    # 对当前组排序，选取第二条记录（若只有一条则取唯一记录）作为默认配置依据
    sorted_group = sorted(group, key=lambda x: x['quantile_level'])
    default_row = sorted_group[1] if len(sorted_group) > 1 else sorted_group[0]
    default_config = UserSetting(
        fiscal_week_name=fiscal_week,
        rtm=rtm,
        sub_rtm=sub_rtm,
        sub_lob=sub_lob,
        range_level_cw=default_row['quantile_level'],
        range_level_cw1=default_row['quantile_level'],
        range_level_cw2=default_row['quantile_level'],
        range_level_cw3=default_row['quantile_level'],
        range_level_cw4=default_row['quantile_level'],
        range_level_cw5=default_row['quantile_level'],
        range_level_cw6=default_row['quantile_level'],
    )

    # 构建 quantile_level 到记录的映射，方便后续快速查找
    quantile_lookup = {float(row['quantile_level']): row for row in group}

    # 构造返回记录的基本信息
    record = {
        "Fiscal Week": fiscal_week,
        "RTM": rtm,
        "Sub-RTM": sub_rtm,
        "LOB": lob,
        "Sub-LOB": sub_lob,
        "sold-to id": sold_to_id,
        "sold-to name": sold_to_name,
        "MPN": mpn
    }

    # 各周的配置属性与 forecast 列名映射关系
    week_mappings = {
        'cw': ('range_level_cw', 'ml forecast_cw', 'quantile_min_cw', 'quantile_max_cw'),
        'cw1': ('range_level_cw1', 'ml forecast_cw1', 'quantile_min_cw1', 'quantile_max_cw1'),
        'cw2': ('range_level_cw2', 'ml forecast_cw2', 'quantile_min_cw2', 'quantile_max_cw2'),
        'cw3': ('range_level_cw3', 'ml forecast_cw3', 'quantile_min_cw3', 'quantile_max_cw3'),
        'cw4': ('range_level_cw4', 'ml forecast_cw4', 'quantile_min_cw4', 'quantile_max_cw4'),
        'cw5': ('range_level_cw5', 'ml forecast_cw5', 'quantile_min_cw5', 'quantile_max_cw5'),
        'cw6': ('range_level_cw6', 'ml forecast_cw6', 'quantile_min_cw6', 'quantile_max_cw6'),
    }
    for week, (config_attr, forecast_col, forecast_min, forecast_max) in week_mappings.items():
        target_quantile = getattr(config, config_attr, None) if config else None
        if target_quantile is None:
            target_quantile = getattr(default_config, config_attr, None)

        # 将target_quantile转为float
        target_quantile = float(target_quantile) if target_quantile else None

        if target_quantile is None or target_quantile not in quantile_lookup:
            record[forecast_col] = record[forecast_min] = record[forecast_max] = None
        else:
            matched_row = quantile_lookup[target_quantile]
            record[forecast_col] = matched_row.get(forecast_col, None)
            record[forecast_min] = matched_row.get(forecast_min, None)
            record[forecast_max] = matched_row.get(forecast_max, None)
            record[f"quantile_level_{week}"] = determine_quantile_level(target_quantile)

    return record


def match_forecast_with_config(forecast_df: pd.DataFrame, user_settings: List[UserSetting]) -> List[dict]:
    config_map = {(s.rtm, s.sub_rtm, s.sub_lob): s for s in user_settings}
    forecast_data = forecast_df.to_dict(orient='records')
    # 按关键字段分组
    grouped_data = {}
    for row in forecast_data:
        key = (row['Fiscal Week'], row['RTM'], row['Sub-RTM'], row['LOB'], row['Sub-LOB'], row['sold-to id'],
               row['sold-to name'], row['MPN'])
        if key not in grouped_data:
            grouped_data[key] = []
        grouped_data[key].append(row)

    result_list = []
    with ThreadPoolExecutor(max_workers=10) as executor:
        futures = [executor.submit(process_group, key, group, config_map) for key, group in grouped_data.items()]
        for future in futures:
            result_list.append(future.result())

    return result_list
