from functools import wraps
from typing import Optional
from flask import g

from data.reseller_soldto_mapping import ResellerSoldtoMapping
from data.user_data import TblUserRole
from domain.permission.entity.permission import Permission
from util.const import ErrCode, Ret, ShieldPrsId


def get_fast_e2e_permissions(rtm: str, platform_prefix: str = 'FAST', 
                             person_id: str = None):
    '''
    获取FAST E2E的权限, 每个RTM的角色权限都是互斥的，唯一的， 只有Multi/Carrier/Online的Expert角色有数据权限
    eg. {'person_id': '2700965151', 'email': '<EMAIL>', 'role_name': 'Expert', 'data_permission': ['1118511', '1230755']}
    '''
    all_permissions = TblUserRole.get_role_platform_by_user(platform_prefix, person_id)
    
    # 初始化权限字典
    permission_dict = {}
    
    for item in all_permissions:
        person_permissions: Permission = permission_dict.setdefault(item.person_id, Permission(
            person_id=item.person_id,
            role_name=None,
            data_permission=[],
            email=item.email,
        ))
        
        ROLE_PERMISSION = platform_prefix + ' ' + rtm
        DATA_PERMISSION = platform_prefix + ' Data Permission ' + rtm
        if item.platform not in [ROLE_PERMISSION, DATA_PERMISSION]:
            continue
        
        # 处理 Role Permission
        if item.platform == ROLE_PERMISSION:
            person_permissions.role_name = person_permissions.role_name or item.role_name

        # 处理 Data Permission
        if item.platform == DATA_PERMISSION:
            # t1_disti_id
            person_permissions.data_permission.append(item.role_name)

    return {
        key: (value.to_dict() if hasattr(value, "to_dict") else value)
        for key, value in permission_dict.items()
    }


MONO_ACCOUNT_INTERFACE_DATA_PERMISSION_T1_DISTI_IDS = [
    "4382119", "3399101", "3413742", "3363850",
    "4227887", "3356989", "3355652", "3483969", "3382045", "3489164", "3415577", "4252675", "3389757", "3412629",
    "1294441", "3434506", "3426057", "3495268", "3435975", "3405858", "4291673", "3569041", "649296", "1035217",
    "3582295", "3449268"
]

# 装饰器函数，用来加载当前用户的权限
def fast_e2e_permissions(http_request):
    def decorator(function):
        @wraps(function)
        def wrapper(*args, **kwargs):
            # before request
            person_id = http_request.headers.get(ShieldPrsId)
            # POST 方法从body中获取rtm
            # GET 方法从params中获取rtm
            if http_request.method == 'POST':
                rtm = http_request.json.get('rtm', None) or http_request.args.get('channel', None)
            else:
                rtm = http_request.args.get('rtm', None) or http_request.args.get('channel', None)
            if rtm is None:
                return {
                    Ret.Code: ErrCode.Permissions,
                    Ret.Msg: 'Need rtm'
                }
            if person_id is None:
                return {
                    Ret.Code: ErrCode.Permissions,
                    Ret.Msg: 'Need Apple Connect'
                }
            # eg. {'person_id': '2700965151', 'email': '<EMAIL>', 'role_name': 'Expert', 'data_permission': ['1118511', '1230755']}
            g.fast_e2e_permission_dict = get_fast_e2e_permissions(rtm=rtm, person_id=person_id).get(person_id)
            # dispatch request view
            result = function(*args, **kwargs)
            return result
        return wrapper
    return decorator


def filtered_t1_disti_ids_by_permission(rtm: str, g) -> Optional[list[str]]:
    if rtm == 'Mono' and g.fast_e2e_permission_dict and g.fast_e2e_permission_dict['role_name'] == 'Account Interface':
        return MONO_ACCOUNT_INTERFACE_DATA_PERMISSION_T1_DISTI_IDS
    elif rtm in ['Multi', 'Online', 'Carrier'] and g.fast_e2e_permission_dict and g.fast_e2e_permission_dict['role_name'] == 'Expert':
        return g.fast_e2e_permission_dict['data_permission']
    else:
        return None


def filtered_soldto_ids_by_permission(rtm: str, g) -> Optional[list[str]]:
    filtered_t1_disti_ids = filtered_t1_disti_ids_by_permission(rtm, g)
    if filtered_t1_disti_ids is None:
        return None
    return ResellerSoldtoMapping.query_soldto_ids_by_reseller_ids(filtered_t1_disti_ids)
