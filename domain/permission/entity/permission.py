'''
[{
    "role_name":"Sales Reviewer",
    "data_permission": [{"sub_rtm": "OTC", "disti_t1": "Telling"}, {"sub_rtm": "OTC", "disti_t1": "<PERSON><PERSON><PERSON>"}],
    "email": "bao<PERSON><PERSON>_<PERSON><EMAIL>",
    "review_status": 10,
},
{
    "role_name":"Sales Reviewer",
    "data_permission": [{"sub_rtm": "OTC", "disti_t1": "Total"}],
    "email": "<EMAIL>",
    "review_status": 10
}]
'''
from dataclasses import dataclass, field
from typing import Optional
from dataclasses_json import dataclass_json


@dataclass_json
@dataclass
class DataPermissionItem:
    sub_rtm: str
    t1_disti_name_en: str
    t1_disti_id: str
    t2_reseller_id: Optional[str] = None
    
    # def __post_init__(self):
    #     # 替换 sub_rtm 的值为映射值，如果找不到映射则保留原值
    #     self.sub_rtm = RESELLER_SUBRTM_MAPPING.get(self.sub_rtm, self.sub_rtm)

@dataclass_json
@dataclass
class Permission:
    person_id: str
    email: str
    role_name: str
    data_permission: list[DataPermissionItem] = field(default_factory=list)
