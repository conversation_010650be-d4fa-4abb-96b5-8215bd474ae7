import datetime
from decimal import Decimal, ROUND_HALF_UP
from enum import Enum


class SmartTowerInvPointsLevel(Enum):
    MPN = 'MPN'
    NAND = 'NAND'
    SUB_LOB = 'SubLoB'


class OutstockSoVelocityWaive:
    def __init__(self, fiscal_week_year: int, fiscal_qtr_week_name: str, rtm: str, sub_rtm: str, level: str,
                 lob: str, sub_lob: str, nand: str, mpn: str, sku: str, so_velocity_cw: float,  so_velocity_lw1: float,
                 so_velocity_lw2: float, inv_point: int, outstock_point: int,
                 outstock_ratio: float, update_time: datetime.datetime):
        self.fiscal_week_year = fiscal_week_year
        self.fiscal_qtr_week_name = fiscal_qtr_week_name
        self.rtm = rtm
        self.sub_rtm = sub_rtm
        self.level = level
        self.lob = lob
        self.sub_lob = sub_lob
        self.nand = nand
        self.mpn = mpn
        self.sku = sku
        self.so_velocity_cw = so_velocity_cw
        self.so_velocity_lw1 = so_velocity_lw1
        self.so_velocity_lw2 = so_velocity_lw2
        self.inv_point = inv_point
        self.outstock_point = outstock_point
        self.outstock_ratio = outstock_ratio
        self.update_time = update_time

    @staticmethod
    def convert_to_thousands(value):
        if value:
            return "{:,.0f}".format(value)
        else:
            return '-'

    @staticmethod
    def format_percentage(rate: float):
        """精准的四舍五入"""
        if rate is None or rate == '':
            return "-"

        # 如结果为 100% 或 0%，则直接展示 100%或 0%
        if rate == 0 or rate == 1:
            return f'{int(rate * 100)}%'

        percent = Decimal(rate) * 100
        return f"{int(percent.quantize(Decimal('1'), rounding=ROUND_HALF_UP))}%"

    def get_inv_points_desc(self):
        if not self.level:
            return "-"
        if self.level == SmartTowerInvPointsLevel.MPN.value:
            return self.sku
        if self.level == SmartTowerInvPointsLevel.NAND.value:
            return self.nand
        if self.level == SmartTowerInvPointsLevel.SUB_LOB.value:
            return self.get_sub_lob_short_desc()
        return "-"

    def get_sub_lob_short_desc(self):
        prefix = "iPhone "
        short_desc = self.sub_lob
        if self.sub_lob and self.sub_lob.startswith(prefix):
            short_desc = self.sub_lob[len(prefix):]
        return short_desc

