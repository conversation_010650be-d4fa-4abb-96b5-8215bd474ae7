FROM docker.apple.com/base-images/ubi9-minimal/python3.9-runtime

USER root
RUN microdnf install -y --enablerepo=epel-9  shadow-utils && microdnf clean all

WORKDIR /usr/local/fast_lite_server
COPY . .
RUN pip install -r requirements.txt
RUN pip install apple-dmp-databend-sqlalchemy==0.2.4 -i https://pypi.apple.com/simple
RUN pip install apple-dmp-common-client==1.1.11 --index https://pypi.apple.com/simple

RUN chmod -R 777 /usr/local/fast_lite_server/
RUN groupadd -r expert && useradd -r -g expert fast_lite_server
RUN chgrp -R expert . && chown -R fast_lite_server .

USER fast_lite_server

ENV TZ=Asia/Shanghai
ENV DB_ENV=aws

ARG BASE_VERSION
ENV RIO_VERSION $BASE_VERSION

ENV AWS_DEFAULT_REGION=us-west-2
# 服务的环境变量配置了no_proxy中已包含.s3.us-west-2.amazonaws.com
# guestbook-cn.corp.apple.com 特殊域名需要走代理
ENV no_proxy=*************,localhost,127.0.0.1,***************,************/20,************/27,************/27,************/27,***********/23,***********/23,***********/23,**********/16,.internal,.apple.com,!guestbook-cn.corp.apple.com,.execute-api.us-west-2.amazonaws.com,.s3.us-west-2.amazonaws.com,.us-west-2.eks.amazonaws.com,.us-west-2.vpce.amazonaws.com,.elb.us-west-2.amazonaws.com,.us-west-2.elb.amazonaws.com,.us-west-2.rds.amazonaws.com,amazonlinux.us-west-2.amazonaws.com,api.sagemaker.us-west-2.amazonaws.com,cloudformation.us-west-2.amazonaws.com,cloudtrail.us-west-2.amazonaws.com,codebuild-fips.us-west-2.amazonaws.com,codebuild.us-west-2.amazonaws.com,config.us-west-2.amazonaws.com,dynamodb.us-west-2.amazonaws.com,ec2.us-west-2.amazonaws.com,ec2messages.us-west-2.amazonaws.com,elasticloadbalancing.us-west-2.amazonaws.com,events.us-west-2.amazonaws.com,kinesis.us-west-2.amazonaws.com,kms.us-west-2.amazonaws.com,logs.us-west-2.amazonaws.com,monitoring.us-west-2.amazonaws.com,runtime.sagemaker.us-west-2.amazonaws.com,secretsmanager.us-west-2.amazonaws.com,servicecatalog.us-west-2.amazonaws.com,sns.us-west-2.amazonaws.com,ssm.us-west-2.amazonaws.com,ssmmessages.us-west-2.amazonaws.com,sts.us-west-2.amazonaws.com
ENV http_proxy=http://proxy.config.pcp.local:3128
ENV https_proxy=http://proxy.config.pcp.local:3128

RUN chmod +x ./start.sh

ENTRYPOINT [ "./start.sh" ]