from .api_common import *
from service.ideal_demand_service import *
from service.operation_workflow_service import insert_operation_workflow
from util.const_tracking import *
from util.util_operation import insert_operate_record


bp = Blueprint('ideal_demand_api', __name__, url_prefix=UrlPrefix)


@bp.route("/ideal_demand/rtm/list", methods=["GET"])
def ideal_demand_rtm_list():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        query_params = request.args
        rtm = query_params.get('rtm')
        page = query_params.get('page', 1, type=int)
        size = query_params.get('size', 11, type=int)
        ret["data"] = get_ideal_demand_list_by_rtm(rtm, page, size)
        
        person_id = request.headers.get(ShieldPrsId, '')
        insert_operation_workflow(IDEAL_DEMAND, VIEW_OPERATION, person_id, TRACKING_RTMS_DICT.get(rtm), request.path)
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg

    return ret


@bp.route("/ideal_demand/rtm/detail", methods=["GET"])
def ideal_demand_rtm_detail():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        query_params = request.args
        rtm = query_params.get('rtm')
        week_date = query_params.get('week_date')
        ret["data"] = get_ideal_demand_detail(rtm, week_date)
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg

    return ret


@bp.route("/ideal_demand/rtm/upload", methods=["POST"])
def upload_ideal_demand_file():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        query_params = request.args
        rtm = query_params.get('rtm')
        week_date = query_params.get('week_date')
        if not rtm:
            raise ErrorExcept(ErrCode.Param, "please input rtm.")
        file = request.files.get("file")
        uploader = request.headers.get(ShieldPrsNickName) + " " + request.headers.get(ShieldPrsLastName)
        uploader_email = request.headers.get(ShieldPrsEmail)
        ret["data"] = upload_ideal_demand_file_by_rtm(file, rtm, week_date, uploader, uploader_email)
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)

    return ret


@bp.route("/ideal_demand/rtm/download")
def rtm_download_ideal_demand_file():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    
    rtm = request.args.get('rtm')
    week_date = request.args.get('week_date')
    category = request.args.get('category', type=int)

    if rtm is None or week_date is None or category is None:
        ret[Ret.Code] = ErrCode.Param
        ret[Ret.Msg] = 'param is error.'
        return ret
    
    try:
        file_name, excel_file = get_rtm_file_binary_service(rtm, week_date, category)
        response = make_response(send_file(excel_file,
                                           mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                                           download_name=file_name,
                                           as_attachment=True))
        response.headers["Content-Disposition"] = f'attachment; filename="{file_name}"'
    except FileNotFoundError:
        ret[Ret.Code] = ErrCode.System
        ret[Ret.Msg] = 'No such file or directory.'
        return ret
    return response


@bp.route("/ideal_demand/cpf/list", methods=["GET"])
def ideal_demand_cpf_list():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        query_params = request.args
        page = query_params.get('page', 1, type=int)
        size = query_params.get('size', 11, type=int)
        ret["data"] = get_ideal_demand_list_by_cpf(page, size)
        
        person_id = request.headers.get(ShieldPrsId, '')
        insert_operation_workflow(IDEAL_DEMAND, VIEW_OPERATION, person_id, TrackingRtms.CP_F, request.path)
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg

    return ret


@bp.route("/ideal_demand/cpf/detail", methods=["GET"])
def ideal_demand_cpf_detail():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        query_params = request.args
        week_date = query_params.get('week_date')
        ret["data"] = get_ideal_demand_cpf_detail(week_date)
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg

    return ret


@bp.route("/ideal_demand/cpf/upload", methods=["POST"])
def cpf_upload_ideal_demand_file():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        query_params = request.args
        week_date = query_params.get('week_date')
        category = query_params.get('category')
        file = request.files.get("file")
        uploader = request.headers.get(ShieldPrsNickName) + " " + request.headers.get(ShieldPrsLastName)
        uploader_email = request.headers.get(ShieldPrsEmail)
        ret["data"] = upload_ideal_demand_file_by_cpf(file, category, week_date, uploader, uploader_email)
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)

    return ret


@bp.route("/ideal_demand/cpf/download")
def cpf_download_ideal_demand_file():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    
    week_date = request.args.get('week_date')
    category = request.args.get('category', type=int)

    if week_date is None or category is None:
        ret[Ret.Code] = ErrCode.Param
        ret[Ret.Msg] = 'param is error.'
        return ret
    
    try:
        file_name, excel_file = get_cpf_file_binary_service(week_date, category)
        response = make_response(send_file(excel_file,
                                           mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                                           download_name=file_name,
                                           as_attachment=True))
        response.headers["Content-Disposition"] = f'attachment; filename="{file_name}"'
    except FileNotFoundError:
        ret[Ret.Code] = ErrCode.System
        ret[Ret.Msg] = 'No such file or directory.'
        return ret
    return response


@bp.route("/ideal_demand/task/generate_week_record", methods=["POST"])
def task_generate_week_record():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        fiscal_dt = request.args.get('fiscal_dt')
        if fiscal_dt is None:
            ret[Ret.Code] = ErrCode.Param
            ret[Ret.Msg] = 'param is error.'
            return ret
        
        ret["data"] = generate_week_record(fiscal_dt)
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)

    return ret


@bp.route("/ideal_demand/task/generate_final_forecast", methods=["POST"])
def task_generate_final_forecast():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        fiscal_dt = request.args.get('fiscal_dt')
        if fiscal_dt is None:
            ret[Ret.Code] = ErrCode.Param
            ret[Ret.Msg] = 'param is error.'
            return ret
        
        ret["data"] = generate_final_forecast(fiscal_dt)
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)

    return ret


@bp.route("/ideal_demand/task/remind_rtm_not_upload", methods=["POST"])
def task_remind_rtm_not_upload():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        fiscal_dt = request.args.get('fiscal_dt')
        rtm = request.args.get('rtm')
        if fiscal_dt is None or rtm is None:
            ret[Ret.Code] = ErrCode.Param
            ret[Ret.Msg] = 'param is error.'
            return ret
        
        ret["data"] = rtm_not_upload_forecast_reminder(fiscal_dt, rtm)
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)

    return ret


@bp.route("/ideal_demand/task/remind_cpf_not_upload", methods=["POST"])
def task_remind_cpf_not_upload():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        fiscal_dt = request.args.get('fiscal_dt')
        adjustment_type = request.args.get('adjustment_type')
        if fiscal_dt is None or adjustment_type is None:
            ret[Ret.Code] = ErrCode.Param
            ret[Ret.Msg] = 'param is error.'
            return ret
        
        ret["data"] = cpf_not_upload_adjustment_reminder(fiscal_dt, adjustment_type)
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)

    return ret


@bp.route("/ideal_demand/task/rtm_not_upload_expired", methods=["POST"])
def task_rtm_not_upload_expired():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        fiscal_dt = request.args.get('fiscal_dt')
        rtm = request.args.get('rtm')
        if fiscal_dt is None or rtm is None:
            ret[Ret.Code] = ErrCode.Param
            ret[Ret.Msg] = 'param is error.'
            return ret
        
        ret["data"] = rtm_not_upload_forecast_expired(fiscal_dt, rtm)
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)

    return ret


@bp.route("/ideal_demand/task/cpf_not_upload_expired", methods=["POST"])
def task_cpf_not_upload_expired():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        fiscal_dt = request.args.get('fiscal_dt')
        adjustment_type = request.args.get('adjustment_type')
        if fiscal_dt is None or adjustment_type is None:
            ret[Ret.Code] = ErrCode.Param
            ret[Ret.Msg] = 'param is error.'
            return ret
        
        ret["data"] = cpf_not_upload_adjustment_expired(fiscal_dt, adjustment_type)
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)

    return ret


@bp.route("/ideal_demand/task/is_so_eoh_data_ready")
def task_is_so_eoh_data_ready():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        fiscal_dt = request.args.get('fiscal_dt')
        rtm = request.args.get('rtm')
        if fiscal_dt is None or rtm is None:
            ret[Ret.Code] = ErrCode.Param
            ret[Ret.Msg] = 'param is error.'
            return ret
        
        ret["data"] = is_so_eoh_data_ready_service(fiscal_dt, rtm)
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)

    return ret


@bp.route("/ideal_demand/task/is_cpf_ideal_demand_data_ready")
def task_is_cpf_ideal_demand_data_ready():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        fiscal_dt = request.args.get('fiscal_dt')
        if fiscal_dt is None:
            ret[Ret.Code] = ErrCode.Param
            ret[Ret.Msg] = 'param is error.'
            return ret
        
        ret["data"] = is_cpf_ideal_demand_data_ready_service(fiscal_dt)
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)

    return ret