from .api_common import *
from service.online_service import *
from service.model_sku_service import *
from service.operation_workflow_service import insert_operation_workflow
from util.const_tracking import *
from util.const import UrlPrefix


bp = Blueprint('online_api', __name__, url_prefix=UrlPrefix)


@bp.route("/online/demand/list")
@shield_person(request)
def get_online_demand_fiscal_qtr_list():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    page_size = request.args.get("page_size", "11")
    page = request.args.get("page", "1")
    try:
        ret["data"] = get_online_fiscal_qtr_week_year(page, page_size)
        person_id = request.headers.get(ShieldPrsId, '')
        insert_operation_workflow(FAST_LITE, VIEW_OPERATION, person_id, TrackingRtms.ONLINE, request.path)
    except Exception as e:
        ret[Ret.Code] = ErrCode.System
        ret[Ret.Msg] = str(e)

    return ret


@bp.route("/online/demand/data")
@shield_person(request)
def get_online_data_by_fiscal_quarter():
    if len(g.get(SoldToNameKey)) == 0:
        return {
            Ret.Code: ErrCode.NoSoldToPermission,
            Ret.Msg: ErrMsg.get(ErrCode.NoSoldToPermission)
        }
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    fiscal_week_year = request.args.get("fiscal_week_year")
    order_by = request.args.getlist("orders[]")
    page_size = request.args.get("page_size", "11")
    page = request.args.get("page", "1")
    platform = request.args.getlist("platform[]")
    sold_to_id = request.args.getlist("sold_to_id[]")
    sold_to_name = request.args.getlist("sold_to_name[]")
    lob = request.args.getlist("lob[]")
    model = request.args.getlist("model[]")
    sku = request.args.getlist("sku[]")
    # model 永不为空
    if model is None or len(model) == 0:
        model = get_model_list(DEMAND, ["iphone"], StrRTMOnlineFull, fiscal_week_year)
    try:
        ret["data"] = get_online_demand_data(
            fiscal_week_year, platform, sold_to_id, sold_to_name,
            lob, model, sku, order_by, page, page_size)
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        ret[Ret.Code] = ErrCode.System
        ret[Ret.Msg] = str(e)
    return ret


@bp.route("/online/demand/data/download")
@shield_person(request)
def download_online_data_by_fiscal_quarter():
    res = {
        Ret.Code: ErrCode.System
    }
    fiscal_week_year = request.args.get("fiscal_week_year")
    if fiscal_week_year is None:
        res[Ret.Msg] = 'please input fiscal_week_year. eg. 202308'
        return res
        # model 永不为空
    model = get_model_list(DEMAND, ["iphone"], StrRTMOnlineFull, fiscal_week_year)
    try:
        fiscal_week_year = int(fiscal_week_year)
    except ValueError:
        res[Ret.Code] = ErrCode.Param
        res[Ret.Msg] = 'fiscal_week_year must be integer'
        return res
    try:
        version = request.args.get("version")
        if version != "1" and version != "2":
            res[Ret.Msg] = 'please input version. version must be 1 or 2'
            return res
        path = gen_online_demand_download_data(fiscal_week_year, version,model)
        paths = os.path.split(path)
        file_name = paths[-1]

        response = make_response(send_file(path, as_attachment=True))
        response.headers["Content-Disposition"] = f"attachment; filename={file_name.encode().decode('latin-1')}"
        return response
    except ErrorExcept as e:
        res[Ret.Code] = e.code
        res[Ret.Msg] = e.err_msg
    except Exception as e:
        logger.exception(e)
        res[Ret.Msg] = str(e)
    return res


@bp.route("/online/forecast/list")
@shield_person(request)
def get_online_forecast_fiscal_qtr_list():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    page_size = request.args.get("page_size", "11")
    page = request.args.get("page", "1")
    try:
        ret["data"] = get_online_forecast_data_list(page, page_size)
        person_id = request.headers.get(ShieldPrsId, '')
        insert_operation_workflow(FAST_LITE, VIEW_OPERATION, person_id, TrackingRtms.ONLINE, request.path)
    except Exception as e:
        ret[Ret.Code] = ErrCode.System
        ret[Ret.Msg] = str(e)

    return ret


@bp.route("/online/forecast/data")
@shield_person(request)
def get_online_forecast_data_by_fiscal_quarter():
    if len(g.get(SoldToNameKey)) == 0:
        return {
            Ret.Code: ErrCode.NoSoldToPermission,
            Ret.Msg: ErrMsg.get(ErrCode.NoSoldToPermission)
        }
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    fiscal_week_year = request.args.get("fiscal_week_year")
    platform = request.args.getlist("platform[]")
    sold_to_id = request.args.getlist("sold_to_id[]")
    sold_to_name = request.args.getlist("sold_to_name[]")
    lob = request.args.getlist("lob[]")
    model = request.args.getlist("model[]")
    # model 永不为空
    if model is None or len(model) == 0:
        model = get_model_list(FORECAST, ["iphone"], StrRTMOnlineFull, fiscal_week_year)
    try:
        ret["data"] = get_online_forecast_data_new(fiscal_week_year, platform, sold_to_id, sold_to_name, lob, model)
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    # except Exception as e:
    #     ret[Ret.Code] = ErrCode.System
    #     ret[Ret.Msg] = str(e)
    return ret


@bp.route("/online/forecast/data/download")
@shield_person(request)
def download_online_forecast_data_by_fiscal_quarter():
    res = {
        Ret.Code: ErrCode.System
    }
    fiscal_week_year = request.args.get("fiscal_week_year")
    if fiscal_week_year is None:
        res[Ret.Msg] = 'please input fiscal_week_year. eg. 202308'
        return res
    model = get_model_list(FORECAST, ["iphone"], StrRTMOnlineFull, fiscal_week_year)

    try:
        fiscal_week_year = int(fiscal_week_year)
    except ValueError:
        res[Ret.Code] = ErrCode.Param
        res[Ret.Msg] = 'fiscal_week_year must be integer'
        return res
    try:
        path = download_online_forecast_data(fiscal_week_year,model)
        paths = os.path.split(path)
        file_name = paths[-1]

        response = make_response(send_file(path, as_attachment=True))
        response.headers["Content-Disposition"] = f"attachment; filename={file_name.encode().decode('latin-1')}"
        return response
    except ErrorExcept as e:
        res[Ret.Code] = e.code
        res[Ret.Msg] = e.err_msg
    except Exception as e:
        logger.exception(e)
        res[Ret.Msg] = str(e)
    return res


@bp.route("/online/demand/sold_to")
@shield_person(request)
def get_online_demand_sold_to_list():
    res = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }

    try:
        res["data"] = {"list": get_online_demand_sold_to_list_data()}
    except Exception as e:
        res[Ret.Code] = ErrCode.System
        res[Ret.Msg] = str(e)

    return res


@bp.route("/online/demand/sku")
@shield_person(request)
def get_online_demand_sku_list():
    res = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }

    try:
        res["data"] = {"list": get_online_demand_sku_list_data()}
    except Exception as e:
        res[Ret.Code] = ErrCode.System
        res[Ret.Msg] = str(e)

    return res


@bp.route("/online/forecast/sold_to")
@shield_person(request)
def get_online_forecast_sold_to_list():
    res = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }

    try:
        res["data"] = {"list": get_online_forecast_sold_to_list_data()}
    except Exception as e:
        res[Ret.Code] = ErrCode.System
        res[Ret.Msg] = str(e)

    return res


@bp.route("/online/forecast/model")
@shield_person(request)
def get_online_forecast_lob_list():
    res = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }

    try:
        res["data"] = {"list": get_online_forecast_model_list_data()}
    except Exception as e:
        res[Ret.Code] = ErrCode.System
        res[Ret.Msg] = str(e)

    return res


@bp.route("/online/demand/platform/list")
@shield_person(request)
def get_online_demand_platform_raw_list():
    res = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    fiscal_week_year = request.args.get("fiscal_week_year")
    try:
        res["data"] = {"list": get_online_raw_business_type_data(fiscal_week_year)}
    except ErrorExcept as e:
        res[Ret.Code] = e.code
        res[Ret.Msg] = e.err_msg
    except Exception as e:
        res[Ret.Code] = ErrCode.System
        res[Ret.Msg] = str(e)

    return res


@bp.route("/online/demand/sold_to/list")
@shield_person(request)
def get_online_demand_sold_to_raw_list():
    res = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    fiscal_week_year = request.args.get("fiscal_week_year")
    platform = request.args.getlist("platform[]")
    sold_to = request.args.get("sold_to")
    try:
        res["data"] = {"list": get_online_raw_sold_to_data(platform, sold_to, fiscal_week_year)}
    except ErrorExcept as e:
        res[Ret.Code] = e.code
        res[Ret.Msg] = e.err_msg
    except Exception as e:
        res[Ret.Code] = ErrCode.System
        res[Ret.Msg] = str(e)

    return res


@bp.route("/online/forecast/platform/list")
@shield_person(request)
def get_online_forecast_platform_raw_list():
    res = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    fiscal_week_year = request.args.get("fiscal_week_year")
    try:
        res["data"] = {"list": get_online_raw_business_type_data(fiscal_week_year)}
    except ErrorExcept as e:
        res[Ret.Code] = e.code
        res[Ret.Msg] = e.err_msg
    except Exception as e:
        res[Ret.Code] = ErrCode.System
        res[Ret.Msg] = str(e)

    return res


@bp.route("/online/forecast/sold_to/list")
@shield_person(request)
def get_online_forecast_sold_to_raw_list():
    res = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    fiscal_week_year = request.args.get("fiscal_week_year")
    platform = request.args.getlist("platform[]")
    sold_to = request.args.get("sold_to")
    try:
        res["data"] = {"list": get_online_raw_sold_to_data(platform, sold_to, fiscal_week_year)}
    except ErrorExcept as e:
        res[Ret.Code] = e.code
        res[Ret.Msg] = e.err_msg
    except Exception as e:
        res[Ret.Code] = ErrCode.System
        res[Ret.Msg] = str(e)

    return res


@bp.route("/online/demand/lob/list")
def get_online_demand_lob_raw_list():
    res = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        res["data"] = {"list": get_lob_list()}
    except Exception as e:
        res[Ret.Code] = ErrCode.System
        res[Ret.Msg] = str(e)

    return res


@bp.route("/online/demand/model/list")
def get_online_demand_model_raw_list():
    res = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    lob = request.args.getlist("lob[]")
    fiscal_week_year = request.args.get("fiscal_week_year")
    try:
        res["data"] = {"list": get_model_list(DEMAND,lob, StrRTMOnlineFull,fiscal_week_year)}
    except Exception as e:
        res[Ret.Code] = ErrCode.System
        res[Ret.Msg] = str(e)

    return res


@bp.route("/online/demand/sku/list")
def get_online_demand_sku_raw_list():
    res = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    model = request.args.getlist("model[]")
    fiscal_week_year = request.args.get("fiscal_week_year")
    if model is None or len(model) == 0:
        model = get_model_list(DEMAND, ["iphone"], StrRTMOnlineFull, fiscal_week_year)
    try:
        res["data"] = {"list": get_sku_list(model, StrRTMOnlineFull)}
    except Exception as e:
        res[Ret.Code] = ErrCode.System
        res[Ret.Msg] = str(e)

    return res


@bp.route("/online/forecast/lob/list")
def get_online_forecast_lob_raw_list():
    res = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        res["data"] = {"list": get_lob_list()}
    except Exception as e:
        res[Ret.Code] = ErrCode.System
        res[Ret.Msg] = str(e)

    return res


@bp.route("/online/forecast/model/list")
def get_online_forecast_model_raw_list():
    res = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    lob = request.args.getlist("lob[]")
    fiscal_week_year = request.args.get("fiscal_week_year")

    try:
        res["data"] = {"list": get_model_list(FORECAST,lob, StrRTMOnlineFull,fiscal_week_year)}
    except Exception as e:
        res[Ret.Code] = ErrCode.System
        res[Ret.Msg] = str(e)

    return res


@bp.route("/online/forecast/sku/list")
def get_online_forecast_sku_raw_list():
    res = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    model = request.args.getlist("model[]")
    fiscal_week_year = request.args.get("fiscal_week_year")
    if model is None or len(model) == 0:
        model = get_model_list(FORECAST, ["iphone"], StrRTMOnlineFull, fiscal_week_year)
    try:
        res["data"] = {"list": get_sku_list(model, StrRTMOnlineFull)}
    except Exception as e:
        res[Ret.Code] = ErrCode.System
        res[Ret.Msg] = str(e)

    return res
