from util.send_email import delete_email_duplication_key
from .api_common import *
from service.common import get_default_dates
from util.redis_pool import delete_cache_from_redis, delete_bulk_cache_from_redis, get_bulk_cache_from_redis

bp = Blueprint('common_redis', __name__, url_prefix='/redis')


@bp.route('/delete_redis_key', methods=['GET'])
def delete_redis_key():
    ret = {
        Ret.Code: ErrCode.Success, 
        Ret.Msg: 'Success'
    }
    try:
        url_params = request.args
        name = url_params.get('name')
        params = url_params.getlist('params')
        
        ret[Ret.Data] = delete_cache_from_redis(name, params)
        
    except ErrorExcept as e: 
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e: 
        ret[Ret.Code] = ErrCode.Success
        ret[Ret.Msg] = str(e)

    return ret 


@bp.route('/delete_bulk_redis_key', methods=['GET'])
def delete_bulk_redis_key():
    ret = {
        Ret.Code: ErrCode.Success, 
        Ret.Msg: 'Success'
    }
    try:
        url_params = request.args
        name = url_params.get('name')
        
        ret[Ret.Data] = delete_bulk_cache_from_redis(name)
        
    except ErrorExcept as e: 
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e: 
        ret[Ret.Code] = ErrCode.Success
        ret[Ret.Msg] = str(e)

    return ret 


@bp.route('/get_bulk_redis_key', methods=['GET'])
def get_bulk_redis_key():
    ret = {
        Ret.Code: ErrCode.Success, 
        Ret.Msg: 'Success'
    }
    try:
        url_params = request.args
        name = url_params.get('name')
        
        ret[Ret.Data] = get_bulk_cache_from_redis(name)
        
    except ErrorExcept as e: 
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e: 
        ret[Ret.Code] = ErrCode.Success
        ret[Ret.Msg] = str(e)

    return ret


@bp.route('/remove_email_duplication_key', methods=['GET'])
def remove_email_duplication_key():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        url_params = request.args
        cmd = url_params.get('cmd', '')
        subject_date = url_params.get('subject_date', '')
        ret[Ret.Data] = delete_email_duplication_key(cmd, subject_date)
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        ret[Ret.Code] = ErrCode.Success
        ret[Ret.Msg] = str(e)

    return ret
