import numpy as np
import pandas as pd
from apis.api_common import *
from data.databend.dashboard.forecast_actual.forecast_accuracy_dfa import (
    ForecastAccuracyDFA
)
from data.databend.dashboard.forecast_actual.forecast_rolling_weeks_mapping import (
    ForecastRollingWeeksMapping,
)
from data.databend.dashboard.forecast_actual.forecast_accuracy_rtm_and_ml import (
    ForecastAccuracyRTMAndML
)
from domain.dashboard.entity.forecast_type import ForecastType, ForecastAccuracyViewerType
from domain.dashboard.entity.fiscal_week import cut_fiscal_weeks
from domain.dashboard.entity.forecast_threshold import ForecastThreshold
from domain.dashboard.entity.forecast_vs_actual import Condition, ForecastVSActualByWeeks
from domain.dashboard.impl.fiscal_week_container import FiscalWeekContainer
from domain.dashboard.impl.forecast_actual.data_handler import DFAHandler, <PERSON><PERSON><PERSON>and<PERSON>, DfaUpdate<PERSON><PERSON><PERSON><PERSON><PERSON>, \
    Threshold<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>old<PERSON>o<PERSON><PERSON>date<PERSON>ime<PERSON>andler
from domain.dashboard.impl.forecast_actual.forecast_actual_impl import get_data_type_by_viewer, \
    get_forecast_actual_order
from domain.demand.entity.common_menu import Menu
from kit.parallel_executor import ParallelExecutor
from util.util import common_sort_df

bp = Blueprint("forecast_actual", __name__, url_prefix="/dashboard")

# Forecast actual HR_LR type mapping
HR_LR_TYPE_MAPPING = {
    "All": "All",
    "High Runner": "HR",
    "Low Runner": "LR"
}


@bp.route("/forecast_vs_actual/menu", methods=["GET"])
def get_menu_for_forecast_actual():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: "Success"}
    try:
        fiscal_weeks = FiscalWeekContainer().get_fiscal_weeks_forecast_actual()
        # 参数处理
        query_params = request.args
        fiscal_week = query_params.get("fiscal_week", "")
        hr_lr_params = HR_LR_TYPE_MAPPING[query_params.get("hr_lr", "High Runner")]
        if not fiscal_week and fiscal_weeks:
            fiscal_week = fiscal_weeks[0]
        menu = Menu(query_func=ForecastAccuracyRTMAndML.get_menu_records_by_fiscal_week,
                    query_kwargs={"fiscal_week": fiscal_week, "hr_lr": hr_lr_params})
        menu_data = menu.get_result_menu(["lob", "sub_lob", "nand", "color"])
        forecast_version = ["CW", "CW+1", "CW+2", "CW+3", "CW+4", "CW+5", "CW+6"]
        hr_lr = ["All", "High Runner", "Low Runner"]
        region = ["China mainland"]
        sub_rtms = list(
            {sub_rtm for i in menu_data["regions"] for rtms in i["rtms"] for sub_rtm in rtms["sub_rtms"] if
             i["region"] == REGION_CM}
        )
        # 重新排序sub_rtm
        sub_rtms.sort(reverse=False, key=lambda x: x.lower())
        sub_lobs = []
        for lobs in menu_data["lobs"]:
            if lobs["lob"] == 'iPhone':
                sub_lobs = lobs["sub_lobs"]
                break
        # 删除所有的All
        for item in sub_lobs:
            colors = item["colors"]
            nands = item["nands"]
            if ALL in colors:
                colors.remove(ALL)
            if ALL in nands:
                nands.remove(ALL)

        fiscal_weeks = cut_fiscal_weeks(fiscal_weeks=fiscal_weeks,
                                        length=QUARTER_3_FISCAL_WEEKS_LENGTH,
                                        end_fiscal_weeks='FY24Q4W2')
        ret_data = {
            "region": region,
            "forecast_version": forecast_version,
            "hr_lr": hr_lr,
            "sub_rtms": sub_rtms,
            "sub_lobs": sub_lobs,
            "fiscal_weeks": fiscal_weeks
        }
        ret[Ret.Data] = ret_data
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e, exc_info=True)
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(e, exc_info=True)
    return ret


def get_parallel_forecast_actual_data_for_soldto(weeks: list,
                                                 condition: Condition,
                                                 fiscal_week: str) -> tuple:
    ml_handler = MLSoldToHandler(name="ml_sold_to_actual", weeks=weeks, condition=condition)
    ml_latest_refresh_time_handler = MLSoldToUpdateTimeHandler(name="ml_sold_to_latest_refresh_time", fiscal_week=fiscal_week)

    pe = ParallelExecutor(source=[ml_handler, ml_latest_refresh_time_handler])
    parallel_result = pe.execute()
    ml_latest_refresh_time = parallel_result[ml_latest_refresh_time_handler.name]
    ml_actual_data: list[ForecastVSActualByWeeks] = parallel_result[ml_handler.name]
    return ml_latest_refresh_time, ml_actual_data


def get_parallel_forecast_actual_data(weeks: list,
                                      condition: Condition,
                                      fiscal_week: str,
                                      condition_for_threshold: dict) -> tuple:
    dfa_handler = DFAHandler(name="dfa_actual", weeks=weeks, condition=condition)
    ml_handler = MLHandler(name="ml_actual", weeks=weeks, condition=condition)
    dfa_latest_refresh_time_handler = DfaUpdateTimeHandler(name="dfa_latest_refresh_time", fiscal_week=fiscal_week)
    ml_latest_refresh_time_handler = MLUpdateTimeHandler(name="ml_latest_refresh_time", fiscal_week=fiscal_week)
    threshold_handler = ThresholdHandler(name="threshold", condition=condition_for_threshold)
    pe = ParallelExecutor(source=[dfa_handler, ml_handler, dfa_latest_refresh_time_handler, ml_latest_refresh_time_handler, threshold_handler])
    parallel_result = pe.execute()
    dfa_latest_refresh_time = parallel_result[dfa_latest_refresh_time_handler.name]
    ml_latest_refresh_time = parallel_result[ml_latest_refresh_time_handler.name]
    dfa_actual_data = parallel_result[dfa_handler.name]
    ml_actual_data = parallel_result[ml_handler.name]
    threshold = parallel_result[threshold_handler.name]
    merged_data: list[ForecastVSActualByWeeks] = dfa_actual_data + ml_actual_data
    latest_refresh_time = dfa_latest_refresh_time if dfa_latest_refresh_time else ml_latest_refresh_time
    return latest_refresh_time, threshold, merged_data


@bp.route("/forecast_vs_actual/view", methods=["GET"])
def get_forecast_actual_overview():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: "Success"}
    try:
        # 参数处理
        query_params = request.args
        fiscal_week = query_params.get("fiscal_week")
        region = query_params.get("region", "China mainland")
        sub_lob = query_params.get("sub_lob")
        forecast_version = query_params.get("forecast_version")
        nand = query_params.get("nand", ALL)
        color = query_params.get("color", ALL)
        sold_to_id = query_params.get("sold_to_id")
        viewer = query_params.get("viewer", ForecastAccuracyViewerType.FAST_DASHBOARD_ACCURACY.value)

        # 兼容feedback review region accuracy, 所以需要传参rtm、sub_rtm
        rtm = query_params.get("rtm", ALL)
        sub_rtm = query_params.get("sub_rtm", ALL)

        if not nand:
            nand = ALL
        if not color:
            color = ALL
        lob = query_params.get("lob", 'iPhone')
        hr_lr = HR_LR_TYPE_MAPPING[query_params.get("hr_lr", ALL)]
        if not all([fiscal_week, sub_lob, forecast_version]):
            raise ErrorExcept(
                ErrCode.Param, "please check params, required parameter missing."
            )
        if viewer == ForecastAccuracyViewerType.FEEDBACK_SOLD_TO_ACCURACY.value and not sold_to_id:
            raise ErrorExcept(
                ErrCode.Param, "please check params, required disti_id parameter missing."
            )

        # 先请求rolling周
        weeks = ForecastRollingWeeksMapping.query_rolling_weeks(
            fiscal_week, forecast_version
        )

        # feedback review accuracy是要求13周的数据
        if viewer != ForecastAccuracyViewerType.FAST_DASHBOARD_ACCURACY.value:
            weeks = weeks[:-1]

        condition = Condition(
            forecast_version=forecast_version,
            sub_rtm=sub_rtm,
            rtm=rtm,
            lob=lob,
            sub_lob=sub_lob,
            nand=nand,
            color=color,
            hr_lr=hr_lr
        )
        condition_for_threshold = condition.fields_for_threshold()

        threshold = None
        # feedback review 详情accuracy
        if viewer == ForecastAccuracyViewerType.FEEDBACK_SOLD_TO_ACCURACY.value:
            condition.set_sold_to_id(sold_to_id)
            latest_refresh_time, merged_data = (
                get_parallel_forecast_actual_data_for_soldto(weeks=weeks, condition=condition, fiscal_week=fiscal_week)
            )
        else:
            # 并发加载数据源
            latest_refresh_time, threshold, merged_data = (
                get_parallel_forecast_actual_data(weeks=weeks, condition=condition, fiscal_week=fiscal_week,
                                                  condition_for_threshold=condition_for_threshold)
            )

        # 不同hr_lr type 对应的datatype列表
        data_type_dict = get_data_type_by_viewer(viewer=viewer)

        forecast_list = []
        actual_model_item = {}
        national_actual_model_item = {}
        for detail in merged_data:
            data_type = detail.data_type
            if data_type not in data_type_dict.get(hr_lr, "All"):
                continue

            detail_item = {
                "data_type": data_type,
            }

            # fast dashboard 看板需要threshold
            if viewer == ForecastAccuracyViewerType.FAST_DASHBOARD_ACCURACY.value:
                qualification = detail.qualification(threshold)
                detail_item['qualification'] = qualification

            for forecast_vs_actual in detail.forecast_vs_actuals:
                detail_item[forecast_vs_actual.fiscal_week] = {
                    "Accuracy%": forecast_vs_actual.accuracy,
                    "Value": forecast_vs_actual.forecast_cw,
                    "Delta": forecast_vs_actual.delta
                }
            forecast_list.append(detail_item)
            # 特殊处理Actual
            if detail.data_type in [ForecastType.ML_BOTTOM_UP.value] and not actual_model_item:
                actual_model_item["data_type"] = "Actual"
                for forecast_vs_actual in detail.forecast_vs_actuals:
                    actual_model_item[forecast_vs_actual.fiscal_week] = {
                        "Value": forecast_vs_actual.ub,
                    }
                forecast_list.append(actual_model_item)

            # 特殊处理 National Actual (UB) 只有HR-LR=All时才在页面展示
            if detail.data_type in [ForecastType.ML_NATIONAL.value] and not national_actual_model_item:
                national_actual_model_item["data_type"] = ForecastType.NATIONAL_ACTUAL_UB.value
                for forecast_vs_actual in detail.forecast_vs_actuals:
                    national_actual_model_item[forecast_vs_actual.fiscal_week] = {
                        "Value": forecast_vs_actual.ub,
                    }
                forecast_list.append(national_actual_model_item)
        # 排序forecast ML National、ML Bottom-up、DFA、RTM Bottom-up、Actual
        try:
            order_list = get_forecast_actual_order(viewer)
            forecast_list = sorted(
                forecast_list, key=lambda x: order_list.index(x["data_type"])
            )
        except Exception as e:
            logger.error(f"sort forecast actual list error: {e}")
        result = {
            "forecast": forecast_list,
            "latest_refresh_time": latest_refresh_time,
            "threshold": threshold,
            "week_columns": weeks,
        }
        ret[Ret.Data] = result
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e, exc_info=True)
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(e, exc_info=True)
    return ret


@bp.route("/forecast_vs_actual/accuracy_detail", methods=["GET"])
def get_forecast_vs_actual_accuracy_detail():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: "Success", Ret.Data: {}}
    try:
        # 参数处理
        query_params = request.args

        fiscal_week = query_params.get("fiscal_week")
        forecast_type = query_params.get("forecast_type")
        forecast_version = query_params.get("forecast_version")
        view_by = query_params.get("view_by")
        sub_rtm = query_params.get("sub_rtm", None)
        sub_lob = query_params.get("sub_lob", None)
        nand = query_params.get("nand", None)
        color = query_params.get("color", None)
        hr_lr = HR_LR_TYPE_MAPPING[query_params.get("hr_lr", ALL)]
        if not sub_rtm:
            sub_rtm = None
        if not sub_lob:
            sub_lob = None
        if not nand:
            nand = None
        if not color:
            color = None
        if not all([fiscal_week, forecast_version, forecast_type, view_by]):
            raise ErrorExcept(
                ErrCode.Param, "please check params, required parameter missing."
            )
            
        # 先请求rolling周
        weeks = ForecastRollingWeeksMapping.query_rolling_weeks(
            fiscal_week, forecast_version
        )
        
        condition = Condition(
            forecast_version=forecast_version,
            sub_rtm=sub_rtm,
            lob="iPhone",
            sub_lob=sub_lob,
            nand=nand,
            color=color,
            hr_lr=hr_lr
        )
        
        condition_for_threshold = condition.fields_for_threshold(view_by)
        
        condition.set_view(view_by) # 根据view_by，重新整合筛选条件
        
        if forecast_type == ForecastType.DFA.value:
            details: list[ForecastVSActualByWeeks] = (
                ForecastAccuracyDFA.query_forcast_and_ub(
                    forecast_type, weeks, condition
                )
            )
        else:
            details: list[ForecastVSActualByWeeks] = (
                ForecastAccuracyRTMAndML.query_forcast_and_ub(
                    forecast_type, weeks, condition
                )
            )
        thresholds = ForecastThreshold(condition_for_threshold)

        # convert to dict {"data_type":"", "rtm":"", "sub_rtm":"", "sub_lob":"", "nand":"", "color":"" ,"week1":forecast1, "week2":forecast2...}
        accuracies = []
        week_columns = weeks
        for detail in details:
            each_detail_condition = {
                "forecast_version": detail.forecast_version,
                "rtm": detail.rtm,
                "sub_rtm": detail.sub_rtm,
                "sub_lob": detail.sub_lob,
                "nand": detail.nand,
                "color": detail.color}
            threshold = thresholds.get_each_threshold(each_detail_condition)  # todo @baoguo_liu get_threshold(detail)
            detail_dict = {
                **each_detail_condition,
                "data_type": detail.data_type,
                "threshold": threshold,
                "qualification": detail.qualification(threshold),
            }
            for forecast_vs_actual in detail.forecast_vs_actuals:
                detail_dict[forecast_vs_actual.fiscal_week] = (
                    forecast_vs_actual.accuracy
                )
                detail_dict["tier"] = forecast_vs_actual.tier

            accuracies.append(detail_dict)
        # 当view_by=sub_rtm需要特殊去除rtm=All的数据
        if view_by == "sub_rtm":
            accuracies = [item for item in accuracies if item.get("rtm") != ALL]
        elif view_by == "sub_lob":
            sub_lob_list = ["iPhone 16 Series", "iPhone 16 Pro Max", "iPhone 16 Pro", "iPhone 16 Plus", "iPhone 16", "iPhone 16e"]
            accuracies = [item for item in accuracies if item.get("sub_lob") in sub_lob_list]

        # 对数据进行排序
        sort_keys = [view_by] if view_by != "sub_rtm" else ["rtm", view_by]
        df_accuracies: pd.DataFrame = common_sort_df(pd.DataFrame(accuracies), sort_keys)
        df_accuracies.replace({np.nan: None}, inplace=True)
        accuracies = df_accuracies.to_dict("records")
        # 暂时写死用来前端高亮展示
        if view_by == "sub_lob":
            for item in accuracies:
                if item.get("sub_lob") == "iPhone 16 Series":
                    item["sub_lob"] = ALL

        ret[Ret.Data] = {
            "accuracy": accuracies,
            "week_columns": week_columns,
        }
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e, exc_info=True)
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(e, exc_info=True)
    return ret
