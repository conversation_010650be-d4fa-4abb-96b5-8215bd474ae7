from apis.api_common import *
from domain.dashboard.impl.po_delinquent_impl import get_product_view_data, get_po_delinquent_file, \
    get_po_delinquent_menu, get_channel_view_data, channel_view_data_for_email, get_sold_to_view_data
from domain.demand.entity.common_menu import Menu

bp = Blueprint("po_delinquent", __name__, url_prefix=UrlPrefixUBVelocity)

# rtm 权限
PO_DELINQUENT_DASHBOARD_RTM_PERMISSIONS = {
    "CP&F": {
        "region": [REGION_CM, REGION_TAIWAN, REGION_HK],
        "rtm": None
    },
    "Mono": {
        "region": [REGION_CM],
        "rtm": 'Mono'
    },
    "Multi": {
        "region": [REGION_CM],
        "rtm": 'Multi'
    },
    "Online": {
        "region": [REGION_CM],
        "rtm": 'Online'
    },
    "Carrier": {
        "region": [REGION_CM],
        "rtm": 'Carrier'
    },
    "Education": {
        "region": [REGION_CM],
        "rtm": 'Education'
    },
    "Enterprise": {
        "region": [REGION_CM],
        "rtm": 'Enterprise'
    },
    "HK/TW RP": {
        "region": [REGION_TAIWAN, REGION_HK],
        "rtm": 'Retail Partner'
    },
    "HK/TW Carrier": {
        "region": [REGION_TAIWAN, REGION_HK],
        "rtm": 'Carrier'
    },
}


@bp.route('/po_delinquent/product_view', methods=['GET'])
def product_view():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: 'Success', Ret.Data: {}}
    try:
        query_params = request.args
        region = query_params.get("region", "China mainland", str)
        rtm = query_params.get("rtm", "", str)
        sub_rtm = query_params.get("sub_rtm", "", str)
        lob = query_params.get("lob", "iPhone", str)
        fiscal_dt = query_params.get("fiscal_dt", "", str)
        sub_lobs = query_params.getlist("sub_lob[]")

        if not all([region, rtm, sub_rtm, lob]):
            raise ErrorExcept(ErrCode.Param, "please check params, required parameter missing.")

        latest_refresh_time, product_view_data = get_product_view_data(region=region,
                                                                       rtm=rtm,
                                                                       sub_rtm=sub_rtm,
                                                                       lob=lob,
                                                                       sub_lobs=sub_lobs)
        ret[Ret.Data] = {
            "latest_refresh_time": latest_refresh_time,
            "po_delinquent_data": product_view_data
        }
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e.err_msg, exc_info=True)
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(str(e), exc_info=True)
    return ret


@bp.route("/po_delinquent/download", methods=["GET"])
def download_po_delinquent_file():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success',
        Ret.Data: {}
    }
    fiscal_dt = request.args.get("fiscal_dt", "", str)
    channel = request.args.get("channel", CPF, str)

    # 1. 非CP&F, channel就是查询指定rtm下的数据(针对权限)
    region_rtm = PO_DELINQUENT_DASHBOARD_RTM_PERMISSIONS.get(channel, None)
    if region_rtm is None:
        raise ErrorExcept(ErrCode.Param, "please check params, required parameter invalid.")
    try:
        file_name, excel_file = get_po_delinquent_file(region=region_rtm['region'], rtm=region_rtm['rtm'], channel=channel)
        response = make_response(send_file(excel_file,
                                           mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                                           download_name=file_name,
                                           as_attachment=True))
        response.headers["Content-Disposition"] = f'attachment; filename="{file_name}"'
        return response
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e.err_msg, exc_info=True)
        return ret
    except FileNotFoundError as e:
        logger.error(str(e))
        ret[Ret.Code] = ErrCode.System
        ret[Ret.Msg] = 'No such file or directory.'
        logger.error(str(e), exc_info=True)
        return ret
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(str(e), exc_info=True)
        return ret


@bp.route('/po_delinquent/menu')
def po_delinquent_menu():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: 'Success'}
    try:
        fiscal_dt = request.args.get("fiscal_dt", "", str)
        channel = request.args.get("channel", CPF, str)

        region_rtm = PO_DELINQUENT_DASHBOARD_RTM_PERMISSIONS.get(channel, None)
        if region_rtm is None:
            raise ErrorExcept(ErrCode.Param, "please check params, required parameter invalid.")

        query_kwargs = {
            "region": region_rtm['region'],
            "rtm": region_rtm['rtm'],
        }
        menu = Menu(query_func=get_po_delinquent_menu, query_kwargs=query_kwargs, need_retail_partner=True)
        menu_data = menu.get_result_menu()
        for item in menu_data["lobs"]:
            for sub_item in item["sub_lobs"]:
                if sub_item["sub_lob"] == "All":
                    item["sub_lobs"].remove(sub_item)
                    break

        # 针对各个rtm的权限
        if channel != CPF:
            for item in menu_data["regions"]:
                # 去掉rtm=all的筛选
                item["rtms"] = [rtm_item for rtm_item in item["rtms"] if rtm_item["rtm"] != "All"]
                if item["region"] != REGION_CM:
                    continue
                if menu_data['retail_partners']:
                    item['rtms'] = [rtm_item for rtm_item in menu_data['retail_partners'] if rtm_item['rtm'] == region_rtm['rtm']]
                menu_data['retail_partners'] = []
        else:
            # Hong Kong/Taiwan 增加Education
            for item in menu_data["regions"]:
                rtm_keys = [rtm_item["rtm"] for rtm_item in item["rtms"]]
                if item["region"] != REGION_CM and Education not in rtm_keys:
                    item["rtms"].append({
                        "rtm": "Education",
                        "sub_rtms": [
                            "All"
                        ]
                    })
        ret[Ret.Data] = menu_data

    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e.err_msg, exc_info=True)
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(str(e), exc_info=True)
    return ret


@bp.route('/po_delinquent/channel_view', methods=['GET'])
def channel_view():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: 'Success', Ret.Data: {}}
    try:
        query_params = request.args
        region = query_params.get("region", "China mainland", str)
        lob = query_params.get("lob", "iPhone", str)
        sub_lobs = request.args.getlist("sub_lob[]")
        fiscal_dt = query_params.get("fiscal_dt", "", str)
        channel = query_params.get("channel", CPF, str)

        # 1. 非CP&F, channel就是查询指定rtm下的数据(针对权限)
        region_rtm = PO_DELINQUENT_DASHBOARD_RTM_PERMISSIONS.get(channel, None)
        if region_rtm is None:
            raise ErrorExcept(ErrCode.Param, "please check params, required parameter invalid.")
        if not all([region, lob]):
            raise ErrorExcept(ErrCode.Param, "please check params, required parameter missing.")

        if region not in region_rtm['region']:
            raise ErrorExcept(ErrCode.Param, "please check params, channel mapped region invalid")

        latest_refresh_time, channel_view_data = get_channel_view_data(region=region,
                                                                       sub_lobs=sub_lobs,
                                                                       lob=lob,
                                                                       rtm=region_rtm['rtm'])
        ret[Ret.Data] = {
            "latest_refresh_time": latest_refresh_time,
            "po_delinquent_data": channel_view_data
        }
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e.err_msg, exc_info=True)
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(str(e), exc_info=True)
    return ret


@bp.route('/po_delinquent/channel_view_in_email', methods=['GET'])
def channel_view_in_email():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: 'Success', Ret.Data: {}}
    try:
        query_params = request.args
        lob = query_params.get("lob", "iPhone", str)
        sub_lobs = request.args.getlist("sub_lob[]")
        latest_refresh_time, channel_view_data = channel_view_data_for_email(sub_lobs=sub_lobs, lob=lob, source="api_email")
        ret[Ret.Data] = {
            "latest_refresh_time": latest_refresh_time,
            "po_delinquent_data": channel_view_data
        }
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e.err_msg, exc_info=True)
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(str(e), exc_info=True)
    return ret


@bp.route('/po_delinquent/sold_to_view', methods=['GET'])
def sold_to_view():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: 'Success', Ret.Data: {}}
    try:
        query_params = request.args
        region = query_params.get("region", "China mainland", str)
        lob = query_params.get("lob", "iPhone", str)
        sub_lobs = request.args.getlist("sub_lob[]")
        fiscal_dt = query_params.get("fiscal_dt", "", str)
        channel = query_params.get("channel", CPF, str)

        # 1. 非CP&F, channel就是查询指定rtm下的数据(针对权限)
        region_rtm = PO_DELINQUENT_DASHBOARD_RTM_PERMISSIONS.get(channel, None)
        if region_rtm is None:
            raise ErrorExcept(ErrCode.Param, "please check params, required parameter invalid.")
        if not all([region, lob]):
            raise ErrorExcept(ErrCode.Param, "please check params, required parameter missing.")

        if region not in region_rtm['region']:
            raise ErrorExcept(ErrCode.Param, "please check params, channel mapped region invalid")

        latest_refresh_time, sold_to_view_data = get_sold_to_view_data(region=region,
                                                                       sub_lobs=sub_lobs,
                                                                       lob=lob,
                                                                       rtm=region_rtm['rtm'])
        ret[Ret.Data] = {
            "latest_refresh_time": latest_refresh_time,
            "po_delinquent_data": sold_to_view_data
        }
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e.err_msg, exc_info=True)
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(str(e), exc_info=True)
    return ret

