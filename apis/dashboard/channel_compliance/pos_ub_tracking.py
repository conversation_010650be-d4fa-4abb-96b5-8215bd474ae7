from apis.api_common import *
from domain.dashboard.impl.channel_compliance import RTM_PERMISSION_TO_DB_QUERY_RTM_MAPPING, PartnerUbViewEnum, \
    display_rtm_to_db_rtm_dict, UbTabType, DataTypeSublob, DataTypeOnlinePlatform, DataTypePartner
from domain.dashboard.impl.channel_compliance.partner_impl import get_overall_ub_records, \
    get_overall_ub_dynamic_columns, get_store_type_ub_records, get_store_type_ub_dynamic_columns, get_partner_menu, \
    get_query_permission_rtm
from domain.dashboard.impl.channel_compliance.ub_tracking_service import get_ub_tracking_view

bp = Blueprint("channel_compliance", __name__, url_prefix="/channel_compliance")


@bp.route('/menu', methods=["GET"])
@person_platform_data_permission(http_request=request, platform='Channel Compliance')
def partner_menu():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: 'Success'}
    try:
        # 权限校验
        permission_rtm_list = [
            RTM_PERMISSION_TO_DB_QUERY_RTM_MAPPING.get(role_name, role_name)
            for role_name in g.platform_role_names
        ]
        if not permission_rtm_list:
            raise ErrorExcept(ErrCode.Permissions, "no permission")

        view = request.args.get("view", DataTypePartner, str)
        view_tab = request.args.get("view_tab", PartnerUbViewEnum.OVERALL.value, str)
        menu_data = get_partner_menu(view = view, view_tab=view_tab, permission_rtm_list=permission_rtm_list)
        ret[Ret.Data] = menu_data
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e.err_msg, exc_info=True)
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(str(e), exc_info=True)
    return ret


@bp.route("/partner/overall/view", methods=["GET"])
@person_platform_data_permission(http_request=request, platform='Channel Compliance')
def get_overall_ub_view():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: "Success"}
    try:
        # 参数处理
        query_params = request.args
        fiscal_date = query_params.get("fiscal_date")
        rtm = query_params.get("rtm")
        lob = query_params.get("lob", StrIphone)
        sub_lobs = query_params.getlist("sub_lobs")
        if not all([fiscal_date, rtm, lob]):
            raise ErrorExcept(ErrCode.Param, "please check params, required parameter missing.")

        rtm = display_rtm_to_db_rtm_dict.get(rtm, rtm)
        query_permission_rtm_list = get_query_permission_rtm(rtm=rtm)

        detail_data = get_overall_ub_records(snapshot_date=fiscal_date, lob=lob, sub_lobs=sub_lobs, query_rtm=rtm,
                                             all_platform_role_names=g.all_platform_role_names,
                                             permission_rtm_list=query_permission_rtm_list)
        dynamic_columns = get_overall_ub_dynamic_columns(snapshot_date=fiscal_date)
        result = {
            "detail_data": detail_data,
            "is_monday": True if datetime.strptime(fiscal_date, "%Y-%m-%d").weekday() == 0 else False
        }
        result.update(dynamic_columns)
        ret[Ret.Data] = result
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e, exc_info=True)
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(e, exc_info=True)
    return ret


@bp.route("/partner/store_type/view", methods=["GET"])
@person_platform_data_permission(http_request=request, platform='Channel Compliance')
def get_store_type_ub_view():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: "Success"}
    try:
        # 参数处理
        query_params = request.args
        fiscal_date = query_params.get("fiscal_date")
        rtm = query_params.get("rtm")
        lob = query_params.get("lob", StrIphone)
        sub_lobs = query_params.getlist("sub_lobs")
        if not all([fiscal_date, rtm, lob]):
            raise ErrorExcept(ErrCode.Param, "please check params, required parameter missing.")

        rtm = display_rtm_to_db_rtm_dict.get(rtm, rtm)
        query_permission_rtm_list = get_query_permission_rtm(rtm=rtm, view_tab=PartnerUbViewEnum.STORE_TYPE.value)

        detail_data = get_store_type_ub_records(snapshot_date=fiscal_date, permission_rtm_list=query_permission_rtm_list,
                                                lob=lob, sub_lobs=sub_lobs)
        dynamic_columns = get_store_type_ub_dynamic_columns(snapshot_date=fiscal_date)
        result = {
            "detail_data": detail_data,
            "is_monday": datetime.strptime(fiscal_date, "%Y-%m-%d").weekday() == 0
        }
        result.update(dynamic_columns)
        ret[Ret.Data] = result
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e, exc_info=True)
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(e, exc_info=True)
    return ret


@bp.route("/online_platform/view", methods=["GET"])
@person_platform_data_permission(http_request=request, platform='Channel Compliance')
def get_online_platform_ub_view():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: "Success"}
    try:
        # 参数处理
        query_params = request.args
        fiscal_date = query_params.get("fiscal_date")
        rtm = query_params.get("rtm")
        lob = query_params.get("lob", StrIphone)
        sub_lobs = query_params.getlist("sub_lobs")
        tab = query_params.get("tab", UbTabType.RTM.value)
        if not all([fiscal_date, rtm, lob]):
            raise ErrorExcept(ErrCode.Param, "please check params, required parameter missing.")

        rtm = display_rtm_to_db_rtm_dict.get(rtm, rtm)
        query_permission_rtm_list = get_query_permission_rtm(rtm=rtm)

        ret[Ret.Data] = get_ub_tracking_view(snapshot_date=fiscal_date,
                                             rtm=rtm,
                                             all_platform_role_names=g.all_platform_role_names,
                                             permission_rtm_list=query_permission_rtm_list,
                                             lob=lob, sub_lobs=sub_lobs, tab=tab, data_type=DataTypeOnlinePlatform)
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e, exc_info=True)
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(e, exc_info=True)
    return ret


@bp.route("/sub_lob/view", methods=["GET"])
@person_platform_data_permission(http_request=request, platform='Channel Compliance')
def get_sub_lob_ub_view():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: "Success"}
    try:
        # 参数处理
        query_params = request.args
        fiscal_date = query_params.get("fiscal_date")
        rtm = query_params.get("rtm")
        lob = query_params.get("lob", StrIphone)
        sub_lobs = query_params.getlist("sub_lobs")
        tab = query_params.get("tab", UbTabType.RTM.value)
        if not all([fiscal_date, rtm, lob]):
            raise ErrorExcept(ErrCode.Param, "please check params, required parameter missing.")

        rtm = display_rtm_to_db_rtm_dict.get(rtm, rtm)
        query_permission_rtm_list = get_query_permission_rtm(rtm=rtm)

        ret[Ret.Data] = get_ub_tracking_view(snapshot_date=fiscal_date,
                                             rtm=rtm,
                                             all_platform_role_names=g.all_platform_role_names,
                                             permission_rtm_list=query_permission_rtm_list,
                                             lob=lob, sub_lobs=sub_lobs, tab=tab, data_type=DataTypeSublob)
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e, exc_info=True)
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(e, exc_info=True)
    return ret

