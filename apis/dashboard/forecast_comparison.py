from apis.api_common import *
from data.databend.dashboard.forecast_comparison.forecast_comparison_rtm_and_ml import ForecastComparisonRTMAndML
from domain.dashboard.entity.fiscal_week import cut_fiscal_weeks
from domain.dashboard.impl.fiscal_week_container import FiscalWeekContainer
from domain.dashboard.impl.forecast_comparison.forecast_comparison_impl import get_comparison_result_data
from domain.demand.entity.common_menu import Menu

bp = Blueprint("forecast_comparison", __name__, url_prefix="/dashboard")


@bp.route("/forecast_comparison/menu", methods=["GET"])
def get_menu_for_comparison():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: "Success"}
    try:
        fiscal_weeks = FiscalWeekContainer().get_fiscal_weeks_forecast_comparison()
        # 参数处理
        query_params = request.args
        fiscal_week = query_params.get(
            "fiscal_week", fiscal_weeks[0] if fiscal_weeks else ""
        )
        menu = Menu(query_func=ForecastComparisonRTMAndML.get_distinct_menu_data,
                    query_kwargs={"fiscal_week": fiscal_week})
        menu_data = menu.get_result_menu()
        regions = [i["region"] for i in menu_data["regions"]]
        sub_lobs = [i["sub_lob"] for lobs in menu_data["lobs"] for i in lobs["sub_lobs"] if lobs["lob"] == 'iPhone']
        fiscal_weeks = cut_fiscal_weeks(fiscal_weeks=fiscal_weeks,
                                        length=QUARTER_3_FISCAL_WEEKS_LENGTH,
                                        end_fiscal_weeks=None)
        ret_data = {
            "regions": regions,
            "sub_lobs": sub_lobs,
            "fiscal_weeks": fiscal_weeks
        }
        ret[Ret.Data] = ret_data

    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e, exc_info=True)
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(e, exc_info=True)
    return ret


@bp.route("/forecast_comparison/view", methods=["GET"])
def get_comparison_overview():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: "Success"}
    try:
        # 参数处理
        query_params = request.args
        fiscal_week = query_params.get("fiscal_week")
        region = query_params.get("region", "China mainland")
        sub_lob = query_params.get("sub_lob")
        if not all([fiscal_week, region, sub_lob]):
            raise ErrorExcept(ErrCode.Param, "please check params, required parameter missing.")
        data = get_comparison_result_data(region=region, sub_lob=sub_lob, fiscal_week=fiscal_week)
        ret[Ret.Data] = data
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e, exc_info=True)
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(e, exc_info=True)
    return ret
