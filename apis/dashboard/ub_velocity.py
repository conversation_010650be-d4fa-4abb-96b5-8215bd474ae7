import traceback

from apis.api_common import *
from data.databend.dashboard.ub_velocity import DashboardUbVelocity
from data.fast_lite_dashboard_data import FastLiteDashboardThreshold
from domain.dashboard.entity.fiscal_week import FiscalWeek, cut_fiscal_weeks
from domain.dashboard.impl.fiscal_week_container import FiscalWeek<PERSON>ontainer
from domain.demand.entity.module_switch_enum import ModuleSwitchEnum
from kit.custom_date.custom_week_date import CustomWeekDate
from util.util import nand_color_sorted

bp = Blueprint("ub_velocity", __name__, url_prefix=UrlPrefixUBVelocity)


@bp.route("/ub_velocity/menu", methods=["GET"])
def ub_velocity_menu():
    try:
        fiscal_weeks = FiscalWeekContainer().get_fiscal_weeks_ub_velocity()

        ret = {Ret.Code: ErrCode.Success, Ret.Msg: "Success"}
        # 参数处理
        query_params = request.args
        fiscal_week = query_params.get(
            "fiscal_week", fiscal_weeks[0] if fiscal_weeks else ""
        )
        # # 返回结果
        ret_data = DashboardUbVelocity.get_ub_velocity_menu(fiscal_week)

        fiscal_weeks = cut_fiscal_weeks(fiscal_weeks, FISCAL_WEEKS_LENGTH)
        ret_data["fiscal_weeks"] = fiscal_weeks

        threshold = FastLiteDashboardThreshold.get_threshold(fiscal_week)
        ret_data["threshold"] = threshold

        ret[Ret.Data] = ret_data
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e.err_msg)
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(traceback.format_exc())
    return ret


@bp.route("/current_week", methods=["GET"])
def current_week():
    try:
        ret = {Ret.Code: ErrCode.Success, Ret.Msg: "Success"}
        ret[Ret.Data] = FiscalWeekContainer().get_current_week()
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e.err_msg)
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(traceback.format_exc())
    return ret


@bp.route("/ub_velocity/save_threshold", methods=["POST"])
def save_threshold():
    try:
        ret = {Ret.Code: ErrCode.Success, Ret.Msg: "Success"}
        if not CustomWeekDate(ModuleSwitchEnum.UB_VELOCITY_UPDATE_THRESHOLD.value).is_before_ddl():
            raise ErrorExcept(ErrCode.TimeOutError, "The operate time has expired.")
        body_params = request.json
        threshold = body_params.get("threshold")
        current_week = FiscalWeekContainer().get_current_week()
        FastLiteDashboardThreshold.save_threshold(threshold, current_week)
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e.err_msg)
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(traceback.format_exc())
    return ret


@bp.route("/ub_velocity/view", methods=["GET"])
def ub_velocity_view():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: "Success"}
    try:
        # 参数处理，为空提前设置默认值
        query_params = request.args
        fiscal_weeks = FiscalWeekContainer().get_fiscal_weeks_ub_velocity()
        fiscal_week = query_params.get(
            "fiscal_week", fiscal_weeks[0] if fiscal_weeks else ""
        )
        region = query_params.get("region", "China mainland")
        rtm = query_params.get("rtm", "All")
        sub_rtm = query_params.get("sub_rtm", "All")
        # 判断rtm和subrtm是否在对应MAP的value中，如果在则获取对应的key
        summarized_by = query_params.get("summarized_by", "Nand")
        lob = query_params.get("lob", "iPhone")
        sub_lob = query_params.get("sub_lob")
        if not sub_lob:
            raise ErrorExcept(ErrCode.Param, "please check params.sub_lob must not empty")
        color = nand = False
        sort_key = []
        if summarized_by == "Nand":
            nand = True
            sort_key = ["sub_lob", "nand", "color"]
        if summarized_by == "Color":
            color = True
            sort_key = ["sub_lob", "color", "nand"]

        # sub_lobs
        if len(fiscal_weeks) >= fiscal_weeks.index(fiscal_week)+FISCAL_WEEK_SEVERAL+1:
            
            start_fiscal_week = fiscal_weeks[fiscal_weeks.index(fiscal_week)+FISCAL_WEEK_SEVERAL]
            week_columns = fiscal_weeks[fiscal_weeks.index(fiscal_week)+1:fiscal_weeks.index(fiscal_week)+FISCAL_WEEK_SEVERAL+1]
        else:
            start_fiscal_week = fiscal_weeks[-1]
            week_columns = fiscal_weeks[fiscal_weeks.index(fiscal_week)+1:]
        ub_velocity_data = DashboardUbVelocity.get_ub_velocitys(
            start_fiscal_week,fiscal_week, region, rtm, sub_rtm, lob, sub_lob, color, nand
        )
        sub_lobs = [ub.as_dict() for ub in ub_velocity_data]

        sub_lobs = nand_color_sorted(sub_lobs, sort_key)

        # columns
        week_columns = sorted(week_columns,key=lambda x:FiscalWeek(x).fiscal_week_int,reverse=False)
        week_columns = [FiscalWeek(week).simp_week_name() for week in week_columns]
        ret[Ret.Data] = {"sub_lobs": sub_lobs, "columns": week_columns}
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e.err_msg)
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(traceback.format_exc())
    return ret
