import traceback
from apis.api_common import *
from data.databend.dashboard.mpn_mix import DashboardMpnMix

from data.mysqls.dashboard.dashboard_file_storage import MixUpload
from domain.dashboard.entity.fiscal_week import cut_fiscal_weeks
from domain.dashboard.impl.fiscal_week_container import FiscalWeekContainer
from domain.dashboard.impl.mix_file_reslover import (
    MpnMixDataReslover
)
from domain.dashboard.impl.mpn_mix_reslover import Mpn<PERSON>ix<PERSON><PERSON><PERSON>
from service.allocation_collection_adjustment_service import *

bp = Blueprint("mpn_mix", __name__, url_prefix=UrlPrefixUBVelocity)


@bp.route("/mpn_mix/menu", methods=["GET"])
def mpn_mix_menu():
    try:
        fiscal_weeks = FiscalWeekContainer().get_fiscal_weeks_mpn_mix()
        ret = {Ret.Code: ErrCode.Success, Ret.Msg: "Success"}
        query_params = request.args
        fiscal_week = query_params.get(
            "fiscal_week", fiscal_weeks[0] if fiscal_weeks else ""
        )
        # lobs
        ret_data = DashboardMpnMix.get_mpn_mix_menu(fiscal_week)
        # fiscal_weeks
        fiscal_weeks = cut_fiscal_weeks(fiscal_weeks, FISCAL_WEEKS_LENGTH)
        ret_data["fiscal_weeks"] = fiscal_weeks
        ret[Ret.Data] = ret_data
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e.err_msg)
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(traceback.format_exc())
    return ret


@bp.route("/mpn_mix/view", methods=["GET"])
def mpn_mix_view():
    try:
        fiscal_weeks = FiscalWeekContainer().get_fiscal_weeks_mpn_mix()

        ret = {Ret.Code: ErrCode.Success, Ret.Msg: "Success"}
        # 参数处理
        query_params = request.args
        fiscal_week = query_params.get(
            "fiscal_week", fiscal_weeks[0] if fiscal_weeks else ""
        )
        region = query_params.get("region", "China mainland")
        lob = query_params.get("lob", "iPhone")
        sub_lob = query_params.get("sub_lob")
        if not sub_lob:
            raise ErrorExcept(ErrCode.Param, "please check params.")
        summarized_by = query_params.get("summarized_by", "Nand")
        color = nand = False
        sort_key = []
        if summarized_by == "Nand":
            nand = True
            sort_key = ["sub_lob", "nand", "color"]
        if summarized_by == "Color":
            color = True
            sort_key = ["sub_lob", "color", "nand"]

        # 返回结果
        result = MpnMixReslover().merge_mpn_mix_and_adjust(fiscal_week, region, lob, sub_lob, color, nand)

        result = nand_color_sorted(result, sort_key)

        ret[Ret.Data] = result
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e.err_msg)
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(traceback.format_exc())
    return ret


@bp.route("/mpn_mix/download_all", methods=["GET"])
def download_all():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: "Success"}
    try:
        query_params = request.args
        fiscal_week = query_params.get("fiscal_week")
        region = query_params.get("region")
        sub_lob = query_params.get("sub_lob")
        
        if not fiscal_week or not region or not sub_lob:
            raise ErrorExcept(ErrCode.Param, "please check params.")
        
        sub_lobs = sub_lob.split(",")
        result = DashboardMpnMix.mpn_mix_for_download(fiscal_week, region, sub_lobs)
        adjust_mix = MixUpload.get_adjust_mix(fiscal_week, region, '', sub_lobs)

        file_name, file_bytes = MpnMixDataReslover().mpn_mix_data_to_file(
            fiscal_week, result, adjust_mix
        )
        response = make_response(
            send_file(
                file_bytes,
                mimetype="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                download_name=file_name,
                as_attachment=True,
            )
        )
        response.headers["Content-Disposition"] = f'attachment; filename="{file_name}"'
        return response
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e.err_msg)
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(traceback.format_exc())
    return ret
