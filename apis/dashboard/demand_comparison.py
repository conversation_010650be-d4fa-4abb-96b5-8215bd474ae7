from apis.api_common import *
from data.cpf_data_source import OdsFastCPFActiveSKULob
from data.mysqls.demand.demand_by_region_pool import DemandByRegionPool
from data.mysqls.demand.demand_state import TblDemandState
from domain.dashboard.entity.fiscal_week import cut_fiscal_weeks
from domain.dashboard.entity.series_tier_sublob import SeriesTierSublob, SeriesTierSublobConverter
from domain.dashboard.impl.demand_comparison_resolver import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ol<PERSON>
from domain.demand.entity.common_menu import Menu
from domain.demand.entity.const import FINAL_DEMAND
from domain.demand.entity.state import DemandState
from domain.supply.entity import Lob
from kit.custom_sort import CustomSort

bp = Blueprint("demand_comparison", __name__, url_prefix=UrlPrefixUBVelocity)

# demand comparison HR_LR type mapping
HR_LR_TYPE_MAPPING = {
    "All": "All",
    "High Runner": "HR",
    "Low Runner": "LR"
}


@bp.route('/demand_comparison/view', methods=['GET'])
def demand_comparison_view():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: 'Success', Ret.Data: {}}
    try:
        # 参数处理
        query_params = request.args
        fiscal_week = query_params.get("fiscal_week")
        region = query_params.get("region", REGION_CM)
        hr_lr = HR_LR_TYPE_MAPPING[query_params.get("hr_lr", ALL)]
        forecast_version = query_params.get("forecast_version")
        lob = query_params.get("lob", Lob.IPHONE.value)
        sub_lob = query_params.get("sub_lob")
        summarized_by = query_params.get("summarized_by", "Nand")
        if not all([fiscal_week, hr_lr, sub_lob, forecast_version, summarized_by]):
            raise ErrorExcept(
                ErrCode.Param, "please check params, required parameter missing."
            )
        if hr_lr == ALL:
            hr_lr = None

        series_and_tier_sublobs: list[SeriesTierSublob] = OdsFastCPFActiveSKULob.get_series_and_tier_sublob(region=region, lob=lob)
        sc = SeriesTierSublobConverter(series_tier_sub_lobs=series_and_tier_sublobs)
        sub_lobs = sc.get_sub_lobs_by_series_tier(sub_lob)
        sub_lobs = sub_lobs or [sub_lob]
        if ALL in sub_lobs:
            sub_lobs = []

        color = nand = False
        sort_key = ["nand", "color"]
        if summarized_by == "Nand":
            nand = True
            sort_key = ["nand", "color"]
        if summarized_by == "Color":
            color = True
            sort_key = ["color", "nand"]

        # 返回结果
        resolver = DemandComparisonResolver(fiscal_week=fiscal_week,
                                            region=region,
                                            lob=lob,
                                            sub_lobs=sub_lobs,
                                            forecast_version=forecast_version,
                                            hr_lr=hr_lr)
        result, latest_refresh_time = resolver.get_demand_comparison_data(color=color, nand=nand, sort_keys=sort_key)
        ret[Ret.Data] = {
            "demand_comparison_detail": result,
            "latest_refresh_time": latest_refresh_time,
        }
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e.err_msg, exc_info=True)
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(str(e), exc_info=True)
    return ret


@bp.route('/demand_comparison/menu', methods=["GET"])
def demand_comparison_menu():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: 'Success'}
    try:
        fiscal_weeks = TblDemandState.query_distinct_fiscal_weeks_by_demand(demand=FINAL_DEMAND,
                                                                            demand_state=DemandState.CalculationCompleted.value)
        # 参数处理
        query_params = request.args
        fiscal_week = query_params.get("fiscal_week", "")
        region = query_params.get("region", REGION_CM)
        lob = query_params.get("lob", Lob.IPHONE.value)
        if not fiscal_week and fiscal_weeks:
            fiscal_week = fiscal_weeks[0]
        menu = Menu(query_func=DemandByRegionPool.get_woi_menu, query_kwargs={"fiscal_week": fiscal_week, "lob": Lob.IPHONE.value})
        menu_data = menu.get_result_menu()
        forecast_version = ["CW+1", "CW+2"]
        hr_lr = ["All", "High Runner", "Low Runner"]
        regions = [region]
        sub_lobs = []
        for lobs in menu_data["lobs"]:
            if lobs["lob"] == Lob.IPHONE.value:
                sub_lobs = [sub_item["sub_lob"] for sub_item in lobs["sub_lobs"]]
                break
        series_and_tier_sublobs: list[SeriesTierSublob] = OdsFastCPFActiveSKULob.get_series_and_tier_sublob(region=region, lob=lob)
        sc = SeriesTierSublobConverter(series_tier_sub_lobs=series_and_tier_sublobs)
        series_tiers = sc.get_series_tier_by_sub_lobs(sub_lobs)
        sub_lobs += series_tiers
        sorted_sub_lobs = CustomSort.sort_iphone_sublobs(sub_lobs)
        fiscal_weeks = cut_fiscal_weeks(fiscal_weeks=fiscal_weeks,
                                        length=QUARTER_3_FISCAL_WEEKS_LENGTH,
                                        end_fiscal_weeks=None)
        ret_data = {
            "regions": regions,
            "forecast_version": forecast_version,
            "hr_lr": hr_lr,
            "sub_lobs": sorted_sub_lobs,
            "fiscal_weeks": fiscal_weeks
        }
        ret[Ret.Data] = ret_data
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e.err_msg, exc_info=True)
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(str(e), exc_info=True)
    return ret
