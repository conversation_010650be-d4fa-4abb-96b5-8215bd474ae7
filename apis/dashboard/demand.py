import traceback
from apis.api_common import *
from domain.dashboard.impl.mix_file_reslover import MpnMixFileReslover, \
    upload_mix_file_service, get_upload_mix_file_service, delete_file_service
from domain.demand.entity.module_switch_enum import ModuleSwitchEnum
from kit.custom_date.custom_week_date import CustomWeekDate
from util.file_util import convert_file_path
from data.databend.dashboard.mpn_mix import DashboardMpnMix
from concurrent.futures import ThreadPoolExecutor

bp = Blueprint("demand", __name__, url_prefix=UrlPrefixdemand)

@bp.route("/mpn_mix/download_adjuested_mix_template", methods=["GET"])
def download_adjuested_mix_template():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: "Success"}
    # 参数处理
    query_params = request.args
    fiscal_week = query_params.get("fiscal_week")
    region = query_params.get("region")
    sub_lob = query_params.get("sub_lob")

    if not fiscal_week or not region or not sub_lob:
        raise ErrorExcept(ErrCode.Param, "please check params.")

    sub_lobs = sub_lob.split(",")
    try:
        result = DashboardMpnMix.get_nand_color_by_fiscal_week(
            fiscal_week, region, sub_lobs
        )
        file_name, file_bytes = MpnMixFileReslover().template_for_adjuested_mix(
            fiscal_week, result
        )
        response = make_response(
            send_file(
                file_bytes,
                mimetype="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                download_name=file_name,
                as_attachment=True,
            )
        )
        response.headers["Content-Disposition"] = f'attachment; filename="{file_name}"'
        return response
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e.err_msg)
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(traceback.format_exc())
    return ret


@bp.route("/mpn_mix/adjusted_mix_file_record", methods=["GET"])
def adjusted_mix_file_record():
    try:
        ret = {Ret.Code: ErrCode.Success, Ret.Msg: "Success"}
        # 参数处理
        query_params = request.args
        fiscal_week = query_params.get('fiscal_week')
        ret[Ret.Data] = get_upload_mix_file_service(fiscal_week)
        return ret
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e.err_msg)
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(traceback.format_exc())
    return ret


@bp.route("/mpn_mix/delete_adjusted_mix_file", methods=["GET"])
def delete_adjusted_mix_file():
    try:
        ret = {Ret.Code: ErrCode.Success, Ret.Msg: "Success"}
        if not CustomWeekDate(ModuleSwitchEnum.MPN_MIX_UPLOAD.value).is_before_ddl():
            raise ErrorExcept(ErrCode.TimeOutError, "The operate time has expired.")
        # 参数处理
        query_params = request.args
        file_id = query_params.get('id')
        # 操作人信息
        operator = request.headers.get(ShieldPrsNickName) + " " + request.headers.get(ShieldPrsLastName)
        ret[Ret.Data] = delete_file_service(file_id, operator)
        return ret
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e.err_msg)
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(traceback.format_exc())
    return ret


@bp.route("/mpn_mix/upload", methods=["POST"])
def upload_mix_file():
    ret = { Ret.Code: ErrCode.Success, Ret.Msg: 'Success' }
    try:
        params = request.args
        if not CustomWeekDate(ModuleSwitchEnum.MPN_MIX_UPLOAD.value).is_before_ddl():
            raise ErrorExcept(ErrCode.TimeOutError, "The operate time has expired.")
        fiscal_week = params.get("fiscal_week", "", str)
        file = request.files.get('file')
        if not fiscal_week or file is None:
            raise ErrorExcept(ErrCode.Param, "Param is required, please double check.")
        
        # 操作人信息
        operator = request.headers.get(ShieldPrsNickName) + " " + request.headers.get(ShieldPrsLastName)
        
        # 查询nand, color
        region, lob = "China mainland", "iPhone"
        
        pool = ThreadPoolExecutor(max_workers=2)
        future_color = pool.submit(DashboardMpnMix.list_sublob_colors, fiscal_week, region, lob)
        future_nand = pool.submit(DashboardMpnMix.list_sublob_nands, fiscal_week, region, lob)

        sublob_colors = future_color.result()
        sublob_nands = future_nand.result()
        pool.shutdown()
        
        ret[Ret.Data] = upload_mix_file_service(fiscal_week, file, operator, {
            "sublob_colors": sublob_colors,
            "sublob_nands": sublob_nands,
        })
        return ret

    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e.err_msg)
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(traceback.format_exc())
    return ret


@bp.route("/download/file")
def download_file():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: "Success"
    }
    
    file_path = request.args.get("file_path")
    if file_path is None:
        ret[Ret.Code] = ErrCode.Param
        ret[Ret.Msg] = 'file_path is required.'
        return ret
    file_path = convert_file_path(file_path)
    if file_path == "":
        ret[Ret.Code] = ErrCode.System
        ret[Ret.Msg] = 'file does not exist.'
        return ret 
    try:
        response = make_response(send_file(file_path, as_attachment=True))
        response.headers["Content-Disposition"] = f"attachment; filename=temporary_name.xlsx"
    except FileNotFoundError:
        ret[Ret.Code] = ErrCode.System
        ret[Ret.Msg] = 'No such file or directory.'
        return ret
    return response
