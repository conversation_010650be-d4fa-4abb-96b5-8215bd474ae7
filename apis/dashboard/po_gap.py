from apis.api_common import *
from domain.dashboard.impl.po_gap_impl import get_po_gap_menu, get_product_view_data, get_channel_view_data, \
    get_po_gap_file, get_sold_to_view_data, get_fiscal_weeks
from domain.demand.entity.common_menu import Menu

bp = Blueprint("po_gap", __name__, url_prefix=UrlPrefixUBVelocity)


@bp.route('/po_gap/product_view', methods=['GET'])
def product_view():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: 'Success', Ret.Data: {}}
    try:
        query_params = request.args
        region = query_params.get("region", "China mainland", str)
        rtm = query_params.get("rtm", "", str)
        sub_rtm = query_params.get("sub_rtm", "", str)
        lob = query_params.get("lob", "iPhone", str)
        sub_lobs = query_params.getlist("sub_lob[]")
        fiscal_week = query_params.get("fiscal_week", "", str)

        if not all([region, rtm, sub_rtm, lob, fiscal_week]):
            raise ErrorExcept(ErrCode.Param, "please check params, required parameter missing.")

        latest_refresh_time, product_view_data = get_product_view_data(region=region,
                                                                       rtm=rtm,
                                                                       sub_rtm=sub_rtm,
                                                                       lob=lob,
                                                                       sub_lobs=sub_lobs,
                                                                       fiscal_week=fiscal_week)
        ret[Ret.Data] = {
            "latest_refresh_time": latest_refresh_time,
            "po_gap_data": product_view_data
        }
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e.err_msg, exc_info=True)
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(str(e), exc_info=True)
    return ret


@bp.route('/po_gap/menu', methods=["GET"])
def po_gap_menu():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: 'Success'}
    try:
        channel = request.args.get("channel", CPF, str)
        fiscal_week = request.args.get("fiscal_week")

        fiscal_week_list = get_fiscal_weeks()
        if not fiscal_week and fiscal_week_list:
            fiscal_week = fiscal_week_list[0]

        # 1. 非CP&F, channel就是查询指定rtm下的数据
        rtm = None
        if channel != CPF:
            rtm = channel
        query_kwargs = {
            "rtm": rtm,
            "fiscal_week": fiscal_week
        }
        menu = Menu(query_func=get_po_gap_menu, need_retail_partner=True, query_kwargs=query_kwargs)
        menu_data = menu.get_result_menu()
        # 去掉sub_lob是All的和mpns字段
        for item in menu_data["lobs"]:
            item["sub_lobs"] = [sub_item["sub_lob"] for sub_item in item["sub_lobs"] if sub_item["sub_lob"] != "All"]

        # 针对各个rtm的权限
        if rtm:
            for item in menu_data["regions"]:
                # 去掉rtm=all的筛选
                item["rtms"] = [rtm_item for rtm_item in item["rtms"] if rtm_item["rtm"] != "All"]
                if item["region"] != REGION_CM:
                    continue
                if menu_data['retail_partners']:
                    item['rtms'] = [rtm_item for rtm_item in menu_data['retail_partners'] if rtm_item['rtm'] == rtm]
                menu_data['retail_partners'] = []
        else:
            # Hong Kong/Taiwan 增加Education
            region_add_rtm_dict = {
                REGION_TAIWAN: [Education],
                REGION_HK: [Education],
            }
            for item in menu_data["regions"]:
                if item["region"] == REGION_CM:
                    continue
                rtm_keys = [rtm_item["rtm"] for rtm_item in item["rtms"]]
                add_rtm_list = region_add_rtm_dict[item["region"]]
                overflow_rtms = [i for i in add_rtm_list if i not in rtm_keys]
                for rtm in overflow_rtms:
                    item["rtms"].append({
                        "rtm": rtm,
                        "sub_rtms": [
                            "All"
                        ]
                    })
            # China Mainland 增加Education、Enterprise
            retail_partners_add_rtm_list = [Enterprise, Education]
            tmp_retail_partners_list = []
            rtm_keys = [rtm_item["rtm"] for rtm_item in menu_data["retail_partners"]]
            overflow_rtms = [i for i in retail_partners_add_rtm_list if i not in rtm_keys]
            for rtm in overflow_rtms:
                tmp_retail_partners_list.append({
                    "rtm": rtm,
                    "sub_rtms": [
                        "All"
                    ]
                })
            if tmp_retail_partners_list:
                menu_data["retail_partners"].extend(tmp_retail_partners_list)

        menu_data['fiscal_weeks'] = fiscal_week_list
        ret[Ret.Data] = menu_data
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e.err_msg, exc_info=True)
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(str(e), exc_info=True)
    return ret


@bp.route('/po_gap/channel_view', methods=['GET'])
def channel_view():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: 'Success', Ret.Data: {}}
    try:
        query_params = request.args
        region = query_params.get("region", "China mainland", str)
        lob = query_params.get("lob", "iPhone", str)
        sub_lobs = request.args.getlist("sub_lob[]")
        channel = query_params.get("channel", CPF, str)
        fiscal_week = query_params.get("fiscal_week", "", str)
        # 1. 非CP&F, channel就是查询指定rtm下的数据(针对权限)
        rtm = None
        if channel != CPF:
            rtm = channel
        if not all([region, lob, fiscal_week]):
            raise ErrorExcept(ErrCode.Param, "please check params, required parameter missing.")
        latest_refresh_time, channel_view_data = get_channel_view_data(region=region,
                                                                       sub_lobs=sub_lobs,
                                                                       lob=lob,
                                                                       rtm=rtm,
                                                                       fiscal_week=fiscal_week)
        ret[Ret.Data] = {
            "latest_refresh_time": latest_refresh_time,
            "po_gap_data": channel_view_data
        }
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e.err_msg, exc_info=True)
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(str(e), exc_info=True)
    return ret


@bp.route("/po_gap/download", methods=["GET"])
def download_po_gap_file():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success',
        Ret.Data: {}
    }
    try:
        channel = request.args.get("channel", CPF, str)
        fiscal_week = request.args.get("fiscal_week", "", str)
        if not fiscal_week:
            raise ErrorExcept(ErrCode.Param, "please check params, required parameter missing.")
        # 1. 非CP&F, channel就是查询指定rtm下的数据(针对权限)
        rtm = None
        if channel != CPF:
            rtm = channel
        file_name, excel_file = get_po_gap_file(rtm=rtm, fiscal_week=fiscal_week, channel=channel)
        response = make_response(send_file(excel_file,
                                           mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                                           download_name=file_name,
                                           as_attachment=True))
        response.headers["Content-Disposition"] = f'attachment; filename="{file_name}"'
        return response
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e.err_msg, exc_info=True)
        return ret
    except FileNotFoundError as e:
        logger.error(str(e))
        ret[Ret.Code] = ErrCode.System
        ret[Ret.Msg] = 'No such file or directory.'
        logger.error(str(e), exc_info=True)
        return ret
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(str(e), exc_info=True)
        return ret


@bp.route('/po_gap/sold_to_view', methods=['GET'])
def sold_to_view():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: 'Success', Ret.Data: {}}
    try:
        query_params = request.args
        region = query_params.get("region", "China mainland", str)
        lob = query_params.get("lob", "iPhone", str)
        sub_lobs = request.args.getlist("sub_lob[]")
        channel = query_params.get("channel", CPF, str)
        fiscal_week = query_params.get("fiscal_week", "", str)

        # 1. 非CP&F, channel就是查询指定rtm下的数据(针对权限)
        rtm = None
        if channel != CPF:
            rtm = channel
        if not all([region, lob, fiscal_week]):
            raise ErrorExcept(ErrCode.Param, "please check params, required parameter missing.")
        latest_refresh_time, sold_to_view_data = get_sold_to_view_data(region=region,
                                                                       sub_lobs=sub_lobs,
                                                                       lob=lob,
                                                                       rtm=rtm,
                                                                       fiscal_week=fiscal_week)
        ret[Ret.Data] = {
            "latest_refresh_time": latest_refresh_time,
            "po_gap_data": sold_to_view_data
        }
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e.err_msg, exc_info=True)
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(str(e), exc_info=True)
    return ret
