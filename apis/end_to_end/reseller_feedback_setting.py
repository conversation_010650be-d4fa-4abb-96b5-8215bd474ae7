from typing import Dict, Any

from flask import Blueprint, request, make_response, send_file

from domain.end_to_end.impl.feedback_setting_impl import get_menu, get_list, publish, update_setting, batch_update
from util.conf import logger
from util.const import ErrCode, Ret, ErrorExcept, ShieldPrsId, UrlEndToEndPrefix

bp = Blueprint('fast_end_to_end_variance', __name__, url_prefix=UrlEndToEndPrefix)


@bp.route("/feedback_setting/menu", methods=["GET"])
def get_feedback_setting_menu() -> Dict[str, Any]:
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        fiscal_week_name = request.args.get('fiscal_week_name')
        lob = request.args.get('lob')

        menu_data = get_menu(fiscal_week_name, lob)
        ret[Ret.Data] = menu_data
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.exception(e)
    return ret


@bp.route("/feedback_setting/list", methods=["POST"])
def get_fcst_list():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        fiscal_week = request.json.get('fiscal_week_name')
        lob = request.json.get('lob')
        list_data = get_list(fiscal_week, lob)
        ret[Ret.Data] = list_data
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.exception(e)
    return ret


@bp.route("/feedback_setting/publish", methods=["POST"])
def publish_fcst():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        data = request.json.get("data")
        lob = request.json.get('lob')
        fiscal_week_name = request.json.get("fiscal_week_name")
        fiscal_week = request.json.get("fiscal_week")
        weeks = request.json.get("forecast_weeks")
        operator = request.headers.get(ShieldPrsId)
        latest_publish_time = publish(data, weeks, fiscal_week, fiscal_week_name, lob, operator)
        ret[Ret.Data] = latest_publish_time.strftime('%Y-%m-%d %H:%M:%S') if latest_publish_time else None
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.exception(e)
    return ret


@bp.route("/feedback_setting/update", methods=["POST"])
def update_fcst():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        data = request.json.get("data")
        lob = request.json.get('lob')
        fiscal_week_name = request.json.get("fiscal_week_name")
        fiscal_week = request.json.get("fiscal_week")
        weeks = request.json.get("forecast_weeks")
        operator = request.headers.get(ShieldPrsId)
        
        update_setting(data, weeks, fiscal_week, fiscal_week_name, lob, operator)
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.exception(e)
    return ret


@bp.route("/feedback_setting/batch_update", methods=["POST"])
def batch_update_fcst():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        operator = request.headers.get(ShieldPrsId)
        update_fiscal_weeks = request.json.get("update_forecast_weeks")
        forecast_weeks = request.json.get("forecast_weeks")
        fiscal_week = request.json.get("fiscal_week")
        fiscal_week_name = request.json.get("fiscal_week_name")
        lob = request.json.get("lob")
        rtms = request.json.get("rtms")
        sub_lobs = request.json.get("sub_lobs")
        variance = request.json.get("variance")
        
        batch_update(fiscal_week_name, lob, update_fiscal_weeks, fiscal_week, forecast_weeks, rtms, sub_lobs, variance, operator)
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.exception(e)
    return ret


