from typing import Dict, Any
from flask import Blueprint, request

from util.conf import logger
from util.const import <PERSON>rrC<PERSON>, Ret, ErrorExcept, UrlEndToEndPrefix
from domain.end_to_end.impl.rtm_view.demand_forecast_feedback_impl import get_demand_forecast_feedback_dict

bp = Blueprint('demand_forecast_feedback', __name__, url_prefix=UrlEndToEndPrefix)


@bp.route("/rtm_view/demand_forecast_feedback", methods=["GET"])
def get_demand_and_forecast_feedback() -> Dict[str, Any]:
    ret = { Ret.Code: ErrCode.Success, Ret.Msg: 'ok' }
    try:
        reseller_id = request.args.get('reseller_id')
        fiscal_week = request.args.get('fiscal_week')
        rtm = request.args.get('rtm')
        sub_lob = request.args.get('sub_lob')
        version = request.args.get('version')
        if version:
            version = int(version)
        if fiscal_week is None:
            raise ErrorExcept(ErrCode.Param, "please check params.")

        data = get_demand_forecast_feedback_dict(reseller_id, fiscal_week,rtm, version, sub_lob)
        ret[Ret.Data] = data
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.exception(e)
    return ret