import traceback
from typing import Dict, Any

from flask import Blueprint, request, make_response, send_file

from domain.demand.impl.forecast_advice import get_forecast_advice_download_file
from domain.end_to_end.impl.ml_fcst_quantile_impl import get_list, update_setting, get_menu, batch_update, \
    get_download_file, publish
from domain.permission.impl.permission_impl import fast_e2e_permissions
from util.conf import logger
from util.const import ErrCode, Ret, ErrorExcept, ShieldPrsId, UrlEndToEndPrefix, StrRTMCPF

bp = Blueprint('fast_end_to_end_quantile', __name__, url_prefix=UrlEndToEndPrefix)


@bp.route("/ml_fcst_quantile/menu", methods=["GET"])
def get_ml_fcst_quantile_menu() -> Dict[str, Any]:
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        fiscal_week_name = request.args.get('fiscal_week_name')
        lob = request.args.get('lob')

        menu_data = get_menu(fiscal_week_name, lob)
        ret[Ret.Data] = menu_data
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.exception(e)
    return ret


# TODO: POST 方法进行过渡使用，前端调用更改之后可以进行删除
@bp.route("/ml_fcst_quantile/list", methods=["POST", "GET"])
def get_fcst_list():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        if request.method == 'GET':
            params = request.args
        else:
            params = request.json
        fiscal_week = params.get('fiscal_week_name')
        lob = params.get('lob')
        list_data = get_list(fiscal_week, lob)
        ret[Ret.Data] = list_data
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.exception(e)
    return ret


@bp.route("/ml_fcst_quantile/publish", methods=["POST"])
def publish_fcst():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        data = request.json.get("data")
        lob = request.json.get('lob')
        fiscal_week_name = request.json.get("fiscal_week_name")
        fiscal_week = request.json.get("fiscal_week")
        weeks = request.json.get("forecast_weeks")
        operator = request.headers.get(ShieldPrsId)
        latest_publish_time = publish(data, weeks, fiscal_week, fiscal_week_name, lob, operator)
        ret[Ret.Data] = latest_publish_time.strftime('%Y-%m-%d %H:%M:%S') if latest_publish_time else None
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.exception(e)
    return ret


@bp.route("/ml_fcst_quantile/update", methods=["POST"])
def update_fcst():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        data = request.json.get("data")
        lob = request.json.get('lob')
        fiscal_week_name = request.json.get("fiscal_week_name")
        fiscal_week = request.json.get("fiscal_week")
        weeks = request.json.get("forecast_weeks")
        operator = request.headers.get(ShieldPrsId)
        
        update_setting(data, weeks, fiscal_week, fiscal_week_name, lob, operator)
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.exception(e)
    return ret


@bp.route("/ml_fcst_quantile/batch_update", methods=["POST"])
def batch_update_fcst():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        operator = request.headers.get(ShieldPrsId)
        update_fiscal_weeks = request.json.get("update_forecast_weeks")
        forecast_weeks = request.json.get("forecast_weeks")
        fiscal_week = request.json.get("fiscal_week")
        fiscal_week_name = request.json.get("fiscal_week_name")
        lob = request.json.get("lob")
        rtms = request.json.get("rtms")
        sub_lobs = request.json.get("sub_lobs")
        quantile_level = request.json.get("quantile_level")
        
        batch_update(fiscal_week_name, lob, update_fiscal_weeks, fiscal_week, forecast_weeks, rtms, sub_lobs, quantile_level, operator)
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.exception(e)
    return ret


@bp.route("/ml_fcst_quantile/download", methods=["GET"])
@fast_e2e_permissions(request)
def forecast_download():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success',
        Ret.Data: {}
    }
    fiscal_week_name = request.args.get("fiscal_week_name", str)
    lob = request.args.get("lob")
    rtm = request.args.get("rtm")
    if rtm == StrRTMCPF:
        rtm = None
    try:
        file_name, excel_file = get_download_file(fiscal_week_name=fiscal_week_name, lob=lob, rtm=rtm)
        response = make_response(send_file(excel_file,
                                           mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                                           download_name=file_name,
                                           as_attachment=True))
        response.headers["Content-Disposition"] = f'attachment; filename="{file_name}"'
        return response
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(traceback.format_exc())
        return ret


@bp.route('/forecast_advice/download', methods=["GET"])
def download_forecast_advice():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success',
        Ret.Data: {}
    }
    fiscal_week_name = request.args.get("fiscal_week_name", str)
    lob = request.args.get("lob")
    rtm = request.args.get("rtm")
    if rtm == 'CPF':
        rtm = None
    try:
        file_name, excel_file = get_forecast_advice_download_file(fiscal_week_name=fiscal_week_name, lob=lob, rtm=rtm)
        response = make_response(send_file(excel_file,
                                           mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                                           download_name=file_name,
                                           as_attachment=True))
        response.headers["Content-Disposition"] = f'attachment; filename="{file_name}"'
        return response
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.exception(e)
        return ret