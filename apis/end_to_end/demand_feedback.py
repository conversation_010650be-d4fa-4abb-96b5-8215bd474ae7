import traceback
from typing import Dict, Any

from flask import Blueprint, request, make_response, send_file

from domain.end_to_end.impl.demand_feedback_impl import get_menu, get_region_list, get_channel_list, \
    get_channel_download_file, get_published_status
from domain.permission.impl.permission_impl import fast_e2e_permissions
from util.conf import logger
from util.const import ErrCode, Ret, ErrorExcept, UrlEndToEndPrefix, StrRTMCPF

bp = Blueprint('fast_end_to_end_demand_woi', __name__, url_prefix=UrlEndToEndPrefix)


@bp.route("/demand_overview/menu", methods=["GET"])
def get_demand_overview_menu() -> Dict[str, Any]:
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        fiscal_week_name = request.args.get('fiscal_week')
        lob = request.args.get('lob')

        menu_data = get_menu(fiscal_week_name, lob)
        ret[Ret.Data] = menu_data
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.exception(e)
    return ret


@bp.route("/demand_overview/country_mpn_view", methods=["POST"])
@fast_e2e_permissions(request)
def get_country_mpn_view() -> Dict[str, Any]:
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        fiscal_week_name = request.json.get('fiscal_week')
        lob = request.json.get('lob')
        sub_lobs = request.json.get('sub_lob')
        nand = request.json.get('nand')
        color = request.json.get('color')
        rtm = request.json.get("rtm")
        if rtm == StrRTMCPF:
            rtm = None
        ret[Ret.Data] = get_region_list(fiscal_week_name=fiscal_week_name, lob=lob, sub_lobs=sub_lobs, rtm=rtm, nand=nand, color=color)
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.exception(e)
    return ret


@bp.route("/demand_overview/get_published_status", methods=["POST"])
def published_status() -> Dict[str, Any]:
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        fiscal_week_name = request.json.get('fiscal_week')
        lob = request.json.get('lob')
        ret[Ret.Data] = get_published_status(fiscal_week_name=fiscal_week_name, lob=lob)
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.exception(e)
    return ret


# TODO: POST 方法进行过渡使用，前端调用更改之后可以进行删除
@bp.route("/demand_overview/channel_view", methods=["POST", "GET"])
def get_channel_view() -> Dict[str, Any]:
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        if request.method == 'GET':
            params = request.args
            sub_lobs = params.getlist('sub_lob[]')
            nands = params.getlist('nand[]')
            colors = params.getlist('color[]')
        else:
            params = request.json
            sub_lobs = params.get('sub_lob')
            nands = params.get('nand')
            colors = params.get('color')
        fiscal_week_name = params.get('fiscal_week')
        lob = params.get('lob')
        ret[Ret.Data] = get_channel_list(fiscal_week_name, lob, sub_lobs, nands, colors)
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.exception(e)
    return ret


@bp.route("/demand_overview/download", methods=["GET"])
@fast_e2e_permissions(request)
def demand_overview_download():
    '''Demand 3.0 下载'''
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success',
        Ret.Data: {}
    }
    fiscal_week_name = request.args.get("fiscal_week")
    lob = request.args.get("lob")
    rtm = request.args.get("rtm")
    file_type = request.args.get("file_type", default='', type=str).lower() # CP&F 可以下载combined/splited版本的数据
    try:
        file_name, excel_file = get_channel_download_file(fiscal_week_name=fiscal_week_name, lob=lob, rtm=rtm, file_type=file_type)
        response = make_response(send_file(excel_file,
                                           mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                                           download_name=file_name,
                                           as_attachment=True))
        response.headers["Content-Disposition"] = f'attachment; filename="{file_name}"'
        return response
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(traceback.format_exc())
        return ret
