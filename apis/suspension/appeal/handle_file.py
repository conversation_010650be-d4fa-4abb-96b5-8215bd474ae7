from datetime import datetime
import os
import sys

from apis.api_common import Blueprint, request, logger
from util.const import (
    Ret, ErrCode, ErrorExcept, ShieldPrsId, ShieldPrsEmail,
    FileUploadError
)
from util.file_util import get_absolute_path
from util.util import env_dev

bp = Blueprint('suspension_appeal', __name__)


@bp.route('/suspension/upload', methods=['POST'])
def suspension_upload_file():
    ret = { Ret.Code: ErrCode.Success, Ret.Msg: 'ok' }
    try:
        appeal_id = request.form.get('appeal_id', type=int)
    except ValueError:
        ret[Ret.Code] = ErrCode.Param
        ret[Ret.Msg] = 'appeal_id must be integer'
        return ret
    
    if appeal_id is None:
        raise ErrorExcept(ErrCode.Param, 'appeal_id must be not null')
    
    try:
        file = request.files.get("file")
        uploader_id = request.headers.get(ShieldPrsId)
        uploader_email = request.headers.get(ShieldPrsEmail)
        logger.info(f"uploader: {uploader_email} start upload file.")
        # 路径中增加日期，方便之后查看和删除对应的EFS文件
        date_str = datetime.now().strftime("%Y-%m-%d")
        common_file_path = f"suspension/appeal/{date_str}/{appeal_id}"
        name, ext = os.path.splitext(file.filename)
        # 特殊处理Keynote文件
        if ext == '.key':
            ext = f'{ext}.tmp' 
        secure_file_name = f"{name}_{uploader_id}{ext}"
        storage_file_path = f"/uploads/{common_file_path}/{secure_file_name}"
        file_path = get_absolute_path(f'/uploads/{common_file_path}')
        save_path = f"{file_path}{secure_file_name}"
        file.save(save_path)
        efs_url = get_efs_url(f"{common_file_path}/{secure_file_name}")
        file_info = {
            "efs_url": efs_url,
            "file_path": storage_file_path,
            "origin_file_name": file.filename
        }
        logger.info(f"uploader: {uploader_email} finish upload file, file info: {file_info}.")
        ret["data"] = file_info
    except ErrorExcept as e:
        logger.exception(e)
        ret[Ret.Code] = e.code
        if len(e.err_msg) <= 500:
            ret[Ret.Msg] = e.err_msg
        else:
            ret[Ret.Msg] = e.err_msg[:500] + "..."
    except Exception as e:
        logger.exception(e)
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = FileUploadError.UnknownError
        ret["error"] = str(e)
    return ret


@bp.route('/suspension/delete_file', methods=['POST'])
def suspension_delete_file():
    ret = { Ret.Code: ErrCode.Success, Ret.Msg: 'ok' }
    
    try:
        uploader_email = request.headers.get(ShieldPrsEmail)
        file_path = request.json.get("file_path")
        if file_path is None or not file_path.strip():
            raise ErrorExcept(ErrCode.Param, 'file_path must be not null')
        logger.info(f"operator: {uploader_email} start delete file {file_path}.")
        project_path = sys.path[0]
        abs_file_path = project_path + file_path
        if os.path.exists(abs_file_path):
            os.remove(abs_file_path)
        logger.info(f"operator: {uploader_email} finish delete file {file_path}.")
        ret["data"] = []
    except ErrorExcept as e:
        logger.exception(e)
        ret[Ret.Code] = e.code
        if len(e.err_msg) <= 500:
            ret[Ret.Msg] = e.err_msg
        else:
            ret[Ret.Msg] = e.err_msg[:500] + "..."
    except Exception as e:
        logger.exception(e)
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = FileUploadError.UnknownError
        ret["error"] = str(e)
    return ret


def get_efs_url(file_path: str):
    efs_host = "https://gcsales.expert.apple.com/"
    if env_dev():
        efs_host = "https://gcsales.expert-dev.apple.com/"
    return f"{efs_host}static/display/fast-lite/{file_path}"
