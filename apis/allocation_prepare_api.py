from .api_common import *
from service.allocation_prepare_service import *
from service.operation_workflow_service import insert_operation_workflow
from util.const_tracking import *
from util.util_operation import insert_operate_record


bp = Blueprint('allocation_prepare_api', __name__, url_prefix=UrlPrefix)


@bp.route('/allocation_prepare/list')
def get_allocation_prepare_list():
    ret = {
        Ret.Code: ErrCode.Success, 
        Ret.Msg: 'Success'
    }
    try:
        query_params = request.args
        
        rtm = query_params.get('rtm')
        
        try:
            fiscal_week_year = int(query_params.get('fiscal_week_year'))
            page_num = int(query_params.get('page_num', 1))  # offset
            page_size = int(query_params.get('page_size', 10))  # limit
        except ValueError:
            ret[Ret.Code] = ErrCode.Param
            ret[Ret.Msg] = 'fiscal_week_year, page_num and page_size must be integer'
            return ret

        ret[Ret.Data] = get_allocation_prepare_list_service(rtm, fiscal_week_year)
        
        person_id = request.headers.get(ShieldPrsId, '')
        insert_operation_workflow(CHANNEL_ALLOCATION, VIEW_OPERATION, person_id, TRACKING_RTMS_DICT.get(rtm), request.path)
    except ErrorExcept as e:
        logger.exception(e)
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        logger.exception(e)
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)

    return ret 

@bp.route('/allocation_prepare/week_list')
def get_allocation_prepare_week_list():
    ret = {
        Ret.Code: ErrCode.Success, 
        Ret.Msg: 'Success'
    }
    try:
        query_params = request.args
        
        rtm = query_params.get('rtm')
        
        if rtm is None:
            ret[Ret.Code] = ErrCode.Param
            ret[Ret.Msg] = "rtm is required."
            return ret

        ret[Ret.Data] = get_allocation_prepare_week_list_service(rtm)
        
    except ErrorExcept as e:
        logger.exception(e)
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        logger.exception(e)
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)

    return ret

@bp.route('/allocation_prepare/check')
def check_data_side_status():
    ret = {
        Ret.Code: ErrCode.Success, 
        Ret.Msg: 'Success'
    }
    try:
        query_params = request.args
        fiscal_week_year = int(query_params.get(StrFiscalWeekYear))
    except ValueError:
        ret[Ret.Code] = ErrCode.Param
        ret[Ret.Msg] = f'{StrFiscalWeekYear} must be integer'
        return ret
    try:
        ret[Ret.Data] = check_whether_ready_service(fiscal_week_year)
    except ErrorExcept as e:
        logger.exception(e)
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        logger.exception(e)
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)

    return ret   

@bp.route('/allocation_prepare/detail')
def get_allocation_prepare_detail():
    ret = {
        Ret.Code: ErrCode.Success, 
        Ret.Msg: 'Success'
    }
    query_params = request.args
    
    try:
        id = int(query_params.get('id'))
        enter_phase = int(query_params.get('phase', 0))
        fiscal_week_year = int(query_params.get('fiscal_week_year', 0))
    except (TypeError, ValueError):
        ret[Ret.Code] = ErrCode.Param
        ret[Ret.Msg] = 'id, enter_phase, fiscal_week_year must be integer'
        return ret
    
    try:
        ret[Ret.Data] = get_allocation_prepare_detail_service(id, enter_phase, fiscal_week_year)
    except ErrorExcept as e:
        logger.exception(e)
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        logger.exception(e)
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)

    return ret

@bp.route('/allocation_prepare/template_file')
def get_allocation_prepare_template_file():
    ret = {
        Ret.Code: ErrCode.Success, 
        Ret.Msg: 'Success'
    }
    try:
        query_params = request.args
        
        rtm = query_params.get('rtm')
        if rtm is None or rtm.strip() == '':
            ret[Ret.Code] = ErrCode.Param
            ret[Ret.Msg] = 'please input rtm.'
            return ret
        
        try:
            fiscal_week_year = int(query_params.get('fiscal_week_year'))
        except ValueError as e:
            logger.exception(e)
            ret[Ret.Code] = ErrCode.Param
            ret[Ret.Msg] = 'fiscal_week_year must be integer'
            return ret

        file_path, file_name = get_allocation_prepare_template_file_service(rtm, fiscal_week_year)
        response = make_response(send_file(file_path, as_attachment=True))
        response.headers["Content-Disposition"] = f'attachment; filename="{file_name}"'
        return response
    except ErrorExcept as e:
        logger.exception(e)
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        logger.exception(e)
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)

    return ret

@bp.route('/allocation_prepare/rtm/upload_file', methods=["POST"])
def rtm_upload_sales_input_file():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        id = int(request.args.get('id'))
    except ValueError:
        ret[Ret.Code] = ErrCode.Param
        ret[Ret.Msg] = 'id must be integer'
        return ret
    file = request.files.get("file")
    uploader = request.headers.get(ShieldPrsNickName) + " " + request.headers.get(ShieldPrsLastName)
    uploader_email = request.headers.get(ShieldPrsEmail)
    try:
        ret["data"] = upload_file_by_rtm_service(file, uploader, uploader_email, id)
    except ErrorExcept as e:
        logger.exception(e)
        ret[Ret.Code] = e.code
        if len(e.err_msg) <= 500:
            ret[Ret.Msg] = e.err_msg
        else:
            ret[Ret.Msg] = e.err_msg[:500] + "..."
    except Exception as e:
        logger.exception(e)
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = FileUploadError.UnknownError
        ret["error"] = str(e)
    return ret

@bp.route("/allocation_prepare/rtm/sales_input/download")
def rtm_download_sales_input_file():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    
    file_path = request.args.get('file_path')
    if file_path is None:
        ret[Ret.Code] = ErrCode.Param
        ret[Ret.Msg] = 'file_path is required.'
        return ret
    operator = request.headers.get(ShieldPrsNickName) + " " + request.headers.get(ShieldPrsLastName)
    insert_operate_record(None, AllocationOperateCategory.Download, None, file_path, operator, None)
    file_path = transfer_file_path(file_path)
    if file_path == "":
        ret[Ret.Code] = ErrCode.System
        ret[Ret.Msg] = 'path not exist.'
        return ret 
    try:
        response = make_response(send_file(file_path, as_attachment=True))
        response.headers["Content-Disposition"] = f"attachment; filename=temporary_name.xlsx"
    except FileNotFoundError as e:
        logger.exception(e)
        ret[Ret.Code] = ErrCode.System
        ret[Ret.Msg] = 'No such file or directory.'
        return ret
    return response
