from .api_common import *
from service.allocation_cpf_run_service import *
from service.allocation_prepare_service import transfer_file_path
from service.allocation_run_supply_service import *
from service.allocation_run_calculate_service import *
from service.allocation_cpf_service import cpf_latest_update

bp = Blueprint('allocation_run_api', __name__, url_prefix=UrlPrefix + '/allocation_run')


@bp.route('/detail', methods=['GET'])
def get_allocation_run_detail():
    """展示财年周，流程方式"""
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: "Success"
    }
    try:
        fiscal_qtr_week_name = request.args.get("fiscal_qtr_week_name", type=str)
        if fiscal_qtr_week_name is None:
            raise ErrorExcept(ErrCode.Param, "please input fiscal_qtr_week_name.")
        ret["data"] = get_allocation_run_detail_service(fiscal_qtr_week_name)
    except ErrorExcept as e:
        logger.exception(e)
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        logger.exception(e)
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
    return ret


@bp.route('/supply_acquisition/detail', methods=['GET'])
def get_supply_acquisition_detail():
    """展示supply data上传文件，以及计算过程"""
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: "Success"
    }
    try:
        fiscal_qtr_week_name = request.args.get("fiscal_qtr_week_name", type=str)
        if fiscal_qtr_week_name is None:
            raise ErrorExcept(ErrCode.Param, "please input fiscal_qtr_week_name.")
        ret["data"] = get_supply_acquisition_detail_service(fiscal_qtr_week_name)
    except ErrorExcept as e:
        logger.exception(e)
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        logger.exception(e)
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
    return ret


@bp.route('/template/list', methods=['GET'])
def get_supply_data_template_list():
    """获取SupplyData的模版列表"""
    ret = {
        Ret.Code: ErrCode.Success, 
        Ret.Msg: "Success"
    }
    try:
        fiscal_qtr_week_name = request.args.get("fiscal_qtr_week_name", type=str)
        if fiscal_qtr_week_name is None:
            raise ErrorExcept(ErrCode.Param, "please input fiscal_qtr_week_name.")
        ret["data"] = get_supply_data_template_list_service(fiscal_qtr_week_name)
    except ErrorExcept as e:
        logger.exception(e)
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        logger.exception(e)
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
    return ret


@bp.route('/template/supply_data', methods=['GET'])
def get_supply_data_template():
    """
    获取SupplyData的模版数据,
    0:SupplyData; 1:ExcessSupply; 2:StopSupply;
    """
    ret = {
        Ret.Code: ErrCode.Success, 
        Ret.Msg: 'Success'
    }
    try:
        supply_type = request.args.get("supply_type", type=int)
        fiscal_qtr_week_name = request.args.get("fiscal_qtr_week_name", type=str)
        if fiscal_qtr_week_name is None:
            raise ErrorExcept(ErrCode.Param, "please input fiscal_qtr_week_name.")
        if supply_type is None:
            raise ErrorExcept(ErrCode.Param, "please input supply_type.")
        if supply_type not in [AllocationRunSupplyDataType.SupplyData,
                                AllocationRunSupplyDataType.ExcessSupply,
                                AllocationRunSupplyDataType.StopSupply]:
            raise ErrorExcept(ErrCode.Param, "please input correct supply_type 0,1,2.")
        # 返回Excel文件路径和文件名称
        file_path, file_name = get_supply_data_template_service(supply_type, fiscal_qtr_week_name)
        response = make_response(send_file(file_path, as_attachment=True))
        response.headers["Content-Disposition"] = f'attachment; filename="{file_name}"'
        return response
    except ErrorExcept as e:
        logger.exception(e)
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        logger.exception(e)
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
    return ret


@bp.route("/upload/supply_data", methods=["POST"])
def upload_supply_data_file():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        supply_type = request.args.get("supply_type", type=int)
        fiscal_qtr_week_name = request.args.get("fiscal_qtr_week_name", type=str)
        if fiscal_qtr_week_name is None:
            raise ErrorExcept(ErrCode.Param, "please input fiscal_qtr_week_name.")
        if supply_type is None:
            raise ErrorExcept(ErrCode.Param, "please input supply_type.")
        if supply_type not in [AllocationRunSupplyDataType.SupplyData,
                               AllocationRunSupplyDataType.ExcessSupply,
                               AllocationRunSupplyDataType.StopSupply]:
            raise ErrorExcept(ErrCode.Param, "please input correct supply_type 0,1,2.")
        file = request.files.get("file")
        uploader_id = request.headers.get(ShieldPrsId)
        ret["data"] = upload_supply_data_file_service(file, supply_type, fiscal_qtr_week_name, uploader_id)
        operator = request.headers.get(ShieldPrsNickName) + " " + request.headers.get(ShieldPrsLastName)
        cpf_latest_update(operator, fiscal_qtr_week_name)
    except ErrorExcept as e:
        logger.exception(e)
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        logger.exception(e)
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
    return ret


@bp.route("/download/file")
def download_file():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: "Success"
    }
    
    file_path = request.args.get("file_path")
    if file_path is None:
        ret[Ret.Code] = ErrCode.Param
        ret[Ret.Msg] = 'file_path is required.'
        return ret
    file_path = transfer_file_path(file_path)
    if file_path == "":
        ret[Ret.Code] = ErrCode.System
        ret[Ret.Msg] = 'path not exist.'
        return ret 
    try:
        response = make_response(send_file(file_path, as_attachment=True))
        response.headers["Content-Disposition"] = f"attachment; filename=temporary_name.xlsx"
    except FileNotFoundError as e:
        logger.exception(e)
        ret[Ret.Code] = ErrCode.System
        ret[Ret.Msg] = 'No such file or directory.'
        return ret
    return response


@bp.route("/supply_acquisition/calculate", methods=["POST"])
def supply_acquisition_calculate():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        fiscal_qtr_week_name = request.args.get("fiscal_qtr_week_name", type=str)
        if fiscal_qtr_week_name is None:
            raise ErrorExcept(ErrCode.Param, "please input fiscal_qtr_week_name.")
        uploader_id = request.headers.get(ShieldPrsId)
        ret["data"] = supply_acquisition_calculate_service(fiscal_qtr_week_name, uploader_id)
        operator = request.headers.get(ShieldPrsNickName) + " " + request.headers.get(ShieldPrsLastName)
        cpf_latest_update(operator, fiscal_qtr_week_name)
    except ErrorExcept as e:
        logger.exception(e)
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        logger.exception(e)
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
    return ret


@bp.route("/supply_acquisition/calculate/status", methods=["GET"])
def get_supply_acquisition_calculate_status():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        calculate_id = request.args.get("calculate_id", type=str)
        if calculate_id is None:
            raise ErrorExcept(ErrCode.Param, "please input calculate_id.")
        ret["data"] = get_supply_acquisition_calculate_status_service(calculate_id)
    except ErrorExcept as e:
        logger.exception(e)
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        logger.exception(e)
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
    return ret


@bp.route("/delete/supply_data", methods=["POST"])
def delete_supply_data_file():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        supply_type = request.args.get("supply_type", type=int)
        fiscal_qtr_week_name = request.args.get("fiscal_qtr_week_name", type=str)
        if fiscal_qtr_week_name is None:
            raise ErrorExcept(ErrCode.Param, "please input fiscal_qtr_week_name.")
        if supply_type is None:
            raise ErrorExcept(ErrCode.Param, "please input supply_type.")
        if supply_type not in [AllocationRunSupplyDataType.SupplyData,
                               AllocationRunSupplyDataType.ExcessSupply,
                               AllocationRunSupplyDataType.StopSupply]:
            raise ErrorExcept(ErrCode.Param, "please input correct supply_type 0,1,2.")
        uploader_id = request.headers.get(ShieldPrsId)
        ret["data"] = delete_supply_data_file_service(supply_type, fiscal_qtr_week_name, uploader_id)
        operator = request.headers.get(ShieldPrsNickName) + " " + request.headers.get(ShieldPrsLastName)
        cpf_latest_update(operator, fiscal_qtr_week_name)
    except ErrorExcept as e:
        logger.exception(e)
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        logger.exception(e)
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
    return ret


@bp.route("/create/supply_data_template", methods=["POST"])
def create_supply_data_template_file():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        fiscal_dt = request.args.get("fiscal_dt", type=str)
        if fiscal_dt is None:
            raise ErrorExcept(ErrCode.Param, "please input fiscal_dt.")
        for i in range(3):
            generate_supply_data_template_file_service(i, fiscal_dt)
    except ErrorExcept as e:
        logger.exception(e)
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        logger.exception(e)
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
    return ret


@bp.route('/update/detail', methods=['POST'])
def update_allocation_run_detail():
    """更新allocation run"""
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: "Success"
    }
    try:
        fiscal_qtr_week_name = request.args.get("fiscal_qtr_week_name", type=str)
        if fiscal_qtr_week_name is None:
            raise ErrorExcept(ErrCode.Param, "please input fiscal_qtr_week_name.")
        update_data = {}
        process_type = request.args.get("process_type", type=int)
        if process_type is not None:
            if process_type not in [0, 1]:
                raise ErrorExcept(ErrCode.Param, 
                                  "please input correct process_type.")
            update_data["process_type"] = process_type
        
        latest_step = request.args.get("latest_step", type=int)
        if latest_step is not None:
            if latest_step not in [1,2,3,4,5]:
                raise ErrorExcept(ErrCode.Param,
                                  "please input correct latest_step.")
            update_data["latest_step"] = latest_step
        
        operator_id = request.headers.get(ShieldPrsId)
        
        ret["data"] = update_allocation_run_detail_service(fiscal_qtr_week_name,
                                                           update_data,
                                                           operator_id)
    except ErrorExcept as e:
        logger.exception(e)
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        logger.exception(e)
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
    return ret


@bp.post("/supply_protection/list")
def allocation_run_supply_protection_get():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        fiscal_week_year = request.json.get("fiscal_week_year")
        if fiscal_week_year is None:
            raise ErrorExcept(ErrCode.Param, "please input fiscal_week_year.")
        strategy = request.json.get("strategy")
        if strategy is None or type(strategy) != int:
            raise ErrorExcept(ErrCode.Param, "strategy must be integer.")
        project_code = request.json.get("project_code", None)
        if project_code is not None and type(project_code) != list:
            raise ErrorExcept(ErrCode.Param, "project_code must be array.")
        mpn = request.json.get("mpn", None)
        if mpn is not None and type(mpn) != list:
            raise ErrorExcept(ErrCode.Param, "mpn must be array.")
        ret[Ret.Data] = supply_protection_get_data_list(fiscal_week_year, strategy, project_code, mpn)
    except ErrorExcept as e:
        logger.exception(e)
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        logger.exception(e)
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
    return ret


@bp.post("/supply_protection/list/to_add")
def allocation_run_supply_protection_get_to_add():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        fiscal_week_year = request.json.get("fiscal_week_year")
        if fiscal_week_year is None:
            raise ErrorExcept(ErrCode.Param, "please input fiscal_week_year.")
        project_code = request.json.get("project_code", None)
        if project_code is not None and type(project_code) != list:
            raise ErrorExcept(ErrCode.Param, "project_code must be array.")
        mpn = request.json.get("mpn", None)
        if mpn is not None and type(mpn) != list:
            raise ErrorExcept(ErrCode.Param, "mpn must be array.")
        ret[Ret.Data] = supply_protection_to_add_data_list(fiscal_week_year, project_code, mpn)
    except ErrorExcept as e:
        logger.exception(e)
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        logger.exception(e)
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
    return ret


@bp.post("/supply_protection/add")
def allocation_run_supply_protection_add():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        fiscal_week_year = request.json.get("fiscal_week_year")
        add_ids = request.json.get("id_list")
        if fiscal_week_year is None:
            raise ErrorExcept(ErrCode.Param, "please input fiscal_week_year.")
        strategy = request.json.get("strategy")
        if strategy is None or type(strategy) != int:
            raise ErrorExcept(ErrCode.Param, "strategy must be integer.")
        ret[Ret.Data] = supply_protection_add_data_list(fiscal_week_year, strategy, add_ids)
    except ErrorExcept as e:
        logger.exception(e)
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        logger.exception(e)
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
    return ret


@bp.post("/supply_protection/delete")
def allocation_run_supply_protection_delete():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        fiscal_week_year = request.json.get("fiscal_week_year")
        delete_ids = request.json.get("id_list")
        if fiscal_week_year is None:
            raise ErrorExcept(ErrCode.Param, "please input fiscal_week_year.")
        strategy = request.json.get("strategy")
        if strategy is None or type(strategy) != int:
            raise ErrorExcept(ErrCode.Param, "strategy must be integer.")
        ret[Ret.Data] = supply_protection_delete_data_list(fiscal_week_year, strategy, delete_ids)
    except ErrorExcept as e:
        logger.exception(e)
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        logger.exception(e)
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
    return ret


@bp.route("/upload/special_supply", methods=["POST"])
def upload_special_supply_file():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        fiscal_qtr_week_name = request.args.get("fiscal_qtr_week_name", type=str)
        if fiscal_qtr_week_name is None:
            raise ErrorExcept(ErrCode.Param, "please input fiscal_qtr_week_name.")
        file = request.files.get("file")
        uploader_id = request.headers.get(ShieldPrsId)
        ret["data"] = upload_special_supply_file_service(file, fiscal_qtr_week_name, uploader_id)
        operator = request.headers.get(ShieldPrsNickName) + " " + request.headers.get(ShieldPrsLastName)
        cpf_latest_update(operator, fiscal_qtr_week_name)
    except ErrorExcept as e:
        logger.exception(e)
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        logger.exception(e)
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
    return ret


@bp.route("/special_supply/file", methods=["GET"])
def special_supply_file_list():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        fiscal_qtr_week_name = request.args.get("fiscal_qtr_week_name", type=str)
        if fiscal_qtr_week_name is None:
            raise ErrorExcept(ErrCode.Param, "please input fiscal_qtr_week_name.")
        ret["data"] = get_special_supply_uploaded_file(fiscal_qtr_week_name)
    except ErrorExcept as e:
        logger.exception(e)
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        logger.exception(e)
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
    return ret


@bp.route("/special_supply/file/delete", methods=["POST"])
def special_supply_file_delete():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        fiscal_qtr_week_name = request.args.get("fiscal_qtr_week_name", type=str)
        if fiscal_qtr_week_name is None:
            raise ErrorExcept(ErrCode.Param, "please input fiscal_qtr_week_name.")
        ret["data"] = delete_special_supply_uploaded_file(fiscal_qtr_week_name)
        operator = request.headers.get(ShieldPrsNickName) + " " + request.headers.get(ShieldPrsLastName)
        cpf_latest_update(operator, fiscal_qtr_week_name)
    except ErrorExcept as e:
        logger.exception(e)
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        logger.exception(e)
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
    return ret


@bp.route('/operate/result')
def get_supply_can_go_next():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        fiscal_week_year = request.args.get("fiscal_week_year", type=str)
        if fiscal_week_year is None:
            raise ErrorExcept(ErrCode.Param, "please input fiscal_week_year.")
        step = request.args.get("step")
        if step is None or not step.isdigit() or int(step) not in [2, 3]:
            raise ErrorExcept(ErrCode.Param, "please input valid step.")
        ret["data"] = get_run_supply_calculate_result(fiscal_week_year, int(step))
    except ErrorExcept as e:
        logger.exception(e)
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        logger.exception(e)
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
    return ret


@bp.route('/template/special_supply', methods=['GET'])
def get_special_supply_data_template():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        fiscal_week_year = request.args.get("fiscal_week_year", type=str)
        if fiscal_week_year is None:
            raise ErrorExcept(ErrCode.Param, "please input fiscal_qtr_week_name.")
        # 返回Excel文件路径和文件名称
        file_path, file_name = get_special_supply_template_service(fiscal_week_year)
        response = make_response(send_file(transfer_file_path(file_path), as_attachment=True))
        response.headers["Content-Disposition"] = f'attachment; filename="{file_name}"'
        return response
    except ErrorExcept as e:
        logger.exception(e)
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        logger.exception(e)
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
    return ret


@bp.post("/supply_protection/calculate")
def allocation_run_supply_protection_calculate():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        fiscal_qtr_week_name = request.args.get("fiscal_qtr_week_name", type=str)
        if fiscal_qtr_week_name is None:
            raise ErrorExcept(ErrCode.Param, "please input fiscal_qtr_week_name.")

        operator_id = request.headers.get(ShieldPrsId)
        ret[Ret.Data] = calculate_supply_protection_service(fiscal_qtr_week_name,
                                                             operator_id)
        operator = request.headers.get(ShieldPrsNickName) + " " + request.headers.get(ShieldPrsLastName)
        cpf_latest_update(operator, fiscal_qtr_week_name)
    except ErrorExcept as e:
        logger.exception(e)
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        logger.exception(e)
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
    return ret


@bp.post("/special_supply/calculate")
def allocation_run_spicial_supply_calculate():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        fiscal_qtr_week_name = request.args.get("fiscal_qtr_week_name", type=str)
        if fiscal_qtr_week_name is None:
            raise ErrorExcept(ErrCode.Param, "please input fiscal_qtr_week_name.")
        operator_id = request.headers.get(ShieldPrsId)
        ret[Ret.Data] = calculate_special_supply_service(fiscal_qtr_week_name,
                                                         operator_id)
        operator = request.headers.get(ShieldPrsNickName) + " " + request.headers.get(ShieldPrsLastName)
        cpf_latest_update(operator, fiscal_qtr_week_name)
    except ErrorExcept as e:
        logger.exception(e)
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        logger.exception(e)
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
    return ret


@bp.post("/operate/go_next")
def allocation_run_go_next():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        fiscal_qtr_week_name = request.args.get("fiscal_qtr_week_name", type=str)
        if fiscal_qtr_week_name is None:
            raise ErrorExcept(ErrCode.Param, "please input fiscal_qtr_week_name.")
        current_step = request.args.get("current_step", type=int)
        if current_step is None:
            raise ErrorExcept(ErrCode.Param, "please input current_step.")
        ret[Ret.Data] = allcation_run_go_next_service(fiscal_qtr_week_name,
                                                      current_step)
        operator = request.headers.get(ShieldPrsNickName) + " " + request.headers.get(ShieldPrsLastName)
        cpf_latest_update(operator, fiscal_qtr_week_name)
    except ErrorExcept as e:
        logger.exception(e)
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        logger.exception(e)
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
    return ret
