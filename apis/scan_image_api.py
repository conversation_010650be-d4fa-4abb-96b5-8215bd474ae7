import os
import math
import threading

from flask import Blueprint, request as http_request
from domain.baozun.scan_image_func import scan_image_data, get_ck_token
from domain.baozun.entity.hitl_image import HitlImg
from data.hitl_verification_result import ScanAssistInfoRepository
from util.conf import logger
from util.const import *
from util.util import env_dev
from util.cpf_util import save_file, create_path

bp = Blueprint('scan_image_api', __name__, url_prefix="/fast")

# def sync_image_local():
#     data = ScanAssistInfoRepository.get_bz_data_count("GBI")
#     count = math.ceil(data / 100)
#     for i in range(1, 100):
#         offset = i * 100
#         scan_image_data(100, offset, "GBI")
#
# sync_image_local()

@bp.route("/sync_scan_data", methods=["GET"])
def get_scan_data():
    ret = {Ret.Code:ErrCode.Success, Ret.Msg:ErrMsg[ErrCode.Success]}
    try:
        person_id = http_request.args.get("Shield-Ds-Prsid")
        if not person_id:
            ret[Ret.Code] = ErrCode.Param
            ret[Ret.Msg] = ErrMsg[ErrCode.Param]
            return ret
        logger.info({"sync scan data person id": person_id})
        query_str = http_request.args.get("query")
        query_str = query_str.lower()
        danger_keys = ["delete", "update", "insert", "drop", "alter"]
        if any([item in query_str for item in danger_keys]):
            ret[Ret.Code] = ErrCode.Permissions
            ret[Ret.Msg] = ErrMsg[ErrCode.Permissions]
            return ret
        limit = http_request.args.get("limit", 100)
        offset = http_request.args.get("offset", 0)
        source = http_request.args.get("source")

        ret[Ret.Data] = scan_image_data(limit=limit, offset=offset, source=source)
    except Exception as e:
        ret[Ret.Code] = ErrCode.System
        ret[Ret.Msg] = str(e)
    return ret


@bp.route("/list_file", methods=["GET"])
def list_file():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: "success"}
    try:
        path = http_request.args.get("path")
        ret["data"] = os.listdir(path)
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        ret[Ret.Code] = ErrCode.System
        ret[Ret.Msg] = str(e)
    return ret

@bp.route("/check_url_test", methods=["GET"])
def check_url_test():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: "success"}
    try:
        url = http_request.json.get("url")
        if not url:
            ret[Ret.Code] = ErrCode.Param
            ret[Ret.Msg] = ErrMsg[ErrCode.Param]
            return ret
        ret["data"] = HitlImg.request_data_by_url(url)
    except Exception as e:
        ret[Ret.Code] = ErrCode.System
        ret[Ret.Msg] = str(e)
    return ret


@bp.route("/write_ck_test", methods=["POST"])
def write_ck_test():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: "success"}
    try:
        ck = http_request.json.get("ck")
        ck_path = os.path.join(create_path(""), "ck.txt")
        logger.info({"ck path": ck_path})
        with open(ck_path, "w") as f:
            f.write(ck)
        ret["data"] = get_ck_token()
    except Exception as e:
        ret[Ret.Code] = ErrCode.System
        ret[Ret.Msg] = str(e)
    return ret



