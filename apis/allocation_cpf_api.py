from .api_common import *
from service.allocation_cpf_service import *
from service.operation_workflow_service import insert_operation_workflow
from util.const_tracking import *


bp = Blueprint('allocation_cpf_api', __name__, url_prefix=UrlPrefix + '/allocation_cpf')


@bp.route('/list', methods=['GET'])
def get_allocation_cpf_list():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'}
    try:
        query_params = request.args
        fiscal_qtr_week_name = query_params.get('fiscal_qtr_week_name')

        ret[Ret.Data] = get_allocation_cpf_lob_list_service(fiscal_qtr_week_name)

        person_id = request.headers.get(ShieldPrsId, '')
        insert_operation_workflow(CHANNEL_ALLOCATION, VIEW_OPERATION, person_id, TrackingRtms.CP_F, request.path)
    except ErrorExcept as e:
        logger.exception(e)
        ret[Ret.Code] = e.code
        if len(e.err_msg) <= 500:
            ret[Ret.Msg] = e.err_msg
        else:
            ret[Ret.Msg] = e.err_msg[:500] + "..."
    except Exception as e:
        logger.exception(e)
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
    return ret


@bp.route('/detail/list', methods=['GET'])
def get_allocation_cpf_detail_list():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'}
    try:
        query_params = request.args
        cpf_lob_id = int(query_params.get('id'))

        ret[Ret.Data] = get_allocation_prepare_cpf_list_service(cpf_lob_id)

    except ErrorExcept as e:
        logger.exception(e)
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        logger.exception(e)
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
    return ret


@bp.route('/download_rtm_file/update/status', methods=['GET'])
def download_rtm_file_update_status():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'}
    try:
        query_params = request.args
        prepare_id = query_params.get('id')
        upload_status = query_params.get('status')
        phase = query_params.get('phase', str(RTMAllocationPhase.SalesInput))
        download_rtm_file_update_status_service(prepare_id, phase, upload_status)
    except ErrorExcept as e:
        logger.exception(e)
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        logger.exception(e)
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
    return ret


@bp.route('/upload_file', methods=['POST'])
def upload_file():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        prepare_id = int(request.args.get('id'))
    except ValueError as e:
        logger.exception(e)
        ret[Ret.Code] = ErrCode.Param
        ret[Ret.Msg] = 'id must be integer'
        return ret
    try:
        file = request.files.get("file")
        uploader = request.headers.get(ShieldPrsNickName) + " " + request.headers.get(ShieldPrsLastName)
        cpf_upload_file_service(prepare_id, file, uploader)
    except ErrorExcept as e:
        logger.exception(e)
        ret[Ret.Code] = e.code
        if len(e.err_msg) <= 500:
            ret[Ret.Msg] = e.err_msg
        else:
            ret[Ret.Msg] = e.err_msg[:500] + "..."
    except Exception as e:
        logger.exception(e)
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
    return ret


@bp.route('/download_merge_file')
def download_merge_file():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        cpf_lob_id = int(request.args.get('id'))
    except ValueError as e:
        logger.exception(e)
        ret[Ret.Code] = ErrCode.Param
        ret[Ret.Msg] = 'id must be integer'
        return ret
    try:
        file_path = download_merge_file_service(cpf_lob_id)
        response = make_response(send_file(file_path, as_attachment=True))
        response.headers["Content-Disposition"] = f"attachment; filename=temporary_name.xlsx"
    except FileNotFoundError as e:
        logger.exception(e)
        ret[Ret.Code] = ErrCode.System
        ret[Ret.Msg] = 'No such file or directory.'
        return ret
    return response


@bp.route('/prepare/re/run', methods=['GET'])
def prepare_re_run():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'}
    try:
        query_params = request.args
        prepare_id = query_params.get('id')
        upload_status = query_params.get('status')
        update_by = request.headers.get(ShieldPrsNickName) + " " + request.headers.get(ShieldPrsLastName)
        update_prepare_status_re_run_service(prepare_id, upload_status, update_by)
    except ErrorExcept as e:
        logger.exception(e)
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        logger.exception(e)
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
    return ret


@bp.route('/publish', methods=['POST'])
def publish():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'}
    try:
        current_user = request.headers.get(ShieldPrsNickName) + " " + request.headers.get(ShieldPrsLastName)
        query_params = request.json
        cpf_lob_id = int(query_params.get('id'))
        description = query_params.get('description')
        publish_service(cpf_lob_id, description, current_user)
    except ErrorExcept as e:
        logger.exception(e)
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        logger.exception(e)
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
    return ret


@bp.route('/init', methods=['GET'])
def init():
    add_allocation_cpf_lob_service()
    return {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'}


@bp.route('/fiscal_qtr_week_name_list', methods=['GET'])
def fiscal_qtr_week_name_list():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'}
    try:
        query_params = request.args
        lob = query_params.get('lob', 'iPad')
        ret[Ret.Data] = fiscal_qtr_week_name_list_service(lob)
    except ErrorExcept as e:
        logger.exception(e)
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        logger.exception(e)
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
    return ret


@bp.route('/publish_status', methods=['GET'])
def publish_status():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'}
    try:
        query_params = request.args
        cpf_lob_id = query_params.get('id')
        ret[Ret.Data] = publish_status_service(cpf_lob_id)
    except ErrorExcept as e:
        logger.exception(e)
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        logger.exception(e)
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
    return ret


@bp.route("/sales_input/approve", methods=["POST"])
def allocation_collection_sales_input_approve():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        body_params = request.json
        rtm = body_params.get('rtm')
        fiscal_week_year = body_params.get('fiscal_week_year')
        if not rtm or not fiscal_week_year:
            raise ErrorExcept(ErrCode.Param, "please check params.")
        uploader = request.headers.get(ShieldPrsNickName) + " " + request.headers.get(ShieldPrsLastName)
        uploader_email = request.headers.get(ShieldPrsEmail)
        ret["data"] = sales_input_approve_service(rtm, fiscal_week_year, uploader, uploader_email)
    except ErrorExcept as e:
        logger.exception(e)
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        logger.exception(e)
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
    return ret


@bp.route("/sales_input/error_confirm", methods=["POST"])
def allocation_collection_sales_input_error_confirm():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        body_params = request.json
        rtm = body_params.get('rtm')
        fiscal_week_year = body_params.get('fiscal_week_year')
        if not rtm or not fiscal_week_year:
            raise ErrorExcept(ErrCode.Param, "please check params.")
        uploader = request.headers.get(ShieldPrsNickName) + " " + request.headers.get(ShieldPrsLastName)
        uploader_email = request.headers.get(ShieldPrsEmail)
        ret["data"] = sales_input_error_confirm_service(rtm, fiscal_week_year, uploader, uploader_email)
    except ErrorExcept as e:
        logger.exception(e)
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        logger.exception(e)
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
    return ret
