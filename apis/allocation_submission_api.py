from .api_common import *
from service.allocation_submission_service import *


bp = Blueprint('allocation_submission_api', __name__, url_prefix=UrlPrefix)


@bp.route("/allocation_submission/upload", methods=["POST"])
def upload_submission_file():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        query_params = request.args
        rtm = query_params.get('rtm')
        fiscal_week_year = query_params.get('fiscal_week_year')
        is_cpf_upload = bool(query_params.get('is_cpf_upload', 0, type=int))
        if rtm != AllocationRTM.Multi and rtm != AllocationRTM.Online:
            raise ErrorExcept(ErrCode.Param, "can only upload Multi or Online submission file")
        file = request.files.get("file")
        uploader = request.headers.get(ShieldPrsNickName) + " " + request.headers.get(ShieldPrsLastName)
        uploader_email = request.headers.get(ShieldPrsEmail)
        ret["data"] = upload_submission_file_by_rtm(rtm, file, fiscal_week_year, uploader, uploader_email, is_cpf_upload)
    except ErrorExcept as e:
        logger.exception(e)
        ret[Ret.Code] = e.code
        if len(e.err_msg) <= 500:
            ret[Ret.Msg] = e.err_msg
        else:
            ret[Ret.Msg] = e.err_msg[:500] + "..."
    except Exception as e:
        logger.exception(e)
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
    return ret
