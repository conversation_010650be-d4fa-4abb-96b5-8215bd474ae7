from data.model_sku_data import DimFastModelSkuList
from service.model_sku_service import get_model_list
from .api_common import *
from service.demand_service import *
from service.operation_workflow_service import insert_operation_workflow
from util.const_tracking import *
from util.const import UrlPrefix


bp = Blueprint('demand_api', __name__, url_prefix=UrlPrefix)


@bp.route('/demand_list', methods=['GET'])
def get_demand_list():
    ret = {
        Ret.Code: ErrCode.Success, 
        Ret.Msg: 'Success'
    }
    try:
        url_params = request.args
        
        page_num = int(url_params.get('page_num', 1))  # offset
        page_size = int(url_params.get('page_size', 10))  # limit

        if page_num > 1:
            page_num = (page_num - 1) * page_size
        else:
            page_num = 0
      
        ret[Ret.Data] = get_demand_list_service(page_num, page_size)
        
        person_id = request.headers.get(ShieldPrsId, '')
        insert_operation_workflow(FAST_LITE, VIEW_OPERATION, person_id, TrackingRtms.CP_F, request.path)
    except ErrorExcept as e: 
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e: 
        ret[Ret.Code] = ErrCode.Success
        ret[Ret.Msg] = str(e)

    return ret 


@bp.route('/rtm_options', methods=['GET'])
def get_rtm_options():
    ret = {
        Ret.Code: ErrCode.Success, 
        Ret.Msg: 'Success'
    }
    try:
        
        ret[Ret.Data] = get_rtm_options_service()
        
    except ErrorExcept as e: 
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e: 
        ret[Ret.Code] = ErrCode.Success
        ret[Ret.Msg] = str(e)

    return ret 


@bp.route('/business_type_options', methods=['POST'])
def get_business_type_options():
    ret = {
        Ret.Code: ErrCode.Success, 
        Ret.Msg: 'Success'
    }
    try:
        req = request.json
        rtm = req.get('rtm')
        if rtm is None:
            rtm = []
        fiscal_week_year = req.get('fiscal_week_year')
        
        if fiscal_week_year is None:
            ret[Ret.Code] = ErrCode.Param
            ret[Ret.Msg] = f'{ErrMsg[ErrCode.Param]}please input param [fiscal_week_year].'
            return ret
        
        ret[Ret.Data] = get_business_type_options_service(fiscal_week_year, rtm)
        
    except ErrorExcept as e: 
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e: 
        ret[Ret.Code] = ErrCode.Success
        ret[Ret.Msg] = str(e)

    return ret 

@bp.route('/sold_to_options', methods=['POST'])
def get_sold_to_options():
    ret = {
        Ret.Code: ErrCode.Success, 
        Ret.Msg: 'Success'
    }
    try:
        req = request.json
        rtm = req.get('rtm')
        business_type = req.get('business_type')
        fuzzy_filter = req.get('fuzzy_filter')
        fiscal_week_year = req.get('fiscal_week_year')
        
        if fiscal_week_year is None:
            ret[Ret.Code] = ErrCode.Param
            ret[Ret.Msg] = f'{ErrMsg[ErrCode.Param]}please input param [fiscal_week_year].'
            return ret
        
        if rtm is None:
            rtm = []
        if business_type is None:
            business_type = []
        
        ret[Ret.Data] = get_sold_to_options_service(fiscal_week_year, rtm, business_type, fuzzy_filter)
        
    except ErrorExcept as e: 
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e: 
        ret[Ret.Code] = ErrCode.Success
        ret[Ret.Msg] = str(e)

    return ret 


@bp.route('/demand_detail', methods=['POST'])
def get_demand_detail():
    ret = {
        Ret.Code: ErrCode.Success, 
        Ret.Msg: 'Success'
    }
    try:
        req = request.json
        fiscal_qtr_week_name = req.get('fiscal_qtr_week_name')
        lob = req.get('lob')
        model = req.get('model')
        sku = req.get('sku')
        rtm = req.get('rtm')
        business_type = req.get('business_type')
        sold_to = req.get('sold_to')
        multi_version = req.get('multi', False)
        
        if fiscal_qtr_week_name is None or fiscal_qtr_week_name.strip() == '':
            ret = {Ret.Code: ErrCode.Param}
            ret[Ret.Msg] = 'required param.'
            return ret
        
        if lob is None or lob.strip() == '':
            lob = 'iPhone'
        
        if model is None:
            # ALL
            model = []
        
        if sku is None:
            # ALL
            sku = []
            
        if rtm is None:
            rtm = []
        
        if business_type is None:
            business_type = []
            
        if sold_to is None:
            sold_to = []

        page_num = req.get('page_num')  # offset
        page_size = req.get('page_size') # limit
        if page_num is None:
            page_num = 1
        if page_size is None:
            page_size = 5
            
        if page_num > 1:
            page_num = (page_num - 1) * page_size
        else:
            page_num = 0

        fiscal_week_year = FiscalYearWeek.get_fiscal_week_year_by_week_date(fiscal_qtr_week_name)
        # model 永不为空
        if model is None or len(model) == 0:
            model = get_model_list(DEMAND, ["iphone"], StrRTMCPF, str(fiscal_week_year))
        orders = req.get('orders')
        if orders is None:
            orders = []
        
        if multi_version:
            ret[Ret.Data] = get_multi_version_demand_detail_service(fiscal_qtr_week_name, lob, model, sku, rtm, business_type, sold_to, orders, page_num, page_size)
        else:
            ret[Ret.Data] = get_demand_detail_service(fiscal_qtr_week_name, lob, model, sku, rtm, business_type, sold_to, orders, page_num, page_size)
        
    except ErrorExcept as e: 
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e: 
        ret[Ret.Code] = ErrCode.Success
        ret[Ret.Msg] = str(e)

    return ret


@bp.route("/demand_download", methods=['POST'])
def demand_download():
    ret = {
        Ret.Code: ErrCode.System
    }
    req = request.json

    # 只下载当前周(FY22Q1W13)的数据, 与筛选器无关, 也就是每次进入到详情页中下载的数据是一致的
    # 只下载rtm的数据
    fiscal_qtr_week_name = req.get('fiscal_qtr_week_name')
    
    download_demand_version = int(req.get('download_version', 0))

    if fiscal_qtr_week_name is None:
        ret[Ret.Msg] = 'please input fiscal_qtr_week_name. eg. FY22Q1W13'
        return ret

    try:

        path = get_fast_demand_download_path(fiscal_qtr_week_name, download_demand_version)
        paths = os.path.split(path)
        file_name = paths[-1]

        response = make_response(send_file(path, as_attachment=True))
        response.headers["Content-Disposition"] = f"attachment; filename={file_name.encode().decode('latin-1')}"
        return response
    except Exception as e:
        logger.exception(e)
        ret[Ret.Msg] = str(e)
        return ret

