from .api_common import *
from service.datasource_service import *
from apis.datasource.supply import fetch_supply_worker
bp = Blueprint('datasource_api', __name__, url_prefix=UrlPrefix)


@bp.route("/file/upload/multi/demand", methods=['POST'])
@shield_person(request)
def file_upload_multi_demand():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    file = request.files.get("file")
    uploader = request.headers.get(ShieldPrsNickName) + " " + request.headers.get(ShieldPrsLastName)
    try:
        ret["data"] = file_save_multi_demand(file, uploader)
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        if len(e.err_msg) <= 500:
            ret[Ret.Msg] = e.err_msg
        else:
            ret[Ret.Msg] = e.err_msg[:500] + "..."
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = FileUploadError.UnknownError
        ret["error"] = str(e)
    return ret


@bp.route("/file/upload/multi/twoi", methods=['POST'])
@shield_person(request)
def file_upload_multi_twoi():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    file = request.files.get("file")
    uploader = request.headers.get(ShieldPrsNickName) + " " + request.headers.get(ShieldPrsLastName)
    try:
        ret["data"] = file_save_multi_twoi(file, uploader)
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        if len(e.err_msg) <= 500:
            ret[Ret.Msg] = e.err_msg
        else:
            ret[Ret.Msg] = e.err_msg[:500] + "..."
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = FileUploadError.UnknownError
        ret["error"] = str(e)
    return ret


@bp.route("/file/upload/carrier/twoi", methods=['POST'])
@shield_person(request)
def file_upload_carrier_twoi():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    file = request.files.get("file")
    uploader = request.headers.get(ShieldPrsNickName) + " " + request.headers.get(ShieldPrsLastName)
    try:
        ret["data"] = file_save_carrier_twoi(file, uploader)
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        if len(e.err_msg) <= 500:
            ret[Ret.Msg] = e.err_msg
        else:
            ret[Ret.Msg] = e.err_msg[:500] + "..."
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = FileUploadError.UnknownError
        ret["error"] = str(e)
    return ret


@bp.route("/datasource/multi/list")
@shield_person(request)
def get_multi_datasource_list():
    res = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        res["data"] = {"list": get_datasource_list(StrRTMMulti)}
    except Exception as e:
        res[Ret.Code] = ErrCode.System
        res[Ret.Msg] = str(e)

    return res


@bp.route("/datasource/multi/demand/list")
@shield_person(request)
def get_multi_demand_datasource_list():
    res = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        res["data"] = {"list": get_datasource_file_list(StrRTMMulti, DataSourceFileType.Demand)}
    except Exception as e:
        res[Ret.Code] = ErrCode.System
        res[Ret.Msg] = str(e)

    return res


@bp.route("/datasource/multi/twoi/list")
@shield_person(request)
def get_multi_twoi_datasource_list():
    res = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        res["data"] = {"list": get_datasource_file_list(StrRTMMulti, DataSourceFileType.TargetTWOI)}
    except Exception as e:
        res[Ret.Code] = ErrCode.System
        res[Ret.Msg] = str(e)

    return res


@bp.route("/datasource/carrier/list")
@shield_person(request)
def get_carrier_datasource_list():
    res = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        res["data"] = {"list": get_datasource_list(StrRTMCarrier)}
    except Exception as e:
        res[Ret.Code] = ErrCode.System
        res[Ret.Msg] = str(e)

    return res


@bp.route("/datasource/carrier/twoi/list")
@shield_person(request)
def get_carrier_twoi_datasource_list():
    res = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        res["data"] = {"list": get_datasource_file_list(StrRTMCarrier, DataSourceFileType.CarrierTargetTWOI)}
    except Exception as e:
        res[Ret.Code] = ErrCode.System
        res[Ret.Msg] = str(e)

    return res


@bp.route("/file/download/<file_id>")
@shield_person(request)
def file_download(file_id):
    path = file_get(file_id)
    response = make_response(send_file(path, as_attachment=True))
    response.headers["Content-Disposition"] = f"attachment; filename={file_id}.xlsx"
    return response


@bp.route("/file/upload/cpf/ipad", methods=['POST'])
@shield_person(request)
def file_upload_cpf_sku():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    file = request.files.get("file")
    uploader = request.headers.get(ShieldPrsNickName) + " " + request.headers.get(ShieldPrsLastName)
    try:
        ret["data"] = file_save_cpf_sku(file, uploader)
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        if len(e.err_msg) <= 500:
            ret[Ret.Msg] = e.err_msg
        else:
            ret[Ret.Msg] = e.err_msg[:500] + "..."
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = FileUploadError.UnknownError
        ret["error"] = str(e)
    return ret


@bp.route("/file/upload/cpf/<sku_lob>", methods=['POST'])
@shield_person(request)
def file_upload_cpf_sku_mac(sku_lob):
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    file = request.files.get("file")
    uploader = request.headers.get(ShieldPrsNickName) + " " + request.headers.get(ShieldPrsLastName)
    try:

        ret["data"] = file_save_cpf_sku_lob(file, uploader, sku_lob)
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        if len(e.err_msg) <= 500:
            ret[Ret.Msg] = e.err_msg
        else:
            ret[Ret.Msg] = e.err_msg[:500] + "..."
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = FileUploadError.UnknownError
        ret["error"] = str(e)
    return ret


# TODO 当前排序是按照datasource_type字典序，后期可能需要优化
@bp.route("/datasource/cpf/list")
@shield_person(request)
def get_cpf_datasource_list():
    res = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        res["data"] = {"list": get_datasource_list(StrRTMCPF)}
    except Exception as e:
        res[Ret.Code] = ErrCode.System
        res[Ret.Msg] = str(e)

    return res


@bp.route("/datasource/cpf/ipad/list")
@shield_person(request)
def get_cpf_ipad_datasource_list():
    res = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        res["data"] = {"list": get_datasource_file_list(StrRTMCPF, DataSourceFileType.CPFSKUiPad)}
    except Exception as e:
        res[Ret.Code] = ErrCode.System
        res[Ret.Msg] = str(e)

    return res


@bp.route("/datasource/cpf/<sku_lob>/list")
@shield_person(request)
def get_cpf_sku_lob_datasource_list(sku_lob) -> object:
    res = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        res["data"] = {"list": get_datasource_file_list_by_sku_lob(StrRTMCPF, sku_lob)}
    except Exception as e:
        res[Ret.Code] = ErrCode.System
        res[Ret.Msg] = str(e)

    return res



# TODO 接口名待优化，不止esr，还有其他的Supply数据
@bp.route("/datasource/cpf/automatic/esr/list")
@shield_person(request)
def get_cpf_automatic_esr_list():
    res = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        res["data"] = {
            "list": get_datasource_automatic_esr_list(),
            "supply_list": get_datasource_automatic_list()
        }
    except Exception as e:
        res[Ret.Code] = ErrCode.System
        res[Ret.Msg] = str(e)

    return res


# 获取最新的supply data，触发发送邮件给自己
@bp.route("/datasource/cpf/automatic/fetch_supply")
@shield_person(request)
def fetch_cpf_automatic_supply():
    res = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    lob = request.args.get("lob")
    debug = request.args.get("debug", '0')
    if lob is None or lob == "":
        res[Ret.Code] = ErrCode.Param
        res[Ret.Msg] = "no lob provided"
        return res
    try:
        recipient = request.headers.get(ShieldPrsEmail)
        uploader = request.headers.get(ShieldPrsNickName) + " " + request.headers.get(ShieldPrsLastName)
        #recipient = "<EMAIL>"
        result = fetch_supply_worker.delay(0, lob, recipient, uploader, debug == '1')
        logger.info(f"fetch_supply_worker delay success: {result}")
        res[Ret.Msg] = "The latest supply data has been sent to your mailbox, please check it!"
    except Exception as e:
        res[Ret.Code] = ErrCode.System
        res[Ret.Msg] = str(e)
        logger.exception(e)
    return res


# 查看record接口
@bp.route("/datasource/cpf/automatic/supply_data/record/list")
@shield_person(request)
def cpf_automatic_record():
    res = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    lob = request.args.get("lob")
    if lob is None or lob == "":
        res[Ret.Code] = ErrCode.Param
        res[Ret.Msg] = "no lob provided"
        return res
    try:
        res["data"] = {"list": get_record_list_by_cmd(lob)}
    except Exception as e:
        res[Ret.Code] = ErrCode.System
        res[Ret.Msg] = str(e)

    return res


# record 发送邮件接口
@bp.route("/datasource/cpf/automatic/record/send_email")
@shield_person(request)
def cpf_automatic_record_send_email():
    res = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    datasource_id = request.args.get("record_id")
    recipient = request.headers.get(ShieldPrsEmail)
    try:
        res["data"] = {"list": email_click_send(datasource_id, recipient)}
    except Exception as e:
        res[Ret.Code] = ErrCode.System
        res[Ret.Msg] = str(e)

    return res


@bp.route("/datasource/cpf/automatic/esr/record/list")
@shield_person(request)
def get_cpf_automatic_esr_record_list():
    res = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    # try:
    #     fiscal_week_year = int(request.args.get(StrFiscalWeekYear))
    # except ValueError:
    #     res[Ret.Code] = ErrCode.Param
    #     res[Ret.Msg] = f"{StrFiscalWeekYear} must be integer"
    #     return res

    try:
        res["data"] = {"list": get_esr_record_list()}
    except Exception as e:
        res[Ret.Code] = ErrCode.System
        res[Ret.Msg] = str(e)

    return res


@bp.route("/datasource/cpf/automatic/esr/record/download")
@shield_person(request)
def get_cpf_automatic_esr_record_download():
    res = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        fiscal_week_year = int(request.args.get(StrFiscalWeekYear))
        version = int(request.args.get("version"))
    except ValueError:
        res[Ret.Code] = ErrCode.Param
        res[Ret.Msg] = f"{StrFiscalWeekYear}, version must be integer"
        return res

    try:
        pass
        esr_content = download_esr_record_by_version(fiscal_week_year, version)
        file_name = f"ESR_{fiscal_week_year}_{version}.csv"
        response = make_response(esr_content.to_csv())
        response.headers["Content-Disposition"] = f"attachment; filename={file_name.encode().decode('latin-1')}"
        response.headers["Content-Type"] = "text/csv"
        return response
    except Exception as e:
        res[Ret.Code] = ErrCode.System
        res[Ret.Msg] = str(e)

    return res


@bp.route("/datasource/cpf/automatic/esr/record/send_email")
@shield_person(request)
def automatic_esr_record_send_email():
    res = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        fiscal_week_year = int(request.args.get(StrFiscalWeekYear))
        fiscal_qtr_week_name = request.args.get(StrFiscalQtrWeekName)
        if fiscal_qtr_week_name is None:
            res[Ret.Msg] = f"{StrFiscalQtrWeekName}, version must not be None"
        receiver = request.headers.get(ShieldPrsEmail)
        record_time = request.args.get("last_upload_date")
        version = int(request.args.get("version"))
    except ValueError:
        res[Ret.Code] = ErrCode.Param
        res[Ret.Msg] = f"{StrFiscalWeekYear}, version must be integer"
        return res

    try:
        download_esr_record_and_send_email.delay(fiscal_week_year, version, fiscal_qtr_week_name, receiver, record_time)
    except Exception as e:
        res[Ret.Code] = ErrCode.System
        res[Ret.Msg] = str(e)
    return res


@bp.route("/file/upload/cpf/iphone", methods=['POST'])
@shield_person(request)
def file_upload_cpf_iphone_twos():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    file = request.files.get("file")
    uploader = request.headers.get(ShieldPrsNickName) + " " + request.headers.get(ShieldPrsLastName)
    try:
        ret["data"] = file_save_cpf_twos_iphone(file, uploader)
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        if len(e.err_msg) <= 500:
            ret[Ret.Msg] = e.err_msg
        else:
            ret[Ret.Msg] = e.err_msg[:500] + "..."
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = FileUploadError.UnknownError
        ret["error"] = str(e)
    return ret


@bp.route("/file/upload/cpf/sold_to_mapping_list_iphone", methods=['POST'])
@shield_person(request)
def file_upload_cpf_sold_to_mapping_list_iphone():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    file = request.files.get("file")
    uploader = request.headers.get(ShieldPrsNickName) + " " + request.headers.get(ShieldPrsLastName)
    try:
        ret["data"] = file_save_sold_to_mapping_iphone(file, uploader)
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        if len(e.err_msg) <= 500:
            ret[Ret.Msg] = e.err_msg
        else:
            ret[Ret.Msg] = e.err_msg[:500] + "..."
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = FileUploadError.UnknownError
        ret["error"] = str(e)
    return ret


@bp.route("/datasource/cpf/iphone/list")
@shield_person(request)
def get_cpf_iphone_datasource_list():
    res = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        res["data"] = {"list": get_datasource_file_list(StrRTMCPF, DataSourceFileType.CPFTWOSiPhone)}
    except Exception as e:
        res[Ret.Code] = ErrCode.System
        res[Ret.Msg] = str(e)

    return res


@bp.route("/datasource/cpf/sold_to_mapping_list_iphone/list")
@shield_person(request)
def get_cpf_sold_to_mapping_list_iphone_datasource_list():
    res = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        res["data"] = {"list": get_datasource_file_list(StrRTMCPF, DataSourceFileType.CPFSoldToMappingListIphone)}
    except Exception as e:
        res[Ret.Code] = ErrCode.System
        res[Ret.Msg] = str(e)

    return res


@bp.post("/file/upload/cpf/ipad/sold_to_mix")
def file_upload_datasource_ipad_mix():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    file = request.files.get("file")
    uploader = request.headers.get(ShieldPrsNickName) + " " + request.headers.get(ShieldPrsLastName)
    try:
        ret["data"] = file_save_datasource_sold_to_mix_ipad(file, uploader)
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        if len(e.err_msg) <= 500:
            ret[Ret.Msg] = e.err_msg
        else:
            ret[Ret.Msg] = e.err_msg[:500] + "..."
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = FileUploadError.UnknownError
        ret["error"] = str(e)
    return ret


@bp.post("/file/upload/cpf/ipad/twos")
def file_upload_datasource_ipad_twos():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    file = request.files.get("file")
    uploader = request.headers.get(ShieldPrsNickName) + " " + request.headers.get(ShieldPrsLastName)
    try:
        ret["data"] = file_save_datasource_twos_ipad(file, uploader)
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        if len(e.err_msg) <= 500:
            ret[Ret.Msg] = e.err_msg
        else:
            ret[Ret.Msg] = e.err_msg[:500] + "..."
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = FileUploadError.UnknownError
        ret["error"] = str(e)
    return ret


@bp.route("/datasource/cpf/sold_to_mix_ipad/list")
@shield_person(request)
def get_cpf_sold_to_mix_ipad_datasource_list():
    res = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        res["data"] = {"list": get_datasource_file_list(StrRTMCPF, DataSourceFileType.AllocationRunSupplyMix)}
    except Exception as e:
        res[Ret.Code] = ErrCode.System
        res[Ret.Msg] = str(e)

    return res


@bp.route("/datasource/cpf/twos_ipad/list")
@shield_person(request)
def get_cpf_twos_ipad_datasource_list():
    res = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        res["data"] = {"list": get_datasource_file_list(StrRTMCPF, DataSourceFileType.AllocationRunSupplyTWOS)}
    except Exception as e:
        res[Ret.Code] = ErrCode.System
        res[Ret.Msg] = str(e)

    return res


@bp.route("/datasource/cpf/supply_data_record")
@shield_person(request)
def cpf_supply_data_record():
    res = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        query_params = request.args
        key = query_params.get('key')
        # res["data"] = {"list": supply_data_record(send_email, ListOrRecord.Record, key)}
    except Exception as e:
        res[Ret.Code] = ErrCode.System
        res[Ret.Msg] = str(e)

    return res
