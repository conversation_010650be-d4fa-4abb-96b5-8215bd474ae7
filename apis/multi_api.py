from .api_common import *
from service.multi_service import *
from service.multi_external_service import *
from service.model_sku_service import *
from service.operation_workflow_service import insert_operation_workflow
from util.const_tracking import *
from util.const import UrlPrefix


bp = Blueprint('multi_api', __name__, url_prefix=UrlPrefix)


@bp.route("/multi/demand/list")
@shield_person(request)
def get_multi_demand_fiscal_qtr_list():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    page_size = request.args.get("page_size", "11")
    page = request.args.get("page", "1")
    external = request.args.get("external", "0")
    try:
        person_id = request.headers.get(ShieldPrsId, '')
        if g.get(UserTypeKey) == UserType.Expert and external != "1":
            ret["data"] = get_multi_fiscal_qtr_week_year(page, page_size)
            insert_operation_workflow(FAST_LITE, VIEW_OPERATION, person_id, TrackingRtms.MULTI, request.path)
        else:
            ret["data"] = get_multi_fiscal_qtr_week_year_external(page, page_size)
            insert_operation_workflow(FAST_LITE_EXTERNAL, VIEW_OPERATION, person_id, TrackingRtms.MULTI, request.path)
    except Exception as e:
        ret[Ret.Code] = ErrCode.System
        ret[Ret.Msg] = str(e)

    return ret


@bp.route("/multi/demand/data")
@shield_person(request)
def get_multi_data_by_fiscal_quarter():
    if g.get(UserTypeKey) == UserType.Expert and len(g.get(SoldToNameKey)) == 0:
        return {
            Ret.Code: ErrCode.NoSoldToPermission,
            Ret.Msg: ErrMsg.get(ErrCode.NoSoldToPermission)
        }
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    fiscal_week_year = request.args.get("fiscal_week_year")
    order_by = request.args.getlist("orders[]")
    page_size = request.args.get("page_size", "11")
    page = request.args.get("page", "1")
    business_type = request.args.getlist("business_type[]")
    sold_to_id = request.args.getlist("sold_to_id[]")
    sold_to_name = request.args.getlist("sold_to_name[]")
    lob = request.args.getlist("lob[]")
    model = request.args.getlist("model[]")
    sku = request.args.getlist("sku[]")
    external = request.args.get("external", "0")
    # model 永不为空
    if model is None or len(model) == 0:
        model = get_model_list(DEMAND, lob, StrRTMMulti, fiscal_week_year)
    try:
        if g.get(UserTypeKey) == UserType.Expert and external != "1":
            ret["data"] = get_multi_demand_data(
                fiscal_week_year, business_type, sold_to_id, sold_to_name,
                lob, model, sku, order_by, page, page_size
            )
        else:
            ret["data"] = get_multi_demand_data_external(
                fiscal_week_year, lob, model, sku, order_by
            )
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        ret[Ret.Code] = ErrCode.System
        ret[Ret.Msg] = str(e)
    return ret


@bp.route("/multi/demand/data/download")
@shield_person(request)
def download_multi_data_by_fiscal_quarter():
    res = {
        Ret.Code: ErrCode.System
    }
    fiscal_week_year = request.args.get("fiscal_week_year")
    if fiscal_week_year is None:
        res[Ret.Msg] = 'please input fiscal_week_year. eg. 202308'
        return res
    # model 永不为空
    model = get_model_list(DEMAND, ["iphone"], StrRTMMulti, fiscal_week_year)
    try:
        fiscal_week_year = int(fiscal_week_year)
    except ValueError:
        res[Ret.Code] = ErrCode.Param
        res[Ret.Msg] = 'fiscal_week_year must be integer'
        return res
    external = request.args.get("external", "0")
    try:
        if g.get(UserTypeKey) == UserType.Expert and external != "1":
            version = request.args.get("version")
            if version != "1" and version != "2":
                res[Ret.Msg] = 'please input version. version must be 1 or 2'
                return res
            path = gen_multi_demand_download_data(fiscal_week_year, version, model)
        else:
            path = gen_multi_demand_download_data_external(fiscal_week_year,model)
        paths = os.path.split(path)
        file_name = paths[-1]

        response = make_response(send_file(path, as_attachment=True))
        response.headers["Content-Disposition"] = f"attachment; filename={file_name.encode().decode('latin-1')}"
        return response
    except ErrorExcept as e:
        res[Ret.Code] = e.code
        res[Ret.Msg] = e.err_msg
    except Exception as e:
        logger.exception(e)
        res[Ret.Msg] = str(e)
    return res


@bp.route("/multi/forecast/list")
@shield_person(request)
def get_multi_forecast_fiscal_qtr_list():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    page_size = request.args.get("page_size", "11")
    page = request.args.get("page", "1")
    external = request.args.get("external", "0")
    try:
        person_id = request.headers.get(ShieldPrsId, '')
        if g.get(UserTypeKey) == UserType.Expert and external != "1":
            ret["data"] = get_multi_forecast_data_list(page, page_size)
            insert_operation_workflow(FAST_LITE, VIEW_OPERATION, person_id, TrackingRtms.MULTI, request.path)
        else:
            ret["data"] = get_multi_forecast_data_list_external(page, page_size)
            insert_operation_workflow(FAST_LITE_EXTERNAL, VIEW_OPERATION, person_id, TrackingRtms.MULTI, request.path)
    except Exception as e:
        ret[Ret.Code] = ErrCode.System
        ret[Ret.Msg] = str(e)

    return ret


@bp.route("/multi/forecast/data")
@shield_person(request)
def get_multi_forecast_data_by_fiscal_quarter():
    if g.get(UserTypeKey) == UserType.Expert and len(g.get(SoldToNameKey)) == 0:
        return {
            Ret.Code: ErrCode.NoSoldToPermission,
            Ret.Msg: ErrMsg.get(ErrCode.NoSoldToPermission)
        }
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    fiscal_week_year = request.args.get("fiscal_week_year")
    business_type = request.args.getlist("business_type[]")
    sold_to_id = request.args.getlist("sold_to_id[]")
    sold_to_name = request.args.getlist("sold_to_name[]")
    lob = request.args.getlist("lob[]")
    model = request.args.getlist("model[]")
    external = request.args.get("external", "0")
    # model 永不为空
    if model is None or len(model) == 0:
        model = get_model_list(FORECAST, lob, StrRTMMulti, fiscal_week_year)
    try:
        if g.get(UserTypeKey) == UserType.Expert and external != "1":
            ret["data"] = get_multi_forecast_data_new(
                fiscal_week_year, business_type, sold_to_id, sold_to_name, lob, model)
        else:
            ret["data"] = get_multi_forecast_data_external(fiscal_week_year, lob, model)
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        ret[Ret.Code] = ErrCode.System
        ret[Ret.Msg] = str(e)
    return ret


@bp.route("/multi/forecast/data/download")
@shield_person(request)
def download_multi_forecast_data_by_fiscal_quarter():
    res = {
        Ret.Code: ErrCode.System
    }
    fiscal_week_year = request.args.get("fiscal_week_year")
    if fiscal_week_year is None:
        res[Ret.Msg] = 'please input fiscal_week_year. eg. 202308'
        return res

    # model 永不为空
    model = get_model_list(FORECAST, ["iphone"], StrRTMMulti, fiscal_week_year)

    try:
        fiscal_week_year = int(fiscal_week_year)
    except ValueError:
        res[Ret.Code] = ErrCode.Param
        res[Ret.Msg] = 'fiscal_week_year must be integer'
        return res
    external = request.args.get("external", "0")
    try:
        if g.get(UserTypeKey) == UserType.Expert and external != "1":
            path = download_multi_forecast_data(fiscal_week_year,model)
        else:
            path = download_multi_forecast_data_external(fiscal_week_year,model)
        paths = os.path.split(path)
        file_name = paths[-1]

        response = make_response(send_file(path, as_attachment=True))
        response.headers["Content-Disposition"] = f"attachment; filename={file_name.encode().decode('latin-1')}"
        return response
    except ErrorExcept as e:
        res[Ret.Code] = e.code
        res[Ret.Msg] = e.err_msg
    except Exception as e:
        logger.exception(e)
        res[Ret.Msg] = str(e)
    return res


@bp.route("/multi/demand/sold_to")
@shield_person(request)
def get_multi_demand_sold_to_list():
    res = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }

    try:
        res["data"] = {"list": get_multi_demand_sold_to_list_data()}
    except Exception as e:
        res[Ret.Code] = ErrCode.System
        res[Ret.Msg] = str(e)

    return res


@bp.route("/multi/demand/sku")
@shield_person(request)
def get_multi_demand_sku_list():
    res = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }

    try:
        res["data"] = {"list": get_multi_demand_sku_list_data()}
    except Exception as e:
        res[Ret.Code] = ErrCode.System
        res[Ret.Msg] = str(e)

    return res


@bp.route("/multi/forecast/sold_to")
@shield_person(request)
def get_multi_forecast_sold_to_list():
    res = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }

    try:
        res["data"] = {"list": get_multi_forecast_sold_to_list_data()}
    except Exception as e:
        res[Ret.Code] = ErrCode.System
        res[Ret.Msg] = str(e)

    return res


@bp.route("/multi/forecast/model")
@shield_person(request)
def get_multi_forecast_lob_list():
    res = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }

    try:
        res["data"] = {"list": get_multi_forecast_model_list_data()}
    except Exception as e:
        res[Ret.Code] = ErrCode.System
        res[Ret.Msg] = str(e)

    return res


@bp.route("/multi/demand/business_type/list")
@shield_person(request)
def get_multi_demand_business_type_raw_list():
    res = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    fiscal_week_year = request.args.get("fiscal_week_year")
    try:
        res["data"] = {"list": get_multi_raw_business_type_data(fiscal_week_year)}
    except ErrorExcept as e:
        res[Ret.Code] = e.code
        res[Ret.Msg] = e.err_msg
    except Exception as e:
        res[Ret.Code] = ErrCode.System
        res[Ret.Msg] = str(e)

    return res


@bp.route("/multi/demand/sold_to/list")
@shield_person(request)
def get_multi_demand_sold_to_raw_list():
    res = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    fiscal_week_year = request.args.get("fiscal_week_year")
    business_type = request.args.getlist("business_type[]")
    sold_to = request.args.get("sold_to")
    try:
        res["data"] = {"list": get_multi_raw_sold_to_data(business_type, sold_to, fiscal_week_year)}
    except ErrorExcept as e:
        res[Ret.Code] = e.code
        res[Ret.Msg] = e.err_msg
    except Exception as e:
        res[Ret.Code] = ErrCode.System
        res[Ret.Msg] = str(e)
        raise e

    return res


@bp.route("/multi/forecast/business_type/list")
@shield_person(request)
def get_multi_forecast_business_type_raw_list():
    res = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    fiscal_week_year = request.args.get("fiscal_week_year")
    try:
        res["data"] = {"list": get_multi_raw_business_type_data(fiscal_week_year)}
    except ErrorExcept as e:
        res[Ret.Code] = e.code
        res[Ret.Msg] = e.err_msg
    except Exception as e:
        res[Ret.Code] = ErrCode.System
        res[Ret.Msg] = str(e)

    return res


@bp.route("/multi/forecast/sold_to/list")
@shield_person(request)
def get_multi_forecast_sold_to_raw_list():
    res = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    fiscal_week_year = request.args.get("fiscal_week_year")
    business_type = request.args.getlist("business_type[]")
    sold_to = request.args.get("sold_to")
    try:
        res["data"] = {"list": get_multi_raw_sold_to_data(business_type, sold_to, fiscal_week_year)}
    except ErrorExcept as e:
        res[Ret.Code] = e.code
        res[Ret.Msg] = e.err_msg
    except Exception as e:
        res[Ret.Code] = ErrCode.System
        res[Ret.Msg] = str(e)

    return res


@bp.route("/multi/demand/lob/list")
def get_multi_demand_lob_raw_list():
    res = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        res["data"] = {"list": get_lob_list()}
    except Exception as e:
        res[Ret.Code] = ErrCode.System
        res[Ret.Msg] = str(e)

    return res


@bp.route("/multi/demand/model/list")
def get_multi_demand_model_raw_list():
    res = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    lob = request.args.getlist("lob[]")
    fiscal_week_year = request.args.get("fiscal_week_year")

    try:
        res["data"] = {"list": get_model_list(DEMAND,lob, StrRTMMulti, fiscal_week_year)}
    except Exception as e:
        res[Ret.Code] = ErrCode.System
        res[Ret.Msg] = str(e)

    return res


@bp.route("/multi/demand/sku/list")
def get_multi_demand_sku_raw_list():
    res = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    model = request.args.getlist("model[]")
    fiscal_week_year = request.args.get("fiscal_week_year")
    if model is None or len(model) == 0:
        model = get_model_list(DEMAND, ["iphone"], StrRTMMulti, fiscal_week_year)
    try:
        res["data"] = {"list": get_sku_list(model, StrRTMMulti)}
    except Exception as e:
        res[Ret.Code] = ErrCode.System
        res[Ret.Msg] = str(e)

    return res


@bp.route("/multi/forecast/lob/list")
def get_multi_forecast_lob_raw_list():
    res = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        res["data"] = {"list": get_lob_list()}
    except Exception as e:
        res[Ret.Code] = ErrCode.System
        res[Ret.Msg] = str(e)

    return res


@bp.route("/multi/forecast/model/list")
def get_multi_forecast_model_raw_list():
    res = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    lob = request.args.getlist("lob[]")
    fiscal_week_year = request.args.get("fiscal_week_year")
    try:
        res["data"] = {"list": get_model_list(FORECAST,lob, StrRTMMulti, fiscal_week_year)}
    except Exception as e:
        res[Ret.Code] = ErrCode.System
        res[Ret.Msg] = str(e)

    return res


@bp.route("/multi/forecast/sku/list")
def get_multi_forecast_sku_raw_list():
    res = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    model = request.args.getlist("model[]")
    fiscal_week_year = request.args.get("fiscal_week_year")
    if model is None or len(model) == 0:
        model = get_model_list(FORECAST, ["iphone"], StrRTMMulti, fiscal_week_year)
    try:
        res["data"] = {"list": get_sku_list(model, StrRTMMulti)}
    except Exception as e:
        res[Ret.Code] = ErrCode.System
        res[Ret.Msg] = str(e)

    return res
