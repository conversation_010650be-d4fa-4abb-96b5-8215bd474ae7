from .api_common import *
from service.allocation_collection_adjustment_service import *
from util.util_operation import insert_operate_record


bp = Blueprint('allocation_collection_api', __name__, url_prefix=UrlPrefix)


@bp.route("/allocation_collection/list", methods=["GET"])
def collection_adjustment_list():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        query_params = request.args
        fiscal_week_year = query_params.get('fiscal_week_year')
        module = query_params.get('module')
        ret["data"] = get_demand_collection_list(fiscal_week_year, module)
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)

    return ret


@bp.route("/allocation_collection/upload", methods=["POST"])
def upload_collection_file():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        query_params = request.args
        rtm = query_params.get('rtm')
        fiscal_week_year = query_params.get('fiscal_week_year')
        if not rtm:
            raise ErrorExcept(ErrCode.Param, "please input rtm.")
        file = request.files.get("file")
        uploader = request.headers.get(ShieldPrsNickName) + " " + request.headers.get(ShieldPrsLastName)
        uploader_email = request.headers.get(ShieldPrsEmail)
        ret["data"] = upload_collection_file_by_rtm(rtm, file, fiscal_week_year, uploader, uploader_email)
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)

    return ret


@bp.route("/allocation_collection/rerun", methods=["POST"])
def collection_adjustment_rerun():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        query_params = request.args
        rtm = query_params.get('rtm')
        fiscal_week_year = query_params.get('fiscal_week_year')
        operator = request.headers.get(ShieldPrsNickName) + " " + request.headers.get(ShieldPrsLastName)
        ret["data"] = cpf_collection_adjustment_rerun(rtm, fiscal_week_year)
        insert_operate_record(rtm, AllocationOperateCategory.AdjustmentRerun, None, None, operator, None)
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)

    return ret


@bp.route("/allocation_collection/adjustment/approve", methods=["POST"])
def allocation_collection_adjustment_approve():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        body_params = request.json
        rtm = body_params.get('rtm')
        fiscal_week_year = body_params.get('fiscal_week_year')
        if not rtm or not fiscal_week_year:
            raise ErrorExcept(ErrCode.Param, "please check params.")
        uploader = request.headers.get(ShieldPrsNickName) + " " + request.headers.get(ShieldPrsLastName)
        uploader_email = request.headers.get(ShieldPrsEmail)
        ret["data"] = adjustment_approve_service(rtm, fiscal_week_year, uploader, uploader_email)
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)

    return ret
