from apis.datasource.supply import latest_fiscal_week, latest_file, target_file_name, fetch_supply_worker
from data.s3bucket.supply_data import list_fiscal_weeks
from domain.supply.entity import SupplyFileName
from service.datasource_service import sort_fiscal_qtr_week


def test_latest_fiscal_week():
    files = [
        SupplyFileName(
            "databend-dumps/FAST/PHOEBE/AirPods/FY24Q2W4/2024-01-1607:32:46/data_2258df5b-339d-4775-a17a-88e6491dc3e5_0000_00000000.parquet"),
        SupplyFileName(
            "databend-dumps/FAST/PHOEBE/AirPods/FY24Q1W13/2024-01-1607:32:46/data_2258df5b-339d-4775-a17a-88e6491dc3e5_0000_00000000.parquet"),
        SupplyFileName(
            "databend-dumps/FAST/PHOEBE/AirPods/FY24Q3W1/2024-01-1607:32:46/data_2258df5b-339d-4775-a17a-88e6491dc3e5_0000_00000000.parquet"),
        SupplyFileName(
            "databend-dumps/FAST/PHOEBE/AirPods/FY24Q3W1/2024-01-1707:32:46/data_2258df5b-339d-4775-a17a-88e6491dc3e5_0000_00000000.parquet"),
    ]
    print('file::', files[0].fiscal_qtr_week_name, files[0].fiscal_week)
    latest_week = latest_fiscal_week(files)
    assert latest_week == "FY24Q3W1"
    file = latest_file(latest_week, files)
    print(file)
    assert file == "databend-dumps/FAST/PHOEBE/AirPods/FY24Q3W1/2024-01-1707:32:46/data_2258df5b-339d-4775-a17a-88e6491dc3e5_0000_00000000.parquet"


def test_target_file_name():
    file_name = "databend-dumps/FAST/PHOEBE/AirPods/FY24Q2W5/2024-01-2906:20:03/data_029f45ba-db16-411b-8c04-a157b69177c5_0000_00000000.parquet"
    f = target_file_name(file_name, {})
    print(f)
    assert f == "AirPods_FY24Q2W5_2024-01-2906:20:03_data_029f45ba-db16-411b-8c04-a157b69177c5_0000_00000000"


# 用于本地调试，测试celeryworker流程
def test_fetch_supply_worker():
    fetch_supply_worker(0, 'iPhone', "<EMAIL>", "jincancan")


def test_list_fiscal_weeks():
    path = "databend-dumps/FAST/PHOEBE/iPhone/FY24Q3W1/"
    f = SupplyFileName(path)

    assert f.fiscal_qtr_week_name== "FY24Q3W1"
    assert f.file_name == "databend-dumps/FAST/PHOEBE/iPhone/FY24Q3W1/"

    files = [
        SupplyFileName(
            "databend-dumps/FAST/PHOEBE/AirPods/FY24Q2W4/2024-01-1607:32:46/data_2258df5b-339d-4775-a17a-88e6491dc3e5_0000_00000000.parquet"),
        SupplyFileName(
            "databend-dumps/FAST/PHOEBE/AirPods/FY24Q1W13/2024-01-1607:32:46/data_2258df5b-339d-4775-a17a-88e6491dc3e5_0000_00000000.parquet"),
        SupplyFileName(
            "databend-dumps/FAST/PHOEBE/AirPods/FY24Q3W1/2024-01-1607:32:46/data_2258df5b-339d-4775-a17a-88e6491dc3e5_0000_00000000.parquet"),
        SupplyFileName(
            "databend-dumps/FAST/PHOEBE/AirPods/FY24Q3W1/2024-01-1707:32:46/data_2258df5b-339d-4775-a17a-88e6491dc3e5_0000_00000000.parquet"),
    ]
    print('file::', files[0].fiscal_qtr_week_name, files[0].fiscal_week, files[0].updated_at)
    fiscal_weeks = list_fiscal_weeks(files)
    print(fiscal_weeks)
    assert fiscal_weeks == ['FY24Q3W1', 'FY24Q2W4', 'FY24Q1W13']


def test_qtr_week_name_sort():
    qtr_list = [{
        "data": [{
            "file_name": "/usr/local/fast_lite_server/uploads/datasource/supply/GC iPad Supply Data--[FY24Q2W5] - [2024-02-06 13:12:11].zip",
            "last_upload_date": "2024-02-20 13:12:08",
        }],
        "fiscal_qtr_week_name": "FY24Q2W5"
    }, {
        "data": [{
            "file_name": "/usr/local/fast_lite_server/uploads/datasource/supply/GC iPad Supply Data--[FY24Q2W7] - [2024-02-19 00:41:41].zip",
            "last_upload_date": "2024-02-19 00:41:41",
        }],
        "fiscal_qtr_week_name": "FY24Q2W7"
    }, {
        "data": [{
            "file_name": "/usr/local/fast_lite_server/uploads/datasource/supply/GC iPad Supply Data--[FY24Q2W6] - [2024-02-12 00:41:41].zip",
            "last_upload_date": "2024-02-12 00:41:41",
        }],
        "fiscal_qtr_week_name": "FY24Q2W6"
    }]

    expect_qtr_list = [{
        "data": [{
            "file_name": "/usr/local/fast_lite_server/uploads/datasource/supply/GC iPad Supply Data--[FY24Q2W7] - [2024-02-19 00:41:41].zip",
            "last_upload_date": "2024-02-19 00:41:41",
        }],
        "fiscal_qtr_week_name": "FY24Q2W7"
    }, {
        "data": [{
            "file_name": "/usr/local/fast_lite_server/uploads/datasource/supply/GC iPad Supply Data--[FY24Q2W6] - [2024-02-12 00:41:41].zip",
            "last_upload_date": "2024-02-12 00:41:41",
        }],
        "fiscal_qtr_week_name": "FY24Q2W6"
    }, {
        "data": [{
            "file_name": "/usr/local/fast_lite_server/uploads/datasource/supply/GC iPad Supply Data--[FY24Q2W5] - [2024-02-06 13:12:11].zip",
            "last_upload_date": "2024-02-20 13:12:08",
        }],
        "fiscal_qtr_week_name": "FY24Q2W5"
    }]
    ret = qtr_list.sort(key=sort_fiscal_qtr_week, reverse=True)
    print(ret)
    assert qtr_list == expect_qtr_list
