import os

from datetime import datetime

import pandas as pd

from data.databend.vw_status_data import CpfVwStatusDatabend
from data.cpf_data_source import OdsFastCPFActiveSKULob
from data.datasource_automatic_update_record import DataSourceFileRecordRepository, DatasourceAutomaticUpdateRecord
from util.conf import logger
from data.s3bucket.supply_data import download_parquet_file_from_s3bucket, list_files, latest_fiscal_week
from domain.supply.supply import handle as supply_handle
from domain.supply.entity import SupplyFileName, Lob
from util.const import ErrorExcept, ErrCode, EmailCmd, AutomicJobType, StrRTMCPF, \
    DataSourceFileStatus
from util.send_email import send_email_by_database
from celery_config import celery
from task_kit.repository.task_repository import TaskRepository, TaskStatus
import traceback


@celery.task(name="fetch_supply_worker")
def fetch_supply_worker(task_id: int,lob: str, recipient: str, uploader: str, is_debug = False) -> None:
    try:
        # 加载supply文件：扫描目录选取最新的supply文件
        if not is_debug:
            files, _ = list_files(prefix=f"databend-dumps/FAST/PHOEBE/{lob}/")
            latest_week = latest_fiscal_week(files)
        else:
            latest_week = 'FY24Q2W5'
            files = []
        file, params = do_fetch_supply(task_id, lob, files, latest_week, is_debug)

        # 3、给用户发邮件
        send_email_by_database(EmailCmd.SupplyDataEmail, file_name=file, recipients=recipient, params=params)
        logger.info(f'send mail to {recipient} success. params = {params}, file_name:{file}')

        # 4、保存record数据至datasource_file表
        save_automatic_datasource_file_record(lob, file, uploader, params, latest_week)

    except Exception as e:
        logger.error(e)
        params = {"error": f'task_id: {task_id}, lob: {lob}, recipient: {recipient},\
                    uploader: {uploader}, is_debug: {is_debug}. error: {traceback.format_exc()}'}
        send_email_by_database(EmailCmd.WarningEmail, file_name='', recipients=None, params=params)
        raise Exception(e)


def do_fetch_supply(task_id: int, lob: str, files:list[SupplyFileName], latest_week, is_debug = False) -> (pd.DataFrame, dict):
    # 异步开始执行 修改status为running
    # TaskRepository.update_status(task_id, TaskStatus.Running.value)

    if not is_debug:
        # 1、加载依赖数据
        latest_file_name = latest_file(latest_week, files)
        if not latest_file_name:
            raise ErrorExcept(ErrCode.NoSupplyFileInTargetWeek, f"no such file in week:{latest_week}")
        logger.info(f"lob:{lob} Fetched {len(files)}. week:{latest_week}, latest_file_name:{latest_file_name}")
        # 从s3下载最新的supply文件
        origin_file_df = download_parquet_file_from_s3bucket(object_key=latest_file_name)
    else:
        origin_file_df = download_parquet_file_from_s3bucket(object_key='', is_debug=True)
        latest_file_name = 'test_file'
    # 加载确认状态流水
    logger.info("begin load confirm flow.")
    confirm_flow = CpfVwStatusDatabend.get_vw_status_by_week(latest_week)
    logger.info("load confirm flow succeed.")
    for f in confirm_flow:
        if f.sales_org == 'China mainland' and f.ops_line_desc == 'iPad (10th Gen)':
            logger.info(
                f"confirm_flow: status_ts:{f.confirmed_at}, fiscal_year:{f.fiscal_year} sales_org:{f.sales_org} ops_line_desc:{f.ops_line_desc}.")
    # 加载当前的mpn list
    mpns = OdsFastCPFActiveSKULob.list_mpns(lob)
    #print("mpns::", mpns)


    # 2、根据supply文件和状态流水，生成最终的supply data
    params = {
        'fiscal_qtr_week_name': latest_week,
        'lob': lob,
        'time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    }
    file = supply_handle(target_file_name(latest_file_name, params), origin_file_df, confirm_flow, mpns, is_debug)
    logger.info(f'save final supply data as file:{file}.')

    # 修改task的状态为done
    # TaskRepository.update_status(task_id, TaskStatus.Done.value)

    return file, params


def target_file_name(file_name: str, params: dict):
    file_name = f"GC {params.get('lob')} Supply Data--[{params.get('fiscal_qtr_week_name')}] - [{params.get('time')}]"
    #file_name = file_name.replace('databend-dumps/FAST/PHOEBE/','').replace('/','_')
    return os.path.splitext(os.path.basename(file_name))[0]


def latest_file(week: str, files: list[SupplyFileName]) -> str:
    # 过滤财年周
    week_files = [file for file in files if file.fiscal_qtr_week_name == week]

    def sort_by_updated_at(file: SupplyFileName) -> int:
        return file.updated_at

    week_files = sorted(week_files, key=sort_by_updated_at, reverse=True)
    if len(week_files) == 0:
        return ''
    return week_files[0].file_name


def save_automatic_datasource_file_record(lob: str,
                                          file_name: str,
                                          upload_by: str,
                                          params: dict = None,
                                          fiscal_qtr_week_name: str = None,
                                          ):
    logger.info(f'start save_automatic_datasource_file_record: lob:{lob}, file_name:{file_name}, upload_by:{upload_by}, params:{params}.')
    record = DatasourceAutomaticUpdateRecord(
        fiscal_qtr_week_name=fiscal_qtr_week_name,
        datasource_type=AutomicJobType.SupplyData,
        upload_by=upload_by,
        lob=lob,
        rtm=StrRTMCPF,
        params=str(params),
        file_name=file_name,
        create_at=params.get('time'),
        update_at=params.get('time'),
        status=DataSourceFileStatus.Enabled
    )
    logger.info(f'end save_automatic_datasource_file_record.')
    DataSourceFileRecordRepository.create(record)





