from apis.api_common import *
from domain.datasource.impl.automatic import (CTO_CHANNEL_PERMISSIONS, KeySubKeyRelation, ESR_CHANNEL_PERMISSIONS,
                                              AutomaticFileResult, ESR_MONO_ONLINE_PERMISSION_LOB_TO_DB_LOB_MAPPING)
from domain.datasource.impl.automatic.esr_impl import download_content_from_db, get_download_record_file
from service.datasource_service import get_datasource_automatic_esr_list, get_datasource_automatic_list, \
    get_esr_record_list

bp = Blueprint('automatic_esr', __name__, url_prefix='/datasource/automatic')


# 20241204统一把snapshot ts字段替换成sync_time字段
@bp.route('/list')
def esr_list():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: 'Success'}
    try:
        channel = request.args.get("channel", "", str)
        # 必填校验
        if not all([channel]):
            raise ErrorExcept(ErrCode.Param, "please check params, required parameter missing.")

        if channel != 'CP&F':
            ret[Ret.Data] = {
                "list": get_datasource_automatic_esr_list(channel),
            }
            return ret

        ret[Ret.Data] = {
            "list": get_datasource_automatic_esr_list(channel),
            "supply_list": get_datasource_automatic_list()
        }
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e.err_msg, exc_info=True)
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(str(e), exc_info=True)
    return ret


# 20241204统一把snapshot ts字段替换成sync_time字段
@bp.route('/record/list')
def esr_detail_list():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: 'Success'}
    try:
        channel = request.args.get("channel", "", str)
        key = request.args.get("key", "", str)
        # 必填校验
        if not all([key]):
            raise ErrorExcept(ErrCode.Param, "please check params, required parameter missing.")

        channel_permissions = {}
        if key in [KeySubKeyRelation.ESR_MAIN_PRODUCTS.value, KeySubKeyRelation.ESR_ACCESSORIES.value]:
            channel_permissions = ESR_CHANNEL_PERMISSIONS
        elif key in [KeySubKeyRelation.CTO_POD.value, KeySubKeyRelation.CTO_BACKLOG.value, KeySubKeyRelation.CTO_EXECUTION.value]:
            channel_permissions = CTO_CHANNEL_PERMISSIONS

        if channel not in channel_permissions.keys():
            raise ErrorExcept(ErrCode.Param, "please check params, channel invalid")

        ret[Ret.Data] = {"list": get_esr_record_list(key, channel)}

    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e.err_msg, exc_info=True)
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(str(e), exc_info=True)
    return ret


# 20241204统一把snapshot ts字段替换成sync_time字段
@bp.route('/record/download')
def esr_download():
    """
    备注: 因为要用sync_time作为更新时间, 所以下载接口使用file_path来下载，而不是用原来的channel、last_update_time来拼接一个唯一key下载
    """
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: 'Success'}
    try:
        channel = request.args.get("channel", "", str)
        key = request.args.get("key", "", str)
        last_update_time = request.args.get("last_update_time", "", str)
        query_file_path = request.args.get("file_path", "", str)
        # 必填校验
        if not all([key, channel, last_update_time]):
            raise ErrorExcept(ErrCode.Param, "please check params, required parameter missing.")

        # lob 下载仅支持mono、online
        lob = None
        if key == KeySubKeyRelation.ESR_MAIN_PRODUCTS.value:
            lob = ESR_MONO_ONLINE_PERMISSION_LOB_TO_DB_LOB_MAPPING.get(request.args.get("lob", "", str), None)

        channel_permissions = {}
        if key in [KeySubKeyRelation.ESR_MAIN_PRODUCTS.value, KeySubKeyRelation.ESR_ACCESSORIES.value]:
            channel_permissions = ESR_CHANNEL_PERMISSIONS
        elif key in [KeySubKeyRelation.CTO_POD.value, KeySubKeyRelation.CTO_BACKLOG.value, KeySubKeyRelation.CTO_EXECUTION.value]:
            channel_permissions = CTO_CHANNEL_PERMISSIONS

        if channel not in channel_permissions.keys():
            raise ErrorExcept(ErrCode.Param, "please check params, channel invalid")

        query_params = channel_permissions[channel]
        query_params["snapshot_ts"] = last_update_time

        if key in [KeySubKeyRelation.CTO_BACKLOG.value, KeySubKeyRelation.CTO_EXECUTION.value]:
            # 这两个直接从数据库中下载内容
            file_type = "CTO-Backlog" if key == KeySubKeyRelation.CTO_BACKLOG.value else "CTO-Execution"
            file_name = f"{last_update_time}_{file_type}_{channel}.xlsx"
            excel_file = download_content_from_db(query_key=key, last_update_time=last_update_time, channel=channel)
            response = make_response(send_file(excel_file,
                                               mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                                               download_name=file_name, as_attachment=True))
        else:
            file_name, file_path = get_download_record_file(query_key=key,
                                                            last_update_time=last_update_time,
                                                            channel=channel,
                                                            lob=lob,
                                                            query_file_path=query_file_path)
            # 创建响应对象
            response = make_response(send_file(file_path,
                                               mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                                               download_name=file_name, as_attachment=True))
        response.headers["Content-Disposition"] = f'attachment; filename="{file_name}"'
        return response
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e.err_msg, exc_info=True)
        return ret
    except FileNotFoundError as e:
        logger.error(str(e))
        ret[Ret.Code] = ErrCode.System
        ret[Ret.Msg] = 'No such file or directory.'
        logger.error(str(e), exc_info=True)
        return ret
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(str(e), exc_info=True)
        return ret
