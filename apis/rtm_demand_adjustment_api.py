from .api_common import *
from service.rtm_demand_adjustment_service import *


bp = Blueprint('rtm_demand_adjustment_api', __name__, url_prefix=UrlPrefix)


@bp.route('/allocation_prepare/demand_adjustment/check_wednesday_esr')
def check_wednesday_esr():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        query_params = request.args
        fiscal_week_year = int(query_params.get(StrFiscalWeekYear))
    except ValueError:
        ret[Ret.Code] = ErrCode.Param
        ret[Ret.Msg] = f'{StrFiscalWeekYear} must be integer'
        return ret
    try:
        ret[Ret.Data] = check_wednesday_esr_service(fiscal_week_year)
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)

    return ret


@bp.route('/allocation_prepare/demand_adjustment/template_file')
def get_demand_adjustment_template_file():
    '''
    直接从数据库中获取模板文件的数据，
    省略了模板数据修改、重新生成等人工参与的环节。
    '''
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        query_params = request.args

        rtm = query_params.get('rtm')
        if rtm is None or rtm.strip() == '':
            ret[Ret.Code] = ErrCode.Param
            ret[Ret.Msg] = 'please input rtm.'
            return ret

        fiscal_qtr_week_name = query_params.get('fiscal_qtr_week_name', '')
        lob = query_params.get('lob', 'iPad')
        try:
            fiscal_week_year = int(query_params.get('fiscal_week_year'))
        except ValueError:
            ret[Ret.Code] = ErrCode.Param
            ret[Ret.Msg] = 'fiscal_week_year must be integer'
            return ret

        file_path, file_name = get_demand_adjustment_template_file_service(
            rtm, fiscal_week_year, fiscal_qtr_week_name, lob)
        response = make_response(
            send_file(file_path+file_name, as_attachment=True))
        response.headers["Content-Disposition"] = f'attachment; filename="{file_name}"'
        return response

    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)

    return ret


@bp.route('/allocation_prepare/demand_adjustment/rtm/upload_file', methods=["POST"])
def rtm_upload_demand_adjustment_file():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    lob = request.args.get('lob', 'iPad')
    try:
        id = int(request.args.get('id'))
    except ValueError:
        ret[Ret.Code] = ErrCode.Param
        ret[Ret.Msg] = 'id must be integer'
        return ret
    file = request.files.get("file")
    uploader = request.headers.get(
        ShieldPrsNickName) + " " + request.headers.get(ShieldPrsLastName)
    uploader_email = request.headers.get(ShieldPrsEmail)
    try:
        ret["data"] = upload_file_by_rtm_service(
            file, uploader, uploader_email, id, lob)
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        if len(e.err_msg) <= 500:
            ret[Ret.Msg] = e.err_msg
        else:
            ret[Ret.Msg] = e.err_msg[:500] + "..."
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = FileUploadError.UnknownError
        ret["error"] = str(e)
    return ret
