from flask import Blueprint, request

from data.fiscal_year_week import FiscalYearWeek
from util.const import *
from task import cpf_prepare_demand_submission_mono
from task import cpf_prepare_demand_submission_template
from task import cpf_prepare_demand_submission_others
from service.cpf_sell_in_demand_service import sell_in_demand_with_tags, generate_sell_in_demand_mono_hr_only
from task.task_cpf_data_source_snapshot import generate_active_sku_snapshot
from task.task_allocation_prepare_record import generate_allocation_record
from task.task_change_rtms_status import change_status_by_week_at_10_clock, change_status_by_week_at_16_clock
from task.task_open_rtm_phase import open_phase_by_week_rtm
from task.task_demand_adjustment_template_file import generate_demand_adjustment_template_file
from task.task_check_esr_mon_wed import check_esr_status
from task.task_sell_in_demand_remind_email import timed_reminder_email
from task.task_allocation_send_email_manually import send_email_manually
from task.task_sales_input_template_file import generate_sales_input_template_file_function,\
    check_open_backlog_less_than_monday, cover_upload_file_by_template_file_all_rtm,\
    send_email_after_system_open, send_email_after_refresh_esr_on_tuesday
from task.task_sell_in_demand_with_tags_multi_online import task_regenerate_multi_online_demand_with_tags_file


bp = Blueprint('task_api', __name__, url_prefix=UrlPrefix)


@bp.route("/task/cpf_prepare_demand_submission_mono", methods=["POST"])
def cpf_prepare_demand_submission_mono_task():
    lob = request.json.get('lob')
    fiscal_date = request.json.get('fiscal_dt')
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        fiscal_obj = FiscalYearWeek.get_fis_by_date(fiscal_date)
        if fiscal_obj is None:
            raise ErrorExcept(ErrCode.Param, "can't find fiscal day")
        cpf_prepare_demand_submission_mono.get_data_by_lob(fiscal_obj, lob)
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        ret[Ret.Code] = ErrCode.System
        ret[Ret.Msg] = str(e)
    return ret


@bp.route("/task/cpf_prepare_demand_submission_template", methods=["POST"])
def cpf_prepare_demand_submission_template_task():
    lob = request.json.get('lob')
    fiscal_date = request.json.get('fiscal_dt')
    rtm = request.json.get('rtm')
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        fiscal_obj = FiscalYearWeek.get_fis_by_date(fiscal_date)
        if fiscal_obj is None:
            raise ErrorExcept(ErrCode.Param, "can't find fiscal day")
        cpf_prepare_demand_submission_template.get_data_by_lob(rtm, fiscal_obj, lob)
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        ret[Ret.Code] = ErrCode.System
        ret[Ret.Msg] = str(e)
    return ret


@bp.route("/task/cpf_prepare_demand_submission_others", methods=["POST"])
def cpf_prepare_demand_submission_others_task():
    lob = request.json.get('lob')
    fiscal_date = request.json.get('fiscal_dt')
    rtm = request.json.get('rtm')
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        fiscal_obj = FiscalYearWeek.get_fis_by_date(fiscal_date)
        if fiscal_obj is None:
            raise ErrorExcept(ErrCode.Param, "can't find fiscal day")
        cpf_prepare_demand_submission_others.get_data_by_lob(rtm, fiscal_obj, lob)
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        ret[Ret.Code] = ErrCode.System
        ret[Ret.Msg] = str(e)
    return ret

@bp.route("/task/sell_in_demand/mono_hr_only", methods=["POST"])
def sell_in_demand_mono_hr_only():
    fiscal_date = request.json.get('fiscal_dt')
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        generate_sell_in_demand_mono_hr_only(fiscal_date)
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        ret[Ret.Code] = ErrCode.System
        ret[Ret.Msg] = str(e)
    return ret


@bp.route("/task/sell_in_demand/rtms_demand_with_tags", methods=["POST"])
def sell_in_demand_rtms_demand_with_tags():
    fiscal_date = request.json.get('fiscal_dt')
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        sell_in_demand_with_tags(fiscal_date)
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        ret[Ret.Code] = ErrCode.System
        ret[Ret.Msg] = str(e)
    return ret


@bp.route("/task/active_sku_ipad_snapshot", methods=["POST"])
def task_active_sku_ipad_snapshot():
    fiscal_date = request.json.get('fiscal_dt')
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        task_result = generate_active_sku_snapshot(fiscal_date)
        ret[Ret.Msg] = task_result
        if not task_result:
            ret[Ret.Code] = ErrCode.System
            ret[Ret.Msg] = 'No data generated.'
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    return ret


@bp.route("/task/generate_allocation_record", methods=["POST"])
def task_create_allocation_record():
    fiscal_date = request.json.get('fiscal_dt')
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        task_result = generate_allocation_record(fiscal_date)
        ret[Ret.Msg] = task_result
        if not task_result:
            ret[Ret.Code] = ErrCode.System
            ret[Ret.Msg] = 'Can not generate data, please check it out.'
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    return ret


@bp.route("/task/change_sales_input_upload_status", methods=["POST"])
def task_change_sales_input_upload_status():
    task_type = request.json.get('task_type')
    fiscal_date = request.json.get('fiscal_dt')
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    if not task_type or not fiscal_date:
        ret[Ret.Code] = ErrCode.Param
        ret[Ret.Msg] = ErrMsg.get(ErrCode.Param)
        return ret
    try:
        if str(task_type) == '10':
            change_status_by_week_at_10_clock(fiscal_date)
        elif str(task_type) == '16':
            change_status_by_week_at_16_clock(fiscal_date)
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    return ret


@bp.route("/task/open_phase_by_week_rtm", methods=["POST"])
def task_open_phase_by_week_rtm():
    rtm = request.json.get('rtm')
    phase = request.json.get('phase')
    fiscal_date = request.json.get('fiscal_dt')
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    if not rtm or not phase or not fiscal_date:
        ret[Ret.Code] = ErrCode.Param
        ret[Ret.Msg] = ErrMsg.get(ErrCode.Param)
        return ret
    try:
        ret[Ret.Msg] = open_phase_by_week_rtm(fiscal_date, rtm, phase)
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    return ret


@bp.route("/task/generate_demand_adjustment_template_file", methods=["POST"])
def task_generate_demand_adjustment_template_file():
    rtm = request.json.get('rtm')
    fiscal_date = request.json.get('fiscal_dt')
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    if not rtm or not fiscal_date:
        ret[Ret.Code] = ErrCode.Param
        ret[Ret.Msg] = ErrMsg.get(ErrCode.Param)
        return ret
    try:
        ret[Ret.Msg] = generate_demand_adjustment_template_file(fiscal_date, rtm)
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    return ret


@bp.route("/task/check_esr_status", methods=["POST"])
def task_check_esr_status():
    task_type = request.json.get('task_type')
    fiscal_date = request.json.get('fiscal_dt')
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    if not task_type or not fiscal_date:
        ret[Ret.Code] = ErrCode.Param
        ret[Ret.Msg] = ErrMsg.get(ErrCode.Param)
        return ret
    try:
        ret[Ret.Msg] = check_esr_status(task_type, fiscal_date)
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    return ret


@bp.route("/task/sell_in_demand_remind_email", methods=["POST"])
def task_sell_in_demand_remind_email():
    task_type = request.json.get('task_type')
    rtm = request.json.get('rtm')
    fiscal_date = request.json.get('fiscal_dt')
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    if not task_type or not fiscal_date or not rtm:
        ret[Ret.Code] = ErrCode.Param
        ret[Ret.Msg] = ErrMsg.get(ErrCode.Param)
        return ret
    try:
        ret[Ret.Msg] = timed_reminder_email(task_type, rtm, fiscal_date)
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    return ret


@bp.route("/task/send_email_manually", methods=["POST"])
def task_send_email_manually():
    email_type = request.json.get('email_type')
    fiscal_date = request.json.get('fiscal_dt')
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    if not email_type or not fiscal_date:
        ret[Ret.Code] = ErrCode.Param
        ret[Ret.Msg] = ErrMsg.get(ErrCode.Param)
        return ret
    try:
        ret[Ret.Msg] = send_email_manually(email_type, fiscal_date)
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    return ret


@bp.route("/task/generate_sales_input_template_file", methods=["POST"])
def task_generate_sales_input_template_file():
    rtm = request.json.get('rtm')
    fiscal_date = request.json.get('fiscal_dt')
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    if not rtm or not fiscal_date:
        ret[Ret.Code] = ErrCode.Param
        ret[Ret.Msg] = ErrMsg.get(ErrCode.Param)
        return ret
    try:
        ret[Ret.Msg] = generate_sales_input_template_file_function(fiscal_date, rtm)
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    return ret


@bp.route("/task/check_open_backlog_less_than_monday", methods=["POST"])
def task_check_open_backlog_less_than_monday():
    fiscal_date = request.json.get('fiscal_dt')
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    if not fiscal_date:
        ret[Ret.Code] = ErrCode.Param
        ret[Ret.Msg] = ErrMsg.get(ErrCode.Param)
        return ret
    try:
        ret[Ret.Msg] = check_open_backlog_less_than_monday(fiscal_date)
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    return ret


@bp.route("/task/regenerate_multi_online_demand_with_tags_file", methods=["POST"])
def task_api_regenerate_multi_online_demand_with_tags_file():
    fiscal_date = request.json.get('fiscal_dt')
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    if not fiscal_date:
        ret[Ret.Code] = ErrCode.Param
        ret[Ret.Msg] = ErrMsg.get(ErrCode.Param)
        return ret
    try:
        ret[Ret.Msg] = task_regenerate_multi_online_demand_with_tags_file(fiscal_date)
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    return ret


@bp.route("/task/cover_sales_input_upload_file", methods=["POST"])
def task_cover_sales_input_upload_file():
    fiscal_date = request.json.get('fiscal_dt')
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    if not fiscal_date:
        ret[Ret.Code] = ErrCode.Param
        ret[Ret.Msg] = ErrMsg.get(ErrCode.Param)
        return ret
    try:
        ret[Ret.Msg] = cover_upload_file_by_template_file_all_rtm(fiscal_date)
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    return ret


@bp.route("/task/notice_email", methods=["POST"])
def task_notice_email():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    fiscal_date = request.json.get('fiscal_dt')
    notice_type = request.json.get('notice_type')
    if not fiscal_date:
        ret[Ret.Code] = ErrCode.Param
        ret[Ret.Msg] = ErrMsg.get(ErrCode.Param)
        return ret
    if not notice_type:
        ret[Ret.Code] = ErrCode.Param
        ret[Ret.Msg] = ErrMsg.get(ErrCode.Param)
        return ret
    try:
        if notice_type == "open":
            ret[Ret.Msg] = send_email_after_system_open(fiscal_date)
        elif notice_type == "refresh":
            ret[Ret.Msg] = send_email_after_refresh_esr_on_tuesday(fiscal_date)
        else:
            ret[Ret.Msg] = "Unsupported type"
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    return ret
