import json
import numpy
from functools import wraps

from flask import Flask, Response, jsonify, request, Blueprint, make_response, send_file, g

from data.mono_user_data import FastMonoUser
from domain.dashboard.impl.channel_compliance import RTM_PERMISSION_TO_DB_QUERY_RTM_MAPPING
from middleware import AppleConnectMiddleware, InvalidAPIUsage
from util.conf import logger, cache
from util.const import *
from util.util import env_dev, env_pre_prod
from data.user_data import *
from data.fast_lite_sold_to_auth import TblRtmSoldToAuth
from data.dev_user_data import DevSpecialUser, SpecialUser
from util.scheduler_config import Config, scheduler
from svc_monitor.monitoring import setup_monitoring


class XEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, numpy.ndarray):
            return obj.tolist()
        elif isinstance(obj, numpy.float):
            return float(obj)
        elif isinstance(obj, numpy.int):
            return int(obj)
        
        return json.JSONEncoder.default(self, obj)

class MLResponse(Response):
    @classmethod
    def force_type(cls, response, environ=None):
        if isinstance(response, (list, dict)):
            response = jsonify(response)
        return super(Response, cls).force_type(response, environ)

server = Flask(__name__)
server.response_class = MLResponse
server.wsgi_app = AppleConnectMiddleware(server.wsgi_app)
cache.init_app(server)

# 添加监控
setup_monitoring(server, 'fast-lite-server')

if not env_pre_prod():
    server.config.from_object(Config())
    scheduler.init_app(server)
    scheduler.start()

@server.errorhandler(InvalidAPIUsage)
def invalid_api_usage(e):
    logger.exception(e)
    return jsonify(e.to_dict())

@server.before_first_request
def before_first_request():
    server.wsgi_app = AppleConnectMiddleware(server.wsgi_app)
    cache.init_app(server)


def get_login_person_name(request) -> tuple[str, str]:
    prsid = request.headers.get('Shield-Ds-Prsid')
    if prsid is None:
        if env_dev() or request.args.get('is_debug', "") == "1":
            prsid = request.args.get('person_id', '')
            if prsid == '' and request.json is not None:
                prsid = request.json.get('person_id', '')
    user = TblUserInfo.get_by_id(prsid)
    if not isinstance(user, TblUserInfo):
        return '', ''
    return user.person_id, f'{user.nick} {user.last_name}'


def get_login_person(request) -> tuple[str, TblUserInfo]:
    prsid = request.headers.get('Shield-Ds-Prsid')
    if prsid is None:
        if env_dev() or request.args.get('is_debug', "") == "1":
            prsid = request.args.get('person_id', '')
            if prsid == '' and request.json is not None:
                prsid = request.json.get('person_id', '')
    user = TblUserInfo.get_by_id(prsid)
    if not isinstance(user, TblUserInfo):
        return prsid, None
    return user.person_id, user

def format_request_date(start_date: Optional[str], end_date: Optional[str]) -> tuple[str, str]:
    if end_date is None or end_date == '' or start_date is None or start_date == '':
        end_date = datetime.today().strftime(DateStrfDay)
        start_date = end_date
    try:
        datetime.strptime(end_date, DateStrfDay)
    except:
        raise ErrorExcept(ErrCode.Param, 'date error. ')
    return start_date, end_date


def get_authorized_sold_to(person_id, rtm: str):
    role_name_list = TblUserRole.get_rolename_by_user_platform(f"FAST {rtm}", person_id)
    role_name = []
    for i in role_name_list:
        if len(i)>0:
            role_name.append(i[0])
    ret = []
    role = None
    if StrRolePlanningTeam in role_name:
        # 可以查看所有的sold_to
        ret = ['All']
        role = StrRolePlanningTeam
    elif StrRoleExpert in role_name:
        # 查询不同rtm对应的sold_to list
        ret = TblRtmSoldToAuth.get_sold_to_auth(rtm, person_id)
        role = StrRoleExpert
    return ret, role


def shield_person(r):
    def decorator(f):
        @wraps(f)
        def wrapper(*args, **kwargs):
            # before request
            prs_id = r.headers.get(ShieldPrsId)
            prs_type_code = r.headers.get(ShieldPrsTypeCode)
            if (prs_id is None) or (prs_type_code is None):
                return {
                    Ret.Code: ErrCode.Permissions,
                    Ret.Msg: 'Need Apple Connect'
                }
            if prs_type_code in ["1", "2", "3", "4", "6"]:
                # expert
                g.user_type = UserType.Expert
                g.prs_id = prs_id
                g.user_name = r.headers.get(ShieldPrsNickName)
                paths = r.path.split("/")
                rtm = {
                    "multi": StrRTMMulti,
                    "online": StrRTMOnline,
                    "carrier": StrRTMCarrier,
                }.get(paths[2], "default")
                sold_to_names, role = get_authorized_sold_to(prs_id, rtm)
                g.sold_to_names = sold_to_names
                if env_dev() and role == StrRoleExpert:
                    # fake expert permission for dev
                    fake_id = DevSpecialUser.get_fake_prs_id(prs_id, rtm)
                    if fake_id is not None:
                        g.prs_id = fake_id
                        sold_to_names, _ = get_authorized_sold_to(fake_id, rtm)
                        g.sold_to_names = sold_to_names
                user = FastMonoUser.find_by_prs_id(prs_id)
                if user is not None:
                    g.rtm = user.rtm
                    g.sold_to_id = int(user.reseller_id)
            else:
                # external_user
                g.user_type = UserType.External
                g.prs_id = prs_id
                user = FastMonoUser.find_by_prs_id(prs_id)
                new_user = SpecialUser(
                    prs_id=prs_id,
                    email_address=r.headers.get(ShieldPrsEmail),
                    prs_type_code=prs_type_code,
                )
                new_user.insert()
                if user is None:
                    return {
                        Ret.Code: 401,
                        Ret.Msg: 'no permission',
                    }
                else:
                    g.rtm = user.rtm
                    g.sold_to_id = int(user.reseller_id)
            # end before request
            result = f(*args, **kwargs)
            # after request
            if type(result) == dict and result[Ret.Code] == ErrCode.System:
                logger.exception(result[Ret.Msg])
                if env_dev():
                    result["error"] = result[Ret.Msg]
                result[Ret.Msg] = ErrMsg.get(ErrCode.UnknownError)
            # end after request
            return result
        return wrapper
    return decorator


# 检查某一platform下多权限并存的装饰器: 数据权限
def person_platform_data_permission(http_request, platform):
    def decorator(function):
        @wraps(function)
        def wrapper(*args, **kwargs):
            # before request
            person_id = http_request.headers.get(ShieldPrsId)
            if person_id is None:
                return {
                    Ret.Code: ErrCode.Permissions,
                    Ret.Msg: 'Need Apple Connect'
                }

            # get role platform
            platform_role_name_list = TblUserRole.get_rolename_by_user_platform(platform=platform, user=person_id)
            role_names = [
                platform_role_item.role_name
                for platform_role_item in platform_role_name_list if platform_role_item.role_name
            ]
            if ALL in role_names:
                g.platform_role_names = list(RTM_PERMISSION_TO_DB_QUERY_RTM_MAPPING.keys())
                g.all_platform_role_names = True
            else:
                g.platform_role_names = role_names
                g.all_platform_role_names = False

            # dispatch request view
            result = function(*args, **kwargs)
            return result
        return wrapper
    return decorator
