from .api_common import *
from service.cpf_overview_service import *
from util.util_operation import insert_operate_record


bp = Blueprint('cpf_overview_api', __name__, url_prefix=UrlPrefix)


@bp.route('/allocation/demand_collection/overview/confirm', methods=['POST'])
def check_wednesday_esr():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        query_params = request.args
        fiscal_week_year = int(query_params.get(StrFiscalWeekYear))
    except ValueError:
        ret[Ret.Code] = ErrCode.Param
        ret[Ret.Msg] = f'{StrFiscalWeekYear} must be integer'
        return ret
    try:
        operator = request.headers.get(ShieldPrsNickName) + " " + request.headers.get(ShieldPrsLastName)
        ret[Ret.Data] = confirm_all_rtm_service(fiscal_week_year, operator)
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)

    return ret

@bp.route("/allocation/demand_collection/overview/download")
def cpf_donwload_demand_with_tags_file():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    
    try:
        query_params = request.args
        fiscal_week_year = int(query_params.get(StrFiscalWeekYear))
    except ValueError:
        ret[Ret.Code] = ErrCode.Param
        ret[Ret.Msg] = f'{StrFiscalWeekYear} must be integer'
        return ret
    file_path, file_name = download_all_demand_with_tags_service(fiscal_week_year)
    operator = request.headers.get(ShieldPrsNickName) + " " + request.headers.get(ShieldPrsLastName)
    insert_operate_record(None, AllocationOperateCategory.OverviewDownload, file_name, file_path, operator, None)
    try:
        response = make_response(send_file(file_path, as_attachment=True))
        response.headers["Content-Disposition"] = f'attachment; filename="{file_name}"'
    except FileNotFoundError as e:
        ret[Ret.Code] = ErrCode.System
        ret[Ret.Msg] = str(e)
        return ret
    return response


@bp.route("/allocation/demand_collection/overview/data_source")
def get_current_week_data_source_list():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        query_params = request.args
        fiscal_week_year = int(query_params.get(StrFiscalWeekYear))
    except ValueError:
        ret[Ret.Code] = ErrCode.Param
        ret[Ret.Msg] = f'{StrFiscalWeekYear} must be integer'
        return ret    

    try:
        ret["data"] = {"list": get_overview_data_source_by_week(fiscal_week_year)}
    except Exception as e:
        ret[Ret.Code] = ErrCode.System
        ret[Ret.Msg] = str(e)

    return ret
