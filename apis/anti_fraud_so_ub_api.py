from apis.api_common import *
from data.databend.dashboard.daily_ub_tracking_summary import DailyUbTrackingSummaryDi
from data.databend.dashboard.weekly_ub_tracking_summary import WeeklyUbTrackingSummaryDi
from task_kit.worker.antifraud_so_ub_email_worker import convert_daily, convert_week

bp = Blueprint("anti_fraud", __name__, url_prefix=UrlPrefixUBVelocity)


@bp.route('/anti_fraud/daily_view')
def anti_fraud_daily_view():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: 'Success'}
    try:
        fiscal_dt = request.args.get("fiscal_dt", "2024-09-11", str)
        refresh_time = datetime.now()
        data = DailyUbTrackingSummaryDi.query_daily_ub_summary_records(fiscal_dt)
        r_data = convert_daily(data)
        ret[Ret.Data] = {
            "refresh_time": refresh_time,
            "detail": r_data,
        }
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e.err_msg, exc_info=True)
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(str(e), exc_info=True)
    return ret


@bp.route('/anti_fraud/week_view')
def anti_fraud_week_view():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: 'Success'}
    try:
        fiscal_dt = request.args.get("fiscal_dt", "2024-09-12", str)
        fiscal_week = request.args.get("fiscal_week")
        refresh_time = datetime.now()

        data = WeeklyUbTrackingSummaryDi.query_week_ub_summary_records(snapshot_date=fiscal_dt, fiscal_week=fiscal_week)
        r_data = convert_week(data)
        ret[Ret.Data] = {
            "refresh_time": refresh_time,
            "detail": r_data,
        }
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e.err_msg, exc_info=True)
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(str(e), exc_info=True)
    return ret

