import traceback
import uuid
from hashlib import md5

import numpy as np
import pandas as pd
from concurrent.futures import ThreadPoolExecutor

from apis.api_common import *
from data.databend.end_to_end.ml_fcst_quantile import MLFcstQuantile
from data.mysqls.demand.demand_y_setting import IdealDemandYValueSetting
from data.mysqls.end_to_end.range_level_setting import RangeLevelSetting
from domain.demand.entity.const import SELL_IN_DEMAND, DEMANDS, RTM_MONO, RTMS
from domain.demand.entity.module_switch_enum import ModuleSwitchEnum
from domain.demand.entity.common_menu import Menu
from domain.demand.impl.carrier_mpn import replace_rp_mpn_to_carrier_mpn
from domain.demand.impl.demand_rtm_sales_forecast import (
    get_rtm_sales_forecast_file, upload_rtm_sales_fcst_file,
    get_rtm_sales_forecast_template_file, delete_rtm_sales_fcst_file,
    rtm_publish_fcst_x, upload_origin_rtm_sales_fcst_file, check_ml_fcst_exist)
from domain.demand.impl.demand_x import get_ideal_demand_x, get_rtm_sales_forecast_info, get_x_menu, update_x_setting
from domain.demand.impl.sellin_woi_setting import get_woi_menu, get_submit_flag, query_woi_settings, \
    batch_update_woi_setting, publish_final_demand_woi, publish_finalized_demand_woi
from domain.demand.impl.state_machine import StateProxy
from domain.demand.impl.y_value_setting import query_y_setting, update_y_setting, get_final_menu_for_y, get_y_menu, \
    re_calculate
from domain.end_to_end.impl.ml_fcst_quantile_impl import match_forecast_with_config
from domain.supply.entity import Lob
from data.cpf_data_source import OdsFastCPFSoldToMappingIPhone
from data.databend.gmacc_sold_to_list import GMACCSoldtoList
from data.cpf_data_source import OdsFastCPFActiveSKULob
from kit.custom_date.custom_week_date import CustomWeekDate
from kit.validator.customer_rule import AllowedRule, PositiveIntegerRangeRule
from kit.validator.file import FileValidator, raise_upload_error
from domain.demand.entity.const import RTM_FORECAST_TEMPLATE_DICT as RTM_FORECAST_DEMAND_TEMPLATE_DICT
from util.file_util import convert_file_path, get_file_path, get_absolute_path
from util.send_email import send_email_by_database
from util.const import EmailCmd
from data.datasource_data import DataSourceFile
from util.util import remove_file

bp = Blueprint('demand_ideal', __name__, url_prefix=UrlPrefixdemand)


@bp.route('/ideal_demand/menu_for_x')
def get_ideal_demand_menu_for_x():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: 'Success'}
    try:
        fiscal_week = request.args.get("fiscal_week", "", str)
        channel = request.args.get("channel", "CP&F", str)

        # 必填校验
        if not all([fiscal_week, channel]):
            raise ErrorExcept(ErrCode.Param, "please check params, required parameter missing.")

        region = "China mainland"  # znh: 这里先暂时只查region为 China mainland, 避免歧义
        # 1. 非CP&F, channel就是查询指定rtm下的数据
        rtm = None
        if channel != 'CP&F':
            rtm = channel
        query_kwargs = {
            "fiscal_week": fiscal_week,
            "region": region,
            "rtm": rtm
        }
        menu = Menu(query_func=get_x_menu, rtm=rtm, query_kwargs=query_kwargs)
        ret[Ret.Data] = menu.get_result_menu()

    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e.err_msg, exc_info=True)
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(str(e), exc_info=True)
    return ret


@bp.route('/ideal_demand/menu_for_y')
def get_ideal_demand_menu_for_y():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: 'Success'}
    try:
        fiscal_week = request.args.get("fiscal_week", "", str)
        channel = request.args.get("channel", "CP&F", str)
        # 必填校验
        if not all([fiscal_week, channel]):
            raise ErrorExcept(ErrCode.Param, "please check params, required parameter missing.")

        region = "China mainland"  # znh: 这里先暂时只查region为 China mainland, 避免歧义
        # 1. 非CP&F, channel就是查询指定rtm下的数据
        rtm = None
        if channel != 'CP&F':
            rtm = channel
        query_kwargs = {
            "fiscal_week": fiscal_week,
            "region": region,
            "rtm": rtm
        }
        menu = Menu(query_func=get_y_menu, rtm=rtm, query_kwargs=query_kwargs)
        menu_data = menu.get_result_menu(["lob", "sub_lob", "nand", "color"])
        final_menu = get_final_menu_for_y(fiscal_week=fiscal_week, menu_y=menu_data)
        final_menu = remove_all_rtm(final_menu)
        ret[Ret.Data] = final_menu

    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e.err_msg, exc_info=True)
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(str(e), exc_info=True)
    return ret

def remove_all_rtm(data):
    for region in data.get('regions', []):
        # 去除rtms中rtm为'All'的数据
        region['rtms'] = [rtm for rtm in region['rtms'] if rtm.get('rtm') != 'All']
    return data

@bp.route('/ideal_demand/query_y_setting')
def ideal_demand_query_y_setting():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: 'Success'}
    try:
        query_params = request.args
        fiscal_week = query_params.get("fiscal_week", "", str)
        setting_by = query_params.get("setting_by", "", str)
        region = query_params.get("region", "", str)
        high_low_runner = query_params.get("high_low_runner", "", str)
        sold_to_id = query_params.get("sold_to_id", "", str)
        nand = query_params.get("nand", "", str)
        color = query_params.get("color", "", str)
        # 必填校验
        if not all([fiscal_week, setting_by, region]):
            raise ErrorExcept(ErrCode.Param, "please check params, required parameter missing.")
        rtm = query_params.get("rtm", "", str)
        if rtm == 'All':
            rtm = ''
        sub_rtm = query_params.get("sub_rtm", "", str)
        if sub_rtm == 'All':
            sub_rtm = ''
        if sold_to_id == 'All':
            sold_to_id = ''
        if nand == 'All':
            nand = ''
        if color == 'All':
            color = ''
        ret[Ret.Data] = query_y_setting(fiscal_week=fiscal_week,
                                        region=region,
                                        high_low_runner=high_low_runner,
                                        rtm=rtm,
                                        sub_rtm=sub_rtm,
                                        sold_to_id=sold_to_id,
                                        nand=nand,
                                        color=color)
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e.err_msg, exc_info=True)
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(str(e), exc_info=True)
    return ret


@bp.route('/ideal_demand/update_y_setting', methods=['POST'])
def ideal_demand_update_y_setting():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: 'Success'}
    try:
        body: dict = request.json
        fiscal_week = body.get("fiscal_week")
        settings = body.get("settings")
        # 必填校验
        if not all([fiscal_week, settings]):
            raise ErrorExcept(ErrCode.Param, "please check params, required parameter missing.")
        operator = request.headers.get(ShieldPrsNickName, "") + " " + request.headers.get(ShieldPrsLastName, "")
        logger.info(f"start update y_setting by {operator}, fiscal_week: {fiscal_week}")
        update_y_setting(fiscal_week, settings)
        logger.info(f"finish update y_setting by {operator}, fiscal_week: {fiscal_week}")
        ret[Ret.Data] = {}
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e.err_msg, exc_info=True)
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(str(e), exc_info=True)
    return ret


@bp.route('/ideal_demand/publish_y_setting', methods=['POST'])
def ideal_demand_publish_y_setting():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: 'Success'}
    try:
        body: dict = request.json
        fiscal_week = body.get("fiscal_week")
        # 必填校验
        if not fiscal_week:
            raise ErrorExcept(ErrCode.Param, ErrMsg.get(ErrCode.Param))
        operator = request.headers.get(ShieldPrsNickName, "") + " " + request.headers.get(ShieldPrsLastName, "")
        logger.info(f"start publish y_setting by {operator}, fiscal_week: {fiscal_week}")

        # 1、更新本周cpf y setting的publish状态字段，暂时没有用到本字段，所以先忽略所有的错误。
        try:
            IdealDemandYValueSetting.update_publish_status_by_fiscal_week(fiscal_week)
        except Exception as e:
            logger.error(f"cpf publish y failed {str(e)} in week {fiscal_week}")

        # 2、将状态机status由0改为waiting for rtm setup
        state_proxy = StateProxy(fiscal_week, "ideal_demand")
        current_state = state_proxy.do_cpf_publish_y()
        ret[Ret.Data] = current_state.format() if current_state else state_proxy.current_state().format()
        # 3 可能触发重新计算
        re_calculate(fiscal_week, DEMANDS)
        logger.info(f"finish publish y_setting by {operator}, fiscal_week: {fiscal_week}")

    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e.err_msg, exc_info=True)
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(str(e), exc_info=True)
    return ret


@bp.route('/rtm_sales_forecast')
def get_rtm_sales_forecast():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: 'Success'}
    try:
        fiscal_week = request.args.get("fiscal_week", "", str)
        channel = request.args.get("channel", "CP&F", str)
        if not all([fiscal_week, channel]):
            raise ErrorExcept(ErrCode.Param, "please check params, required parameter missing.")

        ret[Ret.Data] = get_rtm_sales_forecast_info(fiscal_week=fiscal_week, channel=channel)
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e.err_msg, exc_info=True)
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(str(e), exc_info=True)
    return ret


@bp.route('/query_x')
def query_x():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: 'Success'}
    try:
        query_params = request.args
        fiscal_week = query_params.get("fiscal_week", "", str)
        high_low_runner = query_params.get("hr_lr_type", "", str)
        region = query_params.get("region", "China mainland", str)
        rtm = query_params.get("rtm", "", str)
        sub_rtm = query_params.get("sub_rtm", "", str)
        channel = query_params.get("channel", "CP&F", str)

        if not all([fiscal_week, region, channel]):
            raise ErrorExcept(ErrCode.Param, "please check params, required parameter missing.")

        if rtm == 'All':
            rtm = ''
        if sub_rtm == 'All':
            sub_rtm = ''

        # 1. 非CP&F, channel就是查询指定rtm下的数据
        if channel != 'CP&F' and not rtm:
            rtm = channel

        ret[Ret.Data] = get_ideal_demand_x(fiscal_week=fiscal_week,
                                           high_low_runner=high_low_runner,
                                           region=region,
                                           rtm=rtm,
                                           channel=channel,
                                           sub_rtm=sub_rtm)

    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e.err_msg, exc_info=True)
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(str(e), exc_info=True)
    return ret


@bp.route('/ideal_demand/update_x_setting', methods=['POST'])
def ideal_demand_update_x_setting():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: 'Success', Ret.Data: {}}
    try:
        body: dict = request.json
        fiscal_week = body.get("fiscal_week")
        region = body.get("region", "China mainland")
        lob = body.get("lob", "iPhone")
        settings = body.get("settings")
        # 必填校验
        if not all([fiscal_week, settings]):
            raise ErrorExcept(ErrCode.Param, "please check params, required parameter missing.")
        operator = request.headers.get(ShieldPrsNickName, "") + " " + request.headers.get(ShieldPrsLastName, "")
        logger.info(f"start update x_setting by {operator}, fiscal_week: {fiscal_week}")
        update_x_setting(fiscal_week, settings, region, lob)
        logger.info(f"finish update x_setting by {operator}, fiscal_week: {fiscal_week}")

    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e.err_msg, exc_info=True)
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(str(e), exc_info=True)
    return ret


@bp.route("/rtm_sales_forecast/upload", methods=["POST"])
def upload_rtm_sales_forecast_file():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success',
        Ret.Data: {}
    }
    try:
        if not CustomWeekDate(ModuleSwitchEnum.SALES_FORECAST_OPERATE_DEADLINE.value).is_before_ddl():
            raise ErrorExcept(ErrCode.FileUploadError, "已经超过上传截止时间，不允许上传Forecast文件")

        query_params = request.args
        channel = query_params.get('channel')
        fiscal_week = query_params.get('fiscal_week')

        # check params
        if not all([channel, fiscal_week]):
            raise ErrorExcept(ErrCode.Param, "please check params, required parameter missing.")

        file = request.files.get("file")
        headers = request.headers
        uploader = headers.get(ShieldPrsNickName, "") + " " + headers.get(ShieldPrsLastName, "")
        uploader_email = request.headers.get(ShieldPrsEmail, "")
        if not uploader.strip():
            raise ErrorExcept(ErrCode.Param, "uploader is empty.")
        logger.info(
            f"rtm sales forecast-->upload: channel:{channel}, fiscal_week:{fiscal_week}, uploader:{uploader} start upload file")

        file_name = f"iPhone_{channel}_Sales_Forecast_{fiscal_week}.xlsx"

        # 校验文件
        merged_df = validate_rtm_fcst_file(channel, file, uploader, fiscal_week)

        # url, df = save_file(file, uploader, channel, fiscal_week)

        # upload
        upload_time = upload_rtm_sales_fcst_file(merged_df, fiscal_week, uploader, channel)
        logger.info(
            f"rtm sales forecast-->upload: channel:{channel}, fiscal_week:{fiscal_week}, uploader:{uploader} upload file successfully")
        ret[Ret.Data] = {
            "file_name": file_name,
            "upload_by": uploader,
            "upload_time": upload_time
        }
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e.err_msg, exc_info=True)
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(str(e), exc_info=True)
    return ret


def save_file(file, uploader, channel, fiscal_week):
    date_str = datetime.now().strftime("%Y-%m-%d")
    common_file_path = f"sales_forecast/{date_str}"
    file_path = get_absolute_path(f'/uploads/{common_file_path}')
    dir_name = os.path.dirname(file_path)
    if not os.path.exists(dir_name):
        os.makedirs(dir_name)
    if not os.path.exists(file_path):
        os.mkdir(file_path)
    file_md5 = md5(file.stream.read()).hexdigest()

    # 路径中增加日期，方便之后查看和删除对应的EFS文件
    secure_file_name = file_md5 + '.xlsx'
    efs_url = get_efs_url(f"{common_file_path}/{secure_file_name}")
    save_path = f"{file_path}/{secure_file_name}"

    save_file_info(file, file.filename, uploader, channel, fiscal_week, efs_url)
    file.stream.seek(0)
    file.save(save_path)
    df = pd.read_excel(save_path)
    upload_origin_rtm_sales_fcst_file(df, fiscal_week, uploader)
    return efs_url, df


def get_efs_url(file_path: str):
    efs_host = "https://gcsales.expert.apple.com/"
    if env_dev():
        efs_host = "https://gcsales.expert-dev.apple.com/"
    return f"{efs_host}static/display/fast-lite/{file_path}"


def save_file_info(file, file_name, uploader, rtm, fiscal_week, url):
    file_info = DataSourceFile()
    file_info.rtm = rtm
    file_info.datasource_type = DataSourceFileType.SalesForecast
    file_info.filesize = file.content_length
    file_info.content_type = file.content_type
    file_info.upload_date = fiscal_week
    file_info.upload_by = uploader
    file_info.url = url
    version = DataSourceFile.query_count_by_rtm_and_type(rtm, DataSourceFileType.SalesForecast)
    file_info.version = version
    file_info.filename = file_name
    file_info.status = DataSourceFileStatus.Enabled
    file_info.save()
    return url


# 将文件校验提到API层，可后续进行优化校验
def validate_rtm_fcst_file(rtm: str, file, operator: str, fiscal_week: str):
    # 读取文件
    df = pd.read_excel(file)
    # 表头校验, 必须与模版文件的表头一致
    if not list(df.columns) == list(RTM_FORECAST_DEMAND_TEMPLATE_DICT.keys()):
        raise ErrorExcept(ErrCode.FileUploadError,
                          "Invalid data fields in the table, please follow the template and don't not change any field.")

    rules = {
        "RTM": ["required", AllowedRule([rtm])],
        "CW": ["required", PositiveIntegerRangeRule(0, None)],
        "CW+1": ["required", PositiveIntegerRangeRule(0, None)],
        "CW+2": ["required", PositiveIntegerRangeRule(0, None)],
        "CW+3": ["required", PositiveIntegerRangeRule(0, None)],
        "CW+4": ["required", PositiveIntegerRangeRule(0, None)],
        "CW+5": ["required", PositiveIntegerRangeRule(0, None)],
        "CW+6": ["required", PositiveIntegerRangeRule(0, None)],
    }
    validator = FileValidator(df, rules=rules)
    has_pass_validation, row, errors = validator.do_validate()
    # 返回文件校验的错误信息
    priority_validation = ["RTM", "CW", "CW+1", "CW+2", "CW+3", "CW+4", "CW+5", "CW+6"]
    FileValidator.handle_error_msg(has_pass_validation, errors, priority_validation)

    # 特殊处理，需要把文件中的sold-to-id改为字符串类型
    df["Sold-to ID"] = df["Sold-to ID"].astype(str)

    # 判断文件是否没有数据
    if df.empty:
        raise ErrorExcept(ErrCode.FileUploadError, "The file is empty, please check and upload again.")

    # 在sold-to id & mpn的颗粒度上，不允许出现重复的数据行
    is_duplicate = df.duplicated(subset=['Sold-to ID', 'MPN'], keep=False)
    duplicate_rows = df.loc[is_duplicate].index.tolist()
    if duplicate_rows:
        raise_upload_error("The following data row(s) are invalid for duplicated values: ", duplicate_rows)

    pool = ThreadPoolExecutor(max_workers=4)
    datasource_sold_to_pool = pool.submit(OdsFastCPFSoldToMappingIPhone.get_sold_to_id, rtm)
    gmacc_sold_to_pool = pool.submit(GMACCSoldtoList.get_distinct_sold_to)
    rtm_business_type_pool = pool.submit(OdsFastCPFSoldToMappingIPhone.get_rtm_business_type)
    lob_sub_lob_mpn_pool = pool.submit(OdsFastCPFActiveSKULob.list_lob_sub_lob_mpn, Lob.IPHONE.value)
    valid_datasource_sold_to_id_list = datasource_sold_to_pool.result()
    valid_gmacc_sold_to_id_list = gmacc_sold_to_pool.result()
    rtm_business_type_list = rtm_business_type_pool.result()
    lob_sub_lob_mpn_list = lob_sub_lob_mpn_pool.result()
    pool.shutdown()

    # RTM/ Business Type validate
    not_in_tuple = ~df[['RTM', 'Sub-RTM']].apply(lambda x: tuple(x) in rtm_business_type_list, axis=1)
    invalid_rtm_business_type_rows = df.loc[not_in_tuple].index.tolist()
    if invalid_rtm_business_type_rows:
        raise_upload_error(
            "The followings row(s) are invalid for wrong RTM / Business Type name or mapping relationship: ",
            invalid_rtm_business_type_rows)

    # 产品纬度列合法性校验, 如用户上传的LOB / Sub-LOB / MPN不在SKU List范围内
    # sub_lob_mpn_list = [tuple(item) for item in sub_lob_mpn_list]
    invalid_sub_lob_mpn = ~df[['LOB', 'Sub-LOB', 'MPN']].apply(lambda x: tuple(x) in lob_sub_lob_mpn_list, axis=1)
    invalid_sub_lob_mpn_rows = df.loc[invalid_sub_lob_mpn].index.tolist()
    if invalid_sub_lob_mpn_rows:
        raise_upload_error("The following data row(s) are invalid for wrong LOB / Sub-LOB / MPN: ",
                           invalid_sub_lob_mpn_rows)

    # Sold-to ID合法性检查和补充
    # Step 1 - 通过GMACC数据表校验表格内Sold-to ID列数值，如用户上传的Sold-to ID不在GMACC范围内，需报错提示
    # 先在sold-to mapping list中查看，不存在的再查看GMACC，依然不存在则报错，即两个集合的并集
    valid_soldto_list = list(set(valid_gmacc_sold_to_id_list).union(set(valid_datasource_sold_to_id_list)))
    condition_gamcc = ~df['Sold-to ID'].isin(valid_soldto_list)
    invalid_sold_to_gamcc_rows = df.loc[condition_gamcc].index.to_list()
    if invalid_sold_to_gamcc_rows:
        raise_upload_error("The following data row(s) are invalid for wrong Sold-to ID: ", invalid_sold_to_gamcc_rows)

    # Step 2 - 如用户上传的Sold-to ID在GMACC范围内，但尚未包括在Data Source - Sold-to Mapping List - iPhone中，则需对原Sold-to Mapping List的数据行进行补充，并自动同步更新。
    # 如果在GMACC中（A），但不在sold-to mapping list（B）中，即A差B
    # 特殊逻辑
    try:
        new_soldto_list = list(set(valid_gmacc_sold_to_id_list) - set(valid_datasource_sold_to_id_list))
        condition = df['Sold-to ID'].isin(new_soldto_list)
        new_sold_to_df = df.loc[condition]
        if not new_sold_to_df.empty:
            logger.info(f'有新的sold-to {new_sold_to_df}')
            # 需要把这些行中的数据插入到数据库中并发送邮件通知
            save_sold_to_data(new_sold_to_df, operator, rtm)
    except (Exception, ErrorExcept) as e:
        logger.error(traceback.format_exc())
        # 邮件通知
        send_email_by_database(EmailCmd.RTMFcstErrorEmail, params={
            "rtm": rtm,
            "error": f"新sold-to数据保存失败, error: {traceback.format_exc()}"
        })

    # 校验上传的Sold-to & MPN组合是否包含在当周ML Forecast结果内，如果是，则报错
    df, has_ml_fcst = check_ml_fcst_exist(df, fiscal_week)
    if has_ml_fcst:
        raise ErrorExcept(ErrCode.FileUploadError,
                          "Duplicated forecast records in the table, please follow the coverage of template and do not submit same Sold-to & MPN records that have already been covered by ML model.")
    return df


def validate_forecast_range(fiscal_week: str, rtm, df: pd.DataFrame):
    # 查询 forecast_range_df 数据
    # forecast_range_df = ForecastDemand.query_by_fiscal_qtr_week_name(fiscal_week)
    #
    # # 将字段统一命名（避免拼写问题）
    # forecast_range_df.rename(columns={'sold_to_id': 'Sold-to ID', 'mpn_id': 'MPN'}, inplace=True)
    # # 将forecast_range_df按照 'Sold-to ID' 和 'MPN' 去重
    # forecast_range_df.drop_duplicates(subset=['Sold-to ID', 'MPN'], inplace=True)
    is_cut = False
    if rtm not in RTMS:
        return is_cut, df

    forecast_df = MLFcstQuantile.get_download_data(fiscal_week)
    user_variance_settings = RangeLevelSetting.get_settings(fiscal_week_name=fiscal_week, quantile_is_published=1)

    # 处理数据
    result_list = match_forecast_with_config(forecast_df, user_variance_settings)
    if result_list is None or len(result_list) == 0:
        return is_cut, df
    forecast_range_df = pd.DataFrame(result_list)
    forecast_range_df.rename(columns={'sold-to id': 'Sold-to ID'}, inplace=True)

    if not forecast_range_df.empty:
        replace_rp_mpn_to_carrier_mpn(forecast_range_df, 'MPN', "RTM")

    # 合并两个 DataFrame，按 'Sold-to ID' 和 'MPN' 匹配
    merged_df = pd.merge(df, forecast_range_df, on=['RTM', 'Sub-RTM', 'LOB', 'Sub-LOB', 'Sold-to ID', 'MPN'], how='left')

    # 初始化问题消息列表
    messages = []
    rows = []

    # 验证每一列 (CW, CW+1, ..., CW+4) 是否超出范围
    for i in range(5):
        cw_col = f'CW+{i}' if i > 0 else 'CW'
        forecast_min_col = f'quantile_min_cw{i}' if i > 0 else 'quantile_min_cw'  # 对应的最小列
        forecast_max_col = f'quantile_max_cw{i}' if i > 0 else 'quantile_max_cw' # 对应的最大列

        # 将 forecast_min/forecast_max 列转换为数值，None 转为 NaN
        min_values = pd.to_numeric(merged_df[forecast_min_col], errors='coerce')
        max_values = pd.to_numeric(merged_df[forecast_max_col], errors='coerce')

        # 当对应值不为 NaN 时再进行比较
        mask_min = min_values.notna() & (merged_df[cw_col] < min_values)
        mask_max = max_values.notna() & (merged_df[cw_col] > max_values)

        # 如果存在满足条件的行，则记录is_cut为True
        if mask_min.any() or mask_max.any():
            is_cut = True

        # 对于cw_col小于forecast_min_col的行，将cw_col设置为forecast_min_col的值
        merged_df.loc[mask_min, cw_col] = merged_df.loc[mask_min, forecast_min_col]

        # 对于cw_col大于forecast_max_col的行，将cw_col设置为forecast_max_col的值
        merged_df.loc[mask_max, cw_col] = merged_df.loc[mask_max, forecast_max_col]
    # 保留merged_df中表头为RTM_FORECAST_TEMPLATE_DICT key的列
    merged_df = merged_df[RTM_FORECAST_DEMAND_TEMPLATE_DICT.keys()]
    return is_cut, merged_df


# @celery.task(name="save_sold_to_data")
def save_sold_to_data(df: pd.DataFrame, operator: str, rtm: str):
    datas = []
    new_sold_to = []
    # 要对dataframe进行去重并替换nan为None
    df = df.drop_duplicates(subset='Sold-to ID')
    df.replace({np.nan: None}, inplace=True)

    version_basic = OdsFastCPFSoldToMappingIPhone.get_max_version_basic()
    version = version_basic[0].version
    for _, row in df.iterrows():
        datas.append({
            "fiscal_qtr_week_name": version_basic[0].fiscal_qtr_week_name,
            "fiscal_week_year": version_basic[0].fiscal_week_year,
            "upload_by": "System",
            "upload_at": datetime.now(),
            "region": row["Region"],
            "rtm": row["RTM"],
            "business_type": row["Sub-RTM"],
            "sold_to_id": row["Sold-to ID"],
            "sold_to_name": row["Sold-to Name"],
            "sold_to_name_abbre": None,
            "remark": "System",
            "version": version
        })
        new_sold_to.append(row)
    OdsFastCPFSoldToMappingIPhone.bulk_save(datas)

    # 生成新的文件，并将旧文件删除
    datasource_file = DataSourceFile.query_by_rtm_version(StrRTMCPF, DataSourceFileType.CPFSoldToMappingListIphone,
                                                          version)
    # 查询库中的数据，生成新的文件
    new_soldto_df = OdsFastCPFSoldToMappingIPhone.query_data_by_version(version)
    unique_name = uuid.uuid4().hex
    file_path = get_file_path(f"{unique_name}.xlsx")
    new_soldto_df.to_excel(file_path, index=False)
    new_file_path = f"/file/download/{unique_name}"
    DataSourceFile.update_file_path_by_rtm_version(
        StrRTMCPF, DataSourceFileType.CPFSoldToMappingListIphone,
        version, new_file_path
    )
    logger.info(f"{operator} has uploaded {rtm} new sold-to.")

    # 删除旧文件
    if datasource_file:
        remove_file(convert_file_path(datasource_file.url))

    # 邮件通知
    send_email_by_database(EmailCmd.RTMFcstNewSoldtoEmail, params={
        "rtm": rtm,
        "time": datetime.now(),
        "sold_to_data": ", ".join([x['RTM'] + '-' + str(x['Sold-to ID']) + '-' + x['Sold-to Name'] for x in new_sold_to])
    })


@bp.route("/rtm_sales_forecast/download", methods=["GET"])
def download_rtm_sales_forecast_file():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success',
        Ret.Data: None
    }

    channel = request.args.get('channel')
    fiscal_week = request.args.get('fiscal_week')

    try:
        # 必填校验
        if not all([channel, fiscal_week]):
            raise ErrorExcept(ErrCode.Param, "please check params, required parameter missing.")

        file_name, excel_file = get_rtm_sales_forecast_file(rtm=channel, fiscal_week=fiscal_week)
        response = make_response(send_file(excel_file,
                                           mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                                           download_name=file_name,
                                           as_attachment=True))
        response.headers["Content-Disposition"] = f'attachment; filename="{file_name}"'
        return response
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e.err_msg, exc_info=True)
        return ret
    except FileNotFoundError as e:
        logger.error(str(e))
        ret[Ret.Code] = ErrCode.System
        ret[Ret.Msg] = 'No such file or directory.'
        logger.error(str(e), exc_info=True)
        return ret
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(str(e), exc_info=True)
        return ret


@bp.route("/rtm_sales_forecast/download_template", methods=["GET"])
def download_rtm_sales_forecast_template_file():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success',
        Ret.Data: None
    }

    channel = request.args.get('channel')
    fiscal_week = request.args.get('fiscal_week')

    try:
        # 必填校验
        if not all([channel, fiscal_week]):
            raise ErrorExcept(ErrCode.Param, "please check params, required parameter missing.")

        file_name, excel_file, is_empty = get_rtm_sales_forecast_template_file(rtm=channel, fiscal_week=fiscal_week)
        response = make_response(send_file(excel_file,
                                           mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                                           download_name=file_name,
                                           as_attachment=True))
        response.headers["Content-Disposition"] = f'attachment; filename="{file_name}"'
        response.headers["X-Is-Empty"] = str(is_empty)
        return response
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e.err_msg, exc_info=True)
        return ret
    except FileNotFoundError as e:
        logger.error(str(e))
        ret[Ret.Code] = ErrCode.System
        ret[Ret.Msg] = 'No such file or directory.'
        logger.error(str(e), exc_info=True)
        return ret
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(str(e), exc_info=True)
        return ret


@bp.route("/rtm_sales_forecast/delete", methods=["GET"])
def delete_rtm_sales_forecast_file():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success',
        Ret.Data: None
    }
    channel = request.args.get('channel')
    fiscal_week = request.args.get('fiscal_week')
    try:
        # 必填校验
        if not all([channel, fiscal_week]):
            raise ErrorExcept(ErrCode.Param, "please check params, required parameter missing.")

        logger.info(f"rtm sales forecast-->delete: channel:{channel}, fiscal_week:{fiscal_week} ready delete file")
        delete_success = delete_rtm_sales_fcst_file(rtm=channel, fiscal_week=fiscal_week)
        if not delete_success:
            raise ErrorExcept(ErrCode.UnknownError,
                              f"rtm:{channel}, fiscal_week:{fiscal_week}: delete rtm sales forecast failed")
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(str(e), exc_info=True)
    return ret


@bp.route('/ideal_demand/publish_x_and_fcst', methods=["POST"])
def publish_rtm_sales_forecast_data():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: 'Success'}

    fiscal_week = request.json.get('fiscal_week', "")
    channel = request.json.get('channel', "")

    if not fiscal_week or not channel:
        raise ErrorExcept(ErrCode.Param, ErrMsg.get(ErrCode.Param))

    try:
        operator = request.headers.get(ShieldPrsNickName) + " " + request.headers.get(ShieldPrsLastName)
        ret[Ret.Data] = rtm_publish_fcst_x(fiscal_week, channel, operator)
    except Exception as e:
        ret[Ret.Code] = ErrCode.System
        ret[Ret.Msg] = str(e)
        return ret
    return ret


@bp.route('/sellin_demand/menu_for_woi', methods=["GET"])
def menu_for_woi():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: 'Success'}

    fiscal_week = request.args.get('fiscal_week', "")
    woi_type = request.args.get('woi_type')

    if not fiscal_week:
        raise ErrorExcept(ErrCode.Param, ErrMsg.get(ErrCode.Param))

    try:
        query_kwargs = {
            "lob": Lob.IPHONE.value,
            "fiscal_week": fiscal_week
        }
        menu = Menu(query_func=get_woi_menu, query_kwargs=query_kwargs)
        menu_data = menu.get_result_menu(["lob", "sub_lob", "nand", "color", "mpn"])
        menu_data = remove_all_sublob(menu_data)
        ret[Ret.Data] = menu_data
        state_proxy = StateProxy(fiscal_week, SELL_IN_DEMAND)
        ret[Ret.Data]['status'] = state_proxy.current_state().format()
        ret[Ret.Data]['submit_flag'] = get_submit_flag(fiscal_week, woi_type)
    except Exception as e:
        ret[Ret.Code] = ErrCode.System
        ret[Ret.Msg] = str(e)
        return ret
    return ret


def remove_all_sublob(data):
    for sub_lob in data.get('lobs', []):
        # 去除rtms中rtm为'All'的数据
        sub_lob['sub_lobs'] = [rtm for rtm in sub_lob['sub_lobs'] if rtm.get('sub_lob') != 'All']
    return data

def add_nand_color(data):
    for lob in data.get('lobs', []):
        for sub_lob in lob.get('sub_lobs', []):
            sub_lob['nand'] = 'All'
            sub_lob['color'] = 'All'
    return data


@bp.route('/sellin_demand/query_woi_setting', methods=["GET"])
def query_woi_setting():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: 'Success'}

    fiscal_week = request.args.get('fiscal_week')
    woi_type = request.args.get('woi_type')

    if not fiscal_week:
        raise ErrorExcept(ErrCode.Param, ErrMsg.get(ErrCode.Param))

    try:
        ret[Ret.Data] = query_woi_settings(fiscal_week, woi_type)
    except Exception as e:
        logger.error(traceback.format_exc())
        ret[Ret.Code] = ErrCode.System
        ret[Ret.Msg] = str(e)
        return ret
    return ret


@bp.route('/sellin_demand/update_woi_setting', methods=["POST"])
def update_woi_setting():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: 'Success'}

    fiscal_week = request.json.get('fiscal_week')
    settings = request.json.get('settings')
    mpn_settings = settings.get('mpn_settings')
    sublob_settings = settings.get('sublob_settings')

    woi_type = request.json.get('woi_type')

    if not fiscal_week:
        raise ErrorExcept(ErrCode.Param, ErrMsg.get(ErrCode.Param))

    try:
        operator = request.headers.get(ShieldPrsNickName) + " " + request.headers.get(ShieldPrsLastName)
        # mpn_str = json.dumps(mpn_settings, indent=4)
        # sublob_str = json.dumps(sublob_settings, indent=4)
        # obj = {'woi_by_mpn': mpn_str, 'woi_by_sublob': sublob_str, 'fiscal_week': fiscal_week, 'creator': operator,
        #        'editor': operator}
        # SellinDemandWoiSetting.bulk_insert_or_update(obj)
        batch_update_woi_setting(fiscal_week, mpn_settings, sublob_settings, operator, woi_type)
    except Exception as e:
        ret[Ret.Code] = ErrCode.System
        ret[Ret.Msg] = str(e)
        return ret
    return ret


@bp.route('/sellin_demand/publish_woi_setting', methods=["POST"])
def publish_woi_setting():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: 'Success'}

    fiscal_week = request.json.get('fiscal_week', "")
    woi_type = request.json.get('woi_type')

    if not fiscal_week:
        raise ErrorExcept(ErrCode.Param, ErrMsg.get(ErrCode.Param))

    try:
        operator = request.headers.get(ShieldPrsNickName) + " " + request.headers.get(ShieldPrsLastName)
        if WOITypes.FinalDemand.value == woi_type:
            ret[Ret.Data] = publish_final_demand_woi(fiscal_week, operator, woi_type)
        else:
            publish_finalized_demand_woi(fiscal_week, operator, woi_type)
    except Exception as e:
        ret[Ret.Code] = ErrCode.System
        ret[Ret.Msg] = str(e)
        return ret
    return ret

