from apis.api_common import *
from data.mysqls.demand.ideal_demand_cpf_upload_dfa import IdealDemandCPFUploadDFA
from data.mysqls.demand.rtm_sales_forecast_upload import FastLiteRTMSalesForecastUpload
from domain.dashboard.impl.fiscal_week_container import <PERSON>scalWeekContainer
from domain.demand.entity.const import IDEAL_DEMAND, TOPDOWN_DEMAND, SELL_IN_DEMAND, RTMS, FINAL_DEMAND, \
    NORMALIZED_DEMAND, DELTA_DEMAND, All_RTMS
from domain.demand.entity.module_switch_enum import ModuleSwitchEnum
from domain.demand.entity.state import DemandState
from domain.demand.impl.ideal_demand_result import IDEAL_DEMAND_RESULT_FILE, SALES_FINAL_FORECAST_FILE, \
    IdealDemandResult, TOPDOWN_DEMAND_RESULT_FILE, COUNTRY_COMBINED_FORECAST_FILE, TopDownDemandResult, \
    SELL_IN_DEMAND_RESULT_FILE, SellInDemandResult, NORMALIZED_DEMAND_COUNTY_RESULT_FILE, \
    NORMALIZED_DEMAND_RTM_RESULT_FILE, NORMALIZED_DEMAND_FORECAST_FILE, NormalizedDemandResult, SYSTEM, \
    DELTA_DEMAND_RTM_RESULT_FILE, DeltaDemandResult
from domain.demand.impl.state_machine import StateProxy
from domain.demand.impl.y_value_setting import get_demand_fiscal_weeks
from kit.custom_date.custom_week_date import CustomWeekDate

bp = Blueprint('homepage', __name__, url_prefix=UrlPrefixdemand)


@bp.route('/fiscal_weeks')
def get_fiscal_weeks():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: 'Success'}
    try:
        ret[Ret.Data] = {"fiscal_weeks": get_demand_fiscal_weeks(IDEAL_DEMAND)}
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e.err_msg, exc_info=True)
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(str(e), exc_info=True)
    return ret


def is_dfa_not_upload_due_to_timeout(week: str):
    """
    上传了内容 或者 本周上传截止时间内没有上传，则不提示超时，否则都需要提示
    """
    current_week = FiscalWeekContainer().get_current_week()
    is_current_week = True if current_week == week else False
    is_upload_timeout = CustomWeekDate(ModuleSwitchEnum.DFA_OPERATE_DEADLINE.value).is_before_ddl()
    dfa_upload = IdealDemandCPFUploadDFA.query_count_by_week(week)
    return False if dfa_upload or (is_current_week and is_upload_timeout) else True


def is_automatic_generate_rtm_file(week: str, rtms: list):
    rtm_records = FastLiteRTMSalesForecastUpload.query_rtm_records_by_fiscal_week(week, rtms)
    for record in rtm_records:
        if record.uploader == SYSTEM:
            return True
    return False


@bp.route('/homepage')
def get_homepage():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: 'Success'}
    try:
        fiscal_week = request.args.get("fiscal_week", "", str)
        # 必填校验
        if not fiscal_week:
            raise ErrorExcept(ErrCode.Param, "please check params, required parameter missing.")

        ideal_proxy = StateProxy(fiscal_week, IDEAL_DEMAND)
        topdown_proxy = StateProxy(fiscal_week, TOPDOWN_DEMAND)
        sellin_proxy = StateProxy(fiscal_week, SELL_IN_DEMAND)
        normalized_proxy = StateProxy(fiscal_week, NORMALIZED_DEMAND)
        delta_proxy = StateProxy(fiscal_week, DELTA_DEMAND)
        final_demand_proxy = StateProxy(fiscal_week, FINAL_DEMAND)

        ideal_state = ideal_proxy.current_state()
        topdown_state = topdown_proxy.current_state()
        sellin_state = sellin_proxy.current_state()
        normalized_state = normalized_proxy.current_state()
        delta_state = delta_proxy.current_state()
        final_demand_state = final_demand_proxy.current_state()
        # ideal_demand 抽屉文件相关信息构造
        ideal_demand_result_files = []
        if ideal_proxy.is_completed():
            file_categories = [IDEAL_DEMAND_RESULT_FILE, SALES_FINAL_FORECAST_FILE]
            for file_category in file_categories:
                result = IdealDemandResult(fiscal_week, IDEAL_DEMAND, file_category)
                ideal_demand_result_files.append(result.file_info(ideal_proxy.get_state_update_time()))

        # top_down_demand 抽屉文件相关信息构造
        top_down_demand_result_files = []
        if topdown_proxy.is_completed():
            file_categories = [TOPDOWN_DEMAND_RESULT_FILE, COUNTRY_COMBINED_FORECAST_FILE]
            for file_category in file_categories:
                result = TopDownDemandResult(fiscal_week, TOPDOWN_DEMAND, file_category)
                top_down_demand_result_files.append(result.file_info(topdown_proxy.get_state_update_time()))

        # sellin_demand  抽屉文件相关信息构造
        sellin_demand_result_files = []
        if sellin_proxy.is_completed():
            file_categories = [SELL_IN_DEMAND_RESULT_FILE]
            for file_category in file_categories:
                result = SellInDemandResult(fiscal_week, SELL_IN_DEMAND, file_category)
                sellin_demand_result_files.append(result.file_info(result._get_update_time()))
        # dn 信息构造
        normalized_demand_result_files = []
        if normalized_proxy.is_completed():
            file_categories = [NORMALIZED_DEMAND_COUNTY_RESULT_FILE, NORMALIZED_DEMAND_RTM_RESULT_FILE,
                               NORMALIZED_DEMAND_FORECAST_FILE]
            for file_category in file_categories:
                result = NormalizedDemandResult(fiscal_week, NORMALIZED_DEMAND, file_category)
                normalized_demand_result_files.append(result.file_info(normalized_proxy.get_state_update_time()))
        # dd 信息构造
        delta_demand_result_files = []
        if delta_proxy.is_completed():
            file_categories = [DELTA_DEMAND_RTM_RESULT_FILE]
            for file_category in file_categories:
                result = DeltaDemandResult(fiscal_week, DELTA_DEMAND, file_category)
                delta_demand_result_files.append(result.file_info(result._get_update_time()))

        ret[Ret.Data] = {
            IDEAL_DEMAND: {
                "status": ideal_state.format(),
                "sub_progress": ideal_state.ideal_demand_progress(),
                "is_automatic_generate_rtm_file": is_automatic_generate_rtm_file(fiscal_week, RTMS),
                "result": ideal_demand_result_files
            },
            TOPDOWN_DEMAND: {
                "status": topdown_state.format(),
                "sub_progress": topdown_state.topdown_demand_progress(),
                "is_dfa_not_upload_due_to_timeout": is_dfa_not_upload_due_to_timeout(fiscal_week),
                "result": top_down_demand_result_files
            },
            SELL_IN_DEMAND: {
                "status": sellin_state.format(),
                "sub_progress": sellin_state.sellin_demand_progress(),
                "result": sellin_demand_result_files
            },
            NORMALIZED_DEMAND: {
                "status": normalized_state.format(),
                "sub_progress": normalized_state.default_progress(),
                "result": normalized_demand_result_files
            },
            DELTA_DEMAND: {
                # "status": sellin_state.format(),
                # "sub_progress": sellin_state.sellin_demand_progress(),
                # "result": sellin_demand_result_files
                "status": delta_state.format(),
                "sub_progress": delta_state.default_progress(),
                "result": delta_demand_result_files
            },
            FINAL_DEMAND: {
                "status": final_demand_state.format(),
                "sub_progress": final_demand_state.final_demand_progress()
            },
        }

    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e.err_msg, exc_info=True)
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(str(e), exc_info=True)
    return ret


@bp.route('/rtm_homepage')
def get_rtm_homepage():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: 'Success'}
    try:
        fiscal_week = request.args.get("fiscal_week", "", str)
        channel = request.args.get("channel", "", str)
        # 必填校验
        if not fiscal_week or not channel or channel not in All_RTMS:
            raise ErrorExcept(ErrCode.Param, "please check params, required parameter missing.")

        ideal_proxy = StateProxy(fiscal_week, IDEAL_DEMAND)
        final_proxy = StateProxy(fiscal_week, FINAL_DEMAND)
        current_state = ideal_proxy.current_state()
        final_demand_current_state = final_proxy.current_state()
        ideal_rtm_state = ideal_proxy.get_rtm_state(channel)
        ret[Ret.Data] = {
            IDEAL_DEMAND: {
                "origin_rtm_status": ideal_rtm_state,  # 前端不使用，方便调试
                "status": current_state.rtm_state(ideal_rtm_state).format(),
                "is_generated_by_system": is_automatic_generate_rtm_file(fiscal_week, [channel]),
            },
            FINAL_DEMAND: {
                # "status": 0  # 没有rtm的状态
                "status": final_demand_current_state.format() # 没有rtm的状态
            },
        }

    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e.err_msg, exc_info=True)
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(str(e), exc_info=True)
    return ret


@bp.route("/homepage/result/download", methods=["GET"])
def download_ideal_demand_result_file():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success',
        Ret.Data: None
    }
    fiscal_week = request.args.get('fiscal_week', None, str)
    category = request.args.get('category', None, int)

    try:
        # 必填校验
        if fiscal_week is None or category is None:
            raise ErrorExcept(ErrCode.Param, "please check params, required parameter missing.")

        ideal_demand_result = IdealDemandResult(fiscal_week, IDEAL_DEMAND, category)
        excel_file = ideal_demand_result.download()

        file_name = ideal_demand_result.file_info(fiscal_week)["file_name"]
        response = make_response(send_file(excel_file,
                                           mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                                           download_name=file_name,
                                           as_attachment=True))
        response.headers["Content-Disposition"] = f'attachment; filename="{file_name}"'
        return response
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e.err_msg, exc_info=True)
        return ret
    except FileNotFoundError as e:
        logger.error(str(e))
        ret[Ret.Code] = ErrCode.System
        ret[Ret.Msg] = 'No such file or directory.'
        logger.error(str(e), exc_info=True)
        return ret
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(str(e), exc_info=True)
        return ret


@bp.route("/homepage/top_down_result/download", methods=["GET"])
def download_top_down_demand_result_file():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success',
        Ret.Data: None
    }
    fiscal_week = request.args.get('fiscal_week', None, str)
    category = request.args.get('category', None, int)

    try:
        # 必填校验
        if fiscal_week is None or category is None:
            raise ErrorExcept(ErrCode.Param, "please check params, required parameter missing.")

        topdown_demand_result = TopDownDemandResult(fiscal_week, TOPDOWN_DEMAND, category)
        excel_file = topdown_demand_result.download()

        file_name = topdown_demand_result.file_info(fiscal_week)["file_name"]
        response = make_response(send_file(excel_file,
                                           mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                                           download_name=file_name,
                                           as_attachment=True))
        response.headers["Content-Disposition"] = f'attachment; filename="{file_name}"'
        return response
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e.err_msg, exc_info=True)
        return ret
    except FileNotFoundError as e:
        logger.error(str(e))
        ret[Ret.Code] = ErrCode.System
        ret[Ret.Msg] = 'No such file or directory.'
        logger.error(str(e), exc_info=True)
        return ret
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(str(e), exc_info=True)
        return ret


@bp.route("/homepage/normalized_result/download", methods=["GET"])
def download_normalized_demand_result_file():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success',
        Ret.Data: None
    }
    fiscal_week = request.args.get('fiscal_week', None, str)
    category = request.args.get('category', None, int)

    try:
        # 必填校验
        if fiscal_week is None or category is None:
            raise ErrorExcept(ErrCode.Param, "please check params, required parameter missing.")

        normalized_demand_result = NormalizedDemandResult(fiscal_week, NORMALIZED_DEMAND, category)
        excel_file = normalized_demand_result.download()

        file_name = normalized_demand_result.file_info()["file_name"]
        response = make_response(send_file(excel_file,
                                           mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                                           download_name=file_name,
                                           as_attachment=True))
        response.headers["Content-Disposition"] = f'attachment; filename="{file_name}"'
        return response
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e.err_msg, exc_info=True)
        return ret
    except FileNotFoundError as e:
        logger.error(str(e))
        ret[Ret.Code] = ErrCode.System
        ret[Ret.Msg] = 'No such file or directory.'
        logger.error(str(e), exc_info=True)
        return ret
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(str(e), exc_info=True)
        return ret


@bp.route("/homepage/sell_in_result/download", methods=["GET"])
def download_sell_in_demand_result_file():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success',
        Ret.Data: None
    }
    fiscal_week = request.args.get('fiscal_week', None, str)
    category = request.args.get('category', None, int)

    try:
        # 必填校验
        if fiscal_week is None or category is None:
            raise ErrorExcept(ErrCode.Param, "please check params, required parameter missing.")

        sell_in_demand_result = SellInDemandResult(fiscal_week, SELL_IN_DEMAND, category)
        excel_file = sell_in_demand_result.download()

        file_name = sell_in_demand_result.file_info()["file_name"]
        response = make_response(send_file(excel_file,
                                           mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                                           download_name=file_name,
                                           as_attachment=True))
        response.headers["Content-Disposition"] = f'attachment; filename="{file_name}"'
        return response
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e.err_msg, exc_info=True)
        return ret
    except FileNotFoundError as e:
        logger.error(str(e))
        ret[Ret.Code] = ErrCode.System
        ret[Ret.Msg] = 'No such file or directory.'
        logger.error(str(e), exc_info=True)
        return ret
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(str(e), exc_info=True)
        return ret


@bp.route("/homepage/delta_demand_result/download", methods=["GET"])
def download_delta_demand_result_file():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success',
        Ret.Data: None
    }
    fiscal_week = request.args.get('fiscal_week', None, str)
    category = request.args.get('category', None, int)

    try:
        # 必填校验
        if fiscal_week is None or category is None:
            raise ErrorExcept(ErrCode.Param, "please check params, required parameter missing.")

        delta_demand_result = DeltaDemandResult(fiscal_week, DELTA_DEMAND, category)
        # delta_demand_result = SellInDemandResult(fiscal_week, SELL_IN_DEMAND, category)
        excel_file = delta_demand_result.download()

        file_name = delta_demand_result.file_info()["file_name"]
        response = make_response(send_file(excel_file,
                                           mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                                           download_name=file_name,
                                           as_attachment=True))
        response.headers["Content-Disposition"] = f'attachment; filename="{file_name}"'
        return response
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e.err_msg, exc_info=True)
        return ret
    except FileNotFoundError as e:
        logger.error(str(e))
        ret[Ret.Code] = ErrCode.System
        ret[Ret.Msg] = 'No such file or directory.'
        logger.error(str(e), exc_info=True)
        return ret
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(str(e), exc_info=True)
        return ret
