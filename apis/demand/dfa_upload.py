from apis.api_common import *
from data.mysqls.demand.ideal_demand_cpf_upload_dfa import IdealDemandCPFUploadDFA
from domain.demand.entity.module_switch_enum import ModuleSwitchEnum
from domain.demand.impl.dfa_upload import (
    get_dfa_template_binary, get_dfa_upload_file,
    upload_dfa_file, delete_dfa_data, query_dfa,
    publish_dfa,
)
from kit.custom_date.custom_week_date import CustomWeekDate

bp = Blueprint('dfa_upload', __name__, url_prefix=UrlPrefixdemand)


@bp.route('/dfa/template')
def get_dfa_template():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: 'Success'}

    fiscal_week = request.args.get('fiscal_week', "", type=str)

    if not fiscal_week:
        raise ErrorExcept(ErrCode.Param, ErrMsg.get(ErrCode.Param))

    try:
        file_name, excel_file = get_dfa_template_binary(fiscal_week)
        response = make_response(send_file(excel_file,
                                           mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                                           download_name=file_name,
                                           as_attachment=True))
        response.headers["Content-Disposition"] = f'attachment; filename="{file_name}"'
    except FileNotFoundError:
        ret[Ret.Code] = ErrCode.System
        ret[Ret.Msg] = 'No such file or directory.'
        return ret
    return response


@bp.route('/dfa/download')
def download_dfa_upload_file():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: 'Success'}

    fiscal_week = request.args.get('fiscal_week', "", type=str)

    if not fiscal_week:
        raise ErrorExcept(ErrCode.Param, ErrMsg.get(ErrCode.Param))

    try:
        file_name, excel_file = get_dfa_upload_file(fiscal_week)
        response = make_response(send_file(excel_file,
                                           mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                                           download_name=file_name,
                                           as_attachment=True))
        response.headers["Content-Disposition"] = f'attachment; filename="{file_name}"'
    except FileNotFoundError:
        ret[Ret.Code] = ErrCode.System
        ret[Ret.Msg] = 'No such file or directory.'
        return ret
    return response


@bp.route('/dfa/upload', methods=["POST"])
def dfa_upload_file():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: 'Success'}

    fiscal_week = request.args.get('fiscal_week', "", type=str)

    if not fiscal_week:
        raise ErrorExcept(ErrCode.Param, ErrMsg.get(ErrCode.Param))

    try:
        file = request.files.get("file")
        uploader = request.headers.get(ShieldPrsNickName) + " " + request.headers.get(ShieldPrsLastName)
        logger.info(f"dfa-->upload: fiscal_week:{fiscal_week}, uploader:{uploader} ready upload file")
        ret["data"] = upload_dfa_file(file, fiscal_week, uploader)
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e.err_msg)
    except Exception as e:
        ret[Ret.Code] = ErrCode.System
        ret[Ret.Msg] = str(e)
        return ret
    return ret


@bp.route('/dfa/delete')
def delete_dfa_upload_data():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: 'Success'}

    fiscal_week = request.args.get('fiscal_week', "", type=str)

    if not fiscal_week:
        raise ErrorExcept(ErrCode.Param, ErrMsg.get(ErrCode.Param))

    try:
        # 判断截止时间，超过截止时间之后报错
        if (not CustomWeekDate(ModuleSwitchEnum.DFA_OPERATE_DEADLINE.value).is_before_ddl()
                and IdealDemandCPFUploadDFA.is_published_by_week(fiscal_week)):
            raise Exception("已超过时间窗，不允许删除!")
        operator = request.headers.get(ShieldPrsNickName) + " " + request.headers.get(ShieldPrsLastName)
        logger.info(f"dfa-->delete: fiscal_week:{fiscal_week}, uploader:{operator} ready delete file")
        ret[Ret.Data] = delete_dfa_data(fiscal_week, operator)
    except Exception as e:
        ret[Ret.Code] = ErrCode.System
        ret[Ret.Msg] = str(e)
        return ret
    return ret


@bp.route('/dfa/query')
def query_dfa_info():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: 'Success'}

    fiscal_week = request.args.get('fiscal_week', "", type=str)

    if not fiscal_week:
        raise ErrorExcept(ErrCode.Param, ErrMsg.get(ErrCode.Param))

    try:
        ret[Ret.Data] = query_dfa(fiscal_week)
    except Exception as e:
        ret[Ret.Code] = ErrCode.System
        ret[Ret.Msg] = str(e)
        return ret
    return ret


@bp.route('/dfa/publish', methods=["POST"])
def publish_dfa_data():
    ret = {Ret.Code: ErrCode.Success, Ret.Msg: 'Success'}

    fiscal_week = request.json.get('fiscal_week', "")

    if not fiscal_week:
        raise ErrorExcept(ErrCode.Param, ErrMsg.get(ErrCode.Param))

    try:
        # 判断截止时间，超过截止时间之后报错
        if (not CustomWeekDate(ModuleSwitchEnum.DFA_OPERATE_DEADLINE.value).is_before_ddl()
                and IdealDemandCPFUploadDFA.is_published_by_week(fiscal_week)):
            raise Exception("已超过时间窗，不允许再次Submit!")
        operator = request.headers.get(ShieldPrsNickName) + " " + request.headers.get(ShieldPrsLastName)
        logger.info(f"dfa-->publish: fiscal_week:{fiscal_week}, operator:{operator}")
        ret[Ret.Data] = publish_dfa(fiscal_week, operator)
    except Exception as e:
        ret[Ret.Code] = ErrCode.System
        ret[Ret.Msg] = str(e)
        return ret
    return ret
