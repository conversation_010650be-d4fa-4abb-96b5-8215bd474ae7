"""
调用接口，用于调试
"""
from flask import Blueprint, request

from util.const import <PERSON>rr<PERSON><PERSON>, ErrorExcept, Ret
from domain.demand.entity.const import CHINA_MAINLAND, RTMS, IDEAL_DEMAND, SELL_IN_DEMAND, TOPDOWN_DEMAND
from domain.demand.impl.calculator.ideal_demand_calculator import IdealDemandCalculator
from domain.demand.impl.calculator.sellin_demand_calculator import SellInDemandCalculator
from domain.demand.impl.calculator.topdown_demand_calculator import TopdownDemandCalculator
from domain.demand.impl.initialize import DemandSettingInitializer
from domain.demand.impl.processor.const import SALES_FORECAST_PROCESSOR, BASE_PROCESSOR, DFA_PROCESSOR
from domain.demand.impl.processor.sales_forecast_processor import SalesForecastProcessor
from domain.demand.impl.processor.base_processor import BaseProcessor
from domain.demand.impl.processor.dfa_processor import DFAProcessor
from domain.demand.impl.processor.processor_factory import ProcessFactory
from domain.demand.impl.soldto_mpn_cartesian_product import CartesianProduct
from domain.supply.entity import Lob

bp = Blueprint('ideal_demand', __name__, url_prefix="/ideal_demand")


@bp.route("/calculate_di", methods=["GET"])
def ideal_demand_di_calculate():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        query_params = request.args
        fiscal_week = query_params.get('fiscal_week')
        IdealDemandCalculator(IDEAL_DEMAND, fiscal_week).calculate()
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg

    return ret


@bp.route("/calculate_ds", methods=["GET"])
def ideal_demand_ds_calculate():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        query_params = request.args
        fiscal_week = query_params.get('fiscal_week')
        SellInDemandCalculator(SELL_IN_DEMAND, fiscal_week).calculate()
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg

    return ret


@bp.route("/calculate_dt", methods=["GET"])
def ideal_demand_dt_calculate():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        query_params = request.args
        fiscal_week = query_params.get('fiscal_week')
        TopdownDemandCalculator(TOPDOWN_DEMAND, fiscal_week).calculate()
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg

    return ret


@bp.route("/base_processor", methods=["GET"])
def base_processor():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        query_params = request.args
        fiscal_week = query_params.get('fiscal_week')
        # ret = DemandPoolInitializer(fiscal_week).init()
        # DemandPoolInitializer(fiscal_week).init()
        # factory = ProcessFactory(fiscal_week)
        # factory.get(SALES_FORECAST_PROCESSOR).process()
        # ret = CartesianProduct(fiscal_week, CHINA_MAINLAND, Lob.IPHONE.value, RTMS).get_result()
        # SellInDemandCalculator(SELL_IN_DEMAND).calculate()
        # IdealDemandCalculator(IDEAL_DEMAND).calculate()
        # SalesForecastProcessor(SALES_FORECAST_PROCESSOR, fiscal_week)._do()
        BaseProcessor(BASE_PROCESSOR, fiscal_week)._do()
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg

    return ret


@bp.route("/sales_processor", methods=["GET"])
def sales_processor():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        query_params = request.args
        fiscal_week = query_params.get('fiscal_week')
        # ret = DemandPoolInitializer(fiscal_week).init()
        # DemandPoolInitializer(fiscal_week).init()
        # factory = ProcessFactory(fiscal_week)
        # factory.get(SALES_FORECAST_PROCESSOR).process()
        # ret = CartesianProduct(fiscal_week, CHINA_MAINLAND, Lob.IPHONE.value, RTMS).get_result()
        # SellInDemandCalculator(SELL_IN_DEMAND).calculate()
        # IdealDemandCalculator(IDEAL_DEMAND).calculate()
        SalesForecastProcessor(SALES_FORECAST_PROCESSOR, fiscal_week)._do()
        # BaseProcessor(BASE_PROCESSOR, fiscal_week)._do()
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg

    return ret


@bp.route("/init_x_y", methods=["GET"])
def init_x_y():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        query_params = request.args
        fiscal_week = query_params.get('fiscal_week')

        DemandSettingInitializer(fiscal_week).init()
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg

    return ret


@bp.route("/dfa_processor", methods=["GET"])
def dfa_process():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        query_params = request.args
        fiscal_week = query_params.get('fiscal_week')
        DFAProcessor(DFA_PROCESSOR, fiscal_week)._do()
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg

    return ret
