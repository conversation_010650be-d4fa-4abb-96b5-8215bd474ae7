from .api_common import *
from service.allocation_run_preview_service import *


bp = Blueprint('allocation_run_preview_api', __name__, url_prefix=UrlPrefix)


@bp.route("/allocation_run/preview")
def get_allocation_run_preview_data():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    fiscal_week_year = request.args.get("fiscal_week_year")
    project_code = request.args.getlist("project_code[]")
    mpn = request.args.getlist("mpn[]")
    try:
        ret["data"] = get_preview_data(fiscal_week_year, project_code, mpn)
    except ErrorExcept as e:
        logger.exception(e)
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        logger.exception(e)
        ret[Ret.Code] = ErrCode.System
        ret[Ret.Msg] = str(e)
    return ret


@bp.route("/allocation_run/preview/download")
def get_allocation_run_preview_download_data():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    fiscal_week_year = request.args.get("fiscal_week_year")
    try:
        path = gen_allocation_run_preview_download_data(fiscal_week_year)
        paths = os.path.split(path)
        file_name = paths[-1]

        response = make_response(send_file(path, as_attachment=True))
        response.headers["Content-Disposition"] = f"attachment; filename={file_name.encode().decode('latin-1')}"
        return response
    except ErrorExcept as e:
        logger.exception(e)
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        logger.exception(e)
        ret[Ret.Code] = ErrCode.System
        ret[Ret.Msg] = str(e)
    return ret


@bp.route("/allocation_run/preview/project_code")
def get_allocation_run_preview_project_code():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    fiscal_week_year = request.args.get("fiscal_week_year")
    try:
        ret["data"] = get_project_code(fiscal_week_year)
    except ErrorExcept as e:
        logger.exception(e)
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        logger.exception(e)
        ret[Ret.Code] = ErrCode.System
        ret[Ret.Msg] = str(e)
    return ret


@bp.route("/allocation_run/preview/mpn")
def get_allocation_run_preview_mpn():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    fiscal_week_year = request.args.get("fiscal_week_year")
    project_code = request.args.getlist("project_code[]")
    try:
        ret["data"] = get_mpn_id(fiscal_week_year, project_code)
    except ErrorExcept as e:
        logger.exception(e)
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        logger.exception(e)
        ret[Ret.Code] = ErrCode.System
        ret[Ret.Msg] = str(e)
    return ret
