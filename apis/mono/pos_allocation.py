import threading

from apis.api_common import *
from domain.mono.pos_allocation.impl.pos_allocation_from_shipment_plan_v2 import allocate_task_processor, \
    get_allocate_file_info

bp = Blueprint('mono_allocation', __name__, url_prefix="/allocation")


@bp.route("/pos_allocation/npp_self_allocate", methods=["GET"])
def pos_allocation_allocate_mono():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success',
        Ret.Data: None
    }
    fiscal_week = request.args.get('fiscal_week')
    plan_id = request.args.get('planId')
    try:
        # 必填校验
        if fiscal_week is None or plan_id is None:
            raise ErrorExcept(ErrCode.Param, "please check params, required parameter missing.")
        logger.info(f"pos allocation::mono, fiscal_week: {fiscal_week}, plan_id: {plan_id}")
        t = threading.Thread(target=allocate_task_processor, kwargs={"fiscal_week": fiscal_week, "plan_id": int(plan_id)})
        t.start()
    except Exception as e:
        logger.error(str(e), exc_info=True)
        ret[Ret.Code] = ErrCode.System
        ret[Ret.Msg] = str(e)
    return ret


@bp.route("/pos_allocation/files/npp_self_download")
def rtm_download_sales_input_file():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success',
        Ret.Data: None
    }
    plan_id = request.args.get('planId')
    view_by = request.args.get('view_by')
    try:
        # 必填校验
        if plan_id is None or view_by is None:
            raise ErrorExcept(ErrCode.Param, "please check params, required parameter missing.")
        file_name, file_path = get_allocate_file_info(plan_id=int(plan_id), group_by=view_by)
        response = make_response(send_file(file_path,
                                           mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                                           download_name=file_name,
                                           as_attachment=True))
        response.headers["Content-Disposition"] = f'attachment; filename="{file_name}"'
        return response
    except FileNotFoundError:
        ret[Ret.Code] = ErrCode.System
        ret[Ret.Msg] = 'No such file or directory.'
        return ret
    except Exception as e:
        logger.error(str(e), exc_info=True)
        ret[Ret.Code] = ErrCode.System
        ret[Ret.Msg] = str(e)
        return ret
