from apis.api_common import *
from domain.mono.pos_allocation.impl.mono_pos_allocation_result import MonoPosAllocationFileResult

bp = Blueprint('mono', __name__, url_prefix=UrlPrefixMono)


@bp.route("/pos_allocation/download", methods=["GET"])
def download_pos_allocation_result_file():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success',
        Ret.Data: None
    }
    fiscal_week = request.args.get('fiscal_week', None, str)
    try:
        # 必填校验
        if fiscal_week is None:
            raise ErrorExcept(ErrCode.Param, "please check params, required parameter missing.")

        mono_pos_allocation_result = MonoPosAllocationFileResult(fiscal_week)
        excel_file = mono_pos_allocation_result.download()

        file_name = mono_pos_allocation_result.file_info()["file_name"]
        response = make_response(send_file(excel_file,
                                           mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                                           download_name=file_name,
                                           as_attachment=True))
        response.headers["Content-Disposition"] = f'attachment; filename="{file_name}"'
        return response
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
        logger.error(e.err_msg, exc_info=True)
        return ret
    except FileNotFoundError as e:
        logger.error(str(e))
        ret[Ret.Code] = ErrCode.System
        ret[Ret.Msg] = 'No such file or directory.'
        logger.error(str(e), exc_info=True)
        return ret
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
        logger.error(str(e), exc_info=True)
        return ret


@bp.route("/pos_allocation/npp_self_supply/upload", methods=['POST'])
@shield_person(request)
def file_upload_multi_demand():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    file = request.files.get("file")
    uploader = request.headers.get(ShieldPrsNickName) + " " + request.headers.get(ShieldPrsLastName)
    try:
        ret["data"] = file_save_multi_demand(file, uploader)
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        if len(e.err_msg) <= 500:
            ret[Ret.Msg] = e.err_msg
        else:
            ret[Ret.Msg] = e.err_msg[:500] + "..."
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = FileUploadError.UnknownError
        ret["error"] = str(e)
    return ret
