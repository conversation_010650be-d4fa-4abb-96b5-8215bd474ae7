from .api_common import *
from service.forecast_service import *
from service.operation_workflow_service import insert_operation_workflow
from util.const_tracking import *
from util.const import UrlPrefix


bp = Blueprint('forecast_api', __name__, url_prefix=UrlPrefix)


@bp.route('/forecast_list', methods=['GET'])
def get_forecast_list():
    ret = {
        Ret.Code: ErrCode.Success, 
        Ret.Msg: 'Success'
    }
    try:
        url_params = request.args
        
        page_num = int(url_params.get('page_num', 1))  # offset
        page_size = int(url_params.get('page_size', 10))  # limit

        if page_num > 1:
            page_num = (page_num - 1) * page_size
        else:
            page_num = 0

        ret[Ret.Data] = get_forecast_list_service(page_num, page_size)
        
        person_id = request.headers.get(ShieldPrsId, '')
        insert_operation_workflow(FAST_LITE, VIEW_OPERATION, person_id, TrackingRtms.CP_F, request.path)
    except ErrorExcept as e: 
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e: 
        ret[Ret.Code] = ErrCode.Success
        ret[Ret.Msg] = str(e)

    return ret 


@bp.route('/model_options', methods=['GET'])
def get_model_options():
    ret = {
        Ret.Code: ErrCode.Success, 
        Ret.Msg: 'Success'
    }
    try:
        req = request.args
        lob = req.get('lob', 'iPhone', type=str)
        fiscal_week_year = req.get("fiscal_week_year")
        type = req.get("type")
        ret[Ret.Data] = get_model_list(type, ["iphone"], StrRTMCPF, fiscal_week_year)
        
    except ErrorExcept as e: 
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e: 
        ret[Ret.Code] = ErrCode.Success
        ret[Ret.Msg] = str(e)

    return ret 


@bp.route('/sku_options', methods=['POST'])
def get_sku_options():
    ret = {
        Ret.Code: ErrCode.Success, 
        Ret.Msg: 'Success'
    }
    try:
        req = request.json
        lob = req.get('lob')
        model = req.get('model')
        fiscal_week_year = req.get("fiscal_week_year")
        type = req.get("type")

        
        if model is None:           
            model = []
        
        ret[Ret.Data] = get_sku_options_service(type,lob, model,fiscal_week_year)
        
    except ErrorExcept as e: 
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e: 
        ret[Ret.Code] = ErrCode.Success
        ret[Ret.Msg] = str(e)

    return ret 


@bp.route('/forecast_detail', methods=['POST'])
def get_forecast_detail():
    ret = {
        Ret.Code: ErrCode.Success, 
        Ret.Msg: 'Success'
    }
    try:
        req = request.json
        fiscal_qtr_week_name = req.get('fiscal_qtr_week_name')
        lob = req.get('lob')
        model = req.get('model')
        sku = req.get('sku')
        multi_version = req.get('multi', False)
        
        if fiscal_qtr_week_name is None or fiscal_qtr_week_name.strip() == '':
            ret = {Ret.Code: ErrCode.Param}
            ret[Ret.Msg] = 'required param.'
            return ret
        
        if lob is None or lob.strip() == '':
            lob = 'iPhone'
        
        if model is None:
            # ALL
            model = []
        
        if sku is None:
            # ALL
            sku = []
        
        orders = req.get('orders')
        if orders is None or orders.strip() == '':
            orders = []
        else:
            try:
                orders = json.loads(orders)
            except Exception as e:
                logger.exception(e)
        fiscal_week_year = FiscalYearWeek.get_fiscal_week_year_by_week_date(fiscal_qtr_week_name)
        # model 永不为空
        if model is None or len(model) == 0:
            model = get_model_list(FORECAST, ["iphone"], StrRTMCPF, str(fiscal_week_year))

        if multi_version:
            ret[Ret.Data] = get_forecast_multi_version_detail_service(fiscal_qtr_week_name, lob, model, sku, orders)
        else:
            ret[Ret.Data] = get_forecast_detail_service(fiscal_qtr_week_name, lob, model, sku, orders)
        
    except ErrorExcept as e: 
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e: 
        ret[Ret.Code] = ErrCode.Success
        ret[Ret.Msg] = str(e)

    return ret



@bp.route("/forecast_download", methods=['POST'])
def forecast_download():
    ret = {
        Ret.Code: ErrCode.System
    }
    req = request.json

    # 只下载当前周(FY22Q1W13)的数据, 与筛选器无关, 也就是每次进入到详情页中下载的数据是一致的
    # 分为National和rtm两个类别
    fiscal_qtr_week_name = req.get('fiscal_qtr_week_name')
    download_type = req.get('download_type')
    download_forecast_version = int(req.get('download_version', 0))

    if fiscal_qtr_week_name is None:
        ret[Ret.Msg] = 'please input fiscal_qtr_week_name. eg. FY22Q1W13'
        return ret
    
    if download_type is None:
        ret[Ret.Msg] = 'please input download_type 0 or 1. 0: National, 1: RTM.'
        return ret
    elif download_type not in [0, 1]:
        ret[Ret.Msg] = 'please input correctly int download_type.'
        return ret

    fiscal_week_year = FiscalYearWeek.get_fiscal_week_year_by_week_date(fiscal_qtr_week_name)

    # model 永不为空

    model = get_model_list(FORECAST, ["iphone"], StrRTMCPF, str(fiscal_week_year))

    try:
        path = get_fast_forecast_download_path(fiscal_qtr_week_name, model,download_type, download_forecast_version)
            
        paths = os.path.split(path)
        file_name = paths[-1]

        response = make_response(send_file(path, as_attachment=True))
        response.headers["Content-Disposition"] = f"attachment; filename={file_name.encode().decode('latin-1')}"
        return response
    except Exception as e:
        logger.exception(e)
        ret[Ret.Msg] = str(e)
        return ret
