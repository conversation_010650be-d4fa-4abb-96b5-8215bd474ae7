from .api_common import *
from service.user_service import *
from util.const import UrlPrefix


bp = Blueprint('user_api', __name__, url_prefix=UrlPrefix)


@bp.route("/user/auth")
@shield_person(request)
def fast_user_auth():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        ret["data"] = get_user_auth()
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        ret[Ret.Code] = ErrCode.System
        ret[Ret.Msg] = str(e)

    return ret


@bp.route("/user/info")
@shield_person(request)
def fast_user_info():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        ret["data"] = get_user_info()
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        ret[Ret.Code] = ErrCode.System
        ret[Ret.Msg] = str(e)

    return ret
