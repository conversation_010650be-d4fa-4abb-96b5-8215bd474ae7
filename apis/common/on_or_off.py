from concurrent.futures import ThreadPoolExecutor

from flask import Blueprint, request

from data.mysqls.email_report.ds_ub_wo_ds_wkly import DsUbWoDsWkly
from domain.demand.entity.module_switch_enum import ModuleSwitchEnum
from kit.custom_date.custom_week_date import CustomWeekDate
from util.const import Ret, ErrCode, ErrorExcept

bp = Blueprint('on_or_off', __name__, url_prefix='/common')


@bp.route('/on_or_off', methods=['GET'])
def on_or_off():
    ret = {
        Ret.Code: ErrCode.Success, 
        Ret.Msg: 'Success'
    }
    try:
        url_params = request.args
        module = url_params.get('module')
        if not module:
            raise ErrorExcept(ErrCode.Param, 'please check params.')
        is_on = {"is_on": CustomWeekDate(module.upper()).is_before_ddl()}
        ret[Ret.Data] = is_on
    except ErrorExcept as e: 
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e: 
        ret[Ret.Code] = ErrCode.Success
        ret[Ret.Msg] = str(e)

    return ret


@bp.route('/on_or_off/list', methods=['POST'])
def on_or_off_list():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }

    def process_module(module):
        custom_week_date = CustomWeekDate(module)
        return {
            'module': module,
            'week': custom_week_date.week_date['week'],
            'hour': custom_week_date.week_date['hour'],
            'minute': custom_week_date.week_date['minute'],
            'second': custom_week_date.week_date['second'],
            'start_week': custom_week_date.week_date['start_week'],
            'start_hour': custom_week_date.week_date['start_hour'],
            'start_minute': custom_week_date.week_date['start_minute'],
            'start_second': custom_week_date.week_date['start_second'],
            'is_not_started': custom_week_date.is_not_started(),
            'is_on': custom_week_date.is_on(),
            'is_end': custom_week_date.is_end(),
        }
    try:
        modules = request.json
        # 使用线程池并行处理
        with ThreadPoolExecutor(max_workers=len(modules)) as executor:
            # 提交所有任务
            futures = [executor.submit(process_module, module) for module in modules]
            result_list = []
            for future in futures:
                result_list.append(future.result())
        ret[Ret.Data] = result_list
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        ret[Ret.Code] = ErrCode.Success
        ret[Ret.Msg] = str(e)
    return ret


@bp.route('/test_algo_db', methods=['GET'])
def test_algo_db():
    ret = {
        Ret.Code: ErrCode.Success, 
        Ret.Msg: 'Success'
    }
    try:
        url_params = request.args
        query_date = url_params.get('query_date')
        if not query_date:
            raise ErrorExcept(ErrCode.Param, 'please check params.')
        data = DsUbWoDsWkly.query_overall_record(query_date, 'FY24Q4W12')
        ret[Ret.Data] = data
    except ErrorExcept as e: 
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e: 
        ret[Ret.Code] = ErrCode.Success
        ret[Ret.Msg] = str(e)

    return ret 
