from flask import Blueprint, request

from domain.baozun.entity.bz_client import Bz<PERSON><PERSON>
from util.const import Ret, ErrCode, ErrorExcept, ShieldPrsNickName, ShieldPrsLastName
from util.cpf_util import save_file

bp = Blueprint('file', __name__, url_prefix='/common')


@bp.route('/upload', methods=['POST'])
def upload_file():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        file = request.files.get("file")
        file_name = file.filename
        file_path = request.args.get("file_path")
        uploader = request.headers.get(ShieldPrsNickName) + " " + request.headers.get(ShieldPrsLastName)
        origin_file_path, file_name = save_file(file=file, path=file_path, file_name=file_name)
        ret[Ret.Data] = {
            "file_path": origin_file_path,
            "file_name": file_name
        }
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        if len(e.err_msg) <= 500:
            ret[Ret.Msg] = e.err_msg
        else:
            ret[Ret.Msg] = e.err_msg[:500] + "..."
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
    return ret


@bp.route('/get_file', methods=['GET'])
def get_file():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }
    try:
        ori_url = request.args.get("ori_url")
        aaa = BzClient.get_access_token()
        image_url = BzClient.get_image_url(img_url=ori_url, access_token=aaa)
        ret[Ret.Data] = {
            "image_url": image_url,
        }
    except ErrorExcept as e:
        ret[Ret.Code] = e.code
        if len(e.err_msg) <= 500:
            ret[Ret.Msg] = e.err_msg
        else:
            ret[Ret.Msg] = e.err_msg[:500] + "..."
    except Exception as e:
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
    return ret
