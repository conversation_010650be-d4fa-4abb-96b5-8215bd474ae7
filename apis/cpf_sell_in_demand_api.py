from .api_common import *
from service.cpf_sell_in_demand_service import *
from util.util_operation import insert_operate_record

bp = Blueprint('cpf_sell_in_demand_api', __name__, url_prefix=UrlPrefix + '/sell_in_demand')


@bp.route('/list', methods=['GET'])
def get_sell_in_demand_list():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'}
    try:
        query_params = request.args
        cpf_lob_id = int(query_params.get('id'))
        ret[Ret.Data] = get_sell_in_demand_list_service(cpf_lob_id)
    except ErrorExcept as e:
        logger.exception(e)
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        logger.exception(e)
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
    return ret


@bp.route('/sell_in_demand_with_tags_only_name', methods=['GET'])
def sell_in_demand_with_tags_only_name():
    sell_in_demand_with_tags_only_name_service()
    return {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'}


@bp.route('/download', methods=['GET'])
def download():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'}
    try:
        query_params = request.args
        cpf_lob_id = int(query_params.get('id'))
        category = int(query_params.get('category'))
        operator = request.headers.get(ShieldPrsNickName) + " " + request.headers.get(ShieldPrsLastName)
        file_path, file_name = download_service(cpf_lob_id, category)
        insert_operate_record(None, AllocationOperateCategory.SellInDemandDownload, file_name, file_path, operator, None)
        response = make_response(send_file(file_path, as_attachment=True))
        response.headers["Content-Disposition"] = f'attachment; filename="{file_name}"'
    except FileNotFoundError as e:
        logger.exception(e)
        ret[Ret.Code] = ErrCode.System
        ret[Ret.Msg] = 'No such file or directory.'
        return ret
    return response


@bp.route("/cpf_file_download")
def cpf_file_download():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'
    }

    file_path = request.args.get('file_path')
    file_prefix = request.args.get('file_prefix')
    if file_path is None:
        ret[Ret.Code] = ErrCode.Param
        ret[Ret.Msg] = 'file_path is required.'
        return ret
    file_path = cpf_transfer_file_path(file_path, file_prefix)
    if file_path == "":
        ret[Ret.Code] = ErrCode.System
        ret[Ret.Msg] = 'path not exist.'
        return ret
    try:
        response = make_response(send_file(file_path, as_attachment=True))
        response.headers["Content-Disposition"] = f"attachment; filename=temporary_name.xlsx"
    except FileNotFoundError as e:
        logger.exception(e)
        ret[Ret.Code] = ErrCode.System
        ret[Ret.Msg] = 'No such file or directory.'
        return ret
    return response


@bp.route('/publish', methods=['POST'])
def publish():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'}
    try:
        current_user = request.headers.get(ShieldPrsNickName) + " " + request.headers.get(ShieldPrsLastName)
        query_params = request.json
        cpf_lob_id = int(query_params.get('id'))
        description = query_params.get('description')
        ret[Ret.Data] = publish_service(cpf_lob_id, current_user, description)
    except ErrorExcept as e:
        logger.exception(e)
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        logger.exception(e)
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
    return ret


@bp.route('/confirm', methods=['POST'])
def mono_error_confirm():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'}
    try:
        current_user = request.headers.get(ShieldPrsNickName) + " " + request.headers.get(ShieldPrsLastName)
        query_params = request.args
        prepare_id = int(query_params.get('prepare_id'))
        ret[Ret.Data] = sell_in_demand_confirm_service(prepare_id, current_user)
    except ErrorExcept as e:
        logger.exception(e)
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        logger.exception(e)
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
    return ret


@bp.route('/error_page', methods=['GET'])
def multi_online_error_page():
    ret = {
        Ret.Code: ErrCode.Success,
        Ret.Msg: 'Success'}
    try:
        query_params = request.args
        rtm = query_params.get('rtm')
        fiscal_week_year = int(query_params.get('fiscal_week_year'))
        if not rtm or not fiscal_week_year:
            raise ErrorExcept(ErrCode.Param, "please check params.")
        
        ret[Ret.Data] = get_error_page_info(rtm, fiscal_week_year)
    except ErrorExcept as e:
        logger.exception(e)
        ret[Ret.Code] = e.code
        ret[Ret.Msg] = e.err_msg
    except Exception as e:
        logger.exception(e)
        ret[Ret.Code] = ErrCode.UnknownError
        ret[Ret.Msg] = str(e)
    return ret
