########## must at head ############
import sys
import os

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(SCRIPT_DIR))
##########      end     ############

from data.demand_data import AppFastDemandMultiSummaryAmWa, \
    AppFastDemandOnlineSummaryAmWa, \
    AppFastAdvanceDemandMonoSummaryMondayWa, \
    AppFastAdvanceDemandMonoSummaryTuesdayWa, \
    AppFastDemandMonoSummaryWa
from util.const import *
from service.demand_service import get_multi_version_demand_detail_service


def test_demand_latest_version():
    fiscal_qtr_week_name = 'FY23Q2W6'
    v1 = AppFastDemandMonoSummaryWa.get_version_1_by_week(fiscal_qtr_week_name)
    v2 = AppFastAdvanceDemandMonoSummaryMondayWa.get_version_2_by_week(fiscal_qtr_week_name)
    v3 = AppFastAdvanceDemandMonoSummaryTuesdayWa.get_version_3_by_week(fiscal_qtr_week_name)
    if v3 > 0:
        version = DEMAND_THIRD_VERSION
    elif v2 > 0:
        version = DEMAND_SECOND_VERSION
    elif v1 > 0:
        version = DEMAND_FIRST_VERSION
    assert version == DEMAND_THIRD_VERSION


def test_demand_multi_version_by_week():
    fiscal_qtr_week_name = 'FY23Q2W6'
    ret = get_multi_version_demand_detail_service(fiscal_qtr_week_name=fiscal_qtr_week_name,
                                                  lob=None,model=[],sku=[],rtm=[],business_type=[],sold_to=[],
                                                  orders=[],page_num=0,page_size=10)
    assert len(ret['records']) > 0  and ret['records'][0]['rtm'] == 'Total' and ret['records'][1]['sold_to_id'] == 531599

    
def test_download_different_version_file():
    fiscal_qtr_week_name = 'FY23Q2W6'
    ret = AppFastDemandMonoSummaryWa.get_download_data(fiscal_qtr_week_name, DEMAND_FIRST_VERSION)
    assert len(ret.columns) == 24 and len(ret) == 5929
