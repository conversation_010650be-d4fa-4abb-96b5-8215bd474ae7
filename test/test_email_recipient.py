########## must at head ############
import sys
import os

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(SCRIPT_DIR))
##########      end     ############

import pytest
from util.fast_lite_base import fast_lite_engine
from data.email_recipient import TblEmailRecipient
from util.const import *

@pytest.mark.skip(reason="pass")
def test_create_table():
    try:
        TblEmailRecipient.__table__.create(fast_lite_engine)
        assert 1 == 1
    except Exception as e:
        print("created.")
        assert 1 == 1

def test_get_to_cc():
    try:
        to, cc = TblEmailRecipient.get_to_cc_list(0, None)
        print(f"to>>>{to}, cc>>>{cc}")
        assert 1 == 0
    except Exception as e:
        print(f"error >>> ${e}")
