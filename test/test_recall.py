import os
import sys

import pytest

from data.databend.dashboard.weekly_ub_tracking_summary import WeeklyUbTrackingSummaryDi
from domain.dashboard.entity.anti_fraud.anti_fraud_so_ub import DailyItem, WeeklyItem
from domain.dashboard.entity.fiscal_week import FiscalWeek
from domain.dashboard.entity.ub_recall import UbDailyRecall, UbWeekRecall

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(SCRIPT_DIR))


def test_daily_group_by_sub_rtm():
    # 构造源数据类
    data_source = [
            DailyItem(fiscal_dt='2024-09-11', rtm='Mono Brand', sub_rtm='MONO', sub_lob = 'iPhone 15 Pro Max', total_so=2, lte_3_days_ub=1, lte_7_days_ub=2),
            DailyItem(fiscal_dt='2024-09-11', rtm='Mono Brand', sub_rtm='MONO', sub_lob='iPhone 15 Pro', total_so=2, lte_3_days_ub=1, lte_7_days_ub=2),
            DailyItem(fiscal_dt='2024-09-11', rtm='Mono Brand', sub_rtm='Lifestyle', sub_lob = 'iPhone 15 Pro Max', total_so=2, lte_3_days_ub=1, lte_7_days_ub=2),
            DailyItem(fiscal_dt='2024-09-11', rtm='Mono Brand', sub_rtm='Lifestyle', sub_lob = 'iPhone 15 Pro', total_so=2, lte_3_days_ub=1, lte_7_days_ub=2),

            DailyItem(fiscal_dt='2024-09-11', rtm='Carrier', sub_rtm='CU', sub_lob='iPhone 15 Pro Max', total_so=2, lte_3_days_ub=1, lte_7_days_ub=2),
            DailyItem(fiscal_dt='2024-09-11', rtm='Carrier', sub_rtm='CU', sub_lob='iPhone 15 Pro', total_so=2, lte_3_days_ub=1, lte_7_days_ub=2),
            DailyItem(fiscal_dt='2024-09-11', rtm='Carrier', sub_rtm='CT', sub_lob='iPhone 15 Pro Max', total_so=2, lte_3_days_ub=1, lte_7_days_ub=2),
            DailyItem(fiscal_dt='2024-09-11', rtm='Carrier', sub_rtm='CT', sub_lob='iPhone 15 Pro', total_so=2, lte_3_days_ub=1, lte_7_days_ub=2),
            ]

    data_except = [
        DailyItem(fiscal_dt='2024-09-11', rtm='Carrier', sub_rtm='CT', sub_lob='All', total_so=4, lte_3_days_ub=2, lte_7_days_ub=4),
        DailyItem(fiscal_dt='2024-09-11', rtm='Carrier', sub_rtm='CU', sub_lob='All', total_so=4, lte_3_days_ub=2, lte_7_days_ub=4),
        DailyItem(fiscal_dt='2024-09-11', rtm='Mono Brand', sub_rtm='Lifestyle', sub_lob='All', total_so=4, lte_3_days_ub=2, lte_7_days_ub=4),
        DailyItem(fiscal_dt='2024-09-11', rtm='Mono Brand', sub_rtm='MONO', sub_lob='All', total_so=4, lte_3_days_ub=2, lte_7_days_ub=4),
    ]
    # 执行要测试的方法
    ret = UbDailyRecall.group_by_sub_rtm(data_source)
    # 对比执行结果是否预期
    for i in range(len(ret)):
        assert ret[i].fiscal_dt == data_except[i].fiscal_dt
        assert ret[i].rtm == data_except[i].rtm
        assert ret[i].sub_rtm == data_except[i].sub_rtm
        assert ret[i].sub_lob == data_except[i].sub_lob
        assert ret[i].sub_lob == data_except[i].sub_lob
        assert ret[i].total_so == data_except[i].total_so
        assert ret[i].lte_3_days_ub == data_except[i].lte_3_days_ub
        assert ret[i].lte_7_days_ub == data_except[i].lte_7_days_ub


def test_daily_group_by_rtm():
    # 构造源数据类
    data_source = [
            DailyItem(fiscal_dt='2024-09-11', rtm='Mono Brand', sub_rtm='MONO', sub_lob = 'iPhone 15 Pro Max', total_so=2, lte_3_days_ub=1, lte_7_days_ub=2),
            DailyItem(fiscal_dt='2024-09-11', rtm='Mono Brand', sub_rtm='MONO', sub_lob='iPhone 15 Pro', total_so=2, lte_3_days_ub=1, lte_7_days_ub=2),
            DailyItem(fiscal_dt='2024-09-11', rtm='Mono Brand', sub_rtm='Lifestyle', sub_lob = 'iPhone 15 Pro Max', total_so=2, lte_3_days_ub=1, lte_7_days_ub=2),
            DailyItem(fiscal_dt='2024-09-11', rtm='Mono Brand', sub_rtm='Lifestyle', sub_lob = 'iPhone 15 Pro', total_so=2, lte_3_days_ub=1, lte_7_days_ub=2),

            DailyItem(fiscal_dt='2024-09-11', rtm='Carrier', sub_rtm='CU', sub_lob='iPhone 15 Pro Max', total_so=2, lte_3_days_ub=1, lte_7_days_ub=2),
            DailyItem(fiscal_dt='2024-09-11', rtm='Carrier', sub_rtm='CU', sub_lob='iPhone 15 Pro', total_so=2, lte_3_days_ub=1, lte_7_days_ub=2),
            DailyItem(fiscal_dt='2024-09-11', rtm='Carrier', sub_rtm='CT', sub_lob='iPhone 15 Pro Max', total_so=2, lte_3_days_ub=1, lte_7_days_ub=2),
            DailyItem(fiscal_dt='2024-09-11', rtm='Carrier', sub_rtm='CT', sub_lob='iPhone 15 Pro', total_so=2, lte_3_days_ub=1, lte_7_days_ub=2),
            ]

    data_except = [
        DailyItem(fiscal_dt='2024-09-11', rtm='Mono Brand', sub_rtm='All', sub_lob='All', total_so=8, lte_3_days_ub=4, lte_7_days_ub=8),
        DailyItem(fiscal_dt='2024-09-11', rtm='Carrier', sub_rtm='All', sub_lob='All', total_so=8, lte_3_days_ub=4, lte_7_days_ub=8),
    ]
    # 执行要测试的方法
    ret = UbDailyRecall.group_by_rtm(data_source)
    # 对比执行结果是否预期
    for i in range(len(ret)):
        assert ret[i].fiscal_dt == data_except[i].fiscal_dt
        assert ret[i].rtm == data_except[i].rtm
        assert ret[i].sub_rtm == data_except[i].sub_rtm
        assert ret[i].sub_lob == data_except[i].sub_lob
        assert ret[i].sub_lob == data_except[i].sub_lob
        assert ret[i].total_so == data_except[i].total_so
        assert ret[i].lte_3_days_ub == data_except[i].lte_3_days_ub
        assert ret[i].lte_7_days_ub == data_except[i].lte_7_days_ub


def test_daily_group_by_region():
    # 构造源数据类
    data_source = [
            DailyItem(fiscal_dt='2024-09-11', rtm='Mono Brand', sub_rtm='MONO', sub_lob = 'iPhone 15 Pro Max', total_so=2, lte_3_days_ub=1, lte_7_days_ub=2),
            DailyItem(fiscal_dt='2024-09-11', rtm='Mono Brand', sub_rtm='MONO', sub_lob='iPhone 15 Pro', total_so=2, lte_3_days_ub=1, lte_7_days_ub=2),
            DailyItem(fiscal_dt='2024-09-11', rtm='Mono Brand', sub_rtm='Lifestyle', sub_lob = 'iPhone 15 Pro Max', total_so=2, lte_3_days_ub=1, lte_7_days_ub=2),
            DailyItem(fiscal_dt='2024-09-11', rtm='Mono Brand', sub_rtm='Lifestyle', sub_lob = 'iPhone 15 Pro', total_so=2, lte_3_days_ub=1, lte_7_days_ub=2),

            DailyItem(fiscal_dt='2024-09-11', rtm='Carrier', sub_rtm='CU', sub_lob='iPhone 15 Pro Max', total_so=2, lte_3_days_ub=1, lte_7_days_ub=2),
            DailyItem(fiscal_dt='2024-09-11', rtm='Carrier', sub_rtm='CU', sub_lob='iPhone 15 Pro', total_so=2, lte_3_days_ub=1, lte_7_days_ub=2),
            DailyItem(fiscal_dt='2024-09-11', rtm='Carrier', sub_rtm='CT', sub_lob='iPhone 15 Pro Max', total_so=2, lte_3_days_ub=1, lte_7_days_ub=2),
            DailyItem(fiscal_dt='2024-09-11', rtm='Carrier', sub_rtm='CT', sub_lob='iPhone 15 Pro', total_so=2, lte_3_days_ub=1, lte_7_days_ub=2),
            ]

    data_except = [
        DailyItem(fiscal_dt='2024-09-11', rtm='All', sub_rtm='All', sub_lob='All', total_so=16, lte_3_days_ub=8, lte_7_days_ub=16)
    ]
    # 执行要测试的方法
    ret = UbDailyRecall.group_by_region(data_source)
    # 对比执行结果是否预期
    for i in range(len(ret)):
        assert ret[i].fiscal_dt == data_except[i].fiscal_dt
        assert ret[i].rtm == data_except[i].rtm
        assert ret[i].sub_rtm == data_except[i].sub_rtm
        assert ret[i].sub_lob == data_except[i].sub_lob
        assert ret[i].sub_lob == data_except[i].sub_lob
        assert ret[i].total_so == data_except[i].total_so
        assert ret[i].lte_3_days_ub == data_except[i].lte_3_days_ub
        assert ret[i].lte_7_days_ub == data_except[i].lte_7_days_ub

# @pytest.mark.skip(reason='')
def test_week_group_by_sub_rtm():
    # 构造源数据类
    data_source = [
            WeeklyItem(snapshot_date='2024-09-12', fiscal_qtr_week_name='FY24Q4W9', rtm='Mono Brand', sub_rtm='MONO', sub_lob = 'iPhone 15 Pro Max', total_so=2, lte_7_days_ub=2),
            WeeklyItem(snapshot_date='2024-09-12', fiscal_qtr_week_name='FY24Q4W9', rtm='Mono Brand', sub_rtm='MONO', sub_lob='iPhone 15 Pro', total_so=2, lte_7_days_ub=2),
            WeeklyItem(snapshot_date='2024-09-12', fiscal_qtr_week_name='FY24Q4W9', rtm='Mono Brand', sub_rtm='Lifestyle', sub_lob = 'iPhone 15 Pro Max', total_so=2, lte_7_days_ub=2),
            WeeklyItem(snapshot_date='2024-09-12', fiscal_qtr_week_name='FY24Q4W9', rtm='Mono Brand', sub_rtm='Lifestyle', sub_lob = 'iPhone 15 Pro', total_so=2, lte_7_days_ub=2),

            WeeklyItem(snapshot_date='2024-09-12', fiscal_qtr_week_name='FY24Q4W9', rtm='Carrier', sub_rtm='CU', sub_lob='iPhone 15 Pro Max', total_so=2, lte_7_days_ub=2),
            WeeklyItem(snapshot_date='2024-09-12', fiscal_qtr_week_name='FY24Q4W9', rtm='Carrier', sub_rtm='CU', sub_lob='iPhone 15 Pro', total_so=2, lte_7_days_ub=2),
            WeeklyItem(snapshot_date='2024-09-12', fiscal_qtr_week_name='FY24Q4W9', rtm='Carrier', sub_rtm='CT', sub_lob='iPhone 15 Pro Max', total_so=2, lte_7_days_ub=2),
            WeeklyItem(snapshot_date='2024-09-12', fiscal_qtr_week_name='FY24Q4W9', rtm='Carrier', sub_rtm='CT', sub_lob='iPhone 15 Pro', total_so=2, lte_7_days_ub=2),
            ]

    data_except = [
        WeeklyItem(snapshot_date='2024-09-12', fiscal_qtr_week_name='FY24Q4W9', rtm='Carrier', sub_rtm='CT', sub_lob='All', total_so=4, lte_7_days_ub=4),
        WeeklyItem(snapshot_date='2024-09-12', fiscal_qtr_week_name='FY24Q4W9', rtm='Carrier', sub_rtm='CU', sub_lob='All', total_so=4, lte_7_days_ub=4),
        WeeklyItem(snapshot_date='2024-09-12', fiscal_qtr_week_name='FY24Q4W9', rtm='Mono Brand', sub_rtm='Lifestyle', sub_lob='All', total_so=4, lte_7_days_ub=4),
        WeeklyItem(snapshot_date='2024-09-12', fiscal_qtr_week_name='FY24Q4W9', rtm='Mono Brand', sub_rtm='MONO', sub_lob='All', total_so=4, lte_7_days_ub=4),
    ]
    # 执行要测试的方法
    ret = UbWeekRecall.group_by_sub_rtm(data_source)
    # 对比执行结果是否预期
    for i in range(len(ret)):
        assert ret[i].snapshot_date == data_except[i].snapshot_date
        assert ret[i].fiscal_qtr_week_name == data_except[i].fiscal_qtr_week_name
        assert ret[i].rtm == data_except[i].rtm
        assert ret[i].sub_rtm == data_except[i].sub_rtm
        assert ret[i].sub_lob == data_except[i].sub_lob
        assert ret[i].sub_lob == data_except[i].sub_lob
        assert ret[i].total_so == data_except[i].total_so
        assert ret[i].lte_7_days_ub == data_except[i].lte_7_days_ub


# @pytest.mark.skip(reason='')
def test_week_group_by_rtm():
    # 构造源数据类
    data_source = [
        WeeklyItem(snapshot_date='2024-09-12', fiscal_qtr_week_name='FY24Q4W9', rtm='Mono Brand', sub_rtm='MONO', sub_lob='iPhone 15 Pro Max', total_so=2, lte_7_days_ub=2),
        WeeklyItem(snapshot_date='2024-09-12', fiscal_qtr_week_name='FY24Q4W9', rtm='Mono Brand', sub_rtm='MONO', sub_lob='iPhone 15 Pro', total_so=2, lte_7_days_ub=2),
        WeeklyItem(snapshot_date='2024-09-12', fiscal_qtr_week_name='FY24Q4W9', rtm='Mono Brand', sub_rtm='Lifestyle', sub_lob='iPhone 15 Pro Max', total_so=2, lte_7_days_ub=2),
        WeeklyItem(snapshot_date='2024-09-12', fiscal_qtr_week_name='FY24Q4W9', rtm='Mono Brand', sub_rtm='Lifestyle', sub_lob='iPhone 15 Pro', total_so=2, lte_7_days_ub=2),

        WeeklyItem(snapshot_date='2024-09-12', fiscal_qtr_week_name='FY24Q4W9', rtm='Carrier', sub_rtm='CU', sub_lob='iPhone 15 Pro Max', total_so=2, lte_7_days_ub=2),
        WeeklyItem(snapshot_date='2024-09-12', fiscal_qtr_week_name='FY24Q4W9', rtm='Carrier', sub_rtm='CU', sub_lob='iPhone 15 Pro', total_so=2, lte_7_days_ub=2),
        WeeklyItem(snapshot_date='2024-09-12', fiscal_qtr_week_name='FY24Q4W9', rtm='Carrier', sub_rtm='CT', sub_lob='iPhone 15 Pro Max', total_so=2, lte_7_days_ub=2),
        WeeklyItem(snapshot_date='2024-09-12', fiscal_qtr_week_name='FY24Q4W9', rtm='Carrier', sub_rtm='CT', sub_lob='iPhone 15 Pro', total_so=2, lte_7_days_ub=2),
    ]

    data_except = [
        WeeklyItem(snapshot_date='2024-09-12', fiscal_qtr_week_name='FY24Q4W9', rtm='Mono Brand', sub_rtm='All', sub_lob='All', total_so=8, lte_7_days_ub=8),
        WeeklyItem(snapshot_date='2024-09-12', fiscal_qtr_week_name='FY24Q4W9', rtm='Carrier', sub_rtm='All', sub_lob='All', total_so=8, lte_7_days_ub=8),
    ]
    # 执行要测试的方法
    ret = UbWeekRecall.group_by_rtm(data_source)
    # 对比执行结果是否预期
    for i in range(len(ret)):
        assert ret[i].snapshot_date == data_except[i].snapshot_date
        assert ret[i].fiscal_qtr_week_name == data_except[i].fiscal_qtr_week_name
        assert ret[i].rtm == data_except[i].rtm
        assert ret[i].sub_rtm == data_except[i].sub_rtm
        assert ret[i].sub_lob == data_except[i].sub_lob
        assert ret[i].sub_lob == data_except[i].sub_lob
        assert ret[i].total_so == data_except[i].total_so
        assert ret[i].lte_7_days_ub == data_except[i].lte_7_days_ub


# @pytest.mark.skip(reason='')
def test_week_group_by_region():
    # 构造源数据类
    data_source = [
        WeeklyItem(snapshot_date='2024-09-12', fiscal_qtr_week_name='FY24Q4W9',
                   rtm='Mono Brand', sub_rtm='MONO', sub_lob='iPhone 15 Pro Max', total_so=2, lte_7_days_ub=2),
        WeeklyItem(snapshot_date='2024-09-12', fiscal_qtr_week_name='FY24Q4W9',
                   rtm='Mono Brand', sub_rtm='MONO', sub_lob='iPhone 15 Pro', total_so=2, lte_7_days_ub=2),
        WeeklyItem(snapshot_date='2024-09-12', fiscal_qtr_week_name='FY24Q4W9',
                   rtm='Mono Brand', sub_rtm='Lifestyle', sub_lob='iPhone 15 Pro Max', total_so=2, lte_7_days_ub=2),
        WeeklyItem(snapshot_date='2024-09-12', fiscal_qtr_week_name='FY24Q4W9',
                   rtm='Mono Brand', sub_rtm='Lifestyle', sub_lob='iPhone 15 Pro', total_so=2, lte_7_days_ub=2),

        WeeklyItem(snapshot_date='2024-09-12', fiscal_qtr_week_name='FY24Q4W9', rtm='Carrier',
                   sub_rtm='CU', sub_lob='iPhone 15 Pro Max', total_so=2, lte_7_days_ub=2),
        WeeklyItem(snapshot_date='2024-09-12', fiscal_qtr_week_name='FY24Q4W9', rtm='Carrier',
                   sub_rtm='CU', sub_lob='iPhone 15 Pro', total_so=2, lte_7_days_ub=2),
        WeeklyItem(snapshot_date='2024-09-12', fiscal_qtr_week_name='FY24Q4W9', rtm='Carrier',
                   sub_rtm='CT', sub_lob='iPhone 15 Pro Max', total_so=2, lte_7_days_ub=2),
        WeeklyItem(snapshot_date='2024-09-12', fiscal_qtr_week_name='FY24Q4W9', rtm='Carrier',
                   sub_rtm='CT', sub_lob='iPhone 15 Pro', total_so=2, lte_7_days_ub=2),
    ]

    data_except = [
        WeeklyItem(snapshot_date='2024-09-12', fiscal_qtr_week_name='FY24Q4W9', rtm='All', sub_rtm='All', sub_lob='All', total_so=16, lte_7_days_ub=16),

    ]
    # 执行要测试的方法
    ret = UbWeekRecall.group_by_region(data_source)
    # 对比执行结果是否预期
    for i in range(len(ret)):
        assert ret[i].snapshot_date == data_except[i].snapshot_date
        assert ret[i].fiscal_qtr_week_name == data_except[i].fiscal_qtr_week_name
        assert ret[i].rtm == data_except[i].rtm
        assert ret[i].sub_rtm == data_except[i].sub_rtm
        assert ret[i].sub_lob == data_except[i].sub_lob
        assert ret[i].sub_lob == data_except[i].sub_lob
        assert ret[i].total_so == data_except[i].total_so
        assert ret[i].lte_7_days_ub == data_except[i].lte_7_days_ub

def test_rolling_weeks():
    snapshot_date = '2024-09-23'
    # 默认rolling5周
    origin_weeks = WeeklyUbTrackingSummaryDi.query_rolling_weeks(snapshot_date)
    assert len(origin_weeks) <= 5
    
    # 周数组升序排
    fiscal_weeks = [FiscalWeek(week).fiscal_week_int for week in origin_weeks]
    sorted_week = sorted(fiscal_weeks)
    assert fiscal_weeks == sorted_week
    
    origin_weeks == ["FY24Q4W9","FY24Q4W10","FY24Q4W11","FY24Q4W12","FY24Q4W13"]
