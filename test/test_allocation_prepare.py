########## must at head ############
import sys
import os

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(SCRIPT_DIR))
##########      end     ############

import pytest
from util.fast_lite_base import fast_lite_engine
from data.allocation_prepare_data import TblAllocationPrepare
from service.allocation_prepare_service import get_allocation_prepare_detail_service,get_open_backlog_less_than_monday
from util.const import *
from service.cpf_sell_in_demand_service import sell_in_demand_with_tags

@pytest.mark.skip(reason="pass")
def test_create_table():
    try:
        TblAllocationPrepare.__table__.create(fast_lite_engine)
        assert 1 == 1
    except Exception as e:
        print("created.")
        assert 1 == 1

@pytest.mark.skip(reason="pass")
def test_get_detail():
    try:
        ret = TblAllocationPrepare.get_detail(1)
        assert ret.get(StrFiscalWeekYear) == 202328
    except Exception as e:
        print(f"error >>> ${e}")

@pytest.mark.skip(reason="pass")
def test_get_detail_service():
    ret = get_allocation_prepare_detail_service(1, 202328)
    assert ret.get(StrFiscalWeekYear) == 202328

@pytest.mark.skip(reason="pass") 
def test_get_detail_service_raise():
    with pytest.raises(ErrorExcept) as e:
        get_allocation_prepare_detail_service(1, 202301)
    assert e.value.code == ErrCode.ValidationError and e.value.err_msg == UnreadyDataError

@pytest.mark.skip(reason="pass")
def test_sell_in_demand_with_tags():
    sell_in_demand_with_tags('2023-07-07')


def test_query_less():
    ret = get_open_backlog_less_than_monday('Mono',202350)
    print(ret)
    for i in ret:
        print(i)
