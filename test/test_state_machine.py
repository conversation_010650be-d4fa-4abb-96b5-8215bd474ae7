########## must at head ############
import sys
import os

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(SCRIPT_DIR))
##########      end     ############

from domain.demand.impl.state_machine import StateProxy, set_state_to_DB, IdealDemandState


def test_state_machine():
    fiscal_week = "FY24Q3W3"
    demand = "ideal_demand"

    assert IdealDemandState.WaitingForRTMSetup.sub_progress() == 2

    # rollback 数据库
    set_state_to_DB(fiscal_week, demand, 0)
    
    state_proxy = StateProxy(fiscal_week, demand)
    assert state_proxy.current_state() == IdealDemandState.NotStarted

    state_proxy.do_initialize_y()
    assert state_proxy.current_state() == IdealDemandState.WaitingToSetup
    
    state_proxy.do_cpf_publish_y()
    assert state_proxy.current_state() == IdealDemandState.WaitingForRTMSetup

    state_proxy.do_rtm_publish("Multi")
    assert state_proxy.current_state() == IdealDemandState.WaitingForCalculation
    
    state_proxy.do_calculate_ideal_demand()
    assert state_proxy.current_state() == IdealDemandState.CalculationCompleted
    
    assert state_proxy.get_rtm_state("Multi") == 1
