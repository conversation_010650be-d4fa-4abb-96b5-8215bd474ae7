########## must at head ############
import sys
import os

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(SCRIPT_DIR))
##########      end     ############

import pytest
from data.dmp_dw_common_data import DimPubProdInfo
from data.cascade_filter_data import DimFastBusinessSoldtoMapping
from data.cpf_data_source import OdsFastCPFTWOSiPhone
from util.gc_dmp_datasource import ExDmpEngine

TEST_LOB = 'iPhone'

@pytest.mark.skip(reason="pass")
def test_mock_value():
    ret = DimPubProdInfo.get_valid_model(TEST_LOB)
    print(ret)


@pytest.mark.skip(reason="pass")
def test_rtm_business_type():
    ret = DimFastBusinessSoldtoMapping.get_rtm_business_type(202323)
    print(ret)

@pytest.mark.skip(reason="pass")
def test_rtm_business_type():
    ret = DimFastBusinessSoldtoMapping.get_business_type_relation(202323)
    print(ret)

@pytest.mark.skip(reason="pass")
def test_create_table():
    try:
        OdsFastCPFTWOSiPhone.__table__.create(ExDmpEngine)
        assert 1 == 1
    except Exception as e:
        print("created.")
        assert 1 == 1