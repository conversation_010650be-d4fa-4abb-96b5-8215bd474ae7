########## must at head ############
import os
import sys
from domain.dashboard.impl.po_delinquent_impl import (init_channel_view_schema, init_product_view_schema,
                                                      get_channel_view_data, get_product_view_data)

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(SCRIPT_DIR))
##########      end     ############

# usage
# ENV=dev pytest -s test/test_esr.py

CHANNEL_MONO_DATA = [
    {
        "sub_lob": "iPhone 15 Pro Max",
        "rtm": "Mono",
        "sub_rtm": "Lifestyle",
        "open_po": 5,
        "cond_1": 1,
        "cond_2": 1,
        "cond_3": 1,
        "cond_4": 1,
        "cond_5": 1
    },
    {
        "sub_lob": "iPhone 15 Pro",
        "rtm": "Mono",
        "sub_rtm": "Lifestyle",
        "open_po": 5,
        "cond_1": 1,
        "cond_2": 1,
        "cond_3": 1,
        "cond_4": 1,
        "cond_5": 1
    },
    {
        "sub_lob": "iPhone 15 Pro Max",
        "rtm": "Mono",
        "sub_rtm": "Mono AAR+",
        "open_po": 5,
        "cond_1": 1,
        "cond_2": 1,
        "cond_3": 1,
        "cond_4": 1,
        "cond_5": 1
    },
    {
        "sub_lob": "iPhone 15 Pro",
        "rtm": "Mono",
        "sub_rtm": "Mono AAR+",
        "open_po": 5,
        "cond_1": 1,
        "cond_2": 1,
        "cond_3": 1,
        "cond_4": 1,
        "cond_5": 1
    },
    {
        "sub_lob": "iPhone 15 Pro Max",
        "rtm": "Mono",
        "sub_rtm": "All",
        "open_po": 10,
        "cond_1": 2,
        "cond_2": 2,
        "cond_3": 2,
        "cond_4": 2,
        "cond_5": 2
    },
    {
        "sub_lob": "iPhone 15 Pro",
        "rtm": "Mono",
        "sub_rtm": "All",
        "open_po": 10,
        "cond_1": 2,
        "cond_2": 2,
        "cond_3": 2,
        "cond_4": 2,
        "cond_5": 2
    }
]

CHANNEL_MULTI_DATA = [
    {
        "sub_lob": "iPhone 15 Pro Max",
        "rtm": "Multi",
        "sub_rtm": "Duty Free",
        "open_po": 5,
        "cond_1": 1,
        "cond_2": 1,
        "cond_3": 1,
        "cond_4": 1,
        "cond_5": 1
    },
    {
        "sub_lob": "iPhone 15 Pro",
        "rtm": "Multi",
        "sub_rtm": "Duty Free",
        "open_po": 5,
        "cond_1": 1,
        "cond_2": 1,
        "cond_3": 1,
        "cond_4": 1,
        "cond_5": 1
    },
    {
        "sub_lob": "iPhone 15 Pro Max",
        "rtm": "Multi",
        "sub_rtm": "Mass Merchant",
        "open_po": 5,
        "cond_1": 1,
        "cond_2": 1,
        "cond_3": 1,
        "cond_4": 1,
        "cond_5": 1
    },
    {
        "sub_lob": "iPhone 15 Pro",
        "rtm": "Multi",
        "sub_rtm": "Mass Merchant",
        "open_po": 5,
        "cond_1": 1,
        "cond_2": 1,
        "cond_3": 1,
        "cond_4": 1,
        "cond_5": 1
    },
    {
        "sub_lob": "iPhone 15 Pro Max",
        "rtm": "Multi",
        "sub_rtm": "All",
        "open_po": 10,
        "cond_1": 2,
        "cond_2": 2,
        "cond_3": 2,
        "cond_4": 2,
        "cond_5": 2
    },
    {
        "sub_lob": "iPhone 15 Pro",
        "rtm": "Multi",
        "sub_rtm": "All",
        "open_po": 10,
        "cond_1": 2,
        "cond_2": 2,
        "cond_3": 2,
        "cond_4": 2,
        "cond_5": 2
    }
]

PRODUCT_IPHONE_15_PRO_MAX_DATA = [
    {
        "sub_lob": "iPhone 15 Pro Max",
        "nand": "256GB",
        "color": "BLACK TI",
        "open_po": 5,
        "cond_1": 1,
        "cond_2": 1,
        "cond_3": 1,
        "cond_4": 1,
        "cond_5": 1
    },
    {
        "sub_lob": "iPhone 15 Pro Max",
        "nand": "256GB",
        "color": "BLUE TI",
        "open_po": 5,
        "cond_1": 1,
        "cond_2": 1,
        "cond_3": 1,
        "cond_4": 1,
        "cond_5": 1
    },
    {
        "sub_lob": "iPhone 15 Pro Max",
        "nand": "256GB",
        "color": "All",
        "open_po": 10,
        "cond_1": 2,
        "cond_2": 2,
        "cond_3": 2,
        "cond_4": 2,
        "cond_5": 2
    },
    {
        "sub_lob": "iPhone 15 Pro Max",
        "nand": "1TB",
        "color": "BLACK TI",
        "open_po": 5,
        "cond_1": 1,
        "cond_2": 1,
        "cond_3": 1,
        "cond_4": 1,
        "cond_5": 1
    },
    {
        "sub_lob": "iPhone 15 Pro Max",
        "nand": "1TB",
        "color": "BLUE TI",
        "open_po": 5,
        "cond_1": 1,
        "cond_2": 1,
        "cond_3": 1,
        "cond_4": 1,
        "cond_5": 1
    },
    {
        "sub_lob": "iPhone 15 Pro Max",
        "nand": "1TB",
        "color": "All",
        "open_po": 10,
        "cond_1": 2,
        "cond_2": 2,
        "cond_3": 2,
        "cond_4": 2,
        "cond_5": 2
    },
    {
        "sub_lob": "iPhone 15 Pro Max",
        "nand": "All",
        "color": "All",
        "open_po": 20,
        "cond_1": 4,
        "cond_2": 4,
        "cond_3": 4,
        "cond_4": 4,
        "cond_5": 4
    }
]

PRODUCT_IPHONE_15_PRO_DATA = [
    {
        "sub_lob": "iPhone 15 Pro",
        "nand": "256GB",
        "color": "BLACK TI",
        "open_po": 5,
        "cond_1": 1,
        "cond_2": 1,
        "cond_3": 1,
        "cond_4": 1,
        "cond_5": 1
    },
    {
        "sub_lob": "iPhone 15 Pro",
        "nand": "256GB",
        "color": "BLUE TI",
        "open_po": 5,
        "cond_1": 1,
        "cond_2": 1,
        "cond_3": 1,
        "cond_4": 1,
        "cond_5": 1
    },
    {
        "sub_lob": "iPhone 15 Pro",
        "nand": "256GB",
        "color": "All",
        "open_po": 10,
        "cond_1": 2,
        "cond_2": 2,
        "cond_3": 2,
        "cond_4": 2,
        "cond_5": 2
    },
    {
        "sub_lob": "iPhone 15 Pro",
        "nand": "1TB",
        "color": "BLACK TI",
        "open_po": 5,
        "cond_1": 1,
        "cond_2": 1,
        "cond_3": 1,
        "cond_4": 1,
        "cond_5": 1
    },
    {
        "sub_lob": "iPhone 15 Pro",
        "nand": "1TB",
        "color": "BLUE TI",
        "open_po": 5,
        "cond_1": 1,
        "cond_2": 1,
        "cond_3": 1,
        "cond_4": 1,
        "cond_5": 1
    },
    {
        "sub_lob": "iPhone 15 Pro",
        "nand": "1TB",
        "color": "All",
        "open_po": 10,
        "cond_1": 2,
        "cond_2": 2,
        "cond_3": 2,
        "cond_4": 2,
        "cond_5": 2
    },
    {
        "sub_lob": "iPhone 15 Pro",
        "nand": "All",
        "color": "All",
        "open_po": 20,
        "cond_1": 4,
        "cond_2": 4,
        "cond_3": 4,
        "cond_4": 4,
        "cond_5": 4
    }
]


def test_product_view_data():
    # 示例数据
    data = PRODUCT_IPHONE_15_PRO_MAX_DATA + PRODUCT_IPHONE_15_PRO_DATA

    # 构造前端返回结构的数据并汇总
    res = init_product_view_schema(data)

    # case 1: 最外层Total行
    total_product_view_data = res[0]
    assert (
            total_product_view_data["sub_lob"] == "Total"
            and total_product_view_data["nand"] is None
            and total_product_view_data["color"] is None
    )
    assert total_product_view_data["open_po"] == 40 and total_product_view_data["cond_1"] == 8

    tmp_total_open_po = 0
    tmp_total_open_po_cond_1 = 0
    for item in res[1:]:
        tmp_total_open_po += item["open_po"]
        tmp_total_open_po_cond_1 += item["cond_1"]
    assert (
            total_product_view_data["open_po"] == tmp_total_open_po
            and total_product_view_data['cond_1'] == tmp_total_open_po_cond_1
    )

    # case 2: iPhone 15 Pro Max的整体汇总行(nand=all; color=all)
    iphone_15_pro_max_product_view_data = res[1]
    assert (
            iphone_15_pro_max_product_view_data["sub_lob"] == 'iPhone 15 Pro Max'
            and iphone_15_pro_max_product_view_data["nand"] == 'All'
            and iphone_15_pro_max_product_view_data["color"] is None
    )
    assert iphone_15_pro_max_product_view_data["open_po"] == 20 and iphone_15_pro_max_product_view_data["cond_1"] == 4
    # 所有nand->color下的汇总求和数据
    tmp_15_pro_max_total_open_po = 0
    tmp_15_pro_max_total_open_po_cond_1 = 0
    for item in iphone_15_pro_max_product_view_data["child"]:
        tmp_15_pro_max_total_open_po += item["open_po"]
        tmp_15_pro_max_total_open_po_cond_1 += item["cond_1"]
    assert (
            iphone_15_pro_max_product_view_data["open_po"] == tmp_15_pro_max_total_open_po
            and iphone_15_pro_max_product_view_data["cond_1"] == tmp_15_pro_max_total_open_po_cond_1
    )

    # case 3: iPhone 15 Pro Max的nand=256GB的汇总行
    iphone_15_pro_max_256gb_product_view_data = iphone_15_pro_max_product_view_data['child'][0]
    assert (
            iphone_15_pro_max_256gb_product_view_data["sub_lob"] == 'iPhone 15 Pro Max'
            and iphone_15_pro_max_256gb_product_view_data["nand"] == '256GB'
            and iphone_15_pro_max_256gb_product_view_data["color"] == 'All'
    )
    assert (
            iphone_15_pro_max_256gb_product_view_data["open_po"] == 10
            and iphone_15_pro_max_256gb_product_view_data["cond_1"] == 2
    )
    # 具体(256GB) nand->color下的汇总求和数据
    tmp_256gb_open_po = 0
    tmp_256gb_cond_1 = 0
    for item in iphone_15_pro_max_256gb_product_view_data["child"]:
        tmp_256gb_open_po += item["open_po"]
        tmp_256gb_cond_1 += item["cond_1"]
    assert (
            iphone_15_pro_max_256gb_product_view_data["open_po"] == tmp_256gb_open_po
            and iphone_15_pro_max_256gb_product_view_data['cond_1'] == tmp_256gb_cond_1
    )


def test_channel_view_data():
    # 示例数据
    data = CHANNEL_MONO_DATA + CHANNEL_MULTI_DATA

    # 构造前端返回结构的数据并汇总
    res = init_channel_view_schema(data)

    # case 1: 最外层Total行
    total_channel_view_data = res[0]
    assert total_channel_view_data["rtm"] == "Total"
    assert total_channel_view_data["child"][0]['open_po'] == 40 and total_channel_view_data["child"][0]["cond_1"] == 8

    tmp_total_open_po = 0
    tmp_total_open_po_cond_1 = 0
    for item in res[1:]:  # 每个rtm
        tmp_total_open_po += item["child"][0]["open_po"]  # item["child"][0] 是每一个rtm汇总行(sub_lob=all,sub_rtm=all)
        tmp_total_open_po_cond_1 += item["child"][0]["cond_1"]
    assert (
            total_channel_view_data["child"][0]["open_po"] == tmp_total_open_po
            and total_channel_view_data["child"][0]['cond_1'] == tmp_total_open_po_cond_1
    )

    # case 2: 具体rtm下具体sub_rtm的各个sub lob的整体汇总行. Mono -> Lifestyle -> all
    mono_lifestyle_channel_view_data = res[1]["child"][1]  # res[1][0]是 Mon->All; res[1][1]是 Mon->Lifestyle
    assert (
            mono_lifestyle_channel_view_data["rtm"] == 'Mono'
            and mono_lifestyle_channel_view_data["sub_rtm"] == 'Lifestyle'
            and mono_lifestyle_channel_view_data["sub_lob"] == 'All'
    )
    assert mono_lifestyle_channel_view_data["open_po"] == 10 and mono_lifestyle_channel_view_data["cond_1"] == 2
    # Mono -> Lifestyle下所有的sub_lob汇总求和数据
    tmp_sub_lob_total_open_po = 0
    tmp_sub_lob_total_open_po_cond_1 = 0
    for item in mono_lifestyle_channel_view_data["child"]:
        tmp_sub_lob_total_open_po += item["open_po"]
        tmp_sub_lob_total_open_po_cond_1 += item["cond_1"]
    assert (
            mono_lifestyle_channel_view_data["open_po"] == tmp_sub_lob_total_open_po
            and mono_lifestyle_channel_view_data["cond_1"] == tmp_sub_lob_total_open_po_cond_1
    )

    # case3: 具体rtm下所有sub_rtm的所有sub_lob的汇总求和信息. Mono -> All -> All
    mono_rtm_total_open_po_data = res[1]["child"][0]  # res[1][0]是 Mon->All; res[1][1]是 Mon->Lifestyle
    tmp_rtm_total_open_po = 0
    tmp_rtm_lob_total_open_po_cond_1 = 0
    for item in res[1]["child"][1:]:
        tmp_rtm_total_open_po += item["open_po"]
        tmp_rtm_lob_total_open_po_cond_1 += item["cond_1"]
    assert mono_rtm_total_open_po_data["open_po"] == 20 and mono_rtm_total_open_po_data["cond_1"] == 4
    assert (
            mono_rtm_total_open_po_data["open_po"] == tmp_rtm_total_open_po
            and mono_rtm_total_open_po_data["cond_1"] == tmp_rtm_lob_total_open_po_cond_1
    )


def test_api_channel_view_data():
    region = "China mainland"
    region = "Hong Kong"
    region = "Taiwan"
    lob = "iPhone"
    sub_lobs = [
        "iPhone 15 Pro Max",
        "iPhone 15 Pro",
        "iPhone 15 Plus",
        "iPhone 15",
        "iPhone 14 Plus",
        "iPhone 14",
        "iPhone 13"
    ]

    latest_refresh_time, res = get_channel_view_data(region=region, lob=lob, sub_lobs=sub_lobs)

    # case 1: 最外层Total行
    total_channel_view_data = res[0]
    assert total_channel_view_data["rtm"] == "Total"

    tmp_total_open_po = 0
    tmp_total_open_po_cond_1 = 0
    for item in res[1:]:  # 每个rtm
        tmp_total_open_po += item["child"][0]["open_po"]  # item["child"][0] 是每一个rtm汇总行(sub_lob=all,sub_rtm=all)
        tmp_total_open_po_cond_1 += item["child"][0]["cond_1"]

    assert (
            total_channel_view_data["child"][0]["open_po"] == tmp_total_open_po
            and total_channel_view_data["child"][0]['cond_1'] == tmp_total_open_po_cond_1
    )
    print("*" * 50)
    print(f"total_open_po: {tmp_total_open_po}")
    print(f"total_open_po_cond_1: {tmp_total_open_po_cond_1}")
    print("*" * 50)

    # case 2: 具体rtm下具体sub_rtm的各个sub lob的整体汇总行. Mono -> Lifestyle -> all
    mono_lifestyle_channel_view_data = res[1]["child"][1]  # res[1][0]是 Mon->All; res[1][1]是 Mon->Lifestyle
    # Mono -> Lifestyle下所有的sub_lob汇总求和数据
    tmp_sub_lob_total_open_po = 0
    tmp_sub_lob_total_open_po_cond_1 = 0
    for item in mono_lifestyle_channel_view_data["child"]:
        tmp_sub_lob_total_open_po += item["open_po"]
        tmp_sub_lob_total_open_po_cond_1 += item["cond_1"]
    assert (
            mono_lifestyle_channel_view_data["open_po"] == tmp_sub_lob_total_open_po
            and mono_lifestyle_channel_view_data["cond_1"] == tmp_sub_lob_total_open_po_cond_1
    )
    print("-" * 50)
    print(f"Mono->Lifestyle: sub_lob_total_open_po: {tmp_sub_lob_total_open_po}")
    print(f"Mono->Lifestyle: sub_lob_total_open_po_cond_1: {tmp_sub_lob_total_open_po_cond_1}")
    print("-" * 50)

    # case3: 具体rtm下所有sub_rtm的所有sub_lob的汇总求和信息. Mono -> All -> All
    mono_rtm_total_open_po_data = res[1]["child"][0]  # res[1][0]是 Mon->All; res[1][1]是 Mon->Lifestyle
    tmp_rtm_total_open_po = 0
    tmp_rtm_lob_total_open_po_cond_1 = 0
    for item in res[1]["child"][1:]:
        tmp_rtm_total_open_po += item["open_po"]
        tmp_rtm_lob_total_open_po_cond_1 += item["cond_1"]
    assert (
            mono_rtm_total_open_po_data["open_po"] == tmp_rtm_total_open_po
            and mono_rtm_total_open_po_data["cond_1"] == tmp_rtm_lob_total_open_po_cond_1
    )
    print("=" * 50)
    print(f"Mono: rtm_total_open_po: {tmp_rtm_total_open_po}")
    print(f"Mono: rtm_lob_total_open_po_cond_1: {tmp_rtm_lob_total_open_po_cond_1}")
    print("=" * 50)


def test_api_product_view_data():
    region = "China mainland"
    region = "Hong Kong"
    # region = "Taiwan"
    # rtm = "All"
    rtm = "Carrier"
    # rtm = "Retail Partner"
    # rtm = "Mono"
    sub_rtm = "All"
    sub_rtm = 'China Telecom Macau'
    # sub_rtm = "Chunghwa"
    # sub_rtm = "Lifestyle"
    # sub_rtm = "China Broadcast"
    # sub_rtm = "China Mobile HK"
    lob = 'iPhone'
    sub_lobs = [
        "iPhone 15 Pro Max",
        "iPhone 15 Pro",
        "iPhone 15 Plus",
        "iPhone 15",
        "iPhone 14 Plus",
        "iPhone 14",
        "iPhone 13"
    ]

    latest_refresh_time, res = get_product_view_data(region=region, rtm=rtm, sub_lobs=sub_lobs, sub_rtm=sub_rtm, lob=lob)

    # case 1: 最外层Total行
    total_product_view_data = res[0]
    assert (
            total_product_view_data["sub_lob"] == "Total"
            and total_product_view_data["nand"] is None
            and total_product_view_data["color"] is None
    )
    tmp_total_open_po = 0
    tmp_total_open_po_cond_1 = 0
    for item in res[1:]:
        tmp_total_open_po += item["open_po"]
        tmp_total_open_po_cond_1 += item["cond_1"]
    assert (
            total_product_view_data["open_po"] == tmp_total_open_po
            and total_product_view_data['cond_1'] == tmp_total_open_po_cond_1
    )
    print("*" * 50)
    print(f"total_open_po: {tmp_total_open_po}")
    print(f"total_open_po_cond_1: {tmp_total_open_po_cond_1}")
    print("*" * 50)

    # case 2: iPhone 15 Pro Max的整体汇总行(nand=all; color=all)
    iphone_15_pro_max_product_view_data = res[1]
    assert (
            iphone_15_pro_max_product_view_data["sub_lob"] == 'iPhone 15 Pro Max'
            and iphone_15_pro_max_product_view_data["nand"] == 'All'
            and iphone_15_pro_max_product_view_data["color"] is None
    )
    # 所有nand->color下的汇总求和数据
    tmp_15_pro_max_total_open_po = 0
    tmp_15_pro_max_total_open_po_cond_1 = 0
    for item in iphone_15_pro_max_product_view_data["child"]:
        tmp_15_pro_max_total_open_po += item["open_po"]
        tmp_15_pro_max_total_open_po_cond_1 += item["cond_1"]
    assert (
            iphone_15_pro_max_product_view_data["open_po"] == tmp_15_pro_max_total_open_po
            and iphone_15_pro_max_product_view_data["cond_1"] == tmp_15_pro_max_total_open_po_cond_1
    )
    print("-" * 50)
    print(f"iPhone 15 Pro Max: 15_pro_max_total_open_po: {tmp_15_pro_max_total_open_po}")
    print(f"iPhone 15 Pro Max: 15_pro_max_total_open_po_cond_1: {tmp_15_pro_max_total_open_po_cond_1}")
    print("-" * 50)

    # case 3: iPhone 15 Pro Max的nand=256GB的汇总行
    iphone_15_pro_max_256gb_product_view_data = iphone_15_pro_max_product_view_data['child'][0]
    assert (
            iphone_15_pro_max_256gb_product_view_data["sub_lob"] == 'iPhone 15 Pro Max'
            and iphone_15_pro_max_256gb_product_view_data["nand"] == '256GB'
            and iphone_15_pro_max_256gb_product_view_data["color"] == 'All'
    )
    # 具体(256GB) nand->color下的汇总求和数据
    tmp_256gb_open_po = 0
    tmp_256gb_cond_1 = 0
    for item in iphone_15_pro_max_256gb_product_view_data["child"]:
        tmp_256gb_open_po += item["open_po"]
        tmp_256gb_cond_1 += item["cond_1"]
    assert (
            iphone_15_pro_max_256gb_product_view_data["open_po"] == tmp_256gb_open_po
            and iphone_15_pro_max_256gb_product_view_data['cond_1'] == tmp_256gb_cond_1
    )
    print("=" * 50)
    print(f"iPhone 15 Pro Max->256GB: 256gb_open_po: {tmp_256gb_open_po}")
    print(f"iPhone 15 Pro Max->256GB: 256gb_cond_1: {tmp_256gb_cond_1}")
    print("=" * 50)
