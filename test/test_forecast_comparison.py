########## must at head ############
import os
import sys

from domain.dashboard.impl.forecast_comparison.forecast_comparison_impl import calculate_diff_and_percentage, \
    get_comparison_result_data

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(SCRIPT_DIR))
##########      end     ############

# usage
# ENV=dev pytest -s test/test_esr.py

ML_NATIONAL_FORECAST_RECORDS = {
        'cw': 616863.0,
        'cw1': 689755.0,
        'cw2': 689786.0,
        'cw3': 733988.0,
        'cw4': 728698.0,
        'total': 3459090.0,
        'total_lw': 3170844.0,
        'total_lw_minus_total': -288246.0
}
ML_RTM_FORECAST_RECORDS = {
        'cw': 657812.0,
        'cw1': 700541.0,
        'cw2': 919047.0,
        'cw3': 966736.0,
        'cw4': 881937.0,
        'total': 4126073.0,
        'total_lw': 3940117.0,
        'total_lw_minus_total': -185956.0
}
RTM_FORECAST_RECORDS = [
    {
        'rtm': 'Multi',
        'cw': 169283.0,
        'cw1': 146005.0,
        'cw2': 143969.0,
        'cw3': 146769.0,
        'cw4': 163803.0
    },
    {
        'rtm': 'Online',
        'cw': 167889.0,
        'cw1': 267850.0,
        'cw2': 429744.0,
        'cw3': 494859.0,
        'cw4': 349173.0
    },
    {
        'rtm': 'Mono',
        'cw': 153416.0,
        'cw1': 135757.0,
        'cw2': 163994.0,
        'cw3': 142894.0,
        'cw4': 194358.0
    },
    {
        'rtm': 'Carrier',
        'cw': 167224.0,
        'cw1': 150929.0,
        'cw2': 181340.0,
        'cw3': 182214.0,
        'cw4': 174603.0
    }
]
DFA_FORECAST_RECORDS = {
        'cw': 2484690.0,
        'cw1': 8187471.0,
        'cw2': 1312381.0,
        'cw3': 8931848.0,
        'cw4': 8986775.0,
        'total': 29903165.0,
        'total_lw': 22382219.0,
        'total_lw_minus_total': -7520946.0
}
ML_DFA_FORECAST_RECORDS = {
        'cw': 616863.0,
        'cw1': 689755.0,
        'cw2': 689786.0,
        'cw3': 733988.0,
        'cw4': 728698.0
}


def test_calculate_diff_and_percentage():
    # case 1
    ml_vs_dfa = calculate_diff_and_percentage(s1=ML_NATIONAL_FORECAST_RECORDS,
                                              s2=DFA_FORECAST_RECORDS,
                                              column_names=['cw', 'cw1', 'cw2', 'cw3', 'cw4',
                                                            'total', 'total_lw', 'total_lw_minus_total'],
                                              keywords='ml_vs_dfa')
    assert ml_vs_dfa['cw']['delta'] == -1867827.0
    assert ml_vs_dfa['cw']['delta'] == ML_NATIONAL_FORECAST_RECORDS['cw'] - DFA_FORECAST_RECORDS['cw']
    assert (ml_vs_dfa['cw']['variance'] ==
            (ML_NATIONAL_FORECAST_RECORDS['cw'] - DFA_FORECAST_RECORDS['cw']) / DFA_FORECAST_RECORDS['cw'])

    assert ml_vs_dfa['total_lw_minus_total']['variance'] == ml_vs_dfa['total']['variance'] - ml_vs_dfa['total_lw']['variance']

    print(f"\n ml vs dfa: "
          f"[cw] delta: {ml_vs_dfa['cw']['delta']}, "
          f"[cw] variance: {ml_vs_dfa['cw']['variance']}, "
          f"[total] variance: {ml_vs_dfa['total']['variance']}, "
          f"[total(LW)] variance: {ml_vs_dfa['total_lw']['variance']}, "
          f"[cw vs lw] variance: {ml_vs_dfa['total_lw_minus_total']['variance']}"
          )

    # case 2
    ml_rtm_vs_dfa = calculate_diff_and_percentage(s1=ML_RTM_FORECAST_RECORDS,
                                                  s2=DFA_FORECAST_RECORDS,
                                                  column_names=['cw', 'cw1', 'cw2', 'cw3', 'cw4',
                                                                'total', 'total_lw', 'total_lw_minus_total'],
                                                  keywords='ml_rtm_vs_dfa')

    assert ml_rtm_vs_dfa['cw']['delta'] == -1826878.0
    assert ml_rtm_vs_dfa['cw']['delta'] == ML_RTM_FORECAST_RECORDS['cw'] - DFA_FORECAST_RECORDS['cw']
    assert (ml_rtm_vs_dfa['cw']['variance'] ==
            (ML_RTM_FORECAST_RECORDS['cw'] - DFA_FORECAST_RECORDS['cw']) / DFA_FORECAST_RECORDS['cw'])

    assert ml_rtm_vs_dfa['total_lw_minus_total']['variance'] == ml_rtm_vs_dfa['total']['variance'] - ml_rtm_vs_dfa['total_lw']['variance']

    print(f"\n ml rtm vs dfa: "
          f"[cw] delta: {ml_rtm_vs_dfa['cw']['delta']}, "
          f"[cw] variance: {ml_rtm_vs_dfa['cw']['variance']}, "
          f"[total] variance: {ml_rtm_vs_dfa['total']['variance']}, "
          f"[total(LW)] variance: {ml_rtm_vs_dfa['total_lw']['variance']}, "
          f"[cw vs lw] variance: {ml_rtm_vs_dfa['total_lw_minus_total']['variance']}"
          )

    # case 3
    ml_rtm_vs_ml = calculate_diff_and_percentage(s1=ML_RTM_FORECAST_RECORDS,
                                                 s2=ML_NATIONAL_FORECAST_RECORDS,
                                                 column_names=['cw', 'cw1', 'cw2', 'cw3', 'cw4',
                                                               'total', 'total_lw', 'total_lw_minus_total'],
                                                 keywords='ml_rtm_vs_ml')

    assert ml_rtm_vs_ml['cw']['delta'] == 40949
    assert ml_rtm_vs_ml['cw']['delta'] == ML_RTM_FORECAST_RECORDS['cw'] - ML_NATIONAL_FORECAST_RECORDS['cw']
    assert (ml_rtm_vs_ml['cw']['variance'] ==
            (ML_RTM_FORECAST_RECORDS['cw'] - ML_NATIONAL_FORECAST_RECORDS['cw']) / ML_NATIONAL_FORECAST_RECORDS['cw'])

    assert ml_rtm_vs_ml['total_lw_minus_total']['variance'] == ml_rtm_vs_ml['total']['variance'] - \
           ml_rtm_vs_ml['total_lw']['variance']

    print(f"\n ml rtm vs ml: "
          f"[cw] delta: {ml_rtm_vs_ml['cw']['delta']}, "
          f"[cw] variance: {ml_rtm_vs_ml['cw']['variance']}, "
          f"[total] variance: {ml_rtm_vs_ml['total']['variance']}, "
          f"[total(LW)] variance: {ml_rtm_vs_ml['total_lw']['variance']}, "
          f"[cw vs lw] variance: {ml_rtm_vs_ml['total_lw_minus_total']['variance']}"
          )

    # case 4
    ml_rtm_vs_ml_dfa = calculate_diff_and_percentage(s1=ML_RTM_FORECAST_RECORDS,
                                                     s2=ML_DFA_FORECAST_RECORDS,
                                                     column_names=['cw', 'cw1', 'cw2', 'cw3', 'cw4'],
                                                     need_lw_minus_total=False,
                                                     keywords='ml_rtm_vs_ml_dfa')

    assert ml_rtm_vs_ml_dfa['cw']['delta'] == 40949
    assert ml_rtm_vs_ml_dfa['cw']['delta'] == ML_RTM_FORECAST_RECORDS['cw'] - ML_DFA_FORECAST_RECORDS['cw']
    assert (ml_rtm_vs_ml_dfa['cw']['variance'] ==
            (ML_RTM_FORECAST_RECORDS['cw'] - ML_DFA_FORECAST_RECORDS['cw']) /
            ML_DFA_FORECAST_RECORDS['cw'])

    assert ml_rtm_vs_ml_dfa['cw4']['delta'] == 153239
    assert ml_rtm_vs_ml_dfa['cw4']['delta'] == ML_RTM_FORECAST_RECORDS['cw4'] - ML_DFA_FORECAST_RECORDS['cw4']
    assert (ml_rtm_vs_ml_dfa['cw4']['variance'] ==
            (ML_RTM_FORECAST_RECORDS['cw4'] - ML_DFA_FORECAST_RECORDS['cw4']) /
            ML_DFA_FORECAST_RECORDS['cw4'])

    print(f"\n ml rtm vs ml dfa: "
          f"[cw] delta: {ml_rtm_vs_ml['cw']['delta']}, "
          f"[cw] variance: {ml_rtm_vs_ml['cw']['variance']}, "
          f"[cw4] delta: {ml_rtm_vs_ml['cw4']['delta']}, "
          f"[cw4] variance: {ml_rtm_vs_ml['cw4']['variance']}, "
          )


def test_api_get_comparison_overview():
    region = "China mainland"
    sub_lobs = [
        "iPhone 15 Pro Max",
        "iPhone 15 Pro",
        "iPhone 15 Plus",
        "iPhone 15",
        "iPhone 14 Plus",
        "iPhone 14",
        "iPhone 13"
    ]
    sub_lob = "All"
    fiscal_week = "FY24Q3W10"

    res = get_comparison_result_data(region=region, sub_lob=sub_lob, fiscal_week=fiscal_week)

    # case 1
    ml_vs_dfa = res["comparison"]["ML vs. DFA (%)"]
    ml_national_forecast = res["forecast"]["ML National (UB)"]
    dfa_forecast = res["forecast"]["DFA (UB)"]
    assert ml_vs_dfa['cw']['delta'] == ml_national_forecast['cw'] - dfa_forecast['cw']
    assert ml_vs_dfa['cw']['variance'] == (ml_national_forecast['cw'] - dfa_forecast['cw']) / dfa_forecast['cw']
    assert ml_vs_dfa['total_lw_minus_total']['variance'] == ml_vs_dfa['total']['variance'] - ml_vs_dfa['total_lw']['variance']

    print(f"\n ml vs dfa: "
          f"[cw] delta: {ml_vs_dfa['cw']['delta']}, "
          f"[cw] variance: {ml_vs_dfa['cw']['variance']}, "
          f"[total] variance: {ml_vs_dfa['total']['variance']}, "
          f"[total(LW)] variance: {ml_vs_dfa['total_lw']['variance']}, "
          f"[cw vs lw] variance: {ml_vs_dfa['total_lw_minus_total']['variance']}"
          )

    # case 2
    ml_rtm_vs_dfa = res["comparison"]["ML+RTM vs. DFA (%)"]
    ml_rtm_forecast = res["forecast"]["ML+RTM"]
    assert ml_rtm_vs_dfa['cw']['delta'] == ml_rtm_forecast['cw'] - dfa_forecast['cw']
    assert ml_rtm_vs_dfa['cw']['variance'] == (ml_rtm_forecast['cw'] - dfa_forecast['cw']) / dfa_forecast['cw']
    assert ml_rtm_vs_dfa['total_lw_minus_total']['variance'] == ml_rtm_vs_dfa['total']['variance'] - ml_rtm_vs_dfa['total_lw']['variance']

    print(f"\n ml rtm vs dfa: "
          f"[cw] delta: {ml_rtm_vs_dfa['cw']['delta']}, "
          f"[cw] variance: {ml_rtm_vs_dfa['cw']['variance']}, "
          f"[total] variance: {ml_rtm_vs_dfa['total']['variance']}, "
          f"[total(LW)] variance: {ml_rtm_vs_dfa['total_lw']['variance']}, "
          f"[cw vs lw] variance: {ml_rtm_vs_dfa['total_lw_minus_total']['variance']}"
          )

    # case 3
    ml_rtm_vs_ml = res["comparison"]["ML+RTM vs. ML (%)"]
    assert ml_rtm_vs_ml['cw']['delta'] == ml_rtm_forecast['cw'] - ml_national_forecast['cw']
    assert ml_rtm_vs_ml['cw']['variance'] == (ml_rtm_forecast['cw'] - ml_national_forecast['cw']) / ml_national_forecast['cw']
    assert ml_rtm_vs_ml['total_lw_minus_total']['variance'] == ml_rtm_vs_ml['total']['variance'] - ml_rtm_vs_ml['total_lw']['variance']
    print(f"\n ml rtm vs ml: "
          f"[cw] delta: {ml_rtm_vs_ml['cw']['delta']}, "
          f"[cw] variance: {ml_rtm_vs_ml['cw']['variance']}, "
          f"[total] variance: {ml_rtm_vs_ml['total']['variance']}, "
          f"[total(LW)] variance: {ml_rtm_vs_ml['total_lw']['variance']}, "
          f"[cw vs lw] variance: {ml_rtm_vs_ml['total_lw_minus_total']['variance']}"
          )

    # case 4
    ml_rtm_vs_ml_dfa = res["comparison"]["ML+RTM vs. ML+DFA (%)"]
    ml_dfa_forecast = res["forecast"]["ML+DFA (UB)"]
    assert ml_rtm_vs_ml_dfa['cw']['delta'] == ml_rtm_forecast['cw'] - ml_dfa_forecast['cw']
    assert ml_rtm_vs_ml_dfa['cw']['variance'] == (ml_rtm_forecast['cw'] - ml_dfa_forecast['cw']) / ml_dfa_forecast['cw']

    assert ml_rtm_vs_ml_dfa['cw4']['delta'] == ml_rtm_forecast['cw4'] - ml_dfa_forecast['cw4']
    assert ml_rtm_vs_ml_dfa['cw4']['variance'] == (ml_rtm_forecast['cw4'] - ml_dfa_forecast['cw4']) / ml_dfa_forecast['cw4']

    print(f"\n ml rtm vs ml dfa: "
          f"[cw] delta: {ml_rtm_vs_ml['cw']['delta']}, "
          f"[cw] variance: {ml_rtm_vs_ml['cw']['variance']}, "
          f"[cw4] delta: {ml_rtm_vs_ml['cw4']['delta']}, "
          f"[cw4] variance: {ml_rtm_vs_ml['cw4']['variance']}, "
          )
