########## must at head ############
import sys
import os

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(SCRIPT_DIR))
##########      end     ############

import pytest
from service.datasource_service import *
from data.cpf_data_source import *

@pytest.mark.skip(reason="")
def test_cpf_validate_data_1():
    '''
    表头校验
    '''
    df = pd.DataFrame({'sales_org_id': [1, 2, 3, 4, 4], 'sales_org': [1, 2, 3, 4, 5], 'model': [6, 7, 8, 9, 10]})
    with pytest.raises(ErrorExcept) as e:
        cpf_validate_data(df)
    assert e.value.code == ErrCode.FileUploadError and e.value.err_msg == FileUploadError.CPFSKUTableHeadError

@pytest.mark.skip(reason="")
def test_cpf_validate_data_2():
    '''
    sales_org_id 校验
    '''
    df = pd.DataFrame({
        'sales_org_id': [1, 2, 3, 4, 4],
        'sales_org': [1, 2, 3, 4, 5], 
        'model': [6, 7, 8, 9, 10], 
        'project_code': [6, 7, 8, 9, 10], 
        'mpn_id': [6, 7, 8, 9, 10], 
        'nand': [6, 7, 8, 9, 10], 
        'color': [6, 7, 8, 9, 10], 
        'odq': [6, 7, 8, 9, 10],
        'hr_lr': [6, 7, 8, 9, 10]
        })
    with pytest.raises(ErrorExcept) as e:
        cpf_validate_data(df)
    assert e.value.code == ErrCode.FileUploadError and e.value.err_msg.startswith(FileUploadError.SalesOrgIdError)

@pytest.mark.skip(reason="")
def test_cpf_validate_data_3():
    '''
    sales_org 校验
    '''
    df = pd.DataFrame({
        'sales_org_id': [1510, 1200, 1300, 1510, 1200],
        'sales_org': [1, 2, 3, 4, 5], 
        'model': [6, 7, 8, 9, 10], 
        'project_code': [6, 7, 8, 9, 10], 
        'mpn_id': [6, 7, 8, 9, 10], 
        'nand': [6, 7, 8, 9, 10], 
        'color': [6, 7, 8, 9, 10], 
        'odq': [6, 7, 8, 9, 10],
        'hr_lr': [6, 7, 8, 9, 10]
        })
    with pytest.raises(ErrorExcept) as e:
        cpf_validate_data(df)
    assert e.value.code == ErrCode.FileUploadError and e.value.err_msg.startswith(FileUploadError.SalesOrgError)

@pytest.mark.skip(reason="")
def test_cpf_validate_data_4():
    '''
    mpn_id 校验
    '''
    df = pd.DataFrame({
        'sales_org_id': [1510, 1200, 1300, 1510, 1200],
        'sales_org': ['China mainland', 'Hong Kong', 'Taiwan', 'China mainland', 'Hong Kong'], 
        'model': [6, 7, 8, 9, 10], 
        'project_code': [6, 7, 8, 9, 10], 
        'mpn_id': [6, 7, 8, 9, 10], 
        'nand': [6, 7, 8, 9, 10], 
        'color': [6, 7, 8, 9, 10], 
        'odq': [6, 7, 8, 9, 10],
        'hr_lr': [6, 7, 8, 9, 10]
        })
    with pytest.raises(ErrorExcept) as e:
        cpf_validate_data(df)
    assert e.value.code == ErrCode.FileUploadError and e.value.err_msg.startswith(FileUploadError.MPNInvalid)

@pytest.mark.skip(reason="")
def test_cpf_validate_data_5():
    '''
    odq 校验
    '''
    df = pd.DataFrame({
        'sales_org_id': [1510, 1200, 1300, 1510, 1200],
        'sales_org': ['China mainland', 'Hong Kong', 'Taiwan', 'China mainland', 'Hong Kong'], 
        'model': [6, 7, 8, 9, 10], 
        'project_code': [6, 7, 8, 9, 10], 
        'mpn_id': ['3K564AH/A', 'MPWN3E/A', 'MLK13PA/A', '3L282Z/A', 'MQ023PA/A'], 
        'nand': [6, 7, 8, 9, 10], 
        'color': [6, 7, 8, 9, 10], 
        'odq': [0, 7, 8, 9, 0],
        'hr_lr': [6, 7, 8, 9, 10]
        })
    with pytest.raises(ErrorExcept) as e:
        cpf_validate_data(df)
    assert e.value.code == ErrCode.FileUploadError and e.value.err_msg.startswith(FileUploadError.ODQMoreThanZero)

@pytest.mark.skip(reason="")
def test_cpf_validate_data_6():
    '''
    hr_lr 校验
    '''
    df = pd.DataFrame({
        'sales_org_id': [1510, 1200, 1300, 1510, 1200],
        'sales_org': ['China mainland', 'Hong Kong', 'Taiwan', 'China mainland', 'Hong Kong'], 
        'model': [6, 7, 8, 9, 10], 
        'project_code': [6, 7, 8, 9, 10], 
        'mpn_id': ['3K564AH/A', 'MPWN3E/A', 'MLK13PA/A', '3L282Z/A', 'MQ023PA/A'], 
        'nand': [6, 7, 8, 9, 10], 
        'color': [6, 7, 8, 9, 10], 
        'odq': [6, 7, 8, 9, 10],
        'hr_lr': [6, 7, 8, 9, 10]
        })
    with pytest.raises(ErrorExcept) as e:
        cpf_validate_data(df)
    assert e.value.code == ErrCode.FileUploadError and e.value.err_msg.startswith(FileUploadError.HRLRError)

@pytest.mark.skip(reason="")
def test_cpf_validate_data_7():
    '''
    两列数据 校验
    '''
    df = pd.DataFrame({
        'sales_org_id': [1510, 1200, 1300, 1510, 1200],
        'sales_org': ['China mainland', 'Hong Kong', 'Taiwan', 'Taiwan', 'Hong Kong'], 
        'model': [6, 7, 8, 9, 10], 
        'project_code': [6, 7, 8, 9, 10], 
        'mpn_id': ['3K564AH/A', 'MPWN3E/A', 'MLK13PA/A', '3L282Z/A', 'MQ023PA/A'], 
        'nand': [6, 7, 8, 9, 10], 
        'color': [6, 7, 8, 9, 10], 
        'odq': [6, 7, 8, 9, 10],
        'hr_lr': ['LR', 'LR', 'LR', 'HR', 'HR']
        })
    with pytest.raises(ErrorExcept) as e:
        cpf_validate_data(df)
    assert e.value.code == ErrCode.FileUploadError and e.value.err_msg.startswith(FileUploadError.CPFMismatchError)

@pytest.mark.skip(reason="")
def test_cpf_validate_data_8():
    '''
    两列数据 校验
    '''
    df = pd.DataFrame({
        'sales_org_id': [1510, 1200, 1300, 1510, 1200],
        'sales_org': ['China mainland', 'Hong Kong', 'Taiwan', 'China mainland', 'Hong Kong'], 
        'model': [6, 7, 8, 9, 10], 
        'project_code': [6, 7, 8, 9, 10], 
        'mpn_id': ['3K564AH/A', 'MPWN3E/A', 'MLK13PA/A', '3L282Z/A', '3L282Z/A'], 
        'nand': [6, 7, 8, 9, 10], 
        'color': [6, 7, 8, 9, 10], 
        'odq': [6, 7, 8, 9, 10],
        'hr_lr': ['LR', 'LR', 'LR', 'HR', 'HR']
        })
    with pytest.raises(ErrorExcept) as e:
        cpf_validate_data(df)
    assert e.value.code == ErrCode.FileUploadError and e.value.err_msg.startswith(FileUploadError.BusinessTypeMPNDuplicate)

@pytest.mark.skip(reason="")
def test_create_datasource_table():
    try:
        OdsFastCPFActiveSKUiPad.__table__.create(ExDmpEngine)
    except Exception:
        print('already exists.')
    assert 1 == 1

@pytest.mark.skip(reason="数据已够用")
def test_save_data_source_iPad():
    current_version = 4
    query_version = OdsFastCPFActiveSKUiPad.query_by_version(current_version)
    if query_version > 0:
        current_version += 1
    # df = pd.DataFrame({
    # 'sales_org_id': [1510, 1200, 1300, 1510, 1200],
    # 'sales_org': ['China mainland', 'Hong Kong', 'Taiwan', 'China mainland', 'Hong Kong'], 
    # 'model': ['iPad 9th Gen', 'iPad 9th Gen', 'iPad 9th Gen', 'iPad 9th Gen', 'iPad 9th Gen'], 
    # 'project_code': ['J181', 'J181', 'J181', 'J181', 'J181'], 
    # 'mpn_id': ['3K564AH/A', 'MPWN3E/A', 'MLK13PA/A', '3L282Z/A', '3L282Z/A'], 
    # 'nand': ['64GB', '64GB', '64GB', '64GB', '64GB'], 
    # 'color': ['SPC GRAY', 'SPC GRAY', 'SPC GRAY', 'SPC GRAY', 'SPC GRAY'], 
    # 'odq': [6, 7, 8, 9, 10],
    # 'hr_lr': ['LR', 'LR', 'LR', 'HR', 'HR']
    # })
    df = pd.read_excel("/Users/<USER>/Downloads/Active SKU List-iPad20230315 (1).xlsx")
    cfp_save_data_source(df, current_version, 'system', '2023-04-23')
    old_datas_count = OdsFastCPFActiveSKUiPad.query_by_version(current_version)
    assert old_datas_count > 0

@pytest.mark.skip(reason="")
def test_create_snapshot_table():
    try:
        OdsFastCPFActiveSKUiPadSnapshot.__table__.create(ExDmpEngine)
    except Exception:
        print('already exists.')
    assert 1 == 1

@pytest.mark.skip(reason="")
def test_data_source_snapshot():
    # query latest data
    snapshot_data = OdsFastCPFActiveSKUiPad.query_snapshot_version(202331)

    # insert data
    OdsFastCPFActiveSKUiPadSnapshot.bulk_save(snapshot_data)

@pytest.mark.skip(reason="")
def test_query_snapshot_data():
    # query latest data
    snapshot_data = OdsFastCPFActiveSKUiPad.query_snapshot_data(
        202335, "FY23Q3W8")
    # insert data
    OdsFastCPFActiveSKUiPadSnapshot.bulk_save(snapshot_data)

def test_query_data_by_week():
    ret = OdsFastCPFActiveSKUiPadSnapshot.query_data_by_week(202335)
