########## must at head ############
import sys
import os

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(SCRIPT_DIR))
##########      end     ############

# usage
# ENV=dev pytest -s test/test_esr.py

from data.allocation_esr_data import *
from service.datasource_service import get_datasource_automatic_esr_list, \
    get_esr_record_list, \
    download_esr_record_by_version

def test_get_esr_list():
    ret = AppFastESRMonWi.get_esr_list()
    print(ret)

def test_get_ser_list_service():
    ret = get_datasource_automatic_esr_list()
    print(ret)

def test_get_esr_record_list():
    ret = get_esr_record_list(202330)
    print(ret)

def test_download_esr_content():
    ret = download_esr_record_by_version(202330, ESRMondayVersion)
    print(ret)
