########## must at head ############
import os
import sys

import pandas
import matplotlib.pyplot as plt
import numpy as np
from scipy.interpolate import make_interp_spline

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(SCRIPT_DIR))


##########      end     ############

# usage
# ENV=dev pytest -s test/test_esr.py


def test_rtm_forecast():
    # avg_ub_1_5 数据为0时处理？
    # 示例数据
    data = {
        'mpn': ['A', 'B', 'C'],
        'ml_cw': [100, 3, 5],
        'ml_cw1': [100, 4, 6],
        'dfa_cw2': [90, 5, 7],
        'dfa_cw3': [92, 6, 8],
        'dfa_cw4': [85, 7, 9],
        'dfa_cw5': [85, 7, 9],
        'dfa_cw6': [85, 7, 9],
        'avg_ub_1_5': [10, 10, 10]
    }

    df = pandas.DataFrame(data)
    # columns_to_check = ['dfa_cw4', 'dfa_cw5', 'fa_cw6']
    # # 检查是否有一列包含0
    # has_any_zero_week = (df[columns_to_check] == 0).any(axis=0).any()
    # has_sum_zero = (df['avg_ub_1_5'] == 0).any()
    # print('$$$$$$$$$$$$$$')
    # print(has_any_zero_week)
    # print(has_sum_zero)
    # print('$$$$$$$$$$$$$$')

    # 1 特殊处理函数 前几位固定值
    # 2 普通差分处理，参数 开始值，开始索引，

    # 提取需要的列名作为一个数组
    needed_cols = ['ml_cw', 'ml_cw1', 'dfa_cw2', 'dfa_cw3', 'dfa_cw4', 'dfa_cw5', 'dfa_cw6']
    # 创建一个新的列'cw_list'用于存放每行的数据列表
    df['cw_list'] = df[needed_cols].values.tolist()

    # a1 初始化结果
    col_n = 7
    df['rate'] = df.apply(lambda row: [0] * col_n, axis=1)

    # a2 处理函数
    def df_rate_row_processing(row, start_index, init_field, process_col):
        for i in range(start_index, len(row[process_col])):
            if i == start_index:
                row[process_col][i] = round(row['cw_list'][i] / row[init_field], 2)
            else:
                row[process_col][i] = round(row['cw_list'][i] / row['cw_list'][i - 1], 2)
        for i in range(0, start_index):
            row[process_col][i] = None
        return row

    df = df.apply(lambda row: df_rate_row_processing(row, 2, 'avg_ub_1_5', 'rate'), axis=1)

    print(df)

    sold_to_data = {
        's': ['s1', 's1', 's2', 's3'],
        'mpn': ['A', 'B', 'C', 'C'],
        'ml_cw': [100, 3, 5, 6],
        'ml_cw1': [100, 4, 6, 6],
        'dfa_cw2': [90, 5, 7, 6],
        'dfa_cw3': [92, 6, 8, 6],
        'dfa_cw4': [85, 7, 9, 6],
        'dfa_cw5': [85, 7, 9, 6],
        'dfa_cw6': [85, 7, 9, 6],
        'avg_ub_1_5': [50, 5, 7, 6],
        'shipment_plan_cw': [50, 5, 7, 6],
        'ub_eoh': [50, 5, 7, 6],
        'twos': [2, 2, 2, 2]
    }
    sold_to_df = pandas.DataFrame(sold_to_data)
    # sold_to_df['so_cw_list'] = sold_to_df[needed_cols].values.tolist()
    sold_to_df = sold_to_df.merge(df[['mpn', 'rate']], how='left', on=['mpn'])
    # 结果存到rtm_n_fcst 列中 按照cw-cw4 顺序，cw=ml-cw ，cw1=ml-cw1，
    # 添加一列
    col_n = 7
    sold_to_df['rtm_n_fcst'] = sold_to_df.apply(lambda row: [0] * col_n, axis=1)

    def rtm_n_fcst_processing(row, num):
        row['rtm_n_fcst'][0] = row['ml_cw']
        row['rtm_n_fcst'][1] = row['ml_cw1']
        row['rtm_n_fcst'][2] = row['avg_ub_1_5'] * row['rate'][2]
        # 从 rtm_n_fcst 的 index = num 既+num w开始处理
        for x in range(num, len(row['rtm_n_fcst'])):
            row['rtm_n_fcst'][x] = round(row['rtm_n_fcst'][x - 1] * row['rate'][x], 5)
        return row

    sold_to_df = sold_to_df.apply(rtm_n_fcst_processing, num=3, axis=1)

    # sold_to_df['normalized_fcst_cw2'] = sold_to_df['rtm_n_fcst'].apply(lambda x: x[2])
    # sold_to_df['normalized_fcst_cw3'] = sold_to_df['rtm_n_fcst'].apply(lambda x: x[3])
    # sold_to_df['normalized_fcst_cw4'] = sold_to_df['rtm_n_fcst'].apply(lambda x: x[4])
    # sold_to_df['normalized_fcst_cw5'] = sold_to_df['rtm_n_fcst'].apply(lambda x: x[5])
    # sold_to_df['normalized_fcst_cw6'] = sold_to_df['rtm_n_fcst'].apply(lambda x: x[6])

    def extract_normalized_fcst(row, fields):
        return pandas.Series({fields[i]: row[i+2] for i in range(len(fields))})
    cw_cols = ['normalized_fcst_cw2', 'normalized_fcst_cw3', 'normalized_fcst_cw4', 'normalized_fcst_cw5',
               'normalized_fcst_cw6']
    sold_to_df[cw_cols] = sold_to_df['rtm_n_fcst'].apply(lambda x: extract_normalized_fcst(x, cw_cols))
    print('---------------')
    print(sold_to_df)
    print('---------------')

    # step3
    # CW+1 DN =Sum ML UB FCST (CW , CW+1 ) + Average RTM Normalized FCST (CW+2 ~CW+4)*(TWOS+X) - CW-1 UB EOH (actual) - CW Shipment Plan
    # = 100 + 100 + (450+460+425)/3 * 2 - 50 - 50
    # CW+2 DN =Sum ML UB FCST (CW , CW+1 ) + CW+2  RTM Normalized FCST + Average RTM Normalized FCST(CW+3 ~CW+5)*(TWOS+X) - CW-1 UB EOH (actual) - CW Shipment Plan - CW+1 DN
    #   CW+2  RTM Normalized FCST + Average RTM Normalized FCST(CW+3 ~CW+4)*(TWOS+X) - Average RTM Normalized FCST (CW+2 ~CW+4)*(TWOS+X)
    # 100 + 100 + 450 + (460+425)/2 * 2 - 50 - 50 - 990
    #
    sold_to_df['rtm_dn_cw1'] = None
    sold_to_df['rtm_dn_cw2'] = None

    def rtm_dn_cw_processing(row):
        row['rtm_dn_cw1'] = row['ml_cw'] + row['ml_cw1'] + np.mean(row['rtm_n_fcst'][2:5]) * row['twos'] - row[
            'ub_eoh'] - row['shipment_plan_cw']
        row['rtm_dn_cw2'] = row['ml_cw'] + row['ml_cw1'] + row['rtm_n_fcst'][2] + np.mean(row['rtm_n_fcst'][3:6]) * row[
            'twos'] - row['ub_eoh'] - row['shipment_plan_cw'] - row['rtm_dn_cw1']
        return row

    sold_to_df = sold_to_df.apply(rtm_dn_cw_processing, axis=1)
    print(sold_to_df)
    print('**************************')

    group_rtm_df = sold_to_df[['s', 'mpn', 'rtm_dn_cw1', 'rtm_dn_cw2']]
    grouped_sum_df = group_rtm_df.groupby('mpn')[['rtm_dn_cw1', 'rtm_dn_cw2']].sum().reset_index()
    print(grouped_sum_df)
