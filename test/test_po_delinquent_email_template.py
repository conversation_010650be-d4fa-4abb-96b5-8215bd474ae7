########## must at head ############
import datetime
import sys
import os

from jinja2 import Template


SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(SCRIPT_DIR))
##########      end     ############

from domain.dashboard.impl.po_delinquent_impl import channel_view_data_for_email

template_html = """<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <!--[if !mso]><!-->
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!--<![endif]-->
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="color-scheme" content="light dark"/>
    <meta name="supported-color-schemes" content="light dark"/>
    <style type="text/css">
        * {
            margin: 0;
        }
        .container {
            width: 100%;
            margin: 0px auto;
        }
        .header {
            border-radius: 12px;
            padding: 12px 0px 0;
            position: relative;
            width: 100%;
            box-sizing: border-box;
        }
        .header .header-title {
            display: flex;
            align-items: center;
            font-size: 13px;
            line-height: 22px;
            gap: 4px;
        }
        .header .header-title div:nth-child(1) {
            font-size: 14px;
            margin-top: -0.15em;
        }
        .header .header-subtitle {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 2px;
        }
        .header .header-subtitle div:nth-child(1) {
            font-size: 17px;
            line-height: 22px;
            font-weight: 500;
        }
        .header .header-subtitle div:nth-child(2) {
            font-size: 12px;
            line-height: 16px;
        }
        .header .apple-icon {
            position: absolute;
            right: 0;
            bottom: 0;
            width: 70px;
            height: 80px;
            display: block;
            z-index: 0;
        }

        section {
            box-sizing: border-box;
            width: 100%;
        }

        section .warning {
            color: #F63F54;
            display: flex;
            gap: 4px;
            white-space: pre-wrap;
        }

        section .section-title {
            display: flex;
            justify-content: space-between;
            align-items: center;
            line-height: 20px;
        }
        section .section-title div:nth-child(1) {
            font-size: 16px;
        }
        section .section-title div:nth-child(2) {
            color: #AEAEB2;
        }

        section .section-table {
            box-sizing: border-box;
            padding: 24px;
        }

        .section-table + .section-table {
            margin-top: 16px;
        }

        section .section-table-title {
            font-size: 16px;
            line-height: 20px;
            font-weight: 500;
            box-sizing: border-box;
            padding: 0 0 12px 8px;
        }

        section .seciton-table-body {
            box-sizing: border-box;
            border-radius: 8px;
            border: 1px solid transparent;
            overflow: hidden;
        }

        section .section-grid {
            display: grid;
            align-content: center;
            grid-template-columns: 40% 21% 18% 21%;
        }

        section .section-grid div {
            box-sizing: border-box;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        section .section-no-data {
            margin-top: 40px;
            text-align: center;
        }

        section .section-no-data-title {
            margin-top: 24px;
        }

        section .section-no-data-text {
            margin-top: 8px;
        }

        footer {
            height: 78px;
            border-radius: 12px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 18px;
            width: 100%;
            font-size: 16px;
            line-height: 20px;
        }

        footer .footer-hint {
            color: #8E8E93;
        }

        footer .footer-hint-text {
            color: #4F78E3;
            text-decoration: none;
        }
        .hint-text {
            text-align: center;
            font-size: 12px;
        }
        .section-btn {
            display: block;
            width: 140px;
            height: 48px;
            text-align: center;
            border-radius: 24px;
            line-height: 48px;
            font-size: 18px;
            font-weight: 500;
            cursor: pointer;
            margin: 0 auto;
            text-decoration: none;
            overflow: hidden;
            outline: none;
            background: rgba(254, 254, 254, 1);
            color: #1C1C1E;
        }
        .section-btn:focus {
            outline: none;
        }

        @media (prefers-color-scheme: dark) {
            body,
            .container {
                background: #23232a;
            }
            .header {
                background: rgba(254, 254, 254, 1);
            }
            .header .header-title div,
            .header .header-subtitle div {
                color: #1C1C1E;
            }
            .header .apple-icon {
                background-image: url(data:image/png;base64,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);
            }

            section {
                color: #FFFFFF;
            }

            section .section-title div:nth-child(1) {
                color: #FFFFFF;
            }

            section .section-table {
                background: rgba(245, 245, 247, 0.12);
                box-shadow: none !important;
            }

            section .seciton-table-body {
                border-color: #6E6E73;
            }

            section .section-table-tr + .section-table-tr {
                border-top: 1px solid #6E6E73;
            }

            section .section-grid {
                color: #FFFFFF;
            }

            section .section-table-header {
                color: #D1D1D6;
                background: rgba(0,0,0,0.2);
            }

            section .section-no-data svg #border1, section .section-no-data svg #border2 {
                fill: #FFF;
            }

            section .section-no-data-title {
                color: #FFFFFF;
            }

            section .section-no-data-text {
                color: #6E6E73;
            }

            footer {
                background-color: rgba(255, 255, 255, 0.08);
            }

            .hint-text {
                color: #D1D1D6;
            }
            .section-btn {
                background: rgba(254, 254, 254, 1) !important;
                color: #1C1C1E !important;
            }
        }

        @media (prefers-color-scheme: light) {
            body,
            .container {
                background: #f5f5f7;
            }
            .header {
                background: #1C1C1E;
            }
            .header .header-title div,
            .header .header-subtitle div {
                color: #fff;
            }
            .header .apple-icon {
                background-image: url(data:image/png;base64,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);
            }

            section {
                color: #1C1C1E;
            }

            section .section-title div:nth-child(1) {
                color: #1C1C1E;
            }

            section .section-table {
                background: #ffffff;
                box-shadow: 0 0 23px rgba(0,0,0,0.05);
            }

            section .seciton-table-body {
                border-color: #E5E5EA;
            }

            section .section-table-tr + .section-table-tr {
                border-top: 1px solid #E5E5EA;
            }

            section .section-grid {
                color: #3A3A3C;
            }

            section .section-table-header {
                color: #6E6E73;
                background: #F5F5F7;
            }

            section .section-no-data svg #border1, section .section-no-data svg #border2 {
                fill: #1C1C1E;
            }

            section .section-no-data-title {
                color: #1C1C1E;
            }

            section .section-no-data-text {
                color: #6E6E73;
            }

            footer {
                background-color: #E5E5EA;
            }

            .hint-text {
                color: #AEAEB2;
            }
            .section-btn {
                background: #1C1C1E !important;
                color: #ffffff !important;
            }
        }

        @media screen and (min-width: 501px) {
            .container {
                width: 750px;
                padding: 50px;
            }
            .header {
                padding: 16px 30px;
            }

            section {
                padding: 24px 30px 40px;
            }

            section .warning {
                padding: 0 16px 24px;
                font-size: 14px;
                line-height: 20px;
            }

            section .section-title {
                padding: 0 16px 24px;
            }

            section .section-title div:nth-child(1) {
                font-size: 16px;
            }
            section .section-title div:nth-child(2) {
                font-size: 14px;
            }

            section .section-table {
                border-radius: 20px;
            }

            section .section-table-tr {
                font-size: 14px;
                height: 44px;
            }

            section .section-grid div {
                padding-right: 16px;
            }

            section .section-grid div:first-child {
                padding-left: 16px;
            }

            section .section-table-header {
                height: 40px;
            }

            section .section-table-total {
                font-size: 12px;
                line-height: 16px;
                margin-top: 8px;
            }

            section .section-no-data-title {
                font-size: 28px;
                line-height: 32px;
            }

            section .section-no-data-text {
                font-size: 17px;
                line-height: 20px;
            }

            section .section-btn {
                margin-top: 24px;
            }
        }

        @media screen and (max-width: 1500px ) {
            .container {
                width: 100%;
                padding: 16px 0;
            }
            .header {
                padding: 16px 24px;
            }

            section {
                padding: 16px 16px 40px;
            }

            section .warning {
                padding: 0 12px 16px;
                font-size: 10px;
                line-height: 14px;
            }

            section .section-title {
                padding: 0 12px 16px;
            }

            section .section-title div:nth-child(1) {
                font-size: 16px;
            }
            section .section-title div:nth-child(2) {
                font-size: 10px;
            }

            section .section-table {
                border-radius: 16px;
            }

            section .section-table-tr {
                font-size: 10px;
                height: 38px;
            }

            section .section-grid div {
                padding-right: 12px;
            }

            section .section-grid div:first-child {
                padding-left: 12px;
            }

            section .section-table-header {
                height: 38px;
            }

            section .section-table-total {
                font-size: 12px;
                line-height: 14px;
                margin-top: 4px;
            }

            section .section-no-data-title {
                font-size: 14px;
                line-height: 20px;
            }

            section .section-no-data-title {
                font-size: 20px;
                line-height: 24px;
            }

            section .section-no-data-text {
                font-size: 14px;
                line-height: 20px;
            }

            section .section-btn {
                width: 120px;
                height: 40px;
                line-height: 40px;
                font-size: 14px;
                margin-top: 16px;
            }

            footer {
                font-size: 12px;
                line-height: 14px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="header-title">
                <div></div>
                <div>Expert</div>
            </div>
            <div class="header-subtitle">
                <div>iPhone NPI PO Status</div>
                <div>{{ send_date }}</div>
            </div>
            <div class="apple-icon"></div>
        </div>
        <section class="section">
            <div class="section-title">
                <div>iPhone NPI Models</div>
                <div>Quantity units in K</div>
            </div>
            <div>PO data updated to {{ data_as_of_datetime }}</div>

            <div class="section-table">
                <div class="seciton-table-body">
                    <div class="section-grid section-table-tr section-table-header">
                        <div></div>
                        <div>Cum. PO</div>
                        <div>Open PO</div>
                        <div><1 Week</div>
                        <div>1-2 Weeks</div>
                        <div>2-3 Weeks</div>
                        <div>3-4 Weeks</div>
                        <div>>4 Weeks</div>
                    </div>
                    {% for item in data %}
                        <!-- 先渲染一行region级别的数据-->
                        {% if item.get("region", "") != "Greater China" %}
                            <div class="section-grid section-table-tr">
                                <div>{{ item.get("region", "") }}</div>
                                <div></div> <!-- rtm -->
                                <div></div> <!-- open po -->
                                <div></div> <!-- 1 week -->
                            </div>
                        {% endif %}
                        <!-- 再渲染rtm级别的数据-->
                        {% for detail in item.get("child", []) %}
                            <div class="section-grid section-table-tr">
                                {% if item.get("region", "") != "Greater China" %}
                                    <div></div>
                                {% else %}
                                    <div>{{ item.get("region", "") }}</div>
                                {% endif %}
                                <div>
                                    {% if detail.get("rtm", "") %}
                                        {{ detail.get("rtm", "") }}
                                    {% endif %}
                                </div>
                                <div>{{ detail.get("open_po", "-") }}</div>
                                <div>{{ detail.get("cond_1", "-") }}</div>
                            </div>
                        {% endfor %}
                    {% endfor %}
                </div>
            </div>
            
            
        </section>
        <footer>
            <div class="footer-hint">
                <div style="text-align:center">If you have any questions, please contact</div>
                <div style="text-align:center">
                    <a href="mailto:<EMAIL>" class="footer-hint-text"><EMAIL></a>
                </div>
            </div>
        </footer>
        <div class="hint-text">
            For internal use only. Sent from Expert Technical Support.
        </div>
    </div>
</body>
</html>
"""


def test_init_template():
    send_date = datetime.datetime.today().strftime('%Y-%m-%d')
    t_2_dt = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    jump_workspace_link = ""
    lob = 'iPhone'
    sub_lobs = [
        "iPhone 15 Pro Max",
        "iPhone 15 Pro",
        "iPhone 15 Plus",
        "iPhone 15",
        "iPhone 14 Plus",
        "iPhone 14",
        "iPhone 13",
    ]
    latest_refresh_time, result_list = channel_view_data_for_email(lob=lob, sub_lobs=sub_lobs)
    tips = "Due to Dragon Boat Festival, certain platlorm's (Banking, Kulian, FZDC, Zarva, etc.) SO data will not be provided since 22nd June and will be recovered on 25th June after which related metrics are refreshed."

    template_render_data = {
        "tips": tips,
        "send_date": send_date,
        "data_as_of_datetime": t_2_dt,
        "data": result_list,
        "jump_workspace_link": jump_workspace_link
    }
    template = Template(template_html)
    html_content = template.render(**template_render_data)

    file_path = './rendered_table.html'
    # 使用with语句打开文件，确保文件正确关闭
    with open(file_path, 'w', encoding='utf-8') as file:
        file.write(html_content)

