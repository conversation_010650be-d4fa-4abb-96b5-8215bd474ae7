########## must at head ############
import sys
import os

from domain.usage.impl.write_event_tracking_info import get_last_month, \
    get_week, get_month, get_yesterday_period

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(SCRIPT_DIR))
##########      end     ############

from util.util import *
from kit.custom_date.custom_week_date import CustomWeekDate

fiscal_qtr_week_name = 'FY23Q2W6'

def test_split_fiscal_qtr_week_name():
    ret = split_fiscal_qtr_week_name(fiscal_qtr_week_name)
    assert ret == {
        'fiscal_year': 23,
        'fiscal_quarter': 2,
        'week_in_quarter': 6,
        'fiscal_year_quarter': 'FY23Q2',
        'next_fiscal_year_quarter': 'FY23Q3',
        'last_fiscal_qtr_week_name': 'FY23Q2W5',
        'last_week_in_quarter': 5
        }

def test_get_weeks_info():
    ret = get_weeks_info(fiscal_qtr_week_name)
    assert ret == {
        'quarter_history_weeks': ['FY23Q2W1', 'FY23Q2W2', 'FY23Q2W3', 'FY23Q2W4', 'FY23Q2W5'],
        'current_and_future_weeks': ['FY23Q2W6', 'FY23Q2W7', 'FY23Q2W8', 'FY23Q2W9', 'FY23Q2W10', 'FY23Q2W11', 'FY23Q2W12', 'FY23Q2W13'],
        'current_week': 6,
        'total_weeks': 13,
        'next_quarter_five_weeks': ['FY23Q3W1', 'FY23Q3W2', 'FY23Q3W3', 'FY23Q3W4', 'FY23Q3W5'],
        'all_weeks': ['FY23Q2W1', 'FY23Q2W2', 'FY23Q2W3', 'FY23Q2W4', 'FY23Q2W5', 'FY23Q2W6', 'FY23Q2W7', 'FY23Q2W8', 'FY23Q2W9', 'FY23Q2W10', 'FY23Q2W11', 'FY23Q2W12', 'FY23Q2W13', 'FY23Q3W1', 'FY23Q3W2', 'FY23Q3W3', 'FY23Q3W4', 'FY23Q3W5'],
        'fiscal_year': 23,
        'fiscal_quarter': 2,
        'week_in_quarter': 6,
        'fiscal_year_quarter': 'FY23Q2',
        'next_fiscal_year_quarter': 'FY23Q3',
        'last_fiscal_qtr_week_name': 'FY23Q2W5',
        'last_week_in_quarter': 5
        }

def test_get_rolling_13_weeks():
    ret = get_rolling_13_weeks(fiscal_qtr_week_name)
    assert ret == ['FY23Q2W6', 'FY23Q2W7', 'FY23Q2W8', 'FY23Q2W9', 'FY23Q2W10', 'FY23Q2W11', 'FY23Q2W12', 'FY23Q2W13', 'FY23Q3W1', 'FY23Q3W2', 'FY23Q3W3', 'FY23Q3W4', 'FY23Q3W5']


def test_custom_date():
    # 周六16:00点测试 pass
    DDL_1 = {
        'week': 2,  # 0，1，2，3，4，5，6 分别代表周日到周六，0代表周日
        'hour': 10,
        'minute': 30,
        'second': 0
    }
    ret1 = CustomWeekDate(DDL_1).is_before_ddl()
    
    DDL_2 = {
        'week': 6,  # 0，1，2，3，4，5，6 分别代表周日到周六，0代表周日
        'hour': 17,
        'minute': 30,
        'second': 0
    }
    ret2 = CustomWeekDate(DDL_2).is_before_ddl()

    assert ret1 == False and ret2 == True


def test_get_last_week():
    start_date, end_date, fiscal_dt, calendar_month = get_yesterday_period()
    print(start_date, end_date, fiscal_dt, calendar_month)
