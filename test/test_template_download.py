########## must at head ############
import sys
import os

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(SCRIPT_DIR))
##########      end     ############

from data.allocation_prepare_gc_dmp_data import AppFastDemandRtmSalesTemplateWi

def test_get_ipad_template():
    ret = AppFastDemandRtmSalesTemplateWi.get_template_by_lob(202328)
    assert len(ret) >= 1
    