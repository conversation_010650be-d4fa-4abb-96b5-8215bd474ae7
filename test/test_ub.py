########## must at head ############
import os
import sys

import pandas

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(SCRIPT_DIR))
##########      end     ############

# usage
# ENV=dev pytest -s test/test_esr.py



def test_get_esr_list():

    # 示例数据
    data = {
        'fiscal_week': ['w1', 'w2', 'w3', 'w4','w5', 'w1', 'w2', 'w3'],
        'sold_to_id': [1, 1, 1, 3, 1, 2, 2,4],
        'mpn': ['A', 'A', 'B', 'A', 'A', 'B', 'B','A'],
        'ub': [10, 20, 30, 40, 50, 5, 15,20]
    }
    weeks = ['w1','w2','w3','w4','w5']
    ubs = pandas.DataFrame(data)

    # 透视 DataFrame
    pivot_df = ubs.pivot_table(index=['sold_to_id', 'mpn'], columns='fiscal_week', values='ub', fill_value=0)

    pivot_df['ub_avg'] = pivot_df[weeks].mean(axis=1)
    # 拼接ub1到ub5的值为字符串，创建新列ub_ori
    pivot_df['ub_ori'] = pivot_df[weeks].astype(str).agg(','.join, axis=1)

    # 拼接w1到w5的值为字符串，创建新列w_ori
    pivot_df['w_ori'] = ','.join(weeks)
    pivot_df['fiscal_week'] = 'w1'
    # 打印结果
    print(pivot_df)
    print(pivot_df.to_dict('records'))
    group_df = pivot_df.groupby(['fiscal_week', 'mpn']).sum().reset_index()
    print(group_df)
    print(group_df.to_dict('records'))



