########## must at head ############
import sys
import os

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(SCRIPT_DIR))
##########      end     ############

from service.demand_service import *

fiscal_week_year = 202316

def test_business_type_options():
    ret = get_business_type_options_service(fiscal_week_year, RTM)
    assert ret == ["APR","Disti","HS","KUAI","TYXX","CES","Duty Free",
                   "OTC","Township","Banking","JD","Live Streaming",
                   "Marketplace","SN/MN","Vertical","CM","CT","CU",
                   "ESC","VAR","EDU"]
    
def test_sold_to_options():
    ret = get_sold_to_options_service(fiscal_week_year, RTM, [], '')
    assert ret == ["BEIJING DC","FEI XIANG","SHEN HUI","KU DONG","XIN YA","YING LONG","XIN LIAN","CHENGDU SHIDAI","ZHONG XUAN","HUBEI XINSHENG","GUANG XIAN","KUN SHAN","MEI CHENG","ZHONGHENG YIZHAN","CHANG HANG","SHANG PAI","PIN FENG","HENG ZHOU","PEI ZHI","YI WEI","LONG TENG","PEI DAI","XIN YAO","SHENG DA YUN","INGRAM-TRADING","JIAJIE","SH INGRAM","HAINAN JUSHI","HN XINBOHANG","HS","KUAI","TYXX","INGRAM-CES","SHI MU","CHANGHONG JIAHUA","COSTCO","JURAN","XTJ","ASD","TELLING","YY","JJYT","HZ","SHWY","JD-SZDC","JD-SZTL","JD-HS","JD-BaoTong","SZLY","CHANGHONGIT","KU LIAN","FZ SHENMA","MN","SNYG","QI RUI","SHENG QING","FZZX","SHJB","CM","CT","CU","SUODIAN","BEISHENG","HECHENG","ZHIMAO","DC","FZAP","YIXIN","FYQD","JIALIAN","WISH","SHENHUANG","SH MEICHENG","GUANGDU","FENGQILE","SHAANXI TITI","HUA TONG","TAIHU YUN","TIAN HE","CHUANGSEN","RUIJIAO","NUOXIN","ZI GUAN","CHAOYUN","YUNPIN","TITI YUNCHUANG"]