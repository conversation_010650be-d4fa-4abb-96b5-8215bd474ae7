########## must at head ############
import sys
import os

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(SCRIPT_DIR))
##########      end     ############

import pytest
from service.allocation_run_calculate_service import get_data_from_datasource, \
    pull_in_or_push_out, get_uploaded_supply

def test_get_data_from_datasource():
    ret_mix = get_data_from_datasource('mix')
    print(ret_mix)
    ret_twos = get_data_from_datasource('twos')
    print(ret_twos)
    ret = get_data_from_datasource('fake')
    print(ret)


def test_pull_in_or_push_out():
    ret = pull_in_or_push_out([1,2,0,1])
    print(ret)
    ret1 = pull_in_or_push_out([1,-12,0,1])
    print(ret1)


def test_get_uploaded_supply():
    ret = get_uploaded_supply("FY24Q1W8")
    print(ret['fiscal_qtr_week_name'][0])
