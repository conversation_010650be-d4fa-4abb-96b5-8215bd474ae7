########## must at head ############
import os
import sys

from domain.dashboard.entity.channel_compliance.partner_entity import PartnerO<PERSON>allUbItem
from domain.dashboard.impl.channel_compliance.partner_impl import PartnerOverallUbAggregate


SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(SCRIPT_DIR))
##########      end     ############

# usage
# ENV=dev pytest -s test/test_esr.py

snapshot_date = '2024-11-11'
nd_type = 'T1'
lob = 'iPhone'
sub_lob = 'iPhone 16'
base_data = {
                "fiscal_qtr_year_name_cq": "FY24Q4",
                "total_so_acc_cq": 10,
                "ub3_acc_cq": 3,
                "ub7_acc_cq": 7,
                "fiscal_qtr_year_name_lq": "FY24Q5",
                "total_so_acc_lq": 11,
                "ub3_acc_lq": 4,
                "ub7_acc_lq": 7,
                "fiscal_qtr_week_name_cw": "FY25Q1W7",
                "total_so_cw": 12,
                "ub7_cw": 7,
                "fiscal_qtr_week_name_cw1": "FY25Q1W6",
                "total_so_cw1": 13,
                "ub7_cw1": 7,
                "fiscal_qtr_week_name_cw2": "FY25Q1W5",
                "total_so_cw2": 14,
                "ub7_cw2": 7,
                "fiscal_qtr_week_name_cw3": "FY25Q1W4",
                "total_so_cw3": 15,
                "ub7_cw3": 7,
                "fiscal_qtr_week_name_cw4": "FY25Q1W3",
                "total_so_cw4": 16,
                "ub7_cw4": 7,
                "last_fiscal_qtr_week_name_cw": "FY25Q1W7",
                "last_total_so_cw": 17,
                "last_ub7_cw": 7,
                "last_fiscal_qtr_week_name_cw_1": "FY25Q1W6",
                "last_total_so_cw_1": 18,
                "last_ub7_cw_1": 10,

            }

RTMS_SUB_RTM_HQ_MAPPING = {
    "Mono Brand": {
        "Lifestyle": ["Changhang", "Coodoo"],
        "Mono": ["CHJH", "Hengsha"],
    },
    "Multi Brand": {
        "OTC": ["ASD"],
        "Township": ["JJYT"],
        "MM": ["Ingram"],
    },
    "Carrier": {
        "CM": [None],
        "CU": [None],
        "CT": [None],
        "CB": [None],
    },
    "Education": {
        "Education": ["Haxi"]
    },
    "Channel Online": {
        "Channel Online": [None]
    },
    "Enterprise": {
        "Enterprise": ["JD"]
    },
}

partner_overall_ub_details = []
for rtm, sub_rtm_info in RTMS_SUB_RTM_HQ_MAPPING.items():
    for sub_rtm, hq_info in sub_rtm_info.items():
        for index, hq_name in enumerate(hq_info):
            partner_overall_ub_obj = PartnerOverallUbItem(
                snapshot_date=snapshot_date,
                rtm=rtm,
                sub_rtm=sub_rtm,
                nd_type=nd_type,
                hq_name=hq_name,
                lob=lob,
                sub_lob=sub_lob
            )
            partner_overall_ub_obj.set_ub_data(
                fiscal_qtr_year_name_cq=base_data.get("fiscal_qtr_year_name_cq"),
                total_so_acc_cq=base_data.get("total_so_acc_cq"),
                ub3_acc_cq=base_data.get("ub3_acc_cq"),
                ub7_acc_cq=base_data.get("ub7_acc_cq"),
                fiscal_qtr_year_name_lq=base_data.get("fiscal_qtr_year_name_lq"),
                total_so_acc_lq=base_data.get("total_so_acc_lq"),
                ub3_acc_lq=base_data.get("ub3_acc_lq"),
                ub7_acc_lq=base_data.get("ub7_acc_lq"),
                fiscal_qtr_week_name_cw=base_data.get("fiscal_qtr_week_name_cw"),
                total_so_cw=base_data.get("total_so_cw"),
                ub7_cw=base_data.get("ub7_cw"),
                fiscal_qtr_week_name_cw1=base_data.get("fiscal_qtr_week_name_cw1"),
                total_so_cw1=base_data.get("total_so_cw1"),
                ub7_cw1=base_data.get("ub7_cw1"),
                fiscal_qtr_week_name_cw2=base_data.get("fiscal_qtr_week_name_cw2"),
                total_so_cw2=base_data.get("total_so_cw2"),
                ub7_cw2=base_data.get("ub7_cw2"),
                fiscal_qtr_week_name_cw3=base_data.get("fiscal_qtr_week_name_cw3"),
                total_so_cw3=base_data.get("total_so_cw3"),
                ub7_cw3=base_data.get("ub7_cw3"),
                fiscal_qtr_week_name_cw4=base_data.get("fiscal_qtr_week_name_cw4"),
                total_so_cw4=base_data.get("total_so_cw4"),
                ub7_cw4=base_data.get("ub7_cw4"),
                last_fiscal_qtr_week_name_cw=base_data.get("last_fiscal_qtr_week_name_cw"),
                last_total_so_cw=base_data.get("last_total_so_cw"),
                last_ub7_cw=base_data.get("last_ub7_cw"),
                last_fiscal_qtr_week_name_cw1=base_data.get("last_fiscal_qtr_week_name_cw_1"),
                last_total_so_cw1=base_data.get("last_total_so_cw_1"),
                last_ub7_cw1=base_data.get("last_ub7_cw_1")
            )

            partner_overall_ub_details.append(
                partner_overall_ub_obj
            )


def test_over_all_ub_data():
    detail_records = partner_overall_ub_details

    # 明细数据分组求和
    overall_ub_china_channel_list = PartnerOverallUbAggregate.group_by_region(ub_data_list=detail_records)
    overall_ub_rtm_list = PartnerOverallUbAggregate.group_by_rtm(ub_data_list=detail_records)
    overall_ub_sub_rtm_list = PartnerOverallUbAggregate.group_by_sub_rtm(ub_data_list=detail_records)
    overall_ub_hq_name_list = PartnerOverallUbAggregate.group_by_hq_name(ub_data_list=detail_records)

    # GC 汇总行，总数(分母) assert
    assert (
            overall_ub_china_channel_list[0].total_so_acc_cq == 140
            and overall_ub_china_channel_list[0].total_so_acc_lq == 154
            and overall_ub_china_channel_list[0].total_so_cw == 168
            and overall_ub_china_channel_list[0].total_so_cw1 == 182
            and overall_ub_china_channel_list[0].total_so_cw2 == 196
            and overall_ub_china_channel_list[0].total_so_cw3 == 210
            and overall_ub_china_channel_list[0].total_so_cw4 == 224
            and overall_ub_china_channel_list[0].last_total_so_cw == 238
            and overall_ub_china_channel_list[0].last_total_so_cw1 == 252
    )

    # GC 汇总行 ub3和ub7(分子) assert
    assert (
            overall_ub_china_channel_list[0].ub3_acc_cq == 42
            and overall_ub_china_channel_list[0].ub7_acc_cq == 98
            and overall_ub_china_channel_list[0].ub3_acc_lq == 56
            and overall_ub_china_channel_list[0].ub7_acc_lq == 98
            and overall_ub_china_channel_list[0].ub7_cw == 98
            and overall_ub_china_channel_list[0].ub7_cw1 == 98
            and overall_ub_china_channel_list[0].ub7_cw2 == 98
            and overall_ub_china_channel_list[0].ub7_cw3 == 98
            and overall_ub_china_channel_list[0].ub7_cw4 == 98
            and overall_ub_china_channel_list[0].last_ub7_cw == 98
            and overall_ub_china_channel_list[0].last_ub7_cw1 == 140
    )

    # Mono 汇总行，总数(分母) assert
    assert (
            overall_ub_rtm_list[0].rtm == 'Mono Brand'
            and overall_ub_rtm_list[0].total_so_acc_cq == 40
            and overall_ub_rtm_list[0].total_so_acc_lq == 44
            and overall_ub_rtm_list[0].total_so_cw == 48
            and overall_ub_rtm_list[0].total_so_cw1 == 52
            and overall_ub_rtm_list[0].total_so_cw2 == 56
            and overall_ub_rtm_list[0].total_so_cw3 == 60
            and overall_ub_rtm_list[0].total_so_cw4 == 64
            and overall_ub_rtm_list[0].last_total_so_cw == 68
            and overall_ub_rtm_list[0].last_total_so_cw1 == 72
    )

    # Mono 汇总行 ub3和ub7(分子) assert
    assert (
            overall_ub_rtm_list[0].rtm == 'Mono Brand'
            and overall_ub_rtm_list[0].ub3_acc_cq == 12
            and overall_ub_rtm_list[0].ub7_acc_cq == 28
            and overall_ub_rtm_list[0].ub3_acc_lq == 16
            and overall_ub_rtm_list[0].ub7_acc_lq == 28
            and overall_ub_rtm_list[0].ub7_cw == 28
            and overall_ub_rtm_list[0].ub7_cw1 == 28
            and overall_ub_rtm_list[0].ub7_cw2 == 28
            and overall_ub_rtm_list[0].ub7_cw3 == 28
            and overall_ub_rtm_list[0].ub7_cw4 == 28
            and overall_ub_rtm_list[0].last_ub7_cw == 28
            and overall_ub_rtm_list[0].last_ub7_cw1 == 40
    )

    # Multi Brand- OTC 汇总行，总数(分母) assert
    assert (
            overall_ub_sub_rtm_list[2].sub_rtm == 'OTC'
            and overall_ub_sub_rtm_list[2].total_so_acc_cq == 10
            and overall_ub_sub_rtm_list[2].total_so_acc_lq == 11
            and overall_ub_sub_rtm_list[2].total_so_cw == 12
            and overall_ub_sub_rtm_list[2].total_so_cw1 == 13
            and overall_ub_sub_rtm_list[2].total_so_cw2 == 14
            and overall_ub_sub_rtm_list[2].total_so_cw3 == 15
            and overall_ub_sub_rtm_list[2].total_so_cw4 == 16
            and overall_ub_sub_rtm_list[2].last_total_so_cw == 17
            and overall_ub_sub_rtm_list[2].last_total_so_cw1 == 18
    )

    # Multi Brand- OTC 汇总行 ub3和ub7(分子) assert
    assert (
            overall_ub_sub_rtm_list[2].sub_rtm == 'OTC'
            and overall_ub_sub_rtm_list[2].ub3_acc_cq == 3
            and overall_ub_sub_rtm_list[2].ub7_acc_cq == 7
            and overall_ub_sub_rtm_list[2].ub3_acc_lq == 4
            and overall_ub_sub_rtm_list[2].ub7_acc_lq == 7
            and overall_ub_sub_rtm_list[2].ub7_cw == 7
            and overall_ub_sub_rtm_list[2].ub7_cw1 == 7
            and overall_ub_sub_rtm_list[2].ub7_cw2 == 7
            and overall_ub_sub_rtm_list[2].ub7_cw3 == 7
            and overall_ub_sub_rtm_list[2].ub7_cw4 == 7
            and overall_ub_sub_rtm_list[2].last_ub7_cw == 7
            and overall_ub_sub_rtm_list[2].last_ub7_cw1 == 10
    )

    # ENT JD 汇总行，总数(分母) assert
    assert (
            overall_ub_hq_name_list[7].rtm == 'Enterprise' and overall_ub_hq_name_list[7].hq_name == 'JD'
            and overall_ub_hq_name_list[7].total_so_acc_cq == 10
            and overall_ub_hq_name_list[7].total_so_acc_lq == 11
            and overall_ub_hq_name_list[7].total_so_cw == 12
            and overall_ub_hq_name_list[7].total_so_cw1 == 13
            and overall_ub_hq_name_list[7].total_so_cw2 == 14
            and overall_ub_hq_name_list[7].total_so_cw3 == 15
            and overall_ub_hq_name_list[7].total_so_cw4 == 16
            and overall_ub_hq_name_list[7].last_total_so_cw == 17
            and overall_ub_hq_name_list[7].last_total_so_cw1 == 18
    )

    # ENT JD 汇总行 ub3和ub7(分子) assert
    assert (
            overall_ub_hq_name_list[7].rtm == 'Enterprise' and overall_ub_hq_name_list[7].hq_name == 'JD'
            and overall_ub_hq_name_list[7].ub3_acc_cq == 3
            and overall_ub_hq_name_list[7].ub7_acc_cq == 7
            and overall_ub_hq_name_list[7].ub3_acc_lq == 4
            and overall_ub_hq_name_list[7].ub7_acc_lq == 7
            and overall_ub_hq_name_list[7].ub7_cw == 7
            and overall_ub_hq_name_list[7].ub7_cw1 == 7
            and overall_ub_hq_name_list[7].ub7_cw2 == 7
            and overall_ub_hq_name_list[7].ub7_cw3 == 7
            and overall_ub_hq_name_list[7].ub7_cw4 == 7
            and overall_ub_hq_name_list[7].last_ub7_cw == 7
            and overall_ub_hq_name_list[7].last_ub7_cw1 == 10
    )

    # ENT JD rate assert
    ent_jd = overall_ub_hq_name_list[7].as_display_dict()
    assert (
            ent_jd['rtm'] == 'Enterprise' and ent_jd['hq_name'] == 'JD'
            and ent_jd['qtd_last_quarter_3d_rate'] == 36
            and ent_jd['qtd_last_quarter_7d_rate'] == 64
            and ent_jd['qtd_current_quarter_3d_rate'] == 30
            and ent_jd['qtd_current_quarter_7d_rate'] == 70
            and ent_jd['weekly_trend_cw_7d_rate'] == 58
            and ent_jd['weekly_trend_cw1_7d_rate'] == 54
            and ent_jd['weekly_trend_cw2_7d_rate'] == 50
            and ent_jd['weekly_trend_cw3_7d_rate'] == 47
            and ent_jd['weekly_trend_cw4_7d_rate'] == 44
            and ent_jd['last_weekly_trend_cw_7d_rate'] == 41
            and ent_jd['last_weekly_trend_cw1_7d_rate'] == 56
    )