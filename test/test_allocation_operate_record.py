########## must at head ############
import sys
import os

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(SCRIPT_DIR))
##########      end     ############

import pytest
from util.fast_lite_base import fast_lite_engine
from data.allocation_prepare_data import TblAllocationOperateRecord
from util.util_operation import insert_operate_record
from util.const import *


@pytest.mark.skip(reason="pass")
def test_create_table():
    try:
        TblAllocationOperateRecord.__table__.create(fast_lite_engine)
        assert 1 == 1
    except Exception as e:
        print("created.")
        assert 1 == 1


# @pytest.mark.skip(reason="pass")
def test_insert_record():
    # record = TblAllocationOperateRecord()
    # record.rtm = 'Mono'
    # record.save()
    insert_operate_record('Multi', AllocationOperateCategory.Download, 'W10.xlsx', '', None, None)