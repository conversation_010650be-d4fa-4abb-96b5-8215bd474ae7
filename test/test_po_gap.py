########## must at head ############
import os
import sys
from domain.dashboard.impl.po_gap_impl import (init_channel_view_schema, init_product_view_schema,
                                               get_channel_view_data, get_product_view_data)

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(SCRIPT_DIR))
##########      end     ############

# usage
# ENV=dev pytest -s test/test_esr.py


CHANNEL_MONO_DATA = [
    {
        "sub_lob": "iPhone 15 Pro Max",
        "rtm": "Mono",
        "sub_rtm": "Lifestyle",
        "cw1_demand": 5,
        "available_po_cw1": 1,
        "po_needed_cw1": 1,
        "cw2_demand": 1,
        "available_po_cw2": 1,
        "po_needed_cw2": 1
    },
    {
        "sub_lob": "iPhone 15 Pro",
        "rtm": "Mono",
        "sub_rtm": "Lifestyle",
        "cw1_demand": 5,
        "available_po_cw1": 1,
        "po_needed_cw1": 1,
        "cw2_demand": 1,
        "available_po_cw2": 1,
        "po_needed_cw2": 1
    },
    {
        "sub_lob": "iPhone 15 Pro Max",
        "rtm": "Mono",
        "sub_rtm": "Mono AAR+",
        "cw1_demand": 5,
        "available_po_cw1": 1,
        "po_needed_cw1": 1,
        "cw2_demand": 1,
        "available_po_cw2": 1,
        "po_needed_cw2": 1
    },
    {
        "sub_lob": "iPhone 15 Pro",
        "rtm": "Mono",
        "sub_rtm": "Mono AAR+",
        "cw1_demand": 5,
        "available_po_cw1": 1,
        "po_needed_cw1": 1,
        "cw2_demand": 1,
        "available_po_cw2": 1,
        "po_needed_cw2": 1
    },
    {
        "sub_lob": "iPhone 15 Pro Max",
        "rtm": "Mono",
        "sub_rtm": "All",
        "cw1_demand": 10,
        "available_po_cw1": 2,
        "po_needed_cw1": 2,
        "cw2_demand": 2,
        "available_po_cw2": 2,
        "po_needed_cw2": 2
    },
    {
        "sub_lob": "iPhone 15 Pro",
        "rtm": "Mono",
        "sub_rtm": "All",
        "cw1_demand": 10,
        "available_po_cw1": 2,
        "po_needed_cw1": 2,
        "cw2_demand": 2,
        "available_po_cw2": 2,
        "po_needed_cw2": 2
    }
]

CHANNEL_MULTI_DATA = [
    {
        "sub_lob": "iPhone 15 Pro Max",
        "rtm": "Multi",
        "sub_rtm": "Duty Free",
        "cw1_demand": 5,
        "available_po_cw1": 1,
        "po_needed_cw1": 1,
        "cw2_demand": 1,
        "available_po_cw2": 1,
        "po_needed_cw2": 1
    },
    {
        "sub_lob": "iPhone 15 Pro",
        "rtm": "Multi",
        "sub_rtm": "Duty Free",
        "cw1_demand": 5,
        "available_po_cw1": 1,
        "po_needed_cw1": 1,
        "cw2_demand": 1,
        "available_po_cw2": 1,
        "po_needed_cw2": 1
    },
    {
        "sub_lob": "iPhone 15 Pro Max",
        "rtm": "Multi",
        "sub_rtm": "Mass Merchant",
        "cw1_demand": 5,
        "available_po_cw1": 1,
        "po_needed_cw1": 1,
        "cw2_demand": 1,
        "available_po_cw2": 1,
        "po_needed_cw2": 1
    },
    {
        "sub_lob": "iPhone 15 Pro",
        "rtm": "Multi",
        "sub_rtm": "Mass Merchant",
        "cw1_demand": 5,
        "available_po_cw1": 1,
        "po_needed_cw1": 1,
        "cw2_demand": 1,
        "available_po_cw2": 1,
        "po_needed_cw2": 1
    },
    {
        "sub_lob": "iPhone 15 Pro Max",
        "rtm": "Multi",
        "sub_rtm": "All",
        "cw1_demand": 10,
        "available_po_cw1": 2,
        "po_needed_cw1": 2,
        "cw2_demand": 2,
        "available_po_cw2": 2,
        "po_needed_cw2": 2
    },
    {
        "sub_lob": "iPhone 15 Pro",
        "rtm": "Multi",
        "sub_rtm": "All",
        "cw1_demand": 10,
        "available_po_cw1": 2,
        "po_needed_cw1": 2,
        "cw2_demand": 2,
        "available_po_cw2": 2,
        "po_needed_cw2": 2
    }
]

PRODUCT_IPHONE_15_PRO_MAX_DATA = [
    {
        "sub_lob": "iPhone 15 Pro Max",
        "nand": "256GB",
        "color": "BLACK TI",
        "cw1_demand": 5,
        "available_po_cw1": 1,
        "po_needed_cw1": 1,
        "cw2_demand": 1,
        "available_po_cw2": 1,
        "po_needed_cw2": 1
    },
    {
        "sub_lob": "iPhone 15 Pro Max",
        "nand": "256GB",
        "color": "BLUE TI",
        "cw1_demand": 5,
        "available_po_cw1": 1,
        "po_needed_cw1": 1,
        "cw2_demand": 1,
        "available_po_cw2": 1,
        "po_needed_cw2": 1
    },
    {
        "sub_lob": "iPhone 15 Pro Max",
        "nand": "256GB",
        "color": "All",
        "cw1_demand": 10,
        "available_po_cw1": 2,
        "po_needed_cw1": 2,
        "cw2_demand": 2,
        "available_po_cw2": 2,
        "po_needed_cw2": 2
    },
    {
        "sub_lob": "iPhone 15 Pro Max",
        "nand": "1TB",
        "color": "BLACK TI",
        "cw1_demand": 5,
        "available_po_cw1": 1,
        "po_needed_cw1": 1,
        "cw2_demand": 1,
        "available_po_cw2": 1,
        "po_needed_cw2": 1
    },
    {
        "sub_lob": "iPhone 15 Pro Max",
        "nand": "1TB",
        "color": "BLUE TI",
        "cw1_demand": 5,
        "available_po_cw1": 1,
        "po_needed_cw1": 1,
        "cw2_demand": 1,
        "available_po_cw2": 1,
        "po_needed_cw2": 1
    },
    {
        "sub_lob": "iPhone 15 Pro Max",
        "nand": "1TB",
        "color": "All",
        "cw1_demand": 10,
        "available_po_cw1": 2,
        "po_needed_cw1": 2,
        "cw2_demand": 2,
        "available_po_cw2": 2,
        "po_needed_cw2": 2
    },
    {
        "sub_lob": "iPhone 15 Pro Max",
        "nand": "All",
        "color": "All",
        "cw1_demand": 20,
        "available_po_cw1": 4,
        "po_needed_cw1": 4,
        "cw2_demand": 4,
        "available_po_cw2": 4,
        "po_needed_cw2": 4
    }
]

PRODUCT_IPHONE_15_PRO_DATA = [
    {
        "sub_lob": "iPhone 15 Pro",
        "nand": "256GB",
        "color": "BLACK TI",
        "cw1_demand": 5,
        "available_po_cw1": 1,
        "po_needed_cw1": 1,
        "cw2_demand": 1,
        "available_po_cw2": 1,
        "po_needed_cw2": 1
    },
    {
        "sub_lob": "iPhone 15 Pro",
        "nand": "256GB",
        "color": "BLUE TI",
        "cw1_demand": 5,
        "available_po_cw1": 1,
        "po_needed_cw1": 1,
        "cw2_demand": 1,
        "available_po_cw2": 1,
        "po_needed_cw2": 1
    },
    {
        "sub_lob": "iPhone 15 Pro",
        "nand": "256GB",
        "color": "All",
        "cw1_demand": 10,
        "available_po_cw1": 2,
        "po_needed_cw1": 2,
        "cw2_demand": 2,
        "available_po_cw2": 2,
        "po_needed_cw2": 2
    },
    {
        "sub_lob": "iPhone 15 Pro",
        "nand": "1TB",
        "color": "BLACK TI",
        "cw1_demand": 5,
        "available_po_cw1": 1,
        "po_needed_cw1": 1,
        "cw2_demand": 1,
        "available_po_cw2": 1,
        "po_needed_cw2": 1
    },
    {
        "sub_lob": "iPhone 15 Pro",
        "nand": "1TB",
        "color": "BLUE TI",
        "cw1_demand": 5,
        "available_po_cw1": 1,
        "po_needed_cw1": 1,
        "cw2_demand": 1,
        "available_po_cw2": 1,
        "po_needed_cw2": 1
    },
    {
        "sub_lob": "iPhone 15 Pro",
        "nand": "1TB",
        "color": "All",
        "cw1_demand": 10,
        "available_po_cw1": 2,
        "po_needed_cw1": 2,
        "cw2_demand": 2,
        "available_po_cw2": 2,
        "po_needed_cw2": 2
    },
    {
        "sub_lob": "iPhone 15 Pro",
        "nand": "All",
        "color": "All",
        "cw1_demand": 20,
        "available_po_cw1": 4,
        "po_needed_cw1": 4,
        "cw2_demand": 4,
        "available_po_cw2": 4,
        "po_needed_cw2": 4
    }
]


def test_product_view_data():
    # 示例数据
    data = PRODUCT_IPHONE_15_PRO_MAX_DATA + PRODUCT_IPHONE_15_PRO_DATA

    # 构造前端返回结构的数据并汇总
    res = init_product_view_schema(data)

    # case 1: 最外层Total行
    total_product_view_data = res[0]
    assert (
            total_product_view_data["sub_lob"] == "Total"
            and total_product_view_data["nand"] is None
            and total_product_view_data["color"] is None
    )
    assert total_product_view_data["cw1_demand"] == 40 and total_product_view_data["available_po_cw1"] == 8

    tmp_total_cw1_demand = 0
    tmp_total_available_po_cw1 = 0
    for item in res[1:]:
        tmp_total_cw1_demand += item["cw1_demand"]
        tmp_total_available_po_cw1 += item["available_po_cw1"]
    assert (
            total_product_view_data["cw1_demand"] == tmp_total_cw1_demand
            and total_product_view_data['available_po_cw1'] == tmp_total_available_po_cw1
    )

    # case 2: iPhone 15 Pro Max的整体汇总行(nand=all; color=all)
    iphone_15_pro_max_product_view_data = res[1]
    assert (
            iphone_15_pro_max_product_view_data["sub_lob"] == 'iPhone 15 Pro Max'
            and iphone_15_pro_max_product_view_data["nand"] == 'All'
            and iphone_15_pro_max_product_view_data["color"] is None
    )
    assert iphone_15_pro_max_product_view_data["cw1_demand"] == 20 and iphone_15_pro_max_product_view_data["available_po_cw1"] == 4
    # 所有nand->color下的汇总求和数据
    tmp_15_pro_max_total_cw1_demand = 0
    tmp_15_pro_max_total_total_available_po_cw1 = 0
    for item in iphone_15_pro_max_product_view_data["child"]:
        tmp_15_pro_max_total_cw1_demand += item["cw1_demand"]
        tmp_15_pro_max_total_total_available_po_cw1 += item["available_po_cw1"]
    assert (
            iphone_15_pro_max_product_view_data["cw1_demand"] == tmp_15_pro_max_total_cw1_demand
            and iphone_15_pro_max_product_view_data["available_po_cw1"] == tmp_15_pro_max_total_total_available_po_cw1
    )

    # case 3: iPhone 15 Pro Max的nand=256GB的汇总行
    iphone_15_pro_max_256gb_product_view_data = iphone_15_pro_max_product_view_data['child'][0]
    assert (
            iphone_15_pro_max_256gb_product_view_data["sub_lob"] == 'iPhone 15 Pro Max'
            and iphone_15_pro_max_256gb_product_view_data["nand"] == '256GB'
            and iphone_15_pro_max_256gb_product_view_data["color"] == 'All'
    )
    assert (
            iphone_15_pro_max_256gb_product_view_data["cw1_demand"] == 10
            and iphone_15_pro_max_256gb_product_view_data["available_po_cw1"] == 2
    )
    # 具体(256GB) nand->color下的汇总求和数据
    tmp_256gb_cw1_demand = 0
    tmp_256gb_available_po_cw1 = 0
    for item in iphone_15_pro_max_256gb_product_view_data["child"]:
        tmp_256gb_cw1_demand += item["cw1_demand"]
        tmp_256gb_available_po_cw1 += item["available_po_cw1"]
    assert (
            iphone_15_pro_max_256gb_product_view_data["cw1_demand"] == tmp_256gb_cw1_demand
            and iphone_15_pro_max_256gb_product_view_data['available_po_cw1'] == tmp_256gb_available_po_cw1
    )


def test_channel_view_data():
    # 示例数据
    data = CHANNEL_MONO_DATA + CHANNEL_MULTI_DATA

    # 构造前端返回结构的数据并汇总
    res = init_channel_view_schema(data)

    # case 1: 最外层Total行
    total_channel_view_data = res[0]
    assert total_channel_view_data["rtm"] == "Total"
    assert total_channel_view_data["child"][0]['cw1_demand'] == 40 and total_channel_view_data["child"][0]["available_po_cw1"] == 8

    tmp_total_cw1_demand = 0
    tmp_total_available_po_cw1 = 0
    for item in res[1:]:  # 每个rtm
        tmp_total_cw1_demand += item["child"][0]["cw1_demand"]  # item["child"][0] 是每一个rtm汇总行(sub_lob=all,sub_rtm=all)
        tmp_total_available_po_cw1 += item["child"][0]["available_po_cw1"]
    assert (
            total_channel_view_data["child"][0]["cw1_demand"] == tmp_total_cw1_demand
            and total_channel_view_data["child"][0]['available_po_cw1'] == tmp_total_available_po_cw1
    )

    # case 2: 具体rtm下具体sub_rtm的各个sub lob的整体汇总行. Mono -> Lifestyle -> all
    mono_lifestyle_channel_view_data = res[1]["child"][1]  # res[1][0]是 Mon->All; res[1][1]是 Mon->Lifestyle
    assert (
            mono_lifestyle_channel_view_data["rtm"] == 'Mono'
            and mono_lifestyle_channel_view_data["sub_rtm"] == 'Lifestyle'
            and mono_lifestyle_channel_view_data["sub_lob"] == 'All'
    )
    assert mono_lifestyle_channel_view_data["cw1_demand"] == 10 and mono_lifestyle_channel_view_data["available_po_cw1"] == 2
    # Mono -> Lifestyle下所有的sub_lob汇总求和数据
    tmp_sub_lob_total_cw1_demand = 0
    tmp_sub_lob_total_available_po_cw1 = 0
    for item in mono_lifestyle_channel_view_data["child"]:
        tmp_sub_lob_total_cw1_demand += item["cw1_demand"]
        tmp_sub_lob_total_available_po_cw1 += item["available_po_cw1"]
    assert (
            mono_lifestyle_channel_view_data["cw1_demand"] == tmp_sub_lob_total_cw1_demand
            and mono_lifestyle_channel_view_data["available_po_cw1"] == tmp_sub_lob_total_available_po_cw1
    )

    # case3: 具体rtm下所有sub_rtm的所有sub_lob的汇总求和信息. Mono -> All -> All
    mono_rtm_total_open_po_data = res[1]["child"][0]  # res[1][0]是 Mon->All; res[1][1]是 Mon->Lifestyle
    tmp_rtm_total_cw1_demand = 0
    tmp_rtm_lob_total_available_po_cw1 = 0
    for item in res[1]["child"][1:]:
        tmp_rtm_total_cw1_demand += item["cw1_demand"]
        tmp_rtm_lob_total_available_po_cw1 += item["available_po_cw1"]
    assert mono_rtm_total_open_po_data["cw1_demand"] == 20 and mono_rtm_total_open_po_data["available_po_cw1"] == 4
    assert (
            mono_rtm_total_open_po_data["cw1_demand"] == tmp_rtm_total_cw1_demand
            and mono_rtm_total_open_po_data["available_po_cw1"] == tmp_rtm_lob_total_available_po_cw1
    )


def test_api_channel_view_data():
    region = "Hong Kong"
    region = "Taiwan"
    region = "China mainland"
    lob = "iPhone"
    sub_lobs = [
        "iPhone 15 Pro Max",
        "iPhone 15 Pro",
        "iPhone 15 Plus",
        "iPhone 15",
        "iPhone 14 Plus",
        "iPhone 14",
        "iPhone 13"
    ]

    latest_refresh_time, res = get_channel_view_data(region=region, lob=lob, sub_lobs=sub_lobs)

    # case 1: 最外层Total行
    total_channel_view_data = res[0]
    assert total_channel_view_data["rtm"] == "Total"

    tmp_total_cw1_demand = 0
    tmp_total_available_po_cw1 = 0
    for item in res[1:]:  # 每个rtm
        tmp_total_cw1_demand += item["child"][0]["cw1_demand"]  # item["child"][0] 是每一个rtm汇总行(sub_lob=all,sub_rtm=all)
        tmp_total_available_po_cw1 += item["child"][0]["available_po_cw1"]
    assert (
            total_channel_view_data["child"][0]["cw1_demand"] == tmp_total_cw1_demand
            and total_channel_view_data["child"][0]['available_po_cw1'] == tmp_total_available_po_cw1
    )
    print("*" * 50)
    print(f"total_cw1_demand: {tmp_total_cw1_demand}")
    print(f"total_available_po_cw1: {tmp_total_available_po_cw1}")
    print("*" * 50)

    # case 2: 具体rtm下具体sub_rtm的各个sub lob的整体汇总行. Mono -> Lifestyle -> all
    mono_lifestyle_channel_view_data = res[1]["child"][1]  # res[1][0]是 Mon->All; res[1][1]是 Mon->Lifestyle
    # Mono -> Lifestyle下所有的sub_lob汇总求和数据
    tmp_sub_lob_total_cw1_demand = 0
    tmp_sub_lob_total_available_po_cw1 = 0
    for item in mono_lifestyle_channel_view_data["child"]:
        tmp_sub_lob_total_cw1_demand += item["cw1_demand"]
        tmp_sub_lob_total_available_po_cw1 += item["available_po_cw1"]
    assert (
            mono_lifestyle_channel_view_data["cw1_demand"] == tmp_sub_lob_total_cw1_demand
            and mono_lifestyle_channel_view_data["available_po_cw1"] == tmp_sub_lob_total_available_po_cw1
    )
    print("-" * 50)
    print(f"Mono->Lifestyle: sub_lob_total_cw1_demand: {tmp_sub_lob_total_cw1_demand}")
    print(f"Mono->Lifestyle: sub_lob_total_available_po_cw1: {tmp_sub_lob_total_available_po_cw1}")
    print("-" * 50)

    # case3: 具体rtm下所有sub_rtm的所有sub_lob的汇总求和信息. Mono -> All -> All
    mono_rtm_total_open_po_data = res[1]["child"][0]  # res[1][0]是 Mon->All; res[1][1]是 Mon->Lifestyle
    tmp_rtm_total_cw1_demand = 0
    tmp_rtm_lob_total_available_po_cw1 = 0
    for item in res[1]["child"][1:]:
        tmp_rtm_total_cw1_demand += item["cw1_demand"]
        tmp_rtm_lob_total_available_po_cw1 += item["available_po_cw1"]
    assert (
            mono_rtm_total_open_po_data["cw1_demand"] == tmp_rtm_total_cw1_demand
            and mono_rtm_total_open_po_data["available_po_cw1"] == tmp_rtm_lob_total_available_po_cw1
    )
    print("=" * 50)
    print(f"Mono: rtm_total_cw1_demand: {tmp_rtm_total_cw1_demand}")
    print(f"Mono: rtm_lob_total_available_po_cw1: {tmp_rtm_lob_total_available_po_cw1}")
    print("=" * 50)


def test_api_product_view_data():
    region = "Hong Kong"
    # region = "Taiwan"
    region = "China mainland"
    rtm = "All"
    # rtm = "Carrier"
    # rtm = "Retail Partner"
    # rtm = "Mono"
    sub_rtm = "All"
    # sub_rtm = 'China Telecom Macau'
    # sub_rtm = "Chunghwa"
    # sub_rtm = "Lifestyle"
    # sub_rtm = "China Broadcast"
    # sub_rtm = "China Mobile HK"
    lob = 'iPhone'
    sub_lobs = [
        "iPhone 15 Pro Max",
        "iPhone 15 Pro",
        "iPhone 15 Plus",
        "iPhone 15",
        "iPhone 14 Plus",
        "iPhone 14",
        "iPhone 13"
    ]

    latest_refresh_time, res = get_product_view_data(region=region, rtm=rtm, sub_lobs=sub_lobs, sub_rtm=sub_rtm, lob=lob)

    # case 1: 最外层Total行
    total_product_view_data = res[0]
    assert (
            total_product_view_data["sub_lob"] == "Total"
            and total_product_view_data["nand"] is None
            and total_product_view_data["color"] is None
    )
    tmp_total_cw1_demand = 0
    tmp_total_available_po_cw1 = 0
    for item in res[1:]:
        tmp_total_cw1_demand += item["cw1_demand"]
        tmp_total_available_po_cw1 += item["available_po_cw1"]
    assert (
            total_product_view_data["cw1_demand"] == tmp_total_cw1_demand
            and total_product_view_data['available_po_cw1'] == tmp_total_available_po_cw1
    )
    print("*" * 50)
    print(f"total_cw1_demand: {tmp_total_cw1_demand}")
    print(f"total_available_po_cw1: {tmp_total_available_po_cw1}")
    print("*" * 50)

    # case 2: iPhone 15 Pro Max的整体汇总行(nand=all; color=all)
    iphone_15_pro_max_product_view_data = res[1]
    assert (
            iphone_15_pro_max_product_view_data["sub_lob"] == 'iPhone 15 Pro Max'
            and iphone_15_pro_max_product_view_data["nand"] == 'All'
            and iphone_15_pro_max_product_view_data["color"] is None
    )
    # 所有nand->color下的汇总求和数据
    tmp_15_pro_max_total_cw1_demand = 0
    tmp_15_pro_max_total_total_available_po_cw1 = 0
    for item in iphone_15_pro_max_product_view_data["child"]:
        tmp_15_pro_max_total_cw1_demand += item["cw1_demand"]
        tmp_15_pro_max_total_total_available_po_cw1 += item["available_po_cw1"]
    assert (
            iphone_15_pro_max_product_view_data["cw1_demand"] == tmp_15_pro_max_total_cw1_demand
            and iphone_15_pro_max_product_view_data["available_po_cw1"] == tmp_15_pro_max_total_total_available_po_cw1
    )
    print("-" * 50)
    print(f"iPhone 15 Pro Max: 15_pro_max_total_cw1_demand: {tmp_15_pro_max_total_cw1_demand}")
    print(f"iPhone 15 Pro Max: 15_pro_max_total_total_available_po_cw1: {tmp_15_pro_max_total_total_available_po_cw1}")
    print("-" * 50)

    # case 3: iPhone 15 Pro Max的nand=256GB的汇总行
    iphone_15_pro_max_256gb_product_view_data = iphone_15_pro_max_product_view_data['child'][0]
    assert (
            iphone_15_pro_max_256gb_product_view_data["sub_lob"] == 'iPhone 15 Pro Max'
            and iphone_15_pro_max_256gb_product_view_data["nand"] == '256GB'
            and iphone_15_pro_max_256gb_product_view_data["color"] == 'All'
    )
    # 具体(256GB) nand->color下的汇总求和数据
    tmp_256gb_cw1_demand = 0
    tmp_256gb_available_po_cw1 = 0
    for item in iphone_15_pro_max_256gb_product_view_data["child"]:
        tmp_256gb_cw1_demand += item["cw1_demand"]
        tmp_256gb_available_po_cw1 += item["available_po_cw1"]
    assert (
            iphone_15_pro_max_256gb_product_view_data["cw1_demand"] == tmp_256gb_cw1_demand
            and iphone_15_pro_max_256gb_product_view_data['available_po_cw1'] == tmp_256gb_available_po_cw1
    )
    print("=" * 50)
    print(f"iPhone 15 Pro Max->256GB: 256gb_cw1_demand: {tmp_256gb_cw1_demand}")
    print(f"iPhone 15 Pro Max->256GB: 256gb_available_po_cw1: {tmp_256gb_available_po_cw1}")
    print("=" * 50)
