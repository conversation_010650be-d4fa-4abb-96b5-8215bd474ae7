########## must at head ############
import sys
import os

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(SCRIPT_DIR))
##########      end     ############

from datetime import date, datetime
from data.fiscal_year_week import FiscalYearWeek


kabob_date = '2023-02-09'
int_fiscal_year = 2023
int_fiscal_week_year = 202320
int_fiscal_quarter = 2
int_fiscal_week = 19
str_year_begin_dt = '2022-09-25'
str_year_end_dt = '2023-09-30'
str_fiscal_qtr_year_name = 'FY23Q2'
str_fiscal_qtr_week_name = 'FY23Q2W6'
int_week_in_fiscal_quarter = 6
int_total_weeks_in_quarter = 13


def test_get_fiscal_by_date():
    ret = FiscalYearWeek.get_fiscal_by_date(kabob_date)
    if len(ret) > 0:
        if isinstance(ret[0].fiscal_dt, date):
            fiscal_dt = ret[0].fiscal_dt.strftime('%Y-%m-%d')
        else:
            fiscal_dt = ret[0].fiscal_dt
    else:
        fiscal_dt = ''
    assert fiscal_dt == kabob_date

def test_get_fis_by_date():
    ret = FiscalYearWeek.get_fis_by_date(kabob_date)
    if isinstance(ret, FiscalYearWeek) == True:
        if isinstance(ret.fiscal_dt, date):
            fiscal_dt = ret.fiscal_dt.strftime('%Y-%m-%d')
        else:
            fiscal_dt = ret.fiscal_dt 
    else:
        fiscal_dt = ''           
    assert fiscal_dt == kabob_date

def test_get_last_week_by_date():
    fiscal_year, fiscal_week =  FiscalYearWeek.get_last_week_by_date(kabob_date)
    assert fiscal_year == int_fiscal_year and fiscal_week == int_fiscal_week

def test_get_quarter_by_yw():
    fiscal_year, fiscal_quarter = FiscalYearWeek.get_quarter_by_yw(int_fiscal_year, int_fiscal_week)
    assert fiscal_year == int_fiscal_year and fiscal_quarter == int_fiscal_quarter

def test_get_last_quarter():
    quarter_year, last_quarter = FiscalYearWeek.get_last_quarter(kabob_date)
    assert quarter_year == int_fiscal_year and last_quarter == int_fiscal_quarter - 1

def test_get_boundary_date_for_mystore_year():
    begin_dt, end_dt = FiscalYearWeek.get_boundary_date_for_mystore(int_fiscal_year)
    if isinstance(begin_dt, date) and isinstance(end_dt, date):
        begin_dt = begin_dt.strftime('%Y-%m-%d')
        end_dt = end_dt.strftime('%Y-%m-%d')
    assert begin_dt == str_year_begin_dt and end_dt == str_year_end_dt

def test_get_total_weeks_in_quarter():
    total_weeks_in_quarter = FiscalYearWeek.get_total_weeks_in_quarter(str_fiscal_qtr_year_name, int_week_in_fiscal_quarter)
    assert total_weeks_in_quarter == int_total_weeks_in_quarter
    
def test_get_week_data():
    ret = FiscalYearWeek.get_week_data()
    assert len(ret) > 0

def test_get_fiscal_qtr_week_name_by_date():
    fiscal_qtr_week_name = FiscalYearWeek.get_fiscal_qtr_week_name_by_date(kabob_date)
    assert fiscal_qtr_week_name == str_fiscal_qtr_week_name

def test_get_fiscal_week_year_and_quarter():
    ret = FiscalYearWeek.get_fiscal_week_year_and_quarter(str_fiscal_qtr_year_name, int_week_in_fiscal_quarter)
    assert ret['fiscal_week_year'] == int_fiscal_week_year and ret['fiscal_quarter'] == int_fiscal_quarter
    
def test_get_last_fiscal_week_year():
    last_fiscal_week_year = FiscalYearWeek.get_last_fiscal_week_year(int_fiscal_week_year)
    assert last_fiscal_week_year == int_fiscal_week_year - 1
    
def test_get_last3_and_current_and_next5_quarter_week():
    ret, lq_year_name, cq_year_name = FiscalYearWeek.get_last3_and_current_and_next5_quarter_week(int_fiscal_year, int_fiscal_quarter, int_fiscal_week_year)
    assert len(ret) == 21 and ret == {202312: 'CW-8\nFY23Q1W12', 202313: 'CW-7\nFY23Q1W13', 202314: 'CW-6\nFY23Q1W14', 202315: 'CW-5\nFY23Q2W1', 202316: 'CW-4\nFY23Q2W2', 202317: 'CW-3\nFY23Q2W3', 202318: 'CW-2\nFY23Q2W4', 202319: 'CW-1\nFY23Q2W5', 202320: 'CW\nFY23Q2W6', 202321: 'CW+1\nFY23Q2W7', 202322: 'CW+2\nFY23Q2W8', 202323: 'CW+3\nFY23Q2W9', 202324: 'CW+4\nFY23Q2W10', 202325: 'CW+5\nFY23Q2W11', 202326: 'CW+6\nFY23Q2W12', 202327: 'CW+7\nFY23Q2W13', 202328: 'CW+8\nFY23Q3W1', 202329: 'CW+9\nFY23Q3W2', 202330: 'CW+10\nFY23Q3W3', 202331: 'CW+11\nFY23Q3W4', 202332: 'CW+12\nFY23Q3W5'} and cq_year_name == str_fiscal_qtr_year_name 

def test_get_current_and_next5_quarter_week():
    ret, cq_year_name = FiscalYearWeek.get_current_and_next5_quarter_week(int_fiscal_year, int_fiscal_quarter, int_fiscal_week_year)
    assert len(ret) == 18 and ret == {202315: 'CW-5\nFY23Q2W1', 202316: 'CW-4\nFY23Q2W2', 202317: 'CW-3\nFY23Q2W3', 202318: 'CW-2\nFY23Q2W4', 202319: 'CW-1\nFY23Q2W5', 202320: 'CW\nFY23Q2W6', 202321: 'CW+1\nFY23Q2W7', 202322: 'CW+2\nFY23Q2W8', 202323: 'CW+3\nFY23Q2W9', 202324: 'CW+4\nFY23Q2W10', 202325: 'CW+5\nFY23Q2W11', 202326: 'CW+6\nFY23Q2W12', 202327: 'CW+7\nFY23Q2W13', 202328: 'CW+8\nFY23Q3W1', 202329: 'CW+9\nFY23Q3W2', 202330: 'CW+10\nFY23Q3W3', 202331: 'CW+11\nFY23Q3W4', 202332: 'CW+12\nFY23Q3W5'} and cq_year_name == str_fiscal_qtr_year_name

def test_get_cw_next_4_week():
    ret = FiscalYearWeek.get_cw_next_4_week(int_fiscal_week_year)
    assert len(ret) == 5 and ret == ['FY23Q2W6', 'FY23Q2W7', 'FY23Q2W8', 'FY23Q2W9', 'FY23Q2W10']

def test_get_cw_next_12_week():
    ret = FiscalYearWeek.get_cw_next_12_week(int_fiscal_week_year)
    assert len(ret) == 13 and ret == {'202320': 'CW\nFY23Q2W6', '202321': 'CW+1\nFY23Q2W7', '202322': 'CW+2\nFY23Q2W8', '202323': 'CW+3\nFY23Q2W9', '202324': 'CW+4\nFY23Q2W10', '202325': 'CW+5\nFY23Q2W11', '202326': 'CW+6\nFY23Q2W12', '202327': 'CW+7\nFY23Q2W13', '202328': 'CW+8\nFY23Q3W1', '202329': 'CW+9\nFY23Q3W2', '202330': 'CW+10\nFY23Q3W3', '202331': 'CW+11\nFY23Q3W4', '202332': 'CW+12\nFY23Q3W5'}
    
def test_get_fiscal_week_year_by_week_date():
    ret = FiscalYearWeek.get_fiscal_week_year_by_week_date(str_fiscal_qtr_week_name)
    assert ret == int_fiscal_week_year

def test_get_week_by_date():
    ret = FiscalYearWeek.get_week_by_date(kabob_date)
    assert ret.get('fiscal_week_year') == int_fiscal_week_year

def test_rolling_n_weeks_by_date():
    test_date1, n1 = datetime.strptime('2024-09-18', '%Y-%m-%d'), 3
    ret1 = FiscalYearWeek.get_rolling_n_weeks_by_date(test_date1)
    assert ret1 == ['FY24Q4W11', 'FY24Q4W12']
    ret2 = FiscalYearWeek.get_rolling_n_weeks_by_date(test_date1, n1)
    assert ret2 == ['FY24Q4W10', 'FY24Q4W11', 'FY24Q4W12']
