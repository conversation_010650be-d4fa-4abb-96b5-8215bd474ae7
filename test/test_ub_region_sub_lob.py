import os
import sys

from data.databend.dashboard.daily_ub_tracking_summary import DailyUbTrackingSummaryDi
from data.databend.dashboard.weekly_ub_tracking_summary import WeeklyUbTrackingSummaryDi

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(SCRIPT_DIR))


def test_region_sub_lob():
    daily_data = DailyUbTrackingSummaryDi.query_region_daily_ub_summary_records(fiscal_dt='2024-09-11')
    week_data = WeeklyUbTrackingSummaryDi.query_region_week_ub_summary_records(snapshot_date='2024-09-12', fiscal_week='FY24Q4W8')
    a = 1