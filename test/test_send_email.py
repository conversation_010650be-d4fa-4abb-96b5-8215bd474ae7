########## must at head ############
import sys
import os

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(SCRIPT_DIR))
##########      end     ############

import pytest
from util.mail_sender import *
from util.mail_conf import *


subject = 'test'
subject_chinese = '我是要成为海贼王的男人'
to = ['<EMAIL>']
cc = ['<EMAIL>']
mail_body = 'Dear All: \n\nhello email_report \nbody <h1>are you ok<h/>'
file_paths = ['/Users/<USER>/Downloads/luffy-boy.jpeg',
              '/Users/<USER>/Downloads/temporary_name.xlsx']
mail_extra = '<h1>我是附加的html内容</h1>'
html_type = 'html'


@pytest.mark.skip(reason='')
def test_to_and_cc():
    sender = MailSender(mail_config['smtp_host'], mail_config['smtp_port'],
                        mail_config['login'], mail_config['password'])
    sender.sender(to, to, subject, mail_body)
    sender.close()


@pytest.mark.skip(reason='')
def test_to_and_no_cc():
    sender = MailSender(mail_config['smtp_host'], mail_config['smtp_port'],
                        mail_config['login'], mail_config['password'])
    sender.sender(to, None, subject, mail_body)
    sender.close()


@pytest.mark.skip(reason='')
def test_chinese_subject():
    sender = MailSender(mail_config['smtp_host'], mail_config['smtp_port'],
                        mail_config['login'], mail_config['password'])
    sender.sender(to, to, subject_chinese, mail_body)
    sender.close()


@pytest.mark.skip(reason='')
def test_to_and_no_cc_subject():
    sender = MailSender(mail_config['smtp_host'], mail_config['smtp_port'],
                        mail_config['login'], mail_config['password'])
    sender.sender(to, None, None, mail_body, html_type)
    sender.close()


@pytest.mark.skip(reason='')
def test_to_and_no_cc_subject_body():
    sender = MailSender(mail_config['smtp_host'], mail_config['smtp_port'],
                        mail_config['login'], mail_config['password'])
    sender.sender(to, None, None, None, html_type)
    sender.close()


@pytest.mark.skip(reason='')
def test_attachment():
    sender = MailSender(mail_config['smtp_host'], mail_config['smtp_port'],
                        mail_config['login'], mail_config['password'])
    sender.sender(to, to, subject, mail_body, html_type, file_paths)
    sender.close()


@pytest.mark.skip(reason='')
def test_content_type_error():
    try:
        sender = MailSender(mail_config['smtp_host'], mail_config['smtp_port'],
                            mail_config['login'], mail_config['password'])
        sender.sender(to, cc, subject, mail_body, 'html1',
                      file_paths, mail_extra, html_type)
        sender.close()
    except ValueError as e:
        print(e)


@pytest.mark.skip(reason='')
def test_extras():
    sender = MailSender(mail_config['smtp_host'], mail_config['smtp_port'],
                        mail_config['login'], mail_config['password'])
    sender.sender(to, cc, subject, mail_body, html_type,
                  file_paths, mail_extra, html_type)
    sender.close()


def test_send_from_template():
    with open(os.path.dirname(os.path.dirname(os.path.abspath(__file__))) + '/conf/cpf_allocation_mail_template.html', 'rb') as fb:
        mail_template = fb.read().decode()
    mail_template = mail_template.replace('&TIME&', 'May 12, 2023')
    mail_template = mail_template.replace('&CONTENT&', '<h1>我是正文</h1>')
    sender = MailSender(mail_config['smtp_host'], mail_config['smtp_port'],
                        mail_config['login'], mail_config['password'])
    sender.sender(to, cc, subject, mail_template, html_type,
                  file_paths, mail_extra, html_type)
    sender.close()
