########## must at head ############
import sys
import os

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(SCRIPT_DIR))
##########      end     ############

from data.allocation_prepare_data import TblAllocationMock

# def test_mock_value():
#     ret = TblAllocationMock.get_info_by_mock_type('deadline')
#     print(ret)

def test_aa():
    table_name = "table_name"
    list = [{"fiscal_week": 'FY24Q3W6', "region": "China mainland", "mpn": "MLDU3CH/A","forecast_cw2_dfa": 666,"forecast_cw3_dfa": 888},
            {"fiscal_week": 'FY24Q3W6', "region": "China mainland", "mpn": "MLDV3CH/A","forecast_cw2_dfa": 999,"forecast_cw3_dfa": 999}]
    fields_to_update = ["forecast_cw2_dfa","forecast_cw3_dfa"]
    fields_to_update_st = [f"{field} = :{field}" for field in fields_to_update]
    s1 = ', '.join(fields_to_update_st)
    insert_stmt = f"""
                UPDATE
                {table_name}
                SET
                {s1}
                WHERE fiscal_week = :fiscal_week AND region = :region AND mpn = :mpn
            """
    print(insert_stmt)
    assert insert_stmt == ''
