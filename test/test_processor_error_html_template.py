########## must at head ############
import copy
import sys
import os

import numpy as np

from data.email_config import EmailConfigRepository
from domain.demand.impl.processor.const import BASE_PROCESSOR
from kit.token_bucket import EMAIL_BUCKET_KEY_PREFIX
from util.const import EmailCmd
from util.send_email import set_table_content_email_config, send_email_by_datasource, async_rate_limited_send_email_v2

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(SCRIPT_DIR))
##########      end     ############
from util.mail_sender import *
from util.mail_conf import *


subject = 'test'
subject_chinese = '我是要成为海贼王的男人'
to = ['<EMAIL>']
cc = ['<EMAIL>']
mail_body = 'Dear All: \n\nhello email_report \nbody <h1>are you ok<h/>'
file_paths = ['/Users/<USER>/Downloads/luffy-boy.jpeg',
              '/Users/<USER>/Downloads/temporary_name.xlsx']
mail_extra = '<h1>我是附加的html内容</h1>'
html_type = 'html'

error_datas = [{'sold_to_id': '1612467', 'rtm': 'Multi', 'mpn': np.nan}, {'sold_to_id': '1612467', 'rtm': 'Multi', 'mpn': 'MLDV3CH/A'}, {'sold_to_id': '1612467', 'rtm': 'Multi', 'mpn': 'MLDW3CH/A'}, {'sold_to_id': '1612467', 'rtm': 'Multi', 'mpn': 'MLDX3CH/A'}, {'sold_to_id': '1612467', 'rtm': 'Multi', 'mpn': 'MLDY3CH/A'}, {'sold_to_id': '1612467', 'rtm': 'Multi', 'mpn': 'MLE03CH/A'}, {'sold_to_id': '1612467', 'rtm': 'Multi', 'mpn': 'MLE13CH/A'}, {'sold_to_id': '1612467', 'rtm': 'Multi', 'mpn': 'MLE23CH/A'}, {'sold_to_id': '1612467', 'rtm': 'Multi', 'mpn': 'MLE33CH/A'}, {'sold_to_id': '1612467', 'rtm': 'Multi', 'mpn': 'MLE43CH/A'}, {'sold_to_id': '1612467', 'rtm': 'Multi', 'mpn': 'MLE63CH/A'}, {'sold_to_id': '1612467', 'rtm': 'Multi', 'mpn': 'MLE73CH/A'}, {'sold_to_id': '1612467', 'rtm': 'Multi', 'mpn': 'MLE93CH/A'}, {'sold_to_id': '1612467', 'rtm': 'Multi', 'mpn': 'MLEA3CH/A'}, {'sold_to_id': '1612467', 'rtm': 'Multi', 'mpn': 'MLEC3CH/A'}, {'sold_to_id': '1612467', 'rtm': 'Multi', 'mpn': 'MNG93CH/A'}, {'sold_to_id': '1612467', 'rtm': 'Multi', 'mpn': 'MNGA3CH/A'}, {'sold_to_id': '1612467', 'rtm': 'Multi', 'mpn': 'MNGC3CH/A'}, {'sold_to_id': '1612467', 'rtm': 'Multi', 'mpn': 'MPU93CH/A'}, {'sold_to_id': '1612467', 'rtm': 'Multi', 'mpn': 'MPUJ3CH/A'}, {'sold_to_id': '1612467', 'rtm': 'Multi', 'mpn': 'MPUW3CH/A'}, {'sold_to_id': '1612467', 'rtm': 'Multi', 'mpn': 'MPV63CH/A'}, {'sold_to_id': '1612467', 'rtm': 'Multi', 'mpn': 'MPVG3CH/A'}, {'sold_to_id': '1612467', 'rtm': 'Multi', 'mpn': 'MPVU3CH/A'}, {'sold_to_id': '1612467', 'rtm': 'Multi', 'mpn': 'MPW13CH/A'}, {'sold_to_id': '1612467', 'rtm': 'Multi', 'mpn': 'MPW73CH/A'}, {'sold_to_id': '1612467', 'rtm': 'Multi', 'mpn': 'MPWE3CH/A'}, {'sold_to_id': '1612467', 'rtm': 'Multi', 'mpn': 'MPWL3CH/A'}, {'sold_to_id': '1612467', 'rtm': 'Multi', 'mpn': 'MPWT3CH/A'}, {'sold_to_id': '1612467', 'rtm': 'Multi', 'mpn': 'MPX03CH/A'}, {'sold_to_id': '1612467', 'rtm': 'Multi', 'mpn': 'MPX63CH/A'}, {'sold_to_id': '1612467', 'rtm': 'Multi', 'mpn': 'MPXD3CH/A'}, {'sold_to_id': '1612467', 'rtm': 'Multi', 'mpn': 'MPXK3CH/A'}, {'sold_to_id': '1612467', 'rtm': 'Multi', 'mpn': 'MQ353CH/A'}, {'sold_to_id': '1612467', 'rtm': 'Multi', 'mpn': 'MQ363CH/A'}, {'sold_to_id': '1612467', 'rtm': 'Multi', 'mpn': 'MQ373CH/A'}, {'sold_to_id': '1612467', 'rtm': 'Multi', 'mpn': 'MQ393CH/A'}, {'sold_to_id': '1612467', 'rtm': 'Multi', 'mpn': 'MQ3A3CH/A'}, {'sold_to_id': '1612467', 'rtm': 'Multi', 'mpn': 'MQ3C3CH/A'}, {'sold_to_id': '1612467', 'rtm': 'Multi', 'mpn': 'MQ3D3CH/A'}, {'sold_to_id': '1612467', 'rtm': 'Multi', 'mpn': 'MQ3E3CH/A'}, {'sold_to_id': '1612467', 'rtm': 'Multi', 'mpn': 'MQ3F3CH/A'}, {'sold_to_id': '1612467', 'rtm': 'Multi', 'mpn': 'MQ3G3CH/A'}, {'sold_to_id': '1612467', 'rtm': 'Multi', 'mpn': 'MQ3H3CH/A'}, {'sold_to_id': '1612467', 'rtm': 'Multi', 'mpn': 'MQ3J3CH/A'}, {'sold_to_id': '1612467', 'rtm': 'Multi', 'mpn': 'MQ3K3CH/A'}, {'sold_to_id': '1612467', 'rtm': 'Multi', 'mpn': 'MQ3P3CH/A'}, {'sold_to_id': '1612467', 'rtm': 'Multi', 'mpn': 'MQ3Q3CH/A'}, {'sold_to_id': '1612467', 'rtm': 'Multi', 'mpn': 'MR3F3CH/A'}, {'sold_to_id': '1612467', 'rtm': 'Multi', 'mpn': 'MR3G3CH/A'}, {'sold_to_id': '1612467', 'rtm': 'Multi', 'mpn': 'MR3H3CH/A'}, {'sold_to_id': '1612467', 'rtm': 'Multi', 'mpn': 'MR593CH/A'}, {'sold_to_id': '1612467', 'rtm': 'Multi', 'mpn': 'MR5F3CH/A'}, {'sold_to_id': '1612467', 'rtm': 'Multi', 'mpn': 'MR5J3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MLDU3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MLDV3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MLDW3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MLDX3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MLDY3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MLE03CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MLE13CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MLE23CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MLE33CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MLE43CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MLE63CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MLE73CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MLE93CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MLEA3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MLEC3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MNG93CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MNGA3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MNGC3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MPU93CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MPUJ3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MPUW3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MPV63CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MPVG3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MPVU3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MPW13CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MPW73CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MPWE3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MPWL3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MPWT3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MPX03CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MPX63CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MPXD3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MPXK3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MQ353CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MQ363CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MQ373CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MQ393CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MQ3A3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MQ3C3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MQ3D3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MQ3E3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MQ3F3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MQ3G3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MQ3H3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MQ3J3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MQ3K3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MQ3P3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MQ3Q3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MR3F3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MR3G3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MR3H3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MR593CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MR5F3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MR5J3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MTLD3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MTLE3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MTLF3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MTLG3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MTLH3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MTLJ3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MTLK3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MTLL3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MTLM3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MTLN3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MTLP3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MTLQ3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MTLR3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MTLT3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MTLU3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MTQ43CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MTQ53CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MTQ63CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MTQ73CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MTQ83CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MTQ93CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MTQA3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MTQC3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MTQD3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MTQE3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MTQF3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MTQG3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MTQH3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MTQJ3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MTQK3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MTQL3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MTX93CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MTXA3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MTXC3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MTXD3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MTXE3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MTXF3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MTXG3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MTXH3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MTXJ3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MTXK3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MTXL3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MTXM3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MTXN3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MTXP3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MTXQ3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MU2N3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MU2P3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MU2Q3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MU2R3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MU2T3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MU2U3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MU2V3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MU2W3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MU2X3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MU2Y3CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MU603CH/A'}, {'sold_to_id': '1724614', 'rtm': 'Mono', 'mpn': 'MU613CH/A'}]
title = 'week FY24Q4W8 Demand base_processor soldto_mpn_pool 数据失败: FY24Q4W8  bottom-up forecast miss data:'
title = f"week 【FY24Q4W8】 处理by soldto、by mpn粒度数据失败: <br>ML数据缺失如下forecast数据:"

def test_send_from_template():
    email_config = EmailConfigRepository.query_email_config(EmailCmd.Processor)
    developer_subject_params = {"env": os.environ.get('ENV'), "processor": BASE_PROCESSOR}

    file_paths, email_config = set_table_content_email_config(email_config=copy.deepcopy(email_config),
                                                              content_data=error_datas,
                                                              table_title=title,
                                                              params=developer_subject_params)

    async_rate_limited_send_email_v2(limit_key=EMAIL_BUCKET_KEY_PREFIX + 'ml_miss_data5-' + 'FY24Q4W8',
                                     capacity=1,
                                     expire_time=1200,
                                     email_config=email_config,
                                     file_paths=file_paths)

    # send_email_by_datasource(email_config=email_config)