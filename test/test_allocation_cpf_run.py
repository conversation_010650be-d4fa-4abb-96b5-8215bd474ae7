########## must at head ############
import sys
import os

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(SCRIPT_DIR))
##########      end     ############

import pytest
from data.allocation_cpf_run_data import *
from util.const import *
from service.allocation_cpf_run_service import generate_supply_data_template_file_service
from data.allocation_cpf_lob_data import TblAllocationCpfLob


@pytest.mark.skip(reason="pass")
def test_create_table1():
    try:
        clazz = AllocationRun
        clazz.__table__.create(fast_lite_engine)
        assert 1 == 1
    except Exception as e:
        print(f"{clazz.__tablename__} has already exist.")
        assert 1 == 0


@pytest.mark.skip(reason="pass")
def test_create_table2():
    try:
        clazz = AllocationRunFile
        clazz.__table__.create(fast_lite_engine)
        assert 1 == 1
    except Exception as e:
        print(f"{clazz.__tablename__} has already exist.")
        assert 1 == 0


@pytest.mark.skip(reason="pass")
def test_create_table3():
    try:
        clazz = SupplyAcquisitionCalculateRecord
        clazz.__table__.create(fast_lite_engine)
        assert 1 == 1
    except Exception as e:
        print(f"{clazz.__tablename__} has already exist.")
        assert 1 == 0


@pytest.mark.skip(reason="pass")
def test_create_table4():
    try:
        clazz = AllocationRunSupplyAcquisition
        clazz.__table__.create(fast_lite_engine)
        assert 1 == 1
    except Exception as e:
        print(f"{clazz.__tablename__} has already exist.")
        assert 1 == 0


@pytest.mark.skip(reason="pass")
def test_create_table5():
    try:
        clazz = AllocationRunSupplyDataUpload
        clazz.__table__.create(fast_lite_engine)
        assert 1 == 1
    except Exception as e:
        print(f"{clazz.__tablename__} has already exist.")
        assert 1 == 0


@pytest.mark.skip(reason="pass")
def test_create_table6():
    try:
        clazz = AllocationRunExcessSupplyUpload
        clazz.__table__.create(fast_lite_engine)
        assert 1 == 1
    except Exception as e:
        print(f"{clazz.__tablename__} has already exist.")
        assert 1 == 0


@pytest.mark.skip(reason="pass")
def test_create_table7():
    try:
        clazz = AllocationRunStopSupplyUpload
        clazz.__table__.create(fast_lite_engine)
        assert 1 == 1
    except Exception as e:
        print(f"{clazz.__tablename__} has already exist.")
        assert 1 == 0


@pytest.mark.skip(reason="pass")
def test_create_table8():
    try:
        clazz = AllocationRunSupplyPreview
        clazz.__table__.create(fast_lite_engine)
        assert 1 == 1
    except Exception as e:
        print(f"{clazz.__tablename__} has already exist.")
        assert 1 == 0


@pytest.mark.skip(reason="pass")
def test_mock_creating_allocation_run():
    try:
        ar = AllocationRun("FY24Q1W2", 202402, "iPad",
                        AllocationRunProcessType.Automatic,
                        AllocationRunStep.SupplyAcquisition)
        ar.save()
        logger.info("generate allocation run record")
        assert 1 == 1
    except Exception as e:
        logger.info(f"failed {e}")
        assert 1 == 0


@pytest.mark.skip(reason="pass")
def test_mock_creating_allocation_run():
    try:
        ar = AllocationRunSupplyAcquisition("FY24Q1W2", 202402)
        ar.save()
        logger.info(f"generate {AllocationRunSupplyAcquisition.__tablename__} record")
        assert 1 == 1
    except Exception as e:
        logger.info(f"failed {e}")
        assert 1 == 0

@pytest.mark.skip(reason="pass")
def test_mock_creating_allocation_run_file():
    try:
        for i in range(3):
            generate_supply_data_template_file_service(i, "2023-10-08")
        assert 1 == 1
    except Exception as e:
        logger.info(f"failed {e}")
        assert 1 == 0


@pytest.mark.skip(reason="pass")
def test_mock_supply_preview_data():
    AllocationRunSupplyPreview.bulk_save([{
        'fiscal_qtr_week_name':'FY24Q1W2',
        'fiscal_week_year': 202402,
        'sales_org':'China mainland',
        'rtml4':'CES Offline',
        'fph1':'iPad',
        'fph3':'iPad 9th Gen',
        'project_code':'J181',
        'nand':'64GB',
        'color':'SPC GRAY',
        'mpn':100,
        'max_cw_shipment_plan_consumption_cq_cw':20,
        'shipment_plan_solver_landed_cw':10,
        'shipment_plan_solver_landed_cw1':30,
        'shipment_plan_solver_landed_cw2':40,
        'shipment_plan_solver_landed_cw3':20,
        'shipment_plan_solver_landed_cw4': 20,
        'last_week_shipment_plan_cw':20,
        'last_week_shipment_plan_cw1':30,
        'last_week_shipment_plan_cw2':20,
        'last_week_shipment_plan_cw3':30,
        'last_week_shipment_plan_cw4':20,
        'last_week_por_discrete_cw1':10,
        'last_week_por_discrete_cw2':20,
        'last_week_por_discrete_cw3':30,
        'last_week_por_discrete_cw4':40,
        'cw_pull_in_qty':30,
        'incremental_cw1':10,
        'incremental_cw2':20,
        'incremental_cw3':30,
        'incremental_cw4':40,
        'need_protect_cw1_cw_pull_in':20,
        'need_protect_cw2_cw_pull_in':10,
        'need_protect_cw3_cw_pull_in':30,
        'need_protect_cw4_cw_pull_in':10,
        'need_protect_cw1':10,
        'need_protect_cw2':20,
        'need_protect_cw3':30,
        'need_protect_cw4':40,
        'true_incremental_cw1':10,
        'true_incremental_cw2':10,
        'true_incremental_cw3':10,
        'true_incremental_cw4':10,
        'excess_supply_cw1':10,
        'excess_supply_cw2':10,
        'excess_supply_cw3':10,
        'excess_supply_cw4':10,
        'total_top_up_demand_cw1':10,
        'total_top_up_demand_cw2':20,
        'total_top_up_demand_cw3':10,
        'total_top_up_demand_cw4':20,
        'p0_top_up_demand_cw1':10,
        'p0_top_up_demand_cw2':20,
        'p0_top_up_demand_cw3':10,
        'p0_top_up_demand_cw4':20,
        'p1_top_up_demand_cw1':10,
        'p1_top_up_demand_cw2':20,
        'p1_top_up_demand_cw3':10,
        'p1_top_up_demand_cw4':20,
        'p2_top_up_demand_cw1':10,
        'p2_top_up_demand_cw2':20,
        'p2_top_up_demand_cw3':10,
        'p2_top_up_demand_cw4':20,
        'cum_gap_cw1':10,
        'cum_gap_cw2':20,
        'cum_gap_cw3':10,
        'cum_gap_cw4':20,
        }]
    )


@pytest.mark.skip(reason="pass")
def test_query_file_info_by_id():
    file_info = AllocationRunFile.query_by_id(35)
    logger.info(file_info.file_name)


@pytest.mark.skip(reason="pass")
def test_reset_and_clear_record_by_week():
    week_date = "FY24Q1W5"
    if os.getenv('ENV') != 'dev':
        print('watch out!!!')
        exit(1)
    table_list = [
        AllocationRun.__tablename__,
        AllocationRunFile.__tablename__,
        SupplyAcquisitionCalculateRecord.__tablename__,
        AllocationRunSupplyAcquisition.__tablename__,
        AllocationRunSupplyDataUpload.__tablename__,
        AllocationRunExcessSupplyUpload.__tablename__,
        AllocationRunStopSupplyUpload.__tablename__,
        AllocationRunSupplyPreview.__tablename__,
    ]
    ret = []
    for tbl in table_list:
        sql = f'delete from {tbl} where fiscal_qtr_week_name="{week_date}";'
        r = fast_lite_engine.execute(sql)
        ret.append(f'had delete {tbl} {r} record(s)')
    print(ret)
    cpf_record = TblAllocationCpfLob.get_allocation_cpf_lob_list(week_date)
    if cpf_record and cpf_record[0].phase == CPFAllcationPhase.AllocationRun:
        cpf_lob_data = {
            'phase': CPFAllcationPhase.DemandCollection
        }
        TblAllocationCpfLob.update_data(week_date, 'iPad', cpf_lob_data)
        print(f'change phase {CPFAllcationPhase.AllocationRun} to {CPFAllcationPhase.DemandCollection}')
