from .conf import *
from .util import *


mono_allocation_config = conf.get('mono_allocation')
if os.environ.get('DB_ENV') == 'aws':
    mono_allocation_host = mono_allocation_config['aws_host']
    mono_allocation_sec = get_secret_from_aws(mono_allocation_config['secret_name'], mono_allocation_config['secret_region'])
else :
    mono_allocation_host = mono_allocation_config['apple_host']
    mono_allocation_sec = get_secret_from_apple(mono_allocation_config['secret_name'], mono_allocation_config['secret_region'], secret_req_host, secret_req_path)

mono_allocation_user = mono_allocation_sec['username']
mono_allocation_pass = mono_allocation_sec['password']

mono_allocation_port = '3306'
mono_allocation_name = mono_allocation_config['default_db']

mono_allocation_db = "mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8".format(mono_allocation_user, urlquote(mono_allocation_pass), mono_allocation_host, mono_allocation_port, mono_allocation_name)

mono_allocation_engine = create_engine(mono_allocation_db, pool_pre_ping=True, pool_size=20, max_overflow=5, pool_recycle=3600)

MonoAllocationBase = declarative_base()
MonoAllocationSession = sessionmaker(bind=mono_allocation_engine)
