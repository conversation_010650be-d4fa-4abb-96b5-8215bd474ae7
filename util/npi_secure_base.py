from .conf import *
from .util import *

npi_secure_config = conf.get('npi_secure')
if os.environ.get('DB_ENV') == 'aws':
    npi_secure_host = npi_secure_config['aws_host']
    npi_secure_sec = get_secret_from_aws(npi_secure_config['secret_name'], npi_secure_config['secret_region'])
else:
    npi_secure_host = npi_secure_config['apple_host']
    npi_secure_sec = get_secret_from_apple(npi_secure_config['secret_name'], npi_secure_config['secret_region'],
                                           secret_req_host, secret_req_path)

npi_secure_user = npi_secure_sec['username']
npi_secure_pass = npi_secure_sec['password']

npi_secure_port = '3306'
npi_secure_db_name = npi_secure_config['default_db']

fast_npi_secure_db = "mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8".format(npi_secure_user, urlquote(npi_secure_pass),
                                                                          npi_secure_host, npi_secure_port,
                                                                          npi_secure_db_name)
fast_npi_secure_engine = create_engine(fast_npi_secure_db, pool_pre_ping=True, pool_size=20, max_overflow=5,
                                       pool_recycle=3600)

FASTNPISecureBase = declarative_base()
FASTNPISecureSession = sessionmaker(bind=fast_npi_secure_engine)
