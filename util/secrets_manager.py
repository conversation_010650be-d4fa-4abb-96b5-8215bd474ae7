# Use this code snippet in your app.
# If you need more information about configurations or implementing the sample code, visit the AWS docs:
# https://aws.amazon.com/developers/getting-started/python/
import os
import requests
import ssl 
import boto3
import base64
import json
import os
import requests
from botocore.exceptions import ClientError

ssl._create_default_https_context = ssl._create_unverified_context
def get_secret_from_aws(secret_name: str, region_name: str) -> dict:
    # Create a Secrets Manager client
    session = boto3.session.Session()
    client = session.client(
        service_name='secretsmanager',
        region_name=region_name
    )

    # In this sample we only handle the specific exceptions for the 'GetSecretValue' API.
    # See https://docs.aws.amazon.com/secretsmanager/latest/apireference/API_GetSecretValue.html
    # We rethrow the exception by default.

    try:
        get_secret_value_response = client.get_secret_value(
            SecretId=secret_name
        )
    except ClientError as e:
        if e.response['Error']['Code'] == 'DecryptionFailureException':
            # Secrets Manager can't decrypt the protected secret text using the provided KMS key.
            # Deal with the exception here, and/or rethrow at your discretion.
            raise e
        elif e.response['Error']['Code'] == 'InternalServiceErrorException':
            # An error occurred on the server side.
            # Deal with the exception here, and/or rethrow at your discretion.
            raise e
        elif e.response['Error']['Code'] == 'InvalidParameterException':
            # You provided an invalid value for a parameter.
            # Deal with the exception here, and/or rethrow at your discretion.
            raise e
        elif e.response['Error']['Code'] == 'InvalidRequestException':
            # You provided a parameter value that is not valid for the current state of the resource.
            # Deal with the exception here, and/or rethrow at your discretion.
            raise e
        elif e.response['Error']['Code'] == 'ResourceNotFoundException':
            # We can't find the resource that you asked for.
            # Deal with the exception here, and/or rethrow at your discretion.
            raise e
    else:
        # Decrypts secret using the associated KMS key.
        # Depending on whether the secret is a string or binary, one of these fields will be populated.
        if 'SecretString' in get_secret_value_response:
            secret = get_secret_value_response['SecretString']
        else:
            secret = base64.b64decode(
                get_secret_value_response['SecretBinary'])
    # Your code goes here.
    secrets_dict = json.loads(secret)
    return secrets_dict


def get_secret_from_apple(secret_name: str, region_name: str, host: str = 'gcdmp-eng.corp.apple.com',
                          path: str = '/v2/aws/secrets/get') -> dict:
    try:
        cert = None
        if os.environ.get('DB_ENV') == 'apple':
            cert = ('cert/client.gcdmp-eng.corp.apple.com.chain.pem',
                    'cert/client.gcdmp-eng.corp.apple.com.open.key.pem')

        url = "https://" + host + path
        params = {
            "secret_name": secret_name,
            "secret_region": region_name
        }
        response = requests.get(url, params=params, timeout=2, cert=cert)
        if response.status_code != 200:
            raise f'util get_secret_from_apple url = {response.url} ' \
                  f'ret code = {response.status_code} ret text = {response.text}'
        ret = response.json()
        if ret.get('code') != 0 or ret.get('data') is None:
            raise f'get response error. {ret}'
        secrets = ret.get('data')
    except Exception as e:
        print(e)
        raise e
    return secrets
