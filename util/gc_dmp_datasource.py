from util.conf import *
from urllib.parse import quote_plus as urlquote
from sqlalchemy import MetaData

gc_dmp_config = conf['gc_dmp_datasource']

if os.environ.get('DB_ENV') == 'aws':
    db_host = gc_dmp_config['aws_host']
    datasource_sec = get_secret_from_aws(gc_dmp_config['datasource_secret'], gc_dmp_config['secret_region'])
    db_port = gc_dmp_config['aws_port']
else:
    db_host = gc_dmp_config['apple_host']
    datasource_sec = get_secret_from_apple(gc_dmp_config['datasource_secret'], gc_dmp_config['secret_region'], secret_req_host, secret_req_path)
    db_port = gc_dmp_config['apple_port']

datasource_db = gc_dmp_config["datasource_db"]
ex_name = datasource_sec["username"]
ex_pw = datasource_sec["password"]
ex_db = "mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8".format(ex_name, urlquote(ex_pw), db_host, db_port, datasource_db)
ExDmpEngine = create_engine(ex_db, pool_pre_ping=True, pool_size=20, max_overflow=5, pool_recycle=3600)
ExDmpBase = declarative_base()
ExDmpSession = sessionmaker(bind=ExDmpEngine)
ExMeta = MetaData(bind=ExDmpEngine)
