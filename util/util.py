import decimal
import json
import os
import re
from datetime import datetime, timed<PERSON><PERSON>
from threading import Thread

import numpy as np
import pandas as pd
from pandas import CategoricalDtype

from domain.demand.entity.const import All_RTMS
from kit.custom_sort import CustomSort
from util.conf import logger
from util.const import DateTimeFormat
from decimal import Decimal, ROUND_HALF_UP
from typing import Optional


class NumpyEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, np.integer):
            return int(obj)
        if isinstance(obj, np.floating):
            return float(obj)
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        return json.JSONEncoder.default(self, obj)


def fmt_dt(dt):
    dt = pd.to_datetime(dt)
    return str(dt.date())


def camel(s):
    l = s.split("_")

    def upper(s):
        if not s:
            return s
        return s[0].upper()+s[1:]
    l = [upper(c) for c in l]
    return "".join(l)


def gen_mysql_create(df):
    df = pd.DataFrame(df.dtypes).reset_index()
    df.columns = ['col', 'type']
    df['type'] = df['type'].astype(str)

    def convert(s):
        if s == 'object':
            return 'varchar(256)'
        if s.startswith('int'):
            return 'int'
        if s.startswith('float') or s.startswith('double'):
            return 'double'
        raise
    df['type'] = df['type'].apply(convert)
    df['col'] = df['col'].apply(lambda s: s.lower())
    print("create table xx(")
    for _, row in df.iterrows():
        print('\t'+row['col']+'\t'+row['type']+',')
    print("\tprimary key(xx, xx)")
    print(");")


def fix_nan(s):
    assert isinstance(s, pd.Series)
    s = s.replace([-np.inf, np.inf], np.nan)
    s = s.fillna(0)
    return s


def strip(s):
    s = s.strip()
    if not s:
        return s
    if s[0] == '"' or s[0] == "'":
        return s[1:-1]
    return s


def get_str_list(node, prefix):
    '''
    get string list from (request) dict, return list[str], bool
    '''
    l = node.get(prefix)
    if l is None or not isinstance(l, list):
        return [], False
    ret = []
    for s in l:
        if str(s) == '':
            continue
        ret.append(str(s))
    ret = list(set(ret))
    return ret, True

def get_path_from_id(data_id, model_id=0, suffix=''):
    path = os.path.dirname(os.path.dirname(os.path.abspath(__file__))) + \
        '/datas/data_' + str(model_id)
    dir = os.path.dirname(path)
    if not os.path.exists(dir):
        os.mkdir(dir)
    if not os.path.exists(path):
        os.mkdir(path)
    path += '/' + str(data_id)
    if suffix != '':
        path += suffix
    return path 

def write_file(path, s):
    with open(path, 'w+') as f:
        if isinstance(s, list):
            for l in s:
                f.write(l)
        else:
            assert isinstance(s, str)
            f.write(s)


def env_pre_prod() -> bool:
    if os.environ.get('ENV') == 'pre-prod':
        return True 
    return False


def env_dev() -> bool:
    if os.environ.get('ENV') == 'dev':
        return True 
    return False


def env_version() -> str:
    return os.environ.get('RIO_VERSION')


def get_query_result_path(fiscal_dt, q_id) -> str:
    path = os.path.dirname(os.path.dirname(os.path.abspath(__file__))) + \
        '/tagging/' + fiscal_dt
    dir = os.path.dirname(path)
    if not os.path.exists(dir):
        os.mkdir(dir)
    if not os.path.exists(path):
        os.mkdir(path)
    path += '/' + str(q_id) 
    path += '.csv'
    return path 

def get_tagging_start_end_date(base_interval: int = 2):
    year_interval = 365
    
    end_datetime = datetime.now() - timedelta(days=base_interval)
    start_datetime = end_datetime - timedelta(days=year_interval)
    
    start_date = start_datetime.strftime('%Y-%m-%d')
    end_date = end_datetime.strftime('%Y-%m-%d')
    
    return {
        "start_date": start_date,
        "end_date": end_date
    }

# 默认同步 T-1 的数据
def get_sync_date(base_interval: int = 1) -> tuple[str, str, str]:
    '''
    @params base_interval int, default 1.
    @return str, str eg. 20220402, 2022-04-02, 2022-04-01, 2021-04-01
    '''
    ret_datetime = datetime.now() - timedelta(days=base_interval)
    ret_last_datetime = datetime.now() - timedelta(days=base_interval+1)
    ret_last_year_datetime = datetime.now() - timedelta(days=base_interval+365)
    ret_date = ret_datetime.strftime('%Y%m%d')
    kabob_date = ret_datetime.strftime('%Y-%m-%d')
    kabob_last_date = ret_last_datetime.strftime('%Y-%m-%d')
    kabob_last_year_date = ret_last_year_datetime.strftime('%Y-%m-%d')
    return ret_date, kabob_date, kabob_last_date, kabob_last_year_date

import time
def take_time(function):
    def func(*args, **kwargs):
        t0 = time.time()
        result = function(*args, **kwargs)
        t1 = time.time()
        spend = t1 - t0
        msg_str = "running time %.3f s: function name is %s" % (spend, function.__name__)
        print(msg_str)
        return result
    return func


def get_download_path(fiscal_quarter_week: str, download_type: int, version: int = 0, suffix=''):
    path = os.path.dirname(os.path.dirname(os.path.abspath(__file__))) + \
        f'/downloads'
    dir = os.path.dirname(path)
    if not os.path.exists(dir):
        os.mkdir(dir)
    if not os.path.exists(path):
        os.mkdir(path)
    if suffix != '':
        path += suffix
    if version:
        path += f'/FAST_Lite_CPF_{download_type}_{fiscal_quarter_week}_{version}.csv'
    else:
        path += f'/FAST_Lite_CPF_{download_type}_{fiscal_quarter_week}.csv'
    return path 

def gen_dict_by_tuple(source_tuple: list) -> dict:
    '''
    把数据库中查出来的数据,转化成字典
    '''
    ret = {}
    for i in source_tuple:
        if len(i) >= 2:
            k = i[1]
            v = i[0]
            if k in ret.keys():
                ret[k].append(v)
            else:
                ret[k] = [v]
    
    return ret


def sort_by_list(sort_list: list, target: dict) -> list:
    '''
    按照给定的数组顺序给字典排序, 返回排序后的数组
    '''
    res = []
    temp = []
    for i in sort_list:
        if i in target.keys():
            temp = target[i]
            # 升序排
            temp.sort()
            # 取交集
            intersection_set = set(res).intersection(set(temp))
            # 删除后面出现的相同元素
            if len(intersection_set)>0:
                temp = list(set(temp)-intersection_set)
            res.extend(temp)
        else:
            print('key err >>>', i)
    return res;    

def gen_dict_by_tuple2(source_tuple: list) -> dict:
    '''
    把数据库中查出来的数据,转化成字典
    '''
    ret = {}
    for i in source_tuple:
        if len(i) > 2:
            k = i[1]
            v = {"name": i[0], "sold_to_id": int(i[2])}
            if k in ret.keys():
                ret[k].append(v)
            else:
                ret[k] = [v]
    
    return ret


def sort_by_list2(sort_list: list, target: dict) -> list:
    '''
    按照给定的数组顺序给字典排序, 返回排序后的数组
    '''
    res = []
    for i in sort_list:
        if i in target.keys():
            # 升序排
            order_ = sorted(target[i], key=lambda x: x["sold_to_id"])
            for j in order_:
                res.append(j['name'])
        else:
            print('key err >>>', i)
    return res;   

def get_escape_character(raw_str: str) -> str:
    """将原始字符串进行转义后, 返回新的字符串

    Args:
        raw_str (str): 原始字符串

    Returns:
        str: 返回转义后的字符串
    """    
    spec_str = r"`~!@#$%^&*()_+-='<>?;,./[]{}"
    new_str = r""
    for item in raw_str:
        temp = item
        if item in spec_str:
            temp = r"\{}".format(item)
        new_str += temp
    return new_str


def remove_file(file_path: str) -> bool:
    """删除文件

    Args:
        file_path (str): 文件的绝对路径

    Returns:
        bool: True,文件删除成功; Flase,请检查文件路径.
    """    
    if os.path.exists(file_path):
        os.remove(file_path)
        return True
    else:
        return False


from data.fiscal_year_week import FiscalYearWeek
def get_weeks_info(fiscal_qtr_week_name: str) -> dict:
    current_week_info = split_fiscal_qtr_week_name(fiscal_qtr_week_name)
    
    fiscal_year_quarter = current_week_info['fiscal_year_quarter']
    week = current_week_info['week_in_quarter']
    next_fiscal_year_quarter = current_week_info['next_fiscal_year_quarter']
    
    total_weeks = FiscalYearWeek.get_total_weeks_in_quarter(fiscal_year_quarter, week)
    
    # 本季度历史周
    quarter_history_weeks = []
    for i in range(week - 1):
        quarter_history_weeks.append(fiscal_year_quarter+'W'+str(i+1))
        
    current_and_future_weeks = []
    for j in range(total_weeks-week+1):
        current_and_future_weeks.append(fiscal_year_quarter+'W'+str(week+j))
        
    next_quarter_five_weeks = []
    for m in range(5):
        next_quarter_five_weeks.append(next_fiscal_year_quarter+'W'+str(m+1))
    
    return {
        "quarter_history_weeks": quarter_history_weeks,
        "current_and_future_weeks": current_and_future_weeks,
        "current_week": week,
        "total_weeks": total_weeks,
        "next_quarter_five_weeks": next_quarter_five_weeks,
        "all_weeks": quarter_history_weeks+current_and_future_weeks+next_quarter_five_weeks,
        **current_week_info
    }
    

def get_rolling_13_weeks(fiscal_qtr_week_name: str) -> list:
    current_week_info = split_fiscal_qtr_week_name(fiscal_qtr_week_name)
    
    fiscal_year_quarter = current_week_info['fiscal_year_quarter']
    week = current_week_info['week_in_quarter']
    next_fiscal_year_quarter = current_week_info['next_fiscal_year_quarter']
    
    total_weeks = FiscalYearWeek.get_total_weeks_in_quarter(fiscal_year_quarter, week)
    rolling_13_weeks = []
    for i in range(13):
        if week + i <= total_weeks:
            rolling_13_weeks.append(f'{fiscal_year_quarter}W{week + i}')
        else:
            rolling_13_weeks.append(f'{next_fiscal_year_quarter}W{week + i - total_weeks}')
    return rolling_13_weeks


def list_to_str(l: list)->str:
    res_str = ""
    for i,v in enumerate(l):
        kk = "'"+v+"'"
        if i < len(l)-1:
            kk+=","
        res_str += kk
    return res_str
        
from util.const import TotalAndCurrentQuarterWeeks, SpecialWeek, NextQuarterFiveWeeks
def gen_dynamic_key(current_quarter_total_weeks: int)->list:
    title_list = []
    if current_quarter_total_weeks == 14:
        title_list = TotalAndCurrentQuarterWeeks+SpecialWeek+NextQuarterFiveWeeks
    elif current_quarter_total_weeks == 13:
        title_list = TotalAndCurrentQuarterWeeks+NextQuarterFiveWeeks
    return title_list

def gen_basic_data(data: dict, current_quarter_total_weeks: int):
    title_list = gen_dynamic_key(current_quarter_total_weeks)
    
    ret_dict = {}
    for i in title_list:
        # 返回类型<class 'decimal.Decimal'>, 需要进行转换
        if isinstance(data[i], decimal.Decimal):
            ret_dict[i] = int(data[i])
        else:
            ret_dict[i] = data[i]
    return ret_dict


def split_fiscal_qtr_week_name(fiscal_qtr_week_name: str) -> dict:
    ret = {}
    if fiscal_qtr_week_name is None or fiscal_qtr_week_name.strip() == '':
        return ret
    
    split_week = fiscal_qtr_week_name.split('W')
    fiscal_year_quarter = split_week[0]
    split_qtr = fiscal_year_quarter.split('Q')
    week = int(split_week[1])
    quarter = int(split_qtr[1])
    year = int(split_qtr[0][2:])
    
    next_year, next_quarter = get_next_quarter(year, quarter)
        
    next_fiscal_year_quarter = 'FY'+str(next_year)+'Q'+str(next_quarter)
    
    last_fiscal_qtr_week_name = ''
    last_week_in_quarter = 0
    last_week = week - 1
    if last_week > 0:
        last_week_in_quarter = last_week
        last_fiscal_qtr_week_name = fiscal_year_quarter + 'W'+ str(last_week)
    
    ret['last_week_in_quarter'] = last_week_in_quarter
    ret['week_in_quarter'] = week
    ret['fiscal_year_quarter'] = fiscal_year_quarter
    ret['next_fiscal_year_quarter'] = next_fiscal_year_quarter
    ret['fiscal_year'] = year
    ret['fiscal_quarter'] = quarter
    ret['last_fiscal_qtr_week_name'] = last_fiscal_qtr_week_name
    
    return ret

def get_next_quarter(year: int, quarter: int) -> tuple[int, int]:
    next_quarter = quarter + 1
    if next_quarter > 4:
        next_quarter = 1
        next_year = year + 1
    else:
        next_year = year
    
    return next_year, next_quarter

def _async(f):
    def wrapper(*args, **kwargs):
        thr = Thread(target=f, args=args, kwargs=kwargs)
        thr.start()

    return wrapper

def compare_datetime(dt1: str, dt2: str):
    if dt1 is None or dt2 is None:
        raise "please input correct param."
    
    datetime1 = datetime.strptime(dt1, DateTimeFormat)
    datetime2 = datetime.strptime(dt2, DateTimeFormat)
    return True if datetime1 > datetime2 else False


def get_row_dict(row):
    data = {}
    for key in row.keys():
        data[key] = row.__getattribute__(key)
    return data


def nand_color_sorted(data, sorted_key, reverse=False):
    def custom_sort(item):
        nand_order = ['All', '64GB', '128GB', '256GB', '512GB', '1TB']
        nand_index = nand_order.index(item.get('nand', '')) if item.get('nand', '') in nand_order else len(nand_order)
        # 获取data中的sub_lob list, 并去重
        sub_lob_order = list(set([x.get('sub_lob') for x in data]))
        sub_lob_order.sort(reverse=True)
        if "iPhone SE (3rd Gen)" in sub_lob_order:
            sub_lob_order.remove("iPhone SE (3rd Gen)")
            sub_lob_order.append("iPhone SE (3rd Gen)")
        sub_lob_index = sub_lob_order.index(item.get('sub_lob', '')) if item.get('sub_lob', '') in sub_lob_order else len(sub_lob_order)

        return tuple(nand_index if x == 'nand' else sub_lob_index if x == 'sub_lob' else item.get(x, '') for x in sorted_key)

    return sorted(data, key=custom_sort, reverse=reverse)


def common_sort_df(df, sort_keys, reverse=False):
    if df.empty:
        return df
    for sort_key in sort_keys:
        if 'rtm' == sort_key or 'RTM' == sort_key:
            # rtm按RTMS 顺序排序的分类
            all_rtm = sorted(df[sort_key].dropna().unique())
            key_categories = All_RTMS
            # 判断all_rtm 里的元素是否都在key_categories中，如果不在则删除
            key_categories = [i for i in key_categories if i in all_rtm]
        elif 'sub_lob' == sort_key or 'Sub-LOB' == sort_key:
            # sub_lob按字典序排序的分类
            key_categories = sorted(df[sort_key].dropna().unique(), reverse=True)  # 获取唯一值并排序
            # 判断iPhone SE (3rd Gen)是否在sublob_categories中，如果在则删除，再添加到最后
            key_categories = CustomSort.sort_iphone_sublobs(key_categories)
            if "iPhone SE (3rd Gen)" in key_categories:
                key_categories.remove("iPhone SE (3rd Gen)")
                key_categories.append("iPhone SE (3rd Gen)")
        elif 'nand' == sort_key or 'Nand' == sort_key:
            all_nand = sorted(df[sort_key].dropna().unique())
            key_categories = ['All', '64GB', '128GB', '256GB', '512GB', '1TB']
            if 'All' not in all_nand:
                key_categories.remove('All')
        elif 'sold_to_id' == sort_key or 'sold-to id' == sort_key or 'Sold-to ID' == sort_key:
            key_categories = sorted(df[sort_key].dropna().unique())
            int_list = []
            str_list = []
            for item in key_categories:
                try:
                    int_list.append(int(item))
                except:
                    str_list.append(str(item))
            # 将int_list按照升序排列
            int_list.sort()
            # 将str_list按照字典序排列
            str_list.sort()
            # 拼接int_list和str_list
            key_categories = int_list + str_list
            # 判断df['sold_to_id']类型是否为int
            if df[sort_key].dtype == 'int64':
                key_categories = [int(i) for i in key_categories]
            elif df[sort_key].dtype == 'object':
                key_categories = [str(i) for i in key_categories]
        else:
            # 按字典序排序的分类(默认是正序)
            key_categories = sorted(df[sort_key].dropna().unique(), reverse=reverse)  # 获取唯一值并排序

        key_order = CategoricalDtype(categories=key_categories, ordered=True)
        df[sort_key] = df[sort_key].astype(key_order)

    return df.sort_values(sort_keys)


def custom_round(number, ndigits=None):
    if ndigits is None:
        # 如果未指定保留小数位数,则舍入到最接近的整数
        return int(number + 0.5 * (1 if number >= 0 else -1))
    else:
        # 否则,舍入到指定的小数位数
        factor = 10 ** ndigits
        return round(number * factor) / factor


def traditional_round(num, decimal_places=0):
    '''精确的传统四舍五入'''
    # 特殊检查NaN，不进行数据处理
    if pd.isna(num):
        return num
    num = Decimal(str(num))
    exp = Decimal('1e' + str(-decimal_places))
    result = num.quantize(exp, rounding=ROUND_HALF_UP)
    if decimal_places == 0:
        return int(result)
    return float(result)


def calculate_division(numerator: Optional[int], denominator: Optional[int]) -> Optional[float]:
    if numerator is None or denominator is None or denominator == 0:
        return None
    if numerator == 0:
        return 0

    # 将 numerator 和 denominator 转换为 Decimal 对象
    numerator_decimal = Decimal(numerator)
    denominator_decimal = Decimal(denominator)
    return float(numerator_decimal / denominator_decimal)


def sort_by_suffix(sub_lob):
    pattern = re.compile(r'iPhone (\d+)(.*)')
    match = pattern.match(sub_lob)
    suffixes = ["Series", "Pro Tier", "Consumer Tier", "Pro Max", "Pro", "Plus"]
    seris_no = 0
    suffix_no = 0
    if match and match.group(1):
        seris_no = int(match.group(1))
        if match.group(2):
            suffix = match.group(2)
            pattern = re.compile(r'\s*(Pro Tier|Series|Pro Max|Plus|Consumer Tier|Pro)')
            match = pattern.match(suffix)
            try:
                if match and match.group(1):
                    logger.debug("suffix matched:::", suffix)
                    suffix_no = len(suffixes) - suffixes.index(match.group(1))
            except IndexError:
                suffix_no = 0
            except Exception as e:
                logger.exception(e)
    else:
        if sub_lob == 'All':
            seris_no = (len(suffixes) + 1) * -100
        else:
            logger.debug("badcase:", sub_lob)
    return seris_no * -100 - suffix_no