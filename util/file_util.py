import os
from hashlib import md5
from io import BytesIO
import pandas as pd
from pathlib import Path
import shutil

from util.const import ErrorExcept, ErrCode


def get_absolute_path(folder_path: str):
    slash = '/'
    if not folder_path.startswith(slash):
        folder_path = slash + folder_path
    if not folder_path.endswith(slash):
        folder_path = folder_path + slash
    path = os.path.dirname(os.path.dirname(os.path.abspath(__file__))) + folder_path
    dir_name = os.path.dirname(path)
    if not os.path.exists(dir_name):
        os.makedirs(dir_name)
    if not os.path.exists(path):
        os.mkdir(path)
    return path

def hash_file_md5(file):
    file_md5 = md5(file.stream.read()).hexdigest()
    file.stream.seek(0)
    return file_md5


def save_df_to_md5_name(df: pd.DataFrame, path: str, extension: str = None) -> str:
    # 可保存空表头文件
    # if df.empty:
    #     raise ErrorExcept(ErrCode.DBQueryError, "no data")
    if not os.path.exists(path):
        os.makedirs(path)
    bio = BytesIO()
    writer = pd.ExcelWriter(bio)
    df.to_excel(writer, header=True, index=False)
    writer.close()
    bio.seek(0)
    file_name = md5(bio.getvalue()).hexdigest() + ('' if extension is None else f'.{extension}')
    with open(f"{path}/{file_name}", "wb") as f:
        f.write(bio.getvalue())
    return file_name


def hash_file_md5_by_path(path, extension: str = None):
    file_path = Path(path)
    file_md5 = md5(file_path.read_bytes()).hexdigest() + ('' if extension is None else f'.{extension}')
    return file_md5


def convert_file_path(file_path: str):
    split_path = file_path.split('/file/storage/')
    if len(split_path) > 1:
        absolute_path = get_absolute_path('/uploads/allocation/')
        return f"{absolute_path}{split_path[1]}"
    else:
        return ""


def create_folder(folder_name: str) -> str:
    path = os.path.dirname(os.path.dirname(os.path.abspath(__file__))) + folder_name
    dir_name = os.path.dirname(path)
    if not os.path.exists(dir_name):
        os.makedirs(dir_name)
    if not os.path.exists(path):
        os.mkdir(path)
    return path


def get_file_path(file_name: str, folder_path: str = "/uploads") -> str:
    path = create_folder(folder_path)
    file_path = f'{path}/{file_name}'
    return file_path
