from datetime import datetime

from data.email_recipient import TblEmailRecipient
from util.template_email_sender import async_send_email, read_config_template, Greetings


class IdealDemandTemplateEmail:
    date = ''

    def __init__(self) -> None:
        self.date = datetime.now().strftime("%b %d, %Y")

    # 提醒邮件
    def ideal_demand_rtm_forecast_reminder(self, week_date: str, rtm: str, system_link: str):
        '''
        提醒RTM还未上传Forecast数据
        '''
        # 从邮件邮件人配置中读取
        to, cc = TblEmailRecipient.get_to_cc_list(1, rtm)

        subject = f"[FAST System] Action Required - Current Week RTM Forecast Has Not Been Submitted"
        content = f'''
                <div class="section-info">{Greetings.get('System', '')},</div>
                <div class="section-info">The current week forecast data of <span class="info-weight">{rtm}</span> has not been submitted, and the submission window will be closed soon at 14:00.  Please submit the forecast data ASAP to the FAST System (Ideal Demand module), Thanks.</div>
                <div class="section-info">Please click the following button to enter the FAST System.</div>
                <div class="section-info">Sincerely,</div>
                <div class="section-info"> Expert Technical Support</div>
                <div class="enter-btn">
                    <a href=\"{system_link}\" class="btn">Enter</a>
                </div>
                '''
        email_config = read_config_template(self.date, content)

        async_send_email(to, cc, subject, email_config, 'html')

    def ideal_demand_adjustment_x_y_reminder(self, week_date: str, adjustment_type: str, system_link: str):
        '''

        '''
        # 从邮件邮件人配置中读取
        to, cc = TblEmailRecipient.get_to_cc_list(1, 'CP&F')

        subject = f"FAST System Alert - Current Week TWOS Adjustment ({adjustment_type}) Has Not Been Submitted"
        content = f'''
                <div class="section-info">{Greetings.get('System', '')},</div>
                <div class="section-info">The current week TWOS Adjustment ({adjustment_type}) data has not been submitted, and the submission window will be closed soon at 19:00.  Please submit the forecast data ASAP to the FAST System (Ideal Demand module), Thanks.</div>
                <div class="section-info">Please click the following button to enter the FAST System.</div>
                <div class="section-info">Sincerely,</div>
                <div class="section-info"> Expert Technical Support</div>
                <div class="enter-btn">
                    <a href=\"{system_link}\" class="btn">Enter</a>
                </div>
                '''
        email_config = read_config_template(self.date, content)

        async_send_email(to, cc, subject, email_config, 'html')

    # 报错邮件
    def ideal_demand_rtm_forecast_expired(self, week_date: str, rtm: str, system_link: str):
        '''
        RTM仍未上传Forecast数据
        '''
        # 从邮件邮件人配置中读取
        to, cc = TblEmailRecipient.get_to_cc_list(1, rtm)

        subject = f"FAST System Alert - Current Week RTM Forecast Submission Has Expired"
        content = f'''
                <div class="section-info">{Greetings.get('System', '')},</div>
                <div class="section-info">The current week forecast data of <span class="info-weight">{rtm}</span> has not been submitted, and the submission window has been closed at 14:00. Due to the lack of RTM forecast, the current week Final Forecast will not be successfully generated. Please contact the DMP team ASAP, Thanks.</div>
                <div class="section-info">Please click the following button to enter the FAST System.</div>
                <div class="section-info">Sincerely,</div>
                <div class="section-info"> Expert Technical Support</div>
                <div class="enter-btn">
                    <a href=\"{system_link}\" class="btn">Enter</a>
                </div>
                '''
        email_config = read_config_template(self.date, content)

        async_send_email(to, cc, subject, email_config, 'html')

    def ideal_demand_adjustment_x_y_expired(self, week_date: str, adjustment_type: str, system_link: str):
        '''

        '''
        # 从邮件邮件人配置中读取
        to, cc = TblEmailRecipient.get_to_cc_list(1, 'CP&F')

        subject = f"FAST System Alert - Current Week TWOS Adjustment ({adjustment_type}) Submission Has Expired"
        content = f'''
                <div class="section-info">{Greetings.get('System', '')},</div>
                <div class="section-info">The current week TWOS Adjustment ({adjustment_type}) data has not been submitted, and the submission window has been closed at 19:00. Due to the lack of TWOS Adjustment data, the current week Ideal Demand will not be successfully generated. Please contact the DMP team ASAP, Thanks.</div>
                <div class="section-info">Please click the following button to enter the FAST System.</div>
                <div class="section-info">Sincerely,</div>
                <div class="section-info"> Expert Technical Support</div>
                <div class="enter-btn">
                    <a href=\"{system_link}\" class="btn">Enter</a>
                </div>
                '''
        email_config = read_config_template(self.date, content)

        async_send_email(to, cc, subject, email_config, 'html')

    # 告警邮件
    def ideal_demand_so_eoh_not_ready(self, rtm: str):
        # 从邮件邮件人配置中读取
        to, cc = TblEmailRecipient.get_to_cc_list(1, 'ideal_demand_alert')

        subject = f"FAST System Alert - Current Week SO_EOH Data Failed to Generate"
        content = f'''
                <div class="section-info">Dear team,</div>
                <div class="section-info">The current week <span class="info-weight">{rtm}</span> SO_EOH data has not been successfully fetched by FAST system. Please deal with the issue ASAP, Thanks.</div>
                <div class="section-info">Sincerely,</div>
                <div class="section-info"> Expert Technical Support</div>
                <div class="section-info">Internal alert, automatically sent by the system</div>
                '''
        email_config = read_config_template(self.date, content)

        async_send_email(to, cc, subject, email_config, 'html')

    def ideal_demand_cpf_ideal_demand_not_ready(self):
        # 从邮件邮件人配置中读取
        to, cc = TblEmailRecipient.get_to_cc_list(1, 'ideal_demand_alert_algo')

        subject = f"FAST System Alert - Current Week Ideal Demand Data Failed to Generate"
        content = f'''
                <div class="section-info">Dear team,</div>
                <div class="section-info">The current week Ideal Demand data has not been successfully fetched by FAST system. Please deal with the issue ASAP, Thanks.</div>
                <div class="section-info">Sincerely,</div>
                <div class="section-info"> Expert Technical Support</div>
                <div class="section-info">Internal alert, automatically sent by the system</div>
                '''
        email_config = read_config_template(self.date, content)

        async_send_email(to, cc, subject, email_config, 'html')
