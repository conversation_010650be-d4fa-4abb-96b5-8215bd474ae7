import redis 
from functools import wraps

from util.conf import *
from util.const import RedisCachePrefix

redis_conf = conf.get('redis_config')
if os.environ.get('DB_ENV') == 'aws':
    redis_host = redis_conf['aws_host']
    redis_sec = get_secret_from_aws(redis_conf['secret_name'], redis_conf['secret_region'])
else :
    redis_host = redis_conf['apple_host']
    redis_sec = get_secret_from_apple(redis_conf['secret_name'], redis_conf['secret_region'], secret_req_host, secret_req_path)

redis_port = redis_conf['port']
redis_name = redis_conf['default_db']
redis_pwd = redis_sec['authToken']

pool = redis.ConnectionPool.from_url("rediss://{}:{}@{}:{}/{}?decode_responses=True&health_check_interval=2".format(
    '', urlquote(redis_pwd), redis_host, redis_port, redis_name))

def GetRedis() -> redis.Redis: 
    return redis.Redis(connection_pool=pool)

def get_cache_from_redis(key: str, params: list = [], prefix: str = RedisCachePrefix):
    redis_key = gen_redis_key(key, params, prefix)
    r = GetRedis()
    ret_bytes = r.get(redis_key)
    if ret_bytes is None:
        return None 
    try: 
        ret = json.loads(ret_bytes)
    except:
        ret = ret_bytes 
    return ret 

def set_redis_cache(key: str, params: list, value, ex: float = None, prefix: str = RedisCachePrefix) -> bool:
    redis_key = gen_redis_key(key, params, prefix)

    if isinstance(value, dict) or isinstance(value, list):
        str_value = json.dumps(value)
    else:
        str_value = f'{value}'
    r = GetRedis()
    ret = r.set(redis_key, str_value, ex)
    return ret 


def gen_redis_key(key: str, params: list, prefix: str) -> str:
    if key is None:
        key = ''
    if params is None:
        params = []
    if prefix is None:
        prefix = ''
    redis_key = f'{prefix}:{key}'
    for p in params:
        if isinstance(p, list) or isinstance(p, dict):
            p = json.dumps(p)
        redis_key += f'.{p}'
    return redis_key


def delete_cache_from_redis(key: str, params: list, prefix: str = RedisCachePrefix):
    redis_key = gen_redis_key(key, params, prefix)
    r = GetRedis()
    ret = r.delete(redis_key)
    r.keys()
    return ret


def delete_bulk_cache_from_redis(key: str, prefix: str = RedisCachePrefix):
    ret = 0
    r = GetRedis()
    if key != '' and '*' not in key:
        key_list = r.keys(f"{prefix}:{key}*")
        if len(key_list) != 0:
            ret = r.delete(*key_list)

    return ret

def get_bulk_cache_from_redis(key: str, prefix: str = RedisCachePrefix):
    ret = {}
    r = GetRedis()
    ret['list'] = r.keys(f"{prefix}:{key}*")
    ret['count'] = len(ret['list'])

    return ret


def redis_lock(lock_key, lock_timeout=30):
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Connect to Redis
            r = GetRedis()
            # Attempt to acquire the lock
            lock_acquired = r.set(lock_key, 1, nx=True, ex=lock_timeout)

            # If the lock was not acquired, print a message and return
            if not lock_acquired:
                logger.info(f"PID:{os.getpid()}, Could not acquire Redis lock.")
                return

            # Call the function
            result = func(*args, **kwargs)

            # Release the lock
            # r.delete(lock_key)

            return result
        return wrapper
    return decorator


def redis_lock_release(lock_key, lock_timeout=30):
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Connect to Redis
            r = GetRedis()
            # Attempt to acquire the lock
            lock_acquired = r.set(lock_key, 1, nx=True, ex=lock_timeout)

            # If the lock was not acquired, print a message and return
            if not lock_acquired:
                logger.info(f"PID:{os.getpid()}, Could not acquire Redis lock.")
                return

            # Call the function
            try:
                result = func(*args, **kwargs)
            except Exception as e:
                raise e
            finally:
                # Release the lock
                try:
                    r.delete(lock_key)
                except Exception as e1:
                    logger.error(f"delete lock_key={lock_key} failed")
                    raise e1
            return result
        return wrapper
    return decorator

