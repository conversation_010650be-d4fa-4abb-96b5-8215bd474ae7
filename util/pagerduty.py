import pdpyras
from util.secrets_manager import get_secret_from_aws, get_secret_from_apple
from util.conf import secret_req_host, secret_req_path


def send_alert(summary: str, source: str, env: str):
    routing_key = get_pagerduty_routing_key(f'{env}_routing_key')
    if routing_key == '':
        raise Exception(f'pagerduty send_alert routing_key empty env = {env}')
    pd_session = pdpyras.EventsAPISession(routing_key)
    dedup_key = pd_session.trigger(summary, source)
    return dedup_key


def get_pagerduty_routing_key(key_name: str) -> str:
    pager_secret = 'dmp-eng-pagerduty'
    region = 'us-west-2'
    if os.environ.get('DB_ENV') == 'aws':
        pagerduty_sec = get_secret_from_aws(pager_secret, region)
    else:
        pagerduty_sec = get_secret_from_apple(pager_secret, region, secret_req_host, secret_req_path)

    return pagerduty_sec.get(key_name, '')
