import os
from typing import Op<PERSON>
from .conf import conf
from util.secrets_manager import get_secret_from_apple, get_secret_from_aws

mail_conf = conf['mail_config']
if os.environ.get('DB_ENV') == 'aws':
    mail_sec = get_secret_from_aws(
        mail_conf['secret_name'], mail_conf['secret_region'])
else:
    mail_sec = get_secret_from_apple(
        mail_conf['secret_name'], mail_conf['secret_region'])

mail_config = {
    "login": mail_sec['username'],
    "password": mail_sec['password'],
    "smtp_host": mail_conf['smtp_host'],
    "smtp_port": mail_conf['smtp_port'],
    "receivers": mail_conf['receivers'],
    "cpf_publish_receivers": mail_conf['cpf_publish_receivers'],
    "cpf_sell_in_demand_publish_receivers": mail_conf['cpf_sell_in_demand_publish_receivers'],
}

navigate_config = {
    "app_id_key": conf['navigate']['app_id_key'],
    "apple_connect": conf['navigate']['apple_connect'],
    "path": conf['navigate']['path'],
}

URL_OPEN_PO_AGING = "dashboard/po-delinquent?channel=CP&F"


def get_system_link(system_link: Optional[str] = None, enter_from: str = None, prefixes_enter_from: str = '?'):
    apple_connect = navigate_config['apple_connect']
    app_id_key = navigate_config['app_id_key']
    if system_link is None:
        system_link = navigate_config["path"]
    # 增加email跳转埋点参数
    if enter_from:
        system_link += f"{prefixes_enter_from}{enter_from}"
    else:
        system_link += f"{prefixes_enter_from}enter_from=email_report"
    return f"{apple_connect}?appIdKey={app_id_key}&path=/%23{system_link}"


def get_ali_system_link(system_link: str, enter_from: str = None, prefixes_enter_from: str = '?', host_config_name: str = 'ali_expert_host'):
    host = conf[host_config_name]
    # 增加email跳转埋点参数
    if enter_from:
        system_link += f"{prefixes_enter_from}{enter_from}"
    else:
        system_link += f"{prefixes_enter_from}enter_from=email_report"
    return f"{host}{system_link}"


def get_normal_system_link(host: str, system_link: str, enter_from: str = None):
    # 增加email跳转埋点参数
    if enter_from:
        system_link += f"?{enter_from}"

    return f"{host}/{system_link}"


# email_config表中from_email对应的邮箱配置信息
SYSTEM_MY_BUSINESS_EMAIL = "<EMAIL>"
SYSTEM_MY_BUSINESS_EMAIL_CONFIG_NAME = "mybusiness_mail_config"

SYSTEM_EXPERT_EMAIL = "<EMAIL>"
SYSTEM_EXPERT_EMAIL_CONFIG_NAME = "mail_config"

FROM_EMAIL_CONFIG_NAME_MAPPING = {
    SYSTEM_MY_BUSINESS_EMAIL: SYSTEM_MY_BUSINESS_EMAIL_CONFIG_NAME,  # my business 邮箱
    SYSTEM_EXPERT_EMAIL: SYSTEM_EXPERT_EMAIL_CONFIG_NAME  # expert 邮箱
}


# 根据email_config表中from_email获取邮箱配置信息
def get_main_config_by_from_email(from_email: str) -> dict:
    email_conf_name = FROM_EMAIL_CONFIG_NAME_MAPPING.get(from_email, SYSTEM_EXPERT_EMAIL_CONFIG_NAME)

    mail_conf = conf[email_conf_name]
    if os.environ.get('DB_ENV') == 'aws':
        mail_sec = get_secret_from_aws(
            mail_conf['secret_name'], mail_conf['secret_region'])
    else:
        mail_sec = get_secret_from_apple(
            mail_conf['secret_name'], mail_conf['secret_region'])

    return {
        "login": mail_sec['username'],
        "password": mail_sec['password'],
        "smtp_host": mail_conf['smtp_host'],
        "smtp_port": mail_conf['smtp_port']
    }
