import smtplib
from email.mime.text import MIMEText
from email.mime.application import MIMEApplication
from email.mime.multipart import MIMEMultipart
from email.header import Header

from util.conf import logger


class MailSender:
    host = ""
    port = 587
    login_user = ""
    password = ""
    smtp = None

    def __init__(self, host: str, port: int, login: str, pwd: str) -> None:
        self.host = host
        self.port = port
        self.login_user = login
        self.password = pwd
        self.reset_stmp()

    def reset_stmp(self):
        self.smtp = smtplib.SMTP(self.host, self.port)
        self.smtp.ehlo()
        self.smtp.starttls()
        self.smtp.login(self.login_user, self.password)

    def send(self, receivers: list, title: str, content: str, files: list = None):
        msg = MIMEText(content + '\n\r')
        if files and len(files) > 0:
            send_msg = MIMEMultipart()
            send_msg.attach(msg)
            for file in files:
                part_attach = MIMEApplication(open(file, 'rb').read())
                part_attach.add_header(
                    'Content-Disposition', 'attachment', filename=file)
                send_msg.attach(part_attach)
        else:
            send_msg = msg

        send_msg['Subject'] = title
        send_msg['From'] = self.login_user
        send_msg['To'] = ', '.join(receivers)
        if self.smtp is None:
            self.reset_stmp()
        self.smtp.sendmail(self.login_user, receivers, send_msg.as_string())

    def send_html_single(self, receiver: str, cc_list: list, title: str, content: str):
        if self.smtp is None:
            self.reset_stmp()
        send_msg = MIMEText(content, 'html')
        send_msg['Subject'] = title
        send_msg['From'] = self.login_user
        send_msg['To'] = receiver
        cc_list.append(receiver)
        self.smtp.sendmail(self.login_user, cc_list, send_msg.as_string())

    def close(self):
        if self.smtp:
            self.smtp.quit()

    def send_file(self, receivers: list, title: str, content: str,
                  files: list = None, file_name: str = None):
        msg = MIMEText(content + '\n\r')
        if files and len(files) > 0:
            send_msg = MIMEMultipart()
            send_msg.attach(msg)
            for file in files:
                part_attach = MIMEApplication(open(file, 'rb').read())
                if not file_name:
                    file_name = file
                part_attach.add_header(
                    'Content-Disposition', 'attachment', filename=file_name)
                send_msg.attach(part_attach)
        else:
            send_msg = msg

        send_msg['Subject'] = title
        send_msg['From'] = self.login_user
        send_msg['To'] = ', '.join(receivers)
        if self.smtp is None:
            self.reset_stmp()
        self.smtp.sendmail(self.login_user, receivers, send_msg.as_string())

    def send_html_file(self, receivers: list, title: str, content: str,
                       files: list = None, file_name: str = None, html_content: str = ''):
        msg = MIMEText(content + '\n\r')
        send_msg_html = MIMEText(html_content, 'html')
        if files and len(files) > 0:
            send_msg = MIMEMultipart()
            send_msg.attach(msg)
            for file in files:
                part_attach = MIMEApplication(open(file, 'rb').read())
                if not file_name:
                    file_name = file
                part_attach.add_header(
                    'Content-Disposition', 'attachment', filename=file_name)
                send_msg.attach(part_attach)
        else:
            send_msg = msg
        send_msg['Subject'] = title
        send_msg['From'] = self.login_user
        send_msg['To'] = ', '.join(receivers)
        send_msg.attach(send_msg_html)
        if self.smtp is None:
            self.reset_stmp()
        self.smtp.sendmail(self.login_user, receivers, send_msg.as_string())

    def sender(self, to_reveivers: list, cc_reveivers: list,
               subject: str, content: str, content_type: str,
               file_paths: list = None, extras: str = None, extras_type: str = None,
               bcc_reveivers: list = []):
        '''
        参数content_type和extras_type的可选值为: plain,html. 默认plain
        '''
        text_subtype = ['plain', 'html']
        send_msg = MIMEMultipart()

        # 添加邮件正文
        if content:
            content_type = content_type if content_type else 'plain'
            if content_type not in text_subtype:
                raise ValueError(f'Invalid content_type specified: {content_type}')
            mail_body = MIMEText(content, content_type, 'utf-8')
            send_msg.attach(mail_body)
            # 换行分隔
            send_msg.attach(MIMEText('\n\r'))

        # 添加附件
        if file_paths:
            for file_path in file_paths:
                with open(file_path, 'rb') as f:
                    part_attach = MIMEApplication(f.read())
                    file_name=f.name.split('/')[-1]
                    part_attach.add_header(
                        'Content-Disposition', 'attachment', filename=file_name)
                    send_msg.attach(part_attach)
            # 换行分隔
            send_msg.attach(MIMEText('\n\r'))

        # 附加内容
        if extras:
            extras_type = extras_type if extras_type else 'plain'
            mail_extra = MIMEText(extras, extras_type, 'utf-8')
            if extras_type not in text_subtype:
                raise ValueError(f'Invalid extras_type specified: {extras_type}')
            send_msg.attach(mail_extra)

        send_msg['Subject'] = Header(subject, 'utf-8')
        send_msg['From'] = self.login_user
        send_msg['To'] = ', '.join(to_reveivers)
        if cc_reveivers:
            send_msg['Cc'] = ', '.join(cc_reveivers)
        
        if bcc_reveivers:
            bcc = ", ".join(bcc_reveivers)
            send_msg['Bcc'] = bcc
            logger.info(f"邮件【{subject}】正在密送，收件人是：{bcc}")

        # 发送邮件
        if self.smtp is None:
            self.reset_stmp()
        self.smtp.send_message(send_msg)

