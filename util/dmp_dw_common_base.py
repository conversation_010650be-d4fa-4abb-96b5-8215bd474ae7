from .conf import *
from .util import *


config = conf.get('dmp_dw_common')
if os.environ.get('DB_ENV') == 'aws':
    host = config['aws_host']
    sec = get_secret_from_aws(config['secret_name'], config['secret_region'])
else :
    host = config['apple_host']
    sec = get_secret_from_apple(config['secret_name'], config['secret_region'], secret_req_host, secret_req_path)

user = sec['username']
psd = sec['password']

port = '3306'
table = config['default_db']

db_connect = "mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8".format(user, urlquote(psd), host, port, table)

engine = create_engine(db_connect, pool_pre_ping=True, pool_size=20, max_overflow=5, pool_recycle=3600)

DMPDwCommon = declarative_base()
DMPDwCommonSession = sessionmaker(bind=engine)
