SellInDemandHrOnlyTemplate = {
    "Sales Org": "sales_org",
    "RTML4": "rtml4",
    "LOB / FPH L1": "lob",
    "Prod / FPH L3": "prod",
    "Project Code": "project_code",
    "Nand": "nand",
    "Color": "color",
    "MPN / Apple Part #": "mpn",
    "ODQ": "odq",
    "Customer Sold-to ID": "sold_to_id",
    "Customer Sold-to Name": "sold_to_name_en",
    "QTW Shipment Plan": "qtw_shipment_plan",
    "CW Shipment Plan (Discrete)": "shipment_plan_cw",
    "CW+1 Shipment Plan (Discrete)": "shipment_plan_cw1",
    "CW+2 Shipment Plan (Discrete)": "shipment_plan_cw2",
    "CW+3 Shipment Plan (Discrete)": "shipment_plan_cw3",
    "CW Backlog Gap": "cw_backlog_gap",
    "Top Up Demand CW+1": "top_up_demand_cw1",
    "Top Up Demand CW+2": "top_up_demand_cw2",
    "Top Up Demand CW+3": "top_up_demand_cw3",
    "Top Up Demand CW+4": "top_up_demand_cw4",
    "Discrete CW+1": "discrete_cw1",
    "Discrete CW+2": "discrete_cw2",
    "Discrete CW+3": "discrete_cw3",
    "Discrete CW+4": "discrete_cw4",
}

SellInDemandHrOnlyRtmList = ['Mono', 'Multi', 'Online']

SellInDemandHrOnlyTemplateFileHeader = list(SellInDemandHrOnlyTemplate.keys())
SellInDemandHrOnlyTemplateFileRawHeader = list(SellInDemandHrOnlyTemplate.values())

# 只有uploads目录挂载到外部可以持久化
cpf_file_url_prefix = {
    'file_storage': '/file/storage',
    'uploads': '/uploads/allocation',
    'sell_in_demand': '/uploads/system-generation/sell-in-demand',
    'sell_in_demand_merge': '/tempdir/system-generation/sell-in-demand/merge',
    'sell_in_demand_merge_publish': '/tempdir/system-generation/sell-in-demand/merge/publish',
}

HrOnlySummaryHeader = ['Model', 'Project Code', 'QTW Shipment Plan', 'Shipment Plan CW',
                       'Shipment Plan CW+1', 'Shipment Plan CW+2',
                       'Top Up Demand CW+1', 'Top Up Demand CW+2',
                       'Top Up Demand CW+3', 'Top Up Demand CW+4']


dev_email_receivers = ['<EMAIL>', '<EMAIL>']

class FirstPhaseErrorProcess:
    NoOperation = 0
    Confirm  = 1
    Upload = 2
    Rerun = 3
    Timeout = 4
    
class AllocationRunErrorMsg:
    SupplyUploadCalculateError = "The 4 weeks’ Shipment Plan and the Total Incremental Supply cannot cover the Pull In Qty."
    SupplyProtectionStrategyCalculateError = [
        "the quantity of shipment plan for the following MPNs is insufficient to meet the allocation for reserved portion: ",
        "the quantity of shipment plan for the following MPNs is insufficient to meet the allocation for reserved portion: ",
        "the quantity of shipment plan for the following MPNs is insufficient to meet the allocation for reserved portion: ",
        "the quantity of shipment plan for the following MPNs is insufficient to meet the allocation for reserved portion: "
    ]
    SpecialSupplyCalculateError = "The number of Ture Incremental is less than the number of Special Supply for the following MPNs: "
    
