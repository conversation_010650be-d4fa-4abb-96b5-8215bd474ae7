from util.conf import *
from urllib.parse import quote_plus as urlquote
from sqlalchemy import or_, and_, func, desc, asc, union_all, literal_column, text


mybusiness_config = conf['mybusiness']

if os.environ.get('DB_ENV') == 'aws':
    host = mybusiness_config['aws_host']
    secret = get_secret_from_aws(mybusiness_config['secret_name'], 
                                 mybusiness_config['secret_region'])
    port = mybusiness_config['aws_port']
else :
    host = mybusiness_config['apple_host']
    secret = get_secret_from_apple(mybusiness_config['secret_name'], 
                                   mybusiness_config['secret_region'], 
                                   secret_req_host, secret_req_path)
    port = mybusiness_config['apple_port']
    
username = secret['username']
password = secret['password']

database = mybusiness_config['default_db']

mybusiness_db_url = "mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8".format(username, urlquote(password), host, port, database)

MyBusinessEngine = create_engine(mybusiness_db_url, pool_pre_ping=True, pool_size=20, max_overflow=5, pool_recycle=3600)

MyBusinessBase = declarative_base()
MyBusinessSession = sessionmaker(bind=MyBusinessEngine)
