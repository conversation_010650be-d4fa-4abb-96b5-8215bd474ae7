from datetime import datetime, time
import traceback

from flask_apscheduler import APScheduler

from apis.datasource.supply import do_fetch_supply
from data.datasource_automatic_update_record import DataSourceFileRecordRepository, DatasourceAutomaticUpdateRecord, \
    SYSTEM_OPERATOR
from data.fiscal_year_week import FiscalYearWeek
from data.mysqls.demand.demand_y_setting import IdealDemandYValueSetting
from data.mysqls.demand.rtm_sales_forecast_upload import FastLiteRTMSalesForecastUpload
from data.s3bucket.supply_data import list_files
from domain.dashboard.entity.anti_fraud.anti_fraud_so_ub import RTM_Mono, RTM_CARRIER, RTM_MULTI, RTM_EDU
from domain.baozun.entity.hitl_image import HitlImg
from domain.baozun.scan_image_func import handle_gbi_image_data, notify_not_sync_yet, scan_image_data
from domain.dashboard.impl.fiscal_week_container import FiscalWeekContainer
from domain.datasource.impl.automatic import CTO_CHANNEL_PERMISSIONS, ESR_CHANNEL_PERMISSIONS, KeySubKeyRelation, \
    AutomaticDataType, \
    Automatic_DB_NAME, DataRefreshVersion, EsrTableName, ESR_COB_START_TIME, ESR_COB_END_TIME, ESR_MORNING_START_TIME, \
    ESR_MORNING_END_TIME, ESR_AFTERNOON_START_TIME, ESR_AFTERNOON_END_TIME
from domain.datasource.impl.automatic.cto_task_impl import create_cto_generate_files_task, get_created_cto_tasks, \
    monitor_cto
from domain.datasource.impl.automatic.esr_task_impl import create_esr_task, generate_esr_task, get_reports_todo
from domain.demand.entity.const import IDEAL_DEMAND, TOPDOWN_DEMAND, NORMALIZED_DEMAND, DELTA_DEMAND, FINAL_DEMAND, \
    SELL_IN_DEMAND
from domain.demand.entity.const import RTMS
from domain.demand.entity.module_switch_enum import ModuleSwitchEnum
from domain.demand.impl.calculator.calculator_factory import calculator_factory
from domain.demand.impl.demand_rtm_sales_forecast import auto_generate_rtm_sales_forecast, \
    do_moniter_rtms_upload_forecast
from domain.demand.impl.initialize import DemandPoolInitializer, DemandSettingInitializer
from domain.demand.impl.processor.const import *
from domain.demand.impl.processor.processor_factory import ProcessFactory
from domain.demand.impl.sellin_woi_setting import auto_publish_final_demand_woi
from domain.demand.impl.state_machine import StateProxy
from domain.demand.impl.y_value_setting import get_demand_fiscal_weeks
from domain.hitl.hitl_sync_data import sync_data
from domain.mono.pos_allocation.impl.rewrite_pos_mpn_datasource import RewritePosMpnDatasource
from domain.supply.entity import Lob
from domain.usage.impl.write_event_tracking_info import WriteEventTrackingInfo, DAU, WAU, MAU
from kit.custom_date.custom_week_date import CustomWeekDate
from kit.token_bucket import EMAIL_BUCKET_KEY_PREFIX
from service.cpf_sell_in_demand_service import sell_in_demand_with_tags, refresh_sell_in_demand_with_tags_mono_hr_only, \
    generate_sell_in_demand_mono_hr_only
from service.ideal_demand_service import generate_week_record, generate_final_forecast, \
    rtm_not_upload_forecast_reminder, rtm_not_upload_forecast_expired, \
    cpf_not_upload_adjustment_reminder, cpf_not_upload_adjustment_expired, \
    is_so_eoh_data_ready_service, is_cpf_ideal_demand_data_ready_service
from task.cpf_prepare_demand_submission_mono import main as submission_mono_demand_data
from task.cpf_prepare_demand_submission_others import main as submission_others_demand_data
from task.cpf_prepare_demand_submission_template import main as submission_multi_online_template
from task.task_allocation_prepare_record import generate_allocation_record
from task.task_allocation_run_generate_template import generate_allocation_run_template
from task.task_change_rtms_status import change_status_by_week_at_10_clock, change_status_by_week_at_16_clock
from task.task_check_esr_mon_wed import check_esr_status
from task.task_cpf_data_source_snapshot import generate_active_sku_snapshot
from task.task_demand_adjustment_template_file import generate_demand_adjustment_template_file
from task.task_open_rtm_phase import open_phase_by_week_rtm
from task.task_sales_input_template_file import generate_sales_input_template_file_all_rtm, \
    check_open_backlog_less_than_monday, cover_upload_file_by_template_file_all_rtm, \
    send_email_after_system_open, send_email_after_refresh_esr_on_tuesday
from task.task_sell_in_demand_mono_data_error import check_mono_not_exceed_sales_input
from task.task_sell_in_demand_remind_email import timed_reminder_email
from task.task_sell_in_demand_with_tags_multi_online import task_regenerate_multi_online_demand_with_tags_file
from task_kit.register_task import (AUTOMATIC_CTO_POD_GEN_FILE, schedule_task, TASK_TYPE_SEND_ANTI_FRAUD_SO_UB_EMAIL,
                                    TASK_TYPE_SEND_EOH_AGING_UB_WITHOUT_DS_EMAIL,
                                    TASK_TYPE_SEND_POS_SUSPENSION_EMAIL, AUTOMATIC_ESR_MAIN_PRODUCTS_GEN_FILE,
                                    AUTOMATIC_ESR_ACCESSORIES_GEN_FILE)
from task_kit.register_task import (schedule_task_slowly)
from task_kit.repository.task_repository import TaskRepository, Task, TaskStatus
from util.const import *
from util.redis_pool import *
from util.scheduler_task_record_template import PO_GAP_DELINQUENT_TASK_LIST
from util.send_email import async_send_email, async_rate_limited_send_email
from util.util import env_dev
from util.pagerduty import send_alert


class Config(object):
    SCHEDULER_TIMEZONE = 'Asia/Shanghai'
    SCHEDULER_API_ENABLED = True


scheduler = APScheduler()

PROJECT_NAME = 'Fast_Lite_Allocation'


@scheduler.task('cron', name="每周datasource supply数据自动归档", id="datasource_supply_auto_archived", minute="*/10")
@redis_lock(f'{RedisCachePrefix}:datasource_supply_auto_archived', lock_timeout=10)
def datasource_supply_auto_archived():
    lobs = Lob.all()
    # 已归档的历史数据
    records = DataSourceFileRecordRepository.query_latest_archived_by_system(lobs)

    # 扫描s3目录，获取当前最新的财年周
    for lob in lobs:
        lob_records = [r for r in records if r.lob == lob]
        try:
            do_datasource_supply_auto_archived(lob, lob_records)
        except ErrorExcept as e:
            # 通知开发者执行情况
            developer_subject = f"{os.environ.get(StrENV)} {PROJECT_NAME} - do_datasource_supply_auto_archived failed."
            async_send_email(developer_subject, f"每周datasource supply数据自动归档失败,e:{e}")


def do_datasource_supply_auto_archived(lob, lob_records):
    files, weeks = list_files(prefix=f"databend-dumps/FAST/PHOEBE/{lob}/")
    # 如果当前只有一个财年周，则不需要对上个财年周进行归档
    if len(weeks) < 2:
        return

    for i in range(1, len(weeks)):
        if i > 2:
            break
        archive_week = weeks[i]
        if not list(filter(lambda r: r.fiscal_qtr_week_name == archive_week, lob_records)):
            # 触发归档
            logger.info(f"[supplydata] {lob} weeks:{weeks} archive_week: {archive_week}")
            file, params = do_fetch_supply(0, lob, files, archive_week, False)
            record = DatasourceAutomaticUpdateRecord(
                fiscal_qtr_week_name=archive_week,
                datasource_type=AutomicJobType.SupplyData,
                upload_by=SYSTEM_OPERATOR,
                lob=lob,
                rtm=StrRTMCPF,
                file_name=file,
                params=params,
                create_at=params.get("time"),
                update_at=params.get("time"),
                status=DataSourceFileStatus.Enabled
            )
            DataSourceFileRecordRepository.create(record)
            logger.info(f"[supplydata {lob} {archive_week}] trigger archived. {file} {params}.")


@scheduler.task('cron',
                name='每周一14:00, 同步active sku list快照',
                id='allocation_job_1_id', day_of_week='mon', hour=14, minute=0, start_date='2023-06-08')
@redis_lock(f'{RedisCachePrefix}:allocation_job_1_id')
def allocation_job_1():
    '''
    每周一14:00, 同步active sku list快照
    '''
    fiscal_dt = datetime.now().strftime(DateFormat)
    content = generate_active_sku_snapshot(fiscal_dt)
    # 通知开发者执行情况
    developer_subject = f"{os.environ.get(StrENV)} {PROJECT_NAME} - Allocation Active SKU List Snapshot."
    async_send_email(developer_subject, content)


@scheduler.task('cron',
                name='每周一17:00~17:30之间轮训查询esr、template状态, 如果ready生成对应周的RTMs、CP&F列表页记录, 如果17:30还未ready, 依然生成记录',
                id='allocation_job_2_id', day_of_week='mon', hour=17, minute='0-30/3')
@redis_lock(f'{RedisCachePrefix}:allocation_job_2_id')
def allocation_job_2():
    '''
    每周一17:00~17:30之间轮训查询esr、template状态, 如果ready生成对应周的RTMs、CP&F列表页记录, 如果17:30还未ready, 依然生成记录
    '''
    fiscal_dt = datetime.now().strftime(DateFormat)
    developer_subject = f"{os.environ.get('ENV')} {PROJECT_NAME} - Allocation Automatically Generate Record."
    try:
        content = generate_allocation_record(fiscal_dt)
        if not content:
            exit(1)
        content += generate_sales_input_template_file_all_rtm(fiscal_dt)
        content += send_email_after_system_open(fiscal_dt)
        async_send_email(developer_subject, content)
    except ErrorExcept as e:
        async_send_email(developer_subject, "生成数据失败")


@scheduler.task('cron',
                name='每周二09:30重刷Sales Input模版以及覆盖上传的文件，并检查open_backlog是否比周一的小',
                id='allocation_job_2_1_id', day_of_week='tue', hour=9, minute=30)
@redis_lock(f'{RedisCachePrefix}:allocation_job_2_1_id')
def allocation_job_2_1():
    '''每周二09:30重刷Sales Input模版以及覆盖上传的文件，并检查open_backlog是否比周一的小'''
    fiscal_dt = datetime.now().strftime(DateFormat)
    developer_subject = f"{os.environ.get('ENV')} {PROJECT_NAME} - Regenerate Allocation Sales Input template file and wheather open_backlog less than monday"
    try:
        content = generate_sales_input_template_file_all_rtm(fiscal_dt)
        content += cover_upload_file_by_template_file_all_rtm(fiscal_dt)
        content += check_open_backlog_less_than_monday(fiscal_dt)
        content += send_email_after_refresh_esr_on_tuesday(fiscal_dt)
        async_send_email(developer_subject, content)
    except ErrorExcept as e:
        async_send_email(developer_subject, "重新生成Sales Input模版失败")


@scheduler.task('cron',
                name='每周一17:30, 检查esr、template状态, 如果未ready, 通知RTMs、CP&F',
                id='allocation_job_3_id', day_of_week='mon', hour=17, minute=30)
@redis_lock(f'{RedisCachePrefix}:allocation_job_3_id')
def allocation_job_3():
    '''
    每周一17:30, 检查esr、template状态, 如果未ready, 同时RTMs、CP&F
    '''
    fiscal_dt = datetime.now().strftime(DateFormat)
    ret = check_esr_status('1', fiscal_dt)
    developer_subject = f"{os.environ.get('ENV')} {PROJECT_NAME} - Monday ESR Status Check."
    content = "Ready." if ret else "Unready."
    async_send_email(developer_subject, content)


@scheduler.task('cron',
                name='每周二09:15, 检查esr状态, 如果未ready, 通知CP&F',
                id='allocation_job_3_1_id', day_of_week='tue', hour=9, minute=15)
@redis_lock(f'{RedisCachePrefix}:allocation_job_3_1_id')
def allocation_job_3_1():
    '''每周二09:15, 检查esr状态, 如果未ready, 通知CP&F'''
    fiscal_dt = datetime.now().strftime(DateFormat)
    ret = check_esr_status('2', fiscal_dt)
    developer_subject = f"{os.environ.get('ENV')} {PROJECT_NAME} - Check Tuesday ESR Status"
    content = "Ready." if ret else "Unready."
    async_send_email(developer_subject, content)


@scheduler.task('cron',
                name='每周二13:50, 执行:1、生成Multi/Online Sell-in Demand 模版文件;',
                id='allocation_job_4_id', day_of_week='tue', hour=13, minute=50)
@redis_lock(f'{RedisCachePrefix}:allocation_job_4_id')
def allocation_job_4():
    '''
    每周二13:50执行
    1、生成Multi/Online Sell-in Demand 模版文件
    '''
    developer_subject = f"{os.environ.get('ENV')} {PROJECT_NAME} - generate Demand Submission (Sell in Demand) data and file."
    try:
        fiscal_dt = datetime.now().strftime(DateFormat)
        # 生成Multi/Online Sell-in Demand 模版文件
        submission_multi_online_template(fiscal_dt, SubmissionExecuteLobs)

        async_send_email(developer_subject, '全部正常生成数据')
    except ErrorExcept as e:
        async_send_email(developer_subject, f"生成数据失败, {e}")


@scheduler.task('cron',
                name='每周二14:00, 开启Multi/Online Sell-in Demand阶段',
                id='allocation_job_5_id', day_of_week='tue', hour=14, minute=00)
@redis_lock(f'{RedisCachePrefix}:allocation_job_5_id')
def allocation_job_5():
    '''
    每周二14:00, 开启Multi/Online Sell-in Demand阶段
    '''
    fiscal_dt = datetime.now().strftime(DateFormat)
    developer_subject = f"{os.environ.get('ENV')} {PROJECT_NAME} - Multi and Online open Demand Submission phase."
    content = ''
    for rtm in [AllocationRTM.Multi, AllocationRTM.Online]:
        ret = open_phase_by_week_rtm(fiscal_dt, rtm, str(RTMAllocationPhase.DemandSubmission))
        content += f"{ret}{DOUBLE_LINE_FEED}"
    async_send_email(developer_subject, content)


@scheduler.task('cron',
                name='每周二10:00, 检查第一阶段状态, not upload',
                id='allocation_job_5_1_id', day_of_week='tue', hour=10, minute=0)
@redis_lock(f'{RedisCachePrefix}:allocation_job_5_1_id')
def allocation_job_5_1():
    '''
    每周二10:00, 检查第一阶段状态, not upload
    '''
    fiscal_dt = datetime.now().strftime(DateFormat)
    developer_subject = f"{os.environ.get('ENV')} {PROJECT_NAME} - 检查各个RTM第一阶段状态 Not Upload"
    content = change_status_by_week_at_10_clock(fiscal_dt)
    async_send_email(developer_subject, f'{DOUBLE_LINE_FEED}'.join(content))


@scheduler.task('cron',
                name='每周二16:00, 检查第一阶段状态, uncompleted',
                id='allocation_job_5_2_id', day_of_week='tue', hour=16, minute=0)
@redis_lock(f'{RedisCachePrefix}:allocation_job_5_2_id')
def allocation_job_5_2():
    '''
    每周二16:00, 检查第一阶段状态, uncompleted
    '''
    fiscal_dt = datetime.now().strftime(DateFormat)
    developer_subject = f"{os.environ.get('ENV')} {PROJECT_NAME} - 检查各个RTM第一阶段状态 Uncompleted"
    content = change_status_by_week_at_16_clock(fiscal_dt)
    async_send_email(developer_subject, f'{DOUBLE_LINE_FEED}'.join(content))


@scheduler.task('cron',
                name='每周二18:30, Multi/Online 未提交Demand数据, 提前通知Multi/Online',
                id='allocation_job_6_id', day_of_week='tue', hour=18, minute=30)
@redis_lock(f'{RedisCachePrefix}:allocation_job_6_id')
def allocation_job_6():
    '''
    每周二18:30, Multi/Online 未提交Demand数据, 提前通知Multi/Online
    '''
    fiscal_dt = datetime.now().strftime(DateFormat)
    content = ''
    for rtm in [AllocationRTM.Multi, AllocationRTM.Online]:
        ret = timed_reminder_email('1', rtm, fiscal_dt)
        content += f"{rtm}: upload status is {ret} {DOUBLE_LINE_FEED}"
    developer_subject = f"{os.environ.get('ENV')} {PROJECT_NAME} - Multi/Online 检查Demand数据是否提交"
    async_send_email(developer_subject, content)


@scheduler.task('cron',
                name='每周二19:00, 1、Multi/Online 未提交Demand数据, 通知Multi/CP&F,Online/CP&F; 2、关闭Multi/Online Sell-in Demand阶段',
                id='allocation_job_7_id', day_of_week='tue', hour=19, minute=00)
@redis_lock(f'{RedisCachePrefix}:allocation_job_7_id')
def allocation_job_7():
    '''
    每周二19:00,
    1、Multi/Online 未提交Demand数据, 通知Multi/CP&F,Online/CP&F
    2、关闭Multi/Online Sell-in Demand阶段
    '''
    fiscal_dt = datetime.now().strftime(DateFormat)
    content = ''
    # Multi/Online 未提交Demand数据, 通知Multi/CP&F,Online/CP&F
    for rtm in [AllocationRTM.Multi, AllocationRTM.Online]:
        ret = timed_reminder_email('2', rtm, fiscal_dt)
        content += f"{rtm}: upload status is {ret} {DOUBLE_LINE_FEED}"
    developer_subject = f"{os.environ.get('ENV')} {PROJECT_NAME} - Multi/Online 依旧未提交Demand数据状态"
    async_send_email(developer_subject, content)


@scheduler.task('cron',
                name='每周二17:00, 生成Mono Demand文件和HR only文件',
                id='allocation_job_8_id', day_of_week='tue', hour=17, minute=0)
@redis_lock(f'{RedisCachePrefix}:allocation_job_8_id')
def allocation_job_8():
    '''
    --每周二21:30, 生成Mono Demand文件 以及 with tags 和Mono HR 文件--
    每周二17:00, 生成Mono Demand文件和HR only文件
    '''
    developer_subject = f"{os.environ.get('ENV')} {PROJECT_NAME} - 生成Mono Demand文件和HR only文件"
    try:
        fiscal_dt = datetime.now().strftime(DateFormat)

        submission_mono_demand_data(fiscal_dt, SubmissionExecuteLobs)
        generate_sell_in_demand_mono_hr_only(fiscal_dt)

        async_send_email(developer_subject, '全部正常生成数据')
    except ErrorExcept as e:
        async_send_email(developer_subject, f"生成数据失败, {e}")


@scheduler.task('cron',
                name='每周二19:00, 生成Others Demand文件和RTMs Demand with tags文件',
                id='allocation_job_8_2_id', day_of_week='tue', hour=19, minute=0)
@redis_lock(f'{RedisCachePrefix}:allocation_job_8_2_id')
def allocation_job_8_2():
    '''
    每周二19:00, 生成Others Demand文件和RTMs Demand with tags文件
    '''
    developer_subject = f"{os.environ.get('ENV')} {PROJECT_NAME} - 生成Others Demand文件和RTMs Demand with tags文件"
    try:
        fiscal_dt = datetime.now().strftime(DateFormat)

        submission_others_demand_data(fiscal_dt, SubmissionExecuteLobs)
        sell_in_demand_with_tags(fiscal_dt)

        async_send_email(developer_subject, '全部正常生成数据')
    except ErrorExcept as e:
        async_send_email(developer_subject, f"生成数据失败, {e}")


@scheduler.task('cron',
                name='每周二17:00, 开启Mono/Others第二步',
                id='allocation_job_8_1_id', day_of_week='tue', hour=17, minute=0)
@redis_lock(f'{RedisCachePrefix}:allocation_job_8_1_id')
def allocation_job_8_1():
    '''
    每周二22:00->17:00, 开启Mono/Others第二步
    '''
    fiscal_dt = datetime.now().strftime(DateFormat)
    developer_subject = f"{os.environ.get('ENV')} {PROJECT_NAME} - Open Multi and Online Demand Submission phase."
    content = ''
    for rtm in [AllocationRTM.Mono, AllocationRTM.Carrier,
                AllocationRTM.EDU, AllocationRTM.ENT,
                AllocationRTM.HKTWCarrier, AllocationRTM.HKTWRP,
                AllocationRTM.HKTWEDU, AllocationRTM.HKTWENT]:
        ret = open_phase_by_week_rtm(fiscal_dt, rtm, str(RTMAllocationPhase.DemandSubmission))
        content += f"{ret}{DOUBLE_LINE_FEED}"
    async_send_email(developer_subject, content)


@scheduler.task('cron',
                name='每周二17:30, 从Mono FAST取来的Demand数据出了比Sales Input小的情况时, 发邮件提醒CP&F业务方，进入系统手动处理',
                id='allocation_job_9_id', day_of_week='tue', hour=17, minute=30)
@redis_lock(f'{RedisCachePrefix}:allocation_job_9_id')
def allocation_job_9():
    '''
    每周二22:00->17:30, 从Mono FAST取来的Demand数据出了比Sales Input小的情况时, 发邮件提醒CP&F业务方，进入系统手动处理
    '''
    fiscal_dt = datetime.now().strftime(DateFormat)
    developer_subject = f"{os.environ.get('ENV')} {PROJECT_NAME} - Mono Data Not Exceed Sales Input."
    content = check_mono_not_exceed_sales_input(fiscal_dt)
    content = f'Status is {content}.'
    async_send_email(developer_subject, content)


@scheduler.task('cron',
                name='周三重刷esr之后需要重新执行生成with tags数据 以及 mono hr',
                id='allocation_job_10_id', day_of_week='wed', hour=17, minute=30)
@redis_lock(f'{RedisCachePrefix}:allocation_job_10_id')
def allocation_job_10():
    '''
    周三重刷esr之后需要重新执行生成with tags数据
    '''
    developer_subject = f"{os.environ.get('ENV')} {PROJECT_NAME} - 周三重刷esr后重新生成Mono/Others Demand文件 以及 with tags 和Mono HR 文件"
    try:
        fiscal_dt = datetime.now().strftime(DateFormat)

        submission_mono_demand_data(fiscal_dt, SubmissionExecuteLobs)
        submission_others_demand_data(fiscal_dt, SubmissionExecuteLobs)

        refresh_sell_in_demand_with_tags_mono_hr_only(fiscal_dt)

        async_send_email(developer_subject, '全部正常生成数据')
    except ErrorExcept as e:
        async_send_email(developer_subject, f"生成数据失败, {e}")


@scheduler.task('cron',
                name='周三10:00如有cpf upload file，重新生成Multi/Online Demand with tags数据',
                id='allocation_job_10_1_id', day_of_week='wed', hour=10, minute=0)
@redis_lock(f'{RedisCachePrefix}:allocation_job_10_1_id')
def allocation_job_10_1():
    '''周三10:00如有cpf upload file，重新生成Multi/Online Demand with tags数据'''
    developer_subject = f"{os.environ.get('ENV')} {PROJECT_NAME} - 周三10:00重新生成Multi/Online Demand with tags数据"
    try:
        fiscal_dt = datetime.now().strftime(DateFormat)
        content = task_regenerate_multi_online_demand_with_tags_file(fiscal_dt)
        async_send_email(developer_subject, content)
    except ErrorExcept as e:
        async_send_email(developer_subject, f"生成数据失败, {e}")


@scheduler.task('cron',
                name='每周三17:30, 检查esr状态, 如果不ready, 邮件通知; 如果ready, 开启第三阶段',
                id='allocation_job_11_id', day_of_week='wed', hour=17, minute=30)
@redis_lock(f'{RedisCachePrefix}:allocation_job_11_id')
def allocation_job_11():
    '''
    每周三17:30, 检查esr状态, 如果不ready, 邮件通知; 如果ready, 开启第三阶段
    '''
    fiscal_dt = datetime.now().strftime(DateFormat)
    ret = check_esr_status('3', fiscal_dt)
    developer_subject = f"{os.environ.get('ENV')} {PROJECT_NAME} - Check Wednesday ESR Status."
    content = f"Ready.{DOUBLE_LINE_FEED}" if ret else f"Unready.{DOUBLE_LINE_FEED}"
    if ret:
        # 开启第三阶段
        for rtm in AllocationRTMList:
            ret = open_phase_by_week_rtm(fiscal_dt, rtm, str(RTMAllocationPhase.DemandAdjustment))
            content += f"{ret}{DOUBLE_LINE_FEED}"
    async_send_email(developer_subject, content)


@scheduler.task('cron',
                name='每周三17:30, 生成第三步模版文件',
                id='allocation_job_12_id', day_of_week='wed', hour=17, minute=30)
@redis_lock(f'{RedisCachePrefix}:allocation_job_12_id')
def allocation_job_12():
    '''
    每周三17:30, 生成第三步模版文件
    '''
    fiscal_dt = datetime.now().strftime(DateFormat)
    developer_subject = f"{os.environ.get('ENV')} {PROJECT_NAME} - 生成第三步模版文件"
    content = ''
    for rtm in AllocationRTMList:
        ret = generate_demand_adjustment_template_file(fiscal_dt, rtm)
        content += f"{rtm}: {ret}{DOUBLE_LINE_FEED}"
    content = '未生成模版文件，请检查。' if content == '' else content
    async_send_email(developer_subject, content)


@scheduler.task('cron',
                name='Ideal Demand, 每周一8:00生成Ideal Demand周数据',
                id='ideal_demand_job_1_id', day_of_week='mon', hour=8, minute=0)
@redis_lock(f'{RedisCachePrefix}:ideal_demand_job_1_id')
def ideal_demand_job_1():
    fiscal_dt = datetime.now().strftime(DateFormat)
    developer_subject = f"{os.environ.get('ENV')} Ideal Demand - Generate Week Data."
    content = generate_week_record(fiscal_dt)
    async_send_email(developer_subject, content)


@scheduler.task('cron',
                name='Ideal Demand, 每周二11:30, 13:30未上传文件提醒',
                id='ideal_demand_job_2_id', day_of_week='tue', hour='11, 13', minute=30)
@redis_lock(f'{RedisCachePrefix}:ideal_demand_job_2_id')
def ideal_demand_job_2():
    fiscal_dt = datetime.now().strftime(DateFormat)
    content = ''
    result = []
    for rtm in [AllocationRTM.Mono, AllocationRTM.Multi,
                AllocationRTM.Carrier, AllocationRTM.Online,
                AllocationRTM.EDU, AllocationRTM.ENT]:
        ret = rtm_not_upload_forecast_reminder(fiscal_dt, rtm)
        result.append(ret)

    developer_subject = f"{os.environ.get('ENV')} Ideal Demand - check if upload file or not, reminder."
    content = DOUBLE_LINE_FEED.join(result)
    async_send_email(developer_subject, content)


@scheduler.task('cron',
                name='Ideal Demand, 每周二14:00rtm forecast超时仍未上传文件',
                id='ideal_demand_job_3_id', day_of_week='tue', hour=14, minute=0)
@redis_lock(f'{RedisCachePrefix}:ideal_demand_job_3_id')
def ideal_demand_job_3():
    fiscal_dt = datetime.now().strftime(DateFormat)
    content = ''
    result = []
    for rtm in [AllocationRTM.Mono, AllocationRTM.Multi,
                AllocationRTM.Carrier, AllocationRTM.Online,
                AllocationRTM.EDU, AllocationRTM.ENT]:
        ret = rtm_not_upload_forecast_expired(fiscal_dt, rtm)
        result.append(ret)

    developer_subject = f"{os.environ.get('ENV')} Ideal Demand - check has not upload file, expired."
    content = DOUBLE_LINE_FEED.join(result)
    async_send_email(developer_subject, content)


@scheduler.task('cron',
                name='Ideal Demand, 每周二14:00生成Final Forecast数据',
                id='ideal_demand_job_4_id', day_of_week='tue', hour=14, minute=0)
@redis_lock(f'{RedisCachePrefix}:ideal_demand_job_4_id')
def ideal_demand_job_4():
    fiscal_dt = datetime.now().strftime(DateFormat)
    developer_subject = f"{os.environ.get('ENV')} Ideal Demand - Generate Week Data."
    content = generate_final_forecast(fiscal_dt)
    async_send_email(developer_subject, content)


@scheduler.task('cron',
                name='Ideal Demand, 每周二18:00未上传文件提醒',
                id='ideal_demand_job_5_id', day_of_week='tue', hour='18', minute=0)
@redis_lock(f'{RedisCachePrefix}:ideal_demand_job_5_id')
def ideal_demand_job_5():
    fiscal_dt = datetime.now().strftime(DateFormat)
    content = ''
    result = []
    cpf_x = cpf_not_upload_adjustment_reminder(fiscal_dt, 'x')
    result.append(cpf_x)
    cpf_y = cpf_not_upload_adjustment_reminder(fiscal_dt, 'y')
    result.append(cpf_y)

    developer_subject = f"{os.environ.get('ENV')} Ideal Demand - check if upload file or not, reminder."
    content = DOUBLE_LINE_FEED.join(result)
    async_send_email(developer_subject, content)


@scheduler.task('cron',
                name='Ideal Demand, 每周二19:00 adjustment x,y超时仍未上传文件',
                id='ideal_demand_job_6_id', day_of_week='tue', hour=19, minute=0)
@redis_lock(f'{RedisCachePrefix}:ideal_demand_job_6_id')
def ideal_demand_job_6():
    fiscal_dt = datetime.now().strftime(DateFormat)
    content = ''
    result = []
    cpf_x = cpf_not_upload_adjustment_expired(fiscal_dt, 'x')
    result.append(cpf_x)
    cpf_y = cpf_not_upload_adjustment_expired(fiscal_dt, 'y')
    result.append(cpf_y)

    developer_subject = f"{os.environ.get('ENV')} Ideal Demand - check has not upload file, expired."
    content = DOUBLE_LINE_FEED.join(result)
    async_send_email(developer_subject, content)


@scheduler.task('cron',
                name='Ideal Demand, 每周二15:00检查so_eoh数据状态',
                id='ideal_demand_job_7_id', day_of_week='tue', hour=15, minute=0)
@redis_lock(f'{RedisCachePrefix}:ideal_demand_job_7_id')
def ideal_demand_job_7():
    fiscal_dt = datetime.now().strftime(DateFormat)
    content = ''
    result = []
    for rtm in [AllocationRTM.Mono, AllocationRTM.Online]:
        result.append(is_so_eoh_data_ready_service(fiscal_dt, rtm))

    developer_subject = f"{os.environ.get('ENV')} Ideal Demand - check so eoh data ready."
    content = DOUBLE_LINE_FEED.join(result)
    async_send_email(developer_subject, content)


@scheduler.task('cron',
                name='Ideal Demand, 每周三0:00检查cpf ideal demand数据状态',
                id='ideal_demand_job_8_id', day_of_week='wed', hour=0, minute=0)
@redis_lock(f'{RedisCachePrefix}:ideal_demand_job_8_id')
def ideal_demand_job_8():
    fiscal_dt = datetime.now().strftime(DateFormat)
    content = ''

    developer_subject = f"{os.environ.get('ENV')} Ideal Demand - check cpf ideal demand data status"
    content = is_cpf_ideal_demand_data_ready_service(fiscal_dt)
    async_send_email(developer_subject, content)


@scheduler.task('cron',
                name='Allocation Run, 每周三17:40生成模版',
                id='allocation_run_job_1_id', day_of_week='wed', hour=17, minute=40)
@redis_lock(f'{RedisCachePrefix}:allocation_run_job_1_id')
def allocation_run_job_1():
    fiscal_dt = datetime.now().strftime(DateFormat)
    content = ''

    developer_subject = f"{os.environ.get('ENV')} Allocation Run - Generate Template"
    content = generate_allocation_run_template(fiscal_dt)
    if not content:
        content = 'Generate Successfully'
    async_send_email(developer_subject, content)


@scheduler.task('cron',
                name='Ideal Demand, 每周日0:00生成y setting 和 x setting初始数据 ,soldto, mpn',
                id='ideal_demand_job_9_id', minute="*/1")
@redis_lock(f'{RedisCachePrefix}:ideal_demand_job_9_id', lock_timeout=30)
def ideal_demand_job_9():
    fiscal_weeks = get_demand_fiscal_weeks(IDEAL_DEMAND)
    first_five_weeks = [FiscalWeekContainer().get_current_week()] + fiscal_weeks[:5]
    for current_week in first_five_weeks:
        try:
            # current_week: str = FiscalWeekContainer().get_current_week()
            state_proxy = StateProxy(current_week, IDEAL_DEMAND)
            if not state_proxy.after_start():
                # y和x
                DemandSettingInitializer(current_week).init()
                # demand_by_soldto_pool、demand_by_region_pool 初始化
                DemandPoolInitializer(current_week).init()
                # 扭转状态，任务完成
                state_proxy.do_initialize()
        except (ErrorExcept, Exception) as e:
            logger.error(e)
            developer_subject = f"{os.environ.get('ENV')} Ideal Demand - y setting/x setting/soldto_mpn_pool init."
            async_rate_limited_send_email(EMAIL_BUCKET_KEY_PREFIX + 'demand-init-' + current_week, 2, 1800,
                                          developer_subject,
                                          f"每周Ideal Demand - y setting/x setting/soldto_mpn_pool init数据初始化失败,e:{traceback.format_exc()}")


@scheduler.task('cron',
                name='Demand, 于每周二10:30开始计算所有demand',
                id='calculate_demand_job', minute="*/1", max_instances=1)
@redis_lock(f'{RedisCachePrefix}:calculate_demand_job', lock_timeout=30)
def calculate_demand_job():
    demands = [IDEAL_DEMAND, TOPDOWN_DEMAND, SELL_IN_DEMAND, NORMALIZED_DEMAND, DELTA_DEMAND, FINAL_DEMAND]
    for demand in demands:
        try:
            calculator_factory(demand).calculate()
        except ErrorExcept as e:
            logger.error(traceback.format_exc())
        except Exception as e:
            logger.error(traceback.format_exc())


@scheduler.task('cron',
                name=' soldto_mpn_pool 数据处理',
                id='demand_data_standard_resolve_job', minute="*/2")
@redis_lock(f'{RedisCachePrefix}:demand_data_standard_resolve_job', lock_timeout=60)
def demand_data_standard_resolve_job():
    fiscal_weeks = get_demand_fiscal_weeks(IDEAL_DEMAND)
    first_five_weeks = fiscal_weeks[:5]
    for current_week in first_five_weeks:
        # current_week: str = FiscalWeekContainer().get_current_week()
        state_proxy = StateProxy(current_week, IDEAL_DEMAND)
        if state_proxy.after_start():
            factory = ProcessFactory(current_week)
            for processor in processors:
                try:
                    factory.get(processor).process()
                except (ErrorExcept, Exception) as e:
                    logger.error(traceback.format_exc())
                    developer_subject = f"{os.environ.get('ENV')} Demand - processor {processor} 预处理 soldto_mpn_pool 数据."
                    async_rate_limited_send_email(EMAIL_BUCKET_KEY_PREFIX + 'demand-dt-calculate-' + current_week, 2,
                                                  1800,
                                                  developer_subject,
                                                  f"week {current_week} Demand {processor} soldto_mpn_pool 数据失败,e:{traceback.format_exc()}")


@scheduler.task('cron',
                name='Y-Setting, 每周一下午5点如果未submit Y value/每周二上午10点, 需要自动进行submit',
                id='submit_y_value_job', minute="*/1")
@redis_lock(f'{RedisCachePrefix}:submit_y_value_job', lock_timeout=10)
def submit_y_value_job():
    week = FiscalWeekContainer().get_current_week()
    try:
        auto_publish_y(week)
    except (ErrorExcept, Exception) as e:
        developer_subject = f"{os.environ.get('ENV')} Y-Setting - 每周一下午5点如果未submit Y value/每周二上午10点, 未submit x value and fcst, 需要自动进行submit 失败."
        async_send_email(developer_subject, f"week:{week} 失败, error:{e}")


@scheduler.task('cron', name='使用dn数据生成rtm sales forecast兜底数据', id='auto_generate_rtm_sales_forecast_job',
                minute="*/1")
@redis_lock(f'{RedisCachePrefix}:auto_generate_rtm_sales_forecast_job', lock_timeout=10)
def auto_generate_rtm_sales_forecast_job():
    fiscal_weeks = get_demand_fiscal_weeks(IDEAL_DEMAND)
    weeks = fiscal_weeks[:5]
    for week in weeks:
        # week = FiscalWeekContainer().get_current_week()
        try:
            # 按照时间窗 SALES_FORECAST_DEADLINE，将上传了数据但是没有publish的publish掉
            auto_publish_sales_forecast(week)

            # 取消原超时数据未上传的兜底逻辑。https://quip-apple.com/IeTqAvBtsvgE#temp:C:SAS9e2cb8dc8e864a70bae9115c8
            # # 时间窗 RTM_SALES_FORECAST_AUTO_GENERATE，在dn计算完后，对于没有publish没有upload的rtm生成兜底数据
            # if CustomWeekDate(ModuleSwitchEnum.RTM_SALES_FORECAST_AUTO_GENERATE.value).is_before_ddl():
            #     continue
            # state_proxy = StateProxy(week, NORMALIZED_DEMAND)
            # if state_proxy.is_completed():
            #     auto_generate_rtm_sales_forecast(week)

        except (ErrorExcept, Exception) as e:
            logger.error(traceback.format_exc())
            developer_subject = f"{os.environ.get('ENV')} Demand - auto_generate_rtm_sales_forecast_job."
            async_rate_limited_send_email(EMAIL_BUCKET_KEY_PREFIX + 'auto_generate_rtm_sales_forecast_job-' + week, 1,
                                          1200,
                                          developer_subject,
                                          f"week {week} auto_generate_rtm_sales_forecast_job 失败,e:{traceback.format_exc()}")


@scheduler.task('cron',
                name='每天8点定时生成Usage数据',
                id='auto_generate_usage', hour=8, minute=0)
@redis_lock(f'{RedisCachePrefix}:auto_generate_usage', lock_timeout=10)
def auto_publish_woi_setting():
    try:
        dau = WriteEventTrackingInfo(DAU)
        dau.do_write()
        wau = WriteEventTrackingInfo(WAU)
        wau.do_write()
        mau = WriteEventTrackingInfo(MAU)
        mau.do_write()
    except (ErrorExcept, Exception) as e:
        developer_subject = f"{os.environ.get('ENV')} 定时生成Usage数据失败."
        async_rate_limited_send_email(EMAIL_BUCKET_KEY_PREFIX + 'auto_generate_usage-',
                                      1, 1200,
                                      developer_subject, f"定时生成Usage数据失败, error:{traceback.format_exc()}")



@scheduler.task('cron',
                name='WOI Setting auto publish',
                id='auto_publish_woi_setting', minute="*/1")
@redis_lock(f'{RedisCachePrefix}:publish_woi_setting', lock_timeout=10)
def auto_publish_woi_setting():
    fiscal_weeks = get_demand_fiscal_weeks(IDEAL_DEMAND)
    weeks = fiscal_weeks[:5]
    for week in weeks:
        # week = FiscalWeekContainer().get_current_week()
        try:
            if not CustomWeekDate(ModuleSwitchEnum.WOI_SETTING_AUTO_PUBLISH.value).is_before_ddl():
                auto_publish_final_demand_woi(week, WOITypes.FinalDemand.value)
        except (ErrorExcept, Exception) as e:
            developer_subject = f"{os.environ.get('ENV')} WOI-Setting - 自动保存发布woi失败."
            async_rate_limited_send_email(EMAIL_BUCKET_KEY_PREFIX + 'auto_publish_woi_setting-' + week,
                                          1, 1200,
                                          developer_subject, f"week:{week} 失败, error:{traceback.format_exc()}")


def auto_publish_y(week: str):
    if CustomWeekDate(ModuleSwitchEnum.Y_VALUE_DEAD_LINE.value).is_before_ddl():
        return

    ideal_deamnd_state = StateProxy(week, IDEAL_DEMAND)
    if not ideal_deamnd_state.is_waiting_to_setup():
        return

    try:
        IdealDemandYValueSetting.update_publish_status_by_fiscal_week(week)
    except ErrorExcept as e:
        logger.error(f"cpf publish y failed {str(e)} in week {week}, by system auto publish.")
    except Exception as e:
        logger.error(f"cpf publish y failed {str(e)} in week {week}, by system auto publish.")

    ideal_deamnd_state.do_cpf_publish_y()


def auto_publish_sales_forecast(week: str):
    if CustomWeekDate(ModuleSwitchEnum.SALES_FORECAST_DEADLINE.value).is_before_ddl():
        return

    ideal_demand_state = StateProxy(week, IDEAL_DEMAND)
    if not ideal_demand_state.is_waiting_for_rtm_setup():
        return

    for rtm in RTMS:
        # 先判断rtm状态 再判断数据库有没有上传
        if ideal_demand_state.get_rtm_state(rtm) == 1:
            # 已经发布了不再发布
            continue
        # 2025-04-28 不再校验是否上传数据，因为数据可能都包含在ML中，可以不上传数据也可以走流程
        # rtm_upload_record = FastLiteRTMSalesForecastUpload.query_by_rtm_fiscal_week(rtm=rtm, fiscal_week=week)
        # if rtm_upload_record is None:
        #     # 没有数据 不更新状态
        #     continue
        ideal_demand_state.do_rtm_publish(rtm)


@scheduler.task('cron',
                name='每周一9:00, pos mpn rewrite',
                id='po_mpn_rewrite_id', day_of_week='mon', hour=9, minute=0)
@redis_lock(f'{RedisCachePrefix}:po_mpn_rewrite_id')
def po_mpn_rewrite():
    '''
    每周一9点, pos mpn 搬数
    '''
    # dev 环境不进行搬数
    if os.environ.get('ENV') == 'dev':
        logger.info("po_mpn_rewrite: not ready rewrite for dev env!!")
        return
    developer_subject = f"{os.environ.get('ENV')} {PROJECT_NAME} - 周一9点pos mpn rewrite"
    try:
        # 当前日期对应财周
        current_dt = datetime.now().strftime('%Y-%m-%d')
        fiscal_obj = FiscalYearWeek.get_fis_by_date(date=current_dt)
        if fiscal_obj is None:
            logger.error(f"po_mpn_rewrite: can't find fiscal day, current_dt: {current_dt}")
            return
        fiscal_week = fiscal_obj.fiscal_qtr_week_name
        rpn = RewritePosMpnDatasource(fiscal_week)
        rpn.rewrite()
        async_send_email(developer_subject, f'{fiscal_week} pos mpn rewrite successfully!')
    except Exception as e:
        async_send_email(developer_subject, f"pos mpn rewrite failed, {e}")


# 20250307 从原本的周二上午9点提醒改为周一下午5点半提醒各个RTM还未上传Sales Forecast数据，邮件模版内容直接在数据库进行修改
# 20250411 取消原上传倒计时对于的邮件提醒逻辑，本期暂不发送。 https://quip-apple.com/IeTqAvBtsvgE#temp:C:SAS03bae16a30294662868c84250
# @scheduler.task('cron', name='monitor_rtm_upload_forecast',
#                 id='monitor_rtm_upload_forecast', day_of_week='mon', hour=17, minute=30)
# @redis_lock(f'{RedisCachePrefix}:monitor_rtm_upload_forecast', lock_timeout=10)
# def monitor_rtm_upload_forecast():
#     try:
#         # 当前日期对应财周
#         current_dt = datetime.now().strftime('%Y-%m-%d')
#         fiscal_obj = FiscalYearWeek.get_fis_by_date(date=current_dt)
#         if fiscal_obj is None:
#             logger.error(f"monitor_rtm_upload_forecast: can't find fiscal day, current_dt: {current_dt}")
#             return
#         fiscal_week = fiscal_obj.fiscal_qtr_week_name
        
#         do_moniter_rtms_upload_forecast(fiscal_week)
#     except (ErrorExcept, Exception) as e:
#         developer_subject = f"{os.environ.get('ENV')} 监控DI RTM上传Sales Forecast失败！！！."
#         async_send_email(developer_subject, f"monitor_rtm_upload_forecast failed, {e}")


#baozun 图片处理定时任务
@scheduler.task('cron', name='baozun_image_scan_task',
                id='baozun_image_scan_task', minute="*/2")
@redis_lock(f'{RedisCachePrefix}:baozun_image_scan_task', lock_timeout=30)
def baozun_image_scan_task():
    try:
        HitlImg.init_access_token()
        scan_image_data(1000, 0, "BZ")
    except (ErrorExcept, Exception) as e:
        logger.error({"baozun image scan error": str(e)})


# HITL 拉取 GBI 图片保存到 EFS
@scheduler.task('cron', name='handle_gbi_image_task', id='handle_gbi_image_task_id', minute="*/1", max_instances=1)
@redis_lock(f'{RedisCachePrefix}:handle_gbi_image_task_id', lock_timeout=10)
def handle_gbi_image_task():
    try:
        handle_gbi_image_data()
    except (ErrorExcept, Exception) as e:
        logger.error({"handle gbi image error": str(e)})


# 监控 HITL 到指定时间后还未搬完图
@scheduler.task('cron', name='monitor_hitl_not_sync_yet', id='monitor_hitl_not_sync_yet_id', hour=10, minute=5)
@redis_lock(f'{RedisCachePrefix}:monitor_hitl_not_sync_yet_id', lock_timeout=10)
def monitor_hitl_not_sync_yet():
    try:
        notify_not_sync_yet()
    except (ErrorExcept, Exception) as e:
        logger.error({"monitor_hitl_not_sync_yet error": str(e)})


# 从databend获取数据到mysql
@scheduler.task('cron', name='hitl_sync_data_from_databend_to_mysql', id='hitl_sync_data_from_databend_to_mysql_id', minute="*/1", max_instances=1)
@redis_lock_release(f'{RedisCachePrefix}:hitl_sync_data_from_databend_to_mysql_id', lock_timeout=60*10)
def hitl_sync_data_from_databend_to_mysql():
    try:
        sync_data()
    except (ErrorExcept, Exception) as e:
        developer_subject = f"{os.environ.get('ENV')} hitl_sync_data_from_databend_to_mysql失败！！！."
        if not env_dev():
            send_alert(developer_subject, 'https://github.pie.apple.com/Apple-GC-Sales-Dev/'
                                          'FAST-Lite-Server/util/scheduler_config.py', 'hm')
        logger.error(e)


@redis_lock(f'{RedisCachePrefix}:create_task_schedule_id')
def create_task_schedule():
    # 获取当前时间
    current_time = datetime.now()
    # 生成接下来n天的日期
    DAY_RANGE = 3
    target_dates = [(current_time + timedelta(days=i)).strftime('%Y-%m-%d') for i in range(DAY_RANGE)]

    new_task_list = [
        # 正式发送邮件
        {"name": f"formal_antifraud_so_ub_email_with_boss", "type": TASK_TYPE_SEND_ANTI_FRAUD_SO_UB_EMAIL,
         "partition": 0, "run_time": "09:00:00",
         "params": '{"email_cmd":"formal_antifraud_so_ub_email_with_boss","rtm_version":"All"}',
         "desc": "给大老板正式发送anti-fraud so/ub 的数据，不要随便改动！！！"},
        {"name": f"formal_antifraud_so_ub_email_with_boss_mono", "type": TASK_TYPE_SEND_ANTI_FRAUD_SO_UB_EMAIL,
         "partition": 2, "run_time": "09:00:20",
         "params": '{"email_cmd":"formal_antifraud_so_ub_email_with_boss_mono_brand","rtm_version":"Mono Brand"}',
         "desc": "给Mono正式发送anti-fraud so/ub 的数据，不要随便改动！！！"},
        {"name": f"formal_antifraud_so_ub_email_with_boss_multi", "type": TASK_TYPE_SEND_ANTI_FRAUD_SO_UB_EMAIL,
         "partition": 4, "run_time": "09:00:40",
         "params": '{"email_cmd":"formal_antifraud_so_ub_email_with_boss_multi_brand","rtm_version":"Multi Brand"}',
         "desc": "给Multi正式发送anti-fraud so/ub 的数据，不要随便改动！！！"},
        {"name": f"formal_antifraud_so_ub_email_with_boss_carrier", "type": TASK_TYPE_SEND_ANTI_FRAUD_SO_UB_EMAIL,
         "partition": 6, "run_time": "09:01:00",
         "params": '{"email_cmd":"formal_antifraud_so_ub_email_with_boss_carrier","rtm_version":"Carrier"}',
         "desc": "给Carrier正式发送anti-fraud so/ub 的数据，不要随便改动！！！"},
        {"name": f"formal_antifraud_so_ub_email_with_boss_edu", "type": TASK_TYPE_SEND_ANTI_FRAUD_SO_UB_EMAIL,
         "partition": 10, "run_time": "09:01:20",
         "params": '{"email_cmd":"formal_antifraud_so_ub_email_with_boss_edu","rtm_version":"Education"}',
         "desc": "给EDU正式发送anti-fraud so/ub 的数据，不要随便改动！！！"},
        {"name": f"formal_antifraud_so_ub_email_with_boss_channel_online", "type": TASK_TYPE_SEND_ANTI_FRAUD_SO_UB_EMAIL,
         "partition": 12, "run_time": "09:01:40",
         "params": '{"email_cmd":"formal_antifraud_so_ub_email_with_boss_channel_online","rtm_version":"C.Online"}',
         "desc": "给Channel Online正式发送anti-fraud so/ub 的数据，不要随便改动！！！"},

        # 预发送邮件
        {"name": f"formal_antifraud_so_ub_email_with_boss_pre", "type": TASK_TYPE_SEND_ANTI_FRAUD_SO_UB_EMAIL,
         "partition": 1, "run_time": "08:40:00",
         "params": '{"email_cmd":"formal_antifraud_so_ub_email_with_boss_pre","rtm_version":"All"}',
         "desc": "给自己和DMP团队提前发送anti-fraud so/ub 的数据"},
        {"name": f"formal_antifraud_so_ub_email_with_boss_mono_pre", "type": TASK_TYPE_SEND_ANTI_FRAUD_SO_UB_EMAIL,
         "partition": 3, "run_time": "08:40:20",
         "params": '{"email_cmd":"formal_antifraud_so_ub_email_with_boss_mono_brand_pre","rtm_version":"Mono Brand"}',
         "desc": "给自己和DMP团队提前发送Mono anti-fraud so/ub 的数据"},
        {"name": f"formal_antifraud_so_ub_email_with_boss_multi_pre", "type": TASK_TYPE_SEND_ANTI_FRAUD_SO_UB_EMAIL,
         "partition": 5, "run_time": "08:40:40",
         "params": '{"email_cmd":"formal_antifraud_so_ub_email_with_boss_multi_brand_pre","rtm_version":"Multi Brand"}',
         "desc": "给自己和DMP团队提前发送Multi anti-fraud so/ub 的数据"},
        {"name": f"formal_antifraud_so_ub_email_with_boss_carrier_pre", "type": TASK_TYPE_SEND_ANTI_FRAUD_SO_UB_EMAIL,
         "partition": 7, "run_time": "08:41:00",
         "params": '{"email_cmd":"formal_antifraud_so_ub_email_with_boss_carrier_pre","rtm_version":"Carrier"}',
         "desc": "给自己和DMP团队提前发送Carrier anti-fraud so/ub 的数据"},
        {"name": f"formal_antifraud_so_ub_email_with_boss_edu_pre", "type": TASK_TYPE_SEND_ANTI_FRAUD_SO_UB_EMAIL,
         "partition": 11, "run_time": "08:41:20",
         "params": '{"email_cmd":"formal_antifraud_so_ub_email_with_boss_edu_pre","rtm_version":"Education"}',
         "desc": "给自己和DMP团队提前发送EDU anti-fraud so/ub 的数据"},
        {"name": f"formal_antifraud_so_ub_email_with_boss_channel_online_pre", "type": TASK_TYPE_SEND_ANTI_FRAUD_SO_UB_EMAIL,
         "partition": 13, "run_time": "08:41:40",
         "params": '{"email_cmd":"formal_antifraud_so_ub_email_with_boss_channel_online_pre","rtm_version":"C.Online"}',
         "desc": "给自己和DMP团队提前发送Channel Online anti-fraud so/ub 的数据"},

        # 内部邮件
        {"name": f"formal_antifraud_so_ub_email_with_boss_mono_pre_self", "type": TASK_TYPE_SEND_ANTI_FRAUD_SO_UB_EMAIL,
         "partition": 8, "run_time": "08:40:05",
         "params": '{"email_cmd":"formal_antifraud_so_ub_email_with_boss_mono_brand_pre_self","rtm_version":"Mono Brand"}',
         "desc": "给自己提前发送Mono anti-fraud so/ub 的数据，可以放心触发"},
        {"name": f"formal_antifraud_so_ub_email_with_boss_pre_self", "type": TASK_TYPE_SEND_ANTI_FRAUD_SO_UB_EMAIL,
         "partition": 9, "run_time": "08:40:10",
         "params": '{"email_cmd":"formal_antifraud_so_ub_email_with_boss_pre_self","rtm_version":"All"}',
         "desc": "给自己提前发送anti-fraud so/ub 的数据，可以放心触发"},
    ]
    check_and_create_tasks(target_dates, new_task_list)


@redis_lock(f'{RedisCachePrefix}:create_eoh_aging_task_schedule_id')
def create_eoh_aging_task_schedule():
    # 获取当前时间
    current_time = datetime.now()
    # 生成接下来n天的日期
    DAY_RANGE = 1
    target_dates = [(current_time + timedelta(days=i)).strftime('%Y-%m-%d') for i in range(DAY_RANGE)]

    new_task_list = [
        # 正式发送邮件
        {"name": f"formal_antifraud_eoh_aging_ub_email_with_boss", "type": TASK_TYPE_SEND_EOH_AGING_UB_WITHOUT_DS_EMAIL,
         "partition": 0, "run_time": "09:10:00",
         "params": '{"email_cmd":"formal_antifraud_eoh_aging_ub_email_with_boss","rtm_version":"All"}',
         "desc": "给大老板正式发送【All】anti-fraud eoh aging 的数据，不要随便改动！！！"},
        {"name": f"formal_antifraud_eoh_aging_ub_email_with_multi_brand",
         "type": TASK_TYPE_SEND_EOH_AGING_UB_WITHOUT_DS_EMAIL, "partition": 1, "run_time": "09:10:20",
         "params": '{"email_cmd":"formal_antifraud_eoh_aging_ub_email_with_multi_brand","rtm_version":"' + RTM_MULTI + '"}',
         "desc": "给老板正式发送【Multi】 anti-fraud eoh aging 的数据，不要随便改动！！！"},
        {"name": f"formal_antifraud_eoh_aging_ub_email_with_mono_brand",
         "type": TASK_TYPE_SEND_EOH_AGING_UB_WITHOUT_DS_EMAIL, "partition": 2, "run_time": "09:10:40",
         "params": '{"email_cmd":"formal_antifraud_eoh_aging_ub_email_with_mono_brand","rtm_version":"' + RTM_Mono + '"}',
         "desc": "给老板正式发送【Mono】 anti-fraud eoh aging 的数据，不要随便改动！！！"},
        {"name": f"formal_antifraud_eoh_aging_ub_email_with_carrier",
         "type": TASK_TYPE_SEND_EOH_AGING_UB_WITHOUT_DS_EMAIL, "partition": 3, "run_time": "09:11:00",
         "params": '{"email_cmd":"formal_antifraud_eoh_aging_ub_email_with_carrier","rtm_version":"' + RTM_CARRIER + '"}',
         "desc": "给老板正式发送【Carrier】 anti-fraud eoh aging 的数据，不要随便改动！！！"},
        {"name": f"formal_antifraud_eoh_aging_ub_email_with_edu",
         "type": TASK_TYPE_SEND_EOH_AGING_UB_WITHOUT_DS_EMAIL, "partition": 11, "run_time": "09:11:20",
         "params": '{"email_cmd":"formal_antifraud_eoh_aging_ub_email_with_edu","rtm_version":"' + RTM_EDU + '"}',
         "desc": "给老板正式发送【EDU】 anti-fraud eoh aging 的数据，不要随便改动！！！"},

        # 预发送邮件
        {"name": f"formal_antifraud_eoh_aging_ub_email_with_boss_pre",
         "type": TASK_TYPE_SEND_EOH_AGING_UB_WITHOUT_DS_EMAIL, "partition": 4, "run_time": "09:05:10",
         "params": '{"email_cmd":"formal_antifraud_eoh_aging_ub_email_with_boss_pre","rtm_version":"All"}',
         "desc": "给自己和DMP团队提前发送【All】anti-fraud eoh aging 的数据"},
        {"name": f"formal_antifraud_eoh_aging_ub_email_with_multi_brand_pre",
         "type": TASK_TYPE_SEND_EOH_AGING_UB_WITHOUT_DS_EMAIL, "partition": 5, "run_time": "09:05:20",
         "params": '{"email_cmd":"formal_antifraud_eoh_aging_ub_email_with_multi_brand_pre","rtm_version":"' + RTM_MULTI + '"}',
         "desc": "给自己和DMP团队提前发送【Multi】anti-fraud eoh aging 的数据"},
        {"name": f"formal_antifraud_eoh_aging_ub_email_with_carrier_pre",
         "type": TASK_TYPE_SEND_EOH_AGING_UB_WITHOUT_DS_EMAIL, "partition": 6, "run_time": "09:05:40",
         "params": '{"email_cmd":"formal_antifraud_eoh_aging_ub_email_with_carrier_pre","rtm_version":"' + RTM_CARRIER + '"}',
         "desc": "给自己和DMP团队发送【Carrier】 anti-fraud eoh aging 的数据"},
        {"name": f"formal_antifraud_eoh_aging_ub_email_with_mono_brand_pre",
         "type": TASK_TYPE_SEND_EOH_AGING_UB_WITHOUT_DS_EMAIL, "partition": 7, "run_time": "09:06:00",
         "params": '{"email_cmd":"formal_antifraud_eoh_aging_ub_email_with_mono_brand_pre","rtm_version":"' + RTM_Mono + '"}',
         "desc": "给自己和DMP团队提前发送【Mono】anti-fraud eoh aging 的数据"},
        {"name": f"formal_antifraud_eoh_aging_ub_email_with_edu_pre",
         "type": TASK_TYPE_SEND_EOH_AGING_UB_WITHOUT_DS_EMAIL, "partition": 10, "run_time": "09:06:00",
         "params": '{"email_cmd":"formal_antifraud_eoh_aging_ub_email_with_edu_pre","rtm_version":"' + RTM_EDU + '"}',
         "desc": "给自己和DMP团队提前发送【EDU】anti-fraud eoh aging 的数据"},

        # 内部邮件
        {"name": f"formal_antifraud_eoh_aging_ub_email_with_mono_brand_pre_self",
         "type": TASK_TYPE_SEND_EOH_AGING_UB_WITHOUT_DS_EMAIL, "partition": 8, "run_time": "09:05:00",
         "params": '{"email_cmd":"formal_antifraud_eoh_aging_ub_email_with_mono_brand_pre_self","rtm_version":"' + RTM_Mono + '"}',
         "desc": "给自己发送【Mono】 anti-fraud eoh aging 的数据，可以放心触发"},
        {"name": f"formal_antifraud_eoh_aging_ub_email_with_boss_pre_self",
         "type": TASK_TYPE_SEND_EOH_AGING_UB_WITHOUT_DS_EMAIL, "partition": 9, "run_time": "09:05:05",
         "params": '{"email_cmd":"formal_antifraud_eoh_aging_ub_email_with_boss_pre_self","rtm_version":"All"}',
         "desc": "给自己发送【All】anti-fraud eoh aging 的数据，可以放心触发"},
    ]
    check_and_create_tasks(target_dates, new_task_list)


@redis_lock(f'{RedisCachePrefix}:create_pos_suspension_task_schedule_id')
def create_pos_suspension_task_schedule():
    # 获取当前时间
    current_time = datetime.now()

    # 0 表示周一，6 表示周日
    if current_time.weekday() != 0:
        return
    # 生成接下来n天的日期
    DAY_RANGE = 1
    target_dates = [(current_time + timedelta(days=i)).strftime('%Y-%m-%d') for i in range(DAY_RANGE)]

    new_task_list = [
        # 正式发送邮件
        {"name": f"formal_antifraud_pos_suspension_email_with_boss", "type": TASK_TYPE_SEND_POS_SUSPENSION_EMAIL,
         "partition": 0, "run_time": "10:00:00",
         "params": '{"email_cmd":"formal_antifraud_pos_suspension_email_with_boss","rtm_version":"All"}',
         "desc": "给大老板正式发送pos suspension的数据，不要随便改动！！！"},
        {"name": f"formal_antifraud_pos_suspension_email_with_boss_mono", "type": TASK_TYPE_SEND_POS_SUSPENSION_EMAIL,
         "partition": 2, "run_time": "10:00:20",
         "params": '{"email_cmd":"formal_antifraud_pos_suspension_email_with_boss_mono_brand","rtm_version":"Mono Brand"}',
         "desc": "给Mono正式发送pos suspension 的数据，不要随便改动！！！"},
        {"name": f"formal_antifraud_pos_suspension_email_with_boss_multi", "type": TASK_TYPE_SEND_POS_SUSPENSION_EMAIL,
         "partition": 4, "run_time": "10:00:40",
         "params": '{"email_cmd":"formal_antifraud_pos_suspension_email_with_boss_multi_brand","rtm_version":"Multi Brand"}',
         "desc": "给Multi正式发送pos suspension 的数据，不要随便改动！！！"},
        {"name": f"formal_antifraud_pos_suspension_email_with_boss_carrier",
         "type": TASK_TYPE_SEND_POS_SUSPENSION_EMAIL,
         "partition": 6, "run_time": "10:01:00",
         "params": '{"email_cmd":"formal_antifraud_pos_suspension_email_with_boss_carrier","rtm_version":"Carrier"}',
         "desc": "给Carrier正式发送pos suspension 的数据，不要随便改动！！！"},
        {"name": f"formal_antifraud_pos_suspension_email_with_boss_edu", "type": TASK_TYPE_SEND_POS_SUSPENSION_EMAIL,
         "partition": 10, "run_time": "10:01:20",
         "params": '{"email_cmd":"formal_antifraud_pos_suspension_email_with_boss_edu","rtm_version":"Education"}',
         "desc": "给EDU正式发送pos suspension 的数据，不要随便改动！！！"},


        # 预发送邮件
        {"name": f"formal_antifraud_pos_suspension_email_with_boss_pre", "type": TASK_TYPE_SEND_POS_SUSPENSION_EMAIL,
         "partition": 1, "run_time": "09:30:00",
         "params": '{"email_cmd":"formal_antifraud_pos_suspension_email_with_boss_pre","rtm_version":"All"}',
         "desc": "给自己和DMP团队提前发送pos suspension 的数据"},
        {"name": f"formal_antifraud_pos_suspension_email_with_boss_mono_pre", "type": TASK_TYPE_SEND_POS_SUSPENSION_EMAIL,
         "partition": 3, "run_time": "09:30:20",
         "params": '{"email_cmd":"formal_antifraud_pos_suspension_email_with_boss_mono_brand_pre","rtm_version":"Mono Brand"}',
         "desc": "给自己和DMP团队提前发送Mono pos suspension 的数据"},
        {"name": f"formal_antifraud_pos_suspension_email_with_boss_multi_pre", "type": TASK_TYPE_SEND_POS_SUSPENSION_EMAIL,
         "partition": 5, "run_time": "09:30:40",
         "params": '{"email_cmd":"formal_antifraud_pos_suspension_email_with_boss_multi_brand_pre","rtm_version":"Multi Brand"}',
         "desc": "给自己和DMP团队提前发送Multi pos suspension 的数据"},
        {"name": f"formal_antifraud_pos_suspension_email_with_boss_carrier_pre", "type": TASK_TYPE_SEND_POS_SUSPENSION_EMAIL,
         "partition": 7, "run_time": "09:31:00",
         "params": '{"email_cmd":"formal_antifraud_pos_suspension_email_with_boss_carrier_pre","rtm_version":"Carrier"}',
         "desc": "给自己和DMP团队提前发送Carrier pos suspension 的数据"},
        {"name": f"formal_antifraud_pos_suspension_email_with_boss_edu_pre", "type": TASK_TYPE_SEND_POS_SUSPENSION_EMAIL,
         "partition": 11, "run_time": "09:31:20",
         "params": '{"email_cmd":"formal_antifraud_pos_suspension_email_with_boss_edu_pre","rtm_version":"Education"}',
         "desc": "给自己和DMP团队提前发送EDU pos suspension 的数据"},


        # 内部邮件
        {"name": f"formal_antifraud_pos_suspension_email_with_boss_pre_self", "type": TASK_TYPE_SEND_POS_SUSPENSION_EMAIL,
         "partition": 9, "run_time": "09:29:00",
         "params": '{"email_cmd":"formal_antifraud_pos_suspension_email_with_boss_pre_self","rtm_version":"All"}',
         "desc": "给自己提前发送pos suspension 的数据，可以放心触发"},
        {"name": f"formal_antifraud_pos_suspension_email_with_boss_mono_pre_self", "type": TASK_TYPE_SEND_POS_SUSPENSION_EMAIL,
         "partition": 8, "run_time": "09:29:20",
         "params": '{"email_cmd":"formal_antifraud_pos_suspension_email_with_boss_mono_brand_pre_self","rtm_version":"Mono Brand"}',
         "desc": "给自己提前发送Mono pos suspension 的数据，可以放心触发"},

    ]
    check_and_create_tasks(target_dates, new_task_list)


def check_and_create_tasks(target_dates: list[str], task_list: list[dict]):
    if (isinstance(target_dates, list) and len(target_dates) == 0) or (isinstance(task_list, list) and len(task_list) == 0):
        return
    for task in task_list:
        # 查询数据库中有没有该类型task的7天的数据
        task_periods = TaskRepository.get_task_periods(type=task["type"], partition=task["partition"], period_list=target_dates)
        need_create_list = list(set(target_dates).difference(task_periods))
        task_need_create_list = []
        for task_period in need_create_list:
            task_name = f"{task_period}-" + task["name"]
            task_status = TaskStatus.Ready if not env_dev() else TaskStatus.Done  # dev 环境默认状态都是done
            task_need_create_list.append(
                Task(
                    name=task_name,
                    _type=task["type"],
                    desc=task["desc"],
                    run_at=datetime.strptime(f'{task_period} {task["run_time"]}', '%Y-%m-%d %H:%M:%S'),
                    partition=task['partition'],
                    period=task_period,
                    params=task["params"],
                    status=task_status
                )
            )
        if len(task_need_create_list):
            TaskRepository.bulk_create(task_need_create_list)
            logger.info(f"新建任务-{task_need_create_list}")


# 20241204统一把snapshot ts字段替换成sync_time字段
@redis_lock(f'{RedisCachePrefix}:create_esr_report_task_schedule_id')
def create_esr_report_task_schedule():
    data_type = AutomaticDataType.ESR.value
    db_name = Automatic_DB_NAME

    # 1、ESR主品
    esr_main_table_name = "gc_ro_ds_esr_data"
    # 1.1、邮件通知（版本成功 or 版本延迟）
    generate_esr_notify_task(data_type, db_name, esr_main_table_name)

    # 1.2、预生成esr文件
    reports_todo = get_reports_todo(data_type, db_name, esr_main_table_name, AUTOMATIC_ESR_MAIN_PRODUCTS_GEN_FILE)
    for report in reports_todo:
        create_esr_task(ESR_CHANNEL_PERMISSIONS, report, KeySubKeyRelation.ESR_MAIN_PRODUCTS.value)

    # 2、ESR配件
    esr_acc_table_name = "gc_ro_ds_esr_accy_data"
    # 2.1、邮件通知（版本成功 or 版本延迟）
    generate_esr_notify_task(data_type, db_name, esr_acc_table_name)
    # 2.2、预生成esr文件
    reports_todo = get_reports_todo(data_type, db_name, esr_acc_table_name, AUTOMATIC_ESR_ACCESSORIES_GEN_FILE)
    for report in reports_todo:
        create_esr_task(ESR_CHANNEL_PERMISSIONS, report, KeySubKeyRelation.ESR_ACCESSORIES.value)

    # 只有CTO POD 需要提前生成文件
    cto_pod_table_name = "gc_dmp_pod"
    data_type = AutomaticDataType.CTO.value
    # 监控CTO的数据
    monitor_cto(data_type, db_name, cto_pod_table_name)

    created_cto_tasks, cto_records = get_created_cto_tasks(
        data_type, db_name, cto_pod_table_name, AUTOMATIC_CTO_POD_GEN_FILE
    )
    for record in cto_records:
        if str(record.sync_time) not in created_cto_tasks:
            create_cto_generate_files_task(CTO_CHANNEL_PERMISSIONS, record, KeySubKeyRelation.CTO_POD.value)


# generate_esr_notify_task 邮件任务：
# morning/afternoon版本成功，推送通知邮件
# morning/afternoon版本延迟，推送延迟邮件
# 对于周日多一个cob版本的生成成功or延迟通知
def generate_esr_notify_task(data_type, db_name, table_name):
    now = datetime.now()
    current_time = now.strftime('%H:%M')

    #   1. 每天9:30以后，8:00以后第一个版本的数据刷新尚未完成时（补充：该版本的监控任务包括ESR - Main Products / ESR - Accessories两个版本）
    #   2. 每天16:30以后，15:00以后第一个版本的数据刷新尚未完成时（补充：该版本的监控任务包括ESR - Main Products / ESR - Accessories两个版本）
    #   3. 每周日8:00以后，凌晨4:00后的第一个版本数据刷新尚未完成时（补充：该版本的监控任务有且仅有ESR - Main Products版本）
    notify_time_config = [
        {
            "condition": lambda: table_name == EsrTableName.GC_RO_DS_ESR_DATA.value and now.weekday() == 6,
            "check_begin_time": ESR_COB_START_TIME,
            "check_end_time": ESR_COB_END_TIME,
            "generate_notify_task_begin_time": time(4, 0, 0),
            "generate_notify_task_end_time": time(8, 0, 0),
            "notice_type": DataRefreshVersion.COB.value
        },
        {
            "check_begin_time": ESR_MORNING_START_TIME,
            "check_end_time": ESR_MORNING_END_TIME,
            "generate_notify_task_begin_time": time(8, 0, 0),
            "generate_notify_task_end_time": time(9, 30, 0),
            "notice_type": DataRefreshVersion.MORNING.value
        },
        {
            "check_begin_time": ESR_AFTERNOON_START_TIME,
            "check_end_time": ESR_AFTERNOON_END_TIME,
            "generate_notify_task_begin_time": time(15, 0, 0),
            "generate_notify_task_end_time": time(16, 30, 0),
            "notice_type": DataRefreshVersion.AFTERNOON.value
        }
    ]

    # 遍历时间段配置
    for time_range in notify_time_config:
        if time_range.get("condition", lambda: True)() and time_range["check_begin_time"] <= current_time <= time_range["check_end_time"]:
            begin_time = datetime.combine(now.date(), time_range["generate_notify_task_begin_time"])
            end_time = datetime.combine(now.date(), time_range["generate_notify_task_end_time"])
            notice_type = time_range["notice_type"]
            generate_esr_task(data_type, db_name, table_name, begin_time, end_time, notice_type)


@redis_lock(f'{RedisCachePrefix}:create_po_ga_delinquent_task_schedule_id')
def create_po_ga_delinquent_task_schedule():
    # 获取当前时间
    current_time = datetime.now()

    # 0 表示周一，6 表示周日， 每周四生成对应的任务
    THURSDAY = 3
    if current_time.weekday() != THURSDAY:
        return
    # 生成接下来n天的日期
    DAY_RANGE = 1
    target_dates = [(current_time + timedelta(days=i)).strftime('%Y-%m-%d') for i in range(DAY_RANGE)]
    
    check_and_create_tasks(target_dates, PO_GAP_DELINQUENT_TASK_LIST)


# 统一worker调度
scheduler.add_job(id='task_scheduler', func=schedule_task, trigger="interval", seconds=10)

# 统一worker调度 - 执行慢的任务调度
scheduler.add_job(id='slow_task_scheduler', func=schedule_task_slowly, trigger="interval", seconds=15)

scheduler.add_job(id='fast_lite_create_new_tasks', func=create_task_schedule, trigger="interval", minutes=10)

# 20250514 取消eoh邮件的发送
# scheduler.add_job(id='create_eoh_aging_task_schedule', func=create_eoh_aging_task_schedule, trigger="interval",
#                   minutes=1)

# 20250511取消pos suspension邮件的发送
# scheduler.add_job(id='create_pos_suspension_task_schedule', func=create_pos_suspension_task_schedule,
#                   trigger="interval", minutes=10)

# 【Fast Datasource】30秒检测一次, 预生成esr文件，用于页面下载
scheduler.add_job(id='create_esr_report_task_schedule', func=create_esr_report_task_schedule,
                  trigger="interval", seconds=30)

# 【Po Gap / Po Delinquent】生成task
scheduler.add_job(id='create_po_ga_delinquent_task_schedule', func=create_po_ga_delinquent_task_schedule, 
                  trigger="interval", minutes=1)
