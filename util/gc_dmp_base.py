from util.conf import *
from urllib.parse import quote_plus as urlquote


gc_dmp_config = conf['gc_dmp_db']

if os.environ.get('DB_ENV') == 'aws':
    db_host = gc_dmp_config['aws_host']
    db_sec = get_secret_from_aws(gc_dmp_config['secret_name'], gc_dmp_config['secret_region'])
    db_port = gc_dmp_config['aws_port']
else :
    db_host = gc_dmp_config['apple_host']
    db_sec = get_secret_from_apple(gc_dmp_config['secret_name'], gc_dmp_config['secret_region'], secret_req_host, secret_req_path)
    db_port = gc_dmp_config['apple_port']
    
db_user = db_sec['username']
db_pass = db_sec['password']

db_name = gc_dmp_config['default_db']

db_db = "mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8".format(db_user, urlquote(db_pass), db_host, db_port, db_name)

GcDmpEngine = create_engine(db_db, pool_pre_ping=True, pool_size=20, max_overflow=5, pool_recycle=3600)

GcDmpBase = declarative_base()
GcDmpSession = sessionmaker(bind=GcDmpEngine)
