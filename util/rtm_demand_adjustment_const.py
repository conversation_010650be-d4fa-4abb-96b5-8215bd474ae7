AdjustmentTemplateHeaderDict = {
    "Sales Org": "sales_org",
    "RTML4": "rtml4",
    "LOB / FPH L1": "lob",
    "Prod / FPH L3": "prod",
    "Project Code": "project_code",
    "Nand": "nand",
    "Color": "color",
    "MPN / Apple Part #": "mpn",
    "ODQ": "odq",
    "Customer Sold-to ID": "sold_to_id",
    "Customer Sold-to Name": "sold_to_name_en",
    "QTW Shipment Plan": "qtw_shipment_plan",
    "CW Shipment Plan (Discrete)": "cw_shipment_plan",
    "Sni (Cw-1)": "sni_cw_minus_1",
    "CW+1 Shipment Plan (Discrete)": "shipment_plan_cw1",
    "CW+2 Shipment Plan (Discrete)": "shipment_plan_cw2",
    "EOH": "eoh",
    "St/Ub Cw-1": "st_ub_qty_cw_minus1",
    "ST/UB 5 Wk Bwd Avg": "st_ub_5wk_bwd_avg",
    "Open Backlog over Published for CW+3 SP": "open_backlog_over_published_sp_cw3",
    "Priority": "priority",
    "Qty for CW+1": "sales_input_qty_cw1",
    "Reason for CW+1": "reason_cw1",
    "Qty for CW+2": "sales_input_qty_cw2",
    "Reason for CW+2": "reason_cw2",
    "Qty for CW+3": "sales_input_qty_cw3",
    "Reason for CW+3": "reason_cw3",
    "Qty for CW+4": "sales_input_qty_cw4",
    "Reason for CW+4": "reason_cw4",
    "Comments": "comments",
    "Delta for adjustment in CW+1": "delta_adjustment_cw1",
    "Reason for adjustment in CW+1": "reason_adjustment_cw1",
    "Delta for adjustment in CW+2": "delta_adjustment_cw2",
    "Reason for adjustment in CW+2": "reason_adjustment_cw2",
    "Delta for adjustment in CW+3": "delta_adjustment_cw3",
    "Reason for adjustment in CW+3": "reason_adjustment_cw3",
    "Delta for adjustment in CW+4": "delta_adjustment_cw4",
    "Reason for adjustment in CW+4": "reason_adjustment_cw4",
    "Comments for adjustment": "comments_adjustment"
}

AdjustmentTemplateFileRawHeader = list(AdjustmentTemplateHeaderDict.values())

AdjustmentTemplateFileHeader = list(AdjustmentTemplateHeaderDict.keys())

CompareDataHeader = AdjustmentTemplateFileHeader[:30]
CompareDataRawHeader = AdjustmentTemplateFileRawHeader[:30]

HRLRDict = {
    "Customer Sold-to ID": "sold_to_id",
    "MPN / Apple Part #": "mpn",
    "Priority": "priority",
    "HR_LR": "hr_lr"
}

HRLRMergeRawHeader = list(HRLRDict.values())
HRLRMergeHeader = list(HRLRDict.keys())


class ErrorMessage:
    InconsistentHeader = "The “Demand” data file uploaded must be in the same format as the original demand file."
    InconsistentData = "Rows and columns in the Template can not be added or deleted."
    InvalidRows = "The following rows are invalid: "
    UploadConflict = "Upload Failed. A new file has already been uploaded by CP&F team."
    UploadFailed = "Please contact DMP team for assistance."


class RTMDemandAdjustmentUploadStatus:
    NotStared = 0
    WaitingToUpload = 1
    Uploaded = 2
    Received = 3
    Completed = 4
    NeedToReupload = 5
    NotUpload = 6


class ThirdPhaseUploadFileCategory:
    Template = 0
    RTM = 1
    CPF = 2
