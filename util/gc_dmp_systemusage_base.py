from .conf import *
from .util import *


gc_dmp_systemusage_config = conf.get('gc_dmp_systemusage')
if os.environ.get('DB_ENV') == 'aws':
    gc_dmp_systemusage_host = gc_dmp_systemusage_config['aws_host']
    gc_dmp_systemusage_sec = get_secret_from_aws(gc_dmp_systemusage_config['secret_name'], gc_dmp_systemusage_config['secret_region'])
else :
    gc_dmp_systemusage_host = gc_dmp_systemusage_config['apple_host']
    gc_dmp_systemusage_sec = get_secret_from_apple(gc_dmp_systemusage_config['secret_name'], gc_dmp_systemusage_config['secret_region'], secret_req_host, secret_req_path)

gc_dmp_systemusage_user = gc_dmp_systemusage_sec['username']
gc_dmp_systemusage_pass = gc_dmp_systemusage_sec['password']

gc_dmp_systemusage_port = '3306'
gc_dmp_systemusage_name = gc_dmp_systemusage_config['default_db']

gc_dmp_systemusage_db = "mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8".format(gc_dmp_systemusage_user, urlquote(gc_dmp_systemusage_pass), gc_dmp_systemusage_host, gc_dmp_systemusage_port, gc_dmp_systemusage_name)

gc_dmp_systemusage_engine = create_engine(gc_dmp_systemusage_db, pool_pre_ping=True, pool_size=20, max_overflow=5, pool_recycle=3600)

UsageBase = declarative_base()
UsageSession = sessionmaker(bind=gc_dmp_systemusage_engine)

