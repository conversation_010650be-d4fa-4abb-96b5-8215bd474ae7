from task_kit.register_task import (
    TASK_TYPE_PO_DELINQUENT_EMAIL,
    TASK_TYPE_PO_DELINQUENT_RTM_EMAIL,
    TASK_TYPE_PO_GAP_EMAIL,
    TASK_TYPE_PO_GAP_RTM_EMAIL,
)


FORMAL_PO_GAP_TASKS = [
    {
        "name": f"formal_po_gap_email_for_gm",
        "type": TASK_TYPE_PO_GAP_EMAIL,
        "partition": 0,
        "run_time": "14:00:00",
        "params": '{"email_cmd":"po_gap_email_gm","greeting": "Hello <PERSON>,"}',
        "desc": "每周四给老板发送Po Gap邮件",
    },
    {
        "name": f"formal_po_gap_email_for_planning_team",
        "type": TASK_TYPE_PO_GAP_EMAIL,
        "partition": 1,
        "run_time": "14:00:00",
        "params": '{"email_cmd":"po_gap_email_planning_team","greeting": "Dear Planners,"}',
        "desc": "每周四给Planing Team发送Po Gap邮件",
    },
]

FORMAL_PO_GAP_RTM_TASKS =[
    {
        "name": f"Mono_formal_po_gap_rtm_email_for_gm",
        "type": TASK_TYPE_PO_GAP_RTM_EMAIL,
        "partition": 0,
        "run_time": "14:00:00",
        "params": '{"email_cmd":"Mono_po_gap_rtm_email_gm","rtm":"Mono","greeting": "Hello Steven,"}',
        "desc": "每周四给Mono GM发送Po Gap邮件",
    },
    {
        "name": f"Mono_formal_po_gap_rtm_email_for_planning_team",
        "type": TASK_TYPE_PO_GAP_RTM_EMAIL,
        "partition": 1,
        "run_time": "14:00:00",
        "params": '{"email_cmd":"Mono_po_gap_rtm_email_planning_team","rtm":"Mono","greeting": "Dear Planners,"}',
        "desc": "每周四给Mono Planing Team发送Po Gap邮件",
    },
    {
        "name": f"Multi_formal_po_gap_rtm_email_for_gm",
        "type": TASK_TYPE_PO_GAP_RTM_EMAIL,
        "partition": 2,
        "run_time": "14:00:00",
        "params": '{"email_cmd":"Multi_po_gap_rtm_email_gm","rtm":"Multi","greeting": "Hello Felix,"}',
        "desc": "每周四给Multi GM发送Po Gap邮件",
    },
    {
        "name": f"Multi_formal_po_gap_rtm_email_for_planning_team",
        "type": TASK_TYPE_PO_GAP_RTM_EMAIL,
        "partition": 3,
        "run_time": "14:00:00",
        "params": '{"email_cmd":"Multi_po_gap_rtm_email_planning_team","rtm":"Multi","greeting": "Dear Planners,"}',
        "desc": "每周四给Multi Planing Team发送Po Gap邮件",
    },
    {
        "name": f"Online_formal_po_gap_rtm_email_for_gm",
        "type": TASK_TYPE_PO_GAP_RTM_EMAIL,
        "partition": 4,
        "run_time": "14:00:00",
        "params": '{"email_cmd":"Online_po_gap_rtm_email_gm","rtm":"Online","greeting": "Hello Steven,"}',
        "desc": "每周四给Online GM发送Po Gap邮件",
    },
    {
        "name": f"Online_formal_po_gap_rtm_email_for_planning_team",
        "type": TASK_TYPE_PO_GAP_RTM_EMAIL,
        "partition": 5,
        "run_time": "14:00:00",
        "params": '{"email_cmd":"Online_po_gap_rtm_email_planning_team","rtm":"Online","greeting": "Dear Planners,"}',
        "desc": "每周四给Online Planing Team发送Po Gap邮件",
    },
    {
        "name": f"Carrier_formal_po_gap_rtm_email_for_gm",
        "type": TASK_TYPE_PO_GAP_RTM_EMAIL,
        "partition": 6,
        "run_time": "14:00:00",
        "params": '{"email_cmd":"Carrier_po_gap_rtm_email_gm","rtm":"Carrier","greeting": "Hello Tommy,"}',
        "desc": "每周四给Carrier GM发送Po Gap邮件",
    },
    {
        "name": f"Carrier_formal_po_gap_rtm_email_for_planning_team",
        "type": TASK_TYPE_PO_GAP_RTM_EMAIL,
        "partition": 7,
        "run_time": "14:00:00",
        "params": '{"email_cmd":"Carrier_po_gap_rtm_email_planning_team","rtm":"Carrier","greeting": "Dear Planners,"}',
        "desc": "每周四给Carrier Planing Team发送Po Gap邮件",
    },
    {
        "name": f"Education_formal_po_gap_rtm_email_for_gm",
        "type": TASK_TYPE_PO_GAP_RTM_EMAIL,
        "partition": 8,
        "run_time": "14:00:00",
        "params": '{"email_cmd":"Education_po_gap_rtm_email_gm","rtm":"Education","greeting": "Hello Stella,"}',
        "desc": "每周四给Education GM发送Po Gap邮件",
    },
    {
        "name": f"Education_formal_po_gap_rtm_email_for_planning_team",
        "type": TASK_TYPE_PO_GAP_RTM_EMAIL,
        "partition": 9,
        "run_time": "14:00:00",
        "params": '{"email_cmd":"Education_po_gap_rtm_email_planning_team","rtm":"Education","greeting": "Dear Planners,"}',
        "desc": "每周四给Education Planing Team发送Po Gap邮件",
    },
    {
        "name": f"Enterprise_formal_po_gap_rtm_email_for_gm",
        "type": TASK_TYPE_PO_GAP_RTM_EMAIL,
        "partition": 10,
        "run_time": "14:00:00",
        "params": '{"email_cmd":"Enterprise_po_gap_rtm_email_gm","rtm":"Enterprise","greeting": "Hello Stella,"}',
        "desc": "每周四给Enterprise GM发送Po Gap邮件",
    },
    {
        "name": f"Enterprise_formal_po_gap_rtm_email_for_planning_team",
        "type": TASK_TYPE_PO_GAP_RTM_EMAIL,
        "partition": 11,
        "run_time": "14:00:00",
        "params": '{"email_cmd":"Enterprise_po_gap_rtm_email_planning_team","rtm":"Enterprise","greeting": "Dear Planners,"}',
        "desc": "每周四给Enterprise Planing Team发送Po Gap邮件",
    },
]

FORMAL_PO_DELINQUENT_TASKS = [
    {
        "name": f"formal_po_delinquent_email_for_gm",
        "type": TASK_TYPE_PO_DELINQUENT_EMAIL,
        "partition": 0,
        "run_time": "14:00:00",
        "params": '{"email_cmd":"po_delinquent_email_gm","greeting": "Hello Brian,"}',
        "desc": "每周四给老板发送Po Delinquent邮件",
    },
    {
        "name": f"formal_po_delinquent_email_for_planning_team",
        "type": TASK_TYPE_PO_DELINQUENT_EMAIL,
        "partition": 1,
        "run_time": "14:00:00",
        "params": '{"email_cmd":"po_delinquent_email_planning_team","greeting": "Dear Planners,"}',
        "desc": "每周四给Planing Team发送Po Delinquent邮件",
    },
]

FORMAL_PO_DELINQUENT_RTM_TASKS = [
    {
        "name": f"Mono_formal_po_delinquent_rtm_email_for_gm",
        "type": TASK_TYPE_PO_DELINQUENT_RTM_EMAIL,
        "partition": 0,
        "run_time": "14:00:00",
        "params": '{"email_cmd":"Mono_po_delinquent_rtm_email_gm","rtm":"Mono","greeting": "Hello Steven,"}',
        "desc": "每周四给Mono GM发送Po Delinquent邮件",
    },
    {
        "name": f"Mono_formal_po_delinquent_rtm_email_for_planning_team",
        "type": TASK_TYPE_PO_DELINQUENT_RTM_EMAIL,
        "partition": 1,
        "run_time": "14:00:00",
        "params": '{"email_cmd":"Mono_po_delinquent_rtm_email_planning_team","rtm":"Mono","greeting": "Dear Planners,"}',
        "desc": "每周四给Mono Planing Team发送Po Delinquent邮件",
    },
    {
        "name": f"Multi_formal_po_delinquent_rtm_email_for_gm",
        "type": TASK_TYPE_PO_DELINQUENT_RTM_EMAIL,
        "partition": 2,
        "run_time": "14:00:00",
        "params": '{"email_cmd":"Multi_po_delinquent_rtm_email_gm","rtm":"Multi","greeting": "Hello Felix,"}',
        "desc": "每周四给Multi GM发送Po Delinquent邮件邮件",
    },
    {
        "name": f"Multi_formal_po_delinquent_rtm_email_for_planning_team",
        "type": TASK_TYPE_PO_DELINQUENT_RTM_EMAIL,
        "partition": 3,
        "run_time": "14:00:00",
        "params": '{"email_cmd":"Multi_po_delinquent_rtm_email_planning_team","rtm":"Multi","greeting": "Dear Planners,"}',
        "desc": "每周四给Multi Planing Team发送Po Delinquent邮件邮件",
    },
    {
        "name": f"Online_formal_po_delinquent_rtm_email_for_gm",
        "type": TASK_TYPE_PO_DELINQUENT_RTM_EMAIL,
        "partition": 4,
        "run_time": "14:00:00",
        "params": '{"email_cmd":"Online_po_delinquent_rtm_email_gm","rtm":"Online","greeting": "Hello Steven,"}',
        "desc": "每周四给Online GM发送Po Delinquent邮件邮件",
    },
    {
        "name": f"Online_formal_po_delinquent_rtm_email_for_planning_team",
        "type": TASK_TYPE_PO_DELINQUENT_RTM_EMAIL,
        "partition": 5,
        "run_time": "14:00:00",
        "params": '{"email_cmd":"Online_po_delinquent_rtm_email_planning_team","rtm":"Online","greeting": "Dear Planners,"}',
        "desc": "每周四给Online Planing Team发送Po Delinquent邮件邮件",
    },
    {
        "name": f"Carrier_formal_po_delinquent_rtm_email_for_gm",
        "type": TASK_TYPE_PO_DELINQUENT_RTM_EMAIL,
        "partition": 6,
        "run_time": "14:00:00",
        "params": '{"email_cmd":"Carrier_po_delinquent_rtm_email_gm","rtm":"Carrier","greeting": "Hello Tommy,"}',
        "desc": "每周四给Carrier GM发送Po Delinquent邮件邮件",
    },
    {
        "name": f"Carrier_formal_po_delinquent_rtm_email_for_planning_team",
        "type": TASK_TYPE_PO_DELINQUENT_RTM_EMAIL,
        "partition": 7,
        "run_time": "14:00:00",
        "params": '{"email_cmd":"Carrier_po_delinquent_rtm_email_planning_team","rtm":"Carrier","greeting": "Dear Planners,"}',
        "desc": "每周四给Carrier Planing Team发送Po Delinquent邮件邮件",
    },
    {
        "name": f"Education_formal_po_delinquent_rtm_email_for_gm",
        "type": TASK_TYPE_PO_DELINQUENT_RTM_EMAIL,
        "partition": 8,
        "run_time": "14:00:00",
        "params": '{"email_cmd":"Education_po_delinquent_rtm_email_gm","rtm":"Education","greeting": "Hello Stella,"}',
        "desc": "每周四给Education GM发送Po Delinquent邮件邮件",
    },
    {
        "name": f"Education_formal_po_delinquent_rtm_email_for_planning_team",
        "type": TASK_TYPE_PO_DELINQUENT_RTM_EMAIL,
        "partition": 9,
        "run_time": "14:00:00",
        "params": '{"email_cmd":"Education_po_delinquent_rtm_email_planning_team","rtm":"Education","greeting": "Dear Planners,"}',
        "desc": "每周四给Education Planing Team发送Po Delinquent邮件邮件",
    },
    {
        "name": f"Enterprise_formal_po_delinquent_rtm_email_for_gm",
        "type": TASK_TYPE_PO_DELINQUENT_RTM_EMAIL,
        "partition": 10,
        "run_time": "14:00:00",
        "params": '{"email_cmd":"Enterprise_po_delinquent_rtm_email_gm","rtm":"Enterprise","greeting": "Hello Stella,"}',
        "desc": "每周四给Enterprise GM发送Po Delinquent邮件邮件",
    },
    {
        "name": f"Enterprise_formal_po_delinquent_rtm_email_for_planning_team",
        "type": TASK_TYPE_PO_DELINQUENT_RTM_EMAIL,
        "partition": 11,
        "run_time": "14:00:00",
        "params": '{"email_cmd":"Enterprise_po_delinquent_rtm_email_planning_team","rtm":"Enterprise","greeting": "Dear Planners,"}',
        "desc": "每周四给Enterprise Planing Team发送Po Delinquent邮件邮件",
    },
]

PRESEND_PO_GAP_TASKS = [
    {
        "name": f"pre_po_gap_email_for_gm",
        "desc": "预发送-每周四给老板发送Po Gap邮件",
        "type": TASK_TYPE_PO_GAP_EMAIL,
        "partition": 10,
        "run_time": "13:00:00",
        "params": '{"email_cmd":"pre_po_gap_email_gm","greeting": "Hello Brian,"}',
    },
    {
        "name": f"pre_po_gap_email_for_planning_team",
        "desc": "预发送-每周四给Planing Team发送Po Gap邮件",
        "type": TASK_TYPE_PO_GAP_EMAIL,
        "partition": 11,
        "run_time": "13:00:00",
        "params": '{"email_cmd":"pre_po_gap_email_planning_team","greeting": "Dear Planners,"}',
    },
]

PRESEND_PO_GAP_RTM_TASKS = [
    {
        "name": f"pre_Mono_po_gap_rtm_email_for_gm",
        "desc": "预发送-每周四给Mono GM发送Po Gap邮件",
        "type": TASK_TYPE_PO_GAP_RTM_EMAIL,
        "partition": 20,
        "run_time": "13:00:00",
        "params": '{"email_cmd":"pre_Mono_po_gap_rtm_email_gm","rtm":"Mono","greeting": "Hello Steven,"}',
    },
    {
        "name": f"pre_Mono_po_gap_rtm_email_for_planning_team",
        "desc": "预发送-每周四给Mono Planing Team发送Po Gap邮件",
        "type": TASK_TYPE_PO_GAP_RTM_EMAIL,
        "partition": 21,
        "run_time": "13:00:00",
        "params": '{"email_cmd":"pre_Mono_po_gap_rtm_email_planning_team","rtm":"Mono","greeting": "Dear Planners,"}',
    },
    {
        "name": f"pre_Multi_po_gap_rtm_email_for_gm",
        "desc": "预发送-每周四给Multi GM发送Po Gap邮件",
        "type": TASK_TYPE_PO_GAP_RTM_EMAIL,
        "partition": 22,
        "run_time": "13:00:00",
        "params": '{"email_cmd":"pre_Multi_po_gap_rtm_email_gm","rtm":"Multi","greeting": "Hello Felix,"}',
    },
    {
        "name": f"pre_Multi_po_gap_rtm_email_for_planning_team",
        "desc": "预发送-每周四给Multi Planing Team发送Po Gap邮件",
        "type": TASK_TYPE_PO_GAP_RTM_EMAIL,
        "partition": 23,
        "run_time": "13:00:00",
        "params": '{"email_cmd":"pre_Multi_po_gap_rtm_email_planning_team","rtm":"Multi","greeting": "Dear Planners,"}',
    },
    {
        "name": f"pre_Online_po_gap_rtm_email_for_gm",
        "desc": "预发送-每周四给Online GM发送Po Gap邮件",
        "type": TASK_TYPE_PO_GAP_RTM_EMAIL,
        "partition": 24,
        "run_time": "13:00:00",
        "params": '{"email_cmd":"pre_Online_po_gap_rtm_email_gm","rtm":"Online","greeting": "Hello Steven,"}',
    },
    {
        "name": f"pre_Online_po_gap_rtm_email_for_planning_team",
        "desc": "预发送-每周四给Online Planing Team发送Po Gap邮件",
        "type": TASK_TYPE_PO_GAP_RTM_EMAIL,
        "partition": 25,
        "run_time": "13:00:00",
        "params": '{"email_cmd":"pre_Online_po_gap_rtm_email_planning_team","rtm":"Online","greeting": "Dear Planners,"}',
    },
    {
        "name": f"pre_Carrier_po_gap_rtm_email_for_gm",
        "desc": "预发送-每周四给Carrier GM发送Po Gap邮件",
        "type": TASK_TYPE_PO_GAP_RTM_EMAIL,
        "partition": 26,
        "run_time": "13:00:00",
        "params": '{"email_cmd":"pre_Carrier_po_gap_rtm_email_gm","rtm":"Carrier","greeting": "Hello Tommy,"}',
    },
    {
        "name": f"pre_Carrier_po_gap_rtm_email_for_planning_team",
        "desc": "预发送-每周四给Carrier Planing Team发送Po Gap邮件",
        "type": TASK_TYPE_PO_GAP_RTM_EMAIL,
        "partition": 27,
        "run_time": "13:00:00",
        "params": '{"email_cmd":"pre_Carrier_po_gap_rtm_email_planning_team","rtm":"Carrier","greeting": "Dear Planners,"}',
    },
    {
        "name": f"pre_Education_po_gap_rtm_email_for_gm",
        "desc": "预发送-每周四给Education GM发送Po Gap邮件",
        "type": TASK_TYPE_PO_GAP_RTM_EMAIL,
        "partition": 28,
        "run_time": "13:00:00",
        "params": '{"email_cmd":"pre_Education_po_gap_rtm_email_gm","rtm":"Education","greeting": "Hello Stella,"}',
    },
    {
        "name": f"pre_Education_po_gap_rtm_email_for_planning_team",
        "desc": "预发送-每周四给Education Planing Team发送Po Gap邮件",
        "type": TASK_TYPE_PO_GAP_RTM_EMAIL,
        "partition": 29,
        "run_time": "13:00:00",
        "params": '{"email_cmd":"pre_Education_po_gap_rtm_email_planning_team","rtm":"Education","greeting": "Dear Planners,"}',
    },
    {
        "name": f"pre_Enterprise_po_gap_rtm_email_for_gm",
        "desc": "预发送-每周四给Enterprise GM发送Po Gap邮件",
        "type": TASK_TYPE_PO_GAP_RTM_EMAIL,
        "partition": 30,
        "run_time": "13:00:00",
        "params": '{"email_cmd":"pre_Enterprise_po_gap_rtm_email_gm","rtm":"Enterprise","greeting": "Hello Stella,"}',
    },
    {
        "name": f"pre_Enterprise_po_gap_rtm_email_for_planning_team",
        "desc": "预发送-每周四给Enterprise Planing Team发送Po Gap邮件",
        "type": TASK_TYPE_PO_GAP_RTM_EMAIL,
        "partition": 31,
        "run_time": "13:00:00",
        "params": '{"email_cmd":"pre_Enterprise_po_gap_rtm_email_planning_team","rtm":"Enterprise","greeting": "Dear Planners,"}',
    },
]


PRESEND_PO_DELINQUENT_TASKS = [
    {
        "name": f"pre_po_delinquent_email_for_gm",
        "desc": "预发送-每周四给老板发送Po Delinquent邮件",
        "type": TASK_TYPE_PO_DELINQUENT_EMAIL,
        "partition": 10,
        "run_time": "13:00:00",
        "params": '{"email_cmd":"pre_po_delinquent_email_gm","greeting": "Hello Brian,"}',
    },
    {
        "name": f"pre_po_delinquent_email_for_planning_team",
        "desc": "预发送-每周四给Planing Team发送Po Delinquent邮件",
        "type": TASK_TYPE_PO_DELINQUENT_EMAIL,
        "partition": 11,
        "run_time": "13:00:00",
        "params": '{"email_cmd":"pre_po_delinquent_email_planning_team","greeting": "Dear Planners,"}',
    },
]

PRESEND_PO_DELINQUENT_RTM_TASKS = [
    {
        "name": f"pre_Mono_po_delinquent_rtm_email_for_gm",
        "desc": "预发送-每周四给Mono GM发送Po Delinquent邮件",
        "type": TASK_TYPE_PO_DELINQUENT_RTM_EMAIL,
        "partition": 20,
        "run_time": "13:00:00",
        "params": '{"email_cmd":"pre_Mono_po_delinquent_rtm_email_gm","rtm":"Mono","greeting": "Hello Steven,"}',
    },
    {
        "name": f"pre_Mono_po_delinquent_rtm_email_for_planning_team",
        "desc": "预发送-每周四给Mono Planing Team发送Po Delinquent邮件",
        "type": TASK_TYPE_PO_DELINQUENT_RTM_EMAIL,
        "partition": 21,
        "run_time": "13:00:00",
        "params": '{"email_cmd":"pre_Mono_po_delinquent_rtm_email_planning_team","rtm":"Mono","greeting": "Dear Planners,"}',
    },
    {
        "name": f"pre_Multi_po_delinquent_rtm_email_for_gm",
        "desc": "预发送-每周四给Multi GM发送Po Delinquent邮件邮件",
        "type": TASK_TYPE_PO_DELINQUENT_RTM_EMAIL,
        "partition": 22,
        "run_time": "13:00:00",
        "params": '{"email_cmd":"pre_Multi_po_delinquent_rtm_email_gm","rtm":"Multi","greeting": "Hello Felix,"}',
    },
    {
        "name": f"pre_Multi_po_delinquent_rtm_email_for_planning_team",
        "desc": "预发送-每周四给Multi Planing Team发送Po Delinquent邮件邮件",
        "type": TASK_TYPE_PO_DELINQUENT_RTM_EMAIL,
        "partition": 23,
        "run_time": "13:00:00",
        "params": '{"email_cmd":"pre_Multi_po_delinquent_rtm_email_planning_team","rtm":"Multi","greeting": "Dear Planners,"}',
    },
    {
        "name": f"pre_Online_po_delinquent_rtm_email_for_gm",
        "desc": "预发送-每周四给Online GM发送Po Delinquent邮件邮件",
        "type": TASK_TYPE_PO_DELINQUENT_RTM_EMAIL,
        "partition": 24,
        "run_time": "13:00:00",
        "params": '{"email_cmd":"pre_Online_po_delinquent_rtm_email_gm","rtm":"Online","greeting": "Hello Steven,"}',
    },
    {
        "name": f"pre_Online_po_delinquent_rtm_email_for_planning_team",
        "desc": "预发送-每周四给Online Planing Team发送Po Delinquent邮件邮件",
        "type": TASK_TYPE_PO_DELINQUENT_RTM_EMAIL,
        "partition": 25,
        "run_time": "13:00:00",
        "params": '{"email_cmd":"pre_Online_po_delinquent_rtm_email_planning_team","rtm":"Online","greeting": "Dear Planners,"}',
    },
    {
        "name": f"pre_Carrier_po_delinquent_rtm_email_for_gm",
        "desc": "预发送-每周四给Carrier GM发送Po Delinquent邮件邮件",
        "type": TASK_TYPE_PO_DELINQUENT_RTM_EMAIL,
        "partition": 26,
        "run_time": "13:00:00",
        "params": '{"email_cmd":"pre_Carrier_po_delinquent_rtm_email_gm","rtm":"Carrier","greeting": "Hello Tommy,"}',
    },
    {
        "name": f"pre_Carrier_po_delinquent_rtm_email_for_planning_team",
        "desc": "预发送-每周四给Carrier Planing Team发送Po Delinquent邮件邮件",
        "type": TASK_TYPE_PO_DELINQUENT_RTM_EMAIL,
        "partition": 27,
        "run_time": "13:00:00",
        "params": '{"email_cmd":"pre_Carrier_po_delinquent_rtm_email_planning_team","rtm":"Carrier","greeting": "Dear Planners,"}',
    },
    {
        "name": f"pre_Education_po_delinquent_rtm_email_for_gm",
        "desc": "预发送-每周四给Education GM发送Po Delinquent邮件邮件",
        "type": TASK_TYPE_PO_DELINQUENT_RTM_EMAIL,
        "partition": 28,
        "run_time": "13:00:00",
        "params": '{"email_cmd":"pre_Education_po_delinquent_rtm_email_gm","rtm":"Education","greeting": "Hello Stella,"}',
    },
    {
        "name": f"pre_Education_po_delinquent_rtm_email_for_planning_team",
        "desc": "预发送-每周四给Education Planing Team发送Po Delinquent邮件邮件",
        "type": TASK_TYPE_PO_DELINQUENT_RTM_EMAIL,
        "partition": 29,
        "run_time": "13:00:00",
        "params": '{"email_cmd":"pre_Education_po_delinquent_rtm_email_planning_team","rtm":"Education","greeting": "Dear Planners,"}',
    },
    {
        "name": f"pre_Enterprise_po_delinquent_rtm_email_for_gm",
        "desc": "预发送-每周四给Enterprise GM发送Po Delinquent邮件邮件",
        "type": TASK_TYPE_PO_DELINQUENT_RTM_EMAIL,
        "partition": 30,
        "run_time": "13:00:00",
        "params": '{"email_cmd":"pre_Enterprise_po_delinquent_rtm_email_gm","rtm":"Enterprise","greeting": "Hello Stella,"}',
    },
    {
        "name": f"pre_Enterprise_po_delinquent_rtm_email_for_planning_team",
        "desc": "预发送-每周四给Enterprise Planing Team发送Po Delinquent邮件邮件",
        "type": TASK_TYPE_PO_DELINQUENT_RTM_EMAIL,
        "partition": 31,
        "run_time": "13:00:00",
        "params": '{"email_cmd":"pre_Enterprise_po_delinquent_rtm_email_planning_team","rtm":"Enterprise","greeting": "Dear Planners,"}',
    },
]

PO_GAP_DELINQUENT_TASK_LIST = [
    *FORMAL_PO_GAP_TASKS,
    *FORMAL_PO_GAP_RTM_TASKS,
    *FORMAL_PO_DELINQUENT_TASKS,
    *FORMAL_PO_DELINQUENT_RTM_TASKS,
    *PRESEND_PO_GAP_TASKS,
    *PRESEND_PO_GAP_RTM_TASKS,
    *PRESEND_PO_DELINQUENT_TASKS,
    *PRESEND_PO_DELINQUENT_RTM_TASKS,
]
