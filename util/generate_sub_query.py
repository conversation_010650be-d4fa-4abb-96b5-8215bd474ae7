# 生成union之前的字查询, 如果不使用label, 在后续使用中会报错‘没有对应的字段’
def generate_sold_to_sub_query(db_session, clazz, fiscal_week_year):
    
    return db_session.query(clazz.rtm.label('rtm'),
                                clazz.business_type.label('business_type'),
                                clazz.sold_to_id.label('sold_to_id'),
                                clazz.sold_to_name_en.label('sold_to_name_en'),
                                clazz.abbre.label('abbre'),
                                clazz.start_week.label('start_week'),
                                clazz.end_week.label('end_week'),
                                clazz.create_time.label('create_time'),
                                clazz.update_time.label('update_time'),
                                )\
                            .filter(clazz.start_week <= fiscal_week_year) \
                            .filter(clazz.end_week >= fiscal_week_year) \
                            .subquery()
