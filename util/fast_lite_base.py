from .conf import *
from .util import *


fast_lite_config = conf.get('fast_lite')
if os.environ.get('DB_ENV') == 'aws':
    fast_lite_host = fast_lite_config['aws_host']
    fast_lite_sec = get_secret_from_aws(fast_lite_config['secret_name'], fast_lite_config['secret_region'])
else :
    fast_lite_host = fast_lite_config['apple_host']
    fast_lite_sec = get_secret_from_apple(fast_lite_config['secret_name'], fast_lite_config['secret_region'], secret_req_host, secret_req_path)

fast_lite_user = fast_lite_sec['username']
fast_lite_pass = fast_lite_sec['password']

fast_lite_port = '3306'
fast_lite_name = fast_lite_config['default_db']

fast_lite_db = "mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8".format(fast_lite_user, urlquote(fast_lite_pass), fast_lite_host, fast_lite_port, fast_lite_name)

fast_lite_engine = create_engine(fast_lite_db, pool_pre_ping=True, pool_size=20, max_overflow=5, pool_recycle=3600)

FASTLiteBase = declarative_base()
FASTLiteSession = sessionmaker(bind=fast_lite_engine)
TaskSession = sessionmaker(bind=fast_lite_engine, expire_on_commit=False)

mono_name = fast_lite_config['mono_db']
mono_db = "mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8".format(fast_lite_user, urlquote(fast_lite_pass), fast_lite_host, fast_lite_port, mono_name)
mono_engine = create_engine(mono_db, pool_pre_ping=True, pool_size=20, max_overflow=5, pool_recycle=3600)

FASTMonoBase = declarative_base()
FASTMonoSession = sessionmaker(bind=mono_engine)

channel_compliance_name = fast_lite_config['channel_compliance']
channel_compliance_db = "mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8".format(fast_lite_user, urlquote(fast_lite_pass), fast_lite_host, fast_lite_port, channel_compliance_name)

channel_compliance_engine = create_engine(channel_compliance_db, pool_pre_ping=True, pool_size=20, max_overflow=5, pool_recycle=3600)

ChannelComplianceBase = declarative_base()
ChannelComplianceSession = sessionmaker(bind=channel_compliance_engine)
