import os
from datetime import datetime

from util.mail_sender import MailSender
from util.mail_conf import mail_config
from service.common import xthread
from util.const import AllocationRTM, StrRTMCPF
from data.email_recipient import TblEmailRecipient


def send_customized_email(to: list, cc: list, subject: str, content: str, content_type: str, file_paths: list = None, extras: str = None, extras_type: str = None):
    """自定义收件人和抄送人, 以及邮件内容发送邮件

    Args:
        to (list): 收件人
        cc (list): 抄送人
        subject (str): 主题
        content (str): 正文
        content_type (str): 正文类型, 'html' or 'plain'
        file_paths (list, optional): 附件的绝对路径, eg.["/tmp/file1.txt", "/tmp/file2.txt"], 默认为None.
        extras (str, optional): 附件内容, 默认为None.
        extras_type (str, optional): 附加内容类型, 同content_type, 默认为None.
    """
    try:
        sender = MailSender(mail_config['smtp_host'], mail_config['smtp_port'],
                            mail_config['login'], mail_config['password'])
        sender.sender(to, cc, subject, content, content_type,
                      file_paths, extras, extras_type)
        sender.close()
    except Exception as e:
        raise(e)


def async_send_email(to: list, cc: list, subject: str, content: str, content_type: str, file_paths: list = None, extras: str = None, extras_type: str = None):
    '''
    异步发送邮件
    '''
    t = xthread(send_customized_email, [to, cc, subject, content, content_type,
                                        file_paths, extras, extras_type])
    t.start()


def read_config_template(date: str, content: str):
    with open(os.path.dirname(os.path.dirname(os.path.abspath(__file__))) + '/conf/cpf_allocation_mail_template.html', 'rb') as fb:
        config_template = fb.read().decode()
    config_template = config_template.replace('&TIME&', date)
    config_template = config_template.replace('&CONTENT&', content)
    return config_template


Greetings = {
    'All': "Dear All",
    "System": "Dear FAST system user"
}

class TemplateEmail:
    date = ''

    def __init__(self) -> None:
        self.date = datetime.now().strftime("%b %d, %Y")
        
    def _send_email(self, to, cc):
        async_send_email(to, cc, 'hello test', 'nice to meet you.', 'plain')


    def sales_input_1(self, fiscal_qtr_week_name: str, lob: str = 'iPad'):
        '''
        周一17:30，周一下午的ESR未Ready，iPad Allocation任务无法开启，提醒业务Offline提供
        '''
        # 从邮件邮件人配置中读取
        to, cc = TblEmailRecipient.get_to_cc_list(0, StrRTMCPF)
        
        subject = f"System Alert - FAST Allocation System Failed to Capture ESR - {fiscal_qtr_week_name}"
        content = f'''
                <div class="section-info">{Greetings.get('System', '')},</div>
                <div class="section-info">The FAST Allocation system failed to obtain the latest ESR report by 17:30, and this issue will block the <span class="info-weight">{lob}</span> allocation process. Please provide the latest ESR file to DMP Team ASAP, Thanks.</div>
                <div class="section-info">Sincerely,</div>
                <div class="section-info"> Expert Technical Support</div>
                '''
        email_config = read_config_template(self.date, content)

        async_send_email(to, cc, subject, email_config, 'html')


    def sales_input_2(self, fiscal_qtr_week_name: str, rtm: str, lob: str = 'iPad'):
        '''
        周一17:30，周一下午的ESR未Ready，iPad Allocation任务无法开启，提醒RTM等待入口开启
        '''
        # 从邮件邮件人配置中读取
        to, cc = TblEmailRecipient.get_to_cc_list(0, rtm)
        
        subject = f"System Alert - FAST Allocation Prep. & Submission System Failed to Capture ESR - {fiscal_qtr_week_name}"
        content = f'''
                <div class="section-info">{Greetings.get('System', '')},</div>
                <div class="section-info">The FAST Allocation Prep. & Submission system failed to obtain the latest ESR report by 17:30, and this issue will block the <span class="info-weight">{lob}</span> Sales Input process. Please wait for processing, Thanks.</div>
                <div class="section-info">Sincerely,</div>
                <div class="section-info"> Expert Technical Support</div>
                '''
        email_config = read_config_template(self.date, content)

        async_send_email(to, cc, subject, email_config, 'html')


    def sales_input_3(self, fiscal_qtr_week_name: str, rtm: str, uploader: str, upload_at: str, system_link: str, lob: str = 'iPad'):
        '''
        RTM上传成功iPad Sales Input文件后，通知CP&F查收
        '''
        # 从邮件邮件人配置中读取
        to, _ = TblEmailRecipient.get_to_cc_list(0, StrRTMCPF)
        cc = []
        
        subject = f"FAST System Message - Sales Input Submitted by {rtm} for {lob} Allocation - {fiscal_qtr_week_name}"
        content = f'''
                <div class="section-info">{Greetings.get('System', '')},</div>
                <div class="section-info">The <span class="info-weight">{rtm}</span> <span class="info-weight">{lob}</span> Sales Input data of the current week was uploaded by <span class="info-weight">{uploader}</span> at <span class="info-weight">{upload_at}</span>. Please login on FAST Allocation System for details. </div>
                <div class="section-info">Please click the following button to enter the FAST Allocation System.</div>
                <div class="section-info">Sincerely,</div>
                <div class="section-info"> Expert Technical Support</div>
                <div class="enter-btn">
                    <a href=\"{system_link}\" class="btn">Enter</a>
                </div>
                '''
        
        email_config = read_config_template(self.date, content)

        async_send_email(to, cc, subject, email_config, 'html')


    def sales_input_4(self, fiscal_qtr_week_name: str, rtm: str, re_run_time: str, system_link: str):
        '''
        CP&F Re-run RTM上传的iPad Sales Input，提醒RTM重新上传iPad Sales Input
        '''
        # 从邮件邮件人配置中读取
        to, cc = TblEmailRecipient.get_to_cc_list(0, rtm)
        
        subject = f"FAST System Message - {rtm} Sales Input Re-run Process Is Now Activated  - {fiscal_qtr_week_name}"
        content = f'''
                <div class="section-info">{Greetings.get('System', '')},</div>
                <div class="section-info">The “Re-run” process in the sales-input submission module has been activated at <span class="info-weight">{re_run_time}</span>, you can re-upload a sales-input data file to the system for current week allocation. The file shall be uploaded before 16:00, Tuesday.</div>
                <div class="section-info">Please click the following button to enter the FAST Allocation System.</div>
                <div class="section-info">Sincerely,</div>
                <div class="section-info"> Expert Technical Support</div>
                <div class="enter-btn">
                    <a href=\"{system_link}\" class="btn">Enter</a>
                </div>
                '''
        email_config = read_config_template(self.date, content)

        async_send_email(to, cc, subject, email_config, 'html')


    def sales_input_5(self, fiscal_qtr_week_name: str, description: str, sender_name: str, file_path: list, lob: str = 'iPad'):
        '''
        CP&F Publish iPad Sales Input 合并文件后 发邮件给OPS, 不使用模版
        '''
        # 从邮件邮件人配置中读取
        to, cc = TblEmailRecipient.get_to_cc_list(0, 'OPS_Sales_Input')
        
        subject = f"GC iPad Sales Input - {fiscal_qtr_week_name}"
        description = f"{description}\n\r" if description else ""
        content = f"{Greetings.get('All', '')},\n\rAttached is GC iPad sales input.\n\r{description}Sending by {sender_name}\n\r"

        async_send_email(to, cc, subject, content, 'plain', file_path)


    def sell_in_demand_6(self, rtm: str, fiscal_qtr_week_name: str, system_link: str, lob: str = 'iPad'):
        '''
        周二18:30如果Multi/Online 仍未提交iPad Demand数据，则触发邮件提醒Multi/Online Planner尽快提交Demand数据文件
        '''
        # 从邮件邮件人配置中读取
        to, cc = TblEmailRecipient.get_to_cc_list(0, rtm)
        
        # FAST System Alert - *{LOB}* Current Week Demand Data Not Submitted *- {Fiscal Week}*
        subject = f"FAST System Alert - {lob} Current Week Demand Data Not Submitted - {fiscal_qtr_week_name}"
        content = f'''
                <div class="section-info">{Greetings.get('System', '')},</div>
                <div class="section-info">The Demand Submission phase will end at 19:00. Please submit demand data file for current week allocation ASAP, Thanks.</div>
                <div class="section-info">Please click the following button to enter the FAST Allocation System.</div>
                <div class="section-info">Sincerely,</div>
                <div class="section-info"> Expert Technical Support</div>
                <div class="enter-btn">
                    <a href=\"{system_link}\" class="btn">Enter</a>
                </div>
                '''
        email_config = read_config_template(self.date, content)

        async_send_email(to, cc, subject, email_config, 'html')


    def sell_in_demand_7(self, fiscal_qtr_week_name: str, system_link: str, lob: str = 'iPad'):
        '''
        周二18:30如果Online 仍未提交iPad Demand数据，则触发邮件提醒Online Planner尽快提交Demand数据文件
        '''
        # 从邮件邮件人配置中读取
        to, cc = TblEmailRecipient.get_to_cc_list(0, AllocationRTM.Online)
        
        # FAST System Alert - *{LOB}* Current Week Demand Data Not Submitted *- {Fiscal Week}*
        subject = f"FAST System Alert - {lob} Current Week Demand Data Not Submitted - {fiscal_qtr_week_name}"
        content = f'''
                <div class="section-info">{Greetings.get('System', '')},</div>
                <div class="section-info">The Demand Submission phase will end at 19:00. Please submit demand data file for current week allocation ASAP, Thanks.</div>
                <div class="section-info">Please click the following button to enter the FAST Allocation System.</div>
                <div class="section-info">Sincerely,</div>
                <div class="section-info"> Expert Technical Support</div>
                <div class="enter-btn">
                    <a href=\"{system_link}\" class="btn">Enter</a>
                </div>
                '''
        email_config = read_config_template(self.date, content)

        async_send_email(to, cc, subject, email_config, 'html')


    def sell_in_demand_8(self, fiscal_qtr_week_name: str, rtm: str = AllocationRTM.Multi, lob: str = 'iPad'):
        '''
        周二19:00 Demand Submission 时间窗关闭时，Multi/Online还未提交Demand，Multi Sell-in Demand数据将会生成失败，邮件提醒Multi/Online 业务方
        '''
        # 从邮件邮件人配置中读取
        to, cc = TblEmailRecipient.get_to_cc_list(0, rtm)
        
        # FAST System Alert - *{LOB}* Current Week Demand Submission Not Finished *- {Fiscal Week}*
        subject = f"FAST System Alert - {lob} Current Week Demand Submission Not Finished - {fiscal_qtr_week_name}"
        # FAST system failed to generate the demand data for current week {lob} allocation, since the {rtm} team didn't finish demand data submission on time.
        content = f'''
                <div class="section-info">{Greetings.get('System', '')},</div>
                <div class="section-info">FAST system failed to generate the demand data for current week <span class="info-weight">{lob}</span> allocation, since the <span class="info-weight">{rtm}</span> team didn't finish demand data submission on time.</div>
                <div class="section-info">Sincerely,</div>
                <div class="section-info"> Expert Technical Support</div>
                '''
        email_config = read_config_template(self.date, content)

        async_send_email(to, cc, subject, email_config, 'html')


    def sell_in_demand_9(self, fiscal_qtr_week_name: str, rtm: str = AllocationRTM.Online, lob: str = 'iPad'):
        '''
        周二19:00 Demand Submission 时间窗关闭时，Online还未提交Demand，Online Sell-in Demand数据将会生成失败，邮件提醒Online 业务方
        '''
        # 从邮件邮件人配置中读取
        to, cc = TblEmailRecipient.get_to_cc_list(0, rtm)
        
        # FAST System Alert - *{LOB}* Current Week Demand Submission Not Finished *- {Fiscal Week}*
        subject = f"FAST System Alert - {lob} Current Week Demand Submission Not Finished - {fiscal_qtr_week_name}"
        # FAST system failed to generate the demand data for current week {lob} allocation, since the {rtm} team didn't finish demand data submission on time.
        content = f'''
                <div class="section-info">{Greetings.get('System', '')},</div>
                <div class="section-info">FAST system failed to generate the demand data for current week <span class="info-weight">{lob}</span> allocation, since the <span class="info-weight">{rtm}</span> team didn't finish demand data submission on time.</div>
                <div class="section-info">Sincerely,</div>
                <div class="section-info"> Expert Technical Support</div>
                '''
        email_config = read_config_template(self.date, content)

        async_send_email(to, cc, subject, email_config, 'html')


    def sell_in_demand_10(self, fiscal_qtr_week_name: str, rtm: str, lob: str = 'iPad'):
        '''
        周二19:00 Demand Submission 时间窗关闭时，Multi / Online还未提交Demand，Multi / Online的 Sell-in Demand数据将会生成失败，邮件提醒CP&F 业务方
        '''
        # 从邮件邮件人配置中读取
        to, _ = TblEmailRecipient.get_to_cc_list(0, StrRTMCPF)
        cc = []
        
        # FAST System Alert - *{LOB}* Current Week Demand Submission Not Finished *- {Fiscal Week}*
        subject = f"FAST System Alert - {lob} Current Week Demand Submission Not Finished - {fiscal_qtr_week_name}"
        # FAST system failed to generate the {rtm} {lob} Demand (with Tags) for current week allocation, since demand data was not submitted on time. 
        content = f'''
                <div class="section-info">{Greetings.get('System', '')},</div>
                <div class="section-info">FAST system failed to generate the <span class="info-weight">{rtm}</span> <span class="info-weight">{lob}</span> Demand (with Tags) for current week allocation, since demand data was not submitted on time.</div>
                <div class="section-info">Sincerely,</div>
                <div class="section-info"> Expert Technical Support</div>
                '''
        email_config = read_config_template(self.date, content)

        async_send_email(to, cc, subject, email_config, 'html')


    def sell_in_demand_11(self, fiscal_qtr_week_name: str, system_link: str, lob: str = 'iPad'):
        '''
        周二晚22:00从Mono FAST取来的Demand数据出了比Sales Input 小的情况时，发邮件提醒CP&F 业务方，进入系统手动处理
        '''
        # 从邮件邮件人配置中读取
        to, _ = TblEmailRecipient.get_to_cc_list(0, StrRTMCPF)
        cc = []
        
        # FAST System Alert - *{LOB}* Demand Data Error *- {Fiscal Week}*
        subject = f"FAST System Alert - {lob} Demand Data Error - {fiscal_qtr_week_name}"
        content = f'''
                <div class="section-info">{Greetings.get('System', '')},</div>
                <div class="section-info">FAST allocation system failed to generate Sell-in Demand for allocation, since demand data fetched from Mono FAST system is invalid for Demand values are less than sales input values submitted. Please login on FAST Allocation System to process.</div>
                <div class="section-info">Please click the following button to enter the FAST Allocation System.</div>
                <div class="section-info">Sincerely,</div>
                <div class="section-info"> Expert Technical Support</div>
                <div class="enter-btn">
                    <a href=\"{system_link}\" class="btn">Enter</a>
                </div>
                '''
        email_config = read_config_template(self.date, content)

        async_send_email(to, cc, subject, email_config, 'html')


    def sell_in_demand_12(self, fiscal_qtr_week_name: str, lob: str = 'iPad'):
        '''
        周二晚22:00从Mono FAST取来的Demand数据出了比Sales Input 小的情况时，发邮件提醒Mono 业务方
        '''
        # 从邮件邮件人配置中读取
        to, cc = TblEmailRecipient.get_to_cc_list(0, AllocationRTM.Mono)
        
        # FAST System Alert - *{LOB}* Demand Data Error *- {Fiscal Week}*
        subject = f"FAST System Alert - {lob} Demand Data Error - {fiscal_qtr_week_name}"
        content = f'''
                <div class="section-info">{Greetings.get('System', '')},</div>
                <div class="section-info">CP&F FAST allocation system failed to generate Sell-in Demand for allocation, since demand data fetched from Mono FAST system is invalid for Demand values are less than sales input values submitted. Please contact the CP&F team.</div>
                <div class="section-info">Sincerely,</div>
                <div class="section-info"> Expert Technical Support</div>
                '''
        email_config = read_config_template(self.date, content)

        async_send_email(to, cc, subject, email_config, 'html')


    def sell_in_demand_13(self, fiscal_qtr_week_name: str, file_path: list, description: str, sender_name: str, extras: str, extras_type: str):
        '''
        CP&F Publish iPad Demand (HR Only) 数据，发邮件给OPS, 不使用模版
        '''
        # 从邮件邮件人配置中读取
        to, cc = TblEmailRecipient.get_to_cc_list(0, 'OPS_SI_Demand')
        
        # *GC iPad Partner SIF - {Fiscal Week +1/+2/+3/+4}*  例如：GC iPad Partner SIF - FY23Q3W2/W3/W4/W5
        subject = f"GC iPad Partner SIF - {fiscal_qtr_week_name}"
        description = f"{description}\n\r" if description else ""
        content = f"{Greetings.get('All', '')},\n\rAttached is iPad partner SIF\n\r{description}Sending by {sender_name}\n\r"

        async_send_email(to, cc, subject, content, 'plain', file_path, extras, extras_type)


    def adjustment_14(self, fiscal_qtr_week_name: str, lob: str = 'iPad'):
        '''
        周三17:30，周三下午的ESR未Ready，无法开启iPad Demand Adjustment，提醒业务Offline提供
        '''
        # 从邮件邮件人配置中读取
        to, cc = TblEmailRecipient.get_to_cc_list(0, StrRTMCPF)
        
        # System Alert - FAST Allocation System Failed to Capture ESR *- {Fiscal Week}*
        subject = f"System Alert - FAST Allocation System Failed to Capture ESR - {fiscal_qtr_week_name}"
        content = f'''
                <div class="section-info">{Greetings.get('System', '')},</div>
                <div class="section-info">The FAST Allocation system failed to obtain the latest ESR report by 17:30, and this issue will block the <span class="info-weight">{lob}</span> allocation process. Please provide the latest ESR file to DMP Team ASAP, Thanks.</div>
                <div class="section-info">Sincerely,</div>
                <div class="section-info"> Expert Technical Support</div>
                '''
        email_config = read_config_template(self.date, content)

        async_send_email(to, cc, subject, email_config, 'html')


    def adjustment_15(self, fiscal_qtr_week_name: str, rtm: str, lob: str = 'iPad'):
        '''
        周三17:30，周三下午的ESR未Ready，Demand Adjustment任务无法开启，提醒RTM等待入口开启
        '''
        # 从邮件邮件人配置中读取
        to, cc = TblEmailRecipient.get_to_cc_list(0, rtm)
        
        # System Alert - FAST Allocation System Failed to Capture ESR *- {Fiscal Week}*
        subject = f"System Alert - FAST Allocation System Failed to Capture ESR - {fiscal_qtr_week_name}"
        content = f'''
                <div class="section-info">{Greetings.get('System', '')},</div>
                <div class="section-info">The FAST Allocation Prep. & Submission system failed to obtain the latest ESR report by 17:30, and this issue will block the <span class="info-weight">{lob}</span> Demand Adjustment process. Please wait for processing, Thanks.</div>
                <div class="section-info">Sincerely,</div>
                <div class="section-info"> Expert Technical Support</div>
                '''
        email_config = read_config_template(self.date, content)

        async_send_email(to, cc, subject, email_config, 'html')


    def adjustment_16(self, fiscal_qtr_week_name: str, rtm: str, uploader:str, upload_at: str, system_link: str, lob: str = 'iPad'):
        '''
        iPad Demand Adjustment阶段，RTM提交调整申请后，提醒CP&F进行审核
        '''
        # 从邮件邮件人配置中读取
        to, _ = TblEmailRecipient.get_to_cc_list(0, StrRTMCPF)
        cc = []
        
        # FAST System Message - Demand Adjustment Submitted by *{RTM}* for *{LOB}* Allocation *- {Fiscal Week}*
        subject = f"FAST System Message - Demand Adjustment Submitted by {rtm} for {lob} Allocation - {fiscal_qtr_week_name}"
        content = f'''
                <div class="section-info">{Greetings.get('System', '')},</div>
                <div class="section-info">A demand adjustment for <span class="info-weight">{rtm}</span> <span class="info-weight">{lob}</span> allocation has been submitted by <span class="info-weight">{uploader}</span> at <span class="info-weight">{upload_at}</span>. Please login on FAST Allocation System for details.</div>
                <div class="section-info">Please click the following button to enter the FAST Allocation System.</div>
                <div class="section-info">Sincerely,</div>
                <div class="section-info"> Expert Technical Support</div>
                <div class="enter-btn">
                    <a href=\"{system_link}\" class="btn">Enter</a>
                </div>
                '''
        email_config = read_config_template(self.date, content)

        async_send_email(to, cc, subject, email_config, 'html')


    def adjustment_17(self, fiscal_qtr_week_name: str, rtm: str, re_run_time: str, system_link: str, lob: str = 'iPad'):
        '''
        iPad Demand Adjustment阶段，CP&F将RTM提交的调整申请Re-run，提醒RTM重新提交
        '''
        # 从邮件邮件人配置中读取
        to, cc = TblEmailRecipient.get_to_cc_list(0, rtm)
        
        # FAST System Message - *{RTM}* Demand Adjustment Re-run Process Is Now Activated *- {Fiscal Week}*
        subject = f"FAST System Message - {rtm} Demand Adjustment Re-run Process Is Now Activated - {fiscal_qtr_week_name}"
        content = f'''
                <div class="section-info">{Greetings.get('System', '')},</div>
                <div class="section-info">The “Re-run” process in the demand adjustment submission module has been activated at <span class="info-weight">{re_run_time}</span>, you can re-upload an adjustment data file to the system for current week allocation.</div>
                <div class="section-info">Please click the following button to enter the FAST Allocation System.</div>
                <div class="section-info">Sincerely,</div>
                <div class="section-info"> Expert Technical Support</div>
                <div class="enter-btn">
                    <a href=\"{system_link}\" class="btn">Enter</a>
                </div>
                '''
        email_config = read_config_template(self.date, content)

        async_send_email(to, cc, subject, email_config, 'html')


    def overview_18(self, fiscal_qtr_week_name: str, rtm: str, comfirm_time: str, lob: str = 'iPad'):
        '''
        iPad Demand Overview阶段，CP&F点了Confirm，Adjustment阶段结束，邮件通知RTM
        '''
        # 从邮件邮件人配置中读取
        to, cc = TblEmailRecipient.get_to_cc_list(0, rtm)
        
        # FAST System Message - *{RTM}* Demand data for *{LOB}* CP&F Allocation has been confirmed  *- {Fiscal Week}*
        subject = f"FAST System Message - {rtm} Demand data for {lob} CP&F Allocation has been confirmed - {fiscal_qtr_week_name}"
        content = f'''
                <div class="section-info">{Greetings.get('System', '')},</div>
                <div class="section-info">CP&F has confirmed the <span class="info-weight">{lob}</span> demand data for allocation at <span class="info-weight">{comfirm_time}</span>, and the Demand Adjustment phase has ended.</div>
                <div class="section-info">Sincerely,</div>
                <div class="section-info"> Expert Technical Support</div>
                '''
        email_config = read_config_template(self.date, content)

        async_send_email(to, cc, subject, email_config, 'html')


    def sales_input_19(self, fiscal_qtr_week_name: str, rtm: str, system_link: str, lob: str = 'iPad'):
        '''
        周一17:30，周一下午的ESR未Ready，iPad Allocation任务无法开启，业务Offline提供ESR，研发手动跑完数据并开启任务入口后，提醒业务入口已打开
        '''
        # 通知全部人员
        to, _ = TblEmailRecipient.get_to_cc_list(0, None)
        cc = []
        
        # System Alert - *{LOB}* FAST Allocation Prep.& Submission System has been opened  *- {Fiscal Week}*
        subject = f"System Alert - {lob} FAST Allocation Prep.& Submission System has been opened - {fiscal_qtr_week_name}"
        content = f'''
                <div class="section-info">{Greetings.get('System', '')},</div>
                <div class="section-info">The system entry for <span class="info-weight">{lob}</span> Sales Input of FAST Allocation Pre. & Submission has been opened.</div>
                <div class="section-info">Please click the following button to enter the FAST Allocation System.</div>
                <div class="section-info">Sincerely,</div>
                <div class="section-info"> Expert Technical Support</div>
                <div class="enter-btn">
                    <a href=\"{system_link}\" class="btn">Enter</a>
                </div>
                '''
        email_config = read_config_template(self.date, content)

        async_send_email(to, cc, subject, email_config, 'html')


    def adjustment_20(self, fiscal_qtr_week_name: str, rtm: str, system_link: str, lob: str = 'iPad'):
        '''
        周三17:30，周一下午的ESR未Ready，Demand Adjustment任务无法开启，业务Offline给了之后，研发手动跑完数据并开启任务后，提醒业务入口已打开
        '''
        # 通知全部人员
        to, _ = TblEmailRecipient.get_to_cc_list(0, None)
        cc = []
        
        # System Alert - *{LOB}* Demand Adjustment of ** FAST Allocation Prep.& Submission has been opened  *- {Fiscal Week}*
        subject = f"System Alert - {lob} Demand Adjustment of FAST Allocation Prep.& Submission System has been opened - {fiscal_qtr_week_name}"
        content = f'''
                <div class="section-info">{Greetings.get('System', '')}, </div>
                <div class="section-info">The system entry for <span class="info-weight">{lob}</span> Demand Adjustment of FAST Allocation Pre. & Submission has been opened.</div>
                <div class="section-info">Please click the following button to enter the FAST Allocation System.</div>
                <div class="section-info">Sincerely,</div>
                <div class="section-info"> Expert Technical Support</div>
                <div class="enter-btn">
                    <a href=\"{system_link}\" class="btn">Enter</a>
                </div>
                '''
        email_config = read_config_template(self.date, content)

        async_send_email(to, cc, subject, email_config, 'html')


    def sales_input_21(self, fiscal_qtr_week_name: str, system_link: str, lob: str = 'iPad'):
            '''
            周一17:30，周一下午的ESR未Ready，iPad Allocation任务无法开启，业务Offline提供ESR，研发手动跑完数据并开启任务入口后，提醒CP&F业务入口已打开, 
            跳转至：CP&F FAST Allocation的列表页
            需要后端手动发送
            '''
            to, _ = TblEmailRecipient.get_to_cc_list(0, StrRTMCPF)
            cc = []
            
            # System Alert - *{LOB}* FAST Allocation System has been opened *- {Fiscal Week}*
            subject = f"System Alert - {lob} FAST Allocation System has been opened - {fiscal_qtr_week_name}"
            content = f'''
                    <div class="section-info">{Greetings.get('System', '')},</div>
                    <div class="section-info">The system entry for <span class="info-weight">{lob}</span> Sales Input of FAST Allocation has been opened.</div>
                    <div class="section-info">Please click the following button to enter the FAST Allocation System.</div>
                    <div class="section-info">Sincerely,</div>
                    <div class="section-info"> Expert Technical Support</div>
                    <div class="enter-btn">
                        <a href=\"{system_link}\" class="btn">Enter</a>
                    </div>
                    '''
            email_config = read_config_template(self.date, content)

            async_send_email(to, cc, subject, email_config, 'html')


    def sales_input_22(self, fiscal_qtr_week_name: str, rtm: str, system_link: str, lob: str = 'iPad'):
            '''
            周一17:30，周一下午的ESR未Ready，iPad Allocation任务无法开启，业务Offline提供ESR，研发手动跑完数据并开启任务入口后，提醒RTM业务入口已打开
            RTM FAST Allocation Pre. & Submission的列表页
            需要后端手动发送
            '''
            to, _ = TblEmailRecipient.get_to_cc_list(0, rtm)
            cc = []
            
            # System Alert - *{LOB}* FAST Allocation Prep.& Submission System has been opened *- {Fiscal Week}*
            subject = f"System Alert - {lob} FAST Allocation Prep.& Submission System has been opened - {fiscal_qtr_week_name}"
            content = f'''
                    <div class="section-info">{Greetings.get('System', '')},</div>
                    <div class="section-info">The system entry for <span class="info-weight">{lob}</span> Sales Input of FAST Allocation Pre. & Submission has been opened.</div>
                    <div class="section-info">Please click the following button to enter the FAST Allocation Prep.& Submission System.</div>
                    <div class="section-info">Sincerely,</div>
                    <div class="section-info"> Expert Technical Support</div>
                    <div class="enter-btn">
                        <a href=\"{system_link}\" class="btn">Enter</a>
                    </div>
                    '''
            email_config = read_config_template(self.date, content)

            async_send_email(to, cc, subject, email_config, 'html')


    def adjustment_23(self, fiscal_qtr_week_name: str, system_link: str, lob: str = 'iPad'):
            '''
            周三17:30，周一下午的ESR未Ready，Demand Adjustment任务无法开启，业务Offline给了之后，研发手动跑完数据并开启任务后，提醒CP&F业务入口已打开
            跳转至：CP&F FAST Allocation - Adjustment页面
            需要后端手动发送
            '''
            to, _ = TblEmailRecipient.get_to_cc_list(0, StrRTMCPF)
            cc = []
            
            # System Alert - *{LOB}* Adjustment of FAST Allocation has been opened *- {Fiscal Week}*
            subject = f"System Alert - {lob} Adjustment of FAST Allocation has been opened - {fiscal_qtr_week_name}"
            content = f'''
                    <div class="section-info">{Greetings.get('System', '')},</div>
                    <div class="section-info">The system entry for <span class="info-weight">{lob}</span> Adjustment of FAST Allocation has been opened.</div>
                    <div class="section-info">Please click the following button to enter the FAST Allocation System.</div>
                    <div class="section-info">Sincerely,</div>
                    <div class="section-info"> Expert Technical Support</div>
                    <div class="enter-btn">
                        <a href=\"{system_link}\" class="btn">Enter</a>
                    </div>
                    '''
            email_config = read_config_template(self.date, content)

            async_send_email(to, cc, subject, email_config, 'html')


    def adjustment_24(self, fiscal_qtr_week_name: str, rtm: str, system_link: str, lob: str = 'iPad'):
            '''
            周三17:30，周一下午的ESR未Ready，Demand Adjustment任务无法开启，业务Offline给了之后，研发手动跑完数据并开启任务后，提醒RTM业务入口已打开
            跳转至：RTM FAST Allocation Pre. & Submission - Demand Adjustment页面
            需要后端手动发送
            '''
            to, _ = TblEmailRecipient.get_to_cc_list(0, rtm)
            cc = []
            
            # System Alert - {LOB} Demand Adjustment of FAST Allocation Prep.& Submission has been opened - {Fiscal Week}
            subject = f"System Alert - {lob} Demand Adjustment of FAST Allocation Prep.& Submission has been opened - {fiscal_qtr_week_name}"
            content = f'''
                    <div class="section-info">{Greetings.get('System', '')},</div>
                    <div class="section-info">The system entry for <span class="info-weight">{lob}</span> Demand Adjustment of FAST Allocation Prep.& Submission has been opened.</div>
                    <div class="section-info">Please click the following button to enter the FAST Prep.& Submission Allocation System.</div>
                    <div class="section-info">Sincerely,</div>
                    <div class="section-info"> Expert Technical Support</div>
                    <div class="enter-btn">
                        <a href=\"{system_link}\" class="btn">Enter</a>
                    </div>
                    '''
            email_config = read_config_template(self.date, content)

            async_send_email(to, cc, subject, email_config, 'html')


    def sales_input_25(self, fiscal_qtr_week_name: str, lob: str = 'iPad'):
        '''
        周二09:15，周二上午的ESR未Ready，iPad Allocation无法重新刷新Template数据，提醒业务Offline提供
        '''
        # 从邮件邮件人配置中读取
        to, cc = TblEmailRecipient.get_to_cc_list(0, StrRTMCPF)
        
        subject = f"System Alert - FAST Allocation System Failed to Capture ESR - {fiscal_qtr_week_name}"
        content = f'''
                <div class="section-info">{Greetings.get('System', '')},</div>
                <div class="section-info">The FAST Allocation system failed to obtain the latest ESR report by 09:15, and this issue will block the <span class="info-weight">{lob}</span> allocation process. Please provide the latest ESR file to DMP Team ASAP, Thanks.</div>
                <div class="section-info">Sincerely,</div>
                <div class="section-info"> Expert Technical Support</div>
                '''
        email_config = read_config_template(self.date, content)

        async_send_email(to, cc, subject, email_config, 'html')


    def sales_input_26(self, fiscal_qtr_week_name: str, rtm: str, system_link: str, lob: str = 'iPad'):
        '''
        周二08:30ESR刷新后，有RTM的数据open_backlog比周一的小，需要邮件通知CPF和对应的RTM
        '''
        # 从邮件邮件人配置中读取
        to, _ = TblEmailRecipient.get_to_cc_list(0, rtm)
        cc = []
        
        subject = f"FAST System Alert - {lob} - Demand Data Error - {fiscal_qtr_week_name}"
        content = f'''
                <div class="section-info">{Greetings.get('System', '')},</div>
                <div class="section-info">FAST allocation system failed to generate Sales Input for allocation, since Open Backlog data fetched from ESR is invalid for Demand values are less than sales input values submitted. Please login on FAST Allocation System to process.</div>
                <div class="section-info">Sincerely,</div>
                <div class="section-info"> Expert Technical Support</div>
                <div class="enter-btn">
                    <a href=\"{system_link}\" class="btn">Enter</a>
                </div>
                '''
        email_config = read_config_template(self.date, content)

        async_send_email(to, cc, subject, email_config, 'html')
    
    
    def sales_input_27(self, fiscal_qtr_week_name: str, rtm: str, system_link: str, lob: str = 'iPad'):
        '''
        周一17:30，周一下午的ESR Ready，iPad Allocation任务开启之后，提醒RTM业务入口已打开
        '''
        # 从邮件邮件人配置中读取
        to, _ = TblEmailRecipient.get_to_cc_list(0, rtm)
        cc = []
        
        subject = f"FAST System Message - {lob} - FAST Allocation Prep.& Submission System has been opened - {fiscal_qtr_week_name}"
        content = f'''
                <div class="section-info">{Greetings.get('System', '')},</div>
                <div class="section-info">The system entry for {lob} Sales Input of FAST Allocation Pre. & Submission has been opened.</div>
                <div class="section-info">Please click the following button to enter the FAST Allocation Pre. & Submission System.</div>
                <div class="section-info">Sincerely,</div>
                <div class="section-info"> Expert Technical Support</div>
                <div class="enter-btn">
                    <a href=\"{system_link}\" class="btn">Enter</a>
                </div>
                '''
        email_config = read_config_template(self.date, content)

        async_send_email(to, cc, subject, email_config, 'html')
    
    
    def sales_input_28(self, fiscal_qtr_week_name: str, rtm: str, system_link: str, lob: str = 'iPad'):
        '''
        周二08:30，周二上午的ESR 刷新任务完毕，提醒RTM业务ESR已经刷新完毕
        '''
        # 从邮件邮件人配置中读取
        to, _ = TblEmailRecipient.get_to_cc_list(0, rtm)
        cc = []
        # FAST System Message - *{LOB}* FAST Allocation Prep.& Submission System Finished to Capture ESR *- {Fiscal Week}*
        subject = f"FAST System Message - {lob} - FAST Allocation Prep.& Submission System Finished to Capture ESR - {fiscal_qtr_week_name}"
        content = f'''
                <div class="section-info">{Greetings.get('System', '')},</div>
                <div class="section-info">The latest ESR report from the edition of Tuesday has been refreshed.</div>
                <div class="section-info">Please click the following button to enter the FAST Allocation Pre. & Submission System.</div>
                <div class="section-info">Sincerely,</div>
                <div class="section-info"> Expert Technical Support</div>
                <div class="enter-btn">
                    <a href=\"{system_link}\" class="btn">Enter</a>
                </div>
                '''
        email_config = read_config_template(self.date, content)

        async_send_email(to, cc, subject, email_config, 'html')
    
    
    def sales_input_29(self, fiscal_qtr_week_name: str, rtm: str, system_link: str, lob: str = 'iPad'):
        '''
        周一17:30，周一下午的ESR Ready，iPad Allocation任务开启之后，提醒CP&F业务入口已打开
        '''
        # 从邮件邮件人配置中读取
        to, _ = TblEmailRecipient.get_to_cc_list(0, rtm)
        cc = []
        # FAST System Message - *{LOB}* FAST iPad Allocation System has been opened  *- {Fiscal Week}*
        subject = f"FAST System Message - {lob} - FAST iPad Allocation System has been opened - {fiscal_qtr_week_name}"
        content = f'''
                <div class="section-info">{Greetings.get('System', '')},</div>
                <div class="section-info">The system entry for {lob} FAST Allocation System has been opened.</div>
                <div class="section-info">Please click the following button to enter the FAST iPad Allocation System.</div>
                <div class="section-info">Sincerely,</div>
                <div class="section-info"> Expert Technical Support</div>
                <div class="enter-btn">
                    <a href=\"{system_link}\" class="btn">Enter</a>
                </div>
                '''
        email_config = read_config_template(self.date, content)

        async_send_email(to, cc, subject, email_config, 'html')


    def sales_input_30(self, fiscal_qtr_week_name: str, rtm: str, system_link: str, lob: str = 'iPad'):
        '''
        周二08:30，周二上午的ESR 刷新任务完毕，提醒CP&F业务ESR已经刷新完毕
        '''
        # 从邮件邮件人配置中读取
        to, _ = TblEmailRecipient.get_to_cc_list(0, rtm)
        cc = []
        # FAST System Message - *{LOB}* FAST iPad Allocation System Finished to Capture ESR *- {Fiscal Week}*
        subject = f"FAST System Message - {lob} - FAST iPad Allocation System Finished to Capture ESR - {fiscal_qtr_week_name}"
        content = f'''
                <div class="section-info">{Greetings.get('System', '')},</div>
                <div class="section-info">The latest ESR report from the edition of Tuesday has been refreshed.</div>
                <div class="section-info">Please click the following button to enter the FAST iPad Allocation System.</div>
                <div class="section-info">Sincerely,</div>
                <div class="section-info"> Expert Technical Support</div>
                <div class="enter-btn">
                    <a href=\"{system_link}\" class="btn">Enter</a>
                </div>
                '''
        email_config = read_config_template(self.date, content)

        async_send_email(to, cc, subject, email_config, 'html')

    def final_demand_publish_notice(self, publish_time: str, sub_lob: str, rtm: str, system_link: str):
        """
        final_demand cpf 发布相关sub_lob后,邮件通知
        """
        # 从邮件邮件人配置中读取
        to, cc = TblEmailRecipient.get_to_cc_list(2, rtm)
        if to is None or len(to) == 0:
            return

        subject = f"Data Update Notification"
        content = f'''
                <div class="section-info">{Greetings.get('System', '')},</div>
                <div class="section-info">The CP&F Planning team user has just published the current week final demand result of {sub_lob} at {publish_time}, you can download data via FAST Forecast & Demand module. Thanks!</div>
                <div class="section-info"> FAST System</div>
                <div class="enter-btn">
                    <a href=\"{system_link}\" class="btn">Enter</a>
                </div>
                '''
        email_config = read_config_template(self.date, content)

        async_send_email(to, cc, subject, email_config, 'html')

    def final_demand_upload_notice(self, publish_time: str, sub_lob: str, rtm: str, system_link: str):
        """
        final_demand cpf 发布相关sub_lob后,邮件通知
        """
        # 从邮件邮件人配置中读取
        to, cc = TblEmailRecipient.get_to_cc_list(2, rtm)
        if to is None or len(to) == 0:
            return
        subject = f"Data Update Notification"
        content = f'''
                <div class="section-info">{Greetings.get('System', '')},</div>
                <div class="section-info">The CP&F Planning team user has just revoked the current week final demand result of {sub_lob} published at {publish_time}, please check the latest status shown on FAST Forecast & Demand module and wait for further data result feedback. Thanks!</div>
                <div class="section-info"> FAST System</div>
                <div class="enter-btn">
                    <a href=\"{system_link}\" class="btn">Enter</a>
                </div>
                '''
        email_config = read_config_template(self.date, content)

        async_send_email(to, cc, subject, email_config, 'html')
