from .conf import *
from .util import *

# readonly DB config
common_service_config = conf.get('database')
if os.environ.get('DB_ENV') == 'aws':
    common_service_host = common_service_config['aws_host']
    common_service_sec = get_secret_from_aws(common_service_config['secret_name'], common_service_config['secret_region'])
else :
    common_service_host = common_service_config['apple_host']
    common_service_sec = get_secret_from_apple(common_service_config['secret_name'], common_service_config['secret_region'], secret_req_host, secret_req_path)

common_service_user = common_service_sec['username']
common_service_pass = common_service_sec['password']

common_service_port = '3306'
common_service_name = conf['database']['default_db']

common_service_db = "mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8".format(common_service_user, urlquote(common_service_pass), common_service_host, common_service_port, common_service_name)

common_service_engine = create_engine(common_service_db, pool_pre_ping=True, pool_size=20, max_overflow=5, pool_recycle=3600)

CommonServiceBase = declarative_base()
CommonServiceSession = sessionmaker(bind=common_service_engine)
