import os
import math
import numpy as np
import pandas as pd
from hashlib import md5
from datetime import datetime, timedelta, date
from openpyxl.utils import get_column_letter
from pandas import ExcelWriter

from util.const import DateTimeFormat, DateFormat
from util.mail_conf import mail_config, navigate_config
from util.mail_sender import MailSender
from util.cpf_const import *


cpf_lobs = ['iPad']


def save_file(file, path, file_name):
    path = create_path(path)
    origin_file_path = f'{path}/{file_name}'
    file.save(origin_file_path)
    return origin_file_path, file_name

def save_file_and_create_file_cpf(file):
    path = create_path(f'/uploads/allocation')
    file_md5 = md5(file.stream.read()).hexdigest()
    file.stream.seek(0)
    origin_file_path = f'{path}/{file_md5}.xlsx'
    file.save(origin_file_path)
    final_file_path = f'{path}/{file_md5}_system.xlsx'
    return origin_file_path, final_file_path, f'{file_md5}_system.xlsx'


def cpf_merge_file_path(file_name):
    path = create_path(f'/uploads/allocation')
    file_path = f'{path}/{file_name}'
    return file_path


def sell_in_demand_file_path(file_name):
    path = create_path(f"{cpf_file_url_prefix['sell_in_demand']}")
    current_time = datetime.now().strftime(DateTimeFormat)
    file_path = f'{path}/{current_time}_{file_name}'
    return file_path


def sell_in_demand_file_path_customs(file_name, prefix):
    path = create_path(f"{cpf_file_url_prefix[prefix]}")
    current_time = datetime.now().strftime(DateTimeFormat)
    file_path = f'{path}/{current_time}_{file_name}'
    return file_path


def send_html_email(subject: str, content: str, receiver: str, cc: list):
    try:
        sender = MailSender(mail_config['smtp_host'], mail_config['smtp_port'], mail_config['login'],
                            mail_config['password'])
        sender.send_html_single(receiver, cc, subject, content)
        sender.close()
    except Exception as e:
        raise e


def create_path(service_url):
    path = os.path.dirname(os.path.dirname(os.path.abspath(__file__))) + service_url
    dir_name = os.path.dirname(path)
    if not os.path.exists(dir_name):
        os.makedirs(dir_name)
    if not os.path.exists(path):
        os.mkdir(path)
    return path


def send_file_email(subject: str, content: str, receivers: list, file_paths: list = None, file_name: str = None):
    try:
        sender = MailSender(mail_config['smtp_host'], mail_config['smtp_port'], mail_config['login'],
                            mail_config['password'])
        sender.send_file(receivers, subject, content, file_paths, file_name)
        sender.close()
    except Exception as e:
        raise e


def send_html_file_email(subject: str, content: str, receivers: list, file_paths: list = None, file_name: str = None,
                         html_content: str = ''):
    try:
        sender = MailSender(mail_config['smtp_host'], mail_config['smtp_port'], mail_config['login'],
                            mail_config['password'])
        sender.send_html_file(receivers, subject, content, file_paths, file_name, html_content)
        sender.close()
    except Exception as e:
        raise e


def get_mail_template_content(header_time: str, content: str) -> str:
    with open(os.path.dirname(os.path.dirname(os.path.abspath(__file__))) + "/conf/mail_template.html",
              'rb') as fb:
        mail_template = fb.read().decode()
    mail_template = mail_template.replace('&TIME&', header_time)
    mail_template = mail_template.replace('&CONTENT&', content)
    return mail_template


def get_mail_navigate_url(idx, rtm, fiscal_qtr_week_name, fiscal_week_year, phase) -> str:
    # 要将特殊的字符进行转义，空格换成加号(+)，+ --> %2B
    rtm = rtm.replace('/', '%2F').replace(' ', '%2B')
    return navigate_config['apple_connect'] + '?appIdKey=' + navigate_config['app_id_key'] + '&path=/%23' + \
           navigate_config['path'] + f'?id={idx}%26rtm={rtm}%26fiscal_qtr_week_name={fiscal_qtr_week_name}' \
                                     f'%26fiscal_week_year={fiscal_week_year}%26phase={phase}'

def get_cpf_mail_navigate_url(idx, fiscal_qtr_week_name, fiscal_week_year, lob) -> str:
    return navigate_config['apple_connect'] + '?appIdKey=' + navigate_config['app_id_key'] + '&path=/%23' + \
           '/cpf-allocation/detail' + f'?id={idx}%26fiscal_qtr_week_name={fiscal_qtr_week_name}' \
                                     f'%26fiscal_week_year={fiscal_week_year}%26lob={lob}'

def get_navigate_url_list_page(business_line) -> str:
    '''
    根据不同的业务线返回不同的list page url
    business_line: cpf, rtm
    '''
    list_page = ''
    if business_line == 'cpf':
        list_page = 'cpf-allocation'
    elif business_line == 'rtm':
        list_page = 'prep-allocation'
    return navigate_config['apple_connect'] + '?appIdKey=' + navigate_config['app_id_key'] + '&path=/%23' + list_page

def get_rtm_mail_fast_url(channel) -> str:
    #https://gcsales.expert-dev.apple.com/#/forecast-demand/rtms?channel=Mono
    return navigate_config['apple_connect'] + '?appIdKey=' + navigate_config['app_id_key'] + '&path=/%23' + \
           '/forecast-demand/rtms' + f'?channel={channel}'

def to_excel_auto_column_weight(df: pd.DataFrame, writer: ExcelWriter, sheet_name):
    # DataFrame保存为excel并自动设置列宽
    df.to_excel(writer, sheet_name=sheet_name, index=False)
    #  计算表头的字符宽度
    column_widths = (
        df.columns.to_series().apply(lambda x: len(x.encode('gbk'))).values
    )
    #  计算每列的最大字符宽度
    max_widths = (
        df.astype(str).applymap(lambda x: len(x.encode('gbk'))).agg(max).values
    )
    # 计算整体最大宽度
    widths = np.max([column_widths, max_widths], axis=0)
    if len(widths) > 0 and math.isnan(widths[0]):
        widths = column_widths
    # 设置列宽
    worksheet = writer.sheets[sheet_name]
    for i, width in enumerate(widths, 1):
        # openpyxl引擎设置字符宽度时会缩水0.5左右个字符，所以干脆+2使左右都空出一个字宽。
        worksheet.column_dimensions[get_column_letter(i)].width = width + 3


# /uploads/allocation/
def cpf_transfer_file_path(file_path: str, prefix):
    split_path = file_path.split(cpf_file_url_prefix['file_storage'])
    if len(split_path) > 1:
        absolute_path = create_path(cpf_file_url_prefix[prefix])
        return f"{absolute_path}{split_path[1]}"
    else:
        return ""

def get_list_page_url(page_name: str):
    return navigate_config['apple_connect'] + '?appIdKey=' + navigate_config['app_id_key'] + '&path=/%23' + page_name


def get_config_week_datetime(config_week: int = 0, config_time: str = '00:00:00') -> str:
    today = datetime.strptime(str(date.today()), DateFormat)
    one_day = timedelta(days=1)
    week = config_week - 1
    if today.weekday() < week:
        while today.weekday() != week:
            today += one_day
    else:
        while today.weekday() != week:
            today -= one_day
    return datetime.strftime(today, DateFormat) + ' ' + config_time


def is_after_config_week_time(config_week: int = 0, config_time: str = '00:00:00') -> bool:
    config_week_datetime = get_config_week_datetime(config_week, config_time)
    return datetime.strptime(config_week_datetime, DateTimeFormat) < datetime.now()
