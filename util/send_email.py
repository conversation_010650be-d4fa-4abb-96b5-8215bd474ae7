import ast
import traceback
from typing import Optional

from jinja2 import Template

from domain.email.entity.email_config import EmailConfig
from kit.email.sender import Sender
from kit.token_bucket import TokenBucket
from util.conf import logger
from util.const import EmailCmd, FAST_EMAIL_SUFFIX
from util.mail_conf import mail_config
from util.mail_sender import MailSender
from service.common import xthread
from data.email_config import EmailConfigRepository
from util.redis_pool import GetRedis
from util.template_email_sender import read_config_template
from celery_config import celery

def send_email(subject: str, content: str, file_paths: list=None):
    """按照配置文件发送邮件

    Args:
        subject (str): 主题
        content (str): 正文
        file_paths (list, optional): 附件的绝对路径, eg.["/tmp/file1.txt", "/tmp/file2.txt"], 默认为None.
    """
    # Send email_report from system mailbox to config receivers
    try:
        # receivers 去除掉所有的空格，以及首尾的逗号，保证邮箱的正确性，避免配置错误
        receivers = mail_config['receivers'].replace(' ','').strip(',').split(',')

        sender = MailSender(mail_config['smtp_host'], mail_config['smtp_port'], mail_config['login'], mail_config['password'])
        sender.send(receivers, subject, content, file_paths)
        sender.close()
    except Exception as e:
        raise (e)


def async_send_email(subject: str, content: str, file_paths: list = None):
    '''
    异步发送邮件
    '''
    t = xthread(send_email, [subject, content, file_paths])
    t.start()


def async_rate_limited_send_email(limit_key: str, capacity: int, expire_time: int,
                                  subject: str, content: str, file_paths: list = None):
    bucket = TokenBucket(limit_key, capacity, expire_time)
    if bucket.consume():
        t = xthread(send_email, [subject, content, file_paths])
        t.start()


def async_rate_limited_send_email_v2(limit_key: str, capacity: int, expire_time: int, email_config,
                                     file_paths: list = None, recipients: str = None):
    bucket = TokenBucket(limit_key, capacity, expire_time)
    if bucket.consume():
        t = xthread(send_email_by_datasource, [email_config, recipients, file_paths])
        t.start()

@celery.task(name='send_email_by_celery')
def send_email_by_database(cmd: str, file_name: str = None, recipients: str = None, params: dict = None):
    '''
       发送配置好的邮件
    '''
    logger.info(f'begin send_email. cmd: {cmd}, file_name: {file_name}, recipients: {recipients}, params: {params}.')
    email_config = EmailConfigRepository.query_email_config(cmd)
    url, email_config = del_email_config(email_config, file_name, params)
    send_email_by_datasource(email_config, recipients, url)


def async_send_email_by_database(cmd: str, file_name: str = None, recipients: str = None, params: dict = None):
    send_email_by_database.delay(cmd, file_name, recipients, params)


def del_email_config(email_config, file_name: str = None, params: dict = None, read_template: bool = True):
    # 将email_config.content按照将email_config.params替换对应的params参数
    url = []
    if params:
        email_config.content = email_config.content.format(**params)
        if read_template:
            email_config.content = read_config_template('', email_config.content)

        email_config.subject = email_config.subject.format(**params)
        if email_config.attachments:
            # eg:{{
            #       'path':'/file/download/91bb6c6812e1c04d009c72aac6d1889c',
            #       'destination_file_format':'Supply Data-[{lob}]-[{fiscal_qtr_week_name}] - [{time}]'
            #    }}
            email_config.attachments = email_config.attachments.format(**params)
            email_attachments_dict = ast.literal_eval(email_config.attachments)
            url.append(f"{email_attachments_dict.get('path')}/{email_attachments_dict.get('destination_file_format')}")
    if file_name:
        url = []
        url.append(file_name)
    return url, email_config


def send_email_by_datasource(email_config, recipients: str = None, file_paths: list = None):
    try:
        sender = MailSender(mail_config['smtp_host'], mail_config['smtp_port'], mail_config['login'],
                            mail_config['password'])
        to_receivers = []
        cc_receivers = []
        bcc_receivers = []
        if recipients:
            to_receivers = recipients.replace(' ', '').strip(',').split(',')
        if email_config.recipients:
            to_receivers.extend(email_config.recipients.replace(' ', '').strip(',').split(','))
        if email_config.cc:
            cc_receivers = email_config.cc.replace(' ', '').strip(',').split(',')
        if email_config.bcc:
            bcc_receivers = email_config.bcc.replace(' ', '').strip(',').split(',')
        sender.sender(to_receivers, cc_receivers, email_config.subject, email_config.content, email_config.content_type,
                      file_paths, None, None, bcc_reveivers=bcc_receivers)
        sender.close()
    except Exception as e:
        params = {"error": f'recipient: {recipients}, email_config: {email_config.__dict__}. error: {traceback.format_exc()}'}
        send_email_by_database(EmailCmd.WarningEmail, file_name='', recipients=None, params=params)
        raise e


def set_table_content_email_config(email_config, content_data: list, table_title: str,
                                   email_content_columns: list = None, file_name: str = None, params: dict = None):
    """
    动态渲染表格类型的数据
    """
    # 将email_config.content按照将email_config.params替换对应的params参数
    url = []
    if params:
        email_config.subject = email_config.subject.format(**params)
        if email_config.attachments:
            # eg:{{
            #       'path':'/file/download/91bb6c6812e1c04d009c72aac6d1889c',
            #       'destination_file_format':'Supply Data-[{lob}]-[{fiscal_qtr_week_name}] - [{time}]'
            #    }}
            email_config.attachments = email_config.attachments.format(**params)
            email_attachments_dict = ast.literal_eval(email_config.attachments)
            url.append(f"{email_attachments_dict.get('path')}/{email_attachments_dict.get('destination_file_format')}")
    if file_name:
        url = []
        url.append(file_name)

    def dynamic_render() -> str:
        if email_content_columns:
            columns = email_content_columns
        else:
            columns = sorted(list(content_data[0].keys())) if content_data else []
        template = Template(email_config.content)
        html_content = template.render(data=content_data, columns=columns, title=table_title)
        return html_content

    email_config.content = dynamic_render()
    return url, email_config


def get_email_config(cmd: str):
    return EmailConfigRepository.get_email_config(cmd)


def send_email_limit_frequency(email_config: EmailConfig, recipients: Optional[str] = None, file_paths: Optional[list] = None):
    try:
        # 发送邮件之前先check下配置的频率
        if email_config.is_limit_frequency():
            if email_config.limit_frequency().consume():
                send_email_by_datasource(email_config, recipients, file_paths)
            else:
                logger.info(f'Fast-lite-server-po-aging {email_config} 邮件发送频率已达上限！！！')
        else:
            send_email_by_datasource(email_config, recipients, file_paths)
    except Exception as e:
        logger.exception(e)
        raise e


def delete_email_duplication_key(cmd: str, subject_date: str):
    r = GetRedis()
    if cmd:
        sender = Sender(cmd, subject_date=subject_date)
        return sender.delete_duplication()
    else:
        key_list = r.keys(f'{FAST_EMAIL_SUFFIX}*')
        if len(key_list) != 0:
            return r.delete(*key_list)
