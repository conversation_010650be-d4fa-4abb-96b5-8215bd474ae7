from util.conf import *
from urllib.parse import quote_plus as urlquote
from sqlalchemy import or_, and_, func, desc, asc, union_all, literal_column, text


gc_dmp_fast_write_config = conf['gc_dmp_fast_write']

if os.environ.get('DB_ENV') == 'aws':
    host = gc_dmp_fast_write_config['aws_host']
    secret = get_secret_from_aws(gc_dmp_fast_write_config['secret_name'], 
                                 gc_dmp_fast_write_config['secret_region'])
    port = gc_dmp_fast_write_config['aws_port']
else :
    host = gc_dmp_fast_write_config['apple_host']
    secret = get_secret_from_apple(gc_dmp_fast_write_config['secret_name'], 
                                   gc_dmp_fast_write_config['secret_region'], 
                                   secret_req_host, secret_req_path)
    port = gc_dmp_fast_write_config['apple_port']
    
username = secret['username']
password = secret['password']

database = gc_dmp_fast_write_config['default_db']

db_url = "mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8".format(username, urlquote(password), host, port, database)

GcDmpFastWriteEngine = create_engine(db_url, pool_pre_ping=True, pool_size=20, max_overflow=5, pool_recycle=3600)

GcDmpFastWriteBase = declarative_base()
GcDmpFastWriteSession = sessionmaker(bind=GcDmpFastWriteEngine)
