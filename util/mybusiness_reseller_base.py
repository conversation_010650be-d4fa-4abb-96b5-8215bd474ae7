from .conf import *
from .util import *


mybusiness_secure_config = conf.get('mybusiness_secure')
if os.environ.get('DB_ENV') == 'aws':
    mybusiness_secure_host = mybusiness_secure_config['aws_host']
    mybusiness_secure_sec = get_secret_from_aws(mybusiness_secure_config['secret_name'], mybusiness_secure_config['secret_region'])
else :
    mybusiness_secure_host = mybusiness_secure_config['apple_host']
    mybusiness_secure_sec = get_secret_from_apple(mybusiness_secure_config['secret_name'], mybusiness_secure_config['secret_region'], secret_req_host, secret_req_path)

mybusiness_secure_user = mybusiness_secure_sec['username']
mybusiness_secure_pass = mybusiness_secure_sec['password']

mybusiness_secure_port = '3306'
mybusiness_secure_db_name = mybusiness_secure_config['default_db']

mybusiness_secure_db = "mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8".format(mybusiness_secure_user, urlquote(mybusiness_secure_pass), mybusiness_secure_host, mybusiness_secure_port, mybusiness_secure_db_name)

mybusiness_secure_engine = create_engine(mybusiness_secure_db, pool_pre_ping=True, pool_size=20, max_overflow=5, pool_recycle=3600)

MybusinessSecureBase = declarative_base()
MybusinessSecureSession = sessionmaker(bind=mybusiness_secure_engine)
