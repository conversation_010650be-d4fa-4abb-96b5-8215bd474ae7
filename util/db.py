from .conf import *
from .util import *

# readonly DB config
read_config = conf.get('read_db')
if os.environ.get('DB_ENV') == 'aws':
    read_host = read_config['aws_host']
    read_sec = get_secret_from_aws(read_config['secret_name'], read_config['secret_region'])
else :
    read_host = read_config['apple_host']
    read_sec = get_secret_from_apple(read_config['secret_name'], read_config['secret_region'], secret_req_host, secret_req_path)

read_user = read_sec['username']
read_pass = read_sec['password']

read_port = '3306'
read_name = conf['database']['default_db']

read_db = "mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8".format(read_user, urlquote(read_pass), read_host, read_port, read_name)

read_engine = create_engine(read_db, pool_pre_ping=True, pool_size=20, max_overflow=5, pool_recycle=3600)

ReadBase = declarative_base()
ReadSession = sessionmaker(bind=read_engine)
