import logging
import os
import yaml
from datetime import datetime, timedelta
from urllib.parse import quote_plus as urlquote

from logging.config import dictConfig
from flask_caching import Cache
from sqlalchemy import Column, Integer, String, Numeric, Float, DateTime, Text, SmallInteger, func, FLOAT, Date, distinct, TIMESTAMP
from sqlalchemy.orm import session, sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy import create_engine
from sqlalchemy.exc import IntegrityError

from util.secrets_manager import * 


def load_conf(path='conf/config.yaml'):
    try:
        with open(path, 'r') as f:
            conf = yaml.safe_load(f)
        return conf
    except Exception as e:
        raise Exception("load conf error")


dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if os.environ.get('ENV') == 'dev':
    conf_yaml = dir + '/conf/dev_config.yaml'
else:
    conf_yaml = dir + '/conf/config.yaml'
conf = load_conf(conf_yaml)

# log config
if not os.path.exists(dir + '/logs'):
    os.mkdir(dir + '/logs')
logging.config.fileConfig(dir + "/conf/logging.conf")
logger = logging.getLogger("gunicorn")
task_logger = logging.getLogger('icronjob')

# DB config
secret_req_host = conf['secret_host']
secret_req_path = conf['secret_path']
if os.environ.get('DB_ENV') == 'aws':
    db_host = conf['database']['aws_host']
    db_sec = get_secret_from_aws(conf['database']['secret_name'], conf['database']['secret_region'])
else: 
    db_host = conf['database']['apple_host']
    db_sec = get_secret_from_apple(conf['database']['secret_name'], conf['database']['secret_region'], secret_req_host, secret_req_path)

db_user = db_sec['username']
db_pass = db_sec['password']

db_port = '3306'
db_name = conf['database']['default_db']

db = "mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8".format(db_user, urlquote(db_pass), db_host, db_port, db_name)

engine = create_engine(db, pool_pre_ping=True, pool_size=20, max_overflow=5, pool_recycle=3600)

Base = declarative_base()
Session = sessionmaker(bind=engine)

# cache
cache_type = conf['cache']['type']
cache_dir = dir + conf['cache']['dir']
cache_threshold = conf['cache']['threshold']
cache_default_timeout = conf['cache']['default_timeout']

if cache_dir != dir and not os.path.exists(cache_dir):
    os.mkdir(cache_dir)
cache_config = {
    "CACHE_TYPE": cache_type,
    "CACHE_DIR": cache_dir,
    "CACHE_THRESHOLD": cache_threshold,
    "CACHE_DEFAULT_TIMEOUT": cache_default_timeout
}
cache = Cache(config=cache_config)
