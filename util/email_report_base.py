from .conf import *
from .util import *


email_report_config = conf.get('email_report')
if os.environ.get('DB_ENV') == 'aws':
    email_report_host = email_report_config['aws_host']
    email_report_sec = get_secret_from_aws(email_report_config['secret_name'], email_report_config['secret_region'])
else :
    email_report_host = email_report_config['apple_host']
    email_report_sec = get_secret_from_apple(email_report_config['secret_name'], email_report_config['secret_region'], secret_req_host, secret_req_path)

email_report_user = email_report_sec['username']
email_report_pass = email_report_sec['password']

email_report_port = '3306'
email_report_name = email_report_config['default_db']

email_report_db = "mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8".format(email_report_user, urlquote(email_report_pass), email_report_host, email_report_port, email_report_name)

email_report_engine = create_engine(email_report_db, pool_pre_ping=True, pool_size=20, max_overflow=5, pool_recycle=3600)

EmailReportBase = declarative_base()
EmailReportSession = sessionmaker(bind=email_report_engine)
