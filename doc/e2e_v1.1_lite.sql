ALTER TABLE fast_lite.range_level_setting MODIFY COLUMN range_level_cw5 double DEFAULT 0.9 NOT NULL;
ALTER TABLE fast_lite.range_level_setting MODIFY COLUMN range_level_cw6 double DEFAULT 0.9 NOT NULL;


DROP VIEW fast_lite.forecast_quantile_variance_view;
create VIEW `fast_lite`.`forecast_quantile_variance_view` AS
select
    `d`.`id` AS `id`,
    `d`.`fiscal_week` AS `fiscal_week`,
    `d`.`region` AS `region`,
    `d`.`rtm` AS `rtm`,
    `d`.`sub_rtm` AS `sub_rtm`,
    `d`.`sold_to_id` AS `sold_to_id`,
    `d`.`sold_to_name` AS `sold_to_name`,
    `d`.`lob` AS `lob`,
    `d`.`sub_lob` AS `sub_lob`,
    `d`.`nand` AS `nand`,
    `d`.`color` AS `color`,
    `d`.`mpn` AS `mpn`,
    ifnull(`v`.`range_level_cw`, 0.9) AS `range_level_cw`,
    ifnull(`v`.`range_level_cw1`, 0.9) AS `range_level_cw1`,
    ifnull(`v`.`range_level_cw2`, 0.9) AS `range_level_cw2`,
    ifnull(`v`.`range_level_cw3`, 0.9) AS `range_level_cw3`,
    ifnull(`v`.`range_level_cw4`, 0.9) AS `range_level_cw4`,
    ifnull(`v`.`range_level_cw5`, 0.9) AS `range_level_cw5`,
    ifnull(`v`.`range_level_cw6`, 0.9) AS `range_level_cw6`,
    ifnull(`v`.`range_level_cw7`, 0.15) AS `range_level_cw7`,
    ifnull(`v`.`range_level_cw8`, 0.15) AS `range_level_cw8`,
    ifnull(`v`.`range_level_cw9`, 0.15) AS `range_level_cw9`,
    ifnull(`v`.`range_level_cw10`, 0.15) AS `range_level_cw10`,
    ifnull(`v`.`range_level_cw11`, 0.15) AS `range_level_cw11`,
    ifnull(`v`.`range_level_cw12`, 0.15) AS `range_level_cw12`,
    1 AS `variance_is_published`,
    `v`.`variance_publish_time` AS `variance_publish_time`,
    1 AS `quantile_is_published`,
    `v`.`quantile_publish_time` AS `quantile_publish_time`
from
    `fast_lite`.`demand_by_soldto_pool` `d`
left join `fast_lite`.`range_level_setting` `v`
on `d`.`fiscal_week` = `v`.`fiscal_week_name`
and `d`.`rtm` = `v`.`rtm`
and `d`.`sub_rtm` = `v`.`sub_rtm`
and `d`.`lob` = `v`.`lob`
and `d`.`sub_lob` = `v`.`sub_lob`;

ALTER TABLE gc_dmp_datasource.ods_fast_cpf_twos_iphone ADD mpn varchar(64) NULL;
ALTER TABLE gc_dmp_datasource.ods_fast_cpf_twos_iphone CHANGE mpn mpn varchar(64) NULL AFTER model;
ALTER TABLE gc_dmp_datasource.ods_fast_cpf_twos_iphone ADD sold_to_id varchar(64) NULL;
ALTER TABLE gc_dmp_datasource.ods_fast_cpf_twos_iphone CHANGE sold_to_id sold_to_id varchar(64) NULL AFTER business_type;
ALTER TABLE gc_dmp_datasource.ods_fast_cpf_twos_iphone ADD sold_to_name varchar(128) NULL;
ALTER TABLE gc_dmp_datasource.ods_fast_cpf_twos_iphone CHANGE sold_to_name sold_to_name varchar(128) NULL AFTER sold_to_id;


-- 20250522 新增 原表中的publish_status/publish_time均为demand1.0的状态
ALTER TABLE mybusiness.feedback_demand_by_soldto
ADD COLUMN demand3_publish_status INT NULL 
  COMMENT '由CP&F confirm的Demand 1.0， by sub_lob粒度' 
  AFTER publish_time,
ADD COLUMN demand3_publish_time DATETIME NULL 
  COMMENT '由CP&F confirm的Demand 1.0， by sub_lob粒度' 
  AFTER demand3_publish_status
;

CREATE VIEW `demand_by_region_pool_view` AS
select `dbrp`.`id` AS `id`
    ,`dbrp`.`fiscal_week` AS `fiscal_week`
    ,`dbrp`.`region` AS `region`
    ,`dbrp`.`lob` AS `lob`
    ,`dbrp`.`sub_lob` AS `sub_lob`
    ,`dbrp`.`nand` AS `nand`
    ,`dbrp`.`color` AS `color`
    ,`dbrp`.`mpn` AS `mpn`
    ,`dbrp`.`hr_lr` AS `hr_lr`
    ,`dbrp`.`shipment_plan_cw` AS `shipment_plan_cw`
    ,`dbrp`.`shipment_plan_cw1` AS `shipment_plan_cw1`
    ,`dbrp`.`shipment_plan_cw2` AS `shipment_plan_cw2`
    ,`dbrp`.`avg_dfa_fcst_2_4` AS `avg_dfa_fcst_2_4`
    ,`dbrp`.`avg_dfa_fcst_3_5` AS `avg_dfa_fcst_3_5`
    ,`dbrp`.`ub_eoh` AS `ub_eoh`
    ,`dbrp`.`forecast_cw_ml` AS `forecast_cw`
    ,`dbrp`.`forecast_cw1_ml` AS `forecast_cw1`
    ,`dbrp`.`forecast_cw2_dfa` AS `forecast_cw2_dfa`
    ,`dbrp`.`forecast_cw3_dfa` AS `forecast_cw3_dfa`
    ,`dbrp`.`forecast_cw4_dfa` AS `forecast_cw4_dfa`
    ,`dbrp`.`forecast_cw5_dfa` AS `forecast_cw5_dfa`
    ,`dbrp`.`forecast_cw6_dfa` AS `forecast_cw6_dfa`
    ,`dbrp`.`forecast_cw7_dfa` AS `forecast_cw7_dfa`
    ,`dbrp`.`forecast_cw8_dfa` AS `forecast_cw8_dfa`
    ,`dbrp`.`cw1_x` AS `cw1_x`
    ,`dbrp`.`cw2_x` AS `cw2_x`
    ,`dbrp`.`twos` AS `twos`
    ,`dbrp`.`base` AS `base`
    ,`dbrp`.`cw1_ideal_demand` AS `cw1_ideal_demand`
    ,`dbrp`.`cw2_ideal_demand` AS `cw2_ideal_demand`
    ,`dbrp`.`dt_cw1` AS `dt_cw1`
    ,`dbrp`.`dt_cw1_origin` AS `dt_cw1_origin`
    ,`dbrp`.`dt_cw2` AS `dt_cw2`
    ,`dbrp`.`dt_cw2_origin` AS `dt_cw2_origin`
    ,`dbrp`.`ub_week` AS `ub_week`
    ,`dbrp`.`ub_1_5` AS `ub_1_5`
    ,`dbrp`.`avg_ub_1_5` AS `avg_ub_1_5`
    ,`dbrp`.`dn_cw1` AS `dn_cw1`
    ,`dbrp`.`dn_cw2` AS `dn_cw2`
    ,`dbrp`.`woi_by_mpn_min` AS `woi_by_mpn_min`
    ,`dbrp`.`woi_by_mpn_max` AS `woi_by_mpn_max`
    ,`dbrp`.`woi_by_sublob_max` AS `woi_by_sublob_max`
    ,`dbrp`.`woi_by_mpn_min_cw2` AS `woi_by_mpn_min_cw2`
    ,`dbrp`.`woi_by_mpn_max_cw2` AS `woi_by_mpn_max_cw2`
    ,`dbrp`.`woi_by_sublob_max_cw2` AS `woi_by_sublob_max_cw2`
    ,`dbrp`.`woi_by_mpn_min_cw3` AS `woi_by_mpn_min_cw3`
    ,`dbrp`.`woi_by_mpn_max_cw3` AS `woi_by_mpn_max_cw3`
    ,`dbrp`.`woi_by_sublob_max_cw3` AS `woi_by_sublob_max_cw3`
    ,`dbrp`.`ds_cw1` AS `ds_cw1`
    ,`dbrp`.`ds_cw2` AS `ds_cw2`
    ,`dbrp`.`ds_cw1_woi` AS `ds_cw1_woi`
    ,`dbrp`.`ds_cw2_woi` AS `ds_cw2_woi`
    ,`dbrp`.`final_dn_cw1` AS `final_dn_cw1`
    ,`dbrp`.`final_dn_cw2` AS `final_dn_cw2`
    ,`dbrp`.`df_cw1` AS `df_cw1`
    ,`dbrp`.`df_cw2` AS `df_cw2`
    ,`dbrp`.`create_time` AS `create_time`
    ,`dbrp`.`update_time` AS `update_time`
    ,`dbrp`.`df_cw1_adjusted` AS `df_cw1_adjusted`
    ,`dbrp`.`df_cw2_adjusted` AS `df_cw2_adjusted`
    ,`dbrp`.`base_demand_cw1` AS `base_demand_cw1`
    ,`dbrp`.`base_demand_cw2` AS `base_demand_cw2`
    ,`tdpr`.`publish_time` AS `publish_time`
    ,if((`tdpr`.`publish_time` is null), 0, 1) AS `publish_status`
from fast_lite.`demand_by_region_pool` as `dbrp`
	left join fast_lite.`tbl_demand_publish_record` as `tdpr`
			  on `dbrp`.`fiscal_week` = `tdpr`.`fiscal_week`
				 and `dbrp`.`sub_lob` = `tdpr`.`sub_lob`
;

-- Online 增加JD四家真实sold-to的权限配置
INSERT INTO Users.`tbl_role_info` (`role_name`, `platform`, `frontend_show`, `sort`, `role_level`, `desc`, `deleted`, `create_person`, `update_person`, `create_date`, `update_date`, `role_type`, `parent_platform`, `cloud_platform`)
VALUES
	('3655708', 'FAST Data Permission Online', '厦门宝通', 2, 1, NULL, 1, NULL, '2320471068', '2025-05-23 17:10:10', '2025-05-23 17:10:10', NULL, NULL, 0),
	('3439713', 'FAST Data Permission Online', '海南恒沙', 2, 1, NULL, 1, NULL, '2320471068', '2025-05-23 17:10:10', '2025-05-23 17:10:10', NULL, NULL, 0),
	('2086774', 'FAST Data Permission Online', '深圳天联', 2, 1, NULL, 1, NULL, '2320471068', '2025-05-23 17:10:10', '2025-05-23 17:10:10', NULL, NULL, 0),
	('1587282', 'FAST Data Permission Online', '深圳神码', 2, 1, NULL, 1, NULL, '2320471068', '2025-05-23 17:10:10', '2025-05-23 17:10:10', NULL, NULL, 0);

-- po 邮件增加预发送
INSERT INTO fast_lite.`email_config` (`cmd`, `content_type`, `subject`, `template`, `content`, `params`, `attachments`, `recipients`, `cc`, `bcc`, `is_base_template`, `frequency`, `biz`, `from_email`)
VALUES
	('pre_po_gap_email_gm', 'html', 'iPhone PO Gap', 'email_po_gap_template', '', '', '', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '', 0, '', 'PO dashboard', ''),
	('pre_po_delinquent_email_gm', 'html', 'iPhone PO Delinquent', 'email_po_delinquent_template', '', '', '', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '', 0, '', 'PO dashboard', ''),
	('pre_po_gap_email_planning_team', 'html', 'iPhone PO Gap', 'email_po_gap_template', '', '', '', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '', 0, '', 'PO dashboard', ''),
	('pre_po_delinquent_email_planning_team', 'html', 'iPhone PO Delinquent', 'email_po_delinquent_template', '', '', '', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '', 0, '', 'PO dashboard', ''),
	('pre_Multi_po_gap_rtm_email_planning_team', 'html', 'Multi iPhone PO Gap', 'email_po_gap_rtm_template', '', '', '', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '', 0, '', 'PO Gap dashboard RTM 版本', ''),
	('pre_Multi_po_delinquent_rtm_email_planning_team', 'html', 'Multi iPhone PO Delinquent', 'email_po_delinquent_rtm_template', '', '', '', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '', 0, '', 'PO Delinquent dashboard RTM 版本', ''),
	('pre_Multi_po_gap_rtm_email_gm', 'html', 'Multi iPhone PO Gap', 'email_po_gap_rtm_template', '', '', '', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '', 0, '', 'PO Gap dashboard RTM 版本', ''),
	('pre_Multi_po_delinquent_rtm_email_gm', 'html', 'Multi iPhone PO Delinquent', 'email_po_delinquent_rtm_template', '', '', '', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '', 0, '', 'PO Delinquent dashboard RTM 版本', ''),
	('pre_Mono_po_gap_rtm_email_planning_team', 'html', 'Mono iPhone PO Gap', 'email_po_gap_rtm_template', '', '', '', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '', 0, '', 'PO Gap dashboard RTM 版本', ''),
	('pre_Mono_po_delinquent_rtm_email_planning_team', 'html', 'Mono iPhone PO Delinquent', 'email_po_delinquent_rtm_template', '', '', '', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '', 0, '', 'PO Delinquent dashboard RTM 版本', ''),
	('pre_Mono_po_gap_rtm_email_gm', 'html', 'Mono iPhone PO Gap', 'email_po_gap_rtm_template', '', '', '', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '', 0, '', 'PO Gap dashboard RTM 版本', ''),
	('pre_Mono_po_delinquent_rtm_email_gm', 'html', 'Mono iPhone PO Delinquent', 'email_po_delinquent_rtm_template', '', '', '', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '', 0, '', 'PO Delinquent dashboard RTM 版本', ''),
	('pre_Online_po_gap_rtm_email_planning_team', 'html', 'Online iPhone PO Gap', 'email_po_gap_rtm_template', '', '', '', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '', 0, '', 'PO Gap dashboard RTM 版本', ''),
	('pre_Online_po_delinquent_rtm_email_planning_team', 'html', 'Online iPhone PO Delinquent', 'email_po_delinquent_rtm_template', '', '', '', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '', 0, '', 'PO Delinquent dashboard RTM 版本', ''),
	('pre_Online_po_gap_rtm_email_gm', 'html', 'Online iPhone PO Gap', 'email_po_gap_rtm_template', '', '', '', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '', 0, '', 'PO Gap dashboard RTM 版本', ''),
	('pre_Online_po_delinquent_rtm_email_gm', 'html', 'Online iPhone PO Delinquent', 'email_po_delinquent_rtm_template', '', '', '', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '', 0, '', 'PO Delinquent dashboard RTM 版本', ''),
	('pre_Carrier_po_gap_rtm_email_planning_team', 'html', 'Carrier iPhone PO Gap', 'email_po_gap_rtm_template', '', '', '', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '', 0, '', 'PO Gap dashboard RTM 版本', ''),
	('pre_Carrier_po_delinquent_rtm_email_planning_team', 'html', 'Carrier iPhone PO Delinquent', 'email_po_delinquent_rtm_template', '', '', '', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '', 0, '', 'PO Delinquent dashboard RTM 版本', ''),
	('pre_Carrier_po_gap_rtm_email_gm', 'html', 'Carrier iPhone PO Gap', 'email_po_gap_rtm_template', '', '', '', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '', 0, '', 'PO Gap dashboard RTM 版本', ''),
	('pre_Carrier_po_delinquent_rtm_email_gm', 'html', 'Carrier iPhone PO Delinquent', 'email_po_delinquent_rtm_template', '', '', '', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '', 0, '', 'PO Delinquent dashboard RTM 版本', ''),
	('pre_Education_po_gap_rtm_email_planning_team', 'html', 'Education iPhone PO Gap', 'email_po_gap_rtm_template', '', '', '', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '', 0, '', 'PO Gap dashboard RTM 版本', ''),
	('pre_Education_po_delinquent_rtm_email_planning_team', 'html', 'Education iPhone PO Delinquent', 'email_po_delinquent_rtm_template', '', '', '', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '', 0, '', 'PO Delinquent dashboard RTM 版本', ''),
	('pre_Education_po_gap_rtm_email_gm', 'html', 'Education iPhone PO Gap', 'email_po_gap_rtm_template', '', '', '', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '', 0, '', 'PO Gap dashboard RTM 版本', ''),
	('pre_Education_po_delinquent_rtm_email_gm', 'html', 'Education iPhone PO Delinquent', 'email_po_delinquent_rtm_template', '', '', '', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '', 0, '', 'PO Delinquent dashboard RTM 版本', ''),
	('pre_Enterprise_po_gap_rtm_email_planning_team', 'html', 'Enterprise iPhone PO Gap', 'email_po_gap_rtm_template', '', '', '', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '', 0, '', 'PO Gap dashboard RTM 版本', ''),
	('pre_Enterprise_po_delinquent_rtm_email_planning_team', 'html', 'Enterprise iPhone PO Delinquent', 'email_po_delinquent_rtm_template', '', '', '', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '', 0, '', 'PO Delinquent dashboard RTM 版本', ''),
	('pre_Enterprise_po_gap_rtm_email_gm', 'html', 'Enterprise iPhone PO Gap', 'email_po_gap_rtm_template', '', '', '', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '', 0, '', 'PO Gap dashboard RTM 版本', ''),
	('pre_Enterprise_po_delinquent_rtm_email_gm', 'html', 'Enterprise iPhone PO Delinquent', 'email_po_delinquent_rtm_template', '', '', '', '<EMAIL>', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>', '', 0, '', 'PO Delinquent dashboard RTM 版本', '');

CREATE OR REPLACE
ALGORITHM = UNDEFINED VIEW `forecast_quantile_variance_view` AS
select
	`d`.`id` AS `id`
	, `d`.`fiscal_week` AS `fiscal_week`
	, `d`.`region` AS `region`
	, `d`.`rtm` AS `rtm`
	, `d`.`sub_rtm` AS `sub_rtm`
	, `d`.`sold_to_id` AS `sold_to_id`
	, `d`.`sold_to_name` AS `sold_to_name`
	, `d`.`lob` AS `lob`
	, `d`.`sub_lob` AS `sub_lob`
	, `d`.`nand` AS `nand`
	, `d`.`color` AS `color`
	, `d`.`mpn` AS `mpn`
	, ifnull(`v`.`range_level_cw`, 0.5) AS `range_level_cw`
	, ifnull(`v`.`range_level_cw1`, 0.5) AS `range_level_cw1`
	, ifnull(`v`.`range_level_cw2`, 0.5) AS `range_level_cw2`
	, ifnull(`v`.`range_level_cw3`, 0.5) AS `range_level_cw3`
	, ifnull(`v`.`range_level_cw4`, 0.5) AS `range_level_cw4`
	, ifnull(`v`.`range_level_cw5`, 0.5) AS `range_level_cw5`
	, ifnull(`v`.`range_level_cw6`, 0.5) AS `range_level_cw6`
	, ifnull(`v`.`range_level_cw7`, 0.15) AS `range_level_cw7`
	, ifnull(`v`.`range_level_cw8`, 0.15) AS `range_level_cw8`
	, ifnull(`v`.`range_level_cw9`, 0.15) AS `range_level_cw9`
	, ifnull(`v`.`range_level_cw10`, 0.15) AS `range_level_cw10`
	, ifnull(`v`.`range_level_cw11`, 0.15) AS `range_level_cw11`
	, ifnull(`v`.`range_level_cw12`, 0.15) AS `range_level_cw12`
	, 1 AS `variance_is_published`
	, `v`.`variance_publish_time` AS `variance_publish_time`
	, 1 AS `quantile_is_published`
	, `v`.`quantile_publish_time` AS `quantile_publish_time`
from
	(
		`demand_by_soldto_pool` `d`
	left join `range_level_setting` `v` on
		(
			(
				(
					`d`.`fiscal_week` = `v`.`fiscal_week_name`
				)
					and (
						`d`.`rtm` = `v`.`rtm`
					)
						and (
							`d`.`sub_rtm` = `v`.`sub_rtm`
						)
							and (
								`d`.`lob` = `v`.`lob`
							)
								and (
									`d`.`sub_lob` = `v`.`sub_lob`
								)
			)
		)
	);
