-- fast_lite.demand_by_region_pool definition

CREATE TABLE `demand_by_region_pool` (
  `id` int NOT NULL AUTO_INCREMENT,
  `fiscal_week` varchar(16) DEFAULT NULL,
  `region` varchar(32) DEFAULT NULL,
  `lob` varchar(16) DEFAULT NULL,
  `sub_lob` varchar(64) DEFAULT NULL,
  `nand` varchar(16) DEFAULT NULL,
  `color` varchar(128) DEFAULT NULL,
  `mpn` varchar(16) DEFAULT NULL,
  `hr_lr` varchar(32) DEFAULT NULL,
  `shipment_plan_cw` int DEFAULT NULL,
  `shipment_plan_cw1` int DEFAULT NULL,
  `shipment_plan_cw2` int DEFAULT NULL,
  `avg_dfa_fcst_2_4` float DEFAULT NULL,
  `avg_dfa_fcst_3_5` float DEFAULT NULL,
  `ub_eoh` int DEFAULT NULL,
  `forecast_cw_ml` int DEFAULT NULL,
  `forecast_cw1_ml` int DEFAULT NULL,
  `forecast_cw2_dfa` int DEFAULT NULL,
  `forecast_cw3_dfa` int DEFAULT NULL,
  `forecast_cw4_dfa` int DEFAULT NULL,
  `forecast_cw5_dfa` int DEFAULT NULL,
  `forecast_cw6_dfa` int DEFAULT NULL,
  `forecast_cw7_dfa` int DEFAULT NULL,
  `forecast_cw8_dfa` int DEFAULT NULL,
  `cw1_x` float DEFAULT NULL,
  `cw2_x` float DEFAULT NULL,
  `twos` float DEFAULT NULL,
  `base` float DEFAULT NULL COMMENT 'CW_ML_FORECAST + CW1_ML_FORECAST - UB_EOH - CW_SHIPMENT_PLAN',
  `cw1_ideal_demand` float DEFAULT NULL,
  `cw2_ideal_demand` float DEFAULT NULL,
  `dt_cw1` float DEFAULT NULL,
  `dt_cw2` float DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `demand_by_region_pool_data_UN` (`fiscal_week`,`region`,`mpn`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- fast_lite.demand_by_soldto_pool definition

CREATE TABLE `demand_by_soldto_pool` (
  `id` int NOT NULL AUTO_INCREMENT,
  `fiscal_week` varchar(16) DEFAULT NULL,
  `region` varchar(32) DEFAULT NULL,
  `rtm` varchar(32) DEFAULT NULL,
  `sub_rtm` varchar(64) DEFAULT NULL,
  `sold_to_id` varchar(32) DEFAULT NULL,
  `sold_to_name` varchar(128) DEFAULT NULL,
  `lob` varchar(16) DEFAULT NULL,
  `sub_lob` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `nand` varchar(16) DEFAULT NULL,
  `color` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `mpn` varchar(16) DEFAULT NULL,
  `hr_lr` varchar(32) DEFAULT NULL,
  `shipment_plan_cw` int DEFAULT NULL,
  `shipment_plan_cw1` int DEFAULT NULL,
  `shipment_plan_cw2` int DEFAULT NULL,
  `avg_sales_fcst_2_4` float DEFAULT NULL,
  `avg_sales_fcst_3_5` float DEFAULT NULL,
  `ub_eoh` int DEFAULT NULL,
  `forecast_cw_ml` int DEFAULT NULL,
  `forecast_cw1_ml` int DEFAULT NULL,
  `forecast_cw_sales` int DEFAULT NULL,
  `forecast_cw1_sales` int DEFAULT NULL,
  `forecast_cw2_sales` int DEFAULT NULL,
  `forecast_cw3_sales` int DEFAULT NULL,
  `forecast_cw4_sales` int DEFAULT NULL,
  `forecast_cw5_sales` int DEFAULT NULL,
  `forecast_cw6_sales` int DEFAULT NULL,
  `forecast_cw7_sales` int DEFAULT NULL,
  `forecast_cw8_sales` int DEFAULT NULL,
  `cw1_x` float DEFAULT NULL,
  `cw2_x` float DEFAULT NULL,
  `twos` float DEFAULT NULL,
  `base` float DEFAULT NULL COMMENT 'CW_ML_FORECAST + CW1_ML_FORECAST - UB_EOH - CW_SHIPMENT_PLAN',
  `cw1_ideal_demand_origin` float DEFAULT NULL,
  `cw2_ideal_demand_origin` float DEFAULT NULL,
  `cw1_ideal_demand` float DEFAULT NULL,
  `cw2_ideal_demand` float DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `ideal_demand_calculate_data_UN` (`fiscal_week`,`sold_to_id`,`mpn`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- fast_lite.ideal_demand_x_value_setting definition

CREATE TABLE `ideal_demand_x_value_setting` (
  `id` int NOT NULL AUTO_INCREMENT,
  `fiscal_week` varchar(16) DEFAULT NULL,
  `hr_lr_type` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `lob` varchar(16) DEFAULT NULL,
  `region` varchar(32) DEFAULT NULL,
  `rtm` varchar(32) DEFAULT NULL,
  `sub_rtm` varchar(64) DEFAULT NULL,
  `sold_to_id` varchar(32) DEFAULT NULL,
  `sold_to_name` varchar(128) DEFAULT NULL,
  `cw1` float DEFAULT NULL,
  `twos` float DEFAULT NULL,
  `cw2` float DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `week_hrlrtype_region_lob_rtm_subrtm_uk` (`fiscal_week`,`hr_lr_type`,`region`,`lob`,`rtm`,`sub_rtm`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


CREATE TABLE `ideal_demand_y_value_setting` (
  `id` int NOT NULL AUTO_INCREMENT,
  `fiscal_week` varchar(16) DEFAULT NULL,
  `hr_lr_type` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'HR / LR  枚举类型',
  `lob` varchar(16) DEFAULT NULL,
  `region` varchar(32) DEFAULT NULL,
  `rtm` varchar(32) DEFAULT NULL,
  `sub_rtm` varchar(64) DEFAULT NULL,
  `sold_to_id` varchar(32) DEFAULT NULL,
  `sold_to_name` varchar(128) DEFAULT NULL,
  `cw1_min` float DEFAULT '0',
  `cw1_max` float DEFAULT '0',
  `cw2_min` float DEFAULT '0',
  `cw2_max` float DEFAULT '0',
  `twos` float NOT NULL DEFAULT '0',
  `is_published` tinyint(1) DEFAULT '0' COMMENT '0:draft 1:publish',
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `week_hrlrtype_region_lob_rtm_subrtm` (`fiscal_week`,`hr_lr_type`,`region`,`lob`,`rtm`,`sub_rtm`)
) ENGINE=InnoDB AUTO_INCREMENT=2021 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


CREATE TABLE `tbl_demand_state` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `fiscal_week` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `demand` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'ideal; topdown; normalized',
  `rtm_state` int DEFAULT '0' COMMENT '比特位 0001:mono,0010:multi,0100:Carrier,1000:Online',
  `state` int DEFAULT '0',
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UK_week_demand` (`fiscal_week`,`demand`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

ALTER TABLE gc_dmp_fast_write.fast_lite_ideal_demand_rtm_forecast_upload MODIFY COLUMN sold_to_id varchar(128) NULL COMMENT 'customer sold to id';


CREATE TABLE `sellin_demand_woi_setting` (
  `id` int NOT NULL AUTO_INCREMENT,
  `fiscal_week` varchar(16) NOT NULL,
  `mpn_setting` text,
  `sub_lob_setting` text,
  `publish_time` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `sellin_demand_woi_setting_UN` (`fiscal_week`,`publish_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;