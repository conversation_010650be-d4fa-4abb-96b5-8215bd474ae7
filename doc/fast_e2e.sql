ALTER TABLE fast_lite.sellin_demand_woi_setting ADD woi_type varchar(32) NULL COMMENT '区别woi的类型';
ALTER TABLE fast_lite.sellin_demand_woi_setting CHANGE woi_type woi_type varchar(32) NULL COMMENT '区别woi的类型' AFTER fiscal_week;
ALTER TABLE fast_lite.sellin_demand_woi_setting DROP KEY sellin_demand_woi_setting_UN;
ALTER TABLE fast_lite.sellin_demand_woi_setting ADD CONSTRAINT sellin_demand_woi_setting_UN UNIQUE KEY (fiscal_week,woi_type,publish_timestamp);
update fast_lite.sellin_demand_woi_setting set woi_type ='final_demand';



ALTER TABLE mybusiness.feedback_demand_by_soldto ADD ori_finalized_demand_v2_cw1 FLOAT NULL;
ALTER TABLE mybusiness.feedback_demand_by_soldto ADD ori_finalized_demand_v2_cw2 FLOAT NULL;


ALTER TABLE mybusiness.feedback_demand_by_region ADD ori_finalized_demand_v2_cw1 FLOAT NULL;
ALTER TABLE mybusiness.feedback_demand_by_region ADD ori_finalized_demand_v2_cw2 FLOAT NULL;
