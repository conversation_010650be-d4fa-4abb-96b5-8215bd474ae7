# FAST Lite CP&F - Demand

## 1. 列表接口
* path: /fast_lite_cpf/demand_list
* http_method: get
* Content-Type: application/json
```json
params:
{
"page_num": 1,//页数
"page_size": 10 //每页大小
}

. return:
{
"code": 0,
"msg": "success",
"data": {
  "dataList": {
    "records": [
            {
            "id": 2,
            "fiscalQtrWeekName": "FY23Q1W5", // 显示数据
            "state": 1,
            "type": "demand",
            "createTime": "2022-10-25T10:41:03"
            }
        ],
        "total": 1,
        "size": 10,
        "pages": 1
        }
    }
}
```


## 2. rtm 选项值
* path: /fast_lite_cpf/rtm_options
* http_method: get
* Content-Type: application/json
```json
params:
{}

. return: {
    "code": 0,
    "msg": "success",
    "data": [
        "rtm1",
        "rtm2",
        "rtm3",
        "rtm4",
        "rtm5",
    ]
}
```

## 3. business_type 选项值
* path: /fast_lite_cpf/business_type_options
* http_method: post
* Content-Type: application/json
```json
body:
{
    "rtm" : ["Lifestyle", "Mono"]
}

. return: {
    "code": 0,
    "msg": "success",
    "data": [
        "business_type1",
        "business_type2",
        "business_type3",
        "business_type4",
        "business_type5",
    ]
}
```


## 3. sold_to 选项值
* path: /fast_lite_cpf/sold_to_options
* http_method: post
* Content-Type: application/json
```json
body:
{
    "rtm" : ["Lifestyle", "Mono"],
    "business_type" : ["business_type1", "business_type2"],
    "fuzzy_filter": "to" // sold_to id或者名称进行模糊搜索
}

. return: {
    "code": 0,
    "msg": "success",
    "data": [
        "sold_to1",
        "sold_to2",
        "sold_to3",
        "sold_to4",
        "sold_to5",
    ]
}
```


## 4. 详情页
* path: /fast_lite_cpf/demand_detail
* http_method: post
* Content-Type: application/json
```json
body:
{
  "fiscal_qtr_week_name": "FY23Q1W5",
  "lob": "iPhone",
  "model": ["model1", "model2"],
  "sku": ["sku1", "sku2"],
  "rtm": [],
  "business_type": [],
  "sold_to": [],
  "orders": [{ "name": "", "type": 0 }], // 0:ASC,1:DESC, 只传一个自定义排序字段
  "page_size": 10,
  "page_num": 1
}

. return:
{
    "code": 0,
    "data": {
        "columns": [
            {
                "key": "rtm",
                "value": "RTM"
            },
            {
                "key": "business_type",
                "value": "Business\nType"
            },
            {
                "key": "sold_to_id",
                "value": "Customer\nSold-to ID"
            },
            {
                "key": "sold_to_name",
                "value": "Sold-to Name"
            },
            {
                "key": "top_up_demand_cw1",
                "value": "Top-up Demand CW+1\nFY23Q1W14"
            },
            {
                "key": "top_up_demand_cw2",
                "value": "Top-up Demand CW+2\nFY23Q2W1"
            },
            {
                "key": "top_up_demand_cw3",
                "value": "Top-up Demand CW+3\nFY23Q2W2"
            },
            {
                "key": "top_up_demand_cw4",
                "value": "Top-up Demand CW+4\nFY23Q2W3"
            },
            {
                "key": "shipment_plan_cw",
                "value": "Shipment Plan CW\nFY23Q1W13"
            },
            {
                "key": "shipment_plan_cw1",
                "value": "Shipment Plan CW+1\nFY23Q1W14"
            },
            {
                "key": "shipment_plan_cw2",
                "value": "Shipment Plan CW+2\nFY23Q2W1"
            },
            {
                "key": "shipment_plan_cw3",
                "value": "Shipment Plan CW+3\nFY23Q2W2"
            },
            {
                "key": "po_needed_cw",
                "value": "PO Needed CW\nFY23Q1W13"
            },
            {
                "key": "po_needed_cw1",
                "value": "PO Needed CW+1\nFY23Q1W14"
            },
            {
                "key": "po_needed_cw2",
                "value": "PO Needed CW+2\nFY23Q2W1"
            },
            {
                "key": "po_needed_cw3",
                "value": "PO Needed CW+3\nFY23Q2W2"
            }
        ],
        "pages": 0,
        "records": [
            {
                "business_type": "",
                "po_needed_cw": 0,
                "po_needed_cw1": 1,
                "po_needed_cw2": 3,
                "po_needed_cw3": 6,
                "rtm": "Total",
                "shipment_plan_cw": 123,
                "shipment_plan_cw1": 281,
                "shipment_plan_cw2": 289,
                "shipment_plan_cw3": 85,
                "sold_to_id": "",
                "sold_to_name": "",
                "top_up_demand_cw1": 18,
                "top_up_demand_cw2": 13,
                "top_up_demand_cw3": 2,
                "top_up_demand_cw4": 3
            },
            {
                "business_type": "JD",
                "po_needed_cw": 525,
                "po_needed_cw1": 831,
                "po_needed_cw2": 1209,
                "po_needed_cw3": 1403,
                "rtm": "Channel Online",
                "shipment_plan_cw": 181,
                "shipment_plan_cw1": 132,
                "shipment_plan_cw2": 512,
                "shipment_plan_cw3": 67,
                "sold_to_id": "1118510",
                "sold_to_name": "JD-SZDC",
                "top_up_demand_cw1": 953,
                "top_up_demand_cw2": 1222,
                "top_up_demand_cw3": 116,
                "top_up_demand_cw4": 192
            }
        ],
        "size": 10,
        "total": 23
    },
    "msg": "Success"
}
