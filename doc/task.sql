CREATE TABLE `task` (
                                `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                                `name` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '任务名称',
                                `desc` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '任务描述',
                                `type` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '任务类型  ',
                                `status` enum('ready', 'running', 'done', 'timeout') COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'ready' COMMENT '任务状态：ready可开始执行 running执行中，done完成，timeout超时',

                                `partition` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '分区数',
                                `cursor` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '任务执行游标',

                                `run_at` datetime NOT NULL DEFAULT '1970-01-01 00:00:00' COMMENT '期望执行时间',

                                `worker` varchar(64) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '执行机器ip',
                                `work_mutex_at` datetime NOT NULL DEFAULT '1970-01-01 00:00:00' COMMENT '锁释放时间',
                                `retry_times` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '失败重试次数',
                                `begin_at` datetime NOT NULL DEFAULT '1970-01-01 00:00:00' COMMENT '最近一次开始执行时间',
                                `done_at` datetime NOT NULL DEFAULT '1970-01-01 00:00:00' COMMENT '任务完成时间',

                                `create_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                `update_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                PRIMARY KEY (`id`),
                                UNIQUE KEY `uk_type_partition` (`type`,`name`, `partition`),
                                KEY `k_status` (`status`)
) ENGINE = InnoDB AUTO_INCREMENT = 0 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '任务池'