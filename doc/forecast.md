# FAST Lite CP&F - Forecast

## 1. 列表接口
* path: /fast_lite_cpf/forecast_list
* http_method: get
* Content-Type: application/json
```json
params:
{
"page_num": 1,//页数
"page_size": 10 //每页大小
}

. return:
{
"code": 0,
"msg": "success",
"data": {
    "dataList": {
        "records": [
                {
                "id": 2,
                "fiscalQtrWeekName": "FY23Q1W5", // 显示数据
                "state": 1,
                "type": "forecast",
                "createTime": "2022-10-25T10:41:03"
                }
            ],
        "total": 1,
        "size": 10,
        "pages": 1
    }
}
```


## 2. sku values SKU选项值
* path: /fast_lite_cpf/sku_options
* http_method: post
* Content-Type: application/json
```json
body:
{
    "lob" : "iPhone",
    "model": ["iPhone 14 Pro Max", "iPhone 14 Pro"]
}

. return: {
    "code": 0,
    "msg": "success",
    "data": [
        "sku1",
        "sku2",
        "sku3",
        "sku4",
        "sku5",
    ]
}
```

## 3. model values model选项值
* path: /fast_lite_cpf/model_options
* http_method: get
* Content-Type: application/json
```json
params:
{
    "lob" : "iPhone"
}

. return: {
    "code": 0,
    "msg": "success",
    "data": [
        "model1",
        "model2",
        "model3",
        "model4",
        "model5",
    ]
}
```


## 4. 详情页
* path: /fast_lite_cpf/forecast_detail
* http_method: post
* Content-Type: application/json
```json
body:
{
  "fiscal_qtr_week_name": "FY23Q1W5",
  "lob": "iPhone",
  "model": ["model1", "model2"],
  "sku": ["sku1", "sku2"],
  "order_by": [{ "name": "", "type": 0 }], // 0:ASC,1:DESC, 只传一个自定义排序字段
}

. return:
{
    "code": 0,
    "msg": "success",
    "data": {
        "columns": [... "CW-2\nFY22Q1w2", "CW-1\nFY22Q1w3",...],
        "data_list":[
            {
                "rtm": "Lifestyle",
                "business_type": "All",
                "sold_to": "All",
                "cq_total": "xxxx",
                "cw+5": 34,
                "cw+4": 34,
                "cw+3": 34,
                "cw+2": 34,
                "cw+1": 34,
                "cw": 34,
                "cw-1": 34,
                "cw-2": 34,
                "cw-3": 34,
                "cw-4": 34,
                "cw-5": 35,
                "cw-6": 35,
                "cw-7": 35,
                "cw-8": 35,
                "cw-9": 35,
                "cw-10": 35,
                "cw-11": 34,
                "cw-12": 34,
                "cw-13": 33,
                "children":[
                    {
                        "rtm": "Lifestyle",
                        "business_type": "Disti",
                        "sold_to": "All",
                        "cq_total": "xxxx",
                        "cw+5": 34,
                        "cw+4": 34,
                        "cw+3": 34,
                        "cw+2": 34,
                        "cw+1": 34,
                        "cw": 34,
                        "cw-1": 34,
                        "cw-2": 34,
                        "cw-3": 34,
                        "cw-4": 34,
                        "cw-5": 35,
                        "cw-6": 35,
                        "cw-7": 35,
                        "cw-8": 35,
                        "cw-9": 35,
                        "cw-10": 35,
                        "cw-11": 34,
                        "cw-12": 34,
                        "cw-13": 33,
                        "children":[
                            {
                                "rtm": "Lifestyle",
                                "business_type": "Disti",
                                "sold_to": "SH INGRAM",
                                "cq_total": "xxxx",
                                "cw+5": 34,
                                "cw+4": 34,
                                "cw+3": 34,
                                "cw+2": 34,
                                "cw+1": 34,
                                "cw": 34,
                                "cw-1": 34,
                                "cw-2": 34,
                                "cw-3": 34,
                                "cw-4": 34,
                                "cw-5": 35,
                                "cw-6": 35,
                                "cw-7": 35,
                                "cw-8": 35,
                                "cw-9": 35,
                                "cw-10": 35,
                                "cw-11": 34,
                                "cw-12": 34,
                                "cw-13": 33,
                                "children":[
                                    
                                ]
                            }
                        ]
                    }
                ]
            },
            {
                "rtm": "Lifestyle",
                "business_type": "All",
                "sold_to": "All",
                "cq_total": "xxxx",
                "cw+5": 34,
                "cw+4": 34,
                "cw+3": 34,
                "cw+2": 34,
                "cw+1": 34,
                "cw": 34,
                "cw-1": 34,
                "cw-2": 34,
                "cw-3": 34,
                "cw-4": 34,
                "cw-5": 35,
                "cw-6": 35,
                "cw-7": 35,
                "cw-8": 35,
                "cw-9": 35,
                "cw-10": 35,
                "cw-11": 34,
                "cw-12": 34,
                "cw-13": 33,
                "children":[
                    {
                        "rtm": "Lifestyle",
                        "business_type": "Disti",
                        "sold_to": "All",
                        "cq_total": "xxxx",
                        "cw+5": 34,
                        "cw+4": 34,
                        "cw+3": 34,
                        "cw+2": 34,
                        "cw+1": 34,
                        "cw": 34,
                        "cw-1": 34,
                        "cw-2": 34,
                        "cw-3": 34,
                        "cw-4": 34,
                        "cw-5": 35,
                        "cw-6": 35,
                        "cw-7": 35,
                        "cw-8": 35,
                        "cw-9": 35,
                        "cw-10": 35,
                        "cw-11": 34,
                        "cw-12": 34,
                        "cw-13": 33,
                        "children":[
                            {
                                "rtm": "Lifestyle",
                                "business_type": "Disti",
                                "sold_to": "SH INGRAM",
                                "cq_total": "xxxx",
                                "cw+5": 34,
                                "cw+4": 34,
                                "cw+3": 34,
                                "cw+2": 34,
                                "cw+1": 34,
                                "cw": 34,
                                "cw-1": 34,
                                "cw-2": 34,
                                "cw-3": 34,
                                "cw-4": 34,
                                "cw-5": 35,
                                "cw-6": 35,
                                "cw-7": 35,
                                "cw-8": 35,
                                "cw-9": 35,
                                "cw-10": 35,
                                "cw-11": 34,
                                "cw-12": 34,
                                "cw-13": 33,
                                "children":[
                                    
                                ]
                            }
                        ]
                    }
                ]
            }
        ]
    }
}
