import traceback
from datetime import datetime, timedelta

from kit.email.sender import Sender, CustomizedData
from task_kit.repository.task_repository import TaskRepository, TaskStatus
from task_kit.worker.antifraud_so_ub_email_worker import AntifraudSoUBEmailWorker
from task_kit.worker.automatic.automatic_cto_worker import <PERSON><PERSON><PERSON><PERSON><PERSON>
from task_kit.worker.automatic.automatic_worker import <PERSON><PERSON><PERSON><PERSON>
from task_kit.worker.automatic.cto_notification_email_worker import CTONotificationEmailWorker
from task_kit.worker.automatic.esr_sync_status_worker import EsrCheckSyncStatusWorker
from task_kit.worker.internal_pending_review.pending_review_worker import PendingReviewWorker
from task_kit.worker.macro_forecast_email_worker import MacroForecast<PERSON>mailWorker
from task_kit.worker.eoh_aging_and_wo_ds_email_worker import EohAgingAndWithoutDSEmailWorker
from task_kit.worker.my_business_appeal.my_business_appeal_worker import MyBusiness<PERSON><PERSON>al<PERSON>orker
from task_kit.worker.my_business_worker.my_business_ub_radar_worker import Ub<PERSON><PERSON>r<PERSON><PERSON><PERSON>
from task_kit.worker.po_delinquent_email_worker import Po<PERSON><PERSON>quent<PERSON>mail<PERSON><PERSON><PERSON>
from task_kit.worker.po_delinquent_rtm_email_worker import PoDelinquentRTMEmailWorker
from task_kit.worker.po_gap_rtm_email_worker import PoGapRTMEmailWorker
from task_kit.worker.pos_suspension_email_worker import PosSuspensionEmailWorker
from task_kit.worker.po_gap_email_worker import PoGapEmailWorker
from task_kit.worker.smart_tower.smart_tower_waive_worker import SmartTowerWaiveWorker
from util.conf import logger
from util.const import EmailCmd
from util.util import env_dev

PREFIX_SEND_EMAIL_TASK = "send_email_task"


# 注册task
TASK_TYPE_SEND_ANTI_FRAUD_SO_UB_EMAIL = 201
TASK_TYPE_SEND_EOH_AGING_UB_WITHOUT_DS_EMAIL = 202
TASK_TYPE_SEND_MACRO_FORECAST_EMAIL = 301
TASK_TYPE_SEND_POS_SUSPENSION_EMAIL = 203

AUTOMATIC_ESR_MAIN_PRODUCTS_GEN_FILE = 600  # 主品文件生成
AUTOMATIC_ESR_ACCESSORIES_GEN_FILE = 601    # 配件文件生成
AUTOMATIC_CTO_POD_GEN_FILE = 602            # CTO POD文件生成


TASK_TYPE_ESR_MAIN_PRODUCTS_EMAIL = 701     # 主品邮件检查
TASK_TYPE_ESR_ACCESSORIES_EMAIL = 702       # 配件邮件检查
TAST_TYPE_CTO_NOTIFICATION_EMAIL = 703      # CTO数据检查通知邮件

TASK_TYPE_INTERNAL_PENDING_REVIEW_EMAIL = 500   # 内部停店申诉待审批提醒
TASK_TYPE_MYBIZ_POS_SUSPENSION_ABNORMALLY_REMIND_EMAIL = 800                        # mybiz门店异常结果提醒，申诉即将开始
TASK_TYPE_MYBIZ_POS_SUSPENSION_APPEAL_WINDOW_ABOUT_TO_CLOSE_TEMPLATE_EMAIL = 801    # mybiz申诉窗口即将关闭


TASK_TYPE_PO_GAP_EMAIL = 1001 # PO Gap 邮件
TASK_TYPE_PO_DELINQUENT_EMAIL = 1002 # PO Delinquent 邮件
TASK_TYPE_PO_GAP_RTM_EMAIL = 1003 # PO Gap RTM 邮件
TASK_TYPE_PO_DELINQUENT_RTM_EMAIL = 1004 # PO Delinquent RTM 邮件

TASK_TYPE_MYBIZ_UB_RADAR_EMAIL = 811    # mybiz ub 激活率预警提醒

TASK_TYPE_SMART_TOWER_INVENTORY_WAIVE_EMAIL = 5    # smart tower inventory 大面积缺货


worker_router = {
    TASK_TYPE_SEND_ANTI_FRAUD_SO_UB_EMAIL: AntifraudSoUBEmailWorker,
    # TASK_TYPE_SEND_EOH_AGING_UB_WITHOUT_DS_EMAIL: EohAgingAndWithoutDSEmailWorker,
    TASK_TYPE_SEND_MACRO_FORECAST_EMAIL: MacroForecastEmailWorker,
    # TASK_TYPE_SEND_POS_SUSPENSION_EMAIL: PosSuspensionEmailWorker,

    # ESR/CTO邮件告警提醒
    TASK_TYPE_ESR_MAIN_PRODUCTS_EMAIL: EsrCheckSyncStatusWorker,
    TASK_TYPE_ESR_ACCESSORIES_EMAIL: EsrCheckSyncStatusWorker,
    TAST_TYPE_CTO_NOTIFICATION_EMAIL: CTONotificationEmailWorker,

    # 内部停店申诉待审批提醒
    TASK_TYPE_INTERNAL_PENDING_REVIEW_EMAIL: PendingReviewWorker,

    # smart tower inventory 大面积缺货 提醒
    TASK_TYPE_SMART_TOWER_INVENTORY_WAIVE_EMAIL: SmartTowerWaiveWorker,

    # mybiz门店异常结果提醒，申诉即将开始
    TASK_TYPE_MYBIZ_POS_SUSPENSION_ABNORMALLY_REMIND_EMAIL: MyBusinessAppealWorker,
    # mybiz申诉窗口即将关闭
    TASK_TYPE_MYBIZ_POS_SUSPENSION_APPEAL_WINDOW_ABOUT_TO_CLOSE_TEMPLATE_EMAIL: MyBusinessAppealWorker,
    
    # PO 邮件
    TASK_TYPE_PO_GAP_EMAIL: PoGapEmailWorker,
    TASK_TYPE_PO_DELINQUENT_EMAIL: PoDelinquentEmailWorker,
    # PO RTM邮件
    TASK_TYPE_PO_GAP_RTM_EMAIL: PoGapRTMEmailWorker,
    TASK_TYPE_PO_DELINQUENT_RTM_EMAIL: PoDelinquentRTMEmailWorker,

    # mybiz ub 激活率预警提醒
    TASK_TYPE_MYBIZ_UB_RADAR_EMAIL: UbRadarWorker,
}

slow_worker_router = {
    # ESR/CTO数据生成
    AUTOMATIC_ESR_MAIN_PRODUCTS_GEN_FILE: AutomaticWorker,
    AUTOMATIC_ESR_ACCESSORIES_GEN_FILE: AutomaticWorker,
    AUTOMATIC_CTO_POD_GEN_FILE: AutomaticCTOWorker,
}


# email 执行失败也不进行邮件通知的白名单
email_execute_failed_no_notify_whitelist = [
    TASK_TYPE_ESR_MAIN_PRODUCTS_EMAIL,
    TASK_TYPE_ESR_ACCESSORIES_EMAIL,
    TAST_TYPE_CTO_NOTIFICATION_EMAIL,
    TASK_TYPE_MYBIZ_POS_SUSPENSION_ABNORMALLY_REMIND_EMAIL,
    TASK_TYPE_MYBIZ_POS_SUSPENSION_APPEAL_WINDOW_ABOUT_TO_CLOSE_TEMPLATE_EMAIL,
    TASK_TYPE_MYBIZ_UB_RADAR_EMAIL,
]


def _execute_task(task_type: int, task_router: dict):
    # 按照不同的type筛选不同的任务
    task = TaskRepository.get_to_do(task_type)
    
    if task is None:
        return
    
    # 如果当前时间距离run_at > 6h 置为超时
    if (datetime.now() - task.run_at) > timedelta(hours=6):
        task.status = TaskStatus.TimeOut.value
        TaskRepository.save(task)
        logger.info(f"{PREFIX_SEND_EMAIL_TASK} {task} timeout..")
        return
    
    worker = task_router.get(task.type, None)
    if worker is not None:
        logger.info(f"{PREFIX_SEND_EMAIL_TASK} {task} begin..")
        wk = worker(task.params)
        try:
            wk.do()
        except Exception as e:
            if task_type not in email_execute_failed_no_notify_whitelist:
                sender = Sender(cmd=EmailCmd.EmailExecuteFailedAlert, need_check_duplication=False)
                data = {
                    "env": "dev" if env_dev() else "prod",
                    "desc": task.desc,
                    "period": task.period,
                    "error_info": traceback.format_exc()
                }
                sender.send(customized_data=CustomizedData(refresh_time=datetime.now(), data=data))
        else:
            task.status = TaskStatus.Done.value
            task.done_at = datetime.now()
            TaskRepository.save(task)
            logger.info(f"{PREFIX_SEND_EMAIL_TASK} {task} end..")


def schedule_task():
    # 按照不同的type筛选不同的任务
    for task_type in worker_router.keys():
        _execute_task(task_type, task_router=worker_router)


def schedule_task_slowly():
    # 需要长时间执行的任务单独配置调度f
    for task_type in slow_worker_router.keys():
        _execute_task(task_type, task_router=slow_worker_router)
