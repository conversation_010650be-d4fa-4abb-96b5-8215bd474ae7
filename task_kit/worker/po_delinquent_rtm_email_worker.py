import json

from datetime import datetime
from domain.email.impl.email_po_delinquent_impl import get_po_delinquent_rtm_email_data, whether_to_send_email_for_po_delinquent
from kit.email.sender import Sender, CustomizedData
from util.conf import logger


class PoDelinquentRTMEmailWorker:
    def __init__(self, params: str):
        # 配置在数据库中的是json字符串
        if params is None or "email_cmd" not in params:
            logger.error("no param configed of this Worker.")
            raise Exception("param is required")
        params = json.loads(params)
        self.email_cmd = params.get("email_cmd")
        self.rtm = params.get("rtm")
        self.greeting = params.get("greeting")
        self.params = params

    def do(self):
        try:
            # 发送时间
            now = datetime.now()
            send_date = now.strftime("%Y-%m-%d")
            if not whether_to_send_email_for_po_delinquent(send_date):
                error_message = f"{send_date} 本周demand数据未发布， RTM {self.rtm} 未发送Po Delinquent邮件!!!"
                logger.info(error_message)
                raise Exception(error_message)
            # 获取需要展示的po gap的数据
            render_data = get_po_delinquent_rtm_email_data(self.rtm)
            render_data["rtm"] = self.rtm
            render_data["greeting"] = self.greeting
            # 发送邮件
            sender = Sender(self.email_cmd, custom_format_subject=True)
            sender.send(customized_data=CustomizedData(now, render_data))
        except Exception as e:
            logger.exception(e)
            raise e
