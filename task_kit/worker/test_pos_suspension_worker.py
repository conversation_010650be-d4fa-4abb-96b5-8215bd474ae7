from domain.dashboard.entity.anti_fraud.anti_fraud_so_ub import RTM_Mono, RTM_MULTI, RTM_CARRIER
from task_kit.worker.pos_suspension_email_worker import PosSuspensionEmailWorker


def test_run():
    # PosSuspensionEmailWorker(params='{"email_cmd":"test_ninghui_pos_suspension_email", "rtm_version": "All"}').do()
    PosSuspensionEmailWorker(params='{"email_cmd":"test_ninghui_pos_suspension_email_v4", "rtm_version": "All"}').do()

    # PosSuspensionEmailWorker(params='{"email_cmd":"formal_antifraud_pos_suspension_email_with_boss_mono_brand_pre", "rtm_version": "' +RTM_Mono+ '"}').do()
    # PosSuspensionEmailWorker(params='{"email_cmd":"formal_antifraud_pos_suspension_email_with_boss_multi_brand_pre", "rtm_version": "' +RTM_MULTI+ '"}').do()
    # PosSuspensionEmailWorker(params='{"email_cmd":"formal_antifraud_pos_suspension_email_with_boss_carrier_pre", "rtm_version": "' +RTM_CARRIER+ '"}').do()

