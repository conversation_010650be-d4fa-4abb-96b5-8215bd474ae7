from task_kit.worker.po_delinquent_rtm_email_worker import PoDelinquent<PERSON><PERSON>mailWorker


def test_run():
    # PoDelinquentRTMEmailWorker(params='{"email_cmd":"Mono_po_delinquent_rtm_email_gm", "rtm":"Mono", "greeting":"Hello <PERSON>,"}').do()
    # PoDelinquentRTMEmailWorker(params='{"email_cmd":"Multi_po_delinquent_rtm_email_gm", "rtm":"Multi", "greeting":"Hello <PERSON>,"}').do()
    # PoDelinquentRTMEmailWorker(params='{"email_cmd":"Online_po_delinquent_rtm_email_gm", "rtm":"Online", "greeting":"Hello Steven,"}').do()
    # PoDelinquentRTMEmailWorker(params='{"email_cmd":"Carrier_po_delinquent_rtm_email_gm","rtm":"<PERSON>","greeting": "Hello <PERSON>,"}').do()
    # PoDelinquentRTMEmailWorker(params='{"email_cmd":"Education_po_delinquent_rtm_email_gm","rtm":"Education","greeting": "Hello Stella,"}').do()
    PoDelinquentRTMEmailWorker(params='{"email_cmd":"Enterprise_po_delinquent_rtm_email_gm","rtm":"Enterprise","greeting": "Hello Stella,"}').do()
