import json
from datetime import datetime
from kit.email.sender import Sender, CustomizedData

from util.conf import logger


class PendingReviewWorker:
    def __init__(self, params: str):
        # 配置在数据库中的是json字符串
        if params is None or "email_cmd" not in params or "notify_list" not in params:
            logger.error("no param configed of PendingReviewWorker.")
            raise Exception("param is required")
        params = json.loads(params)

        self.email_cmd = params.get("email_cmd")
        self.notify_list = params.get("notify_list")

    def do(self):
        try:
            now = datetime.now()
            if not self.notify_list:
                logger.info(f'internal pending review, {now} 暂无待审批通知数据')
                return

            logger.info(f'internal pending review begin send, data: {self.notify_list}')
            sender = Sender(self.email_cmd)
            for notify_item in self.notify_list:
                sender.set_recipients(recipient_list=[notify_item['email']])
                sender.send(customized_data=CustomizedData(now, notify_item))
                logger.info(f'internal pending review send email({notify_item["email"]}) successfully!')
        except Exception as e:
            logger.exception(e)
            raise e
