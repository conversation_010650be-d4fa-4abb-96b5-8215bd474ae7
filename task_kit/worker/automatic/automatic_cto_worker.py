import json
from datetime import datetime
import pandas as pd

from data.mysqls.automatic.automatic_file_info import AutomaticFileInfo
from domain.datasource.impl.automatic import CTO_CHANNEL_PERMISSIONS, KeySubKeyRelation, KeyTableSelector, AutomaticFileResult
from util.conf import logger


class AutomaticCTOWorker:
    def __init__(self, params: str):
        # 配置在数据库中的是json字符串
        if params is None:
            logger.error("no param configed of EsrMainProductsWorker.")
            raise Exception("param is required")
        params = json.loads(params)
        self.params = params

    def do(self):
        try:
            key = self.params.get("type")
            snapshot_dt = self.params.get("snapshot_dt")
            snapshot_ts = self.params.get("snapshot_ts")
            channel = self.params.get("rtm")
            fiscal_qtr_week_name = self.params.get("fiscal_qtr_week_name")
            table_name = self.params.get("table_name")
            data_type = self.params.get("data_type")
            db_name = self.params.get("db_name")
            sync_time = self.params.get("sync_time")

            # 防错: 已经生成的文件task再次执行, 不能再生成
            record = AutomaticFileInfo.query_records_exist(
                data_type=data_type,
                db_name=db_name,
                table_name=table_name,
                rtm_view=channel,
                sync_time=sync_time
            )
            if record:
                logger.error(f'automatic gen file existed, db_name: {db_name}, data_type: {data_type}, channel: {channel}, table_name: {table_name}...')
                return

            logger.info(f'automatic gen file successfully begin, db_name: {db_name}, data_type: {data_type}, channel: {channel}, key: {key}...')
            # key 对应的表, 查询数据
            table_name_class_obj = KeyTableSelector.get_table(key)

            # 生成对应权限可以查看的数据
            channel_permissions = {}
            if key in [KeySubKeyRelation.CTO_POD.value]:
                channel_permissions = CTO_CHANNEL_PERMISSIONS
            query_params = channel_permissions[channel]
            query_params["snapshot_dt"] = snapshot_dt
            data = table_name_class_obj.query_records(**query_params)
            df = pd.DataFrame(data)

            # 需要排除的列名
            columns_to_exclude = ['create_time', 'update_time']
            # 检查并删除存在的列
            df.drop(columns=columns_to_exclude, inplace=True, errors='ignore')
            df['snapshot_dt'] = df['snapshot_dt'].dt.strftime('%Y-%m-%d')

            # 文件下载、入库
            automatic_file = AutomaticFileResult(key=key, channel=channel, update_time=sync_time)
            file_info = automatic_file.file_info()
            absolute_file_path = file_info["absolute_file_path"]
            automatic_file.gen_excel_file(data=df, file_path=absolute_file_path)

            # 保存文件信息到数据库供下载使用
            now = datetime.now()
            db_file_info_dict = {
                "bus_key": file_info.get("bus_key"),
                "file_name": file_info.get("file_name"),
                "file_path": file_info.get("relative_file_path"),
                "create_time": now,
                "update_time": now,
                "fiscal_qtr_week_name": fiscal_qtr_week_name,
                "snapshot_ts": snapshot_ts,
                "table_name": table_name,
                "data_type": data_type,
                "db_name": db_name,
                "snapshot_dt": snapshot_dt,
                "sync_time": sync_time,
                "rtm_view": channel
            }
            # CTO的数据只有pod会生成文件，需要也生成backlog、execution 文件信息为空的记录，以便list和recordlist来读取
            file_data = [db_file_info_dict]
            if data_type == "CTO":
                extra_file_info = [
                    {
                        "bus_key": f"{sync_time}_cto_backlog_{channel}".lower(),
                        "table_name": "gc_dmp_backlog",
                    },
                    {
                        "bus_key": f"{sync_time}_cto_execution_{channel}".lower(),
                        "table_name": "gc_dmp_execution",
                    }
                ]
                for item in extra_file_info:
                    basic_info = {
                            "file_name": None,
                            "file_path": None,
                            "create_time": now,
                            "update_time": now,
                            "fiscal_qtr_week_name": fiscal_qtr_week_name,
                            "snapshot_ts": snapshot_ts,
                            "db_name": db_name,
                            "data_type": data_type,
                            "snapshot_dt": snapshot_dt,
                            "sync_time": sync_time,
                            "rtm_view": channel
                        }
                    file_data.append({**basic_info, **item})
            AutomaticFileInfo.bulk_save(file_data)
        except Exception as e:
            logger.exception(e)
            raise e
