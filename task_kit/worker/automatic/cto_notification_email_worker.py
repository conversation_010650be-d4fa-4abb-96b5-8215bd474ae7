import json
from datetime import datetime

from data.mysqls.automatic.automatic_file_info import AutomaticFileInfo

from domain.datasource.impl.automatic import CTO_CHANNEL_PERMISSIONS, KeySubKeyRelation, AutomaticDataStatus, ESR_CHANNEL_PERMISSIONS
from kit.email.sender import Sender, CustomizedData

from util.conf import logger
from util.mail_conf import get_system_link


class CTONotificationEmailWorker:
    def __init__(self, params: str):
        # 配置在数据库中的是json字符串, 检查必要的参数是否已经传入
        if (params is None 
            or "email_cmd" not in params 
            or "sync_status" not in params 
            or "snapshot_dt" not in params 
            or "type" not in params):
            logger.error("no param configed of CTONotificationEmailWorker.")
            raise Exception("param is required")
        params = json.loads(params)

        self.email_cmd = params.get("email_cmd")
        self.sync_status = params.get("sync_status")
        self.key = params.get("type")
        self.snapshot_dt = params.get("snapshot_dt")
        self.sync_time = params.get("sync_time")
        self.fiscal_qtr_week_name = params.get("fiscal_qtr_week_name")
        self.table_name = params.get("table_name")
        self.db_name = params.get("db_name")
        self.data_type = params.get("data_type")

    def do(self):
        try:
            now = datetime.now()
            render_data = {
                "snapshot_dt": self.snapshot_dt,
                "sync_status": self.sync_status,
                "jump_workspace_link": get_system_link(system_link="/data-source", enter_from="is_external=1"),
            }

            # 数据延迟刷新，直接发送延迟邮件
            if AutomaticDataStatus.DELAY.value == self.sync_status:
                sender = Sender(self.email_cmd, need_check_duplication=False, custom_format_subject=True)
                sender.send(customized_data=CustomizedData(now, render_data))
                return

            # 数据准时刷新，需要检查所有rtm版本的文件均生成，才发送通知邮件
            for check_channel, permissions in CTO_CHANNEL_PERMISSIONS.items():
                # 无须生成报告跳过即可
                if self.key not in permissions["reports"]:
                    continue
                record = AutomaticFileInfo.query_records_exist(
                    table_name=self.table_name,
                    sync_time=self.sync_time,
                    snapshot_dt=self.snapshot_dt,
                    fiscal_qtr_week_name=self.fiscal_qtr_week_name,
                    data_type=self.data_type,
                    db_name=self.db_name,
                    rtm_view=check_channel
                )
                if not record:
                    raise Exception(f"任务检测中, 等待文件生成, channel: {check_channel}, sync_time: {self.sync_time}, "
                                    f"table_name: {self.table_name}, db_name: {self.db_name}....")
            else:
                sender = Sender(self.email_cmd, need_check_duplication=False, custom_format_subject=True)
                sender.send(customized_data=CustomizedData(now, render_data))
        except Exception as e:
            logger.exception(e)
            raise e
