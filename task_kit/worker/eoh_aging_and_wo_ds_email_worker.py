from concurrent.futures import ThreadPoolExecutor
import json
from datetime import datetime, timedelta
from data.mysqls.email_report.cdc_inventory_metric import CdcInventoryMetricRepo
from data.mysqls.email_report.ds_ub_wo_ds_wkly import DsUbWoDsWkly
from data.mysqls.email_report.eoh_aging_by_nand_repo import EohAgingByNandRepo
from domain.dashboard.entity.anti_fraud.cdc_inventory_metric import CDCInventoryMetric
from domain.dashboard.entity.anti_fraud.ub_wo_ds_wkly import SUB_LOB_RULES, RTMS_RULES, UbWoDsWklyItem, \
    NO_DISPLAY_SUB_RTM, RTM_EDU
from domain.dashboard.entity.fiscal_week import FiscalWeek
from kit.email.sender import Sender, CustomizedData

from util.conf import logger
from util.const import ALL


class EohAgingAndWithoutDSEmailWorker:
    def __init__(self, params: str):
        # 配置在数据库中的是json字符串
        if params is None or "email_cmd" not in params or "rtm_version" not in params:
            logger.error("no param configed of EohAgingAndWithoutDSEmailWorker.")
            raise Exception("param is required")
        params = json.loads(params)
        self.email_cmd = params.get("email_cmd")

        self.rtm_version = params.get("rtm_version")
        self.params = params

    def get_weeks(self, query_date: str) -> list:
        # rolling_weeks = DsUbWoDsWkly.query_rolling_weeks(query_date=query_date)
        return DsUbWoDsWkly.query_rolling_weeks(query_date=query_date)

    def do(self):
        try:
            now = datetime.now()
            # now = datetime.now() - timedelta(days=1) # 调试使用
            current_date = now.strftime('%Y-%m-%d')
            # 数据刷新时间固定就是t-1, 如果没有数据就需要报错，且不要发邮件
            t_1_date = (now - timedelta(days=1)).strftime('%Y-%m-%d')
            refresh_time = datetime.strptime(t_1_date, '%Y-%m-%d')
            rolling_weeks = self.get_weeks(current_date)
            # --------这是ub without ds模块的overall--------------
            over_all_futures = []
            over_all_pool = ThreadPoolExecutor(max_workers=len(rolling_weeks))
            for week in rolling_weeks:
                over_all_futures.append(
                    over_all_pool.submit(DsUbWoDsWkly.query_overall_record, current_date, week)
                )
            over_all_results = [future.result() for future in over_all_futures]
            over_all_pool.shutdown()
            over_all_ub_cw1 = over_all_results[0]
            over_all_ub_cw2 = over_all_results[1]
            over_all_ub_cw3 = over_all_results[2]
            over_all_ub_cw4 = over_all_results[3]
            over_all_ub_cw5 = over_all_results[4]

            sub_lob_futures = []
            sub_lob_pool = ThreadPoolExecutor(max_workers=len(rolling_weeks))
            for week in rolling_weeks:
                sub_lob_futures.append(
                    sub_lob_pool.submit(DsUbWoDsWkly.query_sub_lob_record, current_date, week)
                )
            sub_lob_results = [future.result() for future in sub_lob_futures]
            sub_lob_pool.shutdown()
            sub_lob_ub_cw1 = sub_lob_results[0]
            sub_lob_ub_cw2 = sub_lob_results[1]
            sub_lob_ub_cw3 = sub_lob_results[2]
            sub_lob_ub_cw4 = sub_lob_results[3]
            sub_lob_ub_cw5 = sub_lob_results[4]

            region_sub_lob_ub_qtd = get_region_sub_lob(sub_lob_ub_cw5)
            region_sub_lob_ub_cw1 = get_region_sub_lob(sub_lob_ub_cw1)
            region_sub_lob_ub_cw2 = get_region_sub_lob(sub_lob_ub_cw2)
            region_sub_lob_ub_cw3 = get_region_sub_lob(sub_lob_ub_cw3)
            region_sub_lob_ub_cw4 = get_region_sub_lob(sub_lob_ub_cw4)
            region_sub_lob_ub_cw5 = get_region_sub_lob(sub_lob_ub_cw5)

            # --------这是eoh aging模块的overall--------------
            eoh_aging_list = CdcInventoryMetricRepo.query(current_date, 'page1')
            # --------这是eoh aging模块的sublob--------------
            eoh_aging_sublob_list = CdcInventoryMetricRepo.query(current_date, 'page2')
            # 过滤一下 rtm下不要sublob的数据，这种数据 rtm！=all sub_rtm=all sub_lob!=all
            # 20250228增加edu数据，edu没有sub_rtm, 所以需要过滤rtm=Education sub_rtm=Education的数据，保留rtm=Education的数据 sub_rtm=All的数据, 白名单配置的时候就只给edu配置了一个all的sub rtm
            eoh_aging_sublob_list = [item for item in eoh_aging_sublob_list if
                                     not (item.rtm not in [ALL, RTM_EDU] and item.sub_rtm == ALL and item.sub_lob != ALL)]

            eoh_aging_nd_list = EohAgingByNandRepo.query(current_date)
            # 过滤一下 china channel下不要hq的数据 rtm==all sub_rtm=all hq_id!=all
            eoh_aging_nd_list = [item for item in eoh_aging_nd_list if
                                 not (item.rtm == ALL and item.sub_rtm == ALL and item.hq_id != ALL)]

            # 取2个季度的季度名称, 从ub daily表取一条数据就行
            last_quarter = over_all_ub_cw5[0].fiscal_qtr_year_name_lq
            current_quarter = over_all_ub_cw5[0].fiscal_qtr_year_name

            render_data = {
                "last_quarter": last_quarter,
                "current_quarter": current_quarter,
                # 这是ub without ds模块的overall
                "over_all_ub_qtd": convert_overall(over_all_ub_cw5, self.rtm_version),
                "over_all_ub_cw1": convert_overall(over_all_ub_cw1, self.rtm_version),
                "over_all_ub_cw2": convert_overall(over_all_ub_cw2, self.rtm_version),
                "over_all_ub_cw3": convert_overall(over_all_ub_cw3, self.rtm_version),
                "over_all_ub_cw4": convert_overall(over_all_ub_cw4, self.rtm_version),
                "over_all_ub_cw5": convert_overall(over_all_ub_cw5, self.rtm_version),
                
                # 这是ub without ds模块的sublob view
                "sub_lob_ub_qtd": convert_sub_lob(sub_lob_ub_cw5),
                "sub_lob_ub_cw1": convert_sub_lob(sub_lob_ub_cw1),
                "sub_lob_ub_cw2": convert_sub_lob(sub_lob_ub_cw2),
                "sub_lob_ub_cw3": convert_sub_lob(sub_lob_ub_cw3),
                "sub_lob_ub_cw4": convert_sub_lob(sub_lob_ub_cw4),
                "sub_lob_ub_cw5": convert_sub_lob(sub_lob_ub_cw5),

                # 这是ub without ds模块的sublob view(china channel)
                "region_sub_lob_ub_qtd": region_sub_lob_ub_qtd,
                "region_sub_lob_ub_cw1": region_sub_lob_ub_cw1,
                "region_sub_lob_ub_cw2": region_sub_lob_ub_cw2,
                "region_sub_lob_ub_cw3": region_sub_lob_ub_cw3,
                "region_sub_lob_ub_cw4": region_sub_lob_ub_cw4,
                "region_sub_lob_ub_cw5": region_sub_lob_ub_cw5,

                "rtm_version": self.rtm_version,
                "rolling_weeks": [FiscalWeek(fiscal_week_name=week) for week in rolling_weeks],

                # 这是eoh aging模块的overall
                "eoh_aging_overall": [item for item in eoh_aging_list if item.sub_lob=='All'],
                # 这是eoh aging模块的sublob
                "eoh_aging_sublob": eoh_aging_sublob_list,
                # 这是eoh aging模块的nand view
                "eoh_aging_nand": eoh_aging_nd_list,
            }

            sender = Sender(self.email_cmd)
            sender.send(customized_data=CustomizedData(refresh_time, render_data))
        except Exception as e:
            logger.exception(e)
            raise e


def convert_overall(items: list[UbWoDsWklyItem], rtm_version) -> list[UbWoDsWklyItem]:
    if not items:
        return items

    def get_region(data_group_by_region):
        return [item for item in data_group_by_region if item.rtm == ALL and item.sub_rtm == ALL and item.sub_lob == ALL]

    def get_rtms(data_group_by_rtm):
        return [item for item in data_group_by_rtm if item.rtm != ALL and item.sub_rtm == ALL and item.sub_lob == ALL]

    def get_sub_rtms(data_group_by_rtm, rtm):
        return [item for item in data_group_by_rtm if item.rtm == rtm and item.sub_rtm != ALL and item.sub_lob == ALL]

    # 没有group, 直接筛选过滤汇总数据
    data_group_by_region = get_region(items)

    ret = []
    for region_item in data_group_by_region:
        item = region_item.as_dict()
        item.update({
            "level": "one",
            "field1": "China Channel",
        })
        ret.append(item)
        rtm_items = get_rtms(items)
        for rtm_item in rtm_items:
            rtm_item_origin = rtm_item.as_dict()
            level = "two"
            if rtm_version != "All":
                level = "one"
            rtm_item_origin.update({
                "level": level,
                "field1": rtm_item.rtm,
            })
            ret.append(rtm_item_origin)
            sub_rtm_items = get_sub_rtms(items, rtm_item.rtm)

            # eoh ub overall 不展示sub_rtm层级的rtm列表
            if rtm_item.rtm in NO_DISPLAY_SUB_RTM:
                continue
            for sub_rtm_item in sub_rtm_items:
                sub_rtm_item_origin = sub_rtm_item.as_dict()
                level = "three"
                if rtm_version != "All":
                    level = "two"
                sub_rtm_item_origin.update({
                    "level": level,
                    "field1": sub_rtm_item.sub_rtm,
                })
                ret.append(sub_rtm_item_origin)
    return sort_and_convert_data(ret)


def convert_sub_lob(items:list[UbWoDsWklyItem]) -> list[UbWoDsWklyItem]:
    if not items:
        return items

    def get_region(data_group_by_region):
        return [item for item in data_group_by_region if item.rtm == ALL and item.sub_rtm == ALL and item.sub_lob == ALL]

    def get_rtms(data_group_by_rtm):
        return [item for item in data_group_by_rtm if item.rtm != ALL and item.sub_rtm == ALL and item.sub_lob == ALL]

    def get_sub_rtms(data_group_by_rtm, rtm):
        return [item for item in data_group_by_rtm if item.rtm == rtm and item.sub_rtm != ALL and item.sub_lob == ALL]

    data_group_by_region = get_region(items)

    ret = []
    for region_item in data_group_by_region:
        print(region_item.as_dict())
        item = region_item.as_dict()
        item.update({
            "level": "one",
            "field1": "China Channel",
        })
        ret.append(item)
        rtm_items = get_rtms(items)
        for rtm_item in rtm_items:
            rtm_item_origin = rtm_item.as_dict()
            rtm_item_origin.update({
                "level": "one",
                "field1": rtm_item.rtm,
            })
            ret.append(rtm_item_origin)

            # eoh ub sub_lob view 不展示sub_rtm层级的rtm列表
            if rtm_item.rtm in NO_DISPLAY_SUB_RTM:
                for sub_lob_item in items:
                    if sub_lob_item.rtm == rtm_item.rtm and sub_lob_item.sub_rtm == rtm_item.sub_rtm and sub_lob_item.sub_lob != ALL:
                        sub_lob_item_origin = sub_lob_item.as_dict()
                        sub_lob_item_origin.update({
                            "level": "three",
                            "field1": sub_lob_item.sub_lob,
                        })
                        ret.append(sub_lob_item_origin)
                continue

            # 非edu的sub rtm下的sub lob 数据
            sub_rtm_items = get_sub_rtms(items, rtm_item.rtm)
            for sub_rtm_item in sub_rtm_items:
                sub_rtm_item_origin = sub_rtm_item.as_dict()
                sub_rtm_item_origin.update({
                    "level": "two",
                    "field1": sub_rtm_item.sub_rtm,
                })
                ret.append(sub_rtm_item_origin)
                for sub_lob_item in items:
                    if sub_lob_item.rtm == rtm_item.rtm and sub_lob_item.sub_rtm == sub_rtm_item.sub_rtm and sub_lob_item.sub_lob != ALL:
                        sub_lob_item_origin = sub_lob_item.as_dict()
                        sub_lob_item_origin.update({
                            "level": "three",
                            "field1": sub_lob_item.sub_lob,
                        })
                        ret.append(sub_lob_item_origin)
    return sort_and_convert_data(ret)


def convert_eoh_aging_overall(items: list[CDCInventoryMetric])->list[dict]:
    if not items:
        raise Exception("No eoh_aging items found")

    ret = [{
        "level": "one",
        "field1": "China Channel",
    }]
    eoh_aging_overall = [item for item in items if item.sub_lob=='All']
    for item in eoh_aging_overall:
        m = item.as_dict()
        m.update({
            "level": item.level(),
            "field1": item.first_column(),
        })
        ret.append(m)
    return sort_and_convert_data(ret)


def convert_eoh_aging_sublob(items: list[CDCInventoryMetric]) -> list[dict]:
    if not items:
        raise Exception("No eoh_aging items found")

    ret = [{
        "level": "one",
        "field1": "China Channel",
    }]
    for item in items:
        m = item.as_dict()
        m.update({
            "level": item.level(),
            "field1": item.first_column(),
        })
        ret.append(m)
    return sort_and_convert_data(ret)


def sort_and_convert_data(data):
    # data = sort_data(data)
    # data = convert_rtm(data)
    return data


def sort_data(data):
    sub_lob_rules = SUB_LOB_RULES
    rtms_rules = RTMS_RULES
    sub_rtms_rules = ["All", "Lifestyle", "Mono", "OTC", "Township", "Mass Merchant", "Duty-free", "CM", "CU", "CT",
                      "CB"]
    data = sorted(data, key=lambda x: (
        rtms_rules.index(x.get('rtm')) if x.get('rtm') in rtms_rules else 999,
        sub_rtms_rules.index(x.get('sub_rtm')) if x.get('sub_rtm') in sub_rtms_rules else 999,
        sub_lob_rules.index(x.get('sub_lob')) if x.get('sub_lob') in sub_lob_rules else 999
    ))
    return data


def get_region_sub_lob(items:list[UbWoDsWklyItem]) -> list[dict]:
    """gc下边的单个sub_lob的汇总信息"""
    if not items:
        return items

    def get_region_sub_lob(data_group_by_region):
        return [item.as_dict() for item in data_group_by_region if item.rtm == ALL and item.sub_rtm == ALL and item.sub_lob != ALL]

    return sort_and_convert_data(get_region_sub_lob(items))

