from concurrent.futures import Thread<PERSON><PERSON><PERSON>xecutor
import json
from datetime import datetime, timedelta
from data.databend.dashboard.daily_ub_tracking_summary import DailyUbTrackingSummaryDi
from data.databend.dashboard.weekly_ub_tracking_summary import WeeklyUbTrackingSummaryDi
from data.databend.email_report.daily_nd_tracking_summary import DailyNdTrackingSummaryDi
from data.databend.email_report.weekly_nd_tracking_summary import WeeklyNdTrackingSummaryDi
from data.fiscal_year_week import FiscalYearWeek
from domain.dashboard.entity.anti_fraud.anti_fraud_so_ub import RTM_ENT, RTMS_SORT_RULES, SUB_LOB_SORT_RULES, \
    SUB_RTMS_SORT_RULES, DailyItem, DailyNdItem, WeeklyItem, RTM_MULTI, RTM_Mono, RTM_CARRIER, \
    ONLINE, OFFLINE, WeeklyNdItem, UB_NO_DISPLAY_SUB_RTM, R<PERSON>_EDU
from domain.dashboard.entity.fiscal_week import <PERSON>scal<PERSON>eek
from domain.dashboard.entity.ub_recall import U<PERSON><PERSON><PERSON><PERSON>Reca<PERSON>, UbWeekRecall, UbNdDailyRecall, UbNdWeekRecall
from kit.email.sender import Sender, CustomizedData

from util.conf import logger
from util.const import ALL
from util.mail_conf import get_ali_system_link


class AntifraudSoUBEmailWorker:
    # 跳转链接 20250319增加
    OVERALL_VIEW_JUMP_WORKSPACE_LINK = "/channel-compliance/abnormal-detectors?nav=pos_ub_tracking&view=partner&view_tab=overall"
    PLATFORM_VIEW_JUMP_WORKSPACE_LINK = "/channel-compliance/abnormal-detectors?nav=pos_ub_tracking&view=online_platform&view_tab=rtm"
    ND_VIEW_JUMP_WORKSPACE_LINK = "/channel-compliance/abnormal-detectors?nav=pos_ub_tracking&view=partner&view_tab=overall"
    SUB_LOB_VIEW_JUMP_WORKSPACE_LINK = "/channel-compliance/abnormal-detectors?nav=pos_ub_tracking&view=sub_lob&view_tab=rtm"
    PREFIXES_ENTER_FROM = '&'  # &符号的编码

    def __init__(self, params: str):
        # 配置在数据库中的是json字符串
        if params is None or "email_cmd" not in params or "rtm_version" not in params:
            logger.error("no param configed of AntifraudSoUBEmailWorker.")
            raise Exception("param is required")
        params = json.loads(params)
        self.email_cmd = params.get("email_cmd")

        self.rtm_version = params.get("rtm_version")
        self.params = params

    @staticmethod
    def _is_cross_quarter(now: str, quarter_begin_dt: datetime, default_offset_day=1):
        """
        now: 当前日期
        quarter_begin_dt: 当前日期所在季度的开始日期
        """
        # 针对t-2数据, 跨季度时, online和edu无数据(跨季度的第二天)
        quarter_begin_dt_for_email = quarter_begin_dt + timedelta(days=default_offset_day)
        return now == quarter_begin_dt_for_email.strftime('%Y-%m-%d')

    def do(self):
        try:
            now = datetime.now()
            # now = datetime.now() + timedelta(days=1) # 调试使用
            current_date = now.strftime('%Y-%m-%d')

            # 当前日期所在季度(跨quarter季度)
            fiscal_obj = FiscalYearWeek.get_fis_by_date(date=current_date)
            current_quarter = fiscal_obj.fiscal_qtr_year_name
            quarter_begin_dt = fiscal_obj.quarter_begin_dt
            is_cross_quarter = self._is_cross_quarter(now=current_date, quarter_begin_dt=quarter_begin_dt)

            t_1_date = (now - timedelta(days=1)).strftime('%Y-%m-%d')
            t_2_date = (now - timedelta(days=2)).strftime('%Y-%m-%d')   # 用于daliy的online数据
            # 数据刷新时间固定就是t-1, 如果没有数据就需要报错，且不要发邮件
            refresh_time = datetime.strptime(t_1_date, '%Y-%m-%d')
            rolling_weeks, d_minus1_weeks = get_weeks(current_date, t_1_date)

            # 动态获取所有ND 数据
            nd_data = get_all_nd_data(weekly_snapshot_date_list=[current_date, t_1_date], weeks=rolling_weeks, snapshot_date=current_date)

            # ----------overall-------------------------
            # 根据rtm-sub_rtm-sub_lob构造的 daily 数据，20241016改为使用snapshot_date取当前日期的数据就行
            data = DailyUbTrackingSummaryDi.query_daily_ub_summary_records(snapshot_date=current_date,
                                                                           is_cross_quarter=is_cross_quarter,
                                                                           current_quarter=current_quarter)
            futures_weekly_summary = []
            pool_weekly_summary = ThreadPoolExecutor(max_workers=len(rolling_weeks))
            for week in rolling_weeks:
                futures_weekly_summary.append(
                    pool_weekly_summary.submit(WeeklyUbTrackingSummaryDi.query_week_ub_summary_records, current_date, week)
                )
            results_weekly_summary = [future.result() for future in futures_weekly_summary]
            pool_weekly_summary.shutdown()
            weekly_data_cw1 = results_weekly_summary[0]
            weekly_data_cw2 = results_weekly_summary[1]
            weekly_data_cw3 = results_weekly_summary[2]
            weekly_data_cw4 = results_weekly_summary[3]
            weekly_data_cw5 = results_weekly_summary[4]

            futures_weekly_summary_d_minus_1 = []
            pool_weekly_summary_d_minus_1 = ThreadPoolExecutor(max_workers=len(d_minus1_weeks))
            for week in d_minus1_weeks:
                futures_weekly_summary_d_minus_1.append(
                    pool_weekly_summary_d_minus_1.submit(WeeklyUbTrackingSummaryDi.query_week_ub_summary_records, t_1_date, week)
                )
            results_weekly_summary_d_minus_1 = [future.result() for future in futures_weekly_summary_d_minus_1]
            pool_weekly_summary_d_minus_1.shutdown()
            weekly_data_d1_cw1 = results_weekly_summary_d_minus_1[0]
            weekly_data_d1_cw2 = None
            if len(d_minus1_weeks) == 2:
                weekly_data_d1_cw2 = results_weekly_summary_d_minus_1[1]

            # ----------sublob------------------------
            # 20241016改为使用snapshot_date取当前日期的数据就行
            daily_data1 = DailyUbTrackingSummaryDi.query_region_daily_ub_summary_records(
                snapshot_date=current_date, is_cross_quarter=is_cross_quarter, current_quarter=current_quarter
            )
            futures_region_week_summarys = []
            pool_weekly_summary = ThreadPoolExecutor(max_workers=len(rolling_weeks))
            for week in rolling_weeks:
                futures_region_week_summarys.append(
                    pool_weekly_summary.submit(WeeklyUbTrackingSummaryDi.query_region_week_ub_summary_records, current_date, week)
                )
            results_region_weekly_summary = [future.result() for future in futures_region_week_summarys]
            pool_weekly_summary.shutdown()
            # 查询rtm=all，按照sublob聚合的数据
            week_data1 = results_region_weekly_summary[0]
            week_data2 = results_region_weekly_summary[1]
            week_data3 = results_region_weekly_summary[2]
            week_data4 = results_region_weekly_summary[3]
            week_data5 = results_region_weekly_summary[4]

            futures_region_d1_summarys = []
            pool_region_d1 = ThreadPoolExecutor(max_workers=len(d_minus1_weeks))
            for week in d_minus1_weeks:
                futures_region_d1_summarys.append(
                    pool_region_d1.submit(WeeklyUbTrackingSummaryDi.query_region_week_ub_summary_records, t_1_date, week)
                )
            results_region_d1_summary = [future.result() for future in futures_region_d1_summarys]
            pool_region_d1.shutdown()
            week_data_d1_w1 = results_region_d1_summary[0]
            week_data_d1_w2 = None
            if len(d_minus1_weeks) == 2:
                week_data_d1_w2 = results_region_d1_summary[1]

            # ----------platform------------------------
            # 20241016改为使用snapshot_date取当前日期的数据就行
            platform_data = DailyUbTrackingSummaryDi.query_platform_daily_ub_summary_records(
                snapshot_date=current_date, is_cross_quarter=is_cross_quarter, current_quarter=current_quarter
            )
            platform_rolling_pool = ThreadPoolExecutor(max_workers=len(rolling_weeks))
            platform_futures = []
            for week in rolling_weeks:
                platform_futures.append(
                    platform_rolling_pool.submit(WeeklyUbTrackingSummaryDi.query_platform_week_ub_summary_records,current_date,week)
                )
            platform_week_results = [future.result() for future in platform_futures]
            platform_rolling_pool.shutdown()
            platform_weekly_data_cw1 = platform_week_results[0]
            platform_weekly_data_cw2 = platform_week_results[1]
            platform_weekly_data_cw3 = platform_week_results[2]
            platform_weekly_data_cw4 = platform_week_results[3]
            platform_weekly_data_cw5 = platform_week_results[4]

            platform_d1_pool = ThreadPoolExecutor(max_workers=len(d_minus1_weeks))
            platform_d1_futures = []
            for d_minus1_week in d_minus1_weeks:
                platform_d1_futures.append(
                    platform_d1_pool.submit(
                        WeeklyUbTrackingSummaryDi.query_platform_week_ub_summary_records, t_1_date, d_minus1_week
                    )
                )
            platform_d1_results = [future.result() for future in platform_d1_futures]
            platform_d1_pool.shutdown()
            platform_weekly_data_d1_cw1 = platform_d1_results[0]
            platform_weekly_data_d1_cw2 = None
            if len(d_minus1_weeks) == 2:
                platform_weekly_data_d1_cw2 = platform_d1_results[1]

            convert_daily_data = convert_daily(data, self.rtm_version)
            convert_week_cw1_data = convert_week(weekly_data_cw1)
            
            # -----ND/T1 View------
            has_nd_t1_view = True
            if self.rtm_version == 'Carrier':
                has_nd_t1_view = False
            # QTD 从daily表中取t-1日期的数据,20241006更改为：使用snapshot_date取当前日期的数据就行
            # weekly trend 从weekly表中取当天日期的数据 / d-1 从weekly表中取t-1日期的数据
            nd_t1_daily_data = DailyNdTrackingSummaryDi.query_daily_nd_summary_records(
                snapshot_date=current_date, is_cross_quarter=is_cross_quarter, current_quarter=current_quarter,
                nd_data=nd_data
            )
            nd_t1_rolling_pool = ThreadPoolExecutor(max_workers=len(rolling_weeks))
            nd_t1_futures = []
            for week in rolling_weeks:
                nd_t1_futures.append(
                    nd_t1_rolling_pool.submit(WeeklyNdTrackingSummaryDi.query_week_nd_summary_records,current_date,week, nd_data)
                )
            nd_t1_week_results = [future.result() for future in nd_t1_futures]
            nd_t1_rolling_pool.shutdown()
            nd_t1_weekly_data_cw1 = nd_t1_week_results[0]
            nd_t1_weekly_data_cw2 = nd_t1_week_results[1]
            nd_t1_weekly_data_cw3 = nd_t1_week_results[2]
            nd_t1_weekly_data_cw4 = nd_t1_week_results[3]
            nd_t1_weekly_data_cw5 = nd_t1_week_results[4]

            nd_t1_d1_pool = ThreadPoolExecutor(max_workers=len(d_minus1_weeks))
            nd_t1_d1_futures = []
            for d_minus1_week in d_minus1_weeks:
                nd_t1_d1_futures.append(
                    nd_t1_d1_pool.submit(
                        WeeklyNdTrackingSummaryDi.query_week_nd_summary_records, t_1_date, d_minus1_week, nd_data
                    )
                )
            nd_t1_d1_results = [future.result() for future in nd_t1_d1_futures]
            nd_t1_d1_pool.shutdown()
            nd_t1_weekly_data_d1_cw1 = nd_t1_d1_results[0]
            nd_t1_weekly_data_d1_cw2 = None
            if len(d_minus1_weeks) == 2:
                nd_t1_weekly_data_d1_cw2 = nd_t1_d1_results[1]

            if len(convert_daily_data) == 0 or len(convert_week_cw1_data) == 0:
                raise Exception(f"convert daily data or covert week cw1 data is empty")

            # 取2个季度的季度名称, 从ub daily表取一条数据就行
            last_quarter = data[0].fiscal_qtr_year_name_lq
            current_quarter = data[0].fiscal_qtr_year_name

            if last_quarter is None:
                raise Exception(f"ub daily has no last quarter data.")

            render_data = {
                "last_quarter": last_quarter,
                "current_quarter": current_quarter,
                "data": convert_daily_data,
                "weekly_data_cw1": convert_week_cw1_data,
                "weekly_data_cw2": convert_week(weekly_data_cw2),
                "weekly_data_cw3": convert_week(weekly_data_cw3),
                "weekly_data_cw4": convert_week(weekly_data_cw4),
                "weekly_data_cw5": convert_week(weekly_data_cw5),
                "weekly_data_d1_cw1": convert_week(weekly_data_d1_cw1),
                "weekly_data_d1_cw2": convert_week(weekly_data_d1_cw2),
                "sub_lob_data": convert_sub_lob(data),
                "rtm_version": self.rtm_version,
                "sub_lob_weekly_data_cw1": convert_sub_lob_weekly(weekly_data_cw1),
                "sub_lob_weekly_data_cw2": convert_sub_lob_weekly(weekly_data_cw2),
                "sub_lob_weekly_data_cw3": convert_sub_lob_weekly(weekly_data_cw3),
                "sub_lob_weekly_data_cw4": convert_sub_lob_weekly(weekly_data_cw4),
                "sub_lob_weekly_data_cw5": convert_sub_lob_weekly(weekly_data_cw5),
                "sub_lob_weekly_data_d1_cw1": convert_sub_lob_weekly(weekly_data_d1_cw1),
                "sub_lob_weekly_data_d1_cw2": convert_sub_lob_weekly(weekly_data_d1_cw2),
                "daily_data1": daily_data1,
                "week_data1": week_data1,
                "week_data2": week_data2,
                "week_data3": week_data3,
                "week_data4": week_data4,
                "week_data5": week_data5,
                "week_data_d1_w1": week_data_d1_w1,
                "week_data_d1_w2": week_data_d1_w2,

                "platform_data": convert_platform_daily(platform_data, self.rtm_version),
                "platform_weekly_data_cw1": convert_platform_week(platform_weekly_data_cw1),
                "platform_weekly_data_cw2": convert_platform_week(platform_weekly_data_cw2),
                "platform_weekly_data_cw3": convert_platform_week(platform_weekly_data_cw3),
                "platform_weekly_data_cw4": convert_platform_week(platform_weekly_data_cw4),
                "platform_weekly_data_cw5": convert_platform_week(platform_weekly_data_cw5),
                "platform_weekly_data_d1_cw1": convert_platform_week(platform_weekly_data_d1_cw1),
                "platform_weekly_data_d1_cw2": convert_platform_week(platform_weekly_data_d1_cw2),
                "rolling_weeks": [FiscalWeek(fiscal_week_name=fiscal_week_name) for fiscal_week_name in rolling_weeks],
                "day_weeks": [FiscalWeek(fiscal_week_name=fiscal_week_name) for fiscal_week_name in d_minus1_weeks],
                
                "has_nd_t1_view": has_nd_t1_view,
                "nd_t1_daily_data": convert_nd_t1_daily(nd_t1_daily_data, self.rtm_version),
                "nd_t1_weekly_data_cw1": convert_nd_t1_week(nd_t1_weekly_data_cw1),
                "nd_t1_weekly_data_cw2": convert_nd_t1_week(nd_t1_weekly_data_cw2),
                "nd_t1_weekly_data_cw3": convert_nd_t1_week(nd_t1_weekly_data_cw3),
                "nd_t1_weekly_data_cw4": convert_nd_t1_week(nd_t1_weekly_data_cw4),
                "nd_t1_weekly_data_cw5": convert_nd_t1_week(nd_t1_weekly_data_cw5),
                "nd_t1_weekly_data_d1_cw1": convert_nd_t1_week(nd_t1_weekly_data_d1_cw1),
                "nd_t1_weekly_data_d1_cw2": convert_nd_t1_week(nd_t1_weekly_data_d1_cw2),
                "overall_view_jump_workspace_link": get_ali_system_link(system_link=self.OVERALL_VIEW_JUMP_WORKSPACE_LINK,
                                                                        prefixes_enter_from=self.PREFIXES_ENTER_FROM),
                "platform_view_jump_workspace_link": get_ali_system_link(system_link=self.PLATFORM_VIEW_JUMP_WORKSPACE_LINK,
                                                                         prefixes_enter_from=self.PREFIXES_ENTER_FROM),
                "nd_view_jump_workspace_link": get_ali_system_link(system_link=self.ND_VIEW_JUMP_WORKSPACE_LINK,
                                                                   prefixes_enter_from=self.PREFIXES_ENTER_FROM),
                "sub_lob_view_jump_workspace_link": get_ali_system_link(system_link=self.SUB_LOB_VIEW_JUMP_WORKSPACE_LINK,
                                                                        prefixes_enter_from=self.PREFIXES_ENTER_FROM),
            }

            sender = Sender(self.email_cmd)
            sender.send(customized_data=CustomizedData(refresh_time, render_data))
        except Exception as e:
            logger.exception(e)
            raise e


def get_all_nd_data(weekly_snapshot_date_list: list[str], weeks: list[str], snapshot_date: str) -> dict:
    """获取本次发送邮件对应的所有nd 数据"""
    nd_data_result = {}

    def add_item(nd_item):
        rtm_info = nd_data_result.setdefault(nd_item.get("rtm"), {})
        sub_rtm_info = rtm_info.setdefault(nd_item.get("sub_rtm"), {})
        nd_type_info_list = sub_rtm_info.setdefault(nd_item.get("nd_type"), set())
        nd_type_info_list.add(nd_item.get("hq_name"))

    # 周的ND
    weekly_records = WeeklyNdTrackingSummaryDi.query_rtm_sub_rtm_nd_records(snapshot_date_list=weekly_snapshot_date_list, fiscal_weeks=weeks)
    for item in weekly_records:
        add_item(item)

    # 日的ND
    daily_records = DailyNdTrackingSummaryDi.query_rtm_sub_rtm_nd_records(snapshot_date=snapshot_date)
    for item in daily_records:
        add_item(item)
    return nd_data_result


def get_weeks(snapshot_date: str, t_1_date: str) -> tuple[list, list]:
    rolling_weeks = WeeklyUbTrackingSummaryDi.query_rolling_weeks(snapshot_date)
    
    # d-1中使用的是t-1日期的最近两周，这样就能在周二～周日展示2周，周一展示1周
    t_1_datetime = datetime.strptime(t_1_date, "%Y-%m-%d")
    d_minus1_weeks = FiscalYearWeek.get_rolling_n_weeks_by_date(t_1_datetime)
    
    # 查询t-1日期下数据库中是否有对应周的数据
    d1_rolling_weeks = WeeklyUbTrackingSummaryDi.query_rolling_weeks(t_1_date)
    
    # 已数据库中的周为准，如果数据库只有一周的数，就显示一周
    d_minus1_weeks = [week for week in d_minus1_weeks if week in d1_rolling_weeks]
    
    return rolling_weeks, d_minus1_weeks


def convert_daily(items:list[DailyItem], rtm_version:str) -> list[DailyItem]:
    # group
    data_group_by_region = UbDailyRecall.group_by_region(items)
    data_group_by_rtm = UbDailyRecall.group_by_rtm(items)
    data_group_by_sub_rtm = UbDailyRecall.group_by_sub_rtm(items)

    def get_by_region(data_group_by_region, region):
        return data_group_by_region

    def get_by_rtm(data_group_by_rtm, rtm):
        return [item for item in data_group_by_rtm if item.rtm == rtm]

    def get_by_sub_rtm(data_group_by_sub_rtm, rtm):
        return [item for item in data_group_by_sub_rtm if item.rtm == rtm]

    ret = []
    for region_item in data_group_by_region:
        print(region_item.as_dict())
        item = region_item.as_dict()
        item.update({
            "level": "one",
            "field1": "China Channel",
        })
        ret.append(item)
        rtm_items = get_by_region(data_group_by_rtm, None)
        for rtm_item in rtm_items:
            rtm_item_origin = rtm_item.as_dict()
            level = "two"
            if rtm_version != "All":
                level = "one"
            rtm_item_origin.update({
                "level": level,
                "field1": rtm_item.rtm,
            })
            ret.append(rtm_item_origin)
            print(rtm_item.as_dict())
            # UB overall mail 不展示sub_rtm层级的rtm列表
            if rtm_item.rtm in UB_NO_DISPLAY_SUB_RTM:
                continue
            sub_rtm_items = get_by_sub_rtm(data_group_by_sub_rtm, rtm_item.rtm)
            for sub_rtm_item in sub_rtm_items:
                sub_rtm_item_origin = sub_rtm_item.as_dict()
                level = "three"
                if rtm_version != "All":
                    level = "two"
                sub_rtm_item_origin.update({
                    "level": level,
                    "field1": sub_rtm_item.sub_rtm,
                })
                ret.append(sub_rtm_item_origin)
                print(sub_rtm_item.as_dict())
    return sort_and_convert_data(ret)


def convert_platform_daily(items:list[DailyItem], rtm_version:str) -> list[DailyItem]:
    # group
    data_group_by_region = UbDailyRecall.group_by_region(items)
    data_group_by_rtm = UbDailyRecall.group_by_rtm(items)
    data_group_by_sub_rtm = UbDailyRecall.group_by_sub_rtm(items)

    # 一级china 使用 获取 online offline数据
    data_group_by_region_online_offline = UbDailyRecall.group_by_region_online_offline(items)
    # # 一级china 使用 获取 online 下的 platform聚合数据
    data_group_by_region_online_platform = UbDailyRecall.group_by_region_platform(items)
    # 四级 rtm subrtm online_offline
    data_group_by_sub_rtm_online_offline = UbDailyRecall.group_by_sub_rtm_online_offline(items)
    # 五级 rtm subrtm online_offline platform
    data_group_by_online_offline_platfrom = UbDailyRecall.group_by_online_offline_platform(items)


    def get_by_region(data_group_by_region, region):
        return data_group_by_region

    def get_by_rtm(data_group_by_rtm, rtm):
        return [item for item in data_group_by_rtm if item.rtm == rtm]

    def get_by_sub_rtm(data_group_by_sub_rtm, rtm):
        return [item for item in data_group_by_sub_rtm if item.rtm == rtm]

    def get_by_online_offline(data_group_by_region_online_offline, online_offline):
        return [item for item in data_group_by_region_online_offline if item.online_offline == online_offline]

    def get_online_offline_by_sub_rtm_(data_group_by_sub_rtm_online_offline, sub_rtm):
        return [item for item in data_group_by_sub_rtm_online_offline if sub_rtm == item.sub_rtm]

    def get_platform_by_sub_rtm_online_offline(data_group_by_online_offline_platfrom, sub_rtm, online_offline):
        return [item for item in data_group_by_online_offline_platfrom if sub_rtm == item.sub_rtm and item.online_offline == online_offline]

    ret = []
    for region_item in data_group_by_region:
        print(region_item.as_dict())
        item = region_item.as_dict()
        item.update({
            "level": "one",
            "field1": "China Channel",
        })
        ret.append(item)
        # 添加一级online数据
        region_onlines = get_by_online_offline(data_group_by_region_online_offline, ONLINE)
        for region_online in region_onlines:
            region_online_ori = region_online.as_dict()
            region_online_ori.update({
                "level": "three",
                "field1": ONLINE,
            })
            ret.append(region_online_ori)
        # 添加一级online下 platform数据
        for region_online_platform in data_group_by_region_online_platform:
            region_online_platform_ori = region_online_platform.as_dict()
            region_online_platform_ori.update({
                "level": "four",
                "field1": region_online_platform.platform,
            })
            ret.append(region_online_platform_ori)
        # 添加一级offline数据
        region_offlines = get_by_online_offline(data_group_by_region_online_offline, OFFLINE)
        for region_offline in region_offlines:
            region_offline_as_dict = region_offline.as_dict()
            region_offline_as_dict.update({
                "level": "three",
                "field1": OFFLINE,
            })
            ret.append(region_offline_as_dict)

        rtm_items = get_by_region(data_group_by_rtm, None)
        for rtm_item in rtm_items:
            rtm_item_origin = rtm_item.as_dict()
            level = "one"
            rtm_item_origin.update({
                "level": level,
                "field1": rtm_item.rtm,
            })
            ret.append(rtm_item_origin)
            print(rtm_item.as_dict())
            sub_rtm_items = get_by_sub_rtm(data_group_by_sub_rtm, rtm_item.rtm)
            for sub_rtm_item in sub_rtm_items:
                sub_rtm_item_origin = sub_rtm_item.as_dict()
                level = "two"
                if rtm_version != "All":
                    level = "one"
                # EDU 特殊处理，不展示sub_rtm层级的数据
                # todo 这里也要加一个 ENT的
                if sub_rtm_item.sub_rtm not in ['EDU', 'Channel Online', 'ENT']:
                    sub_rtm_item_origin.update({
                        "level": level,
                        "field1": sub_rtm_item.sub_rtm,
                    })
                    ret.append(sub_rtm_item_origin)
                    print(sub_rtm_item.as_dict())
                # online offline
                online_offline_items = get_online_offline_by_sub_rtm_(data_group_by_sub_rtm_online_offline,sub_rtm_item.sub_rtm)
                for online_offline_item in online_offline_items:
                    online_offline_item_ori = online_offline_item.as_dict()
                    level = "three"
                    if rtm_version != "All":
                        level = "two"
                    online_offline_item_ori.update({
                        "level": level,
                        "field1": online_offline_item.online_offline,
                    })
                    ret.append(online_offline_item_ori)
                    print(online_offline_item_ori)
                    # online的platform数据
                    if online_offline_item.online_offline == ONLINE:
                        platform_items = get_platform_by_sub_rtm_online_offline(data_group_by_online_offline_platfrom,sub_rtm_item.sub_rtm, ONLINE)
                        # todo 针对某个sub_rtm下只有一个platform的情况,直接不添加了
                        if len(platform_items) == 1:
                            continue
                        for platform_item in platform_items:
                            platform_item_ori = platform_item.as_dict()
                            level = "four"
                            if rtm_version != "All":
                                level = "three"
                            platform_item_ori.update({
                                "level": level,
                                "field1": platform_item.platform,
                            })
                            ret.append(platform_item_ori)
                            print(platform_item_ori)
    return sort_and_convert_data(ret)


def convert_platform_week(items: list[WeeklyItem]) -> list[WeeklyItem]:
    if not items:
        return items
    # group
    data_group_by_region = UbWeekRecall.group_by_region(items)
    data_group_by_rtm = UbWeekRecall.group_by_rtm(items)
    data_group_by_sub_rtm = UbWeekRecall.group_by_sub_rtm(items)

    # 一级china 使用 获取 online offline数据
    data_group_by_region_online_offline = UbWeekRecall.group_by_region_online_offline(items)
    # # 一级china 使用 获取 online 下的 platform聚合数据
    data_group_by_region_online_platform = UbWeekRecall.group_by_region_platform(items)
    # 四级 rtm subrtm online_offline
    data_group_by_sub_rtm_online_offline = UbWeekRecall.group_by_sub_rtm_online_offline(items)
    # 五级 rtm subrtm online_offline platform
    data_group_by_online_offline_platfrom = UbWeekRecall.group_by_online_offline_platform(items)

    def get_by_region(data_group_by_region, region):
        return data_group_by_region

    def get_by_rtm(data_group_by_rtm, rtm):
        return [item for item in data_group_by_rtm if item.rtm == rtm]

    def get_by_sub_rtm(data_group_by_sub_rtm, rtm):
        return [item for item in data_group_by_sub_rtm if item.rtm == rtm]

    def get_by_online_offline(data_group_by_region_online_offline, online_offline):
        return [item for item in data_group_by_region_online_offline if item.online_offline == online_offline]

    def get_online_offline_by_sub_rtm_(data_group_by_sub_rtm_online_offline, sub_rtm):
        return [item for item in data_group_by_sub_rtm_online_offline if sub_rtm == item.sub_rtm]

    def get_platform_by_sub_rtm_online_offline(data_group_by_online_offline_platfrom, sub_rtm, online_offline):
        return [item for item in data_group_by_online_offline_platfrom if sub_rtm == item.sub_rtm and item.online_offline == online_offline]

    ret = []
    for region_item in data_group_by_region:
        print(region_item.as_dict())
        item = region_item.as_dict()
        item.update({
            "level": "one",
            "field1": "China Channel",
        })
        ret.append(item)
        # 添加一级online数据
        region_onlines = get_by_online_offline(data_group_by_region_online_offline, ONLINE)
        for region_online in region_onlines:
            region_online_ori = region_online.as_dict()
            region_online_ori.update({
                "level": "two",
                "field1": ONLINE,
            })
            ret.append(region_online_ori)
        # 添加一级online下 platform数据
        for region_online_platform in data_group_by_region_online_platform:
            region_online_platform_ori = region_online_platform.as_dict()
            region_online_platform_ori.update({
                "level": "three",
                "field1": region_online_platform.platform,
            })
            ret.append(region_online_platform_ori)
        # 添加一级offline数据
        region_offlines = get_by_online_offline(data_group_by_region_online_offline, OFFLINE)
        for region_offline in region_offlines:
            region_offline_as_dict = region_offline.as_dict()
            region_offline_as_dict.update({
                "level": "two",
                "field1": OFFLINE,
            })
            ret.append(region_offline_as_dict)

        rtm_items = get_by_region(data_group_by_rtm, None)
        for rtm_item in rtm_items:
            rtm_item_origin = rtm_item.as_dict()
            rtm_item_origin.update({
                "level": "one",
                "field1": rtm_item.rtm,
            })
            ret.append(rtm_item_origin)
            print(rtm_item.as_dict())
            sub_rtm_items = get_by_sub_rtm(data_group_by_sub_rtm, rtm_item.rtm)
            for sub_rtm_item in sub_rtm_items:
                sub_rtm_item_origin = sub_rtm_item.as_dict()
                # EDU 特殊处理，不展示sub_rtm层级的数据
                # todo 这里也要加一个 ENT的
                if sub_rtm_item.sub_rtm not in ['EDU', 'Channel Online', 'ENT']:
                    sub_rtm_item_origin.update({
                        "level": "two",
                        "field1": sub_rtm_item.sub_rtm,
                    })
                    ret.append(sub_rtm_item_origin)
                    print(sub_rtm_item.as_dict())
                # online offline
                online_offline_items = get_online_offline_by_sub_rtm_(data_group_by_sub_rtm_online_offline,sub_rtm_item.sub_rtm)
                for online_offline_item in online_offline_items:
                    online_offline_item_ori = online_offline_item.as_dict()
                    level = "three"
                    online_offline_item_ori.update({
                        "level": level,
                        "field1": online_offline_item.online_offline,
                    })
                    ret.append(online_offline_item_ori)
                    print(online_offline_item_ori)
                    # online的platform数据
                    if online_offline_item.online_offline == ONLINE:
                        platform_items = get_platform_by_sub_rtm_online_offline(data_group_by_online_offline_platfrom,sub_rtm_item.sub_rtm, ONLINE)
                        # todo 针对某个sub_rtm下只有一个platform的情况,直接不添加了
                        if len(platform_items) == 1:
                            continue
                        for platform_item in platform_items:
                            platform_item_ori = platform_item.as_dict()
                            level = "four"
                            platform_item_ori.update({
                                "level": level,
                                "field1": platform_item.platform,
                            })
                            ret.append(platform_item_ori)
                            print(platform_item_ori)
    return sort_and_convert_data(ret)


def convert_nd_t1_daily(items:list[DailyNdItem], rtm_version:str) -> list[DailyNdItem]:
    # group
    data_group_by_rtm = UbNdDailyRecall.group_by_rtm(items)
    data_group_by_sub_rtm = UbNdDailyRecall.group_by_sub_rtm(items)
    data_group_by_ndt1 = UbNdDailyRecall.group_by_ndt1(items)

    def get_by_sub_rtm(data_group_by_sub_rtm, rtm):
        return [item for item in data_group_by_sub_rtm if item.rtm == rtm]
    
    def get_by_ndt1(data_group_by_ndt1, rtm, sub_rtm):
        return [item for item in data_group_by_ndt1 if item.rtm == rtm and item.sub_rtm == sub_rtm]

    ret = []
    
    # 汇总rtm级别的数据
    for rtm_item in data_group_by_rtm:
        rtm_item_origin = rtm_item.as_dict()
        rtm_item_origin.update({
            "level": "one",
            "field1": rtm_item.rtm,
        })
        ret.append(rtm_item_origin)

        # EDU、ENT 不展示sub_rtm, 直接展示HQ
        if rtm_item.rtm in [RTM_EDU, RTM_ENT]:
            # 汇总ndt1级别的数据
            ndt1_items = get_by_ndt1(data_group_by_ndt1, rtm_item.rtm, rtm_item.rtm)
            for ndt1_item in ndt1_items:
                ndt1_item_origin = ndt1_item.as_dict()
                ndt1_item_origin.update({
                    "level": "three",
                    "field1": ndt1_item.hq_name,
                })
                ret.append(ndt1_item_origin)
            continue

        # 汇总sub_rtm级别的数据
        sub_rtm_items = get_by_sub_rtm(data_group_by_sub_rtm, rtm_item.rtm)
        for sub_rtm_item in sub_rtm_items:
            sub_rtm_item_origin = sub_rtm_item.as_dict()
            sub_rtm_item_origin.update({
                "level": "two",
                "field1": sub_rtm_item.sub_rtm,
            })
            ret.append(sub_rtm_item_origin)
            # 汇总ndt1级别的数据
            ndt1_items = get_by_ndt1(data_group_by_ndt1, rtm_item.rtm, sub_rtm_item.sub_rtm)
            for ndt1_item in ndt1_items:
                ndt1_item_origin =ndt1_item.as_dict()
                ndt1_item_origin.update({
                    "level": "three",
                    "field1":ndt1_item.hq_name,
                })
                ret.append(ndt1_item_origin)
    
    return sort_nd_data(ret)


def convert_nd_t1_week(items: list[WeeklyNdItem]) -> list[WeeklyNdItem]:
    if not items:
        return items
    # group
    data_group_by_rtm = UbNdWeekRecall.group_by_rtm(items)
    data_group_by_sub_rtm = UbNdWeekRecall.group_by_sub_rtm(items)
    data_group_by_ndt1 = UbNdWeekRecall.group_by_ndt1(items)

    def get_by_sub_rtm(data_group_by_sub_rtm, rtm):
        return [item for item in data_group_by_sub_rtm if item.rtm == rtm]
    
    def get_by_ndt1(data_group_by_ndt1, rtm, sub_rtm):
        return [item for item in data_group_by_ndt1 if item.rtm == rtm and item.sub_rtm == sub_rtm]

    ret = []
    
    # 汇总rtm级别的数据
    for rtm_item in data_group_by_rtm:
        rtm_item_origin = rtm_item.as_dict()
        rtm_item_origin.update({
            "level": "one",
            "field1": rtm_item.rtm,
        })
        ret.append(rtm_item_origin)

        # EDU、ENT不展示sub_rtm, 直接展示HQ
        if rtm_item.rtm in [RTM_EDU, RTM_ENT]:
            # 汇总ndt1级别的数据
            ndt1_items = get_by_ndt1(data_group_by_ndt1, rtm_item.rtm, rtm_item.rtm)
            for ndt1_item in ndt1_items:
                ndt1_item_origin = ndt1_item.as_dict()
                ndt1_item_origin.update({
                    "level": "three",
                    "field1": ndt1_item.hq_name,
                })
                ret.append(ndt1_item_origin)
            continue

        # 汇总sub_rtm级别的数据
        sub_rtm_items = get_by_sub_rtm(data_group_by_sub_rtm, rtm_item.rtm)
        for sub_rtm_item in sub_rtm_items:
            sub_rtm_item_origin = sub_rtm_item.as_dict()
            sub_rtm_item_origin.update({
                "level": "two",
                "field1": sub_rtm_item.sub_rtm,
            })
            ret.append(sub_rtm_item_origin)
            # 汇总ndt1级别的数据
            ndt1_items = get_by_ndt1(data_group_by_ndt1, rtm_item.rtm, sub_rtm_item.sub_rtm)
            for ndt1_item in ndt1_items:
                ndt1_item_origin =ndt1_item.as_dict()
                ndt1_item_origin.update({
                    "level": "three",
                    "field1":ndt1_item.hq_name,
                })
                ret.append(ndt1_item_origin)

    return sort_nd_data(ret)


def convert_week(items: list[WeeklyItem]) -> list[WeeklyItem]:
    if not items:
        return items
    # group
    data_group_by_region = UbWeekRecall.group_by_region(items)
    data_group_by_rtm = UbWeekRecall.group_by_rtm(items)
    data_group_by_sub_rtm = UbWeekRecall.group_by_sub_rtm(items)

    def get_by_region(data_group_by_region, region):
        return data_group_by_region

    def get_by_rtm(data_group_by_rtm, rtm):
        return [item for item in data_group_by_rtm if item.rtm == rtm]

    def get_by_sub_rtm(data_group_by_sub_rtm, rtm):
        return [item for item in data_group_by_sub_rtm if item.rtm == rtm]

    ret = []
    for region_item in data_group_by_region:
        print(region_item.as_dict())
        item = region_item.as_dict()
        item.update({
            "level": "one",
            "field1": "China Channel",
        })
        ret.append(item)
        rtm_items = get_by_region(data_group_by_rtm, None)
        for rtm_item in rtm_items:
            rtm_item_origin = rtm_item.as_dict()
            rtm_item_origin.update({
                "level": "two",
                "field1": rtm_item.rtm,
            })
            ret.append(rtm_item_origin)
            print(rtm_item.as_dict())
            # UB overall mail 不展示sub_rtm层级的rtm列表
            if rtm_item.rtm in UB_NO_DISPLAY_SUB_RTM:
                continue
            sub_rtm_items = get_by_sub_rtm(data_group_by_sub_rtm, rtm_item.rtm)
            for sub_rtm_item in sub_rtm_items:
                sub_rtm_item_origin = sub_rtm_item.as_dict()
                sub_rtm_item_origin.update({
                    "level": "three",
                    "field1": sub_rtm_item.sub_rtm,
                })
                ret.append(sub_rtm_item_origin)
                print(sub_rtm_item.as_dict())
    return sort_and_convert_data(ret)


def convert_sub_lob(items:list[DailyItem]) -> list[DailyItem]:
    # group
    data_group_by_region = UbDailyRecall.group_by_region(items)
    data_group_by_rtm = UbDailyRecall.group_by_rtm(items)
    data_group_by_sub_rtm = UbDailyRecall.group_by_sub_rtm(items)

    def get_by_region(data_group_by_region, region):
        return data_group_by_region

    def get_by_rtm(data_group_by_rtm, rtm):
        return [item for item in data_group_by_rtm if item.rtm == rtm]

    def get_by_sub_rtm(data_group_by_sub_rtm, rtm):
        return [item for item in data_group_by_sub_rtm if item.rtm == rtm]

    ret = []
    for region_item in data_group_by_region:
        print(region_item.as_dict())
        item = region_item.as_dict()
        item.update({
            "level": "one",
            "field1": "China Channel",
        })
        ret.append(item)
        rtm_items = get_by_region(data_group_by_rtm, None)
        for rtm_item in rtm_items:
            rtm_item_origin = rtm_item.as_dict()
            rtm_item_origin.update({
                "level": "one",
                "field1": rtm_item.rtm,
            })
            ret.append(rtm_item_origin)
            print(rtm_item.as_dict())
            sub_rtm_items = get_by_sub_rtm(data_group_by_sub_rtm, rtm_item.rtm)
            for sub_rtm_item in sub_rtm_items:
                sub_rtm_item_origin = sub_rtm_item.as_dict()
                # EDU 特殊处理，不展示sub_rtm层级的数据
                # todo 这里也要加一个 ENT的
                if sub_rtm_item.sub_rtm not in ['EDU', 'Channel Online', 'ENT']:
                    sub_rtm_item_origin.update({
                        "level": "two",
                        "field1": sub_rtm_item.sub_rtm,
                    })
                    ret.append(sub_rtm_item_origin)
                    print(sub_rtm_item.as_dict())
                for sub_lob_item in items:
                    if sub_lob_item.rtm == sub_rtm_item.rtm and sub_lob_item.sub_rtm == sub_rtm_item.sub_rtm:
                        sub_lob_item_origin = sub_lob_item.as_dict()
                        sub_lob_item_origin.update({
                            "level": "three",
                            "field1": sub_lob_item.sub_lob,
                        })
                        ret.append(sub_lob_item_origin)
                        print(sub_lob_item.as_dict())
    return sort_and_convert_data(ret)


def convert_sub_lob_weekly(items:list[WeeklyItem]) -> list[WeeklyItem]:
    if not items:
        return items
    # group
    data_group_by_region = UbWeekRecall.group_by_region(items)
    data_group_by_rtm = UbWeekRecall.group_by_rtm(items)
    data_group_by_sub_rtm = UbWeekRecall.group_by_sub_rtm(items)

    def get_by_region(data_group_by_region, region):
        return data_group_by_region

    def get_by_rtm(data_group_by_rtm, rtm):
        return [item for item in data_group_by_rtm if item.rtm == rtm]

    def get_by_sub_rtm(data_group_by_sub_rtm, rtm):
        return [item for item in data_group_by_sub_rtm if item.rtm == rtm]

    ret = []
    for region_item in data_group_by_region:
        print(region_item.as_dict())
        item = region_item.as_dict()
        item.update({
            "level": "one",
            "field1": "China Channel",
        })
        ret.append(item)
        rtm_items = get_by_region(data_group_by_rtm, None)
        for rtm_item in rtm_items:
            rtm_item_origin = rtm_item.as_dict()
            rtm_item_origin.update({
                "level": "one",
                "field1": rtm_item.rtm,
            })
            ret.append(rtm_item_origin)
            print(rtm_item.as_dict())
            sub_rtm_items = get_by_sub_rtm(data_group_by_sub_rtm, rtm_item.rtm)
            for sub_rtm_item in sub_rtm_items:
                sub_rtm_item_origin = sub_rtm_item.as_dict()
                # EDU 特殊处理，不展示sub_rtm层级的数据
                # todo 这里也要加一个 ENT的
                if sub_rtm_item.sub_rtm not in ['EDU', 'Channel Online', 'ENT']:
                    sub_rtm_item_origin.update({
                        "level": "two",
                        "field1": sub_rtm_item.sub_rtm,
                    })
                    ret.append(sub_rtm_item_origin)
                    print(sub_rtm_item.as_dict())
                for sub_lob_item in items:
                    if sub_lob_item.rtm == rtm_item.rtm and sub_lob_item.sub_rtm == sub_rtm_item.sub_rtm:
                        sub_lob_item_origin = sub_lob_item.as_dict()
                        sub_lob_item_origin.update({
                            "level": "three",
                            "field1": sub_lob_item.sub_lob,
                        })
                        ret.append(sub_lob_item_origin)
                        print(sub_lob_item.as_dict())
    return sort_and_convert_data(ret)


def sort_and_convert_data(data):
    data = sort_data(data)
    # data = convert_rtm(data)
    return data


def sort_data(data):
    online_offline_rules = ['All', ONLINE, OFFLINE]
    platform_rules = ['All', 'JD', 'Meituan', 'Wechat', 'Douyin', 'Tmall', 'Eleme', 'Banking', 'Alipay', 'Carrier Own', 'Others']
    data = sorted(data, key=lambda x: (
        RTMS_SORT_RULES.index(x.get('rtm')) if x.get('rtm') in RTMS_SORT_RULES else 999,
        SUB_RTMS_SORT_RULES.index(x.get('sub_rtm')) if x.get('sub_rtm') in SUB_RTMS_SORT_RULES else 999,
        SUB_LOB_SORT_RULES.index(x.get('sub_lob')) if x.get('sub_lob') in SUB_LOB_SORT_RULES else 999,
        online_offline_rules.index(x.get('online_offline')) if x.get('online_offline') in online_offline_rules else 999,
        platform_rules.index(x.get('platform')) if x.get('platform') in platform_rules else 999,
    ))
    return data


def sort_nd_data(data):
    nd_t1_sort_order = ["All", "T1", "ND"]
    data = sorted(data, key=lambda x: (
        RTMS_SORT_RULES.index(x.get('rtm')) if x.get('rtm') in RTMS_SORT_RULES else 999,
        SUB_RTMS_SORT_RULES.index(x.get('sub_rtm')) if x.get('sub_rtm') in SUB_RTMS_SORT_RULES else 999,
        nd_t1_sort_order.index(x.get('nd_type')) if x.get('nd_type') in nd_t1_sort_order and x.get('rtm') == 'Mono Brand' else 999,
        (x.get('hq_name') != ALL, x.get('hq_name')), # hq_name 按照A-Z升序
    ))
    return data