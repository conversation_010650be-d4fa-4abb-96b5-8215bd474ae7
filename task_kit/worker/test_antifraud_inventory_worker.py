from domain.dashboard.entity.anti_fraud.anti_fraud_so_ub import RTM_EDU
from task_kit.worker.eoh_aging_and_wo_ds_email_worker import EohAgingAndWithoutDSEmailWorker


def test_run():
    EohAgingAndWithoutDSEmailWorker(params='{"email_cmd":"test_baoguo_eoh_aging_cross_quarter", "rtm_version": "All"}').do()
    # EohAgingAndWithoutDSEmailWorker(params='{"email_cmd":"test_hk_pos_ub2", "rtm_version": "All"}').do()
    # AntifraudSoUBEmailWorker(params='{"email_cmd":"test_hk2", "rtm_version": "All"}').do_v2()
    # AntifraudSoUBEmailWorker(params='{"email_cmd":"formal_antifraud_so_ub_email_with_boss", "rtm_version": "' +RTM_Mono+ '"}').do()
    # AntifraudSoUBEmailWorker(params='{"email_cmd":"formal_antifraud_so_ub_email_with_boss", "rtm_version": "' +RTM_MULTI+ '"}').do()
    # AntifraudSoUBEmailWorker(params='{"email_cmd":"formal_antifraud_so_ub_email_with_boss", "rtm_version": "' +RTM_CARRIER+ '"}').do()
    # EohAgingAndWithoutDSEmailWorker(params='{"email_cmd":"formal_antifraud_eoh_aging_ub_email_with_edu_pre", "rtm_version": "' +RTM_EDU+ '"}').do()

