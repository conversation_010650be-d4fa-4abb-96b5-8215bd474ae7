import re
import json
import pandas as pd
from datetime import datetime

from data.databend.dashboard.macroforecast_feature_detail import MacroForecastFeatureDetail
from data.databend.dashboard.macroforecast_metadata import MacroForecastMetadata
from data.databend.dashboard.macroforecast_quarterly_ub import MacroForecastQuarterlyUb, GEO_CN, RTM_PG
from data.databend.dashboard.macroforecast_value import MacroForecastValue
from kit.email.sender import Sender, CustomizedData
from util.conf import logger
from util.const import MACRO_FORECAST_UB_QUARTERLY_REPORT, MACRO_FORECAST_UB_ROLLING_REPORT, \
    MACRO_FORECAST_SMARTPHONE_QUARTERLY_REPORT, MACRO_FORECAST_SMARTPHONE_ROLLING_REPORT, ErrorExcept, ErrCode


class MacroForecastEmailWorker:
    def __init__(self, params: str):
        # 配置在数据库中的是json字符串
        if params is None or "email_cmd" not in params or "fiscal_qtr_year_name" not in params:
            logger.error("no param configed of AntifraudSoUBEmailWorker.")
            raise Exception("param is required")
        params = json.loads(params)
        self.email_cmd = params.get("email_cmd")
        self.fiscal_qtr_year_name = params.get("fiscal_qtr_year_name")

    def do(self):
        try:
            render_data = {'fiscal_qtr': self.fiscal_qtr_year_name, 'ub_flg': False, 'ub_quarterly_flg': False,
                           'ub_rolling_flg': False, 'smartphone_flg': False, 'smartphone_quarterly_flg': False,
                           'smartphone_rolling_flg': False}
            # 根据metadata初始化数据
            self.init_metadata(render_data)
            # 初始化表格数据
            self.init_table_data(render_data)

            if not render_data['ub_flg'] and not render_data['smartphone_flg']:
                # 任务跳过，不发邮件
                raise ErrorExcept(ErrCode.UnknownError, "Forecast data error.")

            sender = Sender(self.email_cmd, subject_date=render_data['fiscal_qtr'])
            sender.send(customized_data=CustomizedData(datetime.today(), render_data))
        except Exception as e:
            logger.exception(e)
            raise e

    def init_metadata(self, render_data: dict):
        metadata_list = MacroForecastMetadata.get_qtr_data_list(self.fiscal_qtr_year_name)
        if not metadata_list:
            raise ErrorExcept(ErrCode.UnknownError, "Forecast Metadata data error.")

        for item in metadata_list:
            metadata_item = {'report_name': item.get('report_name'), 'tips': [], 'table_list': []}

            if item.get('report') == MACRO_FORECAST_UB_QUARTERLY_REPORT:
                # 展示标识
                render_data['ub_flg'] = True
                render_data['ub_quarterly_flg'] = True
                # 标题、文案
                if 'quarterly_ub_data' not in render_data:
                    render_data['quarterly_ub_data'] = metadata_item
                render_data['quarterly_ub_data']['tips'].append(item.get('forecast_comment'))
            elif item.get('report') == MACRO_FORECAST_UB_ROLLING_REPORT:
                # 展示标识
                render_data['ub_flg'] = True
                render_data['ub_rolling_flg'] = True
                # 提取文案()内的qtr数据
                render_data['rolling_qtr'] = re.findall(r'\((.*?)\)', metadata_item['report_name'])[0]
                # 标题、文案
                if 'rolling_ub_data' not in render_data:
                    render_data['rolling_ub_data'] = metadata_item
                render_data['rolling_ub_data']['tips'].append(item.get('forecast_comment'))
            elif item.get('report') == MACRO_FORECAST_SMARTPHONE_QUARTERLY_REPORT:
                # 展示标识
                render_data['smartphone_flg'] = True
                render_data['smartphone_quarterly_flg'] = True
                # 标题、文案
                if 'quarterly_smartphone_data' not in render_data:
                    render_data['quarterly_smartphone_data'] = metadata_item
                render_data['quarterly_smartphone_data']['tips'].append(item.get('forecast_comment'))
            elif item.get('report') == MACRO_FORECAST_SMARTPHONE_ROLLING_REPORT:
                # 展示标识
                render_data['smartphone_flg'] = True
                render_data['smartphone_rolling_flg'] = True
                # 提取文案()内的qtr数据
                render_data['rolling_qtr'] = re.findall(r'\((.*?)\)', metadata_item['report_name'])[0]
                # 标题、文案
                if 'rolling_smartphone_data' not in render_data:
                    render_data['rolling_smartphone_data'] = metadata_item
                render_data['rolling_smartphone_data']['tips'].append(item.get('forecast_comment'))

    def init_table_data(self, render_data: dict):
        # Overview数据
        render_data['overview_data'] = {'quarterly_ub_fcst': '', 'quarterly_ub_yoy': '', 'quarterly_ub_yoy_color': '',
                                        'rolling_ub_fcst': '', 'rolling_ub_yoy': '', 'rolling_ub_yoy_color': '',
                                        'quarterly_smartphone_fcst': '', 'quarterly_smartphone_yoy': '', 'quarterly_smartphone_yoy_color': '',
                                        'rolling_smartphone_fcst': '', 'rolling_smartphone_yoy': '', 'rolling_smartphone_yoy_color': ''}
        # 表一 数据
        if render_data['ub_quarterly_flg']:
            quarterly_ub_list = MacroForecastQuarterlyUb.get_qtr_data_list(self.fiscal_qtr_year_name)
            if quarterly_ub_list:
                quarterly_ub_dict = {}
                geo_list = []
                for item in quarterly_ub_list:
                    fcst_value = self.format_thousands(item.get('ub_forecast'))
                    yoy_value, yoy_color = self.format_percentage_value(item.get('yoy'), 1, True)
                    acc_value, _ = self.format_percentage_value(item.get('acc'))
                    # Overview数据
                    if item.get('geo') == GEO_CN and item.get('rtm') == RTM_PG:
                        render_data['overview_data']['quarterly_ub_fcst'] = fcst_value
                        render_data['overview_data']['quarterly_ub_yoy'] = yoy_value
                        render_data['overview_data']['quarterly_ub_yoy_color'] = yoy_color
                    if item.get('geo') not in quarterly_ub_dict:
                        quarterly_ub_dict[item.get('geo')] = []
                        geo_list.append(item.get('geo'))
                    quarterly_ub_dict[item.get('geo')].append({'rtm': item.get('rtm'), 'acc': acc_value, 'fcst': fcst_value, 'yoy': yoy_value, 'yoy_color': yoy_color})

                quarterly_table_list = []
                for item in quarterly_ub_dict:
                    quarterly_table_list.append({'name': item, 'data_list': quarterly_ub_dict.get(item)})
                render_data['quarterly_ub_data']['table_list'] = sorted(quarterly_table_list,
                                                                        key=lambda x: geo_list.index(x['name']))
            else:
                # render_data['ub_quarterly_flg'] = False
                raise ErrorExcept(ErrCode.UnknownError, "Quarterly UB Forecast data error.")

        forecast_dict = MacroForecastValue.get_qtr_data_list(self.fiscal_qtr_year_name)
        detail_dict = MacroForecastFeatureDetail.get_qtr_data_list(self.fiscal_qtr_year_name)
        # 表二 数据
        if render_data['ub_rolling_flg']:
            if MACRO_FORECAST_UB_ROLLING_REPORT not in forecast_dict or MACRO_FORECAST_UB_ROLLING_REPORT not in detail_dict:
                # render_data['ub_rolling_flg'] = False
                raise ErrorExcept(ErrCode.UnknownError, "Rolling 4 Quarters UB Forecast data error.")
            else:
                # forecast部分
                forecast_item = self.get_forecast_item(MACRO_FORECAST_UB_ROLLING_REPORT, forecast_dict)
                render_data['rolling_ub_data'].update(forecast_item)
                # Overview数据
                render_data['overview_data']['rolling_ub_fcst'] = forecast_item['forecast']
                render_data['overview_data']['rolling_ub_yoy'] = forecast_item['yoy']
                render_data['overview_data']['rolling_ub_yoy_color'] = forecast_item['yoy_color']

                # table部分
                render_data['rolling_ub_data']['table_list'] = \
                    self.get_forecast_detail_table(MACRO_FORECAST_UB_ROLLING_REPORT, detail_dict)

        # 表三 数据
        if render_data['smartphone_quarterly_flg']:
            if MACRO_FORECAST_SMARTPHONE_QUARTERLY_REPORT not in forecast_dict \
                    or MACRO_FORECAST_SMARTPHONE_QUARTERLY_REPORT not in detail_dict:
                # render_data['smartphone_quarterly_flg'] = False
                raise ErrorExcept(ErrCode.UnknownError, "Quarterly Smartphone Market Forecast data error.")
            else:
                # forecast部分
                forecast_item = self.get_forecast_item(MACRO_FORECAST_SMARTPHONE_QUARTERLY_REPORT, forecast_dict)
                render_data['quarterly_smartphone_data'].update(forecast_item)
                # Overview数据
                render_data['overview_data']['quarterly_smartphone_fcst'] = forecast_item['forecast']
                render_data['overview_data']['quarterly_smartphone_yoy'] = forecast_item['yoy']
                render_data['overview_data']['quarterly_smartphone_yoy_color'] = forecast_item['yoy_color']

                # table部分
                render_data['quarterly_smartphone_data']['table_list'] = \
                    self.get_forecast_detail_table(MACRO_FORECAST_SMARTPHONE_QUARTERLY_REPORT, detail_dict)

        # 表四 数据
        if render_data['smartphone_rolling_flg']:
            if MACRO_FORECAST_SMARTPHONE_ROLLING_REPORT not in forecast_dict \
                    or MACRO_FORECAST_SMARTPHONE_ROLLING_REPORT not in detail_dict:
                # render_data['smartphone_rolling_flg'] = False
                raise ErrorExcept(ErrCode.UnknownError, "Rolling 4 Quarters Smartphone Market Forecast data error.")
            else:
                # forecast部分
                forecast_item = self.get_forecast_item(MACRO_FORECAST_SMARTPHONE_ROLLING_REPORT, forecast_dict)
                render_data['rolling_smartphone_data'].update(forecast_item)
                # Overview数据
                render_data['overview_data']['rolling_smartphone_fcst'] = forecast_item['forecast']
                render_data['overview_data']['rolling_smartphone_yoy'] = forecast_item['yoy']
                render_data['overview_data']['rolling_smartphone_yoy_color'] = forecast_item['yoy_color']

                # table部分
                render_data['rolling_smartphone_data']['table_list'] = \
                    self.get_forecast_detail_table(MACRO_FORECAST_SMARTPHONE_ROLLING_REPORT, detail_dict)

        # 展示标识计算
        render_data['ub_flg'] = render_data['ub_quarterly_flg'] or render_data['ub_rolling_flg']
        render_data['smartphone_flg'] = render_data['smartphone_quarterly_flg'] or render_data['smartphone_rolling_flg']

    @classmethod
    def get_forecast_item(cls, forecast_key: str, forecast_dict: dict):
        forecast_item = forecast_dict.get(forecast_key)
        yoy_value, yoy_color = cls.format_percentage_value(forecast_item.get('yoy'), 1, True)
        return {'forecast': cls.format_thousands(forecast_item.get('forecast')),
                'forecast_period': forecast_item.get('forecast_period'),
                'actual': cls.format_thousands(forecast_item.get('actual')),
                'actual_period': forecast_item.get('actual_period'),
                'yoy': yoy_value,
                'yoy_color': yoy_color}

    @classmethod
    def get_forecast_detail_table(cls, detail_key: str, detail_dict: dict):
        detail_list = detail_dict.get(detail_key)
        category_dict = {}
        category_list = []
        for item in detail_list:
            if item.get('category') not in category_dict:
                category_dict[item.get('category')] = []
                category_list.append(item.get('category'))
            category_dict[item.get('category')].append({
                'feature': item.get('feature'), 'default_value': item.get('default_value'),
                'default_range': item.get('default_range')})

        ret = []
        for item in category_dict:
            ret.append({'name': item, 'data_list': category_dict.get(item)})
        return sorted(ret, key=lambda x: category_list.index(x['name']))

    @classmethod
    def format_percentage_value(cls, value, scale: int = 1, prefix: bool = False) -> tuple[str, str]:
        if value is None:
            raise ErrorExcept(ErrCode.UnknownError, "data empty.")
        value = pd.to_numeric(value, errors='raise')
        abs_value = abs(value)
        if abs_value * (100 ** scale) * 100 < 5:
            # 0.0%场景
            return '0.0%', ''

        color_str = 'style="color: #68D58F"' if value >= 0 else 'style="color: #F63F54"'
        prefix_str = '+' if prefix and value > 0 else ''
        format_str = '{:.' + str(scale) + 'f}'
        value_str = format_str.format(value * 100)
        ret = '{}{}%'.format(prefix_str, value_str)
        return ret, color_str

    @classmethod
    def format_thousands(cls, value) -> str:
        if value is None:
            raise ErrorExcept(ErrCode.UnknownError, "data empty.")
        value = pd.to_numeric(value, downcast='integer', errors='raise')
        value = round(value)
        ret = '{:,}'.format(value)
        return ret
