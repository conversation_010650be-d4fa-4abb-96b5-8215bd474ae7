import json

from datetime import datetime
from domain.email.impl.email_po_gap_impl import get_po_gap_email_data, whether_to_send_email_for_po_gap
from kit.email.sender import Sender, CustomizedData
from util.conf import logger


class PoGapEmailWorker:
    def __init__(self, params: str):
        # 配置在数据库中的是json字符串
        if params is None or "email_cmd" not in params:
            logger.error("no param configed of this Worker.")
            raise Exception("param is required")
        params = json.loads(params)
        self.email_cmd = params.get("email_cmd")
        self.greeting = params.get("greeting")
        self.params = params

    def do(self):
        try:
            # 发送时间
            now = datetime.now()
            send_date = now.strftime("%Y-%m-%d")
            # 判断是否需要发送邮件
            if not whether_to_send_email_for_po_gap(send_date):
                error_message = f"{send_date} 本周demand数据未发布，未发送Po Gap Channel Total邮件!!!"
                logger.info(error_message)
                raise Exception(error_message)
            # 获取需要展示的po gap的数据
            render_data = get_po_gap_email_data()
            render_data["greeting"] = self.greeting
            # 发送邮件
            sender = Sender(self.email_cmd)
            sender.send(customized_data=CustomizedData(now, render_data))
        except Exception as e:
            logger.exception(e)
            raise e
