import json
import traceback
from datetime import datetime
from typing import Optional

from data.fiscal_year_week import FiscalYearWeek
from data.mysqls.mybusiness_appeal.mybiz_reseller_secure import MybizResellerSecure
from kit.email.sender import Sender, CustomizedData

from util.conf import logger
from util.const import EmailCmd
from util.mail_conf import get_normal_system_link


class MyBusinessAppealWorker:
    def __init__(self, params: str):
        # 配置在数据库中的是json字符串
        if params is None or "email_cmd" not in params:
            logger.error("no param configed of MyBusinessAppealWorker.")
            raise Exception("param is required")

        params = json.loads(params)

        self.params = params
        self.email_cmd = params.get("email_cmd")
        self.fiscal_week = params.get("fiscal_week")
        self.deadline = params.get("deadline")
        self.reseller_id = params.get("reseller_id")
        self.reseller_type = params.get("reseller_type")  # 1 就是总代/总公司 2就是其他的分公司 T1、T2

        self.params['format_deadline'] = self.__format_datetime_to_weekday_string(self.deadline)
        self.params['jump_workspace_link'] = get_normal_system_link(host='https://gcmybusiness.apple.com',
                                                                    system_link='abnormalOperation',
                                                                    enter_from="source=external_email")

        if self.email_cmd == EmailCmd.MybusinessPosSuspensionAppealWindowAboutToClose:
            week_begin_dt, week_end_dt = self.__get_week_range()
            self.params['week_begin_dt'] = week_begin_dt
            self.params['week_end_dt'] = week_end_dt

    def __get_week_range(self):
        day_info_obj = FiscalYearWeek.get_fiscal_day_info_by_fiscal_week(self.fiscal_week)
        week_begin_dt = None
        week_end_dt = None
        if day_info_obj:
            week_begin_dt = day_info_obj.week_begin_dt.strftime("%Y/%m/%d")
            week_end_dt = day_info_obj.week_end_dt.strftime("%Y/%m/%d")
        return week_begin_dt, week_end_dt

    @staticmethod
    def __format_datetime_to_weekday_string(deadline: str) -> str:
        input_datetime = datetime.strptime(deadline, '%Y-%m-%d %H:%M:%S')
        weekday_map = {
            0: "周一",
            1: "周二",
            2: "周三",
            3: "周四",
            4: "周五",
            5: "周六",
            6: "周日",
        }

        # 上午 表示从 00:00:00 到 11:59:59 的时间范围
        # 中午 12:00 是一个特定的时间点，表示中午十二点整
        # 下午 涵盖从 12:00:01 到 23:59:59 的时间范围
        if input_datetime.hour < 12:
            period = "上午"
        elif input_datetime.hour == 12 and input_datetime.minute == 0 and input_datetime.second == 0:
            period = "中午"
        else:
            period = "下午"

        weekday_str = weekday_map[input_datetime.weekday()]
        formatted_date = input_datetime.strftime("%Y/%m/%d")
        formatted_time = input_datetime.strftime("%-I:%M") # 注意 %-I，表示12小时制时间，不加前导零
        
        # 最终展示内容：2025/01/23 下午 8:00 (周四)
        return f"{formatted_date} {period} {formatted_time} ({weekday_str})"
        # PRD 要求 本期设置固定时间为「12:00」
        # return f"{formatted_date} 中午 12:00 ({weekday_str})"

    def __get_reseller_email(self) -> Optional[str]:
        email_records = MybizResellerSecure.get_email_by_reseller_id(self.reseller_id)
        if not email_records:
            return None
        return email_records.email

    def do(self):
        try:
            now = datetime.now()
            reseller_email = self.__get_reseller_email()
            if not reseller_email:
                raise Exception(f"fiscal_week: {self.fiscal_week}, reseller_type:{self.reseller_type}, "
                                f"reseller_id: {self.reseller_id}查无对应邮箱.")
            sender = Sender(self.email_cmd, custom_format_subject=True, need_check_duplication=False)
            sender.set_recipients(recipient_list=[reseller_email])
            sender.send(customized_data=CustomizedData(now, self.params))
        except Exception as e:
            logger.error(f"error_info: {str(e)}, traceback: {traceback.format_exc()}")
            # logger.exception(e)
            raise e
