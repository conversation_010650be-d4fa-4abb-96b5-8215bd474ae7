# ########## must at head ############
# import os
# import sys
#
# import pandas
#
#
# SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
# sys.path.append(os.path.dirname(SCRIPT_DIR))
# ##########      end     ############

from task_kit.worker.automatic.automatic_cto_worker import AutomaticCTOWorker
from task_kit.worker.automatic.automatic_worker import AutomaticWorker
from task_kit.worker.automatic.cto_notification_email_worker import CTONotificationEmailWorker


# usage
# ENV=dev pytest -s test/test_automatic_file.py

def test_automatic_run():
    AutomaticWorker(
        params='{"type":"esr_main_products", '
               '"snapshot_ts": "2024-11-04 08:43:52", '
               '"snapshot_dt": "2024-11-04", '
               '"rtm":"Multi", '
               '"fiscal_qtr_week_name": "FY25Q1W6", '
               '"table_name": "gc_ro_ds_esr_data", '
               '"data_type": "ESR", '
               '"db_name": "test_db", '
               '"sync_time": "2024-11-04 08:36:06"}'
    ).do()


def test_automatic_cto_run():
    AutomaticCTOWorker(
        params='{"type":"cto_pod", '
               '"snapshot_ts": "2024-11-20 08:43:52", '
               '"snapshot_dt": "2024-11-20", '
               '"rtm": "Multi", '
               '"fiscal_qtr_week_name": "FY25Q1W8", '
               '"table_name": "gc_dmp_pod", '
               '"data_type": "CTO", '
               '"db_name": "gc_dmp_data", '
               '"sync_time": "2024-11-20 08:36:06"}'
    ).do()


def test_automatic_cto_notification_run():
    email_cmd = 'cto_notification_email'
    sync_status = 'Delay'
    snapshot_ts = '2024-11-21 08:43:52'
    period = '2024-11-21'
    fiscal_qtr_week_name = 'FY25Q1W8'
    table_name = 'gc_dmp_pod'
    data_type = 'CTO'
    db_name = 'gc_dmp_data'
    params = (f'{{'
        f'"email_cmd":"{email_cmd}", "sync_status":"{sync_status}",'
        f'"type":"{type}",'
        f'"snapshot_ts":"{snapshot_ts}", "snapshot_dt":"{period}",'
        f'"fiscal_qtr_week_name":"{fiscal_qtr_week_name}", "table_name":"{table_name}", '
        f'"data_type":"{data_type}", "db_name":"{db_name}"'
        f'}}')
    CTONotificationEmailWorker(params=params).do()
