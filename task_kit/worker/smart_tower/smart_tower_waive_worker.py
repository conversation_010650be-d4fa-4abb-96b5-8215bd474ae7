import json
import traceback
from datetime import datetime
from itertools import groupby
from typing import Optional, List

from data.databend.smart_tower_inventory.app_smart_tower_inv_diagnosis_oustock_di import InvOutStockWaive
from domain.smart_tower.entity.outstock_so_velocity_waive import OutstockSoVelocityWaive
from kit.custom_sort import CustomSort
from kit.email.sender import Sender, CustomizedData

from util.conf import logger


class SmartTowerWaiveWorker:
    # inventory rtm business_type 排序
    RTM_BUSINESS_TYPE_SORT_RULE = [
        {"rtm": 'Total', 'business_type': ['Total']},
        {"rtm": 'Monobrand', 'business_type': ['Lifestyle', 'MONO']},
        {"rtm": 'Multibrand', 'business_type': ['Duty Free', 'Mass Merchant', 'OTC', 'Township']},
        {"rtm": 'Carrier', 'business_type': ['CM', 'CU', 'CT', 'CB']},
        {"rtm": 'Channel Online', 'business_type': ['JD Self-run', 'Online Others']}
    ]
    RTM_SORT_RULE = [item['rtm'] for item in RTM_BUSINESS_TYPE_SORT_RULE]
    BUSINESS_TYPE_SORT_RULE = [item for business_type_item in RTM_BUSINESS_TYPE_SORT_RULE for item in
                               business_type_item['business_type']]
    NAND_SORT = ["All", "64GB", "128GB", "256GB", "512GB", "1TB"]

    def __init__(self, params: str):
        # 配置在数据库中的是json字符串
        if params is None or "email_cmd" not in params:
            logger.error("no param configed of SmartTowerWaiveWorker.")
            raise Exception("param is required")

        params = json.loads(params)

        self.params = params
        self.email_cmd = params.get("email_cmd")
        self.fiscal_qtr_week_name = params.get("fiscal_qtr_week_name")
        self.fiscal_week_year = params.get("fiscal_week_year")

    def __get_inventory_out_stock_waive(self) -> tuple:
        # 数据查询
        out_stock_waive_records: List[OutstockSoVelocityWaive] = (
            InvOutStockWaive.query_inventory_waive(fiscal_week=self.fiscal_qtr_week_name, waive_tag='Y')
        )
        latest_refresh_time = None
        if out_stock_waive_records:
            latest_refresh_time = out_stock_waive_records[0].update_time

        # 排序： rtm、sub_rtm、nand、sku
        data = sorted(out_stock_waive_records, key=lambda x: (
            self.RTM_SORT_RULE.index(x.rtm) if x.rtm in self.RTM_SORT_RULE else len(self.RTM_SORT_RULE),
            self.BUSINESS_TYPE_SORT_RULE.index(x.sub_rtm) if x.sub_rtm in self.BUSINESS_TYPE_SORT_RULE else len(self.BUSINESS_TYPE_SORT_RULE),
            -(CustomSort.get_sub_lob_order(x.sub_lob)),
            self.NAND_SORT.index(x.nand) if x.nand in self.NAND_SORT else len(self.NAND_SORT),
            x.sku if x.sku else float('inf')
        ))

        # 按照rtm 分类
        grouped_data = []
        for key, group in groupby(data, key=lambda x: (x.rtm, x.sub_rtm)):
            rtm, sub_rtm = key
            group_list = list(group)
            grouped_data.append({
                'rtm': rtm,
                'sub_rtm': sub_rtm,
                'records': group_list
            })
        return grouped_data, latest_refresh_time

    def do(self):
        try:
            grouped_waive_records, latest_refresh_time = self.__get_inventory_out_stock_waive()
            if not grouped_waive_records:
                logger.info(f"SmartTowerWaiveWorker::get inventory out stock waive, fiscal_week: {self.fiscal_qtr_week_name}暂无大面积缺货数据...")
                return

            render_data = {
                "fiscal_week": self.fiscal_qtr_week_name,
                "waive_data": grouped_waive_records
            }
            sender = Sender(self.email_cmd, custom_format_subject=True)
            sender.send(customized_data=CustomizedData(latest_refresh_time, render_data))
        except Exception as e:
            logger.error(f"error_info: {str(e)}, traceback: {traceback.format_exc()}")
            # logger.exception(e)
            raise e
