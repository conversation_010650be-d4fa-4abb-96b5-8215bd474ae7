# ########## must at head ############
# import os
# import sys
#
# import pandas
#
#
# SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
# sys.path.append(os.path.dirname(SCRIPT_DIR))
# ##########      end     ############

from task_kit.worker.automatic.esr_sync_status_worker import EsrCheckSyncStatusWorker


# usage
# ENV=dev pytest -s test/test_automatic_file.py


"""
ESR 邮件通知 task需要传入的params
    email_cmd:                  会有2个cmd, 分别对应主品和配件 esr_main_products_email、esr_accessories_email"
    sync_status:                取值范围是：AutomaticDataStatus中的枚举, DELAY: 异常 REFRESH: 成功
    refresh_version:            取值范围是：DataRefreshVersion中的枚举, MORNING、AFTERNOON、COB
    type:                       取值范围是:  esr_main_products 、esr_accessories
    snapshot_ts:                取值范围是: app_gbi_table_sync_status_di.snapshot_ts
    snapshot_dt:                取值范围是: app_gbi_table_sync_status_di.snapshot_dt
    fiscal_qtr_week_name:       取值范围是: app_gbi_table_sync_status_di.fiscal_qtr_week_name
    table_name:                 取值范围是: app_gbi_table_sync_status_di.table_name
    data_type:                  取值范围是: app_gbi_table_sync_status_di.data_type
    db_name:                    取值范围是: app_gbi_table_sync_status_di.db_name
"""


def test_esr_sync_run():
    EsrCheckSyncStatusWorker(
        params='{"email_cmd":"esr_main_products_email", '
               '"sync_status": "Delay", '
               '"refresh_version": "Afternoon",'
               '"type":"esr_main_products", '
               '"snapshot_ts": "2024-11-04 08:43:52", '
               '"snapshot_dt": "2024-11-05", '
               '"fiscal_qtr_week_name": "FY25Q1W6", '
               '"table_name": "gc_ro_ds_esr_data", '
               '"data_type": "ESR", '
               '"db_name": "test_db"}'
    ).do()


