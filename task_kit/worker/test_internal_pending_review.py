# ########## must at head ############
# import os
# import sys
#
# import pandas
#
#
# SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
# sys.path.append(os.path.dirname(SCRIPT_DIR))
# ##########      end     ############

from task_kit.worker.internal_pending_review.pending_review_worker import PendingReviewWorker


# usage
# ENV=dev pytest -s test/test_esr.py

def test_run():
    PendingReviewWorker(
        params='{"email_cmd": "internal_pending_review_notify_email", "notify_list": [{"role_name": "Sales Reviewer", "pending_review_count": 1, "email": "<EMAIL>"}, {"role_name": "CSO Reviewer", "pending_review_count": 1, "email": "<EMAIL>"}]}'
    ).do()

