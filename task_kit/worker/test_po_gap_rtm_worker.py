from task_kit.worker.po_gap_rtm_email_worker import PoGapRTMEmailWorker


def test_run():
    # PoGapRTMEmailWorker(params='{"email_cmd":"Mono_po_gap_rtm_email_gm", "rtm": "Mono", "greeting": "Hello <PERSON>,"}').do()
    # PoGapRTMEmailWorker(params='{"email_cmd":"Multi_po_gap_rtm_email_gm", "rtm": "Multi", "greeting": "Hello Felix,"}').do()
    PoGapRTMEmailWorker(params='{"email_cmd":"Online_po_gap_rtm_email_gm", "rtm": "Online", "greeting": "Hello <PERSON>,"}').do()
    # PoGapRTMEmailWorker(params='{"email_cmd":"Carrier_po_gap_rtm_email_gm", "rtm": "Carrier", "greeting": "Hello <PERSON>,"}').do()
    # PoGapRTMEmailWorker(params='{"email_cmd":"Education_po_gap_rtm_email_gm","rtm":"Education","greeting": "Hello <PERSON>,"}').do()
    # PoGapRTMEmailWorker(params='{"email_cmd":"Enterprise_po_gap_rtm_email_gm","rtm":"Enterprise","greeting": "Hello Stella,"}').do()
