# ########## must at head ############
# import os
# import sys
#
# import pandas
#
#
# SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
# sys.path.append(os.path.dirname(SCRIPT_DIR))
# ##########      end     ############

from task_kit.worker.my_business_worker.my_business_ub_radar_worker import UbRadarWorker


# usage
# ENV=dev pytest -s test/test_esr.py


def test_mybiz_ub_radar():
    UbRadarWorker(
        params='{"email_cmd":"mybiz_reseller_ub_warning_notice","fiscal_week":"FY25Q3W8","count":"17","reseller_id":"3427815","reseller_type":"2"}'
    ).do()
