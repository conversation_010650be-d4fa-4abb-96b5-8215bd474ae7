# ########## must at head ############
# import os
# import sys
#
# import pandas
#
#
# SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
# sys.path.append(os.path.dirname(SCRIPT_DIR))
# ##########      end     ############

from domain.dashboard.entity.anti_fraud.anti_fraud_so_ub import RTM_Mono, RTM_MULTI, RTM_CARRIER, RTM_CHANNEL_ONLINE, \
    RTM_EDU
from task_kit.worker.antifraud_so_ub_email_worker import AntifraudSoUBEmailWorker, get_weeks


# usage
# ENV=dev pytest -s test/test_esr.py

def test_run():
    # AntifraudSoUBEmailWorker(params='{"email_cmd":"test_ninghui_ub_summary_nd_t1", "rtm_version": "All"}').do()
    # AntifraudSoUBEmailWorker(params='{"email_cmd":"test_ninghui_ub_summary_nd_t1", "rtm_version": "' +RTM_CHANNEL_ONLINE+ '"}').do()
    AntifraudSoUBEmailWorker(params='{"email_cmd":"test_baoguo_ub_summary_nd_t1", "rtm_version": "' + RTM_CHANNEL_ONLINE + '"}').do()
    # AntifraudSoUBEmailWorker(params='{"email_cmd":"test_hk2", "rtm_version": "All"}').do()
    # AntifraudSoUBEmailWorker(params='{"email_cmd":"test_hk2", "rtm_version": "All"}').do_v2()
    # AntifraudSoUBEmailWorker(params='{"email_cmd":"formal_antifraud_so_ub_email_with_boss", "rtm_version": "' +RTM_Mono+ '"}').do()
    # AntifraudSoUBEmailWorker(params='{"email_cmd":"formal_antifraud_so_ub_email_with_boss", "rtm_version": "' +RTM_MULTI+ '"}').do()
    # AntifraudSoUBEmailWorker(params='{"email_cmd":"formal_antifraud_so_ub_email_with_boss", "rtm_version": "' +RTM_CARRIER+ '"}').do()


# def test_get_weeks():
#     snapshot_date = '2024-09-17'
#     t_1_date = '2024-09-16'
#     rolling_weeks, d1_weeks = get_weeks(snapshot_date, t_1_date)
#     print(rolling_weeks, d1_weeks)
#     assert rolling_weeks == ['FY24Q4W8', 'FY24Q4W9', 'FY24Q4W10', 'FY24Q4W11', 'FY24Q4W12']
#     assert d1_weeks == ['FY24Q4W11', 'FY24Q4W12']
