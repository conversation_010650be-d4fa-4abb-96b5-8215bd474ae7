import json
import traceback
from datetime import datetime, timedelta

import pandas as pd

from data.databend.dashboard.pos_suspension import PosSuspensionSummaryDi
from data.databend.dashboard.pos_suspension_detail import PosSuspensionDetailDi
from data.email_config import EmailConfigRepository
from data.fiscal_year_week import FiscalYearWeek
from domain.dashboard.entity.anti_fraud.anti_fraud_so_ub import rtm_dict, RTM_EDU
from domain.dashboard.entity.fiscal_week import FiscalWeek

from kit.email.sender import Sender, CustomizedData

from util.conf import logger
from util.const import ALL
from util.file_util import get_absolute_path


class PosSuspensionEmailWorker:
    # 用于明细数据查询时使用db中的rtm查询(主要是为了替换EDU)
    display_rtm_to_db = {
        RTM_EDU: "EDU"
    }

    attachment_file_path = '/uploads/pos_suspension_details/'

    detail_columns = {
        "fiscal_qtr_week_name": "Fiscal Week (SO)",
        "rtm": "RTM",
        "sub_rtm": "Sub-RTM",
        "reseller_hqid": "HQ ID",
        "reseller_name": "HQ Name",
        "t2_reseller_hqid": "T2 Reseller ID",
        "t2_reseller_name": "T2 Reseller Name",
        "apple_id": "POS ID",
        "pos_name": "POS Name",
        "suspension_times": "# Suspensions",
        "begin_dt": "Begin Date",
        "end_dt": "End Date",
        "suspension_duration": "Suspension Duration"
    }
    query_columns = ["fiscal_qtr_week_name", "rtm", "sub_rtm", "reseller_hqid", "reseller_name", "t2_reseller_hqid",
                     "t2_reseller_name", "apple_id", "pos_name", "suspension_times",
                     "begin_dt", "end_dt", "suspension_duration"
                    ]

    def __init__(self, params: str):
        # 配置在数据库中的是json字符串
        if params is None or "email_cmd" not in params or "rtm_version" not in params:
            logger.error("no param configed of PosSuspensionEmailWorker.")
            raise Exception("param is required")
        params = json.loads(params)
        self.email_cmd = params.get("email_cmd")

        self.rtm_version = params.get("rtm_version")
        self.params = params

    def do(self):
        try:
            now = datetime.now()
            # 对外显示的财年周
            fiscal_week = FiscalYearWeek.get_fiscal_qtr_week_name_by_date(now.strftime('%Y-%m-%d'))

            # 查询数据取上上周的数据
            last_2_week_dt = now - timedelta(days=14)
            query_fiscal_week = FiscalYearWeek.get_fiscal_qtr_week_name_by_date(last_2_week_dt.strftime('%Y-%m-%d'))
            # query_fiscal_week = 'FY25Q1W2'   # 调试模式打开

            # ----------overall-------------------------
            data = PosSuspensionSummaryDi.query_pos_suspension_records(fiscal_week=query_fiscal_week)
            refresh_time = PosSuspensionSummaryDi.get_latest_refresh_time(fiscal_week=query_fiscal_week)
            # overall 只要 sub_rtm=all and disti_name=all
            overall_data = [item for item in data if item.sub_rtm == ALL and item.disti_name == ALL]

            # -----ND View------
            # nand view 只要 sub_rtm !=all and rtm != all
            nand_data = [item for item in data if item.sub_rtm != ALL and item.rtm != ALL]

            # ----附件-----
            # 转换一下rtm, 主要是EDU->Education
            origin_rtm = self.rtm_version
            tmp_rtm = self.display_rtm_to_db.get(origin_rtm, origin_rtm)
            rtm = tmp_rtm if tmp_rtm != 'All' else ""
            detail = PosSuspensionDetailDi.query_pos_suspension_detail_records(fiscal_week=query_fiscal_week, rtm=rtm)
            if detail:
                detail_df = pd.DataFrame(detail)

                # 固定处理的 week 列
                week_columns = [f'week{i}' for i in range(1, 15)]
                # 定义新的列名映射
                new_columns = {}
                new_query_columns = []
                for week_col in week_columns:
                    if week_col in detail_df.columns:
                        prefix = detail_df[week_col][0]  # 获取该 week 列的第一个值
                        if FiscalWeek(prefix).fiscal_week_int < FiscalWeek('FY24Q4W8').fiscal_week_int:
                            continue
                        new_columns[f'so_{week_col}'] = f'{prefix}-SO'
                        new_columns[f'ub7_{week_col}'] = f'{prefix}-UB7'
                        new_columns[f'ub7_rate_{week_col}'] = f'{prefix}-7D UB%'

                        new_query_columns.append(week_col)
                        new_query_columns.append(f'so_{week_col}')
                        new_query_columns.append(f'ub7_{week_col}')
                        new_query_columns.append(f'ub7_rate_{week_col}')

                # 增加对外显示的列名
                self.detail_columns.update(new_columns)
                final_query_columns = self.query_columns + new_query_columns
                detail_df = detail_df[final_query_columns]

                # 使用 rename 方法重命名列
                detail_df.rename(columns=self.detail_columns, inplace=True)

                # 删除原来的 week 列
                detail_df.drop(columns=week_columns, inplace=True, errors='ignore')

                relative_file_path = self.attachment_file_path + f"{query_fiscal_week}_{rtm+'_' if rtm else ''}pos_suspension_detail.xlsx"
                file_path = get_absolute_path(self.attachment_file_path) + f"{query_fiscal_week}_{rtm+'_' if rtm else ''}pos_suspension_detail.xlsx"
                detail_df.to_excel(file_path, index=False)
                file_path_json_str = json.dumps([relative_file_path])
                # 更新附件路径数据
                EmailConfigRepository.update_email_config(cmd=self.email_cmd, update_map={"attachments": file_path_json_str})
            else:
                # 更新附件路径数据, 防止读到上周的附件信息, 置为空
                EmailConfigRepository.update_email_config(cmd=self.email_cmd,
                                                          update_map={"attachments": ''})

            render_data = {
                "fiscal_week": fiscal_week,
                "overall_data": overall_data,
                "nand_data": nand_data,
                "rtm_version": self.rtm_version,
            }
            sender = Sender(cmd=self.email_cmd, subject_date=fiscal_week)
            sender.send(customized_data=CustomizedData(refresh_time, render_data))
        except Exception as e:
            logger.exception("PosSuspensionEmailWorker failed, error:" + traceback.format_exc())
            raise e
