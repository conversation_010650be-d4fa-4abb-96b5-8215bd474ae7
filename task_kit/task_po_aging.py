from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional
from jinja2 import Template

from domain.dashboard.impl.po_delinquent_impl import (
    channel_view_data_for_email, convert_to_email_data
)
from domain.email.entity.email_config import EmailConfig
from task_kit.repository.task_repository import TaskRepository, TaskStatus
from util.conf import logger
from util.send_email import (
    get_email_config, send_email_limit_frequency, async_send_email_by_database
)


PREFIX_SEND_EMAIL_TASK = "send_email_task"


def send_po_aging_email_worker(params: Optional[str] = None):
    send_po_aging_email("fast_lite_po_delinquent_open_po_aging_email")


def pre_1_send_po_aging_email_worker(params: Optional[str] = None):
    send_po_aging_email("fast_lite_po_delinquent_open_po_aging_email_pre")

    
def pre_2_send_po_aging_email_worker(params: Optional[str] = None):
    pre_2_send_po_aging_email("fast_lite_po_delinquent_open_po_aging_email_pre")


def send_po_aging_email(email_cmd: str):
    try:
        # 获取邮件配置信息，模版内容已经配置到content字段中，不需要额外的params
        email_config = get_email_config(email_cmd)
        
        # 将数据装载到邮件内容中
        email_config, _ = load_po_aging_email_content(email_config)
        
        # 使用邮件模版发送邮件
        send_email_limit_frequency(email_config)
    except Exception as e:
        logger.exception(e)
        raise e


def pre_2_send_po_aging_email(email_cmd: str):
    try:
        # 获取邮件配置信息，模版内容已经配置到content字段中，不需要额外的params
        email_config = get_email_config(email_cmd)
        
        # 将数据装载到邮件内容中
        email_config, data_refresh_time = load_po_aging_email_content(email_config)
        # data_refresh_time 与当前时间小于30分钟，则告警邮件
        if(datetime.now() - data_refresh_time) > timedelta(minutes=30):
            # 发送告警邮件
            async_send_email_by_database("fast_lite_po_delinquent_open_po_aging_email_alert")
            return
        
        # 使用邮件模版发送邮件
        send_email_limit_frequency(email_config)
    except Exception as e:
        logger.exception(e)
        raise e


# 组装需要发送邮件的内容
def load_po_aging_email_content(email_config: EmailConfig):
    send_date = datetime.today().strftime("%B %d, %Y")
    lob = 'iPhone'
    sub_lobs = [
        "iPhone 16",
        "iPhone 16 Plus",
        "iPhone 16 Pro",
        "iPhone 16 Pro Max",
    ]
    latest_refresh_time, email_data = channel_view_data_for_email(lob, sub_lobs) # 请求接口中的数据
    
    # 格式化邮件主题
    email_config.subject = email_config.subject.format(
        **{"subject_date": datetime.today().strftime("%Y-%m-%d")}
    )

    # 转化时间格式
    dt_latest_refresh_time = datetime.strptime(latest_refresh_time,"%Y-%m-%d %H:%M:%S")
    # 格式化输出字符串
    formatted_str = dt_latest_refresh_time.strftime("%I:%M %p on %Y-%m-%d")
    template_render_data = {
        "send_date": send_date,
        "data": convert_to_email_data(email_data),
        "data_as_of_datetime": formatted_str,
    }
    template = Template(email_config.content)
    email_config.content = template.render(**template_render_data)
    return email_config, dt_latest_refresh_time


# 注册task
TASK_TYPE_PO_AGING = 101
TASK_TYPE_PO_AGING_PRE_1 = 102
TASK_TYPE_PO_AGING_PRE_2 = 103

TASK_ROUTER = {
    TASK_TYPE_PO_AGING: send_po_aging_email_worker,
    TASK_TYPE_PO_AGING_PRE_1: pre_1_send_po_aging_email_worker,
    TASK_TYPE_PO_AGING_PRE_2: pre_2_send_po_aging_email_worker,
}


def execute_task(task_type: int):
    # 按照不同的type筛选不同的任务
    task = TaskRepository.get_to_do(task_type)
    
    if task is None:
        return
    
    # 如果当前时间距离run_at > 6h 置为超时
    if (datetime.now() - task.run_at) > timedelta(hours=6):
        task.status = TaskStatus.TimeOut.value
        TaskRepository.save(task)
        logger.info(f"{PREFIX_SEND_EMAIL_TASK} {task} timeout..")
        return
    
    worker = TASK_ROUTER.get(task.type, None)
    if worker is not None:
        logger.info(f"{PREFIX_SEND_EMAIL_TASK} {task} begin..")
        worker(task.params)
        task.status = TaskStatus.Done.value
        task.done_at = datetime.now()
        TaskRepository.save(task)
        logger.info(f"{PREFIX_SEND_EMAIL_TASK} {task} end..")


def task_schedule():
    # 按照不同的type筛选不同的任务
    for task_type in TASK_ROUTER.keys():
        execute_task(task_type)
    