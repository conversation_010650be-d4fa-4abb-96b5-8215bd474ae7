from datetime import datetime, timedelta
import socket
from enum import Enum
from sqlalchemy import and_, or_, Column, Integer, String, DateTime
from sqlalchemy.exc import SQLAlchemyError
from util.fast_lite_base import *
from util.conf import logger
import json


class TaskStatus(Enum):
    Ready = "ready"  # 待执行
    Running = "running"  # 进行中
    Done = "done"  # 已完成
    TimeOut = "timeout"  # 超时，异常


# 任务锁自动过期时间
TaskMutexTTL = timedelta(seconds=300)
DateTimeFormat = "%Y-%m-%d %HH:%MM:%SS"


# 任务实体
class Task(FASTLiteBase):
    __tablename__ = 'task'
    __table_args__ = {"schema": "fast_lite"}
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(64), nullable=False, default="")
    desc = Column(String(256), nullable=False, default="")
    status = Column(String(32), default="ready")  # 任务执行状态，enum('ready', 'running', 'done', 'timeout')
    type = Column(Integer)

    partition = Column(Integer, default=0)  # 分区数, 分区数Partition
    cursor = Column(Integer, default=0)  # 任务游标

    run_at = Column(DateTime)  # 期望执行时间
    worker = Column(String(128), default="")  # 当前执行机器ip地址, 以及协程编号（防误删）
    work_mutex_at = Column(DateTime, default=datetime.min)  # 执行锁过期时间，锁过期后，可被其他worker选取。锁过期时间需大于任务执行时间
    retry_times = Column(Integer, default=0)  # 失败重试次数
    begin_at = Column(DateTime, default=datetime.min)  # 最近一次开始执行时间
    done_at = Column(DateTime, default=datetime.min)  # 最近一次完成时间
    create_at = Column(DateTime, default=datetime.min)
    update_at = Column(DateTime, default=datetime.min)
    
    period = Column(String(64))
    params = Column(String(256))

    def __init__(self, name, _type, desc, period, params, run_at=datetime.now(), status=TaskStatus.Ready, partition=0):
        self.name = name
        self.type = _type
        self.desc = desc
        self.run_at = run_at
        self.status = status.value
        self.partition = partition
        self.period = period
        self.params = params

    def __repr__(self):
        d = {
            "name": self.name,
            "desc": self.desc,
            "type": self.type,
            "run_at": self.run_at.strftime(DateTimeFormat),
            "status": self.status,
            "partition": self.partition,
            "worker": self.worker,
            "period": self.period,
            "params": self.params,
        }
        if self.begin_at is not None:
            d["begin_at"] = self.begin_at.strftime(DateTimeFormat)
        return json.dumps(d, indent=True)


# 任务领域仓库
class TaskRepository:
    def __init__(self):
        pass

    @classmethod
    # 任务注册
    def create(self, task: Task) -> None:
        s = FASTLiteSession()
        try:
            s.add(task)
            s.commit()
            s.close()

        except SQLAlchemyError as e:
            logger.error(f"Record already exists:{e}")
            s.rollback()  # 回滚事务，撤销之前的插入操作

    @classmethod
    def bulk_create(cls, tasks: list[Task]):
        s = TaskSession()
        try:
            s.bulk_save_objects(tasks, return_defaults=True)
            s.commit()
        except Exception as e:
            logger.exception(e)
            s.rollback()  # 回滚事务，撤销之前的插入操作
        finally:
            s.close()

    @classmethod
    def get_list(self):
        s = FASTLiteSession()
        ret = s.query(Task).all()
        s.close()
        return ret

    # 获取当前待处理的任务
    # 这里获取历史时间中ready或者在running但是锁时间已经超时的记录，并将其状态改为running
    @classmethod
    def get_to_do(self, type: int):
        s = TaskSession()
        task = None
        try:
            s.begin()
            # sql： (status = ? OR (status = ? AND work_mutex_at < ?)) AND run_at < ?
            q = s.query(Task).filter(Task.run_at < datetime.now()) \
                .filter(Task.type == type) \
                .filter(
                or_(
                    Task.status == TaskStatus.Ready.value,
                    and_(
                        Task.status == TaskStatus.Running.value,
                        Task.work_mutex_at < datetime.now(),
                    ),
                )
            )
            task = q.with_for_update(nowait=True).first()
            if task is not None:
                s.query(Task).filter(Task.id == task.id).update({
                    "status": TaskStatus.Running.value,
                    "work_mutex_at": datetime.now() + TaskMutexTTL,
                    "worker": socket.gethostname(),
                    "begin_at": datetime.now(),
                })
                s.commit()
        except Exception as e:
            logger.error(f"list_todo fail:{e}")
            s.rollback()
            task = None
        finally:
            s.close()
            return task

    @classmethod
    def get(self, task_id: int) -> Task:
        s = FASTLiteSession()
        task = s.query(Task).filter(Task.id == task_id).first()
        s.close()
        return task

    @classmethod
    def get_by_week(self, week: int) -> list:
        s = FASTLiteSession()
        tasks = s.query(Task).filter(Task.fiscal_week == week).all()
        s.close()
        return tasks

    # 释放锁，只有本地ip可以释放，锁过期时间设置为当前-1s
    def unlock(self, task: Task):
        s = FASTLiteSession()
        s.query(Task).filter(Task.task_id == task.task_id). \
            filter(Task.worker == task.worker). \
            update({'work_mutex_at': datetime.utcnow() + timedelta(seconds=-1)})
    
    @classmethod
    def save(cls, task: Task):
        s = FASTLiteSession()
        try:
            if task is not None:
                s.query(Task).filter(Task.id == task.id).update({
                    "status": task.status,
                    "done_at": task.done_at,
                })
                s.commit()
        except Exception as e:
            s.rollback()
            logger.exception(e)
        finally:
            s.close()

    @classmethod
    def get_task_period(cls):
        return ["2024-09-04"]

    @classmethod
    def get_task_periods(cls, type: int, partition: int, period_list: list[str]) -> list:
        s = TaskSession()
        ret = []
        try:
            tasks = (s.query(Task.period)
                        .filter(Task.type == type)
                        .filter(Task.partition == partition)
                        .filter(Task.period.in_(period_list))
                        .all())
            ret = [task[0] for task in tasks]
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return ret

    @classmethod
    def get_task_periods_for_esr(cls, type: int, period_list: list[str]) -> list:
        s = TaskSession()
        ret = []
        try:
            tasks = (s.query(Task.period)
                     .filter(Task.type == type)
                     .filter(Task.period.in_(period_list))
                     .all())
            ret = list(set([task[0] for task in tasks]))

        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return ret
