# ########## must at head ############
# import os
# import sys
#
# import pandas
#
#
# SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
# sys.path.append(os.path.dirname(SCRIPT_DIR))
# ##########      end     ############

from task_kit.worker.smart_tower.smart_tower_waive_worker import SmartTowerWaiveWorker


# usage
# ENV=dev pytest -s test/test_esr.py


def test_waive():
    SmartTowerWaiveWorker(
        params='{"fiscal_qtr_week_name": "FY25Q3W13", "fiscal_week_year": 202539, "email_cmd": "smart_tower_inventory_inv_waive"}'
    ).do()
