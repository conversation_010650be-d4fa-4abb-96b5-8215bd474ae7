import json
from datetime import datetime

from data.fiscal_year_week import FiscalYearWeek
from task_kit.repository.task_repository import TaskRepository, Task, TaskStatus
from util.conf import logger


def create_macro_forecast_email_task_schedule():
    # 获取当前时间
    current_time = datetime.now()
    current_time_str = current_time.strftime('%Y-%m-%d')
    pre_fiscal_list = FiscalYearWeek.get_qtr_by_date(current_time_str, 2)

    # 生成预发送任务数据 周一
    pre_email_task = {"name": "send_macro_forecast_email_pre",
                      "type": 301,
                      "partition": 0,
                      'status': TaskStatus.Ready,
                      "run_time": "18:00:00",
                      "desc": "周一预发送：每个季度给Brian发送邮件，内容为Forecast数据"}
    create_email_task(pre_fiscal_list, pre_email_task)

    # 生成正式发送任务数据
    email_task = {"name": "send_macro_forecast_email",
                  "type": 301,
                  "partition": 0,
                  'status': TaskStatus.Done,
                  "run_time": "09:00:00",
                  "desc": "周二正式发送：每个季度给Brian发送邮件，内容为Forecast数据"}
    # 正式场景从周二开始执行
    fiscal_list = FiscalYearWeek.get_qtr_by_date(current_time_str, 3)
    create_email_task(fiscal_list, email_task)


def create_email_task(fiscal_list: list, task: dict):
    task_need_create_list = []
    for fiscal_item in fiscal_list:
        task_period = fiscal_item.fiscal_dt.strftime('%Y-%m-%d')
        params = {"email_cmd": task["name"], "fiscal_qtr_year_name": fiscal_item.fiscal_qtr_year_name}
        task_name = f"{task_period}-" + task["name"]
        task_need_create_list.append(
            Task(
                name=task_name,
                _type=task["type"],
                desc=task["desc"],
                status=task["status"],
                run_at=datetime.strptime(f'{task_period} {task["run_time"]}', '%Y-%m-%d %H:%M:%S'),
                partition=task['partition'],
                period=task_period,
                params=json.dumps(params),
            )
        )
    if len(task_need_create_list):
        TaskRepository.bulk_create(task_need_create_list)
        logger.info(f"新建任务-{task_need_create_list}")
