from celery import Celery
from urllib.parse import quote_plus as urlquote
from util.conf import logger
from util.redis_pool import redis_pwd, redis_host, redis_port, redis_name

celery = Celery("celery_task", include=[
    "task_kit.task_01",
    "apis.datasource.supply",
    "util.send_email",
    "service.datasource_service"
    # "domain.datasource.impl.automatic.esr_impl"
])

celery_redis_name = 3

# celery的代理服务器
broker_url = "rediss://{}:{}@{}:{}/{}".format(
    '', urlquote(redis_pwd), redis_host, redis_port, celery_redis_name)
celery.conf.broker_url = broker_url
celery.conf.result_backend = broker_url + "?ssl_cert_reqs=CERT_NONE"



