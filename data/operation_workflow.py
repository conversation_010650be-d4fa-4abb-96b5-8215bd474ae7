from util.conf import *

class TblOperationWorkflow(Base):
    __tablename__ = 'tbl_operation_workflow'
    __table_args__ = {
        'schema': f'Workflow'
    }

    id = Column(Integer, autoincrement=True, primary_key=True)
    platform = Column(String(64), nullable=False)
    op_type = Column(Integer, nullable=False)
    uri = Column(String(256), default='')
    person_id = Column(String(256), default=0)
    approved_id = Column(String(256), default='', comment='approved person id')
    resource_id = Column(String(256), default='', comment='abtest/ailab id')
    rtm = Column(SmallInteger)
    # for ailab algorithm model name
    sub_type = Column(SmallInteger, comment='1: Clustering, 2: Forecasting, 3: Anomaly Detection, 4: Correlation')
    fiscal_dt = Column(String(64), default='')
    create_date = Column(DateTime, default=datetime.now, comment='create time')

    def __init__(self, platform, op_type, person_id, rtm, uri='', fiscal_dt='', resource_id='', sub_type=0) -> None:
        self.platform = platform
        self.op_type = op_type
        self.person_id = person_id
        self.uri = uri 
        self.rtm = rtm
        self.fiscal_dt = fiscal_dt
        self.resource_id = resource_id
        self.sub_type = sub_type

    def insert(self):
        s = Session()
        ret = 0
        try:
            s.add(self)
            s.commit()
            ret = self.id 
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret 