from sqlalchemy import case, desc, distinct
from util.gc_dmp_base import *
from util.const import *



class DimFastModelSkuList(GcDmpBase):
    __tablename__ = 'dim_fast_model_sku_list'

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    lob = Column(String(256), comment='')
    model = Column(String(256), comment='')
    sku = Column(String(256), comment='')

    @classmethod
    def get_lob_list(cls):
        s = GcDmpSession()
        res = []
        try:
            res = s.query(cls.lob).group_by(cls.lob).all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return res

    @classmethod
    def get_model_list(cls, lob, rtm, fiscal_week_year):
        s = GcDmpSession()
        res = []
        try:
            query = s.query(DimFastModelSkuList.model).distinct()
            if lob is not None and len(lob) != 0:
                query = query.filter(cls.lob.in_(lob))
            query = query.order_by(desc(cls.model))
            res = query.all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return res

    @classmethod
    def get_sku_list(cls, model: list):
        s = GcDmpSession()
        res = []
        try:
            query = s.query(cls.sku).distinct()
            query = query.filter(cls.model.in_(model))
            res = query \
                .order_by(desc(cls.sku)) \
                .all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return res

    @classmethod
    def get_models_by_skus(cls, rtm: str, lob: list, sku: list,model: list):
        s = GcDmpSession()
        res = []
        try:
            query = s.query(cls.model) \
                .group_by(cls.model)
            if lob is not None and len(lob) != 0:
                query = query.filter(cls.lob.in_(lob))
            if sku is not None and len(sku) != 0:
                query = query.filter(cls.sku.in_(sku))
            if model is not None and len(model) != 0:
                query = query.filter(cls.model.in_(model)) \
                    .order_by(desc(cls.model))

            res = query.all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return res


class DimFastModelSkuMapList(GcDmpBase):
    __tablename__ = 'dim_fast_model_sku_mapping_list'

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    lob = Column(String(256), comment='')
    model = Column(String(256), comment='')
    sku = Column(String(256), comment='')
    mpn_id = Column(String(256), comment='')

    @classmethod
    def get_mpn_list_by_models(cls, model: list):
        s = GcDmpSession()
        res = []
        try:
            res = s.query(cls.mpn_id).filter(cls.model.in_(model)).all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return [x.mpn_id for x in res]

    @classmethod
    def get_mpn_list(cls):
        s = GcDmpSession()
        res = []
        try:
            res = s.query(cls.mpn_id).all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return [x.mpn_id for x in res]


class DimFastiPadSKUList(GcDmpBase):
    __tablename__ = 'dim_fast_ipad_sku_list_a'

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    lob = Column(String(256), comment='')
    model = Column(String(256), comment='')
    sku = Column(String(256), comment='')
    mpn_id = Column(String(256), comment='')

    @classmethod
    def get_mpn_list_by_lob(cls, lob: str):
        s = GcDmpSession()
        res = []
        try:
            res = s.query(cls.mpn_id).filter(cls.lob == lob).all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return [x.mpn_id for x in res]


class DimFastLobSKUList(GcDmpBase):
    __tablename__ = 'dim_fast_sku_list_a'

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    lob = Column(String(256), comment='')
    model = Column(String(256), comment='')
    sku = Column(String(256), comment='')
    mpn_id = Column(String(256), comment='')

    @classmethod
    def get_mpn_list_by_lob(cls, lob: str):
        s = GcDmpSession()
        res = []
        try:
            res = s.query(cls.mpn_id).filter(cls.lob == lob).all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return [x.mpn_id for x in res]
