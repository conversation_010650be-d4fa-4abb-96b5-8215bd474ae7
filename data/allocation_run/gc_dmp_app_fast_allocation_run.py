from util.util import env_dev, pd
from util.gc_dmp_base import *


class AppFastAllocationRunUbEohWi(GcDmpBase):
    __tablename__ = f"app_fast_allocation_run_ub_eoh_wi{'_test' if env_dev() else ''}"
    __table_args__ = {"schema": "gc_dmp_fast"}
    
    id = Column(Integer, primary_key=True)
    fiscal_qtr_week_name = Column(String(256), comment='FY23Q1W12')
    fiscal_week_year = Column(Integer)
    sales_org = Column(String(256))
    rtm = Column(String(256))
    rtml4 = Column(String(256))
    sold_to_id = Column(String(256))
    mpn = Column(String(256))
    sni_cw_minus_1 = Column(Integer)
    ub_eoh = Column(Integer, comment='last week ub eoh')
    avg_ub_lw5 = Column(Float, comment='cum(ub_lw1~ub_lw5)/5')
    
    @classmethod
    def get_ub_eoh_by_week(cls, fiscal_qtr_week_name: str) -> pd.DataFrame:
        s = GcDmpSession()
        ret = pd.DataFrame()
        try:
            query = s.query(cls) \
                .filter(cls.fiscal_qtr_week_name == fiscal_qtr_week_name)
            ret = pd.read_sql_query(query.statement, GcDmpEngine)
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret
