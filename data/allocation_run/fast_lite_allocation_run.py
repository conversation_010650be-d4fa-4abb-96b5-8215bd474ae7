from util.fast_lite_base import *
from util.const import ErrorExcept, ErrCode


class AllocationRunSupplyProtectCalculateResult(FASTLiteBase):
    __tablename__ = "allocation_run_supply_protect_calculate_result"
    __table_args__ = {"schema": "fast_lite"}
    
    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    fiscal_qtr_week_name = Column(String(256), comment='FY23Q1W12')
    fiscal_week_year = Column(Integer)
    sales_org = Column(String(256))
    fph1 = Column(String(256))
    fph3 = Column(String(256))
    project_code = Column(String(256))
    nand = Column(String(256))
    color = Column(String(256))
    mpn = Column(String(256))
    sold_to_id = Column(String(32))
    sold_to_name = Column(String(256))
    max_cw_shipment_plan_consumption_cq_cw = Column(Float)
    shipment_plan_solver_landed_cw = Column(Float)
    shipment_plan_solver_landed_cw1 = Column(Float)
    shipment_plan_solver_landed_cw2 = Column(Float)
    shipment_plan_solver_landed_cw3 = Column(Float)
    shipment_plan_solver_landed_cw4 = Column(Float)
    last_week_por_discrete_cw1 = Column(Float)
    last_week_por_discrete_cw2 = Column(Float)
    last_week_por_discrete_cw3 = Column(Float)
    last_week_por_discrete_cw4 = Column(Float)
    # incremental_cw1 = Column(Float)
    # incremental_cw2 = Column(Float)
    # incremental_cw3 = Column(Float)
    # incremental_cw4 = Column(Float)
    need_protect_cw1 = Column(Float)
    need_protect_cw2 = Column(Float)
    need_protect_cw3 = Column(Float)
    need_protect_cw4 = Column(Float)
    # true_incremental_cw1 = Column(Float)
    # true_incremental_cw2 = Column(Float)
    # true_incremental_cw3 = Column(Float)
    # true_incremental_cw4 = Column(Float)
    # p0_top_up_demand_cw1 = Column(Float)
    # p0_top_up_demand_cw2 = Column(Float)
    # p0_top_up_demand_cw3 = Column(Float)
    # p0_top_up_demand_cw4 = Column(Float)
    # p1_top_up_demand_cw1 = Column(Float)
    # p1_top_up_demand_cw2 = Column(Float)
    # p1_top_up_demand_cw3 = Column(Float)
    # p1_top_up_demand_cw4 = Column(Float)
    # p2_top_up_demand_cw1 = Column(Float)
    # p2_top_up_demand_cw2 = Column(Float)
    # p2_top_up_demand_cw3 = Column(Float)
    # p2_top_up_demand_cw4 = Column(Float)
    
    @classmethod
    def bulk_save(cls, datas: list):
        s = FASTLiteSession()
        try:
            s.execute(cls.__table__.insert(), datas)
            s.commit()
        except Exception as e:
            logger.info(str(e))
            raise ErrorExcept(ErrCode.DBInsert, "insert to db failed" + str(e))
        finally:
            s.close()
        return True
    
    @classmethod
    def delete_by_week(cls, fiscal_qtr_week_name: str):
        s = FASTLiteSession()
        try:
            q = s.query(cls) \
                .filter(cls.fiscal_qtr_week_name == fiscal_qtr_week_name)
            q.delete()
            s.commit()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()

    @classmethod
    def get_supply_by_week(cls, fiscal_qtr_week_name: str):
        s = FASTLiteSession()
        ret = pd.DataFrame()
        try:
            q = s.query(cls) \
                .filter(cls.fiscal_qtr_week_name == fiscal_qtr_week_name)
            ret = pd.read_sql_query(q.statement, fast_lite_engine)
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

    @classmethod
    def get_table_fields_list(cls):
        excluded_fields = ['id']
        column_names = []
        for column in cls.__table__.columns:
            if column.name not in excluded_fields:
                column_names.append(column.name)
        return column_names


class AllocationRunSpecialSupplyCalculateResultTrueIncremental(FASTLiteBase):
    __tablename__ = "allocation_run_special_supply_calculate_result_true_incremental"
    __table_args__ = {"schema": "fast_lite"}
    
    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    fiscal_qtr_week_name = Column(String(256), comment='FY23Q1W12')
    fiscal_week_year = Column(Integer)
    sales_org = Column(String(256))
    project_code = Column(String(256))
    nand = Column(String(256))
    color = Column(String(256))
    mpn = Column(String(256))
    need_protect_cw1 = Column(Float)
    need_protect_cw2 = Column(Float)
    need_protect_cw3 = Column(Float)
    need_protect_cw4 = Column(Float)
    true_incremental_cw1 = Column(Float)
    true_incremental_cw2 = Column(Float)
    true_incremental_cw3 = Column(Float)
    true_incremental_cw4 = Column(Float)
    
    @classmethod
    def bulk_save(cls, datas: list):
        s = FASTLiteSession()
        try:
            s.execute(cls.__table__.insert(), datas)
            s.commit()
        except Exception as e:
            logger.info(str(e))
            raise ErrorExcept(ErrCode.DBInsert, "insert to db failed" + str(e))
        finally:
            s.close()
        return True
    
    @classmethod
    def delete_by_week(cls, fiscal_qtr_week_name: str):
        s = FASTLiteSession()
        try:
            q = s.query(cls) \
                .filter(cls.fiscal_qtr_week_name == fiscal_qtr_week_name)
            q.delete()
            s.commit()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
    
    @classmethod
    def get_table_fields_list(cls):
        excluded_fields = ['id']
        column_names = []
        for column in cls.__table__.columns:
            if column.name not in excluded_fields:
                column_names.append(column.name)
        return column_names


class AllocationRunSpecialSupplyCalculateResultDemandWithTag(FASTLiteBase):
    __tablename__ = "allocation_run_special_supply_calculate_result_demand_with_tag"
    __table_args__ = {"schema": "fast_lite"}
    
    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    fiscal_qtr_week_name = Column(String(256), comment='FY23Q1W12')
    fiscal_week_year = Column(Integer)
    sales_org = Column(String(256))
    fph1 = Column(String(256))
    fph3 = Column(String(256))
    project_code = Column(String(256))
    nand = Column(String(256))
    color = Column(String(256))
    mpn = Column(String(256))
    sold_to_id = Column(String(32))
    sold_to_name = Column(String(256))
    max_cw_shipment_plan_consumption_cq_cw = Column(Float)
    shipment_plan_solver_landed_cw = Column(Float)
    shipment_plan_solver_landed_cw1 = Column(Float)
    shipment_plan_solver_landed_cw2 = Column(Float)
    shipment_plan_solver_landed_cw3 = Column(Float)
    shipment_plan_solver_landed_cw4 = Column(Float)
    last_week_por_discrete_cw1 = Column(Float)
    last_week_por_discrete_cw2 = Column(Float)
    last_week_por_discrete_cw3 = Column(Float)
    last_week_por_discrete_cw4 = Column(Float)
    need_protect_cw1 = Column(Float)
    need_protect_cw2 = Column(Float)
    need_protect_cw3 = Column(Float)
    need_protect_cw4 = Column(Float)
    cw1_p0_demand = Column(Float)
    cw1_p1_demand = Column(Float)
    cw1_p2_demand = Column(Float)
    cw2_p0_demand = Column(Float)
    cw2_p1_demand = Column(Float)
    cw2_p2_demand = Column(Float)
    cw3_p0_demand = Column(Float)
    cw3_p1_demand = Column(Float)
    cw3_p2_demand = Column(Float)
    cw4_p0_demand = Column(Float)
    cw4_p1_demand = Column(Float)
    cw4_p2_demand = Column(Float)

    @classmethod
    def bulk_save(cls, datas: list):
        s = FASTLiteSession()
        try:
            s.execute(cls.__table__.insert(), datas)
            s.commit()
        except Exception as e:
            logger.info(str(e))
            raise ErrorExcept(ErrCode.DBInsert, "insert to db failed" + str(e))
        finally:
            s.close()
        return True
    
    @classmethod
    def delete_by_week(cls, fiscal_qtr_week_name: str):
        s = FASTLiteSession()
        try:
            q = s.query(cls) \
                .filter(cls.fiscal_qtr_week_name == fiscal_qtr_week_name)
            q.delete()
            s.commit()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
    
    @classmethod
    def get_table_fields_list(cls):
        excluded_fields = ['id']
        column_names = []
        for column in cls.__table__.columns:
            if column.name not in excluded_fields:
                column_names.append(column.name)
        return column_names
