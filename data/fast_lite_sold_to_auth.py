from util.const import *
from util.fast_lite_base import *
 
    
class TblRtmSoldToAuth(FASTLiteBase):
    __tablename__ = 'tbl_rtm_sold_to_auth'
    __table_args__ = {'schema': 'fast_lite'}
    
    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    rtm = Column(String(256), comment='')
    sold_to = Column(String(256), comment='abbre.')
    person_id = Column(String(256), comment='')

    @classmethod
    def get_sold_to_auth(self, rtm: str, person_id: str):
        s = FASTLiteSession()
        ret = []
        try:
            if rtm is None or person_id is None:
                return ret
            q = s.query(TblRtmSoldToAuth.sold_to.label('sold_to'))\
                .filter(TblRtmSoldToAuth.rtm == rtm)\
                .filter(TblRtmSoldToAuth.person_id == person_id)
            
            query_result = q.all()
            for i in query_result:
                ret.append(i['sold_to'])
        except Exception as e:
            logger.exception(str(e))
        finally:
            s.close()
        return ret
