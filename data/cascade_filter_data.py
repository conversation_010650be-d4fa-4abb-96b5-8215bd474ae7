from operator import and_
from sqlalchemy import or_, and_, desc,case
import pandas as pd

from util.const import *
from util.gc_dmp_base import *


class DimFastModelSkuList(GcDmpBase):
    __tablename__ = 'dim_fast_model_sku_list'
    __table_args__ = {'schema': 'gc_dmp_fast'}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    lob = Column(String(256), comment='')
    model = Column(String(256), comment='')
    sku = Column(String(256), comment='')
    create_time = Column(DateTime, comment='create time')
    update_time = Column(DateTime, comment='update time')

    @classmethod
    def get_sku(self, lob: str = 'iPhone', model: list = []) -> list:
        s = GcDmpSession()
        ret = []
        try:
            ret = s.query(DimFastModelSkuList.sku.distinct(),
                               DimFastModelSkuList.model,)\
                .filter(DimFastModelSkuList.lob == lob)\
                .filter(DimFastModelSkuList.model.in_(model)) \
                .order_by(desc(DimFastModelSkuList.sku))\
                .all()

        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

    @classmethod
    def get_model_list(cls, lob, rtm, fiscal_week_year):
        s = GcDmpSession()
        res = []
        try:
            query = s.query(DimFastModelSkuList.model).distinct()\
                    .filter(cls.fiscal_week_year == fiscal_week_year)
            if lob is not None and len(lob) != 0:
                query = query.filter(cls.lob.in_(lob))
            query = query.order_by(desc(cls.model))
            res = query.all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return res


class DimFastBusinessSoldtoMapping(GcDmpBase):
    __tablename__ = 'dim_fast_business_soldto_mapping'
    __table_args__ = {'schema': 'gc_dmp_fast'}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    rtm = Column(String(256), comment='')
    business_type = Column(String(256), comment='')
    sold_to_id = Column(String(256), comment='')
    sold_to_name_en = Column(String(256), comment='')
    abbre = Column(String(256), comment='')
    start_week = Column(Integer)
    end_week = Column(Integer)
    create_time = Column(DateTime, comment='create time')
    update_time = Column(DateTime, comment='update time')

    @classmethod
    def get_business_type(cls, fiscal_week_year: int, rtm: list) -> list:
        s = GcDmpSession()
        ret = []
        try:
            ret = s.query(cls.business_type.distinct(),
                          cls.rtm)\
                .filter(cls.rtm.in_(rtm))\
                .filter(cls.start_week <= fiscal_week_year)\
                .filter(cls.end_week >= fiscal_week_year)\
                .all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

    @classmethod
    def get_sold_to(cls, fiscal_week_year: int, rtm: list, business_type: list, fuzzy_filter: str) -> list:
        s = GcDmpSession()
        ret = []
        try:
            q = s.query(cls.abbre.distinct(),
                        cls.business_type,
                        cls.sold_to_id,
                        cls.sold_to_name_en)\
                .filter(cls.rtm.in_(rtm))\
                .filter(cls.business_type.in_(business_type))\
                .filter(cls.start_week <= fiscal_week_year)\
                .filter(cls.end_week >= fiscal_week_year)
            if fuzzy_filter is not None and fuzzy_filter.strip() != '':
                q = q.filter(or_(cls.sold_to_id.like(f"%{fuzzy_filter}%"),
                                 cls.abbre.like(f"%{fuzzy_filter}%")))
            
            # q = q.order_by(func.field(cls.rtm, *rtm),
            #                cls.business_type,
            #                func.cast(cls.sold_to_id, Integer)
            #             )
                
            ret = q.all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret


    @classmethod
    def get_all_data(self):
        s = GcDmpSession()
        ret = []
        try:
            data = s.execute("SELECT * FROM dim_fast_business_soldto_mapping")
            ret = data.fetchall()

        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

    @classmethod
    def get_rtm_business_type(cls, fiscal_week_year: int) -> list:
        s = GcDmpSession()
        ret = []
        if fiscal_week_year is None:
            return ret
        try:
            data = s.execute(f'''
                             SELECT DISTINCT REPLACE(REPLACE(rtm, 'Lifestyle', 'Mono'), 'Channel Online', 'Online') AS rtm, 
                             business_type
                             FROM dim_fast_business_soldto_mapping
                             WHERE start_week <= {fiscal_week_year} and end_week >= {fiscal_week_year};
                             ''')
            ret = data.fetchall()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

    @classmethod
    def get_business_type_relation(cls, fiscal_week_year: int):
        ret = pd.DataFrame()
        if fiscal_week_year is None:
            return ret
        try:
            sql_statement = f'''
                SELECT DISTINCT REPLACE(REPLACE(rtm, 'Lifestyle', 'Mono'), 'Channel Online', 'Online') AS rtm, 
                business_type, sold_to_id, abbre
                FROM dim_fast_business_soldto_mapping
                WHERE start_week <= {fiscal_week_year} and end_week >= {fiscal_week_year}
                and business_type != 'JD';
            '''
            ret = pd.read_sql_query(sql_statement, GcDmpEngine)
            # 添加新的 JD self-run合并后的虚拟sold_to_id为1118511
            new_row = {
                'rtm': 'Online',
                'business_type': 'JD self-run',
                'sold_to_id': '1118511',
                'abbre': 'JD self-run'
            }
            ret = ret.append(new_row, ignore_index=True)
        except Exception as e:
            logger.exception(e)
        return ret

    @classmethod
    def get_sold_to_id(cls, fiscal_week_year: int, rtm: str) -> list:
        s = GcDmpSession()
        ret = []
        if fiscal_week_year is None:
            return ret
        try:
            data = s.execute(f'''
                             SElECT t.sold_to_id as sold_to_id
                             FROM (
                             SELECT DISTINCT REPLACE(REPLACE(rtm, 'Lifestyle', 'Mono'), 'Channel Online', 'Online') AS rtm,
                             sold_to_id
                             FROM dim_fast_business_soldto_mapping
                             WHERE start_week <= {fiscal_week_year} and end_week >= {fiscal_week_year}
                             and business_type != 'JD') as t
                             WHERE t.rtm = '{rtm}';
                             ''')
            fetch_data = data.fetchall()
            ret = [i[0] for i in fetch_data]
            # 特殊的，本期JD sold-to不需拆分，JD self-run合并后的虚拟sold_to_id为1118511
            if rtm == AllocationRTM.Online:
                ret.append('1118511')
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret


class DimFastModelMappingList(GcDmpBase):
    __tablename__ = 'dim_fast_model_sku_mapping_list'
    __table_args__ = {'schema': 'gc_dmp_fast'}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    lob = Column(String(256), comment='')
    model = Column(String(256), comment='')
    sku = Column(String(256), comment='')
    mpn_id = Column(String(256), comment='')
    project_code = Column(String(256), comment='')
    create_time = Column(DateTime, comment='create time')
    update_time = Column(DateTime, comment='update time')

    @classmethod
    def get_mpn_projcet_code(self) -> pd.DataFrame:
        s = GcDmpSession()
        ret = pd.DataFrame()
        try:
            query_sql = "select mpn_id AS MPN, project_code as 'Project Code' from gc_dmp_fast.dim_fast_model_sku_mapping_list;"
                    
            ret = pd.read_sql_query(query_sql, GcDmpEngine)

        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

