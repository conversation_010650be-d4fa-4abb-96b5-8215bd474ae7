from util.const import *
from util.fast_lite_base import *
from util.gc_dmp_base import *


class FastLiteAppFastDemandSummaryWaTemp(FASTLiteBase):
    __tablename__ = 'app_fast_demand_summary_wa_temp'
    __table_args__ = {'schema': 'fast_lite'}
    
    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    fiscal_qtr_week_name = Column(String(256), comment='FY23Q1W12', index=True)
    fiscal_week_year = Column(Integer, comment='')
    week_begin_dt = Column(Date, comment='')
    week_end_dt = Column(Date, comment='')
    rtm = Column(String(256), comment='', index=True)
    sold_to_id = Column(Integer, comment='', index=True)
    lob = Column(String(256), comment='')
    sub_lob = Column(String(256), comment='')
    fph4 = Column(String(256), comment='')
    project_code = Column(String(256), comment='')
    sku = Column(String(256), comment='')
    mpn_id = Column(String(256), comment='')
    demand_cw = Column(Float, comment='')
    demand_cw1 = Column(Float, comment='')
    demand_cw2 = Column(Float, comment='')
    demand_cw3 = Column(Float, comment='')
    shipment_plan_cw = Column(Float, comment='')
    shipment_plan_cw1 = Column(Float, comment='')
    shipment_plan_cw2 = Column(Float, comment='')
    shipment_plan_cw3 = Column(Float, comment='')
    gross_billing_units_cw = Column(Float, comment='')
    sp_remaining_cw1 = Column(Float, comment='')
    sp_remaining_cw2 = Column(Float, comment='')
    top_up_demand_cw1 = Column(Float, comment='')
    top_up_demand_cw2 = Column(Float, comment='')
    top_up_demand_cw3 = Column(Float, comment='')
    top_up_demand_cw4 = Column(Float, comment='')
    po_needed_cw = Column(Float, comment='')
    po_needed_cw1 = Column(Float, comment='')
    po_needed_cw2 = Column(Float, comment='')
    po_needed_cw3 = Column(Float, comment='')
    
    def demand_query_and_insert_data(table_name: str, fiscal_qtr_week_name: str):
        s = FASTLiteSession()
        s1 = GcDmpSession()
        try:
            if fiscal_qtr_week_name is None:
                return ''
            results = []
            if table_name == 'app_fast_demand_mono_summary_wa':
                mono_data = s1.execute(f'''SELECT fiscal_qtr_week_name,fiscal_week_year,week_begin_dt,week_end_dt,
                                    rtm,sold_to_id,lob,sub_lob,fph4,project_code,sku,mpn_id,demand_cw,demand_cw1,
                                    demand_cw2,demand_cw3,shipment_plan_cw,shipment_plan_cw1,shipment_plan_cw2,
                                    shipment_plan_cw3,gross_billing_units_cw,sp_remaining_cw1,sp_remaining_cw2,
                                    top_up_demand_cw1,top_up_demand_cw2,top_up_demand_cw3, NULL as 'top_up_demand_cw4',
                                    NULL as 'po_needed_cw' ,po_needed_cw1, NULL as 'po_needed_cw2', NULL as 'po_needed_cw3' 
                                    FROM {table_name} WHERE fiscal_qtr_week_name = '{fiscal_qtr_week_name}'
                                ''')
                mono_data_list = mono_data.fetchall()
                results = [dict(item) for item in mono_data_list]
            else:
                new_data = s1.execute(f'''SELECT fiscal_qtr_week_name,fiscal_week_year,week_begin_dt,week_end_dt,
                                    rtm,sold_to_id,lob,sub_lob,fph4,project_code,sku,mpn_id,demand_cw,demand_cw1,
                                    demand_cw2,demand_cw3,shipment_plan_cw,shipment_plan_cw1,shipment_plan_cw2,
                                    shipment_plan_cw3,gross_billing_units_cw,sp_remaining_cw1,sp_remaining_cw2,
                                    top_up_demand_cw1,top_up_demand_cw2,top_up_demand_cw3, NULL as 'top_up_demand_cw4',
                                    po_needed_cw,po_needed_cw1,po_needed_cw2,po_needed_cw3 
                                    FROM {table_name} WHERE fiscal_qtr_week_name = '{fiscal_qtr_week_name}'
                                    ''')
                data_list = new_data.fetchall()
                results = [dict(item) for item in data_list]
            s.execute(FastLiteAppFastDemandSummaryWaTemp.__table__.insert(), results)
            s.commit()
            
            return f"{fiscal_qtr_week_name}: insert new data [ {len(results)} ] to [ {FastLiteAppFastDemandSummaryWaTemp.__tablename__} ] from [ {table_name} ].{DOUBLE_LINE_FEED}"
        except Exception as e:
            logger.exception(str(e))
            return ''
        finally:
            s.close()
            s1.close()

    def demand_gen_ent_edu_sold_to_data(fiscal_qtr_week_name: str):
        s = FASTLiteSession()
        s1 = GcDmpSession()
        try:
            if fiscal_qtr_week_name is None:
                return ''
            new_data = s1.execute(f'''SELECT rtm, sold_to_id
                                    FROM dim_fast_business_soldto_mapping 
                                    WHERE rtm in ('ENT', 'EDU')
                                ''')
            data_list = new_data.fetchall()
            sku_data = s1.execute(f'''SELECT lob, model, sku
                                    FROM dim_fast_model_sku_list 
                                    WHERE model in ('iPhone 14 Pro Max', 'iPhone 14 Pro', 'iPhone 14 Plus', 
                                    'iPhone 14', 'iPhone 13', 'iPhone 13 mini', 'iPhone 12', 'iPhone 11', 'iPhone SE (3rd Gen)')
                                ''')
            sku_list = sku_data.fetchall()
            results = [dict(item) for item in data_list]
            fill_data = {
                'fiscal_qtr_week_name': fiscal_qtr_week_name,
                'fiscal_week_year': None, 
                'week_begin_dt': None, 
                'week_end_dt': None,
                'fph4': None, 
                'project_code': None, 
                'mpn_id': None, 
                'demand_cw': None, 
                'demand_cw1': None, 
                'demand_cw2': None, 
                'demand_cw3': None, 
                'shipment_plan_cw': None, 
                'shipment_plan_cw1': None, 
                'shipment_plan_cw2': None, 
                'shipment_plan_cw3': None, 
                'gross_billing_units_cw': None, 
                'sp_remaining_cw1': None, 
                'sp_remaining_cw2': None, 
                'top_up_demand_cw1': None, 
                'top_up_demand_cw2': None, 
                'top_up_demand_cw3': None, 
                'top_up_demand_cw4': None, 
                'po_needed_cw': None, 
                'po_needed_cw1': None, 
                'po_needed_cw2': None, 
                'po_needed_cw3': None
            }
            insert_data = []
            for i in results:
                sold_to_data = {
                    'rtm': i['rtm'],
                    'sold_to_id': int(i['sold_to_id'])
                }
                for m in sku_list:
                    sku_insert = {
                        'lob': m['lob'], 
                        'sub_lob': m['model'], 
                        'sku': m['sku']
                    }
                    insert_data.append({**sold_to_data, **fill_data, **sku_insert})
            s.execute(FastLiteAppFastDemandSummaryWaTemp.__table__.insert(), insert_data)
            s.commit()
            return f"{fiscal_qtr_week_name}: insert new data [ {len(insert_data)} ] to [ {FastLiteAppFastDemandSummaryWaTemp.__tablename__} ] by [ backend generate ].{DOUBLE_LINE_FEED}"
        except Exception as e:
            logger.exception(str(e))
            return ''
        finally:
            s.close()
            s1.close()


class NewAppFastForecastTrmSoWaTemp(FASTLiteBase):
    __tablename__ = 'app_fast_forecast_rtm_so_wa_temp'
    __table_args__ = {'schema': 'fast_lite'}
    
    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    rtm = Column(String(256), comment='', index=True)
    week_date = Column(String(256), comment='FY23Q1W12', index=True)
    fiscal_week_year = Column(Integer, comment='202312')
    fiscal_quarter = Column(Integer, comment='')
    fiscal_week = Column(Integer, comment='')
    week_begin_dt = Column(String(256), comment='')
    week_end_dt = Column(String(256), comment='')
    project_cd = Column(String(256), comment='')
    cust_id = Column(String(256), comment='', index=True)
    lob = Column(String(256), comment='')
    model = Column(String(256), comment='')
    sku = Column(String(256), comment='')
    fph4 = Column(String(256), comment='')
    mpn_id = Column(String(256), comment='')
    show_week = Column(Integer, comment='对应的是哪一周的数据')
    data_type = Column(String(256), comment='')
    fcst = Column(Integer, comment='')
    
    @classmethod
    def bulk_save(self, objs: list):
        s = FASTLiteSession()
        try:
            s.execute(NewAppFastForecastTrmSoWaTemp.__table__.insert(), objs)
            s.commit()
        except Exception as e:
            logger.exception(str(e))
        finally:
            s.close()


class LiteNewAppFastForecastTrmSoWaTemp(FASTLiteBase):
    __tablename__ = 'new_app_fast_forecast_rtm_so_wa_temp'
    __table_args__ = {'schema': 'fast_lite'}
    
    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    week_date = Column(String(256), comment='FY23Q1W12', index=True)
    rtm = Column(String(256), comment='', index=True)
    business_type = Column(String(256), comment='', index=True)
    sold_to_id = Column(String(256), comment='', index=True)
    sold_to_name = Column(String(256), comment='', index=True)
    lob = Column(String(256), comment='')
    model = Column(String(256), comment='')
    sku = Column(String(256), comment='')
    week_in_fiscal_quarter = Column(Integer, comment='')
    total_weeks_in_quarter = Column(Integer, comment='')
    cq_total = Column(Integer, comment='')
    cq_w1 = Column(Integer, comment='')
    cq_w2 = Column(Integer, comment='')
    cq_w3 = Column(Integer, comment='')
    cq_w4 = Column(Integer, comment='')
    cq_w5 = Column(Integer, comment='')
    cq_w6 = Column(Integer, comment='')
    cq_w7 = Column(Integer, comment='')
    cq_w8 = Column(Integer, comment='')
    cq_w9 = Column(Integer, comment='')
    cq_w10 = Column(Integer, comment='')
    cq_w11 = Column(Integer, comment='')
    cq_w12 = Column(Integer, comment='')
    cq_w13 = Column(Integer, comment='')
    cq_w14 = Column(Integer, comment='')
    nq_w1 = Column(Integer, comment='')
    nq_w2 = Column(Integer, comment='')
    nq_w3 = Column(Integer, comment='')
    nq_w4 = Column(Integer, comment='')
    nq_w5 = Column(Integer, comment='')
    nq_w6 = Column(Integer, comment='')
    nq_w7 = Column(Integer, comment='')
    nq_w8 = Column(Integer, comment='')
    nq_w9 = Column(Integer, comment='')
    nq_w10 = Column(Integer, comment='')
    nq_w11 = Column(Integer, comment='')
    nq_w12 = Column(Integer, comment='')
    nq_w13 = Column(Integer, comment='')
    nq_w14 = Column(Integer, comment='') 

    @classmethod
    def gen_data_by_week(self, fiscal_qtr_week_name: str):
        s = FASTLiteSession()
        try:
            if fiscal_qtr_week_name is None:
                return ''
            new_data = s.execute(f'''
                        SELECT week_date,rtm,business_type, cust_id as sold_to_id, abbre as sold_to_name,lob,model,sku,week_in_fiscal_quarter, total_weeks_in_quarter, (ifnull(cq_w1,0)+ifnull(cq_w2,0)+ifnull(cq_w3,0)+ifnull(cq_w4,0)+ifnull(cq_w5,0)+ifnull(cq_w6,0)+ifnull(cq_w7,0)+ifnull(cq_w8,0)+ifnull(cq_w9,0)+ifnull(cq_w10,0)+ifnull(cq_w11,0)+ifnull(cq_w12,0)+ifnull(cq_w13,0)+ifnull(cq_w14,0)) AS cq_total, cq_w1, cq_w2, cq_w3, cq_w4, cq_w5, cq_w6, cq_w7, cq_w8, cq_w9, cq_w10, cq_w11, cq_w12, cq_w13, cq_w14, nq_w1, nq_w2, nq_w3, nq_w4, nq_w5, nq_w6, nq_w7, nq_w8, nq_w9, nq_w10, nq_w11, nq_w12, nq_w13, nq_w14 FROM 
                        (SELECT a.week_date, a.rtm, a.cust_id, d.business_type, d.abbre, a.lob, a.model, a.sku, c.wifq AS week_in_fiscal_quarter, c.total_weeks_in_quarter, max(CASE WHEN a.fiscal_quarter = c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=1 THEN fcst ELSE NULL END) cq_w1, max(CASE WHEN a.fiscal_quarter = c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=2 THEN fcst ELSE NULL END) cq_w2, max(CASE WHEN a.fiscal_quarter = c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=3 THEN fcst ELSE NULL END) cq_w3, max(CASE WHEN a.fiscal_quarter = c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=4 THEN fcst ELSE NULL END) cq_w4, max(CASE WHEN a.fiscal_quarter = c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=5 THEN fcst ELSE NULL END) cq_w5, max(CASE WHEN a.fiscal_quarter = c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=6 THEN fcst ELSE NULL END) cq_w6, max(CASE WHEN a.fiscal_quarter = c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=7 THEN fcst ELSE NULL END) cq_w7, max(CASE WHEN a.fiscal_quarter = c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=8 THEN fcst ELSE NULL END) cq_w8, max(CASE WHEN a.fiscal_quarter = c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=9 THEN fcst ELSE NULL END) cq_w9, max(CASE WHEN a.fiscal_quarter = c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=10 THEN fcst ELSE NULL END) cq_w10, max(CASE WHEN a.fiscal_quarter = c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=11 THEN fcst ELSE NULL END) cq_w11, max(CASE WHEN a.fiscal_quarter = c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=12 THEN fcst ELSE NULL END) cq_w12, max(CASE WHEN a.fiscal_quarter = c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=13 THEN fcst ELSE NULL END) cq_w13, max(CASE WHEN a.fiscal_quarter = c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=14 THEN fcst ELSE NULL END) cq_w14, max(CASE WHEN a.fiscal_quarter > c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=1 THEN fcst ELSE NULL END) nq_w1, max(CASE WHEN a.fiscal_quarter > c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=2 THEN fcst ELSE NULL END) nq_w2, max(CASE WHEN a.fiscal_quarter > c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=3 THEN fcst ELSE NULL END) nq_w3, max(CASE WHEN a.fiscal_quarter > c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=4 THEN fcst ELSE NULL END) nq_w4, max(CASE WHEN a.fiscal_quarter > c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=5 THEN fcst ELSE NULL END) nq_w5, max(CASE WHEN a.fiscal_quarter > c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=6 THEN fcst ELSE NULL END) nq_w6, max(CASE WHEN a.fiscal_quarter > c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=7 THEN fcst ELSE NULL END) nq_w7, max(CASE WHEN a.fiscal_quarter > c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=8 THEN fcst ELSE NULL END) nq_w8, max(CASE WHEN a.fiscal_quarter > c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=9 THEN fcst ELSE NULL END) nq_w9, max(CASE WHEN a.fiscal_quarter > c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=10 THEN fcst ELSE NULL END) nq_w10, max(CASE WHEN a.fiscal_quarter > c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=11 THEN fcst ELSE NULL END) nq_w11, max(CASE WHEN a.fiscal_quarter > c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=12 THEN fcst ELSE NULL END) nq_w12, max(CASE WHEN a.fiscal_quarter > c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=13 THEN fcst ELSE NULL END) nq_w13, max(CASE WHEN a.fiscal_quarter > c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=14 THEN fcst ELSE NULL END) nq_w14
                        FROM (SELECT * FROM app_fast_forecast_rtm_so_wa WHERE week_date = '{fiscal_qtr_week_name}') AS a 
                        LEFT JOIN (SELECT week_in_fiscal_quarter, fiscal_week_year AS bf FROM fiscal_week_year) AS b ON a.show_week = b.bf 
                        LEFT JOIN (SELECT week_in_fiscal_quarter AS wifq, total_weeks_in_quarter, fiscal_quarter AS current_week_fiscal_quarter, fiscal_week_year AS cf FROM fiscal_week_year) AS c ON a.fiscal_week_year = c.cf 
                        RIGHT JOIN dim_fast_business_soldto_mapping AS d ON a.cust_id = d.sold_to_id 
                        GROUP BY a.week_date,a.rtm,a.cust_id,a.lob, a.model, a.sku, a.mpn_id, d.business_type, d.abbre) as d;
                      ''')
            data_list = new_data.fetchall()
            results = [dict(item) for item in data_list]
            s.execute(LiteNewAppFastForecastTrmSoWaTemp.__table__.insert(), results)
            s.commit()
            return f"{fiscal_qtr_week_name}: insert new data [ {len(results)} to [ {LiteNewAppFastForecastTrmSoWaTemp.__tablename__} ].{DOUBLE_LINE_FEED}"
        except Exception as e:
            logger.exception(str(e))
            return ''
        finally:
            s.close() 


class DimFastBusinessSoldtoMappingTemp(FASTLiteBase):
    __tablename__ = 'dim_fast_business_soldto_mapping_temp'
    __table_args__ = {'schema': 'fast_lite'}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    rtm = Column(String(256), comment='')
    business_type = Column(String(256), comment='')
    sold_to_id = Column(String(256), index=True, comment='')
    sold_to_name_en = Column(String(256), comment='')
    abbre = Column(String(256), comment='')
    start_week = Column(Integer)
    end_week = Column(Integer)
    create_time = Column(DateTime, comment='create time')
    update_time = Column(DateTime, comment='update time')

    @classmethod
    def bulk_save(self, objs: list):
        s = FASTLiteSession()
        try:
            s.execute(self.__table__.insert(), objs)
            s.commit()
        except Exception as e:
            logger.exception(str(e))
        finally:
            s.close()
