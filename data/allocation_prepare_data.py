from sqlalchemy import desc, text

from util.const import *
from util.fast_lite_base import *
from data.fiscal_year_week import FiscalYearWeek


class TblAllocationPrepare(FASTLiteBase):
    __tablename__ = "tbl_allocation_prepare"
    
    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    fiscal_qtr_week_name = Column(String(256), comment='FY23Q1W12')
    fiscal_week_year = Column(Integer, comment='202312')
    rtm = Column(String(256))
    lob = Column(String(256))
    operate_phase = Column(String(256))
    # phase1
    upload_status = Column(String(256))
    # RTMs上传记录
    upload_file_name = Column(String(256))
    upload_file_path = Column(String(256))
    upload_file_version = Column(Integer)
    upload_by = Column(String(256), comment='Nick Name + Last Name')
    uploader_email = Column(String(256), comment='xxx.apple.com')
    upload_at = Column(DateTime)
    # CP&F上传记录
    upload_file_name_cpf = Column(String(256))
    upload_file_path_cpf = Column(String(256))
    upload_file_version_cpf = Column(Integer)
    upload_by_cpf = Column(String(256), comment='Nick Name + Last Name')
    uploader_email_cpf = Column(String(256), comment='xxx.apple.com')
    upload_at_cpf = Column(DateTime)
    upload_file_download_cpf = Column(String(256))
    # CP&F操作- download,rerun
    rtm_file_download_by_cpf = Column(Integer, default=0, comment='0: 未被CP&F下载, >0: 被CP&F下载次数')
    rerun = Column(Integer, default=0, comment='0: 未rerun, >0: rerun次数')
    is_first_phase_approved = Column(SmallInteger, server_default='0', comment='0:未审批; 1:已审批')
    first_phase_error = Column(SmallInteger, server_default='0', comment='0:no error; 1:error')
    first_phase_error_process = Column(SmallInteger, server_default='0',
                                       comment='0:未操作; 1:confirm; 2:upload; 3:rerun; 4:timeout')
    
    # phase2
    second_phase_status = Column(Integer, default=1)

    # phase3
    third_phase_status = Column(Integer, default=0)
    third_phase_rerun = Column(Integer, default=0, comment='0: 未rerun, >0: rerun次数')
    third_phase_upload_version = Column(Integer, default=0, comment='上传文件版本加和')
    third_phase_cpf_uploaded = Column(Integer, default=0, comment='0:CP&F未上传; 1:CP&F已上传')
    is_third_phase_approved = Column(SmallInteger, server_default='0', comment='0:未审批; 1:已审批')
    
    cpf_confirmed = Column(Integer, default=0, comment='0:未确认; 1:已确认')    

    update_by = Column(String(256), comment='Nick Name + Last Name')
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    
    def as_dict(self):
        return {c.name: self._convert_datetime(getattr(self, c.name))
                if isinstance(getattr(self, c.name), datetime) 
                else getattr(self, c.name) 
                for c in self.__table__.columns}

    def _convert_datetime(self, dt):
        return dt.strftime(DateTimeFormat)
    
    @classmethod
    def get_fiscal_week(cls, rtm):
        s = FASTLiteSession()
        ret = []
        try:
            ret = s.query(cls.fiscal_week_year.distinct().label(StrFiscalWeekYear), 
                          cls.fiscal_qtr_week_name.label(StrFiscalQtrWeekName))\
                .filter(cls.rtm == rtm)\
                .order_by(desc(cls.fiscal_week_year))\
                .limit(14)\
                .all()
            ret = [{StrFiscalWeekYear: x[StrFiscalWeekYear],
                    StrFiscalQtrWeekName: x[StrFiscalQtrWeekName] } for x in ret]
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

    @classmethod
    def get_allocation_prepare_list(cls, rtm, fiscal_week_year):
        s = FASTLiteSession()
        ret = []
        try:
            ret = s.query(cls)\
                .filter(cls.rtm == rtm)\
                .filter(cls.fiscal_week_year == fiscal_week_year)\
                .all()
            ret = [{
                "id": x.id,
                StrFiscalWeekYear: x.fiscal_week_year,
                StrFiscalQtrWeekName: x.fiscal_qtr_week_name,
                'rtm': x.rtm,
                'lob': x.lob,
                'operate_phase': x.operate_phase,
                'update_by': x.update_by,
                'update_time': x.update_time.strftime(DateTimeFormat) if x.update_by else None} for x in ret]
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret
    
    @classmethod
    def bulk_save(cls, datas: list):
        s = FASTLiteSession()
        try:
            s.bulk_insert_mappings(cls, datas)
            s.commit()
        except Exception as e:
            s.rollback()
            raise ErrorExcept(ErrCode.DBInsert, "insert to database failed" + str(e))
        finally:
            s.close()
        return cls.id
    
    @classmethod
    def get_detail(cls, id: int) -> dict:
        s = FASTLiteSession()
        ret = {}
        try:
            ret = s.query(cls)\
                .filter(cls.id == id)\
                .first()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        if ret is not None:
            ret = ret.as_dict()
        return ret
    
    @classmethod
    def get_template_file_info(cls, id: int) -> dict:
        s = FASTLiteSession()
        ret = {}
        try:
            ret = s.query(cls.template_file_name,
                          cls.template_file_path,
                          cls.template_file_generate_at)\
                .filter(cls.id == id)\
                .first()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        if ret is not None:
            ret = ret.as_dict()
        return ret

    @classmethod
    def find_by_fiscal_week_year(cls, fiscal_week_year):
        s = FASTLiteSession()
        ret = []
        try:
            ret = s.query(cls) \
                .filter(cls.fiscal_week_year == fiscal_week_year) \
                .all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

    @classmethod
    def find_by_fiscal_week_year_and_rtm(cls, fiscal_week_year, rtm):
        s = FASTLiteSession()
        ret = []
        try:
            ret = s.query(cls) \
                .filter(cls.fiscal_week_year == fiscal_week_year) \
                .filter(cls.rtm == rtm) \
                .all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        if len(ret) == 0:
            raise ErrorExcept(ErrCode.DBQueryError, 'can not found record this rtm')
        return ret[0]

    def save(self) -> int:
        s = FASTLiteSession()
        try:
            s.add(self)
            s.commit()
            v_id = self.id
        except Exception as e:
            logger.exception(e)
            v_id = 0
        finally:
            s.close()
        return v_id

    @classmethod
    def update_data_by_id(cls, id: int, update_data: dict) -> bool:
        s = FASTLiteSession()
        try:
            s.query(cls) \
                .filter(cls.id == id) \
                .update(update_data)
            s.commit()
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()

    @classmethod
    def get_allocation_prepare_cpf_list(cls, fiscal_qtr_week_name, lob):
        s = FASTLiteSession()
        ret = []
        try:
            ret = s.query(cls) \
                .filter(cls.fiscal_qtr_week_name == fiscal_qtr_week_name) \
                .filter(cls.lob == lob) \
                .all()
            ret = [{
                "id": x.id,
                "rtm": x.rtm,
                "lob": x.lob,
                'upload_status': x.upload_status,
                'fiscal_week_year': x.fiscal_week_year,
                'fiscal_qtr_week_name': x.fiscal_qtr_week_name,
                'is_first_phase_approved': x.is_first_phase_approved,
                'upload_file_path': x.upload_file_path,
                'upload_file_name': x.upload_file_name,
                'upload_file_name_cpf': x.upload_file_name_cpf,
                'rtm_file_download_by_cpf': x.rtm_file_download_by_cpf,
                'second_phase_status': x.second_phase_status,
                'upload_file_path_cpf': x.upload_file_path_cpf,
                "first_phase_error": x.first_phase_error,
                "first_phase_error_process": x.first_phase_error_process
                } for x in ret]
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

    @classmethod
    def update_by_week_lob(cls, fiscal_qtr_week_name, lob, update_data: dict):
        s = FASTLiteSession()
        try:
            s.query(cls) \
                .filter(cls.fiscal_qtr_week_name == fiscal_qtr_week_name) \
                .filter(cls.lob == lob) \
                .update(update_data)
            s.commit()
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()

    @classmethod
    def update_by_week_rtm_lob(cls, fiscal_qtr_week_name, rtm, lob, update_data: dict):
        s = FASTLiteSession()
        try:
            s.query(cls) \
                .filter(cls.fiscal_qtr_week_name == fiscal_qtr_week_name) \
                .filter(cls.lob == lob) \
                .filter(cls.rtm == rtm) \
                .update(update_data)
            s.commit()
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()

    @classmethod
    def count_current_week_record(cls, fiscal_week_year: int) -> int:
        s = FASTLiteSession()
        ret = 0
        try:
            ret = s.query(func.count(cls.id))\
                .filter(cls.fiscal_week_year == fiscal_week_year)\
                .scalar()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

    @classmethod
    def confirm_demand(cls, fiscal_week_year: int, rtm: str, lob: str = 'iPad'):
        s = FASTLiteSession()
        try:
            s.query(cls) \
                .filter(cls.fiscal_week_year == fiscal_week_year) \
                .filter(cls.lob == lob) \
                .filter(cls.rtm == rtm) \
                .update({cls.cpf_confirmed: 1})
            s.commit()
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return cls.id

    @classmethod
    def find_by_week_and_rtm_list(cls, fiscal_week_year, rtm_list):
        s = FASTLiteSession()
        ret = []
        try:
            ret = s.query(cls) \
                .filter(cls.fiscal_week_year == fiscal_week_year) \
                .filter(cls.rtm.in_(rtm_list)) \
                .all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

    
class TblUploadFileRecord(FASTLiteBase):
    __tablename__ = "tbl_upload_file_record"
    
    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    allocation_prepare_id = Column(Integer)
    upload_file_name = Column(String(256))
    upload_by = Column(String(256), comment='Nick Name + Last Name')
    upload_at = Column(DateTime, default=datetime.now)

    def __init__(self, allocation_prepare_id: int, upload_file_name: str, upload_by: str):
        self.allocation_prepare_id = allocation_prepare_id
        self.upload_file_name = upload_file_name
        self.upload_by = upload_by

    def save(self) -> int:
        s = FASTLiteSession()
        try:
            s.add(self)
            s.commit()
            v_id = self.id
        except Exception as e:
            logger.exception(e)
            v_id = 0
        finally:
            s.close()
        return v_id


class TblAllocationPrepareFile(FASTLiteBase):
    __tablename__ = "tbl_allocation_prepare_file"
    __table_args__ = {"extend_existing": True,
                      "schema": "fast_lite"}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    fiscal_qtr_week_name = Column(String(256), comment='FY23Q1W12')
    fiscal_week_year = Column(Integer, comment='202312')
    rtm = Column(String(256))
    lob = Column(String(256))
    operate_phase = Column(String(256))
    upload_status = Column(String(256))
    upload_file_name = Column(String(256))
    upload_file_path = Column(String(256))
    upload_file_version = Column(Integer)
    upload_by = Column(String(256), comment='Nick Name + Last Name')
    uploader_email = Column(String(256), comment='xxx.apple.com')
    upload_at = Column(DateTime)
    update_by = Column(String(256), comment='Nick Name + Last Name')
    category = Column(Integer)
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    def save(self) -> int:
        s = FASTLiteSession()
        try:
            s.add(self)
            s.commit()
            v_id = self.id
        except Exception as e:
            logger.exception(e)
            v_id = 0
        finally:
            s.close()
        return v_id

    @classmethod
    def find_by_phase_rtm_category(cls, phase: str, rtm: str, category: int, fiscal_week_year: int):
        s = FASTLiteSession()
        ret = []
        try:
            ret = s.query(cls) \
                .filter(cls.fiscal_week_year == fiscal_week_year) \
                .filter(cls.operate_phase == phase) \
                .filter(cls.rtm == rtm) \
                .filter(cls.category == category) \
                .all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

    @classmethod
    def find_by_phase(cls, phase: str, fiscal_week_year: int):
        s = FASTLiteSession()
        ret = []
        try:
            ret = s.query(cls) \
                .filter(cls.fiscal_week_year == fiscal_week_year) \
                .filter(cls.operate_phase == phase) \
                .all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

    @classmethod
    def find_by_week_phase_category(cls, fiscal_week_year: int, phase: str, category: int):
        s = FASTLiteSession()
        ret = []
        try:
            ret = s.query(cls) \
                .filter(cls.fiscal_week_year == fiscal_week_year) \
                .filter(cls.operate_phase == phase) \
                .filter(cls.category == category) \
                .all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret


class TblAllocationMock(FASTLiteBase):
    __tablename__ = "tbl_allocation_mock"
    __table_args__ = {"schema": "fast_lite"}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')

    mock_type = Column(String(256))
    mock_value = Column(String(256))
    active = Column(SmallInteger)
    remarks = Column(String(256))

    @classmethod
    def get_info_by_mock_type(cls, mock_type: str):
        s = FASTLiteSession()
        ret = []
        try:
            ret = s.query(cls)\
                .filter(cls.mock_type == mock_type)\
                .filter(cls.active == 1)\
                .all()
            ret = [{"mock_value": x.mock_value} for x in ret]
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret


class TblAllocationOperateRecord(FASTLiteBase):
    __tablename__ = "tbl_allocation_operate_record"
    __table_args__ = {"schema": "fast_lite"}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    rtm = Column(String(16))
    category = Column(SmallInteger, comment='')
    file_name = Column(String(256))
    file_path= Column(String(256))
    operator = Column(String(64), comment='Nick_Name Last_Name')
    operator_email = Column(String(64), comment='<EMAIL>')
    create_time = Column(DateTime,  server_default=text('CURRENT_TIMESTAMP'))
    update_time = Column(DateTime, server_default=text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'))

    def save(self) -> int:
        s = FASTLiteSession()
        try:
            s.add(self)
            s.commit()
            v_id = self.id
        except Exception as e:
            logger.exception(e)
            v_id = 0
        finally:
            s.close()
        return v_id
