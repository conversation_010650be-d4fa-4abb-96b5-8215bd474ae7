from typing import Optional
from util.conf import *

from sqlalchemy.exc import IntegrityError
from sqlalchemy import or_, UniqueConstraint
from datetime import datetime

from util.const import *

user_db = 'Users'
status_deleted = 2
status_exist = 1
default_expiration = -1
in_whitelist = 1

ttl_time = 86400


class TblUserInfo(Base):
    __tablename__ = 'tbl_user_info'
    __table_args__ = {'schema': user_db}

    id = Column(Integer, primary_key=True, autoincrement=True, nullable=False)
    person_id = Column(String(256), nullable=False,
                       unique=True, comment='person ID')
    employee_id = Column(Integer)
    first_name = Column(String(256))
    last_name = Column(String(256))
    nick = Column(String(256))
    lob = Column(SmallInteger, default=0,
                 comment='0:default, 1:iPhone 2:iPad 3:Mac 4:Watch 100:all')

    rtm = Column(SmallInteger, comment='0: All, 1: D<PERSON>, 2: Mono, 3: Program, 4: Planing')
    rtm_whitelist = Column(SmallInteger, default=0,
                           comment='0: not in whitelist, 1: in whitelist')

    deleted = Column(Integer, default=1, comment='0:exist 1:deleted')
    is_admin = Column(Integer, default=0, comment='0:not admin  1:admin')
    email = Column(String(256))
    phone = Column(String(256))
    create_date = Column(
        DateTime, default=datetime.now, comment='create time')
    update_date = Column(DateTime, default=datetime.now,
                         onupdate=datetime.now, comment='update time')

    def __init__(self, prs_id, employee_id="", first_name="", last_name="", nick="", email="", is_admin=0, lob=0,
                 rtm=100, rtm_whitelist=0):
        self.person_id = str(prs_id)
        self.employee_id = employee_id
        self.first_name = first_name
        self.last_name = last_name
        self.nick = nick
        self.email = email
        self.deleted = status_exist
        self.is_admin = is_admin
        self.lob = lob
        self.rtm = rtm
        self.rtm_whitelist = rtm_whitelist

    def save(self):
        s = Session()
        try:
            s.add(self)
            s.commit()
        except IntegrityError as e:
            self.update()
        finally:
            s.close()

    @classmethod
    def delete(self, person_id, delete_row=False):
        s = Session()
        try:
            q = s.query(TblUserInfo) \
                .filter(TblUserInfo.person_id == person_id)
            if delete_row:
                q = q.delete()
            else:
                q = q.update({TblUserInfo.deleted: status_deleted})
            s.commit()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()

    @classmethod
    def get_by_id(self, prs_id):
        prs_id = str(prs_id)
        s = Session()
        user = None
        try:
            user = s.query(TblUserInfo) \
                .filter(TblUserInfo.person_id == prs_id) \
                .filter(TblUserInfo.deleted != status_deleted) \
                .one_or_none()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return user

    @classmethod
    def get_whitelist_by_rtm(self, rtm):
        s = Session()
        try:
            user_whitelist = s.query(TblUserInfo.id) \
                .filter(TblUserInfo.rtm_whitelist == in_whitelist) \
                .filter(TblUserInfo.rtm == rtm) \
                .count()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()

        return user_whitelist

    @classmethod
    def update_user(self, person_id: int, update_map: dict):
        s = Session()
        try:
            s.query(TblUserInfo) \
                .filter(TblUserInfo.person_id == person_id) \
                .update(update_map)
            s.commit()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()

    def update(self):
        s = Session()
        update_map = {TblUserInfo.deleted: status_exist, TblUserInfo.is_admin: self.is_admin}
        if self.nick is not None and self.nick != '':
            update_map[TblUserInfo.nick] = self.nick
        if self.first_name is not None and self.first_name != '':
            update_map[TblUserInfo.first_name] = self.first_name
        if self.last_name is not None and self.last_name != '':
            update_map[TblUserInfo.last_name] = self.last_name
        if self.email is not None and self.email != '':
            update_map[TblUserInfo.email] = self.email
        try:
            s.query(TblUserInfo) \
                .filter(TblUserInfo.person_id == self.person_id) \
                .update(update_map)
            s.commit()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()

    def update_by_map(self, update_map: dict):
        s = Session()
        try:
            s.query(TblUserInfo) \
                .filter(TblUserInfo.person_id == self.person_id) \
                .update(update_map)
            s.commit()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()

    def middleware_save(self):
        user = TblUserInfo.get_by_id(self.person_id)
        if not isinstance(user, TblUserInfo):
            self.save()

    def middleware_update(self) -> bool:
        user = TblUserInfo.get_by_id(self.person_id)
        if not isinstance(user, TblUserInfo):
            return
        change_flg = False
        update_map = {TblUserInfo.deleted: status_exist}
        if user.last_name != self.last_name:
            change_flg = True
            update_map[TblUserInfo.last_name] = self.last_name
        if user.nick != self.nick:
            change_flg = True
            update_map[TblUserInfo.nick] = self.nick
        if user.first_name != self.first_name:
            change_flg = True
            update_map[TblUserInfo.first_name] = self.first_name
        if user.email != self.email:
            change_flg = True
            update_map[TblUserInfo.email] = self.email
        if user.is_admin != self.is_admin:
            change_flg = True
            update_map[TblUserInfo.is_admin] = self.is_admin
        if change_flg:
            self.update_by_map(update_map)
        return change_flg


    @classmethod
    def get_list(self, platform: str, user_list: list, page: int = 1, page_size: int = 6, cond: str = '',
                 rtm_filter: int = OperationRtm.Mono, rtm: int = int(selectRange[0]), lob: int = int(selectRange[0]),
                 role: str = selectRange[0], sort: str = 'update_date', sort_type: str = 'desc') -> tuple[list, int]:
        s = Session()
        ret = []
        all_num = 0
        try:
            q = s.query(TblUserInfo, ).filter(
                TblUserInfo.deleted != status_deleted)
            if cond != '':
                search = "%{}%".format(cond).lower()
                q = q.filter(or_(func.lower(func.concat(TblUserInfo.nick, ' ', TblUserInfo.last_name)).like(search),
                                 TblUserInfo.person_id.like(search), TblUserInfo.email.like(search),
                                 func.lower(func.concat(TblUserInfo.first_name, ' ', TblUserInfo.last_name)).like(
                                     search)))
            # 筛选角色条件
            if role == selectRange[1]:
                q = q.filter(TblUserInfo.person_id.notin_(user_list))
            elif role != selectRange[0]:
                q = q.filter(TblUserInfo.person_id.in_(user_list))

            if rtm == int(selectRange[1]):
                q = q.filter(TblUserInfo.rtm.is_(None))
            elif rtm != int(selectRange[0]):
                q = q.filter(TblUserInfo.rtm == rtm)
            else:
                if platform == 'Operation':
                    q = q.filter(TblUserInfo.rtm.in_(OperationRtmMap.get(rtm_filter)))

            if lob == int(selectRange[1]) or lob == 0:
                q = q.filter(TblUserInfo.lob == 0)
            elif lob != int(selectRange[0]):
                q = q.filter(TblUserInfo.lob == lob)
            all_num = q.count()
            if sort == 'user_name':
                if sort_type == 'desc':
                    q = q.order_by(TblUserInfo.nick.desc()).order_by(TblUserInfo.last_name.desc())
                else:
                    q = q.order_by(TblUserInfo.nick.asc()).order_by(TblUserInfo.last_name.asc())
            else:
                if sort_type == 'desc':
                    q = q.order_by(TblUserInfo.update_date.desc())
                else:
                    q = q.order_by(TblUserInfo.update_date.asc())
            ret = q.limit(page_size) \
                .offset((page - 1) * page_size) \
                .all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret, all_num

    @classmethod
    def get_select_list(self, cond: str = '', page_size: int = 10) -> tuple[list, int]:
        s = Session()
        ret = []
        all_num = 0
        try:
            q = s.query(TblUserInfo).filter(
                TblUserInfo.deleted != status_deleted)
            if cond != '':
                search = "%{}%".format(cond).lower()
                q = q.filter(or_(func.lower(func.concat(TblUserInfo.nick, ' ', TblUserInfo.last_name)).like(search),
                                 TblUserInfo.person_id.like(search), TblUserInfo.email.like(search),
                                 func.lower(func.concat(TblUserInfo.first_name, ' ', TblUserInfo.last_name)).like(
                                     search)))
            all_num = q.count()
            ret = q.order_by(TblUserInfo.nick.asc()).order_by(TblUserInfo.last_name.asc()) \
                .limit(page_size).all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret, all_num


class TblPlatform(Base):
    __tablename__ = 'tbl_platform'
    __table_args__ = {'schema': user_db}

    id = Column(Integer, primary_key=True, autoincrement=True, nullable=False)
    platform = Column(String(64), nullable=False, unique=True)
    role_type = Column(Integer, default=1)
    desc = Column(String(256), nullable=True)
    deleted = Column(Integer, default=1, comment='0:exist 1:deleted')
    create_person = Column(String(128), default='')
    create_date = Column(DateTime, default=datetime.now, comment='create time')
    update_date = Column(DateTime, default=datetime.now,
                         onupdate=datetime.now, comment='update time')

    def save(self):
        s = Session()
        try:
            s.add(self)
            s.commit()
            cache.delete_memoized_verhash(TblPlatform.get_all_list)
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()

    @classmethod
    @cache.memoize(timeout=ttl_time)
    def get_all_list(self) -> list:
        s = Session()
        ret = []
        try:
            ret = s.query(TblPlatform).filter(
                TblPlatform.deleted != status_deleted).all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

    @classmethod
    @cache.memoize(timeout=ttl_time * 7)
    def get_platform(self, name: str):
        s = Session()
        ret = None
        try:
            ret = s.query(TblPlatform) \
                .filter(TblPlatform.deleted != status_deleted) \
                .filter(TblPlatform.platform == name) \
                .one_or_none()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret


class TblRoleInfo(Base):
    __tablename__ = 'tbl_role_info'
    __table_args__ = {'schema': user_db}

    id = Column(Integer, primary_key=True, autoincrement=True, nullable=False)
    role_name = Column(String(256), nullable=False, default='')
    platform = Column(String(64), nullable=False)
    # role_type = Column(Integer, default=0, nullable=False)
    role_level = Column(Integer, default=0)
    desc = Column(String(256), nullable=True)
    deleted = Column(Integer, default=1, comment='0:exist 1:deleted')
    create_person = Column(String(128), default='')
    update_person = Column(String(128), default='')
    create_date = Column(
        DateTime, default=datetime.now, comment='create time')
    update_date = Column(DateTime, default=datetime.now,
                         onupdate=datetime.now, comment='update time')
    UniqueConstraint(platform, role_name, name='role_name')

    def __init__(self, platform, role_name, role_level=0, desc=""):
        self.platform = platform
        self.role_name = role_name
        self.role_level = role_level
        self.desc = desc

    def save(self):
        s = Session()
        try:
            s.add(self)
            s.commit()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()

    @classmethod
    def get_part_list_by_platform(self, platform: str = '', rtm_type: str = RtmType.Default):
        s = Session()
        ret = []
        try:
            q = s.query(TblRoleInfo).filter(
                TblRoleInfo.deleted != status_deleted)
            if platform != '':
                q = q.filter(TblRoleInfo.platform == platform)
                if platform == 'Operation':
                    q = q.filter(TblRoleInfo.role_name.in_(RtmTypeRoleMap.get(rtm_type)))

            ret = q.all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

    @classmethod
    def get_list_by_platforms(self, platforms: list = []):
        s = Session()
        ret = []
        try:
            q = s.query(TblRoleInfo).filter(
                TblRoleInfo.deleted != status_deleted).filter(TblRoleInfo.platform.in_(platforms))
            ret = q.all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret


class TblResourceInfo(Base):
    __tablename__ = 'tbl_resource_info'
    __table_args__ = {'schema': user_db}
    id = Column(Integer, primary_key=True, autoincrement=True, nullable=False)
    resource_name = Column(String(256), nullable=False, default='')
    platform = Column(String(64), nullable=False)
    url = Column(String(256), default='')
    desc = Column(String(256), nullable=True)
    deleted = Column(Integer, default=1, comment='1:exist 2:deleted')
    create_person = Column(String(128), default='')
    update_person = Column(String(128), default='')
    create_date = Column(
        DateTime, default=datetime.now, comment='create time')
    update_date = Column(DateTime, default=datetime.now,
                         onupdate=datetime.now, comment='update time')
    UniqueConstraint(platform, resource_name, name='role_name')

    def __init__(self, platform, res_name, url='', desc='') -> None:
        self.platform = platform
        self.resource_name = res_name
        self.url = url
        self.desc = desc

    def save(self):
        s = Session()
        try:
            s.add(self)
            s.commit()
            cache.delete_memoized_verhash(TblResourceInfo.get_list)
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()

    @classmethod
    @cache.memoize(ttl_time * 7)
    def get_list(self, platform: str, page: int = 1, page_size: int = 6, cond: str = '',
                 res_names: list = []) -> tuple[list, int]:
        s = Session()
        ret = []
        all_num = 0
        try:
            q = s.query(TblResourceInfo).filter(TblResourceInfo.deleted != status_deleted) \
                .filter(TblResourceInfo.platform == platform)
            if len(res_names) > 0:
                ret = q.filter(
                    TblResourceInfo.resource_name.in_(res_names)).all()
                return ret, len(ret)
            if cond != '':
                search = "%{}%".format(cond)
                q = q.filter(TblResourceInfo.resource_name.like(search))
            all_num = q.count()
            ret = q.order_by(TblResourceInfo.id.desc()) \
                .limit(page_size) \
                .offset((page - 1) * page_size) \
                .all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret, all_num

    @classmethod
    def get_all_list(self) -> list:
        s = Session()
        ret = []
        try:
            ret = s.query(TblResourceInfo).filter(
                TblResourceInfo.deleted != status_deleted).all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret


class TblUserRole(Base):
    __tablename__ = 'tbl_user_role'
    __table_args__ = {'schema': user_db}

    id = Column(Integer, primary_key=True, autoincrement=True, nullable=False)
    person_id = Column(String(256))
    role_name = Column(String(256))
    platform = Column(String(64))
    expiration = Column(Integer, default=-1)
    deleted = Column(Integer, default=1, comment='0:exist 1:deleted')
    create_person = Column(String(128), default='')
    update_person = Column(String(128), default='')
    create_date = Column(
        DateTime, default=datetime.now, comment='create time')
    update_date = Column(DateTime, default=datetime.now,
                         onupdate=datetime.now, comment='update time')
    UniqueConstraint(platform, person_id, role_name, name='role_name')

    def __init__(self, platform, person_id, role_name, expiration=default_expiration) -> None:
        self.platform = platform
        self.role_name = role_name
        self.person_id = person_id
        self.expiration = expiration
        self.deleted = status_exist

    def save_or_update(self) -> int:
        s = Session()
        ret = 0
        try:
            s.add(self)
            s.commit()
            ret = self.id
        except Exception as e:
            if type(e) is IntegrityError:
                ret = self.change_user_role(
                    self.platform, self.person_id, self.role_name, status_exist, self.expiration)
            logger.exception(e)
        finally:
            s.close()
        return ret

    @classmethod
    def change_user_role(self, platform, person_id, role_name, change_type: int,
                         effective: int = default_expiration) -> int:
        s = Session()
        ret = 0
        try:
            ret = s.query(TblUserRole) \
                .filter(TblUserRole.person_id == person_id) \
                .filter(TblUserRole.platform == platform) \
                .filter(TblUserRole.role_name == role_name) \
                .update({
                TblUserRole.deleted: change_type,
                TblUserRole.expiration: effective
            })
            s.commit()
            # cache.delete_memoized_verhash(TblUserRole.get_list)
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

    @classmethod
    def abtest_single_role(self, platform, person_id, role_name) -> int:
        s = Session()
        ret = 0
        try:
            ret = s.query(TblUserRole) \
                .filter(TblUserRole.person_id == person_id) \
                .filter(TblUserRole.platform == platform) \
                .filter(TblUserRole.role_name != role_name) \
                .update({TblUserRole.deleted: status_deleted})
            s.commit()
            # cache.delete_memoized_verhash(TblUserRole.get_list)
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

    @classmethod
    def get_list(self, platform: str, users: list, role: str = selectRange[0], rtm_type: str = RtmType.Default) -> list:
        s = Session()
        ret = []
        try:
            q = s.query(TblUserRole).filter(TblUserRole.deleted != status_deleted) \
                .filter(TblUserRole.platform == platform)  # \
            # .filter(or_((TblUserRole.update_date+datetime.timedelta(days=TblUserRole.expiration)) <= datetime.now(),
            #           TblUserRole.expiration == default_expiration))
            if len(users) > 0:
                q = q.filter(TblUserRole.person_id.in_(users))

            # operation 逻辑
            if platform == 'Operation':
                if role not in selectRange:
                    q = q.filter(TblUserRole.role_name == role)
                elif role == selectRange[1]:
                    q = q.filter(TblUserRole.role_name.in_(RtmTypeRoleMap.get(rtm_type)))
            else:
                if role not in selectRange:
                    q = q.filter(TblUserRole.role_name == role)

            ret = q.all()

        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

    @classmethod
    def get_part_list(self, platform: str, users: list, rtm_type: str = RtmType.Default) -> list:
        s = Session()
        ret = []
        try:
            q = s.query(TblUserRole).filter(TblUserRole.deleted != status_deleted) \
                .filter(TblUserRole.platform == platform)  # \
            if len(users) > 0:
                q = q.filter(TblUserRole.person_id.in_(users))

            # operation 逻辑
            if platform == 'Operation':
                q = q.filter(TblUserRole.role_name.in_(RtmTypeRoleMap.get(rtm_type)))

            ret = q.all()

        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

    @classmethod
    def get_all_role(self, person_id: str) -> list:
        s = Session()
        ret = []
        try:
            q = s.query(TblUserRole).filter(TblUserRole.deleted != status_deleted) \
                .filter(TblUserRole.person_id == person_id)
            ret = q.all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

    @classmethod
    def get_rolename_by_user_platform(self, platform: str, user: str) -> list:
        s = Session()
        ret = []
        try:
            q = s.query(TblUserRole.role_name).filter(TblUserRole.deleted != status_deleted) \
                .filter(TblUserRole.platform == platform)
            if user is not None:
                q = q.filter(TblUserRole.person_id == user)
            ret = q.all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret
    
    @classmethod
    def get_role_platform_by_user(cls, platform_prefix: Optional[str] = None, user_id: Optional[str] = None, platform: Optional[str] = None) -> list:
        s = Session()
        ret = []
        try:
            q = (s.query(cls.person_id, cls.role_name, cls.platform, TblUserInfo.email)
                 .outerjoin(TblUserInfo, cls.person_id == TblUserInfo.person_id)
                 .filter(cls.deleted != status_deleted))
            if platform_prefix is not None and len(platform_prefix):
                q = q.filter(cls.platform.like(f"{platform_prefix}%"))
            if user_id is not None:
                q = q.filter(cls.person_id == user_id)

            if platform is not None:
                q = q.filter(cls.platform == platform)
            ret = q.all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret


class TblRoleResource(Base):
    __tablename__ = 'tbl_role_resource'
    __table_args__ = {'schema': user_db}

    id = Column(Integer, primary_key=True, autoincrement=True, nullable=False)
    resource_name = Column(String(256))
    role_name = Column(String(256))
    platform = Column(String(64))
    create_person = Column(String(128), default='')
    update_person = Column(String(128), default='')
    deleted = Column(Integer, default=1, comment='0:exist 1:deleted')
    create_date = Column(
        DateTime, default=datetime.now, comment='create time')
    update_date = Column(DateTime, default=datetime.now,
                         onupdate=datetime.now, comment='update time')
    UniqueConstraint(platform, role_name, resource_name, name='role_resource')

    def __init__(self, platform, role_name, resource_name) -> None:
        self.platform = platform
        self.role_name = role_name
        self.resource_name = resource_name
        self.deleted = status_exist

    def save_or_update(self) -> int:
        s = Session()
        ret = 0
        try:
            s.add(self)
            s.commit()
            ret = self.id
        except Exception as e:
            if type(e) is IntegrityError:
                ret = self.change_role_resource(
                    self.platform, self.role_name, self.resource_name, status_exist)
            else:
                logger.exception(e)
        finally:
            s.close()
        cache.delete_memoized_verhash(TblRoleResource.get_list)
        return ret

    @classmethod
    def change_role_resource(self, platform, role_name, resource_name, change_type) -> int:
        s = Session()
        ret = 0
        try:
            ret = s.query(TblRoleResource) \
                .filter(TblRoleResource.resource_name == resource_name) \
                .filter(TblRoleResource.platform == platform) \
                .filter(TblRoleResource.role_name == role_name) \
                .update({TblRoleResource.deleted: change_type})
            s.commit()
            cache.delete_memoized(TblRoleResource.get_list)
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

    @classmethod
    def get_list(self, platform: str, resources: list = [], roles: list = []) -> list:
        s = Session()
        ret = []
        try:
            q = s.query(TblRoleResource).filter(TblRoleResource.deleted != status_deleted) \
                .filter(TblRoleResource.platform == platform)
            if len(resources) > 0:
                q = q.filter(TblRoleResource.resource_name.in_(resources))
            if len(roles) > 0:
                q = q.filter(TblRoleResource.role_name.in_(roles))
            ret = q.all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret


class TblUserOperationRecord(Base):
    __tablename__ = 'tbl_user_operation_record'
    __table_args__ = {'schema': user_db}

    id = Column(Integer, autoincrement=True, primary_key=True)
    person_id = Column(String(256), default='')
    op_type = Column(Integer, nullable=False, comment='userAdd:0 userUpdate:1 userChangeRole:2')
    uri = Column(String(256), default='')
    remark = Column(Text, comment='remark')
    fiscal_dt = Column(String(64), default='')
    create_date = Column(DateTime, default=datetime.now, comment='create time')
    create_person = Column(String(256), default='', comment='create person')

    def __init__(self, op_type, person_id, uri='', remark='', fiscal_dt='', create_person='') -> None:
        self.op_type = op_type
        self.person_id = person_id
        self.uri = uri
        self.remark = remark
        self.fiscal_dt = fiscal_dt
        self.create_person = create_person

    def insert(self):
        s = Session()
        ret = 0
        try:
            s.add(self)
            s.commit()
            ret = self.id
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret


# Base.metadata.create_all(engine)
