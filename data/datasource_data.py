from flask import g

from util.const import ErrorExcept, ErrCode, DataSourceFileStatus
from util.fast_lite_base import *


class DataSourceList(FASTLiteBase):
    __tablename__ = 'datasource_list'
    __table_args__ = {"extend_existing": True}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    rtm = Column(String(256), comment='')
    datasource_type = Column(String(256), comment='')
    simple = Column(String(256), comment='')
    create_by = Column(String(256), comment='')
    create_date = Column(DateTime, default=datetime.now, comment='create time')
    update_date = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='update time')

    def save(self):
        s = FASTLiteSession()
        try:
            s.add(self)
            s.commit()
        except Exception as e:
            raise ErrorExcept(ErrCode.DBInsert, "insert to database failed" + str(e))
        finally:
            s.close()
        return self.id

    @classmethod
    def query_list(cls, rtm):
        s = FASTLiteSession()
        try:
            return s.query(cls).filter(cls.rtm == rtm).order_by(cls.update_date.asc()).all()
        except Exception as e:
            raise ErrorExcept(ErrCode.DBQueryError, "db query failed")
        finally:
            s.close()

    @classmethod
    def query_manual_list(cls, rtm):
        s = FASTLiteSession()
        try:
            data = []
            res = s.execute("""
            select dl.rtm                           rtm,
                   dl.simple                        simple,
                   dl.datasource_type               datasource_type,
                   df.last_upload_date              last_upload_date,
                   datasource_file.upload_by        last_upload_by
            from datasource_list dl
                 left join (select MAX(create_date) last_upload_date, upload_by, datasource_type
                    from datasource_file where `status` = 1
                    group by datasource_type) as df on dl.datasource_type = df.datasource_type
                    left join datasource_file 
                        on datasource_file.`create_date` = df.last_upload_date 
                        and datasource_file.datasource_type = df.datasource_type
                    order by dl.`id`;""").fetchall()
            for item in res:
                if item.rtm == rtm:
                    data.append(item)
            return data
        except Exception as e:
            raise ErrorExcept(ErrCode.DBInsert, "insert to database failed" + str(e))
        finally:
            s.close()


class DataSourceFile(FASTLiteBase):
    __tablename__ = 'datasource_file'
    __table_args__ = {"extend_existing": True}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    datasource_type = Column(String(256), comment='')
    rtm = Column(String(256), comment='')
    filename = Column(String(256), comment='')
    upload_by = Column(String(256), comment='')
    upload_date = Column(String(256), comment='')
    version = Column(Integer, default=0, comment='')
    filesize = Column(Integer, comment='')
    url = Column(String(256), comment='')
    content_type = Column(String(256), comment='')
    status = Column(Integer, comment='')
    create_date = Column(DateTime, default=datetime.now, comment='create time')
    update_date = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='update time')
    ext = Column(Text, comment='')

    def save(self):
        s = FASTLiteSession()
        try:
            s.add(self)
            s.commit()
        except Exception as e:
            raise ErrorExcept(ErrCode.DBInsert, "insert to db failed")
        finally:
            s.close()
        return True

    @classmethod
    def query_count_by_rtm_and_type(cls, rtm: str, datasource_type: str):
        s = FASTLiteSession()
        count = 0
        try:
            count = s.query(cls)\
                .filter(cls.rtm == rtm)\
                .filter(cls.datasource_type == datasource_type)\
                .filter(cls.status == DataSourceFileStatus.Enabled)\
                .count()
        except Exception as e:
            raise ErrorExcept(ErrCode.DBQueryError, "query from db failed")
        finally:
            s.close()
        return count

    @classmethod
    def query_count_by_rtm_type_date(cls, rtm: str, datasource_type: str, upload_date: str):
        s = FASTLiteSession()
        count = 0
        try:
            count = s.query(cls)\
                .filter(cls.rtm == rtm)\
                .filter(cls.datasource_type == datasource_type)\
                .filter(cls.upload_date == upload_date)\
                .filter(cls.status == DataSourceFileStatus.Enabled)\
                .count()
        except Exception as e:
            raise ErrorExcept(ErrCode.DBQueryError, "query from db failed")
        finally:
            s.close()
        return count

    @classmethod
    def query_by_file_id(cls, rtm, file_type, file_id: str):
        s = FASTLiteSession()
        try:
            file = s.query(cls)\
                .filter(cls.rtm == rtm)\
                .filter(cls.datasource_type == file_type)\
                .filter(cls.url == f'/file/download/{file_id}')\
                .order_by(cls.create_date.desc())\
                .all()
            if len(file) == 0:
                return None
            return file[0]
        except Exception as e:
            raise ErrorExcept(ErrCode.DBQueryError, "query from db failed")
        finally:
            s.close()

    @classmethod
    def query_list_by_rtm(cls, rtm, datasource_type):
        s = FASTLiteSession()
        try:
            return s.query(cls) \
                .filter(cls.rtm == rtm) \
                .filter(cls.datasource_type == datasource_type)\
                .filter(cls.status == DataSourceFileStatus.Enabled) \
                .order_by(cls.create_date.desc()) \
                .all()
        except Exception as e:
            raise ErrorExcept(ErrCode.DBQueryError, "query from db failed")
        finally:
            s.close()

    @classmethod
    def query_by_rtm_version(cls, rtm, datasource_type, version):
        s = FASTLiteSession()
        try:
            return s.query(cls) \
                .filter(cls.rtm == rtm) \
                .filter(cls.datasource_type == datasource_type)\
                .filter(cls.status == DataSourceFileStatus.Enabled) \
                .filter(cls.version == version) \
                .one_or_none()
        except Exception as e:
            raise ErrorExcept(ErrCode.DBQueryError, "query from db failed")
        finally:
            s.close()

    @classmethod
    def update_file_path_by_rtm_version(cls, rtm, datasource_type, version, file_path):
        s = FASTLiteSession()
        try:
            s.query(cls) \
                .filter(cls.rtm == rtm) \
                .filter(cls.datasource_type == datasource_type)\
                .filter(cls.status == DataSourceFileStatus.Enabled) \
                .filter(cls.version == version) \
                .update({"url": file_path})
            s.commit()
        except Exception as e:
            logger.exception(e)
            raise f"update {cls.__tablename__} error: {e}"
        finally:
            s.close()

    @classmethod
    def query_by_rtm_type_upload_date(cls, rtm, datasource_type, upload_date):
        s = FASTLiteSession()
        try:
            return s.query(cls) \
                .filter(cls.rtm == rtm) \
                .filter(cls.datasource_type == datasource_type)\
                .filter(cls.status == DataSourceFileStatus.Enabled) \
                .filter(cls.upload_date == upload_date) \
                .order_by(cls.id.desc()) \
                .all()
        except Exception as e:
            raise ErrorExcept(ErrCode.DBQueryError, "query from db failed")
        finally:
            s.close()

    @classmethod
    def update_status_by_rtm_type_upload_date(cls, rtm, datasource_type, upload_date):
        s = FASTLiteSession()
        try:
            s.query(cls) \
                .filter(cls.rtm == rtm) \
                .filter(cls.datasource_type == datasource_type)\
                .filter(cls.upload_date == upload_date) \
                .update({"status": DataSourceFileStatus.Draft})
            s.commit()
        except Exception as e:
            logger.exception(e)
            raise f"update {cls.__tablename__} error: {e}"
        finally:
            s.close()