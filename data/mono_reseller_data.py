from flask import g

from util.fast_lite_base import *
from util.const import SoldToIdKey


class FastMonoReseller(FASTMonoBase):
    __tablename__ = "excel_reseller_mapping"
    __table_args__ = {"extend_existing": True}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    hq_id = Column(String(256), comment='')
    sold_to_id = Column(String(256), comment='')
    rtm = Column(String(256), comment='')
    reseller_type = Column(String(256), comment='')
    reseller_name = Column(String(256), comment='')
    reseller_simp_name = Column(String(256), comment='')
    reseller_chinese_name = Column(String(256), comment='')
    region = Column(String(256), comment='')
    am = Column(String(256), comment='')
    supplier_name = Column(String(256), comment='')
    supplier = Column(String(256), comment='')
    supplier_hq_id = Column(String(256), comment='')
    customer_sold_to_id = Column(String(256), comment='')
    npp_reseller_mapping = Column(String(256), comment='')
    version_id = Column(String(256), comment='')

    @classmethod
    def find_by_role(cls):
        s = FASTMonoSession()
        reseller = []
        try:
            reseller = s.query(cls).filter(cls.sold_to_id == g.get(SoldToIdKey)).all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        if len(reseller) == 0:
            return None
        return reseller[0]
