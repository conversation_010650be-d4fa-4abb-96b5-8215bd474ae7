from sqlalchemy import or_, and_
from sqlalchemy import desc, asc
from data.databend.supply_data_databend_base import SupplyDatabendSession
from domain.dashboard.entity.fiscal_week import FiscalWeek
from domain.dashboard.entity.mpn_mix import MpnMix
from kit.custom_sort import CustomSort
from util.conf import Base
from util.conf import Column, String, Float, Integer, DateTime
from util.conf import logger, func
from util.const import ALL, ErrorExcept, ErrCode
from util.util import nand_color_sorted, env_dev


class DashboardMpnMix(Base):
    # __tablename__ = "app_fast_dashboard_country_mpn_mix_wi"
    __tablename__ = f"app_fast_dashboard_country_mpn_mix_wi{'_test' if env_dev() else ''}"
    __table_args__ = {"schema": "gc_dmp_fast"}

    fiscal_week = Column(String(256), primary_key=True)
    fiscal_week_year = Column(Integer, primary_key=True)
    region = Column(String(256))
    lob = Column(String(256))
    sub_lob = Column(String(256))
    nand = Column(String(256))
    color = Column(String(256))
    mpn_id = Column(String(256))
    ml_fcst_mix = Column(Float)
    sales_fcst_mix = Column(Float)
    fd_fcst_mix = Column(Float)
    trimmed_ub_mix = Column(Float)
    trimmed_weeks = Column(String(256))
    trimmed_week_cnt = Column(Integer)
    threshold = Column(Float)
    create_time = Column(DateTime)
    update_time = Column(DateTime)

    @classmethod
    def get_mpn_mix_by_fiscal_week_and_lob(cls, fiscal_week: str, lob: str) -> list:
        s = SupplyDatabendSession()
        res = []
        try:
            res = s.query(cls.region, cls.sub_lob, cls.mpn_id, cls.trimmed_ub_mix) \
                .filter(cls.fiscal_week == fiscal_week) \
                .filter(cls.lob == lob) \
                .query_list()
            return res
        except Exception as e:
            logger.error(e)
            raise ErrorExcept(ErrCode.DBQueryError, "db query error: " + str(e))
        finally:
            s.close()

    @classmethod
    def get_mpn_mix_menu(cls, fiscal_week: str) -> dict:
        s = SupplyDatabendSession()
        try:
            ret = {}
            mpn_mix = s.query(cls.region, cls.lob, cls.sub_lob).distinct().filter(
                cls.fiscal_week == fiscal_week).order_by(
                desc(cls.lob)).order_by(desc(cls.sub_lob)).query_list()

            regions = []
            lobs = []
            lob_tem = {}
            for item in mpn_mix:
                region = item.get("region")
                if region not in regions:
                    regions.append(region)

                lob = item.get("lob")
                sub_lob = item.get("sub_lob")
                if lob not in lob_tem:
                    lob_tem[lob] = [sub_lob]
                else:
                    if sub_lob not in lob_tem[lob]:
                        lob_tem[lob].append(sub_lob)

            for lob, sub_lobs in lob_tem.items():
                sub_lobs = CustomSort.sort_iphone_sublobs(sub_lobs)
                if "iPhone SE (3rd Gen)" in sub_lobs:
                    sub_lobs.remove("iPhone SE (3rd Gen)")
                    sub_lobs.append("iPhone SE (3rd Gen)")
                lobs.append({"lob": lob, "sub_lobs": sub_lobs})

            ret["regions"] = regions
            ret["lobs"] = lobs

        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return ret

    @classmethod
    def get_mpn_mix(
            cls,
            fiscal_week: str,
            region: str,
            lob: str,
            sub_lob: str,
            color: bool,
            nand: bool,
    ) -> list[MpnMix]:
        s = SupplyDatabendSession()
        ret = []
        try:
            obj = s.query(
                cls.fiscal_week,
                cls.sub_lob,
                cls.mpn_id,
                cls.nand,
                cls.color,
                cls.ml_fcst_mix,
                cls.sales_fcst_mix,
                cls.fd_fcst_mix,
                cls.trimmed_ub_mix,
                cls.trimmed_week_cnt,
            )

            obj = obj.filter(cls.fiscal_week == fiscal_week)
            obj = obj.filter(cls.region == region)
            obj = obj.filter(cls.lob == lob)
            if sub_lob:
                obj = obj.filter(cls.sub_lob == sub_lob)

            if color:
                obj = obj.filter(
                    or_(cls.color != "All", and_(cls.color == "All", cls.nand == "All"))
                )
            if nand:
                obj = obj.filter(
                    or_(cls.nand != "All", and_(cls.nand == "All", cls.color == "All"))
                )
            if not color and not nand:
                obj = obj.filter(cls.nand != "All" and cls.color != "All")

            mpn_mixs = obj.query_list()
            for item in mpn_mixs:
                fiscal_week = item.get("fiscal_week")
                sub_lob = item.get("sub_lob")
                mpn_id = item.get("mpn_id")
                nand = item.get("nand")
                color = item.get("color")
                ml_fcst_mix = item.get("ml_fcst_mix")
                sales_fcst_mix = item.get("sales_fcst_mix")
                fd_fcst_mix = item.get("fd_fcst_mix")
                trimmed_ub_mix = item.get("trimmed_ub_mix")
                trimmed_week_cnt = item.get("trimmed_week_cnt")
                mpnmix_obj = MpnMix(
                    fiscal_week=fiscal_week,
                    sub_lob=sub_lob,
                    mpn_id=mpn_id,
                    nand=nand,
                    color=color,
                    ml_fcst_mix=ml_fcst_mix,
                    sales_fcst_mix=sales_fcst_mix,
                    fd_fcst_mix=fd_fcst_mix,
                    trimmed_ub_mix=trimmed_ub_mix,
                    trimmed_week_cnt=trimmed_week_cnt,
                )
                ret.append(mpnmix_obj)
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return ret

    @classmethod
    def get_fiscal_weeks(cls, start_week_int: int = 202534) -> list:
        '''从FY25Q3W8开始展示'''
        s = SupplyDatabendSession()
        ret = []
        try:
            fiscal_weeks = (s.query(cls.fiscal_week).distinct()
                            .filter(cls.fiscal_week_year >= start_week_int).query_list())
            ret = [item.get("fiscal_week") for item in fiscal_weeks]
            ret = sorted(ret, key=lambda x: FiscalWeek(x).fiscal_week_int, reverse=True)
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return ret

    @classmethod
    def get_nand_color_by_fiscal_week(
            cls, fiscal_week: str, region: str, sub_lobs: list
    ) -> list:
        s = SupplyDatabendSession()
        ret = []
        try:
            sub_lobs = (
                s.query(cls.region, cls.lob, cls.sub_lob, cls.nand, cls.color, cls.mpn_id)
                .distinct()
                .filter(cls.fiscal_week == fiscal_week)
                .filter(cls.sub_lob.in_(sub_lobs))
                .filter(cls.region == region)
                .filter(cls.nand != ALL)
                .filter(cls.color != ALL)
                .order_by(desc(cls.sub_lob), asc(cls.nand), asc(cls.color))
                .query_list()
            )
            sort_key = ["sub_lob", "nand", "color"]
            sub_lobs = nand_color_sorted(sub_lobs, sort_key)
            for item in sub_lobs:
                region = item.get("region")
                lob = item.get("lob")
                sub_lob = item.get("sub_lob")
                nand = item.get("nand")
                color = item.get("color")
                mpn = item.get("mpn_id")
                ret.append(
                    {
                        "Region": region,
                        "LOB": lob,
                        "Sub-LOB": sub_lob,
                        "Nand": nand,
                        "Color": color,
                        "Mpn": mpn,
                    }
                )
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return ret

    @classmethod
    def mpn_mix_for_download(cls, fiscal_week: str, region: str, sub_lob: list) -> list:
        s = SupplyDatabendSession()
        ret = []
        try:
            datas = s.query(
                cls.fiscal_week, cls.region, cls.lob,
                cls.sub_lob, cls.nand, cls.color,
                cls.ml_fcst_mix, cls.sales_fcst_mix, cls.fd_fcst_mix, cls.trimmed_ub_mix, cls.trimmed_week_cnt, cls.mpn_id
            ).filter(
                cls.fiscal_week == fiscal_week,
                cls.region == region,
                cls.sub_lob.in_(sub_lob),
                cls.nand != "All",
                cls.color != "All"
            ).query_list()

            sort_key = ["sub_lob", "nand", "color"]
            datas = nand_color_sorted(datas, sort_key)

            for item in datas:
                ret.append(
                    {
                        "Fiscal Week": item.get("fiscal_week"),
                        "Region": item.get("region"),
                        "LOB": item.get("lob"),
                        "Sub-LOB": item.get("sub_lob"),
                        "Nand": item.get("nand"),
                        "Color": item.get("color"),
                        "Mpn": item.get("mpn_id"),
                        "ML Fcst Mix%": item.get("ml_fcst_mix") if item.get("ml_fcst_mix") else 0.0,
                        # "Sales Fcst Mix%": item.get("sales_fcst_mix") if item.get("sales_fcst_mix") else 0.0,
                        "Reseller Feedback Mix%": item.get("fd_fcst_mix") if item.get("fd_fcst_mix") else 0.0,
                        "Trimmed UB Mix%": item.get("trimmed_ub_mix") if item.get("trimmed_ub_mix") else 0.0,
                        "Trimmed Weeks": item.get("trimmed_week_cnt"),
                    }
                )
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return ret

    @classmethod
    def list_sublob_nands(cls, fiscal_week: str, region: str, lob: str) -> list:
        s = SupplyDatabendSession()
        res = []
        try:
            sub_lobs = s.query(cls.sub_lob, cls.nand).distinct() \
                .filter(cls.fiscal_week == fiscal_week) \
                .filter(cls.region == region) \
                .filter(cls.lob == lob) \
                .filter(cls.nand != "All", cls.color != "All") \
                .query_list()
            for sub_lob in sub_lobs:
                res.append((sub_lob.get("sub_lob"), sub_lob.get("nand")))
            return res
        except Exception as e:
            logger.error(e)
            raise ErrorExcept(ErrCode.DBQueryError, "db query error: " + str(e))
        finally:
            s.close()

    @classmethod
    def list_sublob_colors(cls, fiscal_week: str, region: str, lob: str) -> list[str]:
        s = SupplyDatabendSession()
        res = []
        try:
            sub_lobs = s.query(cls.sub_lob, cls.color).distinct() \
                .filter(cls.fiscal_week == fiscal_week) \
                .filter(cls.region == region) \
                .filter(cls.lob == lob) \
                .filter(cls.nand != "All", cls.color != "All") \
                .query_list()
            for sub_lob in sub_lobs:
                res.append((sub_lob.get("sub_lob"), sub_lob.get("color")))
            return res
        except Exception as e:
            logger.error(e)
            raise ErrorExcept(ErrCode.DBQueryError, "db query error: " + str(e))
        finally:
            s.close()
