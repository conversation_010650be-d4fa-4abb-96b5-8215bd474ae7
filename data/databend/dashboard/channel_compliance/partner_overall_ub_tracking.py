from typing import Optional

from sqlalchemy import literal, desc

from data.databend.gc_dmp_directship_databend_base import DirectshipDatabendSession
from domain.dashboard.entity.channel_compliance.partner_entity import PartnerOverallUbItem
from domain.dashboard.impl.channel_compliance import db_rtm_to_display_rtm_dict, db_sub_rtm_to_display_sub_rtm_dict
from util.const import REGION_CM
from util.gc_dmp_base import *
from util.util import env_dev


class PartnerOverallUbTracking(Base):
    __tablename__ = "app_ub_tracking_nd_di"
    __table_args__ = {"schema": "gc_dmp_directship"}

    snapshot_date = Column(String(256), primary_key=True)
    rtm = Column(String(32))
    sub_rtm = Column(String(32))
    nd_type = Column(String(16))
    hq_id = Column(String(256))
    hq_name = Column(String(256))
    product_group = Column(String(256))
    lob = Column(String(64))
    sub_lob = Column(String(256))
    fiscal_qtr_year_name_cq = Column(String(256))
    total_so_acc_cq = Column(Integer)
    ub3_acc_cq = Column(Integer)
    ub7_acc_cq = Column(Integer)
    fiscal_qtr_year_name_lq = Column(String(256))
    total_so_acc_lq = Column(Integer)
    ub3_acc_lq = Column(Integer)
    ub7_acc_lq = Column(Integer)
    fiscal_qtr_week_name_cw = Column(String(256))
    total_so_cw = Column(Integer)
    ub7_cw = Column(Integer)
    fiscal_qtr_week_name_cw_1 = Column(String(256))
    total_so_cw_1 = Column(Integer)
    ub7_cw_1 = Column(Integer)
    fiscal_qtr_week_name_cw_2 = Column(String(256))
    total_so_cw_2 = Column(Integer)
    ub7_cw_2 = Column(Integer)
    fiscal_qtr_week_name_cw_3 = Column(String(256))
    total_so_cw_3 = Column(Integer)
    ub7_cw_3 = Column(Integer)
    fiscal_qtr_week_name_cw_4 = Column(String(256))
    total_so_cw_4 = Column(Integer)
    ub7_cw_4 = Column(Integer)
    last_fiscal_qtr_week_name_cw = Column(String(256))
    last_total_so_cw = Column(Integer)
    last_ub7_cw = Column(Integer)
    last_fiscal_qtr_week_name_cw_1 = Column(String(256))
    last_total_so_cw_1 = Column(Integer)
    last_ub7_cw_1 = Column(Integer)
    create_time = Column(TIMESTAMP)
    update_time = Column(TIMESTAMP)

    @classmethod
    def query_tgt_wi_by_snapshot_date(cls, snapshot_date: str, rtm_list: Optional[list], lob: str,
                                      sub_lobs: list[str], wi_props: list, nd_types: list = None) -> list:
        s = DirectshipDatabendSession()
        result = []
        try:

            filter_params = [cls.snapshot_date == snapshot_date]
            if rtm_list:
                filter_params.append(cls.rtm.in_(rtm_list))
            if lob:
                filter_params.append(cls.lob == lob)
            if sub_lobs:
                filter_params.append(cls.sub_lob.in_(sub_lobs))
            if nd_types:
                filter_params.append(cls.nd_type.in_(nd_types))

            group_by_props = []
            for prop in wi_props:
                group_by_props.append(getattr(cls, prop))

            total_so_acc_cq = func.sum(cls.total_so_acc_cq).label('total_so_acc_cq')
            ub3_acc_cq = func.sum(cls.ub3_acc_cq).label('ub3_acc_cq')
            ub7_acc_cq = func.sum(cls.ub7_acc_cq).label('ub7_acc_cq')
            total_so_acc_lq = func.sum(cls.total_so_acc_lq).label('total_so_acc_lq')
            ub3_acc_lq = func.sum(cls.ub3_acc_lq).label('ub3_acc_lq')
            ub7_acc_lq = func.sum(cls.ub7_acc_lq).label('ub7_acc_lq')
            total_so_cw = func.sum(cls.total_so_cw).label('total_so_cw')
            ub7_cw = func.sum(cls.ub7_cw).label('ub7_cw')
            total_so_cw1 = func.sum(cls.total_so_cw_1).label('total_so_cw1')
            ub7_cw1 = func.sum(cls.ub7_cw_1).label('ub7_cw1')
            total_so_cw2 = func.sum(cls.total_so_cw_2).label('total_so_cw2')
            ub7_cw2 = func.sum(cls.ub7_cw_2).label('ub7_cw2')
            total_so_cw3 = func.sum(cls.total_so_cw_3).label('total_so_cw3')
            ub7_cw3 = func.sum(cls.ub7_cw_3).label('ub7_cw3')
            total_so_cw4 = func.sum(cls.total_so_cw_4).label('total_so_cw4')
            ub7_cw4 = func.sum(cls.ub7_cw_4).label('ub7_cw4')
            last_total_so_cw = func.sum(cls.last_total_so_cw).label('last_total_so_cw')
            last_ub7_cw = func.sum(cls.last_ub7_cw).label('last_ub7_cw')
            last_total_so_cw1 = func.sum(cls.last_total_so_cw_1).label('last_total_so_cw1')
            last_ub7_cw1 = func.sum(cls.last_ub7_cw_1).label('last_ub7_cw1')
            # sum props
            sum_props = [total_so_acc_cq, ub3_acc_cq, ub7_acc_cq, total_so_acc_lq, ub3_acc_lq, ub7_acc_lq,
                         total_so_cw, ub7_cw, total_so_cw1, ub7_cw1, total_so_cw2, ub7_cw2, total_so_cw3,
                         ub7_cw3, total_so_cw4, ub7_cw4, last_total_so_cw, last_ub7_cw, last_total_so_cw1,
                         last_ub7_cw1]
            query_props = group_by_props + sum_props
            sreq = s.query(*query_props).filter(*filter_params).group_by(*group_by_props)
            raw_sql = sreq.statement.compile(compile_kwargs={"literal_binds": True}).string
            logger.info(raw_sql)
            result = sreq.query_list()

        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return result

    @classmethod
    def get_ub_overall_records(cls,
                               snapshot_date: str,
                               rtm_list: Optional[list],
                               lob: str,
                               sub_lobs: list[str]
                               ) -> list[PartnerOverallUbItem]:
        s = DirectshipDatabendSession()
        result = []
        try:
            filter_params = [cls.snapshot_date == snapshot_date]
            if rtm_list:
                filter_params.append(cls.rtm.in_(rtm_list))
            if lob:
                filter_params.append(cls.lob == lob)
            if sub_lobs:
                filter_params.append(cls.sub_lob.in_(sub_lobs))

            overall_list = s.query(cls).filter(*filter_params).query_list()

            for overall_item in overall_list:
                # 标准化对外显示名称(rtm、sub_rtm)
                display_rtm = db_rtm_to_display_rtm_dict.get(overall_item.get("rtm"),
                                                             overall_item.get("rtm"))
                display_sub_rtm = db_sub_rtm_to_display_sub_rtm_dict.get(overall_item.get("sub_rtm"),
                                                                         overall_item.get("sub_rtm"))
                partner_overall_ub_obj = PartnerOverallUbItem(
                    snapshot_date=overall_item.get("snapshot_date"),
                    rtm=display_rtm,
                    sub_rtm=display_sub_rtm,
                    nd_type=overall_item.get("nd_type"),
                    hq_name=overall_item.get("hq_name"),
                    lob=overall_item.get("lob"),
                    sub_lob=overall_item.get("sub_lob")
                )
                partner_overall_ub_obj.set_ub_data(
                    fiscal_qtr_year_name_cq=overall_item.get("fiscal_qtr_year_name_cq"),
                    total_so_acc_cq=overall_item.get("total_so_acc_cq"),
                    ub3_acc_cq=overall_item.get("ub3_acc_cq"),
                    ub7_acc_cq=overall_item.get("ub7_acc_cq"),
                    fiscal_qtr_year_name_lq=overall_item.get("fiscal_qtr_year_name_lq"),
                    total_so_acc_lq=overall_item.get("total_so_acc_lq"),
                    ub3_acc_lq=overall_item.get("ub3_acc_lq"),
                    ub7_acc_lq=overall_item.get("ub7_acc_lq"),
                    fiscal_qtr_week_name_cw=overall_item.get("fiscal_qtr_week_name_cw"),
                    total_so_cw=overall_item.get("total_so_cw"),
                    ub7_cw=overall_item.get("ub7_cw"),
                    fiscal_qtr_week_name_cw1=overall_item.get("fiscal_qtr_week_name_cw_1"),
                    total_so_cw1=overall_item.get("total_so_cw_1"),
                    ub7_cw1=overall_item.get("ub7_cw_1"),
                    fiscal_qtr_week_name_cw2=overall_item.get("fiscal_qtr_week_name_cw_2"),
                    total_so_cw2=overall_item.get("total_so_cw_2"),
                    ub7_cw2=overall_item.get("ub7_cw_2"),
                    fiscal_qtr_week_name_cw3=overall_item.get("fiscal_qtr_week_name_cw_3"),
                    total_so_cw3=overall_item.get("total_so_cw_3"),
                    ub7_cw3=overall_item.get("ub7_cw_3"),
                    fiscal_qtr_week_name_cw4=overall_item.get("fiscal_qtr_week_name_cw_4"),
                    total_so_cw4=overall_item.get("total_so_cw_4"),
                    ub7_cw4=overall_item.get("ub7_cw_4"),
                    last_fiscal_qtr_week_name_cw=overall_item.get("last_fiscal_qtr_week_name_cw"),
                    last_total_so_cw=overall_item.get("last_total_so_cw"),
                    last_ub7_cw=overall_item.get("last_ub7_cw"),
                    last_fiscal_qtr_week_name_cw1=overall_item.get("last_fiscal_qtr_week_name_cw_1"),
                    last_total_so_cw1=overall_item.get("last_total_so_cw_1"),
                    last_ub7_cw1=overall_item.get("last_ub7_cw_1")
                )
                result.append(partner_overall_ub_obj)

        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return result

    @classmethod
    def get_dynamic_info_by_snapshot_date(cls, snapshot_date: str) -> Optional[dict]:
        s = DirectshipDatabendSession()
        try:
            filter_params = []
            if snapshot_date:
                filter_params.append(cls.snapshot_date == snapshot_date)
            ret = s.query(
                cls.update_time.label('last_updated'),
                cls.fiscal_qtr_year_name_cq.label('qtd_current_quarter_name'),
                cls.fiscal_qtr_year_name_lq.label('qtd_last_quarter_name'),
                cls.fiscal_qtr_week_name_cw.label('weekly_trend_cw_name'),
                cls.fiscal_qtr_week_name_cw_1.label('weekly_trend_cw1_name'),
                cls.fiscal_qtr_week_name_cw_2.label('weekly_trend_cw2_name'),
                cls.fiscal_qtr_week_name_cw_3.label('weekly_trend_cw3_name'),
                cls.fiscal_qtr_week_name_cw_4.label('weekly_trend_cw4_name'),
                cls.last_fiscal_qtr_week_name_cw.label('last_weekly_trend_cw_name'),
                cls.last_fiscal_qtr_week_name_cw_1.label('last_weekly_trend_cw1_name')
            ).filter(*filter_params).limit(1).query_dict()

        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return ret

    @classmethod
    def query_menu(cls, rtms: Optional[list]):
        s = DirectshipDatabendSession()
        try:
            filter_params = []
            if rtms:
                filter_params.append(cls.rtm.in_(rtms))
            query_data = s.query(literal(REGION_CM).label("region"), cls.rtm, cls.lob,
                                 cls.sub_lob).filter(*filter_params).distinct().query_list()
            # 处理rtm对外显示名称
            for item in query_data:
                item["rtm"] = db_rtm_to_display_rtm_dict.get(item.get("rtm"), item.get("rtm"))
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return query_data

    @classmethod
    def query_history_report_date(cls, rtms: Optional[list], default_limit: int = 14):
        s = DirectshipDatabendSession()
        try:
            filter_params = []
            if rtms:
                filter_params.append(cls.rtm.in_(rtms))
            res = (
                s.query(cls.snapshot_date).filter(*filter_params).distinct()
                .order_by(desc(cls.snapshot_date)).limit(default_limit).query_list()
            )
            query_data = [item["snapshot_date"].strftime('%Y-%m-%d') for item in res]
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return query_data
