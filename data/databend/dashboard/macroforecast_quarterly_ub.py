import datetime

from sqlalchemy import Integer, Numeric
from data.databend.gc_dmp_macroforecast_databend_base import MacroforecastDatabendSession
from util.conf import Base
from util.conf import Column, String, TIMESTAMP
from util.conf import logger

GEO_CN = 'China mainland'
GEO_GC = 'Greater China'
RTM_PG = 'Pan Geo'


class MacroForecastQuarterlyUb(Base):
    __tablename__ = "app_macroforecast_quarterly_ub_da"
    __table_args__ = {"schema": "gc_dmp_macroforecast"}

    order_number = Column(Integer, primary_key=True)
    forecast_qtr = Column(String(256), primary_key=True)
    forecast_vers = Column(String(256))
    lob = Column(String(256))
    geo = Column(String(256))
    rtm = Column(String(256))
    update_date = Column(String(256))
    ub_forecast = Column(Numeric(24, 6))
    yoy = Column(Numeric(24, 6))
    acc = Column(Numeric(24, 6))
    create_time = Column(TIMESTAMP, default=datetime.datetime.now())
    update_time = Column(TIMESTAMP, default=datetime.datetime.now, onupdate=datetime.datetime.now)

    @classmethod
    def get_qtr_data_list(cls, fiscal_qtr: str) -> list:
        s = MacroforecastDatabendSession()
        ret = []
        try:
            ret = s.query(cls.geo.label("geo"),
                          cls.rtm.label("rtm"),
                          cls.ub_forecast.label("ub_forecast"),
                          cls.yoy.label("yoy"),
                          cls.acc.label("acc"))\
                .filter(cls.forecast_qtr == fiscal_qtr)\
                .order_by(cls.order_number.asc()).query_list()
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return ret
