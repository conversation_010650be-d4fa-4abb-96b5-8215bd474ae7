from data.databend.supply_data_databend_base import SupplyDatabendSession
from util.const import ErrorExcept
from util.gc_dmp_base import *
from util.util import pd


class CountryMlForecast(Base):
    __tablename__ = "app_fast_country_ml_forecast_wi"
    __table_args__ = {"schema": "gc_dmp_fast"}

    fiscal_week_year = Column(Integer, primary_key=True)
    fiscal_qtr_week_name = Column(String(256), comment='FY23Q1W12')
    sales_org_id = Column(Integer)
    sales_org = Column(String(256))
    lob = Column(String(256))
    sub_lob = Column(String(256))
    prod_id = Column(String(256))
    prediction_cw = Column(Integer)
    prediction_cw1 = Column(Integer)
    prediction_cw2 = Column(Integer)
    prediction_cw3 = Column(Integer)
    prediction_cw4 = Column(Integer)
    prediction_cw5 = Column(Integer)
    prediction_cw6 = Column(Integer)
    prediction_cw7 = Column(Integer)
    prediction_cw8 = Column(Integer)
    prediction_cw9 = Column(Integer)
    prediction_cw10 = Column(Integer)
    prediction_cw11 = Column(Integer)
    prediction_cw12 = Column(Integer)
    create_time = Column(DateTime, default=datetime.now, comment='create time')
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='update time')

    @classmethod
    def query_by_week_lob_region(cls, fiscal_qtr_week_name, lob, region):
        s = SupplyDatabendSession()
        res = []
        try:
            res = s.query(
                    cls.fiscal_qtr_week_name.label('fiscal_week'), cls.sales_org.label('region'), cls.prod_id.label('mpn'),
                    cls.prediction_cw.label('forecast_cw_ml'), cls.prediction_cw1.label('forecast_cw1_ml')) \
                .filter(cls.fiscal_qtr_week_name == fiscal_qtr_week_name) \
                .filter(cls.lob == lob) \
                .filter(cls.sales_org == region) \
                .query_list()
            return res
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return res

