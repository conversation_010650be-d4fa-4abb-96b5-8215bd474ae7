import datetime
from typing import Optional

from data.databend.gc_dmp_directship_databend_base import DirectshipDatabendSession
from util.conf import Base
from util.conf import Column, String, Date, Integer, TIMESTAMP, func
from util.conf import logger
from data.databend.supply_data_databend_base import SupplyDatabendSession
from util.const import REGION_CM
from sqlalchemy import or_, and_

from util.util import env_dev


class PoGapDi(Base):
    __tablename__ = "app_fast_dashboard_po_gap_wi"
    # __tablename__ = "app_fast_dashboard_po_gap_jdsplit_wi" # 仅用于测试环境
    __table_args__ = {"schema": "test_db" if env_dev() else "gc_dmp_fast"}

    fiscal_week_year = Column(Integer, primary_key=True)
    fiscal_qtr_week_name = Column(String(256), comment='FY23Q1W12')
    sales_org = Column(String(256))
    rtm = Column(String(256))
    business_type = Column(String(256))
    sold_to_id = Column(String(256))
    sold_to_name = Column(String(512))
    sold_to_name_abbre = Column(String(512))
    lob = Column(String(256))
    sub_lob = Column(String(256))
    nand = Column(String(256))
    color = Column(String(256))
    mpn = Column(String(128))
    cw1_demand = Column(Integer)
    cw2_demand = Column(Integer)
    available_po_cw1 = Column(Integer)
    available_po_cw2 = Column(Integer)
    po_needed_cw1 = Column(Integer)
    po_needed_cw2 = Column(Integer)
    publish_status = Column(Integer)
    create_time = Column(TIMESTAMP, default=datetime.datetime.now())
    update_time = Column(TIMESTAMP, default=datetime.datetime.now, onupdate=datetime.datetime.now)
    
    __PUBLISHED_STATUS = 1

    @classmethod
    def __get_session(cls):
        if env_dev():
            return DirectshipDatabendSession()
        else:
            return SupplyDatabendSession()

    @classmethod
    def query_download_po_gap_records(cls, fiscal_week: str, rtm: str):
        """明细数据下载"""
        s = cls.__get_session()
        try:
            filter_params = [
                cls.rtm != 'All',
                cls.business_type != 'All',
                cls.sold_to_id != 'All',
                cls.nand != 'All',
                cls.color != 'All',
                cls.mpn != 'All',
                or_(
                    and_(
                        cls.sales_org == 'China mainland',
                        cls.rtm != 'Retail Partner'
                    ),
                    cls.sales_org != 'China mainland'
                ),
                cls.publish_status == cls.__PUBLISHED_STATUS
            ]
            if fiscal_week:
                filter_params.append(cls.fiscal_qtr_week_name == fiscal_week)
            if rtm:
                filter_params.append(cls.rtm == rtm)
            query_data = s.query(cls.fiscal_qtr_week_name.label("fiscal_week"),
                                 cls.sales_org.label("region"),
                                 cls.rtm,
                                 cls.business_type.label("sub_rtm"),
                                 cls.sold_to_id,
                                 cls.lob,
                                 cls.sub_lob,
                                 cls.nand,
                                 cls.color,
                                 cls.mpn,
                                 cls.cw1_demand,
                                 cls.cw2_demand,
                                 cls.available_po_cw1,
                                 cls.available_po_cw2,
                                 cls.po_needed_cw1,
                                 cls.po_needed_cw2,
                                 cls.update_time).filter(*filter_params).query_list()
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return query_data

    @classmethod
    def query_menu_po_gap_records(cls, fiscal_week: str, rtm: Optional[str]):
        s = cls.__get_session()
        try:
            filter_params = [cls.publish_status == cls.__PUBLISHED_STATUS]
            if fiscal_week:
                filter_params.append(cls.fiscal_qtr_week_name == fiscal_week)
            if rtm:
                filter_params.append(cls.rtm == rtm)
            query_data = s.query(cls.sales_org.label("region"),
                                 cls.rtm,
                                 cls.business_type.label("sub_rtm"),
                                 cls.lob,
                                 cls.sub_lob
                                 ).filter(*filter_params).distinct().query_list()
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return query_data

    @classmethod
    def get_max_fiscal_week_year(cls):
        s = cls.__get_session()
        try:
            max_fiscal_week_year = s.query(func.max(cls.fiscal_week_year).label('max_fiscal_week_year')).query_dict()
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return max_fiscal_week_year

    @classmethod
    def get_fiscal_weeks(cls, default_limit=0):
        s = cls.__get_session()
        try:
            # 20250307 根据publish_status状态去决定是否漏出数据 publish_status: 1: 已发布; 0: 未发布
            query = s.query(cls.fiscal_qtr_week_name).filter(cls.publish_status == cls.__PUBLISHED_STATUS).distinct().order_by(cls.fiscal_week_year.desc())
            if default_limit:
                query = query.limit(default_limit)

            fiscal_weeks = query.query_list()
            ret = [item.get("fiscal_qtr_week_name") for item in fiscal_weeks]
            return ret
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()

    @classmethod
    def get_latest_refresh_time(cls):
        s = cls.__get_session()
        try:
            # 20250307 根据publish_status状态去决定是否漏出数据 publish_status: 1: 已发布; 0: 未发布
            filter_params = [cls.publish_status == cls.__PUBLISHED_STATUS]
            query_data = s.query(func.max(cls.update_time).label("update_time")).filter(*filter_params).query_dict()
            latest_refresh_time = query_data["update_time"].strftime('%Y-%m-%d %H:%M:%S') if query_data["update_time"] else ""
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return latest_refresh_time

    @classmethod
    def query_product_view(cls, region: str, rtm: str, sub_rtm: str, lob: str, sub_lobs: list, fiscal_week: str) -> list:
        s = cls.__get_session()
        try:
            # 20250306 发现e2e需求中, 数据组会多刷sub_lob = all的数据
            filter_params = [cls.sold_to_id == 'All', cls.mpn == 'All', cls.sub_lob != 'All',
                             cls.publish_status == cls.__PUBLISHED_STATUS]
            if region:
                filter_params.append(cls.sales_org == region)
            if rtm:
                filter_params.append(cls.rtm == rtm)
            if sub_rtm:
                filter_params.append(cls.business_type == sub_rtm)
            if lob:
                filter_params.append(cls.lob == lob)
            if sub_lobs:
                filter_params.append(cls.sub_lob.in_(sub_lobs))
            if fiscal_week:
                filter_params.append(cls.fiscal_qtr_week_name == fiscal_week)
            query_data = (s.query(
                cls.sub_lob.label("sub_lob"),
                cls.nand.label("nand"),
                cls.color.label("color"),
                cls.cw1_demand.label("cw1_demand"),
                cls.available_po_cw1.label("available_po_cw1"),
                cls.po_needed_cw1.label("po_needed_cw1"),
                cls.cw2_demand.label("cw2_demand"),
                cls.available_po_cw2.label("available_po_cw2"),
                cls.po_needed_cw2.label("po_needed_cw2")
            ).filter(*filter_params).query_list())
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return query_data

    @classmethod
    def query_channel_view(cls, region: str, lob: str, sub_lobs: list, fiscal_week: str, rtm: str) -> list:
        s = cls.__get_session()
        try:
            # nand 和color 必须是all，因为channel view 只看到sub lob纬度，sub lob下的nand 和 color 都必须是汇总到一起的
            # rtm 必须不是all，因为rtm是一个单独展示的纬度, 也不可以是RP
            # 20250306 发现e2e需求中, 数据组会多刷sub_lob = all的数据
            filter_params = [cls.nand == "All", cls.color == "All", cls.rtm != 'All',
                             cls.sold_to_id == 'All', cls.mpn == 'All', cls.sub_lob != 'All',
                             cls.publish_status == cls.__PUBLISHED_STATUS]
            if region:
                filter_params.append(cls.sales_org == region)
                if region == REGION_CM:
                    filter_params.append(cls.rtm != 'Retail Partner')
            if lob:
                filter_params.append(cls.lob == lob)
            if sub_lobs:
                filter_params.append(cls.sub_lob.in_(sub_lobs))
            if fiscal_week:
                filter_params.append(cls.fiscal_qtr_week_name == fiscal_week)
            if rtm:
                filter_params.append(cls.rtm == rtm)
            query_data = (s.query(
                cls.rtm.label("rtm"),
                cls.business_type.label("sub_rtm"),
                cls.sub_lob.label("sub_lob"),
                cls.cw1_demand.label("cw1_demand"),
                cls.available_po_cw1.label("available_po_cw1"),
                cls.po_needed_cw1.label("po_needed_cw1"),
                cls.cw2_demand.label("cw2_demand"),
                cls.available_po_cw2.label("available_po_cw2"),
                cls.po_needed_cw2.label("po_needed_cw2")
            ).filter(*filter_params).query_list())
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return query_data

    @classmethod
    def query_sold_to_view(cls, region: str, lob: str, sub_lobs: list, fiscal_week: str, rtm: str) -> list:
        s = cls.__get_session()
        try:
            # nand 和color 必须是all，因为sold to view 只看到sold to纬度，sold to下的nand 和 color 都必须是汇总到一起的
            # 中国大陆的RTM不可以是RP, 因为是分rtm展示的
            # 20250306 发现e2e需求中, 数据组会多刷sub_lob = all的数据
            filter_params = [cls.nand == "All", cls.color == "All", cls.mpn == 'All', cls.sub_lob != 'All',
                             cls.publish_status == cls.__PUBLISHED_STATUS]
            if region:
                filter_params.append(cls.sales_org == region)
                if region == REGION_CM:
                    filter_params.append(cls.rtm != 'Retail Partner')
            if lob:
                filter_params.append(cls.lob == lob)
            if sub_lobs:
                filter_params.append(cls.sub_lob.in_(sub_lobs))
            if fiscal_week:
                filter_params.append(cls.fiscal_qtr_week_name == fiscal_week)
            if rtm:
                filter_params.append(cls.rtm == rtm)
            query_data = (s.query(
                cls.rtm.label("rtm"),
                cls.business_type.label("sub_rtm"),
                cls.sold_to_id.label("sold_to_id"),
                cls.sold_to_name_abbre.label("sold_to_name"), # 使用缩写名称替换长名称
                # cls.sold_to_name_abbre.label("sold_to_name_abbre"),
                func.sum(cls.cw1_demand).label('cw1_demand'),
                func.sum(cls.available_po_cw1).label('available_po_cw1'),
                func.sum(cls.po_needed_cw1).label('po_needed_cw1'),
                func.sum(cls.cw2_demand).label('cw2_demand'),
                func.sum(cls.available_po_cw2).label('available_po_cw2'),
                func.sum(cls.po_needed_cw2).label('po_needed_cw2')
            ).filter(*filter_params).group_by(cls.rtm, cls.business_type, cls.sold_to_id, cls.sold_to_name_abbre).query_list())
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return query_data
    
    @classmethod
    def query_distinct_sublob(cls, region: str, rtm: str, sub_rtm: str, lob: str, fiscal_week: str) -> list:
        s = cls.__get_session()
        ret = []
        try:
            filter_params = [cls.sold_to_id == 'All', cls.mpn == 'All', cls.sub_lob != 'All',
                             cls.publish_status == cls.__PUBLISHED_STATUS]
            if region:
                filter_params.append(cls.sales_org == region)
            if rtm:
                filter_params.append(cls.rtm == rtm)
            if sub_rtm:
                filter_params.append(cls.business_type == sub_rtm)
            if lob:
                filter_params.append(cls.lob == lob)
            if fiscal_week:
                filter_params.append(cls.fiscal_qtr_week_name == fiscal_week)
            query_data = (s.query(
                cls.sub_lob.distinct().label("sub_lob")
            ).filter(*filter_params).query_list())
            
            ret = [item["sub_lob"] for item in query_data]
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return ret

    @classmethod
    def get_max_fiscal_qtr_week_name(cls) -> dict:
        s = cls.__get_session()
        try:
            # 查询已经发布demand的最新周
            max_fiscal_week_dict = s.query(cls.fiscal_qtr_week_name).filter(cls.publish_status == cls.__PUBLISHED_STATUS).order_by(cls.fiscal_week_year.desc()).limit(1).query_dict()
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return max_fiscal_week_dict
