import datetime
import os

from sqlalchemy import Float, func

from data.databend.gc_dmp_directship_databend_base import DirectshipDatabendSession
from data.email_config import EmailConfigRepository
from domain.dashboard.entity.anti_fraud.anti_fraud_pos_suspension import PosSuspensionItem
from domain.dashboard.entity.anti_fraud.anti_fraud_so_ub import rtm_dict, sub_rtm_dict
from domain.dashboard.entity.anti_fraud.pos_suspension_nand import nands
from kit.token_bucket import EMAIL_BUCKET_KEY_PREFIX
from util.conf import Base
from util.conf import Column, String, Integer, TIMESTAMP
from util.conf import logger
from util.const import EmailCmd
from util.send_email import del_email_config, async_rate_limited_send_email_v2
from util.util import env_dev


class PosSuspensionSummaryDi(Base):
    __tablename__ = "app_pos_suspension_summary_wi"
    __table_args__ = {"schema": "gc_dmp_directship"}
    # __table_args__ = {"schema": "test_db" if env_dev() else "gc_dmp_directship"}

    fiscal_qtr_week_name = Column(String(256), primary_key=True)
    rtm = Column(String(256))
    sub_rtm = Column(String(256))
    disti_id = Column(String(256))
    disti_name = Column(String(256))
    ub7_threshold = Column(Float)
    no_ub_pos = Column(Integer)
    bottom_pos = Column(Integer)
    no_ub_pos_lw = Column(Integer)
    bottom_pos_lw = Column(Integer)
    suspend_1_time_pos = Column(Integer)
    suspend_2_time_pos = Column(Integer)
    suspend_3_time_pos = Column(Integer)
    suspend_1_time_pos_lw = Column(Integer)
    suspend_2_time_pos_lw = Column(Integer)
    suspend_3_time_pos_lw = Column(Integer)
    create_time = Column(TIMESTAMP, default=datetime.datetime.now())
    update_time = Column(TIMESTAMP, default=datetime.datetime.now, onupdate=datetime.datetime.now)

    @classmethod
    def query_pos_suspension_records(cls, fiscal_week: str) -> list:
        white_list: list[PosSuspensionItem] = []
        s = DirectshipDatabendSession()
        try:
            pos_suspension_list = s.query(
                cls.rtm,
                cls.sub_rtm,
                cls.disti_name,
                cls.ub7_threshold,
                cls.no_ub_pos,
                cls.bottom_pos,
                cls.no_ub_pos_lw,
                cls.bottom_pos_lw,
                cls.suspend_1_time_pos,
                cls.suspend_2_time_pos,
                cls.suspend_3_time_pos,
                cls.suspend_1_time_pos_lw,
                cls.suspend_2_time_pos_lw,
                cls.suspend_3_time_pos_lw
            ).filter(cls.fiscal_qtr_week_name == fiscal_week).order_by(cls.rtm, cls.sub_rtm, cls.disti_name).query_list()

            if not pos_suspension_list:
                email_config = EmailConfigRepository.query_email_config(EmailCmd.Dashboard_V2)
                params = {"env": os.environ.get('ENV'),
                          "dashboard": 'Anti-Fraud UB POS Suspension',
                          "source": 'email_report',
                          "message": f'ub_pos_suspension_summary no data: {fiscal_week}'
                          }
                file_paths, email_config = del_email_config(email_config=email_config, params=params,
                                                            read_template=False)

                async_rate_limited_send_email_v2(limit_key=EMAIL_BUCKET_KEY_PREFIX + 'ub_pos_suspension',
                                                 capacity=1,
                                                 expire_time=1200,
                                                 email_config=email_config,
                                                 file_paths=file_paths)
                raise Exception(f"ub_pos_suspension_summary no data: {fiscal_week}")

            # 根据白名单去set数据
            for nand in nands:
                target_item = PosSuspensionItem(
                    rtm=nand.rtm,
                    sub_rtm=nand.sub_rtm,
                    disti_name=nand.disti_name,
                    display_name=nand.display_name,
                    level=nand.level
                )
                for item in pos_suspension_list:
                    origin_rtm = item.get("rtm")
                    rtm = rtm_dict.get(origin_rtm, origin_rtm)

                    origin_sub_rtm = item.get("sub_rtm")
                    sub_rtm = sub_rtm_dict.get(origin_sub_rtm, origin_sub_rtm)

                    if (target_item.rtm == rtm and target_item.sub_rtm == sub_rtm
                            and target_item.disti_name == item.get("disti_name")):
                        target_item.set_data(
                            ub7_rate=item.get('ub7_threshold'),
                            no_ub_pos=item.get('no_ub_pos'),
                            bottom_pos=item.get('bottom_pos'),
                            no_ub_pos_lw=item.get('no_ub_pos_lw'),
                            bottom_pos_lw=item.get('bottom_pos_lw'),
                            suspend_1_time_pos=item.get("suspend_1_time_pos"),
                            suspend_2_time_pos=item.get("suspend_2_time_pos"),
                            suspend_3_time_pos=item.get("suspend_3_time_pos"),
                            suspend_1_time_pos_lw=item.get("suspend_1_time_pos_lw"),
                            suspend_2_time_pos_lw=item.get("suspend_2_time_pos_lw"),
                            suspend_3_time_pos_lw=item.get("suspend_3_time_pos_lw")
                        )
                white_list.append(target_item)
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return white_list

    @classmethod
    def get_latest_refresh_time(cls, fiscal_week: str):
        s = DirectshipDatabendSession()
        try:
            filter_params = []
            if fiscal_week:
                filter_params.append(cls.fiscal_qtr_week_name == fiscal_week)
            query_data = s.query(func.max(cls.update_time).label("update_time")).filter(*filter_params).query_dict()
            latest_refresh_time = query_data["update_time"] if query_data["update_time"] else datetime.datetime.now()   # 默认当前时间
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return latest_refresh_time
