import datetime

from sqlalchemy import Integer, Numeric
from data.databend.gc_dmp_macroforecast_databend_base import MacroforecastDatabendSession
from util.conf import Base
from util.conf import Column, String, TIMESTAMP
from util.conf import logger


class MacroForecastValue(Base):
    __tablename__ = "app_macroforecast_value_da"
    __table_args__ = {"schema": "gc_dmp_macroforecast"}

    order_number = Column(Integer, primary_key=True)
    forecast_qtr = Column(String(256), primary_key=True)
    update_date = Column(String(256))
    report = Column(String(256))
    forecast = Column(Numeric(24, 6))
    forecast_period = Column(String(256))
    actual = Column(Numeric(24, 6))
    actual_period = Column(String(256))
    yoy = Column(Numeric(24, 6))
    create_time = Column(TIMESTAMP, default=datetime.datetime.now())
    update_time = Column(TIMESTAMP, default=datetime.datetime.now, onupdate=datetime.datetime.now)

    @classmethod
    def get_qtr_data_list(cls, fiscal_qtr: str) -> dict:
        s = MacroforecastDatabendSession()
        ret = {}
        try:
            data_list = s.query(cls.report.label("report"),
                                cls.forecast.label("forecast"),
                                cls.forecast_period.label("forecast_period"),
                                cls.actual.label("actual"),
                                cls.actual_period.label("actual_period"),
                                cls.yoy.label("yoy"))\
                .filter(cls.forecast_qtr == fiscal_qtr)\
                .order_by(cls.order_number.asc()).query_list()
            if data_list:
                ret = {item.get('report'): item for item in data_list}
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return ret
