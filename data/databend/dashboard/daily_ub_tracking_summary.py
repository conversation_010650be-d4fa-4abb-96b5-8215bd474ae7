import datetime
import os

from sqlalchemy import func, literal, or_, and_

from data.databend.gc_dmp_directship_databend_base import DirectshipDatabendSession
from data.email_config import EmailConfigRepository
from domain.dashboard.entity.anti_fraud.anti_fraud_so_ub import RTMS_SORT_RULES, SUB_LOB_SORT_RULES, \
    SUB_RTMS_SORT_RULES, DailyItem, rtm_dict, RTMS_RULES, \
    SUB_LOB_RULES, RTMS_SUB_RTM_MAPPING, RTMS_SUB_RTM_PLATFORM_MAPPING, ONLINE, OFFLINE, UNAUTHORIZED, RTM_ENT, \
    RTM_CHANNEL_ONLINE
from kit.token_bucket import EMAIL_BUCKET_KEY_PREFIX
from util.conf import Base
from util.conf import Column, String, Integer, TIMESTAMP
from util.conf import logger
from util.const import ALL, EmailCmd
from util.send_email import del_email_config, async_rate_limited_send_email_v2
from util.util import env_dev


class DailyUbTrackingSummaryDi(Base):
    __tablename__ = "app_directship_daily_ub_tracking_summary_di"
    __table_args__ = {"schema": "gc_dmp_directship"}
    # __table_args__ = {"schema": "test_db" if env_dev() else "gc_dmp_directship"}

    snapshot_date = Column(String(256))
    fiscal_dt = Column(String(256), primary_key=True)
    fiscal_qtr_year = Column(Integer)
    fiscal_qtr_year_name = Column(String(256))
    rtm = Column(String(256))
    sub_rtm = Column(String(256))
    lob = Column(String(256))
    sub_lob = Column(String(256))
    online_offline = Column(String(256))
    platform = Column(String(256))
    total_so_acc = Column(Integer)
    ub3_acc = Column(Integer)
    ub7_acc = Column(Integer)
    fiscal_qtr_year_lq = Column(Integer)
    fiscal_qtr_year_name_lq = Column(String(256))
    total_so_acc_lq = Column(Integer)
    ub3_acc_lq = Column(Integer)
    ub7_acc_lq = Column(Integer)
    create_time = Column(TIMESTAMP, default=datetime.datetime.now())
    update_time = Column(TIMESTAMP, default=datetime.datetime.now, onupdate=datetime.datetime.now)

    @staticmethod
    def handle_cross_quarter_data(data: list[DailyItem], is_cross_quarter: bool, current_quarter: str):
        # 跨季度: Online和Ent都是T-2产出数据, 季度开始的第二天,应该无数据
        if not is_cross_quarter:
            return
        for item in data:
            item.set_fiscal_qtr_year_name(current_quarter)
            if item.rtm in [RTM_ENT, RTM_CHANNEL_ONLINE]:
                item.set_total_so(0)
                item.set_lte_7_days_ub(0)
                item.set_lte_3_days_ub(0)

    @classmethod
    def query_daily_ub_summary_records(cls, snapshot_date: str, is_cross_quarter: bool, current_quarter: str, rtm: str = "All") -> list:
        result = []
        white_list: list[DailyItem] = []
        s = DirectshipDatabendSession()
        try:
            filter_params = [
                cls.snapshot_date == snapshot_date
            ]
            query = s.query(
                            cls.snapshot_date.label("fiscal_dt"),
                            cls.fiscal_qtr_year_name,
                            cls.rtm,
                            cls.sub_rtm,
                            cls.sub_lob,
                            cls.total_so_acc.label("total_so"),
                            cls.ub3_acc.label("ub3"),
                            cls.ub7_acc.label("ub7"),
                            cls.fiscal_qtr_year_name_lq,
                            cls.total_so_acc_lq.label("total_so_lq"),
                            cls.ub3_acc_lq.label("ub3_lq"),
                            cls.ub7_acc_lq.label("ub7_lq"),
                            cls.online_offline.label("online_offline"),
                            cls.platform.label("platform"),
                    ).filter(cls.platform==ALL).filter(cls.online_offline==ALL).filter(*filter_params)
            # todo 此代码不能传rtm的筛选
            if rtm != "All":
                query = query.filter(cls.rtm == rtm)
            daily_list = query.query_list()

            if not daily_list:
                email_config = EmailConfigRepository.query_email_config(EmailCmd.Dashboard_V2)
                params = {"env": os.environ.get('ENV'),
                          "dashboard": 'Anti-iPhone UB Summary',
                          "source": 'email_report',
                          "message": f'daily_ub_summary no data: {snapshot_date}'
                          }
                file_paths, email_config = del_email_config(email_config=email_config, params=params,
                                                            read_template=False)

                async_rate_limited_send_email_v2(limit_key=EMAIL_BUCKET_KEY_PREFIX + 'daily_ub',
                                                 capacity=1,
                                                 expire_time=1200,
                                                 email_config=email_config,
                                                 file_paths=file_paths)
                raise Exception(f"daily_ub_summary no data: {snapshot_date}")

            for item in daily_list:
                # todo entiy构造
                origin_rtm = item.get("rtm")
                rtm = rtm_dict.get(origin_rtm, origin_rtm)
                result.append(DailyItem(
                    fiscal_dt=item.get("fiscal_dt"),
                    fiscal_qtr_year_name=item.get("fiscal_qtr_year_name"),
                    rtm=rtm,
                    sub_rtm=item.get("sub_rtm"),
                    sub_lob=item.get("sub_lob"),
                    total_so=item.get("total_so"),
                    lte_3_days_ub=item.get("ub3"),
                    lte_7_days_ub=item.get("ub7"),
                    fiscal_qtr_year_name_lq=item.get("fiscal_qtr_year_name_lq"),
                    total_so_lq=item.get("total_so_lq"),
                    lte_3_days_ub_lq=item.get("ub3_lq"),
                    lte_7_days_ub_lq=item.get("ub7_lq")
                ))
            # 取其中一条季度信息, 用于白名单填充
            fiscal_qtr_year_name = daily_list[0].get("fiscal_qtr_year_name")
            fiscal_qtr_year_name_lq = daily_list[0].get("fiscal_qtr_year_name_lq")

            # 构造一个白名单
            for rtm in RTMS_RULES:
                for sub_rtm in RTMS_SUB_RTM_MAPPING[rtm]:
                    for sub_lob in SUB_LOB_RULES:
                        white_list.append(
                            DailyItem(
                                fiscal_dt=snapshot_date,
                                rtm=rtm,
                                sub_rtm=sub_rtm,
                                sub_lob=sub_lob,
                                total_so=0,
                                lte_3_days_ub=0,
                                lte_7_days_ub=0,
                                total_so_lq=0,
                                lte_3_days_ub_lq=0,
                                lte_7_days_ub_lq=0,
                                fiscal_qtr_year_name=fiscal_qtr_year_name,
                                fiscal_qtr_year_name_lq=fiscal_qtr_year_name_lq
                            )
                        )
            # 根据白名单去set数据
            for white_item in white_list:
                for item in result:
                    if white_item.rtm == item.rtm and white_item.sub_rtm == item.sub_rtm and white_item.sub_lob == item.sub_lob:
                        white_item.set_fiscal_dt(item.fiscal_dt)
                        white_item.set_fiscal_qtr_year_name(item.fiscal_qtr_year_name)
                        white_item.set_total_so(item.total_so)
                        white_item.set_lte_7_days_ub(item.lte_7_days_ub)
                        white_item.set_lte_3_days_ub(item.lte_3_days_ub)

                        white_item.set_fiscal_qtr_year_name_lq(item.fiscal_qtr_year_name_lq)
                        white_item.set_total_so_lq(item.total_so_lq)
                        white_item.set_lte_7_days_ub_lq(item.lte_7_days_ub_lq)
                        white_item.set_lte_3_days_ub_lq(item.lte_3_days_ub_lq)

        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()

        # 跨季度: Online和Ent都是T-2产出数据, 季度开始的第二天,应该无数据
        cls.handle_cross_quarter_data(data=white_list, is_cross_quarter=is_cross_quarter, current_quarter=current_quarter)
        return white_list

    @classmethod
    def get_latest_refresh_time(cls, fiscal_dt: str):
        s = DirectshipDatabendSession()
        latest_refresh_time = ""
        try:
            filter_params = []
            if fiscal_dt:
                filter_params.append(cls.fiscal_dt == fiscal_dt)
            query_data = s.query(func.max(cls.update_time).label("update_time")).filter(*filter_params).query_dict()
            latest_refresh_time = query_data["update_time"].strftime('%Y-%m-%d %H:%M:%S') if query_data[
                "update_time"] else ""
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return latest_refresh_time

    @classmethod
    def query_region_daily_ub_summary_records(cls, snapshot_date: str, is_cross_quarter: bool, current_quarter: str, rtm: str = "All") -> list:
        result = []
        white_list = []
        s = DirectshipDatabendSession()
        try:
            filter_params = [
                cls.snapshot_date == snapshot_date
            ]
            q = s.query(
                        cls.snapshot_date.label("fiscal_dt"),
                        cls.fiscal_qtr_year_name,
                        cls.rtm.label('rtm'),
                        cls.sub_rtm.label('sub_rtm'),
                        cls.sub_lob,
                        cls.total_so_acc.label("total_so"),
                        cls.ub3_acc.label("ub3"),
                        cls.ub7_acc.label("ub7"),
                        cls.fiscal_qtr_year_name_lq,
                        cls.total_so_acc_lq.label("total_so_lq"),
                        cls.ub3_acc_lq.label("ub3_lq"),
                        cls.ub7_acc_lq.label("ub7_lq")
                        ).filter(*filter_params).filter(cls.platform==ALL).filter(cls.online_offline==ALL)\
                        .filter(cls.sub_rtm != "Duty-free") # 需要过滤掉Duty-free的数据再进行汇总
            # todo 此代码不能传rtm的筛选
            if rtm != "All":
                q = s.filter(cls.rtm == rtm)

            # q = q.group_by(cls.snapshot_date).group_by(cls.sub_lob).group_by(cls.fiscal_qtr_year_name).group_by(cls.fiscal_qtr_year_name_lq)
            daily_list = q.query_list()

            new_daily_list = []
            for item in daily_list:
                # todo entiy构造
                origin_rtm = item.get("rtm")
                rtm = rtm_dict.get(origin_rtm, origin_rtm)
                new_daily_list.append(DailyItem(
                    fiscal_dt=item.get("fiscal_dt"),
                    fiscal_qtr_year_name=item.get("fiscal_qtr_year_name"),
                    rtm=rtm,
                    sub_rtm=item.get("sub_rtm"),
                    sub_lob=item.get("sub_lob"),
                    total_so=item.get("total_so"),
                    lte_3_days_ub=item.get("ub3"),
                    lte_7_days_ub=item.get("ub7"),
                    fiscal_qtr_year_name_lq=item.get("fiscal_qtr_year_name_lq"),
                    total_so_lq=item.get("total_so_lq"),
                    lte_3_days_ub_lq=item.get("ub3_lq"),
                    lte_7_days_ub_lq=item.get("ub7_lq")
                ))

            # 跨季度: Online和Ent都是T-2产出数据, 季度开始的第二天,应该无数据
            cls.handle_cross_quarter_data(data=new_daily_list, is_cross_quarter=is_cross_quarter, current_quarter=current_quarter)

            # 取其中一条季度信息, 用于白名单填充
            fiscal_qtr_year_name = new_daily_list[0].fiscal_qtr_year_name
            fiscal_qtr_year_name_lq = new_daily_list[0].fiscal_qtr_year_name_lq

            for sub_lob in SUB_LOB_RULES:
                white_list.append(DailyItem(
                    fiscal_dt=snapshot_date,
                    rtm=ALL,
                    sub_rtm=ALL,
                    sub_lob=sub_lob,
                    total_so=0,
                    lte_3_days_ub=0,
                    lte_7_days_ub=0,
                    total_so_lq=0,
                    lte_3_days_ub_lq=0,
                    lte_7_days_ub_lq=0,
                    fiscal_qtr_year_name=fiscal_qtr_year_name,
                    fiscal_qtr_year_name_lq=fiscal_qtr_year_name_lq
                ))

            # 根据白名单去set求和数据
            for white_item in white_list:
                for item in new_daily_list:
                    if white_item.sub_lob == item.sub_lob:
                        white_item.set_fiscal_qtr_year_name(item.fiscal_qtr_year_name)
                        white_item.add_total_so(item.total_so)
                        white_item.add_lte_3_days_ub(item.lte_3_days_ub)
                        white_item.add_lte_7_days_ub(item.lte_7_days_ub)

                        white_item.set_fiscal_qtr_year_name_lq(item.fiscal_qtr_year_name_lq)
                        white_item.add_total_so_lq(item.total_so_lq)
                        white_item.add_lte_7_days_ub_lq(item.lte_7_days_ub_lq)
                        white_item.add_lte_3_days_ub_lq(item.lte_3_days_ub_lq)
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()

        white_list = [item.as_dict() for item in white_list]
        white_list = sorted(white_list, key=lambda x: (
            RTMS_SORT_RULES.index(x.get('rtm')) if x.get('rtm') in RTMS_SORT_RULES else 999,
            SUB_RTMS_SORT_RULES.index(x.get('sub_rtm')) if x.get('sub_rtm') in SUB_RTMS_SORT_RULES else 9999,
            SUB_LOB_SORT_RULES.index(x.get('sub_lob')) if x.get('sub_lob') in SUB_LOB_SORT_RULES else 999999))
        return white_list

    @classmethod
    def query_platform_daily_ub_summary_records(cls, snapshot_date: str, is_cross_quarter: bool, current_quarter: str, rtm: str = "All") -> list:
        result = []
        white_list: list[DailyItem] = []
        s = DirectshipDatabendSession()
        try:
            filter_params = [
                cls.snapshot_date == snapshot_date,
                or_(
                    and_(
                        cls.online_offline == 'Online',
                        cls.platform != ALL
                    ),
                    and_(
                        cls.online_offline == 'Offline',
                        cls.platform == ALL
                    ),
                    and_(
                        cls.online_offline == 'Unauthorized',
                        cls.platform == ALL
                    )
                )
            ]
            query = s.query(
                            cls.snapshot_date.label("fiscal_dt"),
                            cls.fiscal_qtr_year_name,
                            cls.rtm,
                            cls.sub_rtm,
                            cls.sub_lob,
                            cls.total_so_acc.label("total_so"),
                            cls.ub3_acc.label("ub3"),
                            cls.ub7_acc.label("ub7"),
                            cls.online_offline.label("online_offline"),
                            cls.platform.label("platform"),
                            cls.fiscal_qtr_year_name_lq,
                            cls.total_so_acc_lq.label("total_so_lq"),
                            cls.ub3_acc_lq.label("ub3_lq"),
                            cls.ub7_acc_lq.label("ub7_lq")
                            ).filter(*filter_params)
            # todo 此代码不能传rtm的筛选
            if rtm != "All":
                query = query.filter(cls.rtm == rtm)
            daily_list = query.query_list()

            if not daily_list:
                email_config = EmailConfigRepository.query_email_config(EmailCmd.Dashboard_V2)
                params = {"env": os.environ.get('ENV'),
                          "dashboard": 'Anti-iPhone UB Summary',
                          "source": 'email_report',
                          "message": f'platform_daily_ub_summary no data: {snapshot_date}'
                          }

                file_paths, email_config = del_email_config(email_config=email_config, params=params,
                                                            read_template=False)

                async_rate_limited_send_email_v2(limit_key=EMAIL_BUCKET_KEY_PREFIX + 'platform_daily_ub',
                                                 capacity=1,
                                                 expire_time=1200,
                                                 email_config=email_config,
                                                 file_paths=file_paths)
                raise Exception(f"platform_daily_ub_summary no data: {snapshot_date}")

            for item in daily_list:
                origin_rtm = item.get("rtm")
                rtm = rtm_dict.get(origin_rtm, origin_rtm)
                result.append(DailyItem(
                    fiscal_dt=item.get("fiscal_dt"),
                    rtm=rtm,
                    sub_rtm=item.get("sub_rtm"),
                    sub_lob=item.get("sub_lob"),
                    total_so=item.get("total_so"),
                    lte_3_days_ub=item.get("ub3"),
                    lte_7_days_ub=item.get("ub7"),
                    online_offline=item.get("online_offline"),
                    platform=item.get("platform"),
                    fiscal_qtr_year_name=item.get("fiscal_qtr_year_name"),
                    fiscal_qtr_year_name_lq=item.get("fiscal_qtr_year_name_lq"),
                    total_so_lq=item.get("total_so_lq"),
                    lte_3_days_ub_lq=item.get("ub3_lq"),
                    lte_7_days_ub_lq=item.get("ub7_lq")
                ))
            # 取其中一条季度信息, 用于白名单填充
            fiscal_qtr_year_name = daily_list[0].get("fiscal_qtr_year_name")
            fiscal_qtr_year_name_lq = daily_list[0].get("fiscal_qtr_year_name_lq")

            # 构造一个白名单
            for rtm in RTMS_RULES:
                for sub_rtm, online_offline_info in RTMS_SUB_RTM_PLATFORM_MAPPING[rtm].items():
                    for online_offline, platforms in online_offline_info.items():
                        for platform in platforms:
                            for sub_lob in SUB_LOB_RULES:
                                white_list.append(
                                    DailyItem(
                                        fiscal_dt=snapshot_date,
                                        rtm=rtm,
                                        sub_rtm=sub_rtm,
                                        sub_lob=sub_lob,
                                        total_so=0,
                                        lte_3_days_ub=0,
                                        lte_7_days_ub=0,
                                        online_offline=online_offline,
                                        platform=platform,
                                        total_so_lq=0,
                                        lte_3_days_ub_lq=0,
                                        lte_7_days_ub_lq=0,
                                        fiscal_qtr_year_name=fiscal_qtr_year_name,
                                        fiscal_qtr_year_name_lq=fiscal_qtr_year_name_lq
                                    )
                                )
            # 根据白名单去set数据
            for white_item in white_list:
                for item in result:
                    if (white_item.rtm == item.rtm and white_item.sub_rtm == item.sub_rtm
                            and white_item.sub_lob == item.sub_lob and white_item.online_offline == item.online_offline):
                        if (
                                white_item.online_offline == UNAUTHORIZED
                                or white_item.online_offline == OFFLINE
                                or (white_item.online_offline == ONLINE and white_item.platform == item.platform)
                        ):
                            white_item.set_fiscal_qtr_year_name(item.fiscal_qtr_year_name)
                            white_item.set_total_so(item.total_so)
                            white_item.set_lte_7_days_ub(item.lte_7_days_ub)
                            white_item.set_lte_3_days_ub(item.lte_3_days_ub)

                            white_item.set_fiscal_qtr_year_name_lq(item.fiscal_qtr_year_name_lq)
                            white_item.set_total_so_lq(item.total_so_lq)
                            white_item.set_lte_7_days_ub_lq(item.lte_7_days_ub_lq)
                            white_item.set_lte_3_days_ub_lq(item.lte_3_days_ub_lq)

            # 跨季度: Online和Ent都是T-2产出数据, 季度开始的第二天,应该无数据
            cls.handle_cross_quarter_data(data=white_list, is_cross_quarter=is_cross_quarter, current_quarter=current_quarter)
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return white_list
