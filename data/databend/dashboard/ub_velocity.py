from sqlalchemy import or_, and_, subquery, desc, distinct, func
from data.databend.supply_data_databend_base import SupplyDatabendSession
from domain.dashboard.entity.fiscal_week import FiscalWeek
from domain.dashboard.entity.ub_velocity import UbVelocity, UbVelocityByWeeks
from util.conf import Base
from util.conf import Column, String, Float, DateTime
from util.conf import logger
from util.const import (
    FISCAL_WEEK_SEVERAL,
    REGION_CM,
    REGION_HK,
    REGION_TAIWAN,
    ALL,
    CARRIER,
    RETAIL_PARTNER,
)


class DashboardUbVelocity(Base):
    __tablename__ = "app_fast_dashboard_ub_velocity_wi"
    __table_args__ = {"schema": "gc_dmp_fast"}

    fiscal_week = Column(String(256), primary_key=True)
    filter_fiscal_week = Column(String(256))
    region = Column(String(256))
    rtm = Column(String(256))
    business_type = Column(String(256))
    lob = Column(String(256))
    sublob = Column(String(256))
    nand = Column(String(256))
    color = Column(String(256))
    actual_ub = Column(Float)
    ub_eoh = Column(Float)
    ub_velocity = Column(Float)
    create_time = Column(DateTime)
    update_time = Column(DateTime)

    @classmethod
    def get_ub_velocity_menu(cls, fiscal_week: str) -> dict:
        ret = {}
        s = SupplyDatabendSession()
        try:
            velocitys = (
                s.query(cls.region, cls.rtm, cls.business_type, cls.lob, cls.sublob)
                .distinct()
                .filter(cls.filter_fiscal_week == fiscal_week)
                .query_list()
            )

            lob_tem = {}
            region_rtm_tem = {
                REGION_CM: {},
                REGION_TAIWAN: {},
                REGION_HK: {},
            }  # {"region1":{"rtm1":[sub_rtms]},"region2":{"rtm1":[sub_rtms]}}
            retail_partners_tem = {}
            for item in velocitys:
                # lobs + sub_lobs================================================
                lob = item.get("lob")
                sublob = item.get("sublob")
                if lob not in lob_tem:
                    lob_tem[lob] = [sublob]
                else:
                    if sublob not in lob_tem[lob]:
                        lob_tem[lob].append(sublob)
                # region + rtm + sub_rtm=========================================
                region = item.get("region")
                rtm = item.get("rtm")
                sub_rtm = item.get("business_type")
                # 名称映射处理

                if region == REGION_CM and rtm != ALL and rtm != CARRIER:
                    # 特殊处理China Mainland下Retail Partner的按照rtm聚合sub_rtm后结果单独存放到变量retail_partners_tem
                    if rtm not in retail_partners_tem:
                        retail_partners_tem[rtm] = [sub_rtm]
                    else:
                        if sub_rtm not in retail_partners_tem[rtm]:
                            retail_partners_tem[rtm].append(sub_rtm)
                else:
                    if rtm not in region_rtm_tem.get(region, {}):
                        region_rtm_tem[region][rtm] = [sub_rtm]
                    else:
                        if sub_rtm not in region_rtm_tem[region][rtm]:
                            region_rtm_tem[region][rtm].append(sub_rtm)

            # 如果存在属于retail_partners下的rtms,给region_rtm_tem[REGION_CM]增加{"rtm":"Retail Partner","sub_rtms":None}
            if retail_partners_tem:
                region_rtm_tem[REGION_CM].update({RETAIL_PARTNER: None})
            # 组合返回结果中的lobs================================================
            lobs = []
            for lob, sub_lob in lob_tem.items():
                sub_lob.sort(reverse=True)
                if "iPhone SE (3rd Gen)" in sub_lob:
                    sub_lob.remove("iPhone SE (3rd Gen)")
                    sub_lob.append("iPhone SE (3rd Gen)")
                lobs.append({"lob": lob, "sub_lobs": sub_lob})
            lobs.sort(key=lambda x: x["lob"], reverse=False)
            # 组合region结果并在组合过程中进行排序==================================
            regions = []
            RTM_RETAIL_PARTNER_SORT_RULE = [
                "All",
                "Mono",
                "Multi",
                "Online",
                "Carrier",
                "Retail Partner",
                "Enterprise",
                "Education",
            ]
            for region, rtms in region_rtm_tem.items():
                rtms_tem = []
                for rtm, sub_rtms in rtms.items():
                    if ("Retail Partner" == rtm or "Education" == rtm) and "China mainland" != region:
                        sub_rtms = ['All']
                    if ("Enterprise" == rtm or "Education" == rtm) and "China mainland" == region:
                        sub_rtms = ['All']
                    # 所有的sub_rtm有值情况下均按照字母正序排列
                    sub_rtms.sort(reverse=False) if sub_rtms else sub_rtms
                    rtms_tem.append({"rtm": rtm, "sub_rtms": sub_rtms})
                try:
                    rtms_tem = sorted(
                        rtms_tem, key=lambda x: RTM_RETAIL_PARTNER_SORT_RULE.index(x["rtm"])
                    )
                except Exception as e:
                    logger.error(f"sort ub_velocity rtms error{e}")
                regions.append({"region": region, "rtms": rtms_tem})
            # 组合retail_partners结果并排序===========================================
            retail_partners = []
            if retail_partners_tem.get(RETAIL_PARTNER):
                # Retail Partner在页面菜单展示All
                retail_partners_tem[ALL] = retail_partners_tem.pop(RETAIL_PARTNER)
            for rtm, sub_rtms in retail_partners_tem.items():
                if ("Enterprise" == rtm or "Education" == rtm):
                    sub_rtms = ['All']
                sub_rtms.sort(reverse=False)
                retail_partners.append({"rtm": rtm, "sub_rtms": sub_rtms})
            try:
                retail_partners = sorted(
                    retail_partners,
                    key=lambda x: RTM_RETAIL_PARTNER_SORT_RULE.index(x["rtm"]),
                )
            except Exception as e:
                logger.error(f"sort retail_partners error {e}")
            ret["regions"] = regions
            ret["retail_partners"] = retail_partners
            ret["lobs"] = lobs
        finally:
            s.close()
        return ret

    @classmethod
    def get_ub_velocitys(
        cls,
        start_fiscal_week: str,
        end_fiscal_week: str,
        region: str,
        rtm: str,
        sub_rtm: str,
        lob: str,
        sub_lob: str,
        color: str,
        nand: str,
    ) -> list[UbVelocityByWeeks]:
        s = SupplyDatabendSession()
        ret = []
        try:
            obj = s.query(
                cls.fiscal_week,
                cls.sublob,
                cls.nand,
                cls.color,
                cls.ub_velocity,
                cls.rtm,
                cls.business_type,
            )

            obj = obj.filter(
                (
                        func.substr(cls.fiscal_week, 3, 2) * 1000
                        + func.substr(cls.fiscal_week, 6, 1) * 100
                        + func.substr(cls.fiscal_week, 8, 2)
                )
                >= FiscalWeek(start_fiscal_week).fiscal_week_int
            )
            obj = obj.filter(
                (
                        func.substr(cls.fiscal_week, 3, 2) * 1000
                        + func.substr(cls.fiscal_week, 6, 1) * 100
                        + func.substr(cls.fiscal_week, 8, 2)
                )
                <= FiscalWeek(end_fiscal_week).fiscal_week_int
            )

            obj = obj.filter(cls.region == region)
            obj = obj.filter(cls.rtm == rtm)
            obj = obj.filter(cls.business_type == sub_rtm)
            obj = obj.filter(cls.lob == lob)
            obj = obj.filter(cls.sublob == sub_lob)

            if color:
                obj = obj.filter(
                    or_(cls.color != "All", and_(cls.color == "All", cls.nand == "All"))
                )
            if nand:
                obj = obj.filter(
                    or_(cls.nand != "All", and_(cls.nand == "All", cls.color == "All"))
                )
            velocitys = obj.query_list()
            # 第一遍循环拿到的所有数据，将sublob,nand,color相同的组合到一起 即weeks{sublob_nand_color:[13周的UbVelocity对象]}
            weeks = {}
            for item in velocitys:
                fiscal_week = item.get("fiscal_week")
                sublob = item.get("sublob")
                nand = item.get("nand")
                color = item.get("color")
                ub_velocity = item.get("ub_velocity")

                velocity = UbVelocity(
                    fiscal_week=fiscal_week,
                    sub_lob=sublob,
                    nand=nand,
                    color=color,
                    ub_velocity=ub_velocity,
                )

                week_key = sublob + "_" + nand + "_" + color
                if week_key not in weeks:
                    weeks[week_key] = [velocity]
                else:
                    weeks[week_key].append(velocity)
            # 循环上一步的weeks，返回UbVelocityByWeeks对象列表
            for key, week in weeks.items():
                items = key.split("_")
                sublob, nand, color = items[0], items[1], items[2]
                velocity_by_weeks = UbVelocityByWeeks(
                    sub_lob=sublob, nand=nand, color=color, weeks=week
                )
                ret.append(velocity_by_weeks)
        finally:
            s.close()
        return ret

    @classmethod
    def get_fiscal_weeks(cls) -> list:
        s = SupplyDatabendSession()
        ret = []
        try:
            fiscal_weeks = s.query(cls.filter_fiscal_week).distinct().query_list()
            ret = [item.get("filter_fiscal_week") for item in fiscal_weeks]
            ret = sorted(ret, key=lambda x: FiscalWeek(x).fiscal_week_int, reverse=True)
        finally:
            s.close()
        return ret
