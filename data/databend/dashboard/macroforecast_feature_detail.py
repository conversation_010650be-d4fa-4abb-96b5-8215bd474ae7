import datetime

from sqlalchemy import Integer

from data.databend.gc_dmp_macroforecast_databend_base import MacroforecastDatabendSession
from util.conf import Base
from util.conf import Column, String, TIMESTAMP
from util.conf import logger


class MacroForecastFeatureDetail(Base):
    __tablename__ = "app_macroforecast_feature_detail_da"
    __table_args__ = {"schema": "gc_dmp_macroforecast"}

    order_number = Column(Integer, primary_key=True)
    forecast_qtr = Column(String(256), primary_key=True)
    update_date = Column(String(256))
    report = Column(String(256))
    category = Column(String(256))
    feature = Column(String(256))
    default_value = Column(String(256))
    default_range = Column(String(256))
    create_time = Column(TIMESTAMP, default=datetime.datetime.now())
    update_time = Column(TIMESTAMP, default=datetime.datetime.now, onupdate=datetime.datetime.now)

    @classmethod
    def get_qtr_data_list(cls, fiscal_qtr: str) -> dict:
        s = MacroforecastDatabendSession()
        ret = {}
        try:
            data_list = s.query(cls.report.label("report"),
                                cls.category.label("category"),
                                cls.feature.label("feature"),
                                cls.default_value.label("default_value"),
                                cls.default_range.label("default_range"))\
                .filter(cls.forecast_qtr == fiscal_qtr)\
                .order_by(cls.order_number.asc()).query_list()
            if data_list:
                for item in data_list:
                    if item.get('report') not in ret:
                        ret[item.get('report')] = []
                    ret[item.get('report')].append(item)
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return ret
