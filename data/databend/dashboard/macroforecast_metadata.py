import datetime

from data.databend.gc_dmp_macroforecast_databend_base import MacroforecastDatabendSession
from util.conf import Base
from util.conf import Column, String, TIMESTAMP
from util.conf import logger


class MacroForecastMetadata(Base):
    __tablename__ = "app_macroforecast_metadata_da"
    __table_args__ = {"schema": "gc_dmp_macroforecast"}

    forecast_qtr = Column(String(256), primary_key=True)
    report = Column(String(256))
    report_name = Column(String(256))
    forecast_comment = Column(String(256))
    create_time = Column(TIMESTAMP, default=datetime.datetime.now())
    update_time = Column(TIMESTAMP, default=datetime.datetime.now, onupdate=datetime.datetime.now)

    @classmethod
    def get_qtr_data_list(cls, fiscal_qtr: str) -> list:
        s = MacroforecastDatabendSession()
        ret = []
        try:
            ret = s.query(cls.report.label("report"),
                          cls.report_name.label("report_name"),
                          cls.forecast_comment.label("forecast_comment"))\
                .filter(cls.forecast_qtr == fiscal_qtr).query_list()
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return ret
