from util.gc_dmp_base import *
from data.databend.dfa_data_databend_base import DFADatabendSession
from util.util import env_dev


class ForecastComparisonDFA(Base):
    __tablename__ = "app_fast_dashboard_forecast_comparison_dfa_wi"
    __table_args__ = {"schema": f"gc_dmp_fast{'' if env_dev() else '_secure'}"}

    fiscal_week_year = Column(Integer, primary_key=True)
    fiscal_qtr_week_name = Column(String(256), comment='FY23Q1W12')
    data_type = Column(String(256))
    sales_org = Column(String(256))
    rtm = Column(String(256))
    lob = Column(String(256))
    sub_lob = Column(String(256))
    prediction_cw = Column(Float)
    prediction_cw1 = Column(Float)
    prediction_cw2 = Column(Float)
    prediction_cw3 = Column(Float)
    prediction_cw4 = Column(Float)
    prediction_cw5 = Column(Float)
    prediction_cw6 = Column(Float)
    prediction_total = Column(Float)
    prediction_total_lw = Column(Float)
    total_cw_minus_lw = Column(Float)
    create_time = Column(DateTime)
    update_time = Column(DateTime)

    @classmethod
    def query_comparison_dfa_data(cls, region: str, sub_lob: str, fiscal_week: str) -> list:
        s = DFADatabendSession()
        try:
            filter_params = []
            if region:
                filter_params.append(cls.sales_org == region)
            if sub_lob:
                filter_params.append(cls.sub_lob == sub_lob)
            if fiscal_week:
                filter_params.append(cls.fiscal_qtr_week_name == fiscal_week)
            query_data = s.query(
                cls.fiscal_week_year,
                cls.fiscal_qtr_week_name,
                cls.data_type,
                cls.sales_org,
                cls.rtm,
                cls.lob,
                cls.sub_lob,
                cls.prediction_cw.label('cw'),
                cls.prediction_cw1.label('cw1'),
                cls.prediction_cw2.label('cw2'),
                cls.prediction_cw3.label('cw3'),
                cls.prediction_cw4.label('cw4'),
                cls.prediction_cw5.label('cw5'),
                cls.prediction_cw6.label('cw6'),
                cls.prediction_total.label('total'),
                cls.prediction_total_lw.label('total_lw'),
                cls.total_cw_minus_lw.label('total_lw_minus_total')
            ).filter(*filter_params).query_list()
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return query_data

    @classmethod
    def get_latest_refresh_time(cls, fiscal_week: str):
        s = DFADatabendSession()
        try:
            filter_params = []
            if fiscal_week:
                filter_params.append(cls.fiscal_qtr_week_name == fiscal_week)
            query_data = s.query(func.max(cls.update_time).label("update_time")).filter(*filter_params).query_dict()
            latest_refresh_time = query_data["update_time"].strftime('%Y-%m-%d %H:%M:%S') if query_data["update_time"] else ""
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return latest_refresh_time
