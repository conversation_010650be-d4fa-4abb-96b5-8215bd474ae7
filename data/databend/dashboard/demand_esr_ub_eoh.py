from data.databend.gc_dmp_channel_compliance_databend import ComplianceDatabendSession
from data.databend.supply_data_databend_base import SupplyDatabendSession
from util.gc_dmp_base import *
from util.util import env_dev


class DemandEsrUbEoh(Base):
    __tablename__ = "app_fast_demand_esr_ub_eoh_wi"
    __table_args__ = {"schema": "gc_dmp_fast"}
    session = SupplyDatabendSession()
    if env_dev():
        __tablename__ = "app_fast_demand_esr_ub_eoh_wi_test"
        __table_args__ = {"schema": "test_db"}
        session = ComplianceDatabendSession()

    fiscal_week_year = Column(Integer, primary_key=True)
    fiscal_qtr_week_name = Column(String(256), comment='FY23Q1W12')
    sales_org_id = Column(Integer)
    sales_org = Column(String(256))
    sold_to_id = Column(String(256))
    sold_to_name = Column(String(256))
    rtm = Column(String(256))
    rtml4 = Column(String(256))
    business_type = Column(String(256))
    lob = Column(String(256))
    sub_lob = Column(String(256))
    mpn_id = Column(String(256))
    shipment_plan_cw = Column(Integer)
    shipment_plan_cw1 = Column(Integer)
    shipment_plan_cw2 = Column(Integer)
    shipment_plan_cw3 = Column(Integer)
    ub_eoh_lw = Column(Integer, comment='last week ub eoh')
    ub_lw = Column(Integer)

    @classmethod
    def query_eoh_by_region_rtm_fiscal_week(cls, region, fiscal_qtr_week_name: str, rtms: list[str]) -> list:
        s = cls.session
        res = []
        try:
            res = s.query(cls.fiscal_qtr_week_name.label('fiscal_week'), cls.rtm, cls.sales_org.label('region'),
                          cls.sold_to_id, cls.mpn_id.label('mpn'), cls.shipment_plan_cw, cls.shipment_plan_cw1,
                          cls.shipment_plan_cw2, cls.shipment_plan_cw3, cls.ub_eoh_lw.label('ub_eoh')) \
                .filter(cls.fiscal_qtr_week_name == fiscal_qtr_week_name) \
                .filter(cls.rtm.in_(rtms)) \
                .filter(cls.sales_org == region) \
                .query_list()
            return res
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return res

    @classmethod
    def query_by_region_rtm_fiscal_weeks(cls, region, fiscal_qtr_week_names: list[str], rtms: list[str]) -> list:
        s = cls.session
        res = []
        try:
            res = s.query(cls.fiscal_qtr_week_name.label('fiscal_week'), cls.sales_org.label('region'), cls.sold_to_id,
                          cls.mpn_id.label('mpn'),
                          func.coalesce(cls.ub_lw, 0).label('ub')) \
                .filter(cls.fiscal_qtr_week_name.in_(fiscal_qtr_week_names)) \
                .filter(cls.rtm.in_(rtms)) \
                .filter(cls.sales_org == region) \
                .query_list()
            return res
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return res