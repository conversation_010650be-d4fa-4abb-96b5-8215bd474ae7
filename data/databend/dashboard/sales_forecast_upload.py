from data.databend.gc_dmp_channel_compliance_databend import ComplianceDatabendSession
from data.databend.supply_data_databend_base import SupplyDatabendSession
from util.const import ErrorExcept
from util.gc_dmp_base import *
from util.util import pd, env_dev


class SalesForecastUpload(Base):
    __tablename__ = f"app_fast_dashboard_rtm_sales_forecast_upload_wi"
    __table_args__ = {"schema": "gc_dmp_fast"}
    session = SupplyDatabendSession()
    if env_dev():
        __tablename__ = f"app_fast_dashboard_rtm_sales_forecast_upload_wi_test"
        __table_args__ = {"schema": "test_db"}
        session = ComplianceDatabendSession()

    # rtm, fiscal_week_year, week_date, sold_to_id, sold_to_name, lob, model, mpn, uploader, forecast_cw, forecast_cw1, forecast_cw2, forecast_cw3, forecast_cw4, forecast_cw5, forecast_cw6, forecast_cw7, forecast_cw8, forecast_cw9, forecast_type, region, sub_rtm, sub_lob, nand, color, create_time, update_time
    fiscal_week_year = Column(Integer, primary_key=True)
    week_date = Column(String(256), comment='FY23Q1W12')
    region = Column(String(256))
    rtm = Column(String(256))
    sub_rtm = Column(String(256))
    sold_to_id = Column(String(256))
    sold_to_name = Column(String(256))
    lob = Column(String(256))
    sub_lob = Column(String(256))
    model = Column(String(256))
    mpn = Column(String(256))
    nand = Column(String(256))
    color = Column(String(256))
    forecast_type = Column(String(256))
    forecast_cw = Column(Integer)
    forecast_cw1 = Column(Integer)
    forecast_cw2 = Column(Integer)
    forecast_cw3 = Column(Integer)
    forecast_cw4 = Column(Integer)
    forecast_cw5 = Column(Integer)
    forecast_cw6 = Column(Integer)
    forecast_cw7 = Column(Integer)
    forecast_cw8 = Column(Integer)
    forecast_cw9 = Column(Integer)
    forecast_cw10 = Column(Integer)
    forecast_cw11 = Column(Integer)
    forecast_cw12 = Column(Integer)
    uploader = Column(String(256))
    create_time = Column(DateTime, default=datetime.now, comment='create time')
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='update time')

    @classmethod
    def query_sales_by_region_rtm_fiscal_week(cls, region, fiscal_week, rtms):
        s = cls.session
        res = []
        try:
            res = s.query(
                cls.week_date.label('fiscal_week'), cls.region, cls.sold_to_id, cls.mpn,
                cls.lob, cls.rtm, cls.sub_rtm, cls.forecast_cw.label('forecast_cw_sales'),
                cls.forecast_cw1.label('forecast_cw1_sales'), cls.forecast_cw2.label('forecast_cw2_sales'),
                cls.forecast_cw3.label('forecast_cw3_sales'), cls.forecast_cw4.label('forecast_cw4_sales'),
                cls.forecast_cw5.label('forecast_cw5_sales'), cls.forecast_cw6.label('forecast_cw6_sales'),
                cls.forecast_cw7.label('forecast_cw7_sales'), cls.forecast_cw8.label('forecast_cw8_sales'),
                cls.forecast_cw9.label('forecast_cw9_sales'), cls.forecast_cw10.label('forecast_cw10_sales'),
                cls.forecast_cw11.label('forecast_cw11_sales'), cls.forecast_cw12.label('forecast_cw12_sales')) \
                .filter(cls.week_date == fiscal_week, cls.rtm.in_(rtms), cls.region == region) \
                .query_list()
            return res
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return res

    @classmethod
    def query_sales_by_region_fiscal_week(cls, region, fiscal_week, lob):
        s = cls.session
        res = []
        try:
            res = s.query(
                cls.week_date.label('fiscal_week'), cls.region, cls.sold_to_id, cls.mpn,
                cls.lob, cls.rtm, cls.sub_rtm, cls.forecast_cw.label('forecast_cw_sales'),
                cls.forecast_cw1.label('forecast_cw1_sales'), cls.forecast_cw2.label('forecast_cw2_sales'),
                cls.forecast_cw3.label('forecast_cw3_sales'), cls.forecast_cw4.label('forecast_cw4_sales'),
                cls.forecast_cw5.label('forecast_cw5_sales'), cls.forecast_cw6.label('forecast_cw6_sales')) \
                .filter(cls.week_date == fiscal_week, cls.region == region, cls.lob == lob) \
                .query_list()
            return res
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return res

