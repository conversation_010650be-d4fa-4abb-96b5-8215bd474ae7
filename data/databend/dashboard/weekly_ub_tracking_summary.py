import datetime
import os

from sqlalchemy import func, literal, or_, and_

from data.databend.gc_dmp_directship_databend_base import DirectshipDatabendSession
from data.email_config import EmailConfigRepository
from domain.dashboard.entity.anti_fraud.anti_fraud_so_ub import RTMS_SORT_RULES, SUB_LOB_SORT_RULES, \
    SUB_RTMS_SORT_RULES, WeeklyItem, RTMS_RULES, SUB_LOB_RULES, \
    RTMS_SUB_RTM_MAPPING, ONLINE, RTMS_SUB_RTM_PLATFORM_MAPPING, OFFLINE, rtm_dict, UNAUTHORIZED
from kit.token_bucket import EMAIL_BUCKET_KEY_PREFIX
from util.conf import Base
from util.conf import Column, String, Integer, TIMESTAMP
from util.conf import logger
from util.const import ALL, EmailCmd
from util.send_email import del_email_config, async_rate_limited_send_email_v2
from util.util import env_dev


class WeeklyUbTrackingSummaryDi(Base):
    __tablename__ = "app_directship_weekly_ub_tracking_summary_di"
    __table_args__ = {"schema": "gc_dmp_directship"}
    # __table_args__ = {"schema": "test_db" if env_dev() else "gc_dmp_directship"}

    snapshot_date = Column(String(256), primary_key=True)
    fiscal_week_year = Column(Integer, primary_key=True)
    fiscal_qtr_week_name = Column(String(256), comment='FY23Q1W12', primary_key=True)
    rtm = Column(String(256))
    sub_rtm = Column(String(256))
    lob = Column(String(256))
    sub_lob = Column(String(256))
    online_offline = Column(String(256))
    platform = Column(String(256))
    total_so = Column(Integer)
    ub7 = Column(Integer)
    create_time = Column(TIMESTAMP, default=datetime.datetime.now())
    update_time = Column(TIMESTAMP, default=datetime.datetime.now, onupdate=datetime.datetime.now)

    @classmethod
    def query_week_ub_summary_records(cls, snapshot_date: str, fiscal_week: str):
        result = []
        white_list: list[WeeklyItem] = []
        s = DirectshipDatabendSession()
        try:
            week_list = (s.query(
                cls.snapshot_date,
                cls.fiscal_qtr_week_name,
                cls.rtm,
                cls.sub_rtm,
                cls.sub_lob,
                cls.total_so,
                cls.ub7
                ).filter(cls.snapshot_date == snapshot_date).filter(cls.fiscal_qtr_week_name == fiscal_week)
                         .filter(cls.platform==ALL).filter(cls.online_offline==ALL)
                         .query_list()
            )
            if not week_list:
                email_config = EmailConfigRepository.query_email_config(EmailCmd.Dashboard_V2)
                params = {"env": os.environ.get('ENV'),
                          "dashboard": 'Anti-iPhone UB Summary',
                          "source": 'email_report',
                          "message": f"week_ub_summary no data: fiscal_week: {fiscal_week}, snapshot_date: {snapshot_date}"
                          }

                file_paths, email_config = del_email_config(email_config=email_config, params=params,
                                                            read_template=False)

                async_rate_limited_send_email_v2(limit_key=EMAIL_BUCKET_KEY_PREFIX + 'week_ub',
                                                 capacity=1,
                                                 expire_time=1200,
                                                 email_config=email_config,
                                                 file_paths=file_paths)
                raise Exception(f"week_ub_summary no data: fiscal_week: {fiscal_week}, snapshot_date: {snapshot_date}")

            for item in week_list:
                origin_rtm = item.get("rtm")
                rtm = rtm_dict.get(origin_rtm, origin_rtm)
                result.append(WeeklyItem(
                    item.get('snapshot_date'),
                    item.get('fiscal_qtr_week_name'),
                    rtm,
                    item.get('sub_rtm'),
                    item.get('sub_lob'),
                    item.get('total_so'),
                    item.get('ub7')
                ))

            # 构造一个白名单
            for rtm in RTMS_RULES:
                for sub_rtm in RTMS_SUB_RTM_MAPPING[rtm]:
                    for sub_lob in SUB_LOB_RULES:
                        white_list.append(
                            WeeklyItem(
                                snapshot_date,
                                fiscal_week,
                                rtm,
                                sub_rtm,
                                sub_lob,
                                0,
                                0
                            )
                        )
            # 根据白名单去set数据
            for white_item in white_list:
                for item in result:
                    if white_item.rtm == item.rtm and white_item.sub_rtm == item.sub_rtm and white_item.sub_lob == item.sub_lob:
                        white_item.set_total_so(item.total_so)
                        white_item.set_lte_7_days_ub(item.lte_7_days_ub)

        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return white_list

    @classmethod
    def get_latest_refresh_time(cls, snapshot_date: str):
        s = DirectshipDatabendSession()
        latest_refresh_time = ""
        try:
            filter_params = []
            if snapshot_date:
                filter_params.append(cls.snapshot_date == snapshot_date)
            query_data = s.query(func.max(cls.update_time).label("update_time")).filter(*filter_params).query_dict()
            latest_refresh_time = query_data["update_time"].strftime('%Y-%m-%d %H:%M:%S') if query_data["update_time"] else ""
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return latest_refresh_time

    @classmethod
    def query_region_week_ub_summary_records(cls, snapshot_date: str, fiscal_week: str):
        result = []
        white_list = []
        s = DirectshipDatabendSession()
        try:
            q = (s.query(
                cls.snapshot_date,
                cls.fiscal_qtr_week_name,
                literal(ALL).label('rtm'),
                literal(ALL).label('sub_rtm'),
                cls.sub_lob,
                func.sum(cls.total_so).label("total_so"),
                func.sum(cls.ub7).label("ub7")
            ).filter(cls.snapshot_date == snapshot_date).filter(cls.fiscal_qtr_week_name == fiscal_week)
                 .filter(cls.platform==ALL).filter(cls.online_offline==ALL)
                 .filter(cls.sub_rtm != "Duty-free") # 需要过滤掉Duty-free的数据再进行汇总
                 )

            daily_list = q.group_by(cls.snapshot_date).group_by(
                cls.fiscal_qtr_week_name).group_by(cls.sub_lob).query_list()

            for sub_lob in SUB_LOB_RULES:
                white_list.append(WeeklyItem(
                    snapshot_date,
                    fiscal_week,
                    ALL,
                    ALL,
                    sub_lob,
                    0,
                    0
                ))

            # 根据白名单去set数据
            for white_item in white_list:
                for item in daily_list:
                    if white_item.rtm == item.get("rtm") and white_item.sub_rtm == item.get("sub_rtm") and white_item.sub_lob == item.get("sub_lob"):
                        white_item.set_total_so(item.get("total_so"))
                        white_item.set_lte_7_days_ub(item.get("ub7"))
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        white_list = [item.as_dict() for  item in white_list]
        
        white_list = sorted(white_list, key=lambda x: (
            RTMS_SORT_RULES.index(x.get('rtm')) if x.get('rtm') in RTMS_SORT_RULES else 999,
            SUB_RTMS_SORT_RULES.index(x.get('sub_rtm')) if x.get('sub_rtm') in SUB_RTMS_SORT_RULES else 9999,
            SUB_LOB_SORT_RULES.index(x.get('sub_lob')) if x.get('sub_lob') in SUB_LOB_SORT_RULES else 999999))
        return white_list

    @classmethod
    def query_platform_week_ub_summary_records(cls, snapshot_date: str, fiscal_week: str):
        result = []
        white_list: list[WeeklyItem] = []
        s = DirectshipDatabendSession()
        try:
            filter_params = [
                cls.snapshot_date == snapshot_date,
                cls.fiscal_qtr_week_name == fiscal_week,
                or_(
                    and_(
                        cls.online_offline == 'Online',
                        cls.platform != ALL
                    ),
                    and_(
                        cls.online_offline == 'Offline',
                        cls.platform == ALL
                    ),
                    and_(
                        cls.online_offline == 'Unauthorized',
                        cls.platform == ALL
                    )
                )
            ]
            week_list = (s.query(
                cls.snapshot_date,
                cls.fiscal_qtr_week_name,
                cls.rtm,
                cls.sub_rtm,
                cls.sub_lob,
                cls.total_so,
                cls.ub7,
                cls.platform,
                cls.online_offline
                ).filter(*filter_params).query_list()
            )
            if not week_list:
                email_config = EmailConfigRepository.query_email_config(EmailCmd.Dashboard_V2)
                params = {"env": os.environ.get('ENV'),
                          "dashboard": 'Anti-iPhone UB Summary',
                          "source": 'email_report',
                          "message": f"platform_week_ub_summary no data: fiscal_week: {fiscal_week}, snapshot_date: {snapshot_date}"
                          }
                file_paths, email_config = del_email_config(email_config=email_config, params=params,
                                                            read_template=False)

                async_rate_limited_send_email_v2(limit_key=EMAIL_BUCKET_KEY_PREFIX + 'platform_week_ub',
                                                 capacity=1,
                                                 expire_time=1200,
                                                 email_config=email_config,
                                                 file_paths=file_paths)
                raise Exception(f"platform_week_ub_summary no data: fiscal_week: {fiscal_week}, snapshot_date: {snapshot_date}")
            for item in week_list:
                origin_rtm = item.get("rtm")
                rtm = rtm_dict.get(origin_rtm, origin_rtm)
                result.append(WeeklyItem(
                    snapshot_date=item.get('snapshot_date'),
                    fiscal_qtr_week_name=item.get('fiscal_qtr_week_name'),
                    rtm=rtm,
                    sub_rtm=item.get('sub_rtm'),
                    sub_lob=item.get('sub_lob'),
                    total_so=item.get('total_so'),
                    lte_7_days_ub=item.get('ub7'),
                    online_offline=item.get('online_offline'),
                    platform=item.get('platform'),
                ))

            # 构造一个白名单
            for rtm in RTMS_RULES:
                for sub_rtm, online_offline_info in RTMS_SUB_RTM_PLATFORM_MAPPING[rtm].items():
                    for online_offline, platforms in online_offline_info.items():
                        for platform in platforms:
                            for sub_lob in SUB_LOB_RULES:
                                white_list.append(
                                    WeeklyItem(
                                        snapshot_date=snapshot_date,
                                        fiscal_qtr_week_name=fiscal_week,
                                        rtm=rtm,
                                        sub_rtm=sub_rtm,
                                        sub_lob=sub_lob,
                                        total_so=0,
                                        lte_7_days_ub=0,
                                        online_offline=online_offline,
                                        platform=platform,
                                    )
                                )
            # 根据白名单去set数据
            for white_item in white_list:
                for item in result:
                    if (white_item.rtm == item.rtm and white_item.sub_rtm == item.sub_rtm
                            and white_item.sub_lob == item.sub_lob and white_item.online_offline == item.online_offline):
                        if (
                                white_item.online_offline == UNAUTHORIZED
                                or white_item.online_offline == OFFLINE
                                or (white_item.online_offline == ONLINE and white_item.platform == item.platform)
                        ):
                            white_item.set_total_so(item.total_so)
                            white_item.set_lte_7_days_ub(item.lte_7_days_ub)
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return white_list

    @classmethod
    def query_rolling_weeks(cls, snapshot_date: str, limit: int = 5):
        s = DirectshipDatabendSession()
        rolling_weeks = []
        try:
            filter_params = []
            if snapshot_date:
                filter_params.append(cls.snapshot_date == snapshot_date)
            
            q = (s.query(cls.fiscal_week_year, cls.fiscal_qtr_week_name).distinct()
                 .filter(*filter_params)
                 .order_by(cls.fiscal_week_year.desc())
                 .limit(limit))
            query_result = q.query_list()
            # 降序查询最新n周，再将数组逆序转换为正常升序数组
            rolling_weeks = [week.get('fiscal_qtr_week_name') for week in query_result]
            rolling_weeks.reverse()
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return rolling_weeks
