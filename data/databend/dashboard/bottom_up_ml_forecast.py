from data.databend.gc_dmp_channel_compliance_databend import ComplianceDatabendSession
from data.databend.supply_data_databend_base import SupplyDatabendSession
from util.gc_dmp_base import *
from util.util import env_dev


class BottomUpMlForecast(Base):
    __tablename__ = "app_fast_bottom_up_ml_forecast_wi"
    __table_args__ = {"schema": "gc_dmp_fast"}
    session = SupplyDatabendSession()
    if env_dev():
        __tablename__ = "app_fast_bottom_up_ml_forecast_wi_test"
        __table_args__ = { "schema": "test_db" }
        session = ComplianceDatabendSession()

    fiscal_week_year = Column(Integer, primary_key=True)
    fiscal_qtr_week_name = Column(String(256), comment='FY23Q1W12')
    sales_org_id = Column(Integer)
    sales_org = Column(String(256))
    sold_to_id = Column(String(256))
    sold_to_name = Column(String(256))
    rtm = Column(String(256))
    rtml4 = Column(String(256))
    business_type = Column(String(256))
    abbre = Column(String(256))
    lob = Column(String(256))
    sub_lob = Column(String(256))
    prod_id = Column(String(256))
    measure_cd = Column(String(256))
    prediction_cw = Column(Integer)
    prediction_cw1 = Column(Integer)
    prediction_cw2 = Column(Integer)
    prediction_cw3 = Column(Integer)
    prediction_cw4 = Column(Integer)
    prediction_cw5 = Column(Integer)
    prediction_cw6 = Column(Integer)
    prediction_cw7 = Column(Integer)
    prediction_cw8 = Column(Integer)
    prediction_cw9 = Column(Integer)
    prediction_cw10 = Column(Integer)
    prediction_cw11 = Column(Integer)
    prediction_cw12 = Column(Integer)
    create_time = Column(DateTime, default=datetime.now, comment='create time')
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='update time')

    @classmethod
    def query_all_forecast_by_week_and_lob(cls, fiscal_qtr_week_name, lob, region):
        s = cls.session
        res = []
        try:
            res = s.query(
                            cls.fiscal_qtr_week_name.label('fiscal_week'), cls.sold_to_id, cls.prod_id.label('mpn'),
                            cls.prediction_cw.label('forecast_cw_ml'), cls.prediction_cw1.label('forecast_cw1_ml'),
                            cls.prediction_cw2.label('forecast_cw2_ml'), cls.prediction_cw3.label('forecast_cw3_ml'),
                            cls.prediction_cw4.label('forecast_cw4_ml'), cls.prediction_cw5.label('forecast_cw5_ml'),
                            cls.prediction_cw6.label('forecast_cw6_ml')) \
                .filter(cls.fiscal_qtr_week_name == fiscal_qtr_week_name) \
                .filter(cls.lob == lob) \
                .filter(cls.sales_org == region) \
                .query_list()
            return res
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return res
