import datetime

from sqlalchemy import Date, Float

from data.databend.gc_dmp_directship_databend_base import DirectshipDatabendSession

from util.conf import Base
from util.conf import Column, String, Integer, TIMESTAMP
from util.conf import logger
from util.util import env_dev


class PosSuspensionDetailDi(Base):
    __tablename__ = "app_pos_suspension_detail_wi"
    __table_args__ = {"schema": "gc_dmp_directship"}
    # __table_args__ = {"schema": "test_db" if env_dev() else "gc_dmp_directship"}

    fiscal_week_year = Column(Integer)
    fiscal_qtr_week_name = Column(String(256), primary_key=True)
    rtm = Column(String(256))
    sub_rtm = Column(String(256))
    reseller_hqid = Column(String(256))
    reseller_name = Column(String(256))
    t2_reseller_hqid = Column(String(256))
    t2_reseller_name = Column(String(256))
    apple_id = Column(String(256))
    pos_name = Column(String(256))
    suspension_times = Column(Integer)
    begin_dt = Column(Date)
    end_dt = Column(Date)
    suspension_duration = Column(String(256))
    reason_code = Column(String(256))
    week1 = Column(String(256))
    so_week1 = Column(Integer)
    ub7_week1 = Column(Integer)
    ub7_rate_week1 = Column(Float)
    week2 = Column(String(256))
    so_week2 = Column(Integer)
    ub7_week2 = Column(Integer)
    ub7_rate_week2 = Column(Float)
    week3 = Column(String(256))
    so_week3 = Column(Integer)
    ub7_week3 = Column(Integer)
    ub7_rate_week3 = Column(Float)
    week4 = Column(String(256))
    so_week4 = Column(Integer)
    ub7_week4 = Column(Integer)
    ub7_rate_week4 = Column(Float)
    week5 = Column(String(256))
    so_week5 = Column(Integer)
    ub7_week5 = Column(Integer)
    ub7_rate_week5 = Column(Float)
    week6 = Column(String(256))
    so_week6 = Column(Integer)
    ub7_week6 = Column(Integer)
    ub7_rate_week6 = Column(Float)
    week7 = Column(String(256))
    so_week7 = Column(Integer)
    ub7_week7 = Column(Integer)
    ub7_rate_week7 = Column(Float)
    week8 = Column(String(256))
    so_week8 = Column(Integer)
    ub7_week8 = Column(Integer)
    ub7_rate_week8 = Column(Float)
    week9 = Column(String(256))
    so_week9 = Column(Integer)
    ub7_week9 = Column(Integer)
    ub7_rate_week9 = Column(Float)
    week10 = Column(String(256))
    so_week10 = Column(Integer)
    ub7_week10 = Column(Integer)
    ub7_rate_week10 = Column(Float)
    week11 = Column(String(256))
    so_week11 = Column(Integer)
    ub7_week11 = Column(Integer)
    ub7_rate_week11 = Column(Float)
    week12 = Column(String(256))
    so_week12 = Column(Integer)
    ub7_week12 = Column(Integer)
    ub7_rate_week12 = Column(Float)
    week13 = Column(String(256))
    so_week13 = Column(Integer)
    ub7_week13 = Column(Integer)
    ub7_rate_week13 = Column(Float)
    week14 = Column(String(256))
    so_week14 = Column(Integer)
    ub7_week14 = Column(Integer)
    ub7_rate_week14 = Column(Float)
    create_time = Column(TIMESTAMP, default=datetime.datetime.now())
    update_time = Column(TIMESTAMP, default=datetime.datetime.now, onupdate=datetime.datetime.now)

    @classmethod
    def query_pos_suspension_detail_records(cls, fiscal_week: str, rtm: str = None) -> list:
        s = DirectshipDatabendSession()
        try:
            filter_params = [
                cls.fiscal_qtr_week_name == fiscal_week
            ]
            if rtm:
                filter_params.append(cls.rtm == rtm)

            pos_suspension_list = s.query(cls).filter(*filter_params).query_list()
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return pos_suspension_list
