from sqlalchemy import func

from data.databend.dashboard.forecast_actual.forecast_accuracy_abstract import (
    ForecastAccuracyAbstract,
)
from data.databend.gc_dmp_directship_databend_base import DirectshipDatabendSession
from data.databend.supply_data_databend_base import SupplyDatabendSession
from domain.dashboard.entity.fiscal_week import FiscalWeek
from util.gc_dmp_base import Base
from util.conf import logger
from util.util import env_dev


class ForecastAccuracyRTMAndML(Base, ForecastAccuracyAbstract):
    __tablename__ = "app_fast_dashboard_forecast_accuracy_wi"
    __table_args__ = {"schema": "test_db" if env_dev() else "gc_dmp_fast"}
    
    @classmethod
    def _get_session(cls):
        if env_dev():
            return DirectshipDatabendSession()
        else:
            return SupplyDatabendSession()

    @classmethod
    def get_fiscal_weeks(cls, start_week_int: int = 202534) -> list:
        '''从FY25Q3W8开始展示'''
        s = cls._get_session()
        ret = []
        try:
            fiscal_weeks = (s.query(cls.fiscal_qtr_week_name.label("fiscal_week")).distinct()
                            .filter(cls.fiscal_week_year >= start_week_int).query_list())
            ret = [item.get("fiscal_week") for item in fiscal_weeks]
            ret = sorted(ret, key=lambda x: FiscalWeek(x).fiscal_week_int, reverse=True)
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

    @classmethod
    def get_menu_records_by_fiscal_week(cls, fiscal_week: str, hr_lr: str) -> list:
        s = cls._get_session()
        res = []
        try:
            res = s.query(
                cls.sales_org.label("region"),
                cls.lob,
                cls.rtm,
                cls.sub_rtm,
                cls.sub_lob,
                cls.nand,
                cls.color
            ).filter(cls.fiscal_qtr_week_name == fiscal_week, cls.hr_lr == hr_lr).distinct().query_list()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return res

    @classmethod
    def get_latest_refresh_time(cls, fiscal_week: str):
        s = cls._get_session()
        try:
            filter_params = []
            if fiscal_week:
                filter_params.append(cls.fiscal_qtr_week_name == fiscal_week)
            query_data = s.query(func.max(cls.update_time).label("update_time")).filter(*filter_params).query_dict()
            latest_refresh_time = query_data["update_time"].strftime('%Y-%m-%d %H:%M:%S') if query_data["update_time"] else ""
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return latest_refresh_time
