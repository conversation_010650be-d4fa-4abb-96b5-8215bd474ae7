from domain.dashboard.entity.fiscal_week import FiscalWeek
from util.gc_dmp_base import *
from data.databend.dfa_data_databend_base import DFADatabendSession
from data.databend.gc_dmp_directship_databend_base import DirectshipDatabendSession
from util.util import env_dev


class ForecastRollingWeeksMapping(Base):
    __tablename__ = "app_forecast_filter_fiscal_week_mapping_wi"
    __table_args__ = {"schema": "test_db" if env_dev() else "gc_dmp_fast"}

    filter_fiscal_week = Column(Integer, primary_key=True)
    fiscal_qtr_week_name = Column(String(16), comment="FY23Q1W12")
    fiscal_week_year = Column(Integer)
    forecast_version = Column(String(16))
    rolling_week = Column(Integer)
    create_time = Column(DateTime)
    update_time = Column(DateTime)

    @classmethod
    def query_rolling_weeks(
        cls, filter_fiscal_week: str, forecast_version: str, rolling_week: int = -13
    ) -> list:
        s = DirectshipDatabendSession() if env_dev() else DFADatabendSession()
        ret = []
        try:
            fiscal_weeks = (
                s.query(cls.fiscal_qtr_week_name)
                .distinct()
                .filter(
                    cls.filter_fiscal_week == filter_fiscal_week,
                    cls.forecast_version == forecast_version,
                    cls.rolling_week >= rolling_week,
                )
                .query_list()
            )
            ret = [item.get("fiscal_qtr_week_name") for item in fiscal_weeks]
            ret = sorted(ret, key=lambda x: FiscalWeek(x).fiscal_week_int)
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret
