from typing import Optional

import numpy as np
import pandas as pd

from domain.dashboard.entity.forecast_vs_actual import (
    Condition,
    ForecastVSActual,
    ForecastVSActualByWeeks,
    get_obj_attr_by_str,
)
from util.gc_dmp_base import *


class ForecastAccuracyAbstract:
    fiscal_week_year = Column(Integer, primary_key=True)
    fiscal_qtr_week_name = Column(String(16), comment="FY23Q1W12")
    forecast_version = Column(String(16))
    asof_fiscal_week_year = Column(Integer)
    data_type = Column(String(256))
    sales_org = Column(String(256))
    rtm = Column(String(256))
    sub_rtm = Column(String(256))
    lob = Column(String(256))
    sub_lob = Column(String(256))
    nand = Column(String(256))
    color = Column(String(256))
    hr_lr = Column("high_low_runner", String(32))
    prediction_cw = Column(Integer, comment="forecast_cw")
    ub = Column(Integer)
    delta = Column(Integer)
    accuracy = Column(Float)
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    
    def _get_session():
        pass

    @classmethod
    def query_forcast_and_ub(
        cls,
        data_type: Optional[str],
        weeks: list,
        condition: Condition,
    ) -> list[ForecastVSActualByWeeks]:
        s = cls._get_session()
        ret = []
        try:
            q = s.query(
                cls.fiscal_qtr_week_name.label("fiscal_week"),
                cls.forecast_version,
                cls.data_type,
                cls.rtm,
                cls.sub_rtm,
                cls.lob,
                cls.sub_lob,
                cls.nand,
                cls.color,
                cls.hr_lr.label("hr_lr"),
                cls.prediction_cw.label("forecast_cw"),
                cls.ub,
                cls.delta,
                cls.accuracy,
            ).filter(
                cls.fiscal_qtr_week_name.in_(weeks),
            )
            if data_type is not None:
                q = q.filter(cls.data_type == data_type)
            
            for field, value in condition.fields.items():
                if value is None:
                    continue
                q = q.filter(get_obj_attr_by_str(cls, field) == value)
            
            query_data: list[dict] = q.query_list()
            # 替换nan为None
            df = pd.DataFrame(query_data)
            df.replace({np.nan: None}, inplace=True)
            query_data = df.to_dict('records')
            # 第一遍循环整合week数据, 即weeks{week_key:[14个 ForecastAccuracy 对象]}
            weeks = {}
            for item in query_data:
                fiscal_week = item.get("fiscal_week")
                forecast_version = item.get("forecast_version")
                data_type = item.get("data_type")
                rtm = item.get("rtm")
                sub_rtm = item.get("sub_rtm")
                lob = item.get("lob")
                sub_lob = item.get("sub_lob")
                hr_lr = item.get("hr_lr")
                nand = item.get("nand")
                color = item.get("color")
                tier = "iPhone 16 Series" if sub_lob.startswith("iPhone 16") else "N-"
                forecast_accuracy = ForecastVSActual(
                    fiscal_week=fiscal_week,
                    data_type=data_type,
                    forecast_version=forecast_version,
                    rtm=rtm,
                    sub_rtm=sub_rtm,
                    lob=lob,
                    sub_lob=sub_lob,
                    tier=tier,
                    nand=nand,
                    color=color,
                    forecast_cw=item.get("forecast_cw"),
                    ub=item.get("ub"),
                    delta=item.get("delta"),
                    accuracy=item.get("accuracy"),
                )

                week_key = "_".join(
                    [
                        forecast_version,
                        data_type,
                        rtm,
                        sub_rtm,
                        lob,
                        sub_lob,
                        hr_lr,
                        nand,
                        color,
                    ]
                )
                if week_key not in weeks:
                    weeks[week_key] = [forecast_accuracy]
                else:
                    weeks[week_key].append(forecast_accuracy)

            # 循环上一步的weeks，返回 ForecastAccuracyByWeeks 对象列表
            for key, weeks_content in weeks.items():
                (
                    forecast_version,
                    data_type,
                    rtm,
                    sub_rtm,
                    lob,
                    sub_lob,
                    hr_lr,
                    nand,
                    color,
                ) = key.split("_")
                ret.append(
                    ForecastVSActualByWeeks(
                        data_type=data_type,
                        forecast_version=forecast_version,
                        rtm=rtm,
                        sub_rtm=sub_rtm,
                        sub_lob=sub_lob,
                        nand=nand,
                        color=color,
                        forecast_vs_actuals=weeks_content,
                    )
                )
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret
