from sqlalchemy import func

from data.databend.dashboard.forecast_actual.forecast_accuracy_abstract import (
    ForecastAccuracyAbstract,
)
from util.conf import logger
from util.gc_dmp_base import Base
from data.databend.dfa_data_databend_base import DFADatabendSession
from util.util import env_dev


class ForecastAccuracyDFA(Base, ForecastAccuracyAbstract):
    __tablename__ = "app_fast_dashboard_forecast_accuracy_dfa_wi"
    __table_args__ = {"schema": f"gc_dmp_fast{'' if env_dev() else '_secure'}"}

    def _get_session():
        return DFADatabendSession()

    @classmethod
    def get_latest_refresh_time(cls, fiscal_week: str):
        s = DFADatabendSession()
        try:
            filter_params = []
            if fiscal_week:
                filter_params.append(cls.fiscal_qtr_week_name == fiscal_week)
            query_data = s.query(func.max(cls.update_time).label("update_time")).filter(*filter_params).query_dict()
            latest_refresh_time = query_data["update_time"].strftime('%Y-%m-%d %H:%M:%S') if query_data["update_time"] else ""
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return latest_refresh_time
