import datetime

from util.conf import Base
from util.conf import Column, String, Date, Integer, TIMESTAMP, func
from util.conf import logger
from data.databend.supply_data_databend_base import SupplyDatabendSession
from data.databend.gc_dmp_directship_databend_base import DirectshipDatabendSession
from util.const import REGION_CM
from sqlalchemy import or_, and_, case
from util.util import env_dev


class PoDelinquentDi(Base):
    __tablename__ = "app_fast_dashboard_po_delinquent_di"
    # __tablename__ = "app_fast_dashboard_po_delinquent_jdsplit_di" # 仅用于测试环境
    __table_args__ = {"schema": "test_db" if env_dev() else "gc_dmp_fast"}

    fiscal_dt = Column(Date, primary_key=True)
    sales_org = Column(String(256))
    rtm = Column(String(256))
    business_type = Column(String(256))
    sold_to_id = Column(String(256))
    sold_to_name = Column(String(256))
    sold_to_name_abbre = Column(String(256))
    lob = Column(String(256))
    sub_lob = Column(String(256))
    nand = Column(String(256))
    color = Column(String(256))
    mpn_id = Column(String(128))
    two_wks_total_demand = Column(Integer)
    group1_open_po_qty = Column(Integer)
    group2_open_po_qty = Column(Integer)
    group3_open_po_qty = Column(Integer)
    group4_open_po_qty = Column(Integer)
    group5_open_po_qty = Column(Integer)
    total_open_po_qty = Column(Integer)
    cumulative_po = Column(Integer)
    publish_status = Column(Integer)
    create_time = Column(TIMESTAMP, default=datetime.datetime.now())
    update_time = Column(TIMESTAMP, default=datetime.datetime.now, onupdate=datetime.datetime.now)
    
    __PUBLISHED_STATUS = 1

    @classmethod
    def __get_session(cls):
        if env_dev():
            return DirectshipDatabendSession()
        else:
            return SupplyDatabendSession()

    @classmethod
    def query_download_po_delinquent_records(cls, fiscal_dt: dict, region: list, rtm: str):
        """明细数据下载"""
        s = cls.__get_session()
        try:
            # China mainland需要过滤Retail Partner
            filter_params = [
                cls.rtm != 'All',
                cls.business_type != 'All',
                cls.sold_to_id != 'All',
                cls.nand != 'All',
                cls.color != 'All',
                cls.mpn_id != 'All',
                cls.sales_org == 'China mainland',
                cls.rtm != 'Retail Partner',
                cls.publish_status == cls.__PUBLISHED_STATUS,
                # or_(
                #     and_(
                #         cls.sales_org == 'China mainland',
                #         cls.rtm != 'Retail Partner'
                #     ),
                #     cls.sales_org != 'China mainland'
                # )
            ]
            if fiscal_dt:
                filter_params.append(cls.fiscal_dt == fiscal_dt["max_fiscal_dt"])
            if region:
                filter_params.append(cls.sales_org.in_(region))
            if rtm:
                filter_params.append(cls.rtm == rtm)
            query_data = s.query(cls.sales_org.label("region"),
                                 cls.rtm,
                                 cls.business_type.label("sub_rtm"),
                                 cls.sold_to_id.label("cust_id"),
                                 cls.lob,
                                 cls.sub_lob,
                                 cls.nand,
                                 cls.color,
                                 cls.mpn_id.label("mpn"),
                                 cls.two_wks_total_demand.label("2wks total demand"),
                                 cls.group1_open_po_qty.label("<1 Week"),
                                 cls.group2_open_po_qty.label("1-2 Week"),
                                 cls.group3_open_po_qty.label("2-3 Week"),
                                 cls.group4_open_po_qty.label("3-4 Week"),
                                 cls.group5_open_po_qty.label(">4 Week"),
                                 cls.total_open_po_qty.label("Available PO"),
                                 cls.update_time).filter(*filter_params).query_list()
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return query_data

    @classmethod
    def query_menu_po_delinquent_records(cls, fiscal_dt: dict, region: list, rtm: str):
        s = cls.__get_session()
        try:
            filter_params = [cls.lob != 'All', cls.publish_status == cls.__PUBLISHED_STATUS]
            if fiscal_dt:
                filter_params.append(cls.fiscal_dt == fiscal_dt["max_fiscal_dt"])
            if region:
                filter_params.append(cls.sales_org.in_(region))
            if rtm:
                filter_params.append(cls.rtm == rtm)
            query_data = s.query(cls.sales_org.label("region"),
                                 cls.rtm,
                                 cls.business_type.label("sub_rtm"),
                                 cls.lob,
                                 cls.sub_lob
                                 ).filter(*filter_params).query_list()
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return query_data

    @classmethod
    def get_max_fiscal_dt(cls):
        s = cls.__get_session()
        try:
            # 20250307 根据publish_status状态去决定是否漏出数据 publish_status: 1: 已发布; 0: 未发布
            max_fiscal_dt = s.query(func.max(cls.fiscal_dt).label('max_fiscal_dt')).filter(cls.publish_status == cls.__PUBLISHED_STATUS).query_dict()
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return max_fiscal_dt

    @classmethod
    def get_latest_refresh_time(cls, fiscal_dt: dict):
        s = cls.__get_session()
        try:
            filter_params = [cls.publish_status == cls.__PUBLISHED_STATUS]
            if fiscal_dt:
                filter_params.append(cls.fiscal_dt == fiscal_dt["max_fiscal_dt"])
            query_data = s.query(func.max(cls.update_time).label("update_time")).filter(*filter_params).query_dict()
            latest_refresh_time = query_data["update_time"].strftime('%Y-%m-%d %H:%M:%S') if query_data["update_time"] else ""
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return latest_refresh_time

    @classmethod
    def query_product_view(cls, region: str, rtm: str, sub_rtm: str, lob: str, sub_lobs: list, fiscal_dt: dict) -> list:
        s = cls.__get_session()
        try:
            filter_params = [cls.sold_to_id == 'All', cls.mpn_id == 'All', cls.publish_status == cls.__PUBLISHED_STATUS]
            if region:
                filter_params.append(cls.sales_org == region)
            if rtm:
                filter_params.append(cls.rtm == rtm)
            if sub_rtm:
                filter_params.append(cls.business_type == sub_rtm)
            if lob:
                filter_params.append(cls.lob == lob)
            if sub_lobs:
                filter_params.append(cls.sub_lob.in_(sub_lobs))
            if fiscal_dt:
                filter_params.append(cls.fiscal_dt == fiscal_dt["max_fiscal_dt"])
            query_data = (s.query(
                cls.sub_lob.label("sub_lob"),
                cls.nand.label("nand"),
                cls.color.label("color"),
                cls.total_open_po_qty.label("open_po"),
                cls.two_wks_total_demand.label("2wks_total_demand"),
                cls.group1_open_po_qty.label("cond_1"),
                cls.group2_open_po_qty.label("cond_2"),
                cls.group3_open_po_qty.label("cond_3"),
                cls.group4_open_po_qty.label("cond_4"),
                cls.group5_open_po_qty.label("cond_5")
            ).filter(*filter_params).query_list())
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return query_data

    @classmethod
    def query_channel_view(cls, region: str, lob: str, sub_lobs: list, fiscal_dt: dict, rtm: str) -> list:
        s = cls.__get_session()
        try:
            # nand 和color 必须是all，因为channel view 只看到sub lob纬度，sub lob下的nand 和 color 都必须是汇总到一起的
            # rtm 必须不是all，因为rtm是一个单独展示的纬度, 也不可以是RP
            filter_params = [cls.nand == "All", cls.color == "All", cls.rtm != 'All',
                             cls.sold_to_id == 'All', cls.mpn_id == 'All',
                             cls.publish_status == cls.__PUBLISHED_STATUS]
            if region:
                filter_params.append(cls.sales_org == region)
                if region == REGION_CM:
                    filter_params.append(cls.rtm != 'Retail Partner')
            if lob:
                filter_params.append(cls.lob == lob)
            if sub_lobs:
                filter_params.append(cls.sub_lob.in_(sub_lobs))
            if fiscal_dt:
                filter_params.append(cls.fiscal_dt == fiscal_dt["max_fiscal_dt"])
            if rtm:
                filter_params.append(cls.rtm == rtm)
            query_data = (s.query(
                cls.sales_org.label("region"),
                cls.rtm.label("rtm"),
                cls.business_type.label("sub_rtm"),
                cls.sub_lob.label("sub_lob"),
                case([(cls.cumulative_po.is_(None), 0)], else_=cls.cumulative_po).label('cum_po'),
                cls.total_open_po_qty.label("open_po"),
                cls.two_wks_total_demand.label("2wks_total_demand"),
                cls.group1_open_po_qty.label("cond_1"),
                cls.group2_open_po_qty.label("cond_2"),
                cls.group3_open_po_qty.label("cond_3"),
                cls.group4_open_po_qty.label("cond_4"),
                cls.group5_open_po_qty.label("cond_5")
            ).filter(*filter_params).query_list())
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return query_data

    @classmethod
    def query_sold_to_view(cls, region: str, lob: str, sub_lobs: list, fiscal_dt: dict, rtm: str) -> list:
        s = cls.__get_session()
        try:
            # nand 和color 必须是all，因为sold to view 只看到sold to纬度，sold to下的nand 和 color 都必须是汇总到一起的
            # 中国大陆的RTM不可以是RP, 因为是分rtm展示的
            filter_params = [cls.nand == "All", cls.color == "All", cls.mpn_id == 'All',
                             cls.publish_status == cls.__PUBLISHED_STATUS]
            if region:
                filter_params.append(cls.sales_org == region)
                if region == REGION_CM:
                    filter_params.append(cls.rtm != 'Retail Partner')
            if lob:
                filter_params.append(cls.lob == lob)
            if sub_lobs:
                filter_params.append(cls.sub_lob.in_(sub_lobs))
            if fiscal_dt:
                filter_params.append(cls.fiscal_dt == fiscal_dt["max_fiscal_dt"])
            if rtm:
                filter_params.append(cls.rtm == rtm)

            query_data = (s.query(
                cls.lob,
                cls.sales_org.label('region'),
                cls.rtm.label("rtm"),
                cls.business_type.label("sub_rtm"),
                case([(cls.sold_to_id.is_(None), 'All')], else_=cls.sold_to_id).label('sold_to_id'),
                case([(cls.sold_to_name_abbre.is_(None), 'All')], else_=cls.sold_to_name_abbre).label('sold_to_name'), # 使用缩写名称替换长名称
                # case([(cls.sold_to_name_abbre.is_(None), 'All')], else_=cls.sold_to_name_abbre).label('sold_to_name_abbre'),
                func.sum(case([(cls.cumulative_po.is_(None), 0)], else_=cls.cumulative_po)).label('cum_po'),
                func.sum(cls.two_wks_total_demand).label("2wks_total_demand"),
                func.sum(cls.group1_open_po_qty).label("cond_1"),
                func.sum(cls.group2_open_po_qty).label("cond_2"),
                func.sum(cls.group3_open_po_qty).label("cond_3"),
                func.sum(cls.group4_open_po_qty).label("cond_4"),
                func.sum(cls.group5_open_po_qty).label("cond_5"),
                func.sum(cls.total_open_po_qty).label("open_po"),
            ).filter(*filter_params).group_by(cls.sales_org, cls.rtm, cls.business_type, cls.lob, cls.sold_to_name_abbre, cls.sold_to_id).query_list())
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return query_data
