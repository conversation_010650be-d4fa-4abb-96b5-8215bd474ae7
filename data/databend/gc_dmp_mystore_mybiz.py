from sqlalchemy.orm import Query
import pandas as pd

from databend_sqlalchemy import connector
from util.conf import *

databend_connect_info = {}
gc_dmp_mystore_mybiz_config = conf['gc_dmp_mystore_mybiz']
if os.environ.get('DB_ENV') == 'aws':
    gc_dmp_data_sec = get_secret_from_aws(gc_dmp_mystore_mybiz_config['secret_name'],
                                          gc_dmp_mystore_mybiz_config['secret_region'])
else:
    gc_dmp_data_sec = get_secret_from_apple(gc_dmp_mystore_mybiz_config['secret_name'],
                                            gc_dmp_mystore_mybiz_config['secret_region'])


databend_connect_info['host'] = gc_dmp_mystore_mybiz_config['https_host']
databend_connect_info['database'] = gc_dmp_mystore_mybiz_config['default_db']
databend_connect_info['user'] = gc_dmp_data_sec['username']
databend_connect_info['password'] = gc_dmp_data_sec['password']


class CustomQuery(Query):
    def _get_query_str(self) -> str:
        raw_sql = self.statement.compile(compile_kwargs={"literal_binds": True}).string
        logger.debug(raw_sql)
        return raw_sql

    def query_dataframe(self) -> pd.DataFrame:
        raw_sql = self._get_query_str()
        session_info = self.session.info
        host_name = session_info.get('host')
        user_name = session_info.get('user')
        password = session_info.get('password')
        if not host_name or not password or not user_name:
            raise Exception('databend connection info error. ')

        df = pd.DataFrame()
        # logger.debug(raw_sql)
        conn = connector.connect(f"https://{user_name}:{password}@{host_name}?secure=true")
        try:
            df = pd.read_sql(raw_sql, conn)
        except Exception as e:
            raise e
        finally:
            conn.close()
        return df

    def query_dict(self) -> dict:
        df = self.query_dataframe()
        df = df.astype(object).where(pd.notnull(df), None)
        query_data = df.to_dict('records')
        ret = {}
        if len(query_data) > 0:
            ret = query_data[0]
        return ret

    def query_list(self) -> list:
        df = self.query_dataframe()
        df = df.astype(object).where(pd.notnull(df), None)
        return df.to_dict('records')


user = databend_connect_info['user']
password = databend_connect_info['password']
default_db = databend_connect_info['database']
databend_db_host = databend_connect_info['host']
mystore_mybiz_engine = create_engine(f"databend://{user}:{password}@{databend_db_host}/{default_db}?secure=true")

MyStoreMyBizDatabendSession = sessionmaker(bind=mystore_mybiz_engine, query_cls=CustomQuery, info=databend_connect_info)
