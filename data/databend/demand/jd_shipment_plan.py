import pandas as pd

from data.databend.supply_data_databend_base import SupplyDatabendSession
from util.gc_dmp_base import *


class JDShipmentPlan(Base):
    '''
    京东真实soldto的shipment_plan和eoh
    '''
    __tablename__ = "app_fast_jd_shipment_plan_wi"
    __table_args__ = {"schema": "gc_dmp_fast"}

    snapshot_ts = Column(DateTime, comment='快照时间')
    fiscal_week_year = Column(Integer, primary_key=True)
    sold_to_id = Column(String(256))
    sold_to_name = Column(String(256))
    rtm = Column(String(256))
    rtml4 = Column(String(256))
    lob = Column(String(256))
    sub_lob = Column(String(256))
    mpn = Column(String(256))
    cw_shipment_plan = Column(Float) # 下面4个字段原始表中的类型是varchar
    shipment_plan_cw1 = Column(Float)
    shipment_plan_cw2 = Column(Float)
    shipment_plan_cw3 = Column(Float)
    eoh_lw = Column(Integer)
    create_time = Column(DateTime, comment='创建时间')
    update_time = Column(DateTime, comment='更新时间')

    @classmethod
    def query_by_week(cls, fiscal_week_year: int, lob: str = 'iPhone') -> pd.DataFrame:
        s = SupplyDatabendSession()
        try:
            result = s.query(
                cls.sold_to_id,
                cls.sold_to_name,
                cls.lob,
                cls.sub_lob,
                cls.mpn,
                cls.cw_shipment_plan.label("shipment_plan_cw"),
                cls.shipment_plan_cw1,
                cls.shipment_plan_cw2,
                cls.shipment_plan_cw3,
                cls.eoh_lw,
            ).filter(cls.fiscal_week_year == fiscal_week_year) \
                .filter(cls.lob == lob) \
                .query_dataframe()
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return result
