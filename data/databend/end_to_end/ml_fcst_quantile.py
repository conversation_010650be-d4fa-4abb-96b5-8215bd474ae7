from typing import Optional
import pandas as pd
from sqlalchemy import Column, String, Integer, DECIMAL, desc, DateTime, Float

from data.databend.gc_dmp_channel_compliance_databend import ComplianceDatabendSession, db_engine
from data.databend.supply_data_databend_base import SupplyDatabendSession, gc_dmp_fast_databend_engine
from util.conf import Base, logger
from util.util import env_dev


class MLFcstQuantile(Base):
    __tablename__ = 'app_fast_ml_forecast_quantile_wi'
    __table_args__ = {'schema': 'gc_dmp_fast'}
    session = SupplyDatabendSession()
    engine = gc_dmp_fast_databend_engine
    if env_dev():
        __tablename__ = 'app_fast_ml_forecast_quantile_wi_test'
        __table_args__ = {'schema': 'test_db'}
        session = ComplianceDatabendSession()
        engine = db_engine

    rtm = Column(String(16), comment='', primary_key=True)
    sub_rtm = Column(String(16), comment='')
    hq_id = Column(String(16), comment='')
    hq_name = Column(String(128), comment='')
    sold_to_id = Column(String(32), comment='')
    sold_to_name = Column(String(128), comment='')
    fiscal_week = Column(Integer, comment='')
    fiscal_week_name = Column(String(16), comment='')
    lob = Column(String(16), comment='')
    sub_lob = Column(String(32), comment='')
    mpn = Column(String(32), comment='')
    nand = Column(String(32), comment='')
    color = Column(String(32), comment='')
    quantile_level = Column(Float, comment='')
    ml_forecast_cw = Column(Integer, comment='')
    quantile_min_cw = Column(Integer, comment='')
    quantile_max_cw = Column(Integer, comment='')
    ml_forecast_cw1 = Column(Integer, comment='')
    quantile_min_cw1 = Column(Integer, comment='')
    quantile_max_cw1 = Column(Integer, comment='')
    ml_forecast_cw2 = Column(Integer, comment='')
    quantile_min_cw2 = Column(Integer, comment='')
    quantile_max_cw2 = Column(Integer, comment='')
    ml_forecast_cw3 = Column(Integer, comment='')
    quantile_min_cw3 = Column(Integer, comment='')
    quantile_max_cw3 = Column(Integer, comment='')
    ml_forecast_cw4 = Column(Integer, comment='')
    quantile_min_cw4 = Column(Integer, comment='')
    quantile_max_cw4 = Column(Integer, comment='')
    ml_forecast_cw5 = Column(Integer, comment='')
    quantile_min_cw5 = Column(Integer, comment='')
    quantile_max_cw5 = Column(Integer, comment='')
    ml_forecast_cw6 = Column(Integer, comment='')
    quantile_min_cw6 = Column(Integer, comment='')
    quantile_max_cw6 = Column(Integer, comment='')
    create_time = Column(DateTime, comment='create time')
    update_time = Column(DateTime, comment='update time')

    @classmethod
    def get_download_data(cls, fiscal_week_name: str, lob: str = "iPhone", rtm: str = None, 
                          soldto_ids: Optional[list[str]] = None) -> pd.DataFrame:
        s = cls.session
        try:
            q = s.query(cls.fiscal_week_name.label("Fiscal Week"),
                        cls.rtm.label("RTM"),
                        cls.sub_rtm.label("Sub-RTM"),
                        cls.sold_to_id.label("sold-to id"),
                        cls.sold_to_name.label("sold-to name"),
                        cls.lob.label("LOB"),
                        cls.sub_lob.label("Sub-LOB"),
                        cls.mpn.label("MPN"),
                        cls.quantile_level.label("quantile_level"),
                        cls.ml_forecast_cw.label("ml forecast_cw"),
                        cls.quantile_min_cw.label("quantile_min_cw"),
                        cls.quantile_max_cw.label("quantile_max_cw"),
                        cls.ml_forecast_cw1.label("ml forecast_cw1"),
                        cls.quantile_min_cw1.label("quantile_min_cw1"),
                        cls.quantile_max_cw1.label("quantile_max_cw1"),
                        cls.ml_forecast_cw2.label("ml forecast_cw2"),
                        cls.quantile_min_cw2.label("quantile_min_cw2"),
                        cls.quantile_max_cw2.label("quantile_max_cw2"),
                        cls.ml_forecast_cw3.label("ml forecast_cw3"),
                        cls.quantile_min_cw3.label("quantile_min_cw3"),
                        cls.quantile_max_cw3.label("quantile_max_cw3"),
                        cls.ml_forecast_cw4.label("ml forecast_cw4"),
                        cls.quantile_min_cw4.label("quantile_min_cw4"),
                        cls.quantile_max_cw4.label("quantile_max_cw4"),
                        cls.ml_forecast_cw5.label("ml forecast_cw5"),
                        cls.quantile_min_cw5.label("quantile_min_cw5"),
                        cls.quantile_max_cw5.label("quantile_max_cw5"),
                        cls.ml_forecast_cw6.label("ml forecast_cw6"),
                        cls.quantile_min_cw6.label("quantile_min_cw6"),
                        cls.quantile_max_cw6.label("quantile_max_cw6"),
                        cls.update_time.label("update time")
                        ).filter(cls.fiscal_week_name == fiscal_week_name, cls.lob == lob)
            if rtm:
                q = q.filter(cls.rtm == rtm)
            if soldto_ids is not None:
                q = q.filter(cls.sold_to_id.in_(soldto_ids))
            ret = pd.read_sql_query(q.statement, cls.engine)
            return ret
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()

    @classmethod
    def get_data_by_fiscal_week(cls, fiscal_week_name: str, lob: str = "iPhone", rtm: str = None) -> pd.DataFrame:
        s = cls.session
        try:
            q = s.query(cls.rtm.label("RTM"),
                        cls.sold_to_id.label("sold-to id"),
                        cls.mpn.label("MPN")
                        ).filter(cls.fiscal_week_name == fiscal_week_name, cls.lob == lob)
            if rtm:
                q = q.filter(cls.rtm == rtm)
            ret = pd.read_sql_query(q.statement, cls.engine)
            return ret
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()