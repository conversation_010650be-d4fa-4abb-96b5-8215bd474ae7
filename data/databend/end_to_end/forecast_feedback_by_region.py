from typing import Optional
import pandas as pd
from sqlalchemy import Column, Integer, String, Float, PrimaryKeyConstraint, DateTime

from data.databend.gc_dmp_channel_compliance_databend import ComplianceDatabendSession, db_engine
from data.databend.supply_data_databend_base import SupplyDatabendSession, gc_dmp_fast_databend_engine, \
    SupplyDatabendSessionV2
from util.conf import logger, Base
from util.util import env_dev


class ForecastFeedbackRegion(Base):
    __tablename__ = f"app_fast_forecast_feedback_by_region_wi"
    __table_args__ = (
        PrimaryKeyConstraint('fiscal_week', 'mpn'),  # 定义组合主键
        {"schema": "test_db" if env_dev() else "gc_dmp_fast"}
    )

    id = Column(Integer)
    fiscal_week = Column(String(32))
    region = Column(String(32))
    lob = Column(String(32))
    sub_lob = Column(String(32))
    nand = Column(String(32))
    color = Column(String(32))
    mpn = Column(String(32))
    hr_lr = Column(String(32))
    shipment_plan_cw = Column(Float)
    shipment_plan_cw1 = Column(Float)
    shipment_plan_cw2 = Column(Float)
    ub_eoh = Column(Float)
    forecast_cw_ml = Column(Integer)
    forecast_cw1_ml = Column(Integer)
    forecast_cw2_dfa = Column(Integer)
    forecast_cw3_dfa = Column(Integer)
    forecast_cw4_dfa = Column(Integer)
    forecast_cw5_dfa = Column(Integer)
    forecast_cw6_dfa = Column(Integer)
    forecast_cw7_dfa = Column(Integer)
    forecast_cw8_dfa = Column(Integer)
    df_cw1 = Column(Float)
    df_cw2 = Column(Float)
    df_cw1_adjusted = Column(Float)
    df_cw2_adjusted = Column(Float)
    forecast_feedback_cw = Column(Integer)
    forecast_feedback_cw1 = Column(Integer)
    forecast_feedback_cw2 = Column(Integer)
    forecast_feedback_cw3 = Column(Integer)
    forecast_feedback_cw4 = Column(Integer)
    forecast_feedback_cw5 = Column(Integer)
    forecast_feedback_cw6 = Column(Integer)
    forecast_feedback_cw7 = Column(Integer)
    forecast_feedback_cw8 = Column(Integer)
    forecast_feedback_cw9 = Column(Integer)
    forecast_feedback_cw10 = Column(Integer)
    forecast_feedback_cw11 = Column(Integer)
    forecast_feedback_cw12 = Column(Integer)
    trial_demand_cw1 = Column(Float)
    trial_demand_cw2 = Column(Float)
    trial_po_needed_cw1 = Column(Float)
    trial_po_needed_cw2 = Column(Float)
    publish_time = Column(DateTime)
    publish_status = Column(Integer)

    @classmethod
    def get_menu_data(cls, fiscal_week_name: str, lob: str, publish_status: Optional[int] = None) -> pd.DataFrame:
        s = ComplianceDatabendSession() if env_dev() else SupplyDatabendSession()
        engine = db_engine if env_dev() else gc_dmp_fast_databend_engine
        try:
            filter_params = [cls.fiscal_week == fiscal_week_name, cls.lob == lob]
            if publish_status is not None:
                filter_params.append(cls.publish_status == publish_status)
            q = (s.query(cls.sub_lob, cls.nand, cls.color)
                 .filter(*filter_params))
            ret = pd.read_sql_query(q.statement, engine)
            return ret
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()

    @classmethod
    def get_list_data(cls, fiscal_week_name: str, lob: str = 'iPhone', sub_lobs: list[str] = None, nand: list[str] = None, color: list[str] = None) -> pd.DataFrame:
        s = ComplianceDatabendSession() if env_dev() else SupplyDatabendSession()
        engine = db_engine if env_dev() else gc_dmp_fast_databend_engine
        try:
            q = s.query(cls).filter(cls.fiscal_week == fiscal_week_name).filter(cls.lob == lob)
            if sub_lobs:
                q = q.filter(cls.sub_lob.in_(sub_lobs))
            if nand:
                q = q.filter(cls.nand.in_(nand))
            if color:
                q = q.filter(cls.color.in_(color))
            ret = pd.read_sql_query(q.statement, engine)
            return ret
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()

    @classmethod
    def get_default_woi(cls, fiscal_week_name: str, lob: str) -> pd.DataFrame:
        s = ComplianceDatabendSession() if env_dev() else SupplyDatabendSession()
        engine = db_engine if env_dev() else gc_dmp_fast_databend_engine
        try:
            filter_params = [cls.fiscal_week == fiscal_week_name, cls.lob == lob]
            q = (s.query(cls)
                 .filter(*filter_params))
            ret = pd.read_sql_query(q.statement, engine)
            return ret
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()

    @classmethod
    def find_by_fiscal_week_name(cls, fiscal_week_name: str) -> list[dict]:
        s = ComplianceDatabendSession() if env_dev() else SupplyDatabendSessionV2()
        try:
            query_result = s.query(cls).filter(cls.fiscal_week == fiscal_week_name).all()
            if not query_result:
                return []
            return [record.__dict__ for record in query_result]
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
