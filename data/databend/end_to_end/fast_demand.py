import pandas as pd
from sqlalchemy import Column, String, Integer, DECIMAL, DateTime

from data.databend.gc_dmp_channel_compliance_databend import ComplianceDatabendSession, db_engine
from data.databend.gc_dmp_mystore_mybiz import MyStoreMyBizDatabendSession, mystore_mybiz_engine
from util.conf import Base, logger
from util.util import env_dev


class ForecastDemand(Base):
    __tablename__ = f"app_mybiz_fast_forecast_demand_wi{'_test' if env_dev() else ''}"
    __table_args__ = {"schema": "test_db" if env_dev() else "gc_dmp_mystore_mybiz"}

    fiscal_week_year = Column(Integer, primary_key=True)
    fiscal_qtr_week_name = Column(String(32))
    reseller_id = Column(String(32))
    reseller_name = Column(String(64))
    reseller_tier = Column(String(32))
    sold_to_id = Column(String(32))
    sold_to_name = Column(String(64))
    rtm = Column(String(32))
    sub_rtm = Column(String(32))
    lob = Column(String(32))
    sub_lob = Column(String(32))
    mpn_id = Column(String(32))
    mpn_type = Column(String(32))
    mpn_desc = Column(String(64))
    forecast_cw = Column(DECIMAL(20, 2))
    forecast_cw1 = Column(DECIMAL(20, 2))
    forecast_cw2 = Column(DECIMAL(20, 2))
    forecast_cw3 = Column(DECIMAL(20, 2))
    forecast_cw4 = Column(DECIMAL(20, 2))
    forecast_cw5 = Column(DECIMAL(20, 2))
    forecast_cw6 = Column(DECIMAL(20, 2))
    forecast_cw7 = Column(DECIMAL(20, 2))
    forecast_cw8 = Column(DECIMAL(20, 2))
    forecast_cw9 = Column(DECIMAL(20, 2))
    forecast_cw10 = Column(DECIMAL(20, 2))
    forecast_cw11 = Column(DECIMAL(20, 2))
    forecast_cw12 = Column(DECIMAL(20, 2))
    ub_eoh_lw = Column(DECIMAL(20, 2))
    final_demand_cw1 = Column(DECIMAL(20, 2))
    final_demand_cw2 = Column(DECIMAL(20, 2))
    available_po_cw1 = Column(DECIMAL(20, 2))
    available_po_cw2 = Column(DECIMAL(20, 2))
    po_needed_cw1 = Column(DECIMAL(20, 2))
    po_needed_cw2 = Column(DECIMAL(20, 2))
    shipment_plan_cw = Column(DECIMAL(20, 2))
    shipment_plan_cw1 = Column(DECIMAL(20, 2))
    shipment_plan_cw2 = Column(DECIMAL(20, 2))
    open_backlog_over_published_sp = Column(DECIMAL(20, 2))
    twos = Column(DECIMAL(20, 2))
    twos_cw1 = Column(DECIMAL(20, 2))
    twos_cw2 = Column(DECIMAL(20, 2))
    forecast_min_cw = Column(DECIMAL(20, 2))
    forecast_max_cw = Column(DECIMAL(20, 2))
    forecast_min_cw1 = Column(DECIMAL(20, 2))
    forecast_max_cw1 = Column(DECIMAL(20, 2))
    forecast_min_cw2 = Column(DECIMAL(20, 2))
    forecast_max_cw2 = Column(DECIMAL(20, 2))
    forecast_min_cw3 = Column(DECIMAL(20, 2))
    forecast_max_cw3 = Column(DECIMAL(20, 2))
    forecast_min_cw4 = Column(DECIMAL(20, 2))
    forecast_max_cw4 = Column(DECIMAL(20, 2))
    forecast_min_cw5 = Column(DECIMAL(20, 2))
    forecast_max_cw5 = Column(DECIMAL(20, 2))
    forecast_min_cw6 = Column(DECIMAL(20, 2))
    forecast_max_cw6 = Column(DECIMAL(20, 2))
    forecast_min_cw7 = Column(DECIMAL(20, 2))
    forecast_max_cw7 = Column(DECIMAL(20, 2))
    forecast_min_cw8 = Column(DECIMAL(20, 2))
    forecast_max_cw8 = Column(DECIMAL(20, 2))
    forecast_min_cw9 = Column(DECIMAL(20, 2))
    forecast_max_cw9 = Column(DECIMAL(20, 2))
    forecast_min_cw10 = Column(DECIMAL(20, 2))
    forecast_max_cw10 = Column(DECIMAL(20, 2))
    forecast_min_cw11 = Column(DECIMAL(20, 2))
    forecast_max_cw11 = Column(DECIMAL(20, 2))
    forecast_min_cw12 = Column(DECIMAL(20, 2))
    forecast_max_cw12 = Column(DECIMAL(20, 2))
    created_ts = Column(DateTime)
    updated_ts = Column(DateTime)

    @classmethod
    def query_by_fiscal_qtr_week_name(cls, fiscal_qtr_week_name: str, lob: str = 'iPhone') -> pd.DataFrame:
        s = ComplianceDatabendSession() if env_dev() else MyStoreMyBizDatabendSession()
        engine = db_engine if env_dev() else mystore_mybiz_engine
        try:
            q = (s.query(cls).filter(cls.fiscal_qtr_week_name == fiscal_qtr_week_name)
                 .filter(cls.lob == lob).filter(cls.mpn_id != 'All'))
            ret = pd.read_sql_query(q.statement, engine)
            return ret
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()

    @classmethod
    def get_download_data(cls, fiscal_qtr_week_name: str, lob: str = 'iPhone', rtm: str = None) -> pd.DataFrame:
        s = ComplianceDatabendSession() if env_dev() else MyStoreMyBizDatabendSession()
        engine = db_engine if env_dev() else mystore_mybiz_engine
        try:
            q = (s.query(cls.fiscal_qtr_week_name.label("Fiscal Week"),
                         cls.rtm.label("RTM"),
                         cls.sub_rtm.label("Sub-RTM"),
                         cls.sold_to_id.label("sold-to id"),
                         cls.sold_to_name.label("sold-to name"),
                         cls.lob.label("LOB"),
                         cls.sub_lob.label("Sub-LOB"),
                         cls.mpn_id.label("MPN"),
                         cls.forecast_cw.label("ml forecast_cw"),
                         cls.forecast_min_cw.label("quantile_min_cw"),
                         cls.forecast_max_cw.label("quantile_max_cw"),
                         cls.forecast_cw1.label("ml forecast_cw1"),
                         cls.forecast_min_cw1.label("quantile_min_cw1"),
                         cls.forecast_max_cw1.label("quantile_max_cw1"),
                         cls.forecast_cw2.label("ml forecast_cw2"),
                         cls.forecast_min_cw2.label("quantile_min_cw2"),
                         cls.forecast_max_cw2.label("quantile_max_cw2"),
                         cls.forecast_cw3.label("ml forecast_cw3"),
                         cls.forecast_min_cw3.label("quantile_min_cw3"),
                         cls.forecast_max_cw3.label("quantile_max_cw3"),
                         cls.forecast_cw4.label("ml forecast_cw4"),
                         cls.forecast_min_cw4.label("quantile_min_cw4"),
                         cls.forecast_max_cw4.label("quantile_max_cw4"),
                         cls.forecast_cw5.label("forecast_cw5"),
                         cls.forecast_cw6.label("forecast_cw6"),
                         cls.forecast_cw7.label("forecast_cw7"),
                         cls.forecast_cw8.label("forecast_cw8"),
                         cls.forecast_cw9.label("forecast_cw9"),
                         cls.forecast_cw10.label("forecast_cw10"),
                         cls.forecast_cw11.label("forecast_cw11"),
                         cls.forecast_cw12.label("forecast_cw12"))
                 .filter(cls.fiscal_qtr_week_name == fiscal_qtr_week_name)
                 .filter(cls.lob == lob)
                 .filter(cls.mpn_id != 'All'))
            if rtm:
                q = q.filter(cls.rtm == rtm)
            ret = pd.read_sql_query(q.statement, engine)
            return ret
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
