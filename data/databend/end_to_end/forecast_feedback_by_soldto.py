from typing import Optional
import pandas as pd
from sqlalchemy import Column, Integer, String, Float, DateTime, PrimaryKeyConstraint

from data.databend.gc_dmp_channel_compliance_databend import ComplianceDatabendSession, db_engine
from data.databend.gc_dmp_mystore_mybiz import MyStoreMyBizDatabendSession, mystore_mybiz_engine
from util.conf import logger, Base
from util.util import env_dev


class ForecastFeedbackSoldto(Base):
    __tablename__ = f"app_mybiz_fast_forecast_feedback_wi{'_test' if env_dev() else ''}"
    __table_args__ = (
        PrimaryKeyConstraint('fiscal_qtr_week_name', 'sold_to_id', 'mpn_id'),  # 定义组合主键
        {"schema": "test_db" if env_dev() else "gc_dmp_mystore_mybiz"}
    )

    id = Column(Integer)
    fiscal_week_year = Column(Integer)
    fiscal_qtr_week_name = Column(String(32))
    reseller_id = Column(String(32))
    reseller_name = Column(String(256))
    reseller_tier = Column(String(32))
    sold_to_id = Column(String(32))
    sold_to_name = Column(String(256))
    rtm = Column(String(32))
    sub_rtm = Column(String(32))
    lob = Column(String(32))
    sub_lob = Column(String(32))
    mpn_id = Column(String(32))
    nand = Column(String(32))
    color = Column(String(32))
    mpn_type = Column(String(32))
    mpn_desc = Column(String(256))
    mpn_order = Column(Integer)
    forecast_cw = Column(Integer)
    forecast_cw1 = Column(Integer)
    forecast_cw2 = Column(Integer)
    forecast_cw3 = Column(Integer)
    forecast_cw4 = Column(Integer)
    forecast_cw5 = Column(Integer)
    forecast_cw6 = Column(Integer)
    forecast_cw7 = Column(Integer)
    forecast_cw8 = Column(Integer)
    forecast_cw9 = Column(Integer)
    forecast_cw10 = Column(Integer)
    forecast_cw11 = Column(Integer)
    forecast_cw12 = Column(Integer)
    final_demand_cw1 = Column(Float)
    final_demand_cw2 = Column(Float)
    po_needed_cw1 = Column(Float)
    po_needed_cw2 = Column(Float)
    ub_eoh_lw = Column(Integer)
    shipment_plan_cw = Column(Integer)
    shipment_plan_cw1 = Column(Integer)
    shipment_plan_cw2 = Column(Integer)
    forecast_feedback_cw = Column(Integer)
    forecast_feedback_cw1 = Column(Integer)
    forecast_feedback_cw2 = Column(Integer)
    forecast_feedback_cw3 = Column(Integer)
    forecast_feedback_cw4 = Column(Integer)
    forecast_feedback_cw5 = Column(Integer)
    forecast_feedback_cw6 = Column(Integer)
    forecast_feedback_cw7 = Column(Integer)
    forecast_feedback_cw8 = Column(Integer)
    forecast_feedback_cw9 = Column(Integer)
    forecast_feedback_cw10 = Column(Integer)
    forecast_feedback_cw11 = Column(Integer)
    forecast_feedback_cw12 = Column(Integer)
    trial_demand_cw1 = Column(Float)
    trial_demand_cw2 = Column(Float)
    trial_po_needed_cw1 = Column(Float)
    trial_po_needed_cw2 = Column(Float)
    review_status = Column(String(32))
    open_backlog_over_published_sp = Column(Integer)
    publish_time = Column(DateTime)
    publish_status = Column(Integer)
    updated_ts = Column(DateTime, comment='updated_ts')
    created_ts = Column(DateTime, comment='created_ts')

    @classmethod
    def get_list_data(cls, fiscal_week_name: str, lob: str,
                      sub_lobs: list[str] = None, nand: list[str] = None, color: list[str] = None, rtm: str = None,
                      reseller_ids: Optional[list[str]] = None, publish_status: Optional[int] = None) -> pd.DataFrame:
        s = ComplianceDatabendSession() if env_dev() else MyStoreMyBizDatabendSession()
        engine = db_engine if env_dev() else mystore_mybiz_engine
        try:
            q = (s.query(cls).filter(cls.fiscal_qtr_week_name == fiscal_week_name)
                 .filter(cls.lob == lob))
            if rtm:
                q = q.filter(cls.rtm == rtm)
            if sub_lobs:
                q = q.filter(cls.sub_lob.in_(sub_lobs))
            if nand:
                q = q.filter(cls.nand.in_(nand))
            if color:
                q = q.filter(cls.color.in_(color))
            if reseller_ids is not None:
                q = q.filter(cls.reseller_id.in_(reseller_ids))
            if publish_status is not None:
                q = q.filter(cls.publish_status == publish_status)
            ret = pd.read_sql_query(q.statement, engine)
            return ret
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()

    @classmethod
    def query_by_fiscal_qtr_week_name(cls, fiscal_qtr_week_name: str, sublob: str='', lob: str = 'iPhone') -> pd.DataFrame:
        s = ComplianceDatabendSession() if env_dev() else MyStoreMyBizDatabendSession()
        engine = db_engine if env_dev() else mystore_mybiz_engine
        try:
            q = s.query(cls).filter(cls.fiscal_qtr_week_name == fiscal_qtr_week_name).filter(cls.lob == lob)
            if sublob:
                q = q.filter(cls.sub_lob == sublob)
            ret = pd.read_sql_query(q.statement, engine)
            return ret
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()

    @classmethod
    def find_by_fiscal_week_name(cls, fiscal_week_name: str) -> list[dict]:
        s = ComplianceDatabendSession() if env_dev() else MyStoreMyBizDatabendSession()
        try:
            query_result = s.query(cls).filter(cls.fiscal_qtr_week_name == fiscal_week_name).all()
            if not query_result:
                return []
            return [record.__dict__ for record in query_result]
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
