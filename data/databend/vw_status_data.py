from sqlalchemy import tuple_

from data.databend.supply_data_databend_base import SupplyDatabendSession
from domain.supply.entity import ConfirmFlow
from util.conf import *
from util.util import env_dev


# 状态确认表
class CpfVwStatusDatabend(Base):
    __tablename__ = 'app_fast_phoebe_cpf_vw_status_wi'
    __table_args__ = {'schema': 'gc_dmp_fast'}

    fiscal_week = Column(String(256), comment='', primary_key=True)
    sales_org = Column(String(256), comment='')
    ops_line_desc = Column(String(256), comment='')
    updated_ts = Column(DateTime, comment='updated_ts')
    created_ts = Column(DateTime, comment='created_ts')

    @classmethod
    def get_vw_status_by_week(cls, fiscal_week) -> list[ConfirmFlow]:
        s = SupplyDatabendSession()
        try:
            res = []
            result = (s.query(
                CpfVwStatusDatabend.fiscal_week,
                CpfVwStatusDatabend.sales_org,
                CpfVwStatusDatabend.ops_line_desc,
                func.max(CpfVwStatusDatabend.updated_ts).label('updated_ts')
            ).filter(CpfVwStatusDatabend.fiscal_week == fiscal_week)
                    .group_by(
                CpfVwStatusDatabend.fiscal_week,
                CpfVwStatusDatabend.sales_org,
                CpfVwStatusDatabend.ops_line_desc
            ).query_list())

            if len(result) > 0:
                for item in result:
                    cf = ConfirmFlow(item["fiscal_week"], item["sales_org"], item["ops_line_desc"], item["updated_ts"])
                    res.append(cf)

        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return res


