from typing import Optional

from sqlalchemy import desc, DECIMAL

from data.databend.gc_dmp_directship_databend_base import DirectshipDatabendSession
from data.databend.supply_data_databend_base import SupplyDatabendSession
from util.gc_dmp_base import *
from util.util import env_dev, take_time


class GcRoDsEsrAccyData(Base):
    __tablename__ = "gc_ro_ds_esr_accy_data"
    __table_args__ = {"schema": "gc_dmp_data"}

    snapshot_dt = Column(Date, primary_key=True)
    snapshot_ts = Column(DateTime)
    sub_region_cd = Column(String(256))
    hq_segment_cd = Column(String(256))
    alt_hier_level_3_desc = Column(String(256))
    lob_id = Column(String(256))
    hq_apple_id = Column(Integer)
    hq_apple_name = Column(String(256))
    cust_id = Column(String(256))
    cust_name = Column(String(256))
    ops_prod_class_desc = Column(String(256))
    project_short_desc = Column(String(256))
    prod_sub_family_desc = Column(String(256))
    type_short_desc = Column(String(256))
    lifecycle_desc = Column(String(256))
    prod_id = Column(String(256))
    prod_desc = Column(String(256))
    cw_allocation_qty = Column(DECIMAL(18, 2))
    cw_shipped_qty = Column(DECIMAL(18, 2))
    qtr_boh_qty = Column(DECIMAL(18, 2))
    qtd_billing_qty = Column(DECIMAL(18, 2))
    qtd_st_adj_qty = Column(DECIMAL(18, 2))
    eoh_qty = Column(DECIMAL(18, 2))
    tot_backlog_qty = Column(DECIMAL(18, 2))
    open_backlog_over_published_sp_gt_3w = Column(DECIMAL(18, 2))
    open_backlog_over_published_sp_gt_9w = Column(DECIMAL(18, 2))
    open_backlog_over_published_sp_gt_16w = Column(DECIMAL(18, 2))
    shippable_backlog_qty = Column(DECIMAL(18, 2))
    scheduled_backlog_qty = Column(DECIMAL(18, 2))
    remaining_backlog_qty = Column(DECIMAL(18, 2))
    st_cw_minus1 = Column(DECIMAL(18, 2))
    st_cw_minus2 = Column(DECIMAL(18, 2))
    st_cw_minus3 = Column(DECIMAL(18, 2))
    st_cw_minus4 = Column(DECIMAL(18, 2))
    st_cw_minus5 = Column(DECIMAL(18, 2))
    st_5wk_bwd_avg = Column(DECIMAL(18, 2))
    wos = Column(DECIMAL(18, 2))
    sdm_jst_cw = Column(DECIMAL(18, 2))
    sdm_jst_cw_minus_1 = Column(DECIMAL(18, 2))
    delta_sni = Column(DECIMAL(18, 2))
    lw_sni = Column(DECIMAL(18, 2))
    last_week_open_dn = Column(DECIMAL(18, 2))
    rollover = Column(DECIMAL(18, 2))
    cw_gross_billing = Column(DECIMAL(18, 2))
    dns_on_credit_hold = Column(DECIMAL(18, 2))
    dns_not_on_credit_hold = Column(DECIMAL(18, 2))
    lw_allocation = Column(DECIMAL(18, 2))
    lw_shipped = Column(DECIMAL(18, 2))
    pod_landed_qty = Column(DECIMAL(18, 2))
    billing_cw_minus1 = Column(DECIMAL(18, 2))
    billing_cw_minus2 = Column(DECIMAL(18, 2))
    billing_cw_minus3 = Column(DECIMAL(18, 2))
    billing_cw_minus4 = Column(DECIMAL(18, 2))
    billing_cw_minus5 = Column(DECIMAL(18, 2))
    billing_cw_5wk_bwd_avg = Column(DECIMAL(18, 2))
    open_delivery_qty = Column(DECIMAL(18, 2))
    cum_bob = Column(DECIMAL(18, 2))
    cw_backlog_gap = Column(DECIMAL(18, 2))
    cw_supply_gap = Column(DECIMAL(18, 2))
    shipment_plan_cw1 = Column(DECIMAL(18, 2))
    gatp_allocation = Column(DECIMAL(18, 2))
    ltd_st = Column(DECIMAL(18, 2))
    ltd_si = Column(DECIMAL(18, 2))
    rtm_new = Column(String(256))
    rtm_level_3 = Column(String(256))
    create_time = Column(DateTime, default=datetime.now, comment='create time')
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='update time')

    COLUMNS_TO_FLOAT_LIST = [
        "cw_allocation_qty", "cw_shipped_qty", "qtr_boh_qty", "qtd_billing_qty", "qtd_st_adj_qty",
        "eoh_qty", "tot_backlog_qty", "open_backlog_over_published_sp_gt_3w", "open_backlog_over_published_sp_gt_9w",
        "open_backlog_over_published_sp_gt_16w", "shippable_backlog_qty", "scheduled_backlog_qty",
        "remaining_backlog_qty", "st_cw_minus1", "st_cw_minus2", "st_cw_minus3", "st_cw_minus4", "st_cw_minus5",
        "st_5wk_bwd_avg", "wos", "sdm_jst_cw", "sdm_jst_cw_minus_1", "delta_sni", "lw_sni", "last_week_open_dn",
        "rollover", "cw_gross_billing", "dns_on_credit_hold", "dns_not_on_credit_hold", "lw_allocation", "lw_shipped",
        "pod_landed_qty", "billing_cw_minus1", "billing_cw_minus2", "billing_cw_minus3", "billing_cw_minus4",
        "billing_cw_minus5", "billing_cw_5wk_bwd_avg", "open_delivery_qty", "cum_bob", "cw_backlog_gap", "cw_supply_gap",
        "shipment_plan_cw1", "gatp_allocation", "ltd_st", "ltd_si"
    ]

    @classmethod
    def convert_columns_to_float(cls, data, columns):
        """
        将字典列表中指定列的数字字符串转换为浮点数，并将空值替换为 None。

        参数:
        - data (list[dict]): 字典列表形式的数据。
        - columns (list): 需要处理的列名称列表。

        返回:
        - list[dict]: 转换后的字典列表。
        """
        for row in data:
            for column in columns:
                if column in row:  # 检查列是否存在
                    value = row[column]
                    # 转换逻辑：如果值是数字字符串且非空，则转换为 float，否则替换为 None
                    row[column] = float(value) if value not in [None, '', 'NaN'] and value is not None else None
        return data

    @classmethod
    @take_time
    def query_records(cls, snapshot_ts: str, rtms: list[str] = None, regions: list[str] = None, lobs: str = None, **kwargs):
        s = SupplyDatabendSession()
        try:
            filter_params = [cls.snapshot_ts == snapshot_ts]
            if rtms:
                filter_params.append(cls.rtm_level_3.in_(rtms))
            if regions:
                filter_params.append(cls.alt_hier_level_3_desc.in_(regions))
            if lobs:
                filter_params.append(cls.lob_id.in_(lobs))
            res = s.query(cls).filter(*filter_params).query_list()
            res = cls.convert_columns_to_float(data=res, columns=cls.COLUMNS_TO_FLOAT_LIST)
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return res

    @classmethod
    def get_max_snapshot_ts(cls) -> Optional[str]:
        s = SupplyDatabendSession()
        try:
            query_data = s.query(func.max(cls.snapshot_ts).label("snapshot_ts")).query_dict()
            snapshot_ts = query_data["snapshot_ts"].strftime('%Y-%m-%d %H:%M:%S') if query_data["snapshot_ts"] else None
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return snapshot_ts

    @classmethod
    def get_distinct_snapshot_dt_ts_records(cls) -> Optional[str]:
        s = SupplyDatabendSession()
        try:
            records = s.query(cls.snapshot_dt, cls.snapshot_ts).distinct().order_by(desc(cls.snapshot_dt),
                                                                                    desc(cls.snapshot_ts)).query_list()
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return records

