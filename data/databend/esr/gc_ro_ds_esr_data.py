from typing import Optional

from sqlalchemy import desc, DECIMAL

from data.databend.gc_dmp_directship_databend_base import DirectshipDatabendSession
from data.databend.supply_data_databend_base import SupplyDatabendSession
from util.gc_dmp_base import *
from util.util import env_dev, take_time


class GcRoDsEsrData(Base):
    __tablename__ = "gc_ro_ds_esr_data"
    __table_args__ = {"schema": "gc_dmp_data"}

    snapshot_dt = Column(Date, primary_key=True)
    snapshot_ts = Column(DateTime)
    hq_segment_cd = Column(String(256))
    alt_hier_level_3_desc = Column(String(256))
    roc_cust_desc = Column(String(256))
    cust_id = Column(String(256))
    lob_id = Column(String(256))
    sub_lob_id = Column(String(256))
    project_cd = Column(String(256))
    type_desc = Column(String(256))
    prod_id = Column(String(256))
    lifecycle = Column(String(256))
    prod_description = Column(String(256))
    display_size = Column(String(256))
    color_desc = Column(String(256))
    wireless_desc = Column(String(256))
    color_shrt_desc = Column(String(256))
    storage_size = Column(String(256))
    qtw_shipment_plan = Column(DECIMAL(18, 2))
    qtd_gross_billing = Column(DECIMAL(18, 2))
    cw_shipment_plan = Column(DECIMAL(18, 2))
    cw_consumed_qty = Column(DECIMAL(18, 2))
    gatp_allocation = Column(DECIMAL(18, 2))
    delta_sni = Column(DECIMAL(18, 2))
    sni_cw_minus_1 = Column(DECIMAL(18, 2))
    last_week_open_dn = Column(DECIMAL(18, 2))
    rollover = Column(DECIMAL(18, 2))
    shipment_plan_cw1 = Column(DECIMAL(18, 2))
    shipment_plan_cw2 = Column(DECIMAL(18, 2))
    shipment_plan_cw3 = Column(DECIMAL(18, 2))
    cw_gross_billing = Column(DECIMAL(18, 2))
    cw_shipped_units = Column(DECIMAL(18, 2))
    shipment_attainment = Column(DECIMAL(18, 2))
    po_attainment = Column(DECIMAL(18, 2))
    cw_backlog_gap = Column(DECIMAL(18, 2))
    cw_supply_gap = Column(DECIMAL(18, 2))
    dns_on_credit_hold = Column(DECIMAL(18, 2))
    dns_not_on_credit_hold = Column(DECIMAL(18, 2))
    sni_qty = Column(DECIMAL(18, 2))
    tot_backlog = Column(DECIMAL(18, 2))
    backlog_gap_cum_cw1 = Column(DECIMAL(18, 2))
    shippable_backlog = Column(DECIMAL(18, 2))
    scheduled_backlog = Column(DECIMAL(18, 2))
    sif_actualised = Column(DECIMAL(18, 2))
    pod_landed_qty = Column(DECIMAL(18, 2))
    eoh_qty = Column(DECIMAL(18, 2))
    st_ub_qty_cw_minus1 = Column(DECIMAL(18, 2))
    st_ub_qty_cw_minus2 = Column(DECIMAL(18, 2))
    st_ub_qty_cw_minus3 = Column(DECIMAL(18, 2))
    st_ub_qty_cw_minus4 = Column(DECIMAL(18, 2))
    st_ub_qty_cw_minus5 = Column(DECIMAL(18, 2))
    st_ub_5wk_bwd_avg = Column(DECIMAL(18, 2))
    woi_before_allocatn = Column(DECIMAL(18, 2))
    wos_after_allocation = Column(DECIMAL(18, 2))
    open_backlog_over_published_sp = Column(DECIMAL(18, 2))
    open_backlog_over_published_sp_cw1 = Column(DECIMAL(18, 2))
    cum_shipment_plan_cw2 = Column(DECIMAL(18, 2))
    backlog_gap_cum_cw2 = Column(DECIMAL(18, 2))
    backlog_gap_cum_cw3 = Column(DECIMAL(18, 2))
    open_backlog_over_published_sp_cw2 = Column(DECIMAL(18, 2))
    open_backlog_over_published_sp_cw3 = Column(DECIMAL(18, 2))
    billing_cw_minus1 = Column(DECIMAL(18, 2))
    billing_cw_minus2 = Column(DECIMAL(18, 2))
    open_backlog_over_published_sp_gt_2w = Column(DECIMAL(18, 2))
    open_backlog_over_published_sp_gt_3w = Column(DECIMAL(18, 2))
    open_backlog_over_published_sp_gt_9w = Column(DECIMAL(18, 2))
    rtm_new = Column(String(256))
    rtm_level_3 = Column(String(256))
    create_time = Column(DateTime, default=datetime.now, comment='create time')
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='update time')

    COLUMNS_TO_FLOAT_LIST = [
        "qtw_shipment_plan", "qtd_gross_billing", "cw_shipment_plan", "qtd_billing_qty", "cw_consumed_qty",
        "gatp_allocation", "delta_sni", "sni_cw_minus_1", "last_week_open_dn", "rollover", "shipment_plan_cw1",
        "shipment_plan_cw2", "shipment_plan_cw3", "cw_gross_billing", "cw_shipped_units", "shipment_attainment",
        "po_attainment", "cw_backlog_gap", "cw_supply_gap", "dns_on_credit_hold", "dns_not_on_credit_hold",
        "sni_qty", "tot_backlog", "backlog_gap_cum_cw1", "shippable_backlog", "scheduled_backlog", "sif_actualised",
        "pod_landed_qty", "eoh_qty", "st_ub_qty_cw_minus1", "st_ub_qty_cw_minus2", "st_ub_qty_cw_minus3",
        "st_ub_qty_cw_minus4", "st_ub_qty_cw_minus5", "st_ub_5wk_bwd_avg", "woi_before_allocatn",
        "wos_after_allocation",
        "open_backlog_over_published_sp", "open_backlog_over_published_sp_cw1", "cum_shipment_plan_cw2",
        "backlog_gap_cum_cw2", "backlog_gap_cum_cw3", "open_backlog_over_published_sp_cw2",
        "open_backlog_over_published_sp_cw3", "billing_cw_minus1", "billing_cw_minus2",
        "open_backlog_over_published_sp_gt_2w",
        "open_backlog_over_published_sp_gt_3w", "open_backlog_over_published_sp_gt_9w"
    ]

    @classmethod
    def convert_columns_to_float(cls, data, columns):
        """
        将字典列表中指定列的数字字符串转换为浮点数，并将空值替换为 None。

        参数:
        - data (list[dict]): 字典列表形式的数据。
        - columns (list): 需要处理的列名称列表。

        返回:
        - list[dict]: 转换后的字典列表。
        """
        for row in data:
            for column in columns:
                if column in row:  # 检查列是否存在
                    value = row[column]
                    # 转换逻辑：如果值是数字字符串且非空，则转换为 float，否则替换为 None
                    row[column] = float(value) if value not in [None, '', 'NaN'] and value is not None else None
        return data

    @classmethod
    @take_time
    def query_records(cls, snapshot_ts: str, rtms: list[str] = None, regions: list[str] = None, lobs: str = None, **kwargs):
        s = SupplyDatabendSession()
        try:
            filter_params = [cls.snapshot_ts == snapshot_ts]
            if rtms:
                filter_params.append(cls.rtm_level_3.in_(rtms))
            if regions:
                filter_params.append(cls.alt_hier_level_3_desc.in_(regions))
            if lobs:
                filter_params.append(cls.lob_id.in_(lobs))
            res = s.query(cls).filter(*filter_params).query_list()
            res = cls.convert_columns_to_float(data=res, columns=cls.COLUMNS_TO_FLOAT_LIST)
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return res

    @classmethod
    def get_max_snapshot_ts(cls) -> Optional[str]:
        s = SupplyDatabendSession()
        try:
            query_data = s.query(func.max(cls.snapshot_ts).label("snapshot_ts")).query_dict()
            snapshot_ts = query_data["snapshot_ts"].strftime('%Y-%m-%d %H:%M:%S') if query_data["snapshot_ts"] else None
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return snapshot_ts

    @classmethod
    def get_distinct_snapshot_dt_ts_records(cls) -> Optional[str]:
        s = SupplyDatabendSession()
        try:
            records = s.query(cls.snapshot_dt, cls.snapshot_ts).distinct().order_by(desc(cls.snapshot_dt),
                                                                                    desc(cls.snapshot_ts)).query_list()
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return records
