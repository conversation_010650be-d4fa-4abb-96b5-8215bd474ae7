from data.databend.supply_data_databend_base import SupplyDatabendSession
from domain.datasource.entity.automatic.gbi_sync_status import GbiSyncStatus
from util.const import DateTimeFormat
from util.gc_dmp_base import *


class AppGbiTableSyncStatusDi(Base):
    __tablename__ = "app_gbi_table_sync_status_di"
    __table_args__ = {"schema": "gc_dmp_fast"}

    data_type = Column(String(256))
    db_name = Column(String(256))
    table_name = Column(String(256))
    snapshot_dt = Column(Date, primary_key=True)
    snapshot_ts = Column(DateTime)
    fiscal_qtr_week_name = Column(String(256))
    sync_time = Column(DateTime)
    create_time = Column(DateTime, default=datetime.now, comment='create time')
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='update time')

    @classmethod
    def query_sync_status_records(cls, data_type: str = None, db_name: str = None, table_name: str = None, snapshot_dt: str = None) -> list[GbiSyncStatus]:
        s = SupplyDatabendSession()
        res = []
        try:
            filter_params = []
            if data_type:
                filter_params.append(cls.data_type == data_type)
            if db_name:
                filter_params.append(cls.db_name == db_name)
            if table_name:
                filter_params.append(cls.table_name == table_name)
            if snapshot_dt:
                filter_params.append(cls.snapshot_dt == snapshot_dt)
            query_data = s.query(cls).filter(*filter_params).order_by(cls.sync_time.desc()).query_list()
            for item in query_data:
                res.append(
                    GbiSyncStatus(
                        data_type=item.get("data_type"),
                        db_name=item.get("db_name"),
                        table_name=item.get("table_name"),
                        snapshot_dt=item.get("snapshot_dt"),
                        snapshot_ts=item.get("snapshot_ts"),
                        fiscal_qtr_week_name=item.get("fiscal_qtr_week_name"),
                        sync_time=item.get("sync_time").strftime(DateTimeFormat) if item.get("sync_time") else None,
                    )
                )

        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return res

    @classmethod
    def query_last_update_data(cls, data_type: str = None, db_name: str = None, table_name: str = None) -> GbiSyncStatus:
        s = SupplyDatabendSession()
        ret = None
        res = []
        try:
            filter_params = []
            if data_type:
                filter_params.append(cls.data_type == data_type)
            if db_name:
                filter_params.append(cls.db_name == db_name)
            if table_name:
                filter_params.append(cls.table_name == table_name)
            query_data = s.query(cls).filter(*filter_params).order_by(cls.sync_time.desc()).limit(1).query_list()
            for item in query_data:
                res.append(
                    GbiSyncStatus(
                        data_type=item.get("data_type"),
                        db_name=item.get("db_name"),
                        table_name=item.get("table_name"),
                        snapshot_dt=item.get("snapshot_dt"),
                        snapshot_ts=item.get("snapshot_ts"),
                        fiscal_qtr_week_name=item.get("fiscal_qtr_week_name"),
                        sync_time=item.get("sync_time").strftime(DateTimeFormat) if item.get("sync_time") else None,
                    )
                )
            if res:
                ret = res[0]
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return ret

    @classmethod
    def query_last_ten_update_data(cls, data_type: str = None, db_name: str = None, table_name: str = None) -> list[GbiSyncStatus]:
        s = SupplyDatabendSession()
        res = []
        try:
            filter_params = []
            if data_type:
                filter_params.append(cls.data_type == data_type)
            if db_name:
                filter_params.append(cls.db_name == db_name)
            if table_name:
                filter_params.append(cls.table_name == table_name)
            query_data = s.query(cls).filter(*filter_params).order_by(cls.sync_time.desc()).limit(10).query_list()
            for item in query_data:
                res.append(
                    GbiSyncStatus(
                        data_type=item.get("data_type"),
                        db_name=item.get("db_name"),
                        table_name=item.get("table_name"),
                        snapshot_dt=item.get("snapshot_dt"),
                        snapshot_ts=item.get("snapshot_ts"),
                        fiscal_qtr_week_name=item.get("fiscal_qtr_week_name"),
                        sync_time=item.get("sync_time").strftime(DateTimeFormat) if item.get("sync_time") else None,
                    )
                )
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return res

    @classmethod
    def query_esr_data_by_time_range(cls, data_type: str = None,
                                     db_name: str = None,
                                     table_name: str = None,
                                     begin_time: str = None,
                                     end_time: str = None) -> GbiSyncStatus:
        s = SupplyDatabendSession()
        res = []
        ret = None
        try:
            filter_params = []
            if data_type:
                filter_params.append(cls.data_type == data_type)
            if db_name:
                filter_params.append(cls.db_name == db_name)
            if table_name:
                filter_params.append(cls.table_name == table_name)
            if begin_time:
                filter_params.append(cls.sync_time >= begin_time)
            if end_time:
                filter_params.append(cls.sync_time <= end_time)

            query_data = s.query(cls).filter(*filter_params).order_by(cls.sync_time).limit(1).query_list()
            for item in query_data:
                res.append(
                    GbiSyncStatus(
                        data_type=item.get("data_type"),
                        db_name=item.get("db_name"),
                        table_name=item.get("table_name"),
                        snapshot_dt=item.get("snapshot_dt"),
                        snapshot_ts=item.get("snapshot_ts"),
                        fiscal_qtr_week_name=item.get("fiscal_qtr_week_name"),
                        sync_time=item.get("sync_time").strftime(DateTimeFormat) if item.get("sync_time") else None,
                    )
                )
            if res:
                ret = res[0]
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return ret

