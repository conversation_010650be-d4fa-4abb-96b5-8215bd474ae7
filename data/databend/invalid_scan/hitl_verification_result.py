import pandas as pd
from datetime import datetime, timedelta
from sqlalchemy import and_, func
from datetime import date, datetime
from data.databend.gc_dmp_channel_compliance_databend import ComplianceDatabendSession
from util.conf import Base, Column, Integer, String, TIMESTAMP, logger
from util.util import env_dev


class HitlVerificationResult(Base):
    __tablename__ = "compliance_scan_assist_original_info"
    __table_args__ = { "schema": "gc_dmp_channel_compliance" }
    if env_dev():
        __tablename__ = "compliance_scan_assist_original_info_test"
        __table_args__ = { "schema": "test_db" }
    # __table_args__ = {"schema": "gc_dmp_channel_compliance" if env_dev() else "gc_dmp_channel_compliance"}

    id = Column(Integer, primary_key=True, autoincrement=True, nullable=False, comment='ID')
    final_result = Column(String)
    ml_percentage = Column(Integer)
    rtm = Column(String(16))
    sub_rtm = Column(String(16))
    disti_id = Column(String(16))
    disti_name = Column(String(64))
    reseller_id = Column(String(16))
    reseller_name = Column(String(64))
    pos_id = Column(String(16))
    pos_name = Column(String(64))
    lob = Column(String(16))
    sub_lob = Column(String(16))
    mpn = Column(String(16))
    original_image_url = Column(String(511))
    source = Column(String)
    scan_type = Column(String(16))
    scan_time = Column(TIMESTAMP)
    random_id = Column(String)
    create_time = Column(TIMESTAMP, default=datetime.now)
    update_time = Column(TIMESTAMP, default=datetime.now)
    
    inventory_status = Column(String(32))
    error_code = Column(String(32))
    is_kiosk_marked = Column(Integer)

    @classmethod
    def query_data_list(cls, limit: int, offset: int, last_scan_time: str) -> list:
        s = ComplianceDatabendSession()
        ret = []
        try:
            q = s.query(cls)
            if not last_scan_time:
                last_scan_time = "{} 00:00:00".format(date.today())
            q = q.filter(cls.scan_time >= last_scan_time)
            ret = q.order_by(cls.scan_time.asc()) \
                .limit(limit) \
                .offset(offset) \
                .query_list()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

    @classmethod
    def query_data_by_scan_time_and_random_id(cls, scan_time: str, random_id: str, limit: int) -> list:
        s = ComplianceDatabendSession()
        ret = []
        try:
            q = s.query(cls)
            q = q.filter(cls.scan_time >= scan_time)
            if random_id:
                q = q.filter(cls.random_id > random_id)
            ret = q.order_by(cls.random_id.asc()) \
                .limit(limit) \
                .query_list()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

    @classmethod
    def count_by_scan_time(cls, start_time: str,end_time: str) -> int:
        s = ComplianceDatabendSession()
        count = 0
        try:
            count = s.query(func.count(1)).filter(
                cls.scan_time >= start_time,
                cls.scan_time <= end_time
            ).scalar()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return count
