from typing import List

from sqlalchemy import *

from data.databend.smart_tower_inventory_base import SmartTowerSession
from domain.smart_tower.entity.outstock_so_velocity_waive import OutstockSoVelocityWaive
from util.conf import Base, logger
from util.util import env_dev


class InvOutStockWaive(Base):
    __tablename__ = "app_smart_tower_inv_diagnosis_outstock_so_velocity_waive_wi"
    # __table_args__ = {"schema": "gc_dmp_tower"}
    __table_args__ = {"schema": "test_db" if env_dev() else "gc_dmp_tower"}

    fiscal_week_year = Column(Integer, primary_key=True)
    fiscal_qtr_week_name = Column(String)
    fiscal_qtr_year = Column(Integer)
    fiscal_qtr_year_name = Column(String)
    fiscal_year = Column(Integer)
    rtm = Column(String)
    business_type = Column(String)
    level = Column(String)
    lob = Column(String)
    sub_lob = Column(String)
    nand = Column(String)
    case_size = Column(String)
    mpn = Column(String)
    sku = Column(String)
    so_velocity_cw = Column(Float)
    so_velocity_lw1 = Column(Float)
    so_velocity_lw2 = Column(Float)
    inv_point = Column(Integer)
    outstock_point = Column(Integer)
    outstock_ratio = Column(Float)
    so_velocity_tag = Column(String)
    waive_tag = Column(String)
    create_time = Column(TIMESTAMP)
    update_time = Column(TIMESTAMP)

    @classmethod
    def query_inventory_waive(cls, fiscal_week: str, waive_tag: str) -> List[OutstockSoVelocityWaive]:
        s = SmartTowerSession()
        res = []
        try:
            q = s.query(cls).filter(cls.fiscal_qtr_week_name == fiscal_week).filter(cls.waive_tag == waive_tag)
            ret = q.query_list()
            for item in ret:
                res.append(
                    OutstockSoVelocityWaive(
                        fiscal_week_year=item['fiscal_week_year'],
                        fiscal_qtr_week_name=item['fiscal_qtr_week_name'],
                        rtm=item['rtm'],
                        sub_rtm=item['business_type'],
                        level=item['level'],
                        lob=item['lob'],
                        sub_lob=item['sub_lob'],
                        nand=item['nand'],
                        mpn=item['mpn'],
                        sku=item['sku'],
                        so_velocity_cw=item['so_velocity_cw'],
                        so_velocity_lw1=item['so_velocity_lw1'],
                        so_velocity_lw2=item['so_velocity_lw2'],
                        inv_point=item['inv_point'],
                        outstock_point=item['outstock_point'],
                        outstock_ratio=item['outstock_ratio'],
                        update_time=item['update_time']
                    )
                )
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return res

    @classmethod
    def get_latest_refresh_time(cls, fiscal_week: str):
        s = SmartTowerSession()
        try:
            query_data = s.query(func.max(cls.update_time).label("update_time")).filter(cls.fiscal_qtr_week_name == fiscal_week).query_dict()
            latest_refresh_time = query_data["update_time"] if query_data.get("update_time") else None
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return latest_refresh_time
