import pandas as pd
from sqlalchemy.orm import Query

from databend_sqlalchemy import connector
from util.conf import *

databend_connect_info = {}
fast_write_config = conf['databend_gc_dmp_fast_write']
if os.environ.get('DB_ENV') == 'aws':
    fast_write_sec = get_secret_from_aws(fast_write_config['secret_name'],
                                         fast_write_config['secret_region'])
else:
    fast_write_sec = get_secret_from_apple(fast_write_config['secret_name'],
                                           fast_write_config['secret_region'])


class CustomQuery(Query):
    def _get_query_str(self) -> str:
        raw_sql = self.statement.compile(compile_kwargs={"literal_binds": True}).string
        return raw_sql

    def query_dataframe(self) -> pd.DataFrame:
        raw_sql = self._get_query_str()
        session_info = self.session.info
        db_url = session_info.get("db_url")
        if not db_url:
            raise Exception('databend connection info error. ')

        df = pd.DataFrame()
        conn = connector.connect(db_url)
        try:
            df = pd.read_sql(raw_sql, conn)
        except Exception as e:
            raise e
        finally:
            conn.close()
        return df

    def query_dict(self) -> dict:
        df = self.query_dataframe()
        df = df.astype(object).where(pd.notnull(df), None)
        query_data = df.to_dict('records')
        ret = {}
        if len(query_data) > 0:
            ret = query_data[0]
        return ret

    def query_list(self) -> list:
        df = self.query_dataframe()
        df = df.astype(object).where(pd.notnull(df), None)
        return df.to_dict('records')


user = fast_write_sec['username']
password = fast_write_sec['password']
default_db = fast_write_config['default_db']
databend_db_host = fast_write_config['https_host']
fast_write_url = f"https://{user}:{password}@{databend_db_host}/{default_db}?secure=true"
fast_write_engine = create_engine(f"databend://{user}:{password}@{databend_db_host}/{default_db}?secure=true")
FastWriteDatabendSession = sessionmaker(bind=fast_write_engine, query_cls=CustomQuery, info={"db_url": fast_write_url})
