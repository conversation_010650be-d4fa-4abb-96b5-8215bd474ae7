from data.databend.supply_data_databend_base import SupplyDatabendSession
from util.gc_dmp_base import *


class CTOExecution(Base):
    __tablename__ = "gc_dmp_execution"
    __table_args__ = {"schema": "gc_dmp_data"}

    snapshot_dt = Column(Date, primary_key=True)
    country = Column(String(256))
    rtm = Column(String(256))
    cust_id = Column(String(256))
    cust_name = Column(String(256))
    lob = Column(String(256))
    sub_lob = Column(String(256))
    project_cd = Column(String(256))
    cto = Column(String(256))
    cto_description = Column(String(256))
    total_backlog = Column(Numeric(18,3))
    balance_to_execute = Column(Numeric(18,3))
    dn_create_qty = Column(Numeric(18,3))
    dn_shipped_qty = Column(Numeric(18,3))
    open_dn = Column(Numeric(18,3))
    dn_on_credit_hold = Column(Numeric(18,3))
    dn_on_credit_hold_3days = Column(Numeric(18,3))
    rtm_new = Column(String(256))
    rtm_level_3 = Column(String(256))
    create_time = Column(DateTime, default=datetime.now, comment='create time')
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='update time')

    @classmethod
    def query_records(cls, snapshot_dt: str, rtms: list[str] = None, regions: list[str] = None, **kwargs):
        s = SupplyDatabendSession()
        try:
            filter_params = [cls.snapshot_dt == snapshot_dt]
            if rtms:
                filter_params.append(cls.rtm_level_3.in_(rtms))
            if regions:
                filter_params.append(cls.country.in_(regions))
            res = s.query(cls).filter(*filter_params).query_dataframe()
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return res
