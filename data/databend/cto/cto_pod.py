from data.databend.supply_data_databend_base import SupplyDatabendSession
from util.gc_dmp_base import *


class CTOPod(Base):
    __tablename__ = "gc_dmp_pod"
    __table_args__ = {"schema": "gc_dmp_data"}

    snapshot_dt = Column(Date, primary_key=True)
    cust_id = Column(String(256))
    cust_name = Column(String(256))
    ship_to_cust_id = Column(String(256))
    ship_to_name = Column(String(256))
    created_dt = Column(Date)
    item_created_dt = Column(Date)
    overall_credit_status_cd = Column(String(256))
    prod_id = Column(String(256))
    prod_desc = Column(String(256))
    acctng_doc_cre_dt = Column(Date)
    order_id = Column(String(256))
    order_item_nr = Column(String(256))
    delivery_id = Column(String(256))
    delivery_qty = Column(Numeric(38,0))
    lc_net_invoice_val = Column(Numeric(38,0))
    planned_goods_issue_dt = Column(Date)
    ac_gi_time = Column(String(256))
    billing_dt = Column(Date)
    release_dt = Column(Date)
    prod_class_desc = Column(String(256))
    prod_sub_class_desc = Column(String(256))
    sales_district_cd = Column(String(256))
    cust_grp_cd = Column(String(256))
    config_id = Column(String(256))
    orderer_name = Column(String(256))
    pod_dt = Column(Date)
    payment_term_cd = Column(String(256))
    invoice_id = Column(String(256))
    cust_po_nr = Column(String(256))
    net_invoice_val = Column(Numeric(38,0))
    ship_to_city_cd = Column(String(256))
    rtm = Column(String(256))
    rtm_new = Column(String(256))
    rtm_level_3 = Column(String(256))
    country = Column(String(256))
    create_time = Column(DateTime, default=datetime.now, comment='create time')
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='update time')

    @classmethod
    def query_records(cls, snapshot_dt: str, rtms: list[str] = None, regions: list[str] = None, **kwargs):
        s = SupplyDatabendSession()
        try:
            filter_params = [cls.snapshot_dt == snapshot_dt]
            if rtms:
                filter_params.append(cls.rtm_level_3.in_(rtms))
            if regions:
                filter_params.append(cls.country.in_(regions))
            res = s.query(cls).filter(*filter_params).query_list()
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return res
