from data.databend.supply_data_databend_base import SupplyDatabendSession
from util.gc_dmp_base import *


class CTOBacklog(Base):
    __tablename__ = "gc_dmp_backlog"
    __table_args__ = {"schema": "gc_dmp_data"}

    snapshot_dt = Column(Date, primary_key=True)
    cust_id = Column(String(256))
    sold_to_name = Column(String(256))
    payer_cust_id = Column(String(256))
    payer_to_name = Column(String(256))
    order_id = Column(String(256))
    order_item_nr = Column(Numeric(38,0))
    req_deliv_dt = Column(Date)
    del_block = Column(String(256))
    local_order_dt = Column(Date)
    cust_po_nr = Column(String(256))
    ship_to_cust_id = Column(String(256))
    ship_to_name = Column(String(256))
    ship_to_city_cd = Column(String(256))
    ship_to_state_cd = Column(String(256))
    po_number = Column(String(256))
    po_item = Column(String(256))
    prod_id = Column(String(256))
    item_desc = Column(String(256))
    project_cd = Column(String(256))
    atp_request_dt = Column(String(256))
    item_lower_commit_dt = Column(Date)
    item_upper_commit_dt = Column(Date)
    order_qty = Column(Numeric(18,3))
    delivery_qty = Column(Numeric(18,3))
    shipped_qty = Column(Numeric(18,3))
    remaining_qty = Column(Numeric(18,3))
    scheduled_qty = Column(Numeric(18,3))
    net_open_amt = Column(Numeric(38,2))
    selling_price = Column(String(256))
    config_id = Column(String(256))
    aging = Column(Numeric(18,3))
    rtm = Column(String(256))
    rtm_new = Column(String(256))
    rtm_level_3 = Column(String(256))
    country = Column(String(256))
    create_time = Column(DateTime, default=datetime.now, comment='create time')
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='update time')

    @classmethod
    def query_records(cls, snapshot_dt: str, rtms: list[str] = None, regions: list[str] = None,  **kwargs):
        s = SupplyDatabendSession()
        try:
            filter_params = [cls.snapshot_dt == snapshot_dt]
            if rtms:
                filter_params.append(cls.rtm_level_3.in_(rtms))
            if regions:
                filter_params.append(cls.country.in_(regions))
            res = s.query(cls).filter(*filter_params).query_dataframe()
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return res
