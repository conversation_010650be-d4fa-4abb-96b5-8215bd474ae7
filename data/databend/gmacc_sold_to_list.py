from data.databend.supply_data_databend_base import SupplyDatabendSession
from util.conf import *


# 状态确认表
class GMACCSoldtoList(Base):
    __tablename__ = "dim_fast_gmacc_sold_to_list_di"
    __table_args__ = {"schema": "gc_dmp_fast"}

    sales_org_cd = Column(String(256), primary_key=True)
    hq_id = Column(String(256))
    sold_to_id = Column(String(256))
    create_time = Column(DateTime)
    update_time = Column(DateTime)

    @classmethod
    def get_distinct_sold_to(cls) -> list[str]:
        try:
            res = []
            s = SupplyDatabendSession()
            result = s.query(cls.sold_to_id).distinct().query_list()
            res = [item.get("sold_to_id") for item in result]
        except Exception as e:
            logger.exception(e)
            raise e

        return res
