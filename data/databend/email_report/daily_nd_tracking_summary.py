import datetime
import os


from data.databend.gc_dmp_directship_databend_base import DirectshipDatabendSession
from data.email_config import EmailConfigRepository
from domain.dashboard.entity.anti_fraud.anti_fraud_so_ub import (rtm_dict, DailyNdItem, sub_rtm_dict, RTM_ENT, RTM_CHANNEL_ONLINE)
from kit.token_bucket import EMAIL_BUCKET_KEY_PREFIX
from util.conf import Base
from util.conf import Column, String, Integer, TIMESTAMP
from util.conf import logger
from util.const import ALL, EmailCmd
from util.send_email import del_email_config, async_rate_limited_send_email_v2
from util.util import env_dev


class DailyNdTrackingSummaryDi(Base):
    __tablename__ = "app_directship_daily_nd_ub_tracking_summary_di"
    __table_args__ = {"schema": "gc_dmp_directship"}
    # __table_args__ = {"schema": "test_db" if env_dev() else "gc_dmp_directship"}

    snapshot_date = Column(String(256))
    fiscal_dt = Column(String(256), primary_key=True)
    fiscal_qtr_year = Column(Integer)
    fiscal_qtr_year_name = Column(String(256))
    rtm = Column(String(256))
    sub_rtm = Column(String(256))
    nd_type = Column(String(256))
    hq_id = Column(String(256))
    hq_name = Column(String(256))
    lob = Column(String(256))
    sub_lob = Column(String(256))
    total_so_acc = Column(Integer)
    ub3_acc = Column(Integer)
    ub7_acc = Column(Integer)
    fiscal_qtr_year_lq = Column(Integer)
    fiscal_qtr_year_name_lq = Column(String(256))
    total_so_acc_lq = Column(Integer)
    ub3_acc_lq = Column(Integer)
    ub7_acc_lq = Column(Integer)
    create_time = Column(TIMESTAMP, default=datetime.datetime.now())
    update_time = Column(TIMESTAMP, default=datetime.datetime.now, onupdate=datetime.datetime.now)

    @staticmethod
    def handle_cross_quarter_data(data: list[DailyNdItem], is_cross_quarter: bool, current_quarter: str):
        # 跨季度: Online和Ent都是T-2产出数据, 季度开始的第二天,应该无数据
        if not is_cross_quarter:
            return

        for item in data:
            item.fiscal_qtr_year_name = current_quarter
            if item.rtm in [RTM_ENT, RTM_CHANNEL_ONLINE]:
                item.total_so = 0
                item.ub3 = 0
                item.ub7 = 0

    @classmethod
    def query_rtm_sub_rtm_nd_records(cls, snapshot_date: str):
        result = []
        s = DirectshipDatabendSession()
        try:
            records = (
                s.query(
                    cls.rtm,
                    cls.sub_rtm,
                    cls.hq_name,
                    cls.nd_type
                ).filter(cls.snapshot_date == snapshot_date).filter(cls.sub_rtm != 'Duty-free').distinct().query_list()
            )
            for item in records:
                origin_rtm = item.get("rtm")
                rtm = rtm_dict.get(origin_rtm, origin_rtm)
                origin_sub_rtm = item.get("sub_rtm")
                sub_rtm = sub_rtm_dict.get(origin_sub_rtm, origin_sub_rtm)

                result.append({
                    "rtm": rtm,
                    "sub_rtm": sub_rtm,
                    "hq_name": item.get("hq_name"),
                    "nd_type": item.get("nd_type"),
                })
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return result

    @classmethod
    def query_daily_nd_summary_records(cls, snapshot_date: str, is_cross_quarter: bool, current_quarter: str, nd_data: dict) -> list:
        result = []
        s = DirectshipDatabendSession()
        try:
            daily_list = s.query(
                            cls.snapshot_date.label('fiscal_dt'),
                            cls.fiscal_qtr_year_name,
                            cls.nd_type,
                            cls.rtm,
                            cls.sub_rtm,
                            cls.hq_name,
                            cls.sub_lob,
                            cls.total_so_acc.label("total_so"),
                            cls.ub3_acc.label("ub3"),
                            cls.ub7_acc.label("ub7"),
                            cls.fiscal_qtr_year_name_lq.label('fiscal_qtr_year_name_lq'),
                            cls.total_so_acc_lq.label("total_so_lq"),
                            cls.ub3_acc_lq.label('ub3_lq'),
                            cls.ub7_acc_lq.label('ub7_lq'),
                    ).filter(cls.snapshot_date == snapshot_date).filter(cls.sub_rtm != 'Duty-free').query_list()

            if not daily_list:
                email_config = EmailConfigRepository.query_email_config(EmailCmd.Dashboard_V2)
                params = {"env": os.environ.get('ENV'),
                          "dashboard": 'Anti-iPhone ND/T1 daliy View',
                          "source": 'email_report',
                          "message": f"daily_nd_view no data: {snapshot_date}"
                          }
                file_paths, email_config = del_email_config(email_config=email_config, params=params,
                                                            read_template=False)

                async_rate_limited_send_email_v2(limit_key=EMAIL_BUCKET_KEY_PREFIX + 'daily_nd',
                                                 capacity=1,
                                                 expire_time=1200,
                                                 email_config=email_config,
                                                 file_paths=file_paths)
                raise Exception(f"daily_nd_view no data: {snapshot_date}")
            # 取第一条数据, 用于临时给EDU、ENT补充缺失数据
            tmp_fiscal_dt = daily_list[0].get("fiscal_dt")
            tmp_fiscal_qtr_year_name = daily_list[0].get("fiscal_qtr_year_name")
            tmp_sub_lob = daily_list[0].get("sub_lob")  # 随便一个sub_lob就行, 不重要
            tmp_fiscal_qtr_year_name_lq = daily_list[0].get("fiscal_qtr_year_name_lq")
            tmp_hq = {}

            for item in daily_list:
                origin_rtm = item.get("rtm")
                rtm = rtm_dict.get(origin_rtm, origin_rtm)

                # 主要就是为了替换EDU、ENT
                origin_sub_rtm = item.get("sub_rtm")
                sub_rtm = sub_rtm_dict.get(origin_sub_rtm, origin_sub_rtm)

                # 统计本周数据库已经有哪些HQ, 用于补充数据
                tmp_rtm_info = tmp_hq.setdefault(rtm, {})
                tmp_sub_rtm_info = tmp_rtm_info.setdefault(sub_rtm, {})
                tmp_nd_info = tmp_sub_rtm_info.setdefault(item.get('nd_type'), set())
                tmp_nd_info.add(item.get("hq_name"))

                result.append(DailyNdItem(
                    fiscal_dt=item.get("fiscal_dt"),
                    fiscal_qtr_year_name=item.get("fiscal_qtr_year_name"),
                    rtm=rtm,
                    sub_rtm=sub_rtm,
                    hq_name=item.get("hq_name"),
                    sub_lob=item.get("sub_lob"),
                    total_so=item.get("total_so"),
                    ub3=item.get("ub3"),
                    ub7=item.get("ub7"),
                    nd_type=item.get("nd_type"),
                    fiscal_qtr_year_name_lq=item.get("fiscal_qtr_year_name_lq"),
                    total_so_lq=item.get("total_so_lq"),
                    ub3_lq=item.get("ub3_lq"),
                    ub7_lq=item.get("ub7_lq"),
                ))

            # 补充缺少HQ的数据
            for tmp_rtm, rmt_info in nd_data.items():
                for tmp_sub_rtm, sub_rtm_info in rmt_info.items():
                    for tmp_nd_type, tmp_hq_list in sub_rtm_info.items():
                        existing_hq_set = tmp_hq.setdefault(tmp_rtm, {}).setdefault(tmp_sub_rtm, {}).setdefault(tmp_nd_type, set())
                        new_hq_names = set(tmp_hq_list) - existing_hq_set  # 只取不存在的 HQ
                        result.extend(
                            DailyNdItem(
                                fiscal_dt=tmp_fiscal_dt,
                                fiscal_qtr_year_name=tmp_fiscal_qtr_year_name,
                                rtm=tmp_rtm,
                                sub_rtm=tmp_sub_rtm,
                                hq_name=tmp_hq_name,
                                sub_lob=tmp_sub_lob,
                                total_so=0,
                                ub3=0,
                                ub7=0,
                                nd_type=tmp_nd_type,
                                fiscal_qtr_year_name_lq=tmp_fiscal_qtr_year_name_lq,
                                total_so_lq=0,
                                ub3_lq=0,
                                ub7_lq=0,
                            )
                            for tmp_hq_name in new_hq_names
                        )

        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()

        # 跨季度: Online和Ent都是T-2产出数据, 季度开始的第二天,应该无数据
        cls.handle_cross_quarter_data(data=result, is_cross_quarter=is_cross_quarter, current_quarter=current_quarter)

        return result
