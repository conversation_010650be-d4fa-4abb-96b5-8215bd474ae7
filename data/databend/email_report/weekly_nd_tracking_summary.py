import datetime
import os
from typing import List

from data.databend.gc_dmp_directship_databend_base import DirectshipDatabendSession
from data.email_config import EmailConfigRepository
from domain.dashboard.entity.anti_fraud.anti_fraud_so_ub import WeeklyNdItem, rtm_dict,  sub_rtm_dict
from kit.token_bucket import EMAIL_BUCKET_KEY_PREFIX
from util.conf import Base
from util.conf import Column, String, Integer, TIMESTAMP
from util.conf import logger
from util.const import ALL, EmailCmd
from util.send_email import del_email_config, async_rate_limited_send_email_v2
from util.util import env_dev


class WeeklyNdTrackingSummaryDi(Base):
    __tablename__ = "app_directship_weekly_nd_ub_tracking_summary_di"
    __table_args__ = {"schema": "gc_dmp_directship"}
    # __table_args__ = {"schema": "test_db" if env_dev() else "gc_dmp_directship"}

    snapshot_date = Column(String(256), primary_key=True)
    fiscal_week_year = Column(Integer, primary_key=True)
    fiscal_qtr_week_name = Column(String(256), comment='FY23Q1W12', primary_key=True)
    rtm = Column(String(256))
    sub_rtm = Column(String(256))
    nd_type = Column(String(256))
    hq_id = Column(String(256))
    hq_name = Column(String(256))
    lob = Column(String(256))
    sub_lob = Column(String(256))
    total_so = Column(Integer)
    ub7 = Column(Integer)
    create_time = Column(TIMESTAMP, default=datetime.datetime.now())
    update_time = Column(TIMESTAMP, default=datetime.datetime.now, onupdate=datetime.datetime.now)

    @classmethod
    def query_rtm_sub_rtm_nd_records(cls, snapshot_date_list: List[str], fiscal_weeks: List[str]):
        result = []
        s = DirectshipDatabendSession()
        try:
            records = (
                s.query(
                    cls.rtm,
                    cls.sub_rtm,
                    cls.hq_name,
                    cls.nd_type
                ).filter(cls.snapshot_date.in_(snapshot_date_list)).filter(cls.fiscal_qtr_week_name.in_(fiscal_weeks)).filter(
                    cls.sub_rtm != 'Duty-free').distinct().query_list()
            )
            for item in records:
                origin_rtm = item.get("rtm")
                rtm = rtm_dict.get(origin_rtm, origin_rtm)
                origin_sub_rtm = item.get("sub_rtm")
                sub_rtm = sub_rtm_dict.get(origin_sub_rtm, origin_sub_rtm)

                result.append({
                    "rtm": rtm,
                    "sub_rtm": sub_rtm,
                    "hq_name": item.get("hq_name"),
                    "nd_type": item.get("nd_type"),
                })
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return result

    @classmethod
    def query_week_nd_summary_records(cls, snapshot_date: str, fiscal_week: str, nd_data: dict):
        result = []
        s = DirectshipDatabendSession()
        try:
            week_list = (s.query(
                cls.snapshot_date,
                cls.fiscal_qtr_week_name,
                cls.rtm,
                cls.sub_rtm,
                cls.hq_name,
                cls.nd_type,
                cls.sub_lob,
                cls.total_so,
                cls.ub7
                ).filter(cls.snapshot_date == snapshot_date).filter(cls.fiscal_qtr_week_name == fiscal_week).filter(cls.sub_rtm != 'Duty-free').query_list()
            )
            if not week_list:
                email_config = EmailConfigRepository.query_email_config(EmailCmd.Dashboard_V2)
                params = {"env": os.environ.get('ENV'),
                          "dashboard": 'Anti-iPhone ND/T1 weekly View',
                          "source": 'email_report',
                          "message": f"week_nd_view: fiscal_week: {fiscal_week}, snapshot_date: {snapshot_date}"
                          }
                file_paths, email_config = del_email_config(email_config=email_config, params=params,
                                                            read_template=False)

                async_rate_limited_send_email_v2(limit_key=EMAIL_BUCKET_KEY_PREFIX + 'week_nd',
                                                 capacity=1,
                                                 expire_time=1200,
                                                 email_config=email_config,
                                                 file_paths=file_paths)
                raise Exception(f"week_nd_view: fiscal_week: {fiscal_week}, snapshot_date: {snapshot_date}")

            # 取第一条数据, 用于临时给EDU、ENT补充缺失数据
            tmp_snapshot_date = week_list[0].get("snapshot_date")
            tmp_fiscal_qtr_year_name = week_list[0].get("fiscal_qtr_week_name")
            tmp_sub_lob = week_list[0].get("sub_lob")  # 随便一个sub_lob就行, 不重要
            tmp_hq = {}

            for item in week_list:
                origin_rtm = item.get("rtm")
                rtm = rtm_dict.get(origin_rtm, origin_rtm)

                # 主要就是为了替换EDU、ENT
                origin_sub_rtm = item.get("sub_rtm")
                sub_rtm = sub_rtm_dict.get(origin_sub_rtm, origin_sub_rtm)

                # 统计本周数据库已经有哪些HQ, 用于补充数据
                tmp_rtm_info = tmp_hq.setdefault(rtm, {})
                tmp_sub_rtm_info = tmp_rtm_info.setdefault(sub_rtm, {})
                tmp_nd_info = tmp_sub_rtm_info.setdefault(item.get('nd_type'), set())
                tmp_nd_info.add(item.get("hq_name"))

                result.append(WeeklyNdItem(
                    snapshot_date=item.get('snapshot_date'),
                    fiscal_qtr_week_name=item.get('fiscal_qtr_week_name'),
                    rtm=rtm,
                    sub_rtm=sub_rtm,
                    sub_lob=item.get('sub_lob'),
                    hq_name=item.get('hq_name'),
                    nd_type=item.get('nd_type'),
                    total_so=item.get('total_so'),
                    lte_7_days_ub=item.get('ub7')
                ))

            # 补充缺少HQ的数据
            for tmp_rtm, rmt_info in nd_data.items():
                for tmp_sub_rtm, sub_rtm_info in rmt_info.items():
                    for tmp_nd_type, tmp_hq_list in sub_rtm_info.items():
                        existing_hq_set = tmp_hq.setdefault(tmp_rtm, {}).setdefault(tmp_sub_rtm,{}).setdefault(tmp_nd_type, set())
                        new_hq_names = set(tmp_hq_list) - existing_hq_set  # 只取不存在的 HQ
                        result.extend(
                            WeeklyNdItem(
                                snapshot_date=tmp_snapshot_date,
                                fiscal_qtr_week_name=tmp_fiscal_qtr_year_name,
                                rtm=tmp_rtm,
                                sub_rtm=tmp_sub_rtm,
                                sub_lob=tmp_sub_lob,
                                hq_name=tmp_hq_name,
                                nd_type=tmp_nd_type,
                                total_so=0,
                                lte_7_days_ub=0
                            )
                            for tmp_hq_name in new_hq_names
                        )

        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return result
