from itertools import groupby

from data.databend.supply_data_databend_base import SupplyDatabendSession
from domain.mono.pos_allocation.entity.cdc import Cdc
from domain.mono.pos_allocation.entity.mpn import Mpn
from util.const import ErrorExcept
from util.gc_dmp_base import *
 


class DsCdcPodInv(Base):
    __tablename__ = "app_fast_mono_ds_pod_wi"
    __table_args__ = {"schema": "gc_dmp_fast"}

    fiscal_qtr_week_name = Column(String(32), comment='FY23Q1W12', primary_key=True)
    sold_to = Column(Integer, comment='')
    ship_to = Column(Integer)
    mpn = Column(String(32))
    qty = Column(Integer)

    @classmethod
    def query_by_week(cls, fiscal_week, mpns: list[Mpn]):
        s = SupplyDatabendSession()
        mpn_ids = [mpn.id for mpn in mpns]
        try:
            result = s.query(
                cls.fiscal_qtr_week_name,
                cls.sold_to,
                cls.ship_to,
                cls.mpn,
                cls.qty,
            ).filter(cls.fiscal_qtr_week_name == fiscal_week) \
                .filter(cls.mpn.in_(mpn_ids)).order_by(cls.ship_to).query_list()
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return result

 
 
 
 
 

