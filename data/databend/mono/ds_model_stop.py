from data.databend.supply_data_databend_base import SupplyDatabendSession
from domain.mono.pos_allocation.entity.pos_sublob_stop import PosSublobStop
from util.gc_dmp_base import *


class DsModelStop(Base):
    __tablename__ = "app_fast_mono_ds_model_stop_wi"
    __table_args__ = {"schema": "gc_dmp_fast",
                      "extend_existing": True}

    fiscal_qtr_week_name = Column(String(16), primary_key=True)
    customer_sold_to_id = Column(String(64))
    customer_sold_to_name = Column(String(128))
    hqid = Column(String(64))
    reseller_name = Column(String(16))
    pos_id = Column(String(16))
    pos_name = Column(String(16))
    model = Column(String(16))
    create_time = Column(DateTime)
    update_time = Column(DateTime)

    @classmethod
    def query_by_week(cls, fiscal_week: str) -> list[PosSublobStop]:
        s = SupplyDatabendSession()
        ret = []
        try:
            query_result = (s.query(cls.fiscal_qtr_week_name.label("fiscal_week"),
                                    cls.customer_sold_to_id.label("sold_to_id"),
                                    cls.pos_id,
                                    cls.model.label("sub_lob"))
                            .filter(cls.fiscal_qtr_week_name == fiscal_week)
                            .query_list())
            for item in query_result:
                ret.append(
                    PosSublobStop(
                        item.get("fiscal_week"),
                        item.get("pos_id"),
                        item.get("sold_to_id"),
                        item.get("sub_lob"),
                    )
                )
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret
