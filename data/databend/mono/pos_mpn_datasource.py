import traceback

from data.databend.gc_dmp_fast_write_databend_base import FastWriteDatabendSession
from util.const import ErrorExcept, ErrCode
from util.gc_dmp_base import *


class PosMpnDatasource(Base):
    __tablename__ = "pos_mpn_datasource"
    __table_args__ = {"schema": "gc_dmp_fast_write"}

    week_date = Column(String(16), comment='FY23Q1W12', primary_key=True)
    apple_id = Column(Integer, comment='pos_id')
    prod_id = Column(String(64), comment='MPN')
    customer_sold_to_id = Column(String(256))
    high_low_runner = Column(String(64))
    npp_reseller = Column(String(256))
    reseller_npp_type = Column(String(32))
    customer_sold_to_id_origin = Column(String(256))
    lw_eoh = Column(Integer)
    sell_out_five_week_avg = Column(Float)
    extra = Column(Text)
    display_info = Column(Text)
    create_time = Column(DateTime)

    @classmethod
    def bulk_save(cls, objs: list):
        ret = []
        s = FastWriteDatabendSession()
        try:
            columns = [column.name for column in cls.__table__.columns]
            insert_sql = f"INSERT INTO {cls.__tablename__} ({', '.join(columns)}) VALUES "
            # 构造占位符
            placeholders = []
            for item in objs:
                tmp_insert_values = []
                for column in columns:
                    value = 'NULL' if item[column] is None else f"'{str(item[column])}'"
                    tmp_insert_values.append(value)
                placeholders.append(f"({', '.join(tmp_insert_values)})")
            # 将所有行的占位符合并
            full_values = ', '.join(placeholders)
            # 拼接完整的SQL语句
            insert_sql += full_values
            s.execute(insert_sql)
            s.commit()
        except Exception as e:
            logger.exception(e)
            raise ErrorExcept(ErrCode.DBInsert, "pos_mpn_datasource: insert to db failed" +
                              traceback.format_exc() + "params:" + str(objs))
        return ret
