from data.databend.supply_data_databend_base import SupplyDatabendSession
from domain.mono.pos_allocation.entity.pos import Pos, sort_pos
from util.const import ErrorExcept
from util.gc_dmp_base import *
from util.util import pd


class DsPos(Base):
    __tablename__ = "app_fast_mono_ds_pos_list_wi"
    __table_args__ = {"schema": "gc_dmp_fast"}

    fiscal_qtr_week_name = Column(String(256), primary_key=True, comment='FY23Q1W12')
    update_category = Column(String(32), comment='Actived,Deleted')
    hqid = Column(String(64))
    reseller_name = Column(String(128))
    pos_id = Column(Integer)
    pos_name = Column(String(256))
    customer_sold_to_id = Column(Integer)
    customer_sold_to_name = Column(String(128))
    pos_type = Column(String(32))
    pos_location_type = Column(String(32))
    district_group = Column(Integer)
    cdc1 = Column(String(32))
    cdc2 = Column(String(32))
    cdc3 = Column(String(32))
    cdc4 = Column(String(32))
    cdc5 = Column(String(32))
    cdc6 = Column(String(32))
    create_time = Column(DateTime, default=datetime.now, comment='create time')
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='update time')

    @classmethod
    def query_by_fiscal_week(cls, fiscal_week) -> list[Pos]:
        s = SupplyDatabendSession()
        res = []
        try:
            result = s.query(
                cls.fiscal_qtr_week_name,
                cls.update_category.label("category"),
                cls.hqid,
                cls.reseller_name,
                cls.pos_id,
                cls.pos_name,
                cls.customer_sold_to_id.label("sold_to_id"),
                cls.customer_sold_to_name.label("sold_to_name"),
                cls.pos_type, cls.pos_location_type, cls.district_group,
                cls.cdc1, cls.cdc2, cls.cdc3, cls.cdc4, cls.cdc5, cls.cdc6) \
                .filter(cls.fiscal_qtr_week_name == fiscal_week) \
                .query_list()
            for item in result:
                cdcs = []
                cdc_fields = ["cdc1", "cdc2", "cdc3", "cdc4", "cdc5", "cdc6"]
                for field in cdc_fields:
                    if item.get(field) is not None:
                        cdcs.append(item.get(field))
                p = Pos(
                    hq_id=item["hqid"],
                    reseller_name=item["reseller_name"],
                    sold_to_id=item["sold_to_id"],
                    sold_to_name=item["sold_to_name"],
                    pos_id=int(item["pos_id"]),
                    pos_name=item["pos_name"],
                    pos_type=item["pos_type"],
                    location_type=item["pos_location_type"],
                    category=item["category"],
                    district_group=int(item["district_group"]),
                    cdcs=cdcs,
                )
                res.append(p)
            res = sort_pos(res)  # 排序，保证每次读到的都是有序的pos列表
            return res
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return res
    
    @classmethod
    def query_pos_ship_to_mapping(cls, fiscal_week) -> pd.DataFrame:
        s = SupplyDatabendSession()
        res = pd.DataFrame()
        try:
            res = s.query(
                cls.pos_id,
                cls.cdc1.label("ship_to_id")) \
                .filter(cls.fiscal_qtr_week_name == fiscal_week) \
                .query_list()
            res = pd.DataFrame(res)
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return res
