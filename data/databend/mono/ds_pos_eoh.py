from data.databend.supply_data_databend_base import SupplyDatabendSession
from domain.mono.pos_allocation.entity.mpn import Mpn
from domain.mono.pos_allocation.entity.pos import Pos, sort_pos
from domain.mono.pos_allocation.entity.pos_eoh import PosEoh
from util.const import ErrorExcept
from util.gc_dmp_base import *
from util.util import pd


# pos每周日版本的店面库存
class DsPosEoh(Base):
    __tablename__ = "app_fast_mono_pos_inv_wa"
    __table_args__ = {"schema": "gc_dmp_fast",
                      "extend_existing": True}

    fiscal_week_year = Column(Integer, primary_key=True)
    apple_id = Column(String(64))
    pos_name = Column(String(128))
    prod_id = Column(String(64))
    fph1 = Column(String(16))
    fph2 = Column(String(16))
    fph3 = Column(String(16))
    fph4 = Column(String(16))
    fph5 = Column(String(16))
    inv_eoh_cnt = Column(Integer)
    create_time = Column(DateTime)
    update_time = Column(DateTime)

    @classmethod
    def query_pos_eoh(cls, mpns: list[Mpn], pos_list: list[Pos]) -> list[PosEoh]:
        s = SupplyDatabendSession()
        ret = []
        try:
            mpn_ids = [mpn.id for mpn in mpns]
            pos_ids = [str(pos.pos_id) for pos in pos_list]
            # 用最大周去查询, 不用输入的财年周
            sub_query = s.query(func.max(cls.fiscal_week_year))
            query_result = (s.query(cls.apple_id.label("pos_id"),
                                    cls.fph1.label("lob"),
                                    cls.prod_id.label("mpn"),
                                    cls.inv_eoh_cnt.label("eoh"))
                            .filter(cls.fiscal_week_year == sub_query)
                            .filter(cls.prod_id.in_(mpn_ids))
                            .filter(cls.apple_id.in_(pos_ids))
                            .filter(cls.inv_eoh_cnt > 0)
                            .query_list())
            for item in query_result:
                # 店面库存为负数的，先置为0
                eoh = item.get("eoh")
                if eoh < 0:
                    eoh = 0
                ret.append(
                    PosEoh(
                        item.get("pos_id"),
                        item.get("lob"),
                        item.get("mpn"),
                        eoh,
                    )
                )
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret
