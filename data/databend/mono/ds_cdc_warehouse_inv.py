from itertools import groupby

import pandas as pd

from data.databend.supply_data_databend_base import SupplyDatabendSession
from domain.mono.pos_allocation.entity.cdc import Cdc, get_cdc_by_ship_to
from domain.mono.pos_allocation.entity.mpn import Mpn
from util.const import ErrorExcept
from util.gc_dmp_base import *


class DsCdcWarehouseInv(Base):
    __tablename__ = "app_fast_mono_wh_inv_wa"
    __table_args__ = {"schema": "gc_dmp_fast"}

    fiscal_qtr_week_name = Column(String(32), comment='FY23Q1W12', primary_key=True)
    sold_to_id = Column(Integer, comment='')
    ship_to = Column(String(32))
    lob = Column(String(32))
    sublob = Column(String(64))
    mpn = Column(String(32))
    warehouse_name = Column(String(32))
    warehouse_inv = Column(Integer)

    @classmethod
    def query_by_week(cls, fiscal_week, mpns:list[Mpn])->list[Cdc]:
        s = SupplyDatabendSession()
        mpn_ids = [mpn.id for mpn in mpns]
        cdcs = []
        try:
            result = s.query(
                cls.fiscal_qtr_week_name,
                cls.sold_to_id.label('sold_to'),
                cls.ship_to,
                cls.lob,
                cls.mpn,
                cls.warehouse_name,
                cls.warehouse_inv,
            ).filter(cls.fiscal_qtr_week_name == fiscal_week)\
                .filter(cls.mpn.in_(mpn_ids)).order_by(cls.ship_to).query_list()

            for item in result:
                ship_to = item["ship_to"]
                cdc = get_cdc_by_ship_to(cdcs, ship_to)
                if cdc is None:
                    cdc = Cdc(ship_to, item["sold_to"], item["warehouse_name"])
                    cdcs.append(cdc)
                cdc.add_warehouse_supply(item["mpn"], int(item["warehouse_inv"]))
            return cdcs
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
    
    @classmethod
    def query_ship_to_warehouse_name(cls, fiscal_week: str) -> pd.DataFrame:
        s = SupplyDatabendSession()
        ret = []
        try:
            ret = s.query(
                cls.ship_to.label("ship_to_id"),
                cls.warehouse_name,
            ).distinct().filter(cls.fiscal_qtr_week_name == fiscal_week, cls.ship_to != None)\
            .filter(cls.warehouse_name != "天音信息服务郑州配ECPP良品仓")\
            .order_by(cls.ship_to).query_dataframe()
            return ret
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()