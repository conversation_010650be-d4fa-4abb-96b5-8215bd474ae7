from data.databend.supply_data_databend_base import SupplyDatabendSession
from domain.mono.pos_allocation.entity.mpn_info import MpnInfo
from domain.mono.pos_allocation.entity.shipment_plan_supply import ShipmentPlanSupplier, get_supplier_by_sold_to
from util.gc_dmp_base import *


class DsShipmentPlanSupply(Base):
    __tablename__ = "app_fast_mono_ds_supply_wi"
    __table_args__ = {"schema": "gc_dmp_fast"}

    fiscal_qtr_week_name = Column(String(16), comment='FY23Q1W12', primary_key=True)
    customer_name = Column(String(256))
    customer_sold_to_id = Column(String(256), comment='')
    lob = Column(String(32))
    model = Column(String(64))
    project_short_desc = Column(String(32))
    revenue_demo = Column(String(32))
    apple_part = Column(String(64))
    lifecycle = Column(String(32))
    description = Column(String(256))
    cw_supply = Column(Integer)
    create_time = Column(DateTime)
    update_time = Column(DateTime)

    @classmethod
    def query_by_week(cls, fiscal_week: str) -> list[ShipmentPlanSupplier] and list[MpnInfo]:
        s = SupplyDatabendSession()
        suppliers = []
        mpn_infos = []
        try:
            result = (s.query(cls.fiscal_qtr_week_name.label("fiscal_week"),
                              cls.customer_name.label("sold_to_name"),
                              cls.customer_sold_to_id.label("sold_to"),
                              cls.lob,
                              cls.model.label("sub_lob"),
                              cls.project_short_desc,
                              cls.revenue_demo,
                              cls.apple_part.label('mpn_id'),
                              cls.lifecycle,
                              cls.description,
                              cls.cw_supply,
                              )
                      .filter(cls.fiscal_qtr_week_name == fiscal_week)
                      .query_list())

            for item in result:
                sold_to = int(item["sold_to"])
                supplier = get_supplier_by_sold_to(suppliers, sold_to)
                if supplier is None:
                    supplier = ShipmentPlanSupplier(sold_to)
                    suppliers.append(supplier)
                supplier.add_supply(item["mpn_id"], int(item["cw_supply"]))

                mpn_info = MpnInfo(item["mpn_id"], item["sold_to"], item["sold_to_name"], item["lob"], item["sub_lob"], item["project_short_desc"], item["description"])
                mpn_infos.append(mpn_info)
            # 去除mpn_infos中的重复数据
            mpn_infos = list({mpn_info.id: mpn_info for mpn_info in mpn_infos}.values())

        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return suppliers, mpn_infos
