from datetime import timedelta
import pandas as pd

from util.gc_dmp_base import *


class DimPubProdInfo(GcDmpBase):
    __tablename__ = 'dim_pub_prod_info'
    __table_args__ = {'schema': 'dmp_dw_common'}
    
    id = Column(Integer, autoincrement=True, primary_key=True)
    mpn_id = Column(String(256))
    hier_node_level_1_name = Column(String(256))
    hier_node_level_2_name = Column(String(256))
    hier_node_level_3_name = Column(String(256))
    hier_node_level_4_name = Column(String(256))
    hier_node_level_5_name = Column(String(256))
    sku = Column(String(256))
    project_cd = Column(String(256))
    project_short_desc = Column(String(256))
    type_cd = Column(String(256))
    type_short_desc = Column(String(256))
    form_cd = Column(String(256))
    form_short_desc = Column(String(256))
    mpn_desc = Column(String(256))
    nand = Column(String(256))
    color_medium_desc = Column(String(256))
    lob = Column(String(256))
    sub_lob = Column(String(256))
    create_time = Column(DateTime)
    update_time = Column(DateTime)


    @classmethod
    def get_valid_model_list(cls, lob: str):
        s = GcDmpSession()
        ret = []
        try:
            q = s.query(cls.hier_node_level_3_name.distinct()) \
                .filter(cls.lob == lob) \
                .all()
            ret = [i[0] for i in q]
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret
    
    
    @classmethod
    def get_valid_mpn_list(cls, lob: str):
        s = GcDmpSession()
        ret = []
        try:
            q = s.query(cls.mpn_id.distinct()) \
                .filter(cls.lob == lob) \
                .all()
            ret = [i[0] for i in q]
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

    @classmethod
    def get_mpn_fph4_project_code(cls, lob: str):
        ret = pd.DataFrame()
        try:
            sql_statement = f'''
                SELECT DISTINCT mpn_id as mpn, hier_node_level_4_name as fph4, project_short_desc AS project_code, sku
                FROM {cls.__table_args__.get('schema')}.{cls.__tablename__}
                WHERE lob='{lob}'
            '''
            ret = pd.read_sql_query(sql_statement, GcDmpEngine)
        except Exception as e:
            logger.exception(e)
        return ret
    
    @classmethod
    def get_mpn_nand_color(cls, lob: str):
        ret = pd.DataFrame()
        try:
            sql_statement = f'''
                SELECT DISTINCT mpn_id as mpn, nand, color_medium_desc AS color
                FROM {cls.__table_args__.get('schema')}.{cls.__tablename__}
                WHERE lob='{lob}'
            '''
            ret = pd.read_sql_query(sql_statement, GcDmpEngine)
        except Exception as e:
            logger.exception(e)
        return ret
