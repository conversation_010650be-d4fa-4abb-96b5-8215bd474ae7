from util.fast_lite_base import *


class DevSpecialUser(FASTLiteBase):
    __tablename__ = 'dev_special_user'

    id = Column(Integer, autoincrement=True, primary_key=True)
    prs_id = Column(String(256), comment="")
    rtm = Column(String(256), comment="")
    account_name = Column(String(256), comment="")
    sub_prs_id = Column(String(256), comment="")

    @classmethod
    def get_fake_prs_id(cls, prs_id: str, rtm: str):
        s = FASTLiteSession()
        res = None
        try:
            res = s.query(cls).filter(cls.prs_id == prs_id).filter(cls.rtm == rtm).first()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        if res is None:
            return None
        else:
            return res.sub_prs_id


class SpecialUser(FASTLiteBase):
    __tablename__ = 'special_user'

    id = Column(Integer, autoincrement=True, primary_key=True)
    prs_id = Column(String(256), comment='')
    email_address = Column(String(256), comment='')
    prs_type_code = Column(Integer, comment='')

    def __init__(self, prs_id, email_address, prs_type_code, *args, **kwargs):
        self.prs_id = prs_id
        self.email_address = email_address
        self.prs_type_code = prs_type_code
        super().__init__(*args, **kwargs)

    def insert(self):
        s = FASTLiteSession()
        try:
            data = s.query(SpecialUser).filter(SpecialUser.prs_id == self.prs_id).all()
            if len(data) == 0:
                s.add(self)
                s.commit()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return
