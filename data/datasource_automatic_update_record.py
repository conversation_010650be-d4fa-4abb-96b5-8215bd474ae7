from util.fast_lite_base import *
from sqlalchemy.exc import SQLAlchemyError
from typing import Any


SYSTEM_OPERATOR = "system"

class DatasourceAutomaticUpdateRecord(FASTLiteBase):
    __tablename__ = 'datasource_automatic_update_record'
    __table_args__ = {"extend_existing": True}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    datasource_type = Column(String(32), default='')  # supply_data / esr
    lob = Column(String(16), default='')
    rtm = Column(String(32), default='')
    file_name = Column(String(256), default='')
    fiscal_qtr_week_name = Column(String(32), default='')
    upload_by = Column(String(32), default='')
    trigger_reason = Column(String(128), default='', unique=True)  # 触发原因，唯一键(防重复)：system_weekname / username_timestamp
    status = Column(Integer, default=None)
    params = Column(String(256), default='')
    create_at = Column(DateTime, default=None, comment='create time')
    update_at = Column(DateTime, default=None, comment='update time')

    def __init__(self, fiscal_qtr_week_name, datasource_type, lob, rtm, file_name, params: dict, upload_by, status=0,
                 *args: Any, **kwargs: Any):
        super().__init__(*args, **kwargs)
        self.fiscal_qtr_week_name = fiscal_qtr_week_name
        self.datasource_type = datasource_type
        self.lob = str(lob)
        self.rtm = str(rtm)
        if upload_by == SYSTEM_OPERATOR:
            self.trigger_reason = f"{upload_by}_{fiscal_qtr_week_name}"
        else:
            self.trigger_reason = f"{upload_by}_{time.time()}"
        self.file_name = file_name
        self.params = str(params)
        self.upload_by = upload_by
        self.status = status


# datasource自动化接入数据记录的仓库
class DataSourceFileRecordRepository:
    def __init__(self):
        pass

    @classmethod
    # 数据插入
    def create(cls, record: DatasourceAutomaticUpdateRecord) -> None:
        s = FASTLiteSession()
        try:
            s.add(record)
            s.commit()
            s.close()

        except SQLAlchemyError as e:
            logger.error(f"Record {record} already exists:{e}")
            s.rollback()  # 回滚事务，撤销之前的插入操作

    @classmethod
    def query_latest_archived_by_system(cls, lobs) -> list[DatasourceAutomaticUpdateRecord]:
        s = FASTLiteSession()
        try:
            ret = s.query(DatasourceAutomaticUpdateRecord) \
                .filter(DatasourceAutomaticUpdateRecord.datasource_type == "supply_data") \
                .filter(DatasourceAutomaticUpdateRecord.upload_by == SYSTEM_OPERATOR) \
                .filter(DatasourceAutomaticUpdateRecord.lob.in_(lobs)) \
                .order_by(DatasourceAutomaticUpdateRecord.update_at.desc()) \
                .limit(60)\
                .all()

            return ret
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()

    @classmethod
    def query_record_by_cmd(cls, cmd, lob) -> list[DatasourceAutomaticUpdateRecord]:
        s = FASTLiteSession()
        try:
            ret = s.query(DatasourceAutomaticUpdateRecord) \
                .filter(DatasourceAutomaticUpdateRecord.datasource_type == cmd) \
                .filter(DatasourceAutomaticUpdateRecord.lob == lob) \
                .order_by(DatasourceAutomaticUpdateRecord.create_at.desc()) \
                .all()

            return ret
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()

    @classmethod
    def query_last_record_by_cmd(cls, cmd) -> list[DatasourceAutomaticUpdateRecord]:
        s = FASTLiteSession()
        try:
            ret = s.query(DatasourceAutomaticUpdateRecord.lob, func.max(DatasourceAutomaticUpdateRecord.update_at)) \
                .filter(DatasourceAutomaticUpdateRecord.datasource_type == cmd) \
                .group_by(DatasourceAutomaticUpdateRecord.lob) \
                .all()

            return ret
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()

    @classmethod
    def query_record_by_id(cls, id) -> DatasourceAutomaticUpdateRecord:
        s = FASTLiteSession()
        try:
            ret = s.query(DatasourceAutomaticUpdateRecord) \
                .filter(DatasourceAutomaticUpdateRecord.id == id) \
                .first()
            return ret
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
