from operator import and_
from sqlalchemy import or_, and_


from util.const import *
from util.db import *


class GcDmpMonoPosStatusDi(ReadBase):
    __tablename__ = 'gc_dmp_mono_pos_status_di'
    __table_args__ = {'schema': 'common_service'}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    apple_id = Column(String(64), comment='')
    apple_name = Column(String(64), comment='')
    reseller_id = Column(String(64), comment='')
    reseller_name = Column(String(64), comment='')
    program_name = Column(String(64), comment='')
    fiscal_week_year = Column(String(64), comment='')
    rtm_level_4 = Column(String(64), comment='')
    fiscal_dt = Column(Date, comment='')
    week_begin_dt = Column(Date, comment='')
    week_end_dt = Column(Date, comment='')
    appointed_date = Column(String(64), comment='')
    status = Column(String(64), comment='')
    disti_name = Column(String(64))
    disti_id = Column(String(64))
    temporary_closure_start_date = Column(Date, comment='')
    temporary_closure_end_date = Column(Date, comment='')
    daily_close_status = Column(String(1), comment='')
    wkly_close_status = Column(String(1), comment='')
    create_time = Column(DateTime, comment='create time')
    update_time = Column(DateTime, comment='update time')

    @classmethod
    def get_pre_appointed_stores(self, start_dt: str, end_dt: str) -> list:
        s = ReadSession()
        ret = []
        try:
            ids = s.query(GcDmpMonoPosStatusDi.apple_id.distinct().label('apple_id'))\
                .filter(GcDmpMonoPosStatusDi.fiscal_dt >= start_dt)\
                .filter(GcDmpMonoPosStatusDi.fiscal_dt <= end_dt)\
                .filter(or_(GcDmpMonoPosStatusDi.appointed_date > start_dt, GcDmpMonoPosStatusDi.appointed_date == None))\
                .all()
            for apple_id in ids:
                ret.append(apple_id['apple_id'])
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

    @classmethod
    def get_pre_appointed_stores_num(self, start_dt: str, end_dt: str) -> int:
        s = ReadSession()
        ret = 0
        try:
            ret = s.query(func.count(GcDmpMonoPosStatusDi.apple_id.distinct()))\
                .filter(GcDmpMonoPosStatusDi.fiscal_dt >= start_dt)\
                .filter(GcDmpMonoPosStatusDi.fiscal_dt <= end_dt)\
                .filter(or_(GcDmpMonoPosStatusDi.appointed_date > start_dt, GcDmpMonoPosStatusDi.appointed_date == None))\
                .scalar()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        if ret is None:
            ret = 0
        return ret

    @classmethod
    def get_operating_stores(self, start_dt: str, end_dt: str) -> list:
        s = ReadSession()
        ret = []
        try:
            days = (datetime.strptime(end_dt, DateStrfDay) - datetime.strptime(start_dt, DateStrfDay)).days + 1 

            sub_q = s.query(GcDmpMonoPosStatusDi.apple_id.label('apple_id'), func.count(GcDmpMonoPosStatusDi.id).label('days'))\
                .filter(GcDmpMonoPosStatusDi.fiscal_dt >= start_dt)\
                .filter(GcDmpMonoPosStatusDi.fiscal_dt <= end_dt)\
                .filter(GcDmpMonoPosStatusDi.appointed_date <= start_dt)\
                .filter(GcDmpMonoPosStatusDi.appointed_date != None)\
                .filter(or_(or_(GcDmpMonoPosStatusDi.temporary_closure_start_date == None, GcDmpMonoPosStatusDi.temporary_closure_end_date == None), 
                    and_(or_(GcDmpMonoPosStatusDi.temporary_closure_start_date > start_dt, GcDmpMonoPosStatusDi.temporary_closure_end_date < start_dt), 
                         or_(GcDmpMonoPosStatusDi.temporary_closure_end_date < end_dt, GcDmpMonoPosStatusDi.temporary_closure_start_date > end_dt))))\
                .group_by(GcDmpMonoPosStatusDi.apple_id)\
                .subquery()
            q = s.query(sub_q.c.apple_id).filter(sub_q.c.days >= days)
            ids = q.all()
            for apple_id in ids:
                ret.append(apple_id['apple_id'])
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

    @classmethod
    def get_temp_close_stores(self, start_dt: str, end_dt: str) -> list:
        s = ReadSession()
        ret = []
        try:
            q = s.query(GcDmpMonoPosStatusDi.apple_id.distinct().label('apple_id'))\
                .filter(GcDmpMonoPosStatusDi.fiscal_dt >= start_dt)\
                .filter(GcDmpMonoPosStatusDi.fiscal_dt <= end_dt)\
                .filter(or_(and_(GcDmpMonoPosStatusDi.temporary_closure_start_date <= start_dt, GcDmpMonoPosStatusDi.temporary_closure_end_date >= start_dt), 
                    and_(GcDmpMonoPosStatusDi.temporary_closure_end_date >= end_dt, GcDmpMonoPosStatusDi.temporary_closure_start_date <= end_dt)))
            ids = q.all()
            for apple_id in ids:
                ret.append(apple_id['apple_id'])
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

    @classmethod
    def get_temp_close_stores_num(self, start_dt: str, end_dt: str) -> int:
        s = ReadSession()
        ret = 0
        try:
            ret = s.query(func.count(GcDmpMonoPosStatusDi.apple_id.distinct()))\
                .filter(GcDmpMonoPosStatusDi.fiscal_dt >= start_dt)\
                .filter(GcDmpMonoPosStatusDi.fiscal_dt <= end_dt)\
                .filter(or_(and_(GcDmpMonoPosStatusDi.temporary_closure_start_date <= start_dt, GcDmpMonoPosStatusDi.temporary_closure_end_date >= start_dt), 
                    and_(GcDmpMonoPosStatusDi.temporary_closure_end_date >= end_dt, GcDmpMonoPosStatusDi.temporary_closure_start_date <= end_dt)))\
                .scalar()
            
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        if ret is None:
            ret = 0
        return ret

    @classmethod
    def get_all_pos_count(self, start_dt: str, end_dt: str) -> int:
        s = ReadSession()
        ret = 0
        try:
            ret = s.query(func.count(GcDmpMonoPosStatusDi.apple_id.distinct()))\
                .filter(GcDmpMonoPosStatusDi.fiscal_dt >= start_dt)\
                .filter(GcDmpMonoPosStatusDi.fiscal_dt <= end_dt)\
                .scalar()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        if ret is None:
            ret = 0
        return ret

    @classmethod
    def get_all_not_login_stores(self, start_dt: str, end_dt: str, login_apple_ids: list = [], limit: int = 0) -> list:
        s = ReadSession()
        ret = []
        try:
            q = s.query(GcDmpMonoPosStatusDi.apple_id.label('apple_id'), GcDmpMonoPosStatusDi.apple_name.label('apple_name'), 
                GcDmpMonoPosStatusDi.reseller_id.label('reseller_id'), GcDmpMonoPosStatusDi.reseller_name.label('reseller_name'), 
                GcDmpMonoPosStatusDi.rtm_level_4.label('rtm_level_4'), GcDmpMonoPosStatusDi.program_name.label('program_name'), 
                GcDmpMonoPosStatusDi.disti_name.label('disti_name'), GcDmpMonoPosStatusDi.disti_id.label('disti_id'))\
                .distinct(GcDmpMonoPosStatusDi.apple_id)\
                .filter(GcDmpMonoPosStatusDi.fiscal_dt >= start_dt)\
                .filter(GcDmpMonoPosStatusDi.fiscal_dt <= end_dt)\
                .filter(GcDmpMonoPosStatusDi.apple_id.not_in(login_apple_ids))
            if limit > 0:
                q = q.limit(limit)
            stores = q.all()
            for store in stores:
                ret.append(store._asdict())
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

def get_operating_pos_sub_query(s, start_dt: str, end_dt: str):
    days = (datetime.strptime(end_dt, DateStrfDay) - datetime.strptime(start_dt, DateStrfDay)).days + 1 
    sub_q = s.query(GcDmpMonoPosStatusDi.apple_id.label('apple_id'), func.count(GcDmpMonoPosStatusDi.id).label('days'))\
        .filter(GcDmpMonoPosStatusDi.fiscal_dt >= start_dt)\
        .filter(GcDmpMonoPosStatusDi.fiscal_dt <= end_dt)\
        .filter(GcDmpMonoPosStatusDi.appointed_date <= start_dt)\
        .filter(GcDmpMonoPosStatusDi.appointed_date != None)\
        .filter(or_(or_(GcDmpMonoPosStatusDi.temporary_closure_start_date == None, GcDmpMonoPosStatusDi.temporary_closure_end_date == None), 
            and_(or_(GcDmpMonoPosStatusDi.temporary_closure_start_date > start_dt, GcDmpMonoPosStatusDi.temporary_closure_end_date < start_dt), 
                or_(GcDmpMonoPosStatusDi.temporary_closure_end_date < end_dt, GcDmpMonoPosStatusDi.temporary_closure_start_date > end_dt))))\
        .group_by(GcDmpMonoPosStatusDi.apple_id)\
        .subquery()
    q = s.query(sub_q.c.apple_id.label('store_id')).filter(sub_q.c.days >= days).subquery()
    return q 