from util.const import ErrorExcept, ErrCode
from util.gc_dmp_datasource import *
from util.util import env_dev


class OdsFastCarrierTargetTwoi(ExDmpBase):
    __tablename__ = f"ods_fast_carrier_target_twoi{'_test' if env_dev() else ''}"
    __table_args__ = {"extend_existing": True}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    sold_to_id = Column(Integer, comment='')
    sold_to_name = Column(String(256), comment='')
    lob = Column(String(256), comment='')
    twoi = Column(String(256), comment='')
    version = Column(Integer, default=0, comment='')
    create_date = Column(DateTime, default=datetime.now, comment='create time')
    update_date = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='update time')

    @classmethod
    def query_by_version(cls, version):
        s = ExDmpSession()
        try:
            return s.query(cls).filter(cls.version == version).all()
        except Exception as e:
            logger.debug(str(e))
            raise ErrorExcept(ErrCode.DBQueryError, "db query error: " + str(e))
        finally:
            s.close()

    @classmethod
    def batch_save(cls, datas: list):
        s = ExDmpSession()
        try:
            s.bulk_insert_mappings(cls, datas)
            s.commit()
        except Exception as e:
            logger.debug(str(e))
            raise ErrorExcept(ErrCode.DBInsert, "insert to db failed" + str(e))
        finally:
            s.close()
        return True

