from sqlalchemy import insert
from domain.dashboard.entity.fiscal_week import FiscalWeek
from domain.dashboard.impl.fiscal_week_container import FiscalWeekContainer
from util.const import *
from util.fast_lite_base import *


class FastLiteDashboardThreshold(FASTLiteBase):
    __tablename__ = 'dashboard_ubvelocity_threshold'
    # __table_args__ = 

    start_fiscal_week = Column(Integer, primary_key=True, comment='2024112')
    start_fiscal_week_name = Column(String(256))
    threshold = Column(Float)
    create_time = Column(DateTime)
    update_time = Column(DateTime)

    @classmethod
    def get_threshold(cls, fiscal_week: str) -> float:
        fiscal_week_int = FiscalWeek(fiscal_week).fiscal_week_int
        s = FASTLiteSession()
        ret = None
        try:
            threshold = s.query(cls.threshold).filter(cls.start_fiscal_week <= fiscal_week_int).order_by(
                cls.start_fiscal_week.desc()).first()
            ret = threshold.threshold if threshold else None
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return ret

    @classmethod
    def save_threshold(cls, threshold: float, fiscal_week: str) -> bool:
        start_fiscal_week = FiscalWeek(fiscal_week).fiscal_week_int
        s = FASTLiteSession()
        now = datetime.now()
        try:
            query = f"""
                INSERT INTO {cls.__tablename__} (start_fiscal_week,start_fiscal_week_name, threshold,create_time, update_time)
                VALUES ({start_fiscal_week},'{fiscal_week}',{threshold},'{now}','{now}')
                ON DUPLICATE KEY UPDATE threshold = VALUES(threshold), update_time = VALUES(update_time)
            """
            s.execute(query)
            s.commit()
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        # insert_stmt = insert(FastLiteDashboardThreshold).values(start_fiscal_week = start_fiscal_week,start_fiscal_week_name = fiscal_week,threshold = threshold,create_time = now,update_time = now)
        # on_duplicate_key_stmt = insert_stmt.on_duplicate_key_update({"threshold":threshold,"update_time" : now})
        # s.execute(on_duplicate_key_stmt)
        return True
