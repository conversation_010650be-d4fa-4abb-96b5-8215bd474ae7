from util.const import ErrorExcept, ErrCode
from util.gc_dmp_datasource import *


class ResellerSoldtoMapping(ExDmpBase):
    __tablename__ = f"view_ods_fast_cpf_sold_to_mapping_list_iphone_reseller"
    __table_args__ = {"extend_existing": True,
                      "schema": "gc_dmp_datasource"}

    fiscal_qtr_week_name = Column(String(256), comment='FY23Q1W12')
    fiscal_week_year = Column(Integer, comment='202312', primary_key=True)
    upload_by = Column(String(256))
    upload_at = Column(DateTime)
    region = Column(String(256))
    rtm = Column(String(256))
    business_type = Column(String(256))
    reseller_id = Column(String(256))
    reseller_id_for_carrier = Column(String(256))
    sold_to_id = Column(String(256))
    sold_to_name = Column(String(256))
    sold_to_name_abbre = Column(String(256))
    remark = Column(String(256))
    version = Column(Integer)
    create_time = Column(DateTime)
    update_time = Column(DateTime)

    @classmethod
    def query_soldto_ids_by_reseller_ids(cls, reseller_ids: list[str]) -> list[str]:
        s = ExDmpSession()
        ret = []
        try:
            q = s.query(cls.sold_to_id).filter(cls.reseller_id_for_carrier.in_(reseller_ids))
            ret = q.all()
            ret = [x.sold_to_id for x in ret]
        except Exception as e:
            logger.exception(str(e))
            raise ErrorExcept(ErrCode.DBQueryError, "db query error: " + str(e))
        finally:
            s.close()
        return ret
