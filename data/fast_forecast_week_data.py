from util.const import ExternalViewBeginWeek
from util.gc_dmp_base import *


class FastForecastWeek(GcDmpBase):
    __tablename__ = "fast_forecast_rtm_weeklist_wa"
    __table_args__ = {"extend_existing": True}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    rtm = Column(String(256), comment='')
    fiscal_week_year = Column(Integer, comment='')
    week_date = Column(String(256), comment='')

    @classmethod
    def get_by_rtm(cls, rtm: str, page_num: int, page_size: int):
        s = GcDmpSession()
        res = []
        count = 0
        try:
            query = s.query(
                cls.week_date,
                cls.fiscal_week_year
            )\
                .filter(cls.rtm == rtm) \
                .group_by(cls.fiscal_week_year) \
                .order_by(cls.fiscal_week_year.desc())
            count = query.count()
            res = query.limit(page_size) \
                .offset((page_num - 1) * page_size) \
                .all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return res, count

    @classmethod
    def get_by_rtm_external(cls, rtm: str, page_num: int, page_size: int):
        s = GcDmpSession()
        res = []
        count = 0
        try:
            query = s.query(
                cls.week_date,
                cls.fiscal_week_year
            )\
                .filter(cls.rtm == rtm) \
                .filter(cls.fiscal_week_year >= ExternalViewBeginWeek) \
                .group_by(cls.fiscal_week_year) \
                .order_by(cls.fiscal_week_year.desc())
            count = query.count()
            res = query.limit(page_size) \
                .offset((page_num - 1) * page_size) \
                .all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        # 外部系统最多只能看到14周数据
        if count > 14:
            count = 14
        if ((page_num - 1) * page_size + len(res)) > 14:
            res = res[:14-((page_num - 1) * page_size)]
        return res, count
