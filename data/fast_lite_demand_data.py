from operator import and_
from sqlalchemy import or_, and_, func, desc, asc, union_all
import pandas as pd


from util.const import *
from util.fast_lite_base import *
from data.fast_lite_forecast_data import NewDimFastBusinessSoldtoMapping
from data.fiscal_year_week import FiscalYearWeek

class FastLiteAppFastDemandSummaryWa(FASTLiteBase):
    __tablename__ = 'app_fast_demand_summary_wa'
    __table_args__ = {'schema': 'fast_lite'}
    
    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    fiscal_qtr_week_name = Column(String(256), comment='FY23Q1W12')
    fiscal_week_year = Column(Integer, comment='')
    week_begin_dt = Column(Date, comment='')
    week_end_dt = Column(Date, comment='')
    rtm = Column(String(256), comment='')
    sold_to_id = Column(Integer, comment='')
    lob = Column(String(256), comment='')
    sub_lob = Column(String(256), comment='')
    fph4 = Column(String(256), comment='')
    project_code = Column(String(256), comment='')
    sku = Column(String(256), comment='')
    mpn_id = Column(String(256), comment='')
    demand_cw = Column(Float, comment='')
    demand_cw1 = Column(Float, comment='')
    demand_cw2 = Column(Float, comment='')
    demand_cw3 = Column(Float, comment='')
    shipment_plan_cw = Column(Float, comment='')
    shipment_plan_cw1 = Column(Float, comment='')
    shipment_plan_cw2 = Column(Float, comment='')
    shipment_plan_cw3 = Column(Float, comment='')
    gross_billing_units_cw = Column(Float, comment='')
    sp_remaining_cw1 = Column(Float, comment='')
    sp_remaining_cw2 = Column(Float, comment='')
    top_up_demand_cw1 = Column(Float, comment='')
    top_up_demand_cw2 = Column(Float, comment='')
    top_up_demand_cw3 = Column(Float, comment='')
    top_up_demand_cw4 = Column(Float, comment='')
    po_needed_cw = Column(Float, comment='')
    po_needed_cw1 = Column(Float, comment='')
    po_needed_cw2 = Column(Float, comment='')
    po_needed_cw3 = Column(Float, comment='')

    @classmethod
    def get_fiscal_year_quarter_week_name(self, page_num: int, page_size: int):
        s = FASTLiteSession()
        ret = []
        count = 0
        try:
        
            q = s.query(FastLiteAppFastDemandSummaryWa.fiscal_qtr_week_name.distinct())
            
            count = q.count()
            q = q.order_by(FastLiteAppFastDemandSummaryWa.fiscal_week_year.desc())
            
            if page_num is not None and page_size is not None:
                q = q.limit(page_size).offset(page_num)
                
            ret = q.all()
            
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret, count
    
    
    @classmethod
    def get_total(self, fiscal_qtr_week_name: str, lob: str, model: list, sku: list, rtm: list, business_type: list, sold_to: list, orders: list):
        s = FASTLiteSession()
        ret = []
        try:
            sub_query_demand = s.query(FastLiteAppFastDemandSummaryWa)\
                                .filter(FastLiteAppFastDemandSummaryWa.fiscal_qtr_week_name == fiscal_qtr_week_name)\
                                .filter(FastLiteAppFastDemandSummaryWa.lob == lob)\
                                .filter(FastLiteAppFastDemandSummaryWa.sub_lob.in_(model))
            if sku is not None and len(sku) != 0:
                sub_query_demand = sub_query_demand.filter(FastLiteAppFastDemandSummaryWa.sku.in_(sku))
            sub_query_demand = sub_query_demand.subquery()
            
            q = s.query(NewDimFastBusinessSoldtoMapping.rtm,
                        NewDimFastBusinessSoldtoMapping.business_type.label('business_type'),
                        NewDimFastBusinessSoldtoMapping.sold_to_id.label('sold_to_id'),
                        NewDimFastBusinessSoldtoMapping.abbre.label('sold_to_name'),
                        func.sum(sub_query_demand.c.top_up_demand_cw1).label('top_up_demand_cw1'),
                        func.sum(sub_query_demand.c.top_up_demand_cw2).label('top_up_demand_cw2'),
                        func.sum(sub_query_demand.c.top_up_demand_cw3).label('top_up_demand_cw3'),
                        func.sum(sub_query_demand.c.top_up_demand_cw4).label('top_up_demand_cw4'),
                        func.sum(sub_query_demand.c.shipment_plan_cw).label('shipment_plan_cw'),
                        func.sum(sub_query_demand.c.shipment_plan_cw1).label('shipment_plan_cw1'),
                        func.sum(sub_query_demand.c.shipment_plan_cw2).label('shipment_plan_cw2'),
                        func.sum(sub_query_demand.c.shipment_plan_cw3).label('shipment_plan_cw3'),
                        func.sum(sub_query_demand.c.po_needed_cw).label('po_needed_cw'),
                        func.sum(sub_query_demand.c.po_needed_cw1).label('po_needed_cw1'),
                        func.sum(sub_query_demand.c.po_needed_cw2).label('po_needed_cw2'),
                        func.sum(sub_query_demand.c.po_needed_cw3).label('po_needed_cw3'),
                        )\
                    .outerjoin(sub_query_demand,
                          func.cast(NewDimFastBusinessSoldtoMapping.sold_to_id, Integer) == sub_query_demand.c.sold_to_id)
            if rtm is not None and len(rtm) != 0:
                q = q.filter(NewDimFastBusinessSoldtoMapping.rtm.in_(rtm))
            if business_type is not None and len(business_type) != 0:
                q = q.filter(NewDimFastBusinessSoldtoMapping.business_type.in_(business_type))
            if sold_to is not None and len(sold_to) != 0:
                q = q.filter(NewDimFastBusinessSoldtoMapping.abbre.in_(sold_to))
            
            if orders != []:
                for i in orders:
                    if i['type']:
                        q = q.order_by(desc(i['name']))
                    else:
                        q = q.order_by(asc(i['name']))
                
            data_list = q.all()
            data_structure = {}
            if len(data_list) > 0:
                basic_data = gen_demand_basic_data(data_list[0])
                data_structure = {
                    "rtm": 'Total',
                    "business_type": '',
                    "sold_to_id": '',
                    "sold_to_name": '',
                    **basic_data
                }
                ret.append(data_structure)
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret, data_structure.keys()
    

    @classmethod
    def get_sold_to_total(self, fiscal_qtr_week_name: str, lob: str, model: list, sku: list, 
                          rtm: list, business_type: list, sold_to: list, orders: list,
                          page_num: int, page_size: int):
        s = FASTLiteSession()
        ret = []
        count = 0
        try:
            sub_query_demand = s.query(FastLiteAppFastDemandSummaryWa)\
                                .filter(FastLiteAppFastDemandSummaryWa.fiscal_qtr_week_name == fiscal_qtr_week_name)\
                                .filter(FastLiteAppFastDemandSummaryWa.lob == lob)\
                                .filter(FastLiteAppFastDemandSummaryWa.sub_lob.in_(model))
            if sku is not None and len(sku) != 0:
                sub_query_demand = sub_query_demand.filter(FastLiteAppFastDemandSummaryWa.sku.in_(sku))
            sub_query_demand = sub_query_demand.subquery()
            
            q = s.query(NewDimFastBusinessSoldtoMapping.rtm.label('rtm'),
                        NewDimFastBusinessSoldtoMapping.business_type.label('business_type'),
                        NewDimFastBusinessSoldtoMapping.sold_to_id.label('sold_to_id'),
                        NewDimFastBusinessSoldtoMapping.abbre.label('sold_to_name'),
                        func.sum(sub_query_demand.c.top_up_demand_cw1).label('top_up_demand_cw1'),
                        func.sum(sub_query_demand.c.top_up_demand_cw2).label('top_up_demand_cw2'),
                        func.sum(sub_query_demand.c.top_up_demand_cw3).label('top_up_demand_cw3'),
                        func.sum(sub_query_demand.c.top_up_demand_cw4).label('top_up_demand_cw4'),
                        func.sum(sub_query_demand.c.shipment_plan_cw).label('shipment_plan_cw'),
                        func.sum(sub_query_demand.c.shipment_plan_cw1).label('shipment_plan_cw1'),
                        func.sum(sub_query_demand.c.shipment_plan_cw2).label('shipment_plan_cw2'),
                        func.sum(sub_query_demand.c.shipment_plan_cw3).label('shipment_plan_cw3'),
                        func.sum(sub_query_demand.c.po_needed_cw).label('po_needed_cw'),
                        func.sum(sub_query_demand.c.po_needed_cw1).label('po_needed_cw1'),
                        func.sum(sub_query_demand.c.po_needed_cw2).label('po_needed_cw2'),
                        func.sum(sub_query_demand.c.po_needed_cw3).label('po_needed_cw3'),
                        )\
                    .outerjoin(sub_query_demand,
                          func.cast(NewDimFastBusinessSoldtoMapping.sold_to_id, Integer) == sub_query_demand.c.sold_to_id)
            if rtm is not None and len(rtm) != 0:
                q = q.filter(NewDimFastBusinessSoldtoMapping.rtm.in_(rtm))
            if business_type is not None and len(business_type) != 0:
                q = q.filter(NewDimFastBusinessSoldtoMapping.business_type.in_(business_type))
            if sold_to is not None and len(sold_to) != 0:
                q = q.filter(NewDimFastBusinessSoldtoMapping.abbre.in_(sold_to))    
            
            q = q.group_by(NewDimFastBusinessSoldtoMapping.rtm,
                            NewDimFastBusinessSoldtoMapping.business_type,
                            NewDimFastBusinessSoldtoMapping.sold_to_id,
                            NewDimFastBusinessSoldtoMapping.abbre,)
            
            count = q.count()
            if orders != []:
                for i in orders:
                    if i['type']:
                        q = q.order_by(desc(i['name']))
                    else:
                        q = q.order_by(asc(i['name']))
            else:
                q = q.order_by(func.field(NewDimFastBusinessSoldtoMapping.rtm, *rtm),
                               asc(NewDimFastBusinessSoldtoMapping.business_type),
                               asc(func.cast(NewDimFastBusinessSoldtoMapping.sold_to_id, Integer)))

            data_list = q.limit(page_size).offset(page_num).all()
            for i in data_list:
                basic_data = gen_demand_basic_data(i)
                data_structure = {
                    "rtm": i['rtm'],
                    "business_type": i['business_type'],
                    "sold_to_id": i['sold_to_id'],
                    "sold_to_name": i['sold_to_name'],
                    **basic_data
                }
                ret.append(data_structure)
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret, count
    

    @classmethod
    def get_title(self, fiscal_qtr_week_name: str):
        titles = []
        try:
            # FY23Q1W13
            fiscal_year_quarter_week = fiscal_qtr_week_name.split('W')
            fiscal_year_quarter = fiscal_year_quarter_week[0]
            week = int(fiscal_year_quarter_week[1])
            fiscal_year = fiscal_year_quarter.split('Q')[0]
            quarter = int(fiscal_year_quarter.split('Q')[1])
            year = int(fiscal_year.split('FY')[1])
            
            next_quarter = quarter + 1
            if next_quarter > 4:
                next_quarter = 1
                next_year = year + 1
            else:
                next_year = year
            next_fiscal_year_quarter = 'FY'+str(next_year)+'Q'+str(next_quarter)
            total_weeks = FiscalYearWeek.get_total_weeks_in_quarter(fiscal_year_quarter, week)
            
            next_quarter = quarter + 1
            if next_quarter > 4:
                next_quarter = 1
                next_year = year + 1
            else:
                next_year = year
            
            # 固定标题
            fixed_title = ['RTM', 'Business\nType', 'Customer\nSold-to ID', 'Sold-to Name']

            fiscal_weeks = [] 
            # cw+1/2/3/4
            for k in range(5):
                if k > 0:
                    if week + k > total_weeks:
                        fiscal_weeks.append(f"CW+{k}\n{next_fiscal_year_quarter}W{week + k - total_weeks}")
                    else:
                        fiscal_weeks.append(f"CW+{k}\n{fiscal_year_quarter}W{week + k}")
                else:
                    fiscal_weeks.append(f"CW\n{fiscal_qtr_week_name}")
            
            top_up_title = []
            shipment_plan_title = []
            po_needed_title = []
            for m in range(4):
                top_up_title.append(f"Top-up Demand {fiscal_weeks[m+1]}")
                shipment_plan_title.append(f"Shipment Plan {fiscal_weeks[m]}")
                po_needed_title.append(f"PO Needed {fiscal_weeks[m]}")

            titles = fixed_title+top_up_title+shipment_plan_title+po_needed_title
                
        except Exception as e:
            logger.exception(e)
        return titles


    @classmethod
    def get_download_data(self, fiscal_qtr_week_name: str):
        s = FASTLiteSession()
        ret = []
        try:
            q = s.query(FastLiteAppFastDemandSummaryWa.fiscal_qtr_week_name.label('Week_Date'),
                        NewDimFastBusinessSoldtoMapping.rtm.label('RTM'),
                        NewDimFastBusinessSoldtoMapping.business_type.label('Business Type'),
                        NewDimFastBusinessSoldtoMapping.sold_to_id.label('Customer Sold-to ID'),
                        NewDimFastBusinessSoldtoMapping.sold_to_name_en.label('Sold-to Name'),
                        NewDimFastBusinessSoldtoMapping.abbre.label('Abbre.'),
                        FastLiteAppFastDemandSummaryWa.lob.label('LOB'),
                        FastLiteAppFastDemandSummaryWa.sub_lob.label('Model'),
                        FastLiteAppFastDemandSummaryWa.fph4.label('FPH4'),
                        FastLiteAppFastDemandSummaryWa.project_code.label('Project Code'),
                        FastLiteAppFastDemandSummaryWa.sku.label('SKU'),
                        FastLiteAppFastDemandSummaryWa.mpn_id.label('MPN'),
                        FastLiteAppFastDemandSummaryWa.top_up_demand_cw1.label('Top Up Demand CW+1'),
                        FastLiteAppFastDemandSummaryWa.top_up_demand_cw2.label('Top Up Demand CW+2'),
                        FastLiteAppFastDemandSummaryWa.top_up_demand_cw3.label('Top Up Demand CW+3'),
                        FastLiteAppFastDemandSummaryWa.top_up_demand_cw4.label('Top Up Demand CW+4'),
                        FastLiteAppFastDemandSummaryWa.shipment_plan_cw.label('Shipment Plan CW'),
                        FastLiteAppFastDemandSummaryWa.shipment_plan_cw1.label('Shipment Plan CW+1'),
                        FastLiteAppFastDemandSummaryWa.shipment_plan_cw2.label('Shipment Plan CW+2'),
                        FastLiteAppFastDemandSummaryWa.shipment_plan_cw3.label('Shipment Plan CW+3'),
                        FastLiteAppFastDemandSummaryWa.po_needed_cw.label('PO Needed CW'),
                        FastLiteAppFastDemandSummaryWa.po_needed_cw1.label('PO Needed CW+1'),
                        FastLiteAppFastDemandSummaryWa.po_needed_cw2.label('PO Needed CW+2'),
                        FastLiteAppFastDemandSummaryWa.po_needed_cw3.label('PO Needed CW+3'),
                        )\
                .outerjoin(FastLiteAppFastDemandSummaryWa,
                      and_(func.cast(NewDimFastBusinessSoldtoMapping.sold_to_id, Integer) == func.cast(FastLiteAppFastDemandSummaryWa.sold_to_id, Integer),
                           FastLiteAppFastDemandSummaryWa.fiscal_qtr_week_name == fiscal_qtr_week_name))\
                .order_by(NewDimFastBusinessSoldtoMapping.rtm, 
                          NewDimFastBusinessSoldtoMapping.business_type,
                          func.cast(NewDimFastBusinessSoldtoMapping.sold_to_id, Integer),
                          NewDimFastBusinessSoldtoMapping.abbre,
                          FastLiteAppFastDemandSummaryWa.lob,
                          FastLiteAppFastDemandSummaryWa.sub_lob)
            ret = pd.read_sql_query(q.statement, fast_lite_engine)
            
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret
    

def gen_demand_basic_data(data: dict):
    return {
        "top_up_demand_cw1": data['top_up_demand_cw1'],
        "top_up_demand_cw2": data['top_up_demand_cw2'],
        "top_up_demand_cw3": data['top_up_demand_cw3'],
        "top_up_demand_cw4": data['top_up_demand_cw4'],
        "shipment_plan_cw": data['shipment_plan_cw'],
        "shipment_plan_cw1": data['shipment_plan_cw1'],
        "shipment_plan_cw2": data['shipment_plan_cw2'],
        "shipment_plan_cw3": data['shipment_plan_cw3'],
        "po_needed_cw": data['po_needed_cw'],
        "po_needed_cw1": data['po_needed_cw1'],
        "po_needed_cw2": data['po_needed_cw2'],
        "po_needed_cw3": data['po_needed_cw3'],
    }

