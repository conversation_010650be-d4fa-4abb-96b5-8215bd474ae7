import os

from sqlalchemy import Date, literal, case

from data.email_config import EmailConfigRepository
from domain.dashboard.entity.anti_fraud.ub_wo_ds_wkly import UbWoDsWklyItem, RTMS_RULES, RTMS_SUB_RTM_MAPPING, \
    SUB_LOB_RULES, rtm_dict, sub_lob_dict
from kit.token_bucket import EMAIL_BUCKET_KEY_PREFIX
from util.conf import Column, String, Integer
from util.conf import logger
from util.const import ALL, EmailCmd
from util.email_report_base import EmailReportBase, EmailReportSession
from util.send_email import del_email_config, async_rate_limited_send_email_v2
from util.util import env_dev


class DsUbWoDsWkly(EmailReportBase):
    __tablename__ = "ds_ub_wo_ds_wkly_ds2" if env_dev() else "ds_ub_wo_ds_wkly"
    __table_args__ = {"schema": "email_report"}

    rtm = Column(String(256))
    sub_rtm = Column(String(256))
    prod_group = Column(String(256))
    fiscal_week_year = Column(Integer)
    landing_pod_weekly = Column("Landing_POD_weekly", Integer)
    total_ub_weekly = Column("Total_UB_weekly", Integer)
    ub_without_ds_weekly = Column("UB_without_DS_weekly", Integer)
    landing_pod_accum = Column("Landing_POD_accum", Integer)
    total_ub_accum = Column("Total_UB_accum", Integer)
    ub_without_ds_accum = Column("UB_without_DS_accum", Integer)
    query_date = Column(Date, primary_key=True)
    week_date = Column(String(256))
    total_ub_accum_lq = Column("Total_UB_accum_lq", Integer)
    ub_without_ds_accum_lq = Column("UB_without_DS_accum_lq", Integer)
    fiscal_qtr_year_name = Column(String(256))

    @classmethod
    def query_overall_record(cls, query_date: str, fiscal_week: str) -> list:
        s = EmailReportSession()
        result = []
        white_list: list[UbWoDsWklyItem] = []
        try:
            week_list = (s.query(
                cls.rtm,
                # cls.sub_rtm,
                case([(cls.sub_rtm == 'Mass Merchant', 'MM')], else_=cls.sub_rtm).label('sub_rtm'),
                cls.prod_group.label('sub_lob'),
                cls.query_date,
                cls.week_date,
                cls.total_ub_weekly,
                cls.ub_without_ds_weekly,
                cls.total_ub_accum,
                cls.ub_without_ds_accum,
                cls.total_ub_accum_lq,
                cls.ub_without_ds_accum_lq,
                cls.fiscal_qtr_year_name
            ).filter(cls.query_date == query_date).filter(cls.week_date == fiscal_week).filter(cls.prod_group == ALL).all()
         )
            if not week_list:
                email_config = EmailConfigRepository.query_email_config(EmailCmd.Dashboard_V2)
                params = {"env": os.environ.get('ENV'),
                          "dashboard": 'iPhone EOH Aging & UB Without DS',
                          "source": 'email_report',
                          "message": f"ub_without_ds overall no data: query_date: {query_date}, fiscal_week: {fiscal_week}"
                          }
                file_paths, email_config = del_email_config(email_config=email_config, params=params,
                                                            read_template=False)

                async_rate_limited_send_email_v2(limit_key=EMAIL_BUCKET_KEY_PREFIX + 'ub_without_ds_overall',
                                                 capacity=1,
                                                 expire_time=1200,
                                                 email_config=email_config,
                                                 file_paths=file_paths)
                raise Exception(f"ub_without_ds overall no data: query_date: {query_date}, fiscal_week: {fiscal_week}")

            for item in week_list:
                origin_rtm = item.rtm
                origin_sub_lob = item.sub_lob
                rtm = rtm_dict.get(origin_rtm, origin_rtm)
                sub_lob = sub_lob_dict.get(origin_sub_lob, origin_sub_lob)
                result.append(UbWoDsWklyItem(
                    query_date=item.query_date,
                    rtm=rtm,
                    sub_rtm=item.sub_rtm,
                    sub_lob=sub_lob,
                    week_date=item.week_date,
                    total_ub_accum=item.total_ub_accum,
                    ub_without_ds_accum=item.ub_without_ds_accum,
                    total_ub_weekly=item.total_ub_weekly,
                    ub_without_ds_weekly=item.ub_without_ds_weekly,
                    total_ub_accum_lq=item.total_ub_accum_lq,
                    ub_without_ds_accum_lq=item.ub_without_ds_accum_lq,
                    fiscal_qtr_year_name=item.fiscal_qtr_year_name
                ))

            # 取其中一条季度信息, 用于白名单填充
            fiscal_qtr_year_name = result[0].fiscal_qtr_year_name
            fiscal_qtr_year_name_lq = result[0].fiscal_qtr_year_name_lq

            # 构造一个白名单
            for rtm in RTMS_RULES:
                for sub_rtm in RTMS_SUB_RTM_MAPPING[rtm]:
                    for sub_lob in SUB_LOB_RULES:
                        white_list.append(
                            UbWoDsWklyItem(
                                query_date=query_date,
                                week_date=fiscal_week,
                                rtm=rtm,
                                sub_rtm=sub_rtm,
                                sub_lob=sub_lob,
                                total_ub_accum=0,
                                ub_without_ds_accum=0,
                                total_ub_weekly=0,
                                ub_without_ds_weekly=0,
                                fiscal_qtr_year_name=fiscal_qtr_year_name,
                                total_ub_accum_lq=0,
                                ub_without_ds_accum_lq=0,
                                fiscal_qtr_year_name_lq=fiscal_qtr_year_name_lq
                            )
                        )
            # 根据白名单去set数据
            for white_item in white_list:
                for item in result:
                    if white_item.rtm == item.rtm and white_item.sub_rtm == item.sub_rtm and white_item.sub_lob == item.sub_lob:
                        white_item.set_total_ub_accum(item.total_ub_accum)
                        white_item.set_ub_without_ds_accum(item.ub_without_ds_accum)
                        white_item.set_total_ub_weekly(item.total_ub_weekly)
                        white_item.set_ub_without_ds_weekly(item.ub_without_ds_weekly)

                        white_item.set_total_ub_accum_lq(item.total_ub_accum_lq)
                        white_item.set_ub_without_ds_accum_lq(item.ub_without_ds_accum_lq)
                        white_item.set_fiscal_qtr_year_name(item.fiscal_qtr_year_name)
                        white_item.set_fiscal_qtr_year_name_lq(item.fiscal_qtr_year_name_lq)
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return white_list

    @classmethod
    def query_sub_lob_record(cls, query_date: str, fiscal_week: str) -> list:
        s = EmailReportSession()
        result = []
        white_list: list[UbWoDsWklyItem] = []
        try:
            week_list = (s.query(
                cls.rtm,
                # cls.sub_rtm,
                case([(cls.sub_rtm == 'Mass Merchant', 'MM')], else_=cls.sub_rtm).label('sub_rtm'),
                cls.prod_group.label('sub_lob'),
                cls.query_date,
                cls.week_date,
                cls.total_ub_weekly,
                cls.ub_without_ds_weekly,
                cls.total_ub_accum,
                cls.ub_without_ds_accum,
                cls.total_ub_accum_lq,
                cls.ub_without_ds_accum_lq,
                cls.fiscal_qtr_year_name
            ).filter(cls.query_date == query_date).filter(cls.week_date == fiscal_week).all()
            )

            for item in week_list:
                origin_rtm = item.rtm
                origin_sub_lob = item.sub_lob
                rtm = rtm_dict.get(origin_rtm, origin_rtm)
                sub_lob = sub_lob_dict.get(origin_sub_lob, origin_sub_lob)
                result.append(UbWoDsWklyItem(
                    query_date=item.query_date,
                    rtm=rtm,
                    sub_rtm=item.sub_rtm,
                    sub_lob=sub_lob,
                    week_date=item.week_date,
                    total_ub_accum=item.total_ub_accum,
                    ub_without_ds_accum=item.ub_without_ds_accum,
                    total_ub_weekly=item.total_ub_weekly,
                    ub_without_ds_weekly=item.ub_without_ds_weekly,
                    total_ub_accum_lq=item.total_ub_accum_lq,
                    ub_without_ds_accum_lq=item.ub_without_ds_accum_lq,
                    fiscal_qtr_year_name=item.fiscal_qtr_year_name,
                ))

            if not week_list:
                email_config = EmailConfigRepository.query_email_config(EmailCmd.Dashboard_V2)
                params = {"env": os.environ.get('ENV'),
                          "dashboard": 'iPhone EOH Aging & UB Without DS',
                          "source": 'email_report',
                          "message": f"ub_without_ds sub_lob view no data: query_date: {query_date}, fiscal_week: {fiscal_week}"
                          }
                file_paths, email_config = del_email_config(email_config=email_config, params=params,
                                                            read_template=False)

                async_rate_limited_send_email_v2(limit_key=EMAIL_BUCKET_KEY_PREFIX + 'ub_without_ds_sub_lob',
                                                 capacity=1,
                                                 expire_time=1200,
                                                 email_config=email_config,
                                                 file_paths=file_paths)
                raise Exception(f"ub_without_ds sub_lob view no data: query_date: {query_date}, fiscal_week: {fiscal_week}")

            # 取其中一条季度信息, 用于白名单填充
            fiscal_qtr_year_name = result[0].fiscal_qtr_year_name
            fiscal_qtr_year_name_lq = result[0].fiscal_qtr_year_name_lq

            # 构造一个白名单
            for rtm in RTMS_RULES:
                for sub_rtm in RTMS_SUB_RTM_MAPPING[rtm]:
                    for sub_lob in SUB_LOB_RULES:
                        white_list.append(
                            UbWoDsWklyItem(
                                query_date=query_date,
                                week_date=fiscal_week,
                                rtm=rtm,
                                sub_rtm=sub_rtm,
                                sub_lob=sub_lob,
                                total_ub_accum=0,
                                ub_without_ds_accum=0,
                                total_ub_weekly=0,
                                ub_without_ds_weekly=0,
                                fiscal_qtr_year_name=fiscal_qtr_year_name,
                                total_ub_accum_lq=0,
                                ub_without_ds_accum_lq=0,
                                fiscal_qtr_year_name_lq=fiscal_qtr_year_name_lq
                            )
                        )
            # 根据白名单去set数据
            for white_item in white_list:
                for item in result:
                    if white_item.rtm == item.rtm and white_item.sub_rtm == item.sub_rtm and white_item.sub_lob == item.sub_lob:
                        white_item.set_total_ub_accum(item.total_ub_accum)
                        white_item.set_ub_without_ds_accum(item.ub_without_ds_accum)
                        white_item.set_total_ub_weekly(item.total_ub_weekly)
                        white_item.set_ub_without_ds_weekly(item.ub_without_ds_weekly)

                        white_item.set_total_ub_accum_lq(item.total_ub_accum_lq)
                        white_item.set_ub_without_ds_accum_lq(item.ub_without_ds_accum_lq)
                        white_item.set_fiscal_qtr_year_name(item.fiscal_qtr_year_name)
                        white_item.set_fiscal_qtr_year_name_lq(item.fiscal_qtr_year_name_lq)
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return white_list

    @classmethod
    def query_rolling_weeks(cls, query_date: str, limit: int = 5):
        s = EmailReportSession()
        rolling_weeks = []
        try:
            filter_params = []
            if query_date:
                filter_params.append(cls.query_date == query_date)

            q = (s.query(cls.fiscal_week_year, cls.week_date).distinct()
                 .filter(*filter_params)
                 .order_by(cls.fiscal_week_year.desc())
                 .limit(limit))
            query_result = q.all()
            # 降序查询最新n周，再将数组逆序转换为正常升序数组
            rolling_weeks = [week['week_date'] for week in query_result]
            rolling_weeks.reverse()
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return rolling_weeks
