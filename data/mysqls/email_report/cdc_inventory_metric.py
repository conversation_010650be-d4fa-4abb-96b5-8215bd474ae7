import os

from sqlalchemy import func, case

from data.email_config import EmailConfigRepository
from domain.dashboard.entity.anti_fraud.cdc_inventory_metric import CDCInventoryMetric
from domain.dashboard.entity.anti_fraud.ub_wo_ds_wkly import UbWoDsWklyItem, RTMS_RULES, RTMS_SUB_RTM_MAPPING, \
    SUB_LOB_RULES, SUB_LOB_RULES_NORMAL, sub_lob_dict
from kit.token_bucket import EMAIL_BUCKET_KEY_PREFIX
from util.conf import Column, String, Integer
from util.conf import logger
from util.const import ALL, EmailCmd
from util.email_report_base import EmailReportBase, EmailReportSession
from util.send_email import del_email_config, async_rate_limited_send_email_v2
from util.util import env_dev


class CdcInventoryMetricRepo(EmailReportBase):
    __tablename__ = "dws_cdc_inventory_metric_use_latest_timestamp_ds2" if env_dev() else "dws_cdc_inventory_metric_use_latest_timestamp"
    __table_args__ = {"schema": "email_report"}

    rtm = Column(String(256))
    sub_rtm = Column(String(256))
    lob = Column(String(256))
    sub_lob_cluster = Column(String(256))
    landing_qtd = Column(Integer)
    landing_eoh = Column(Integer)
    age_gre35 = Column(Integer)
    age_leq35 = Column(Integer)
    age_leq1weeks = Column(Integer)
    age_1to2weeks = Column(Integer)
    age_2to3weeks = Column(Integer)
    age_3to4weeks = Column(Integer)
    age_4to5weeks = Column(Integer)
    age_5to6weeks = Column(Integer)
    age_6to7weeks = Column(Integer)
    age_7to8weeks = Column(Integer)
    age_8to9weeks = Column(Integer)
    age_9to10weeks = Column(Integer)
    age_gre70 = Column(Integer)
    query_datetime = Column(String(256), primary_key=True)
    query_date = Column(String(32), primary_key=True)
    query_timestamp = Column(Integer)
    page = Column(String(256))

    @classmethod
    def query(cls, query_date: str, page:str = 'page1') -> list[CDCInventoryMetric]:
        s = EmailReportSession()
        result = []
        white_list: list[CDCInventoryMetric] = []
        try:
            sub_query = s.query(func.max(cls.query_timestamp)).subquery()
            result = (s.query(
                cls.rtm,
                # cls.sub_rtm,
                case([(cls.sub_rtm == 'Mass Merchant', 'MM')], else_=cls.sub_rtm).label('sub_rtm'),
                cls.sub_lob_cluster.label('sub_lob'),
                cls.landing_qtd,
                cls.landing_eoh,
                cls.age_gre35,
                cls.age_leq35,
                cls.age_leq1weeks,
                cls.age_1to2weeks,
                cls.age_2to3weeks,
                cls.age_3to4weeks,
                cls.age_4to5weeks,
                cls.age_5to6weeks,
                cls.age_6to7weeks,
                cls.age_7to8weeks,
                cls.age_8to9weeks,
                cls.age_9to10weeks,
                cls.age_gre70,
            ).filter(cls.query_date == query_date).filter(
                cls.page == page).filter(cls.query_timestamp == sub_query).order_by(cls.rtm, cls.sub_rtm, cls.sub_lob_cluster).all()
                   )

            if not result:
                email_config = EmailConfigRepository.query_email_config(EmailCmd.Dashboard_V2)
                params = {"env": os.environ.get('ENV'),
                          "dashboard": 'iPhone EOH Aging & UB Without DS',
                          "source": 'email_report',
                          "message": f"eoh_aging view no data: query_date: {query_date}, page: {page}"
                          }
                file_paths, email_config = del_email_config(email_config=email_config, params=params,
                                                            read_template=False)

                async_rate_limited_send_email_v2(limit_key=EMAIL_BUCKET_KEY_PREFIX + 'eoh_aging',
                                                 capacity=1,
                                                 expire_time=1200,
                                                 email_config=email_config,
                                                 file_paths=file_paths)
                raise Exception(
                    f"eoh_aging view no data: query_date: {query_date}, page: {page}")

            # 构造一个白名单
            for rtm in RTMS_RULES:
                for sub_rtm in RTMS_SUB_RTM_MAPPING[rtm]:
                    for sub_lob in SUB_LOB_RULES:
                        white_list.append(
                            CDCInventoryMetric(
                                rtm=rtm,
                                sub_rtm=sub_rtm,
                                sub_lob=sub_lob
                            )
                        )
            # 根据白名单去set数据
            for white_item in white_list:
                for item in result:
                    rtm = item.rtm
                    if item.rtm == 'Monobrand':
                        rtm = 'Mono Brand'
                    elif item.rtm == 'Multibrand':
                        rtm = 'Multi Brand'
                    origin_sub_lob = item.sub_lob
                    sub_lob = sub_lob_dict.get(origin_sub_lob, origin_sub_lob)
                    if white_item.rtm == rtm and white_item.sub_rtm == item.sub_rtm and white_item.sub_lob == sub_lob:
                        white_item.set_data(item.landing_qtd, item.landing_eoh, item.age_gre35, item.age_leq35,
                                            item.age_leq1weeks,item.age_1to2weeks, item.age_2to3weeks, item.age_3to4weeks, item.age_4to5weeks,
                                            item.age_5to6weeks, item.age_6to7weeks, item.age_7to8weeks, item.age_8to9weeks, item.age_9to10weeks,
                                            item.age_gre70)

        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return white_list
