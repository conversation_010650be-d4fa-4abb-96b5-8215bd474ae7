import decimal
from datetime import datetime

from sqlalchemy import Column, Integer, String, Float, case, func, text, DateTime

from domain.end_to_end.entity.demand_forecast_entity import DemandForecastItem
from util.conf import logger
from util.const import ErrorExcept, ErrCode
from util.mybusiness_base import MyBusinessBase, MyBusinessSession


class MybusinessForecastFeedbackView(MyBusinessBase):
    __tablename__ = 'view_forecast_feedback'    # 20250314 多版本控制后，RTM Leader页面的汇总只需要使用最新version的数据进行汇总就行
    __table_args__ = {"schema": "mybusiness"}

    id = Column(Integer, primary_key=True, autoincrement=True, comment='ID')
    version = Column(Integer, comment='')
    fiscal_week_year = Column(Integer, comment='')
    fiscal_qtr_week_name = Column(String(16), comment='')
    reseller_id = Column(String(255), comment='')
    reseller_name = Column(String(255), comment='')
    reseller_tier = Column(String(255), comment='')
    sold_to_id = Column(String(255), comment='')
    sold_to_name = Column(String(255), comment='')
    rtm = Column(String(32), comment='')
    sub_rtm = Column(String(32), comment='')
    lob = Column(String(16), comment='')
    sub_lob = Column(String(32), comment='')
    mpn_id = Column(String(32), comment='')
    mpn_type = Column(String(32), comment='')
    mpn_desc = Column(String(255), comment='')
    mpn_order = Column(Integer, comment='')
    forecast_cw = Column(Integer, comment='')
    forecast_cw1 = Column(Integer, comment='')
    forecast_cw2 = Column(Integer, comment='')
    forecast_cw3 = Column(Integer, comment='')
    forecast_cw4 = Column(Integer, comment='')
    forecast_cw5 = Column(Integer, comment='')
    forecast_cw6 = Column(Integer, comment='')
    forecast_cw7 = Column(Integer, comment='')
    forecast_cw8 = Column(Integer, comment='')
    forecast_cw9 = Column(Integer, comment='')
    forecast_cw10 = Column(Integer, comment='')
    forecast_cw11 = Column(Integer, comment='')
    forecast_cw12 = Column(Integer, comment='')
    ub_eoh_lw = Column(Integer, comment='')
    final_demand_cw1 = Column(Integer, comment='')
    final_demand_cw2 = Column(Integer, comment='')
    available_po_cw1 = Column(Integer, comment='')
    available_po_cw2 = Column(Integer, comment='')
    po_needed_cw1 = Column(Integer, comment='')
    po_needed_cw2 = Column(Integer, comment='')
    shipment_plan_cw = Column(Integer, comment='')
    shipment_plan_cw1 = Column(Integer, comment='')
    shipment_plan_cw2 = Column(Integer, comment='')
    open_backlog_over_published_sp = Column(Integer, comment='')
    twos = Column(Float, comment='')
    twos_cw1 = Column(Float, comment='')
    twos_cw2 = Column(Float, comment='')
    adjusted_forecast_cw = Column(Integer, comment='')
    adjusted_forecast_cw1 = Column(Integer, comment='')
    adjusted_forecast_cw2 = Column(Integer, comment='')
    adjusted_forecast_cw3 = Column(Integer, comment='')
    adjusted_forecast_cw4 = Column(Integer, comment='')
    adjusted_forecast_cw5 = Column(Integer, comment='')
    adjusted_forecast_cw6 = Column(Integer, comment='')
    adjusted_forecast_cw7 = Column(Integer, comment='')
    adjusted_forecast_cw8 = Column(Integer, comment='')
    adjusted_forecast_cw9 = Column(Integer, comment='')
    adjusted_forecast_cw10 = Column(Integer, comment='')
    adjusted_forecast_cw11 = Column(Integer, comment='')
    adjusted_forecast_cw12 = Column(Integer, comment='')
    trial_demand_cw1 = Column(Float, comment='')
    trial_demand_cw2 = Column(Float, comment='')
    trial_po_needed_cw1 = Column(Float, comment='')
    trial_po_needed_cw2 = Column(Float, comment='')
    create_time = Column(DateTime, default=datetime.now, comment='create time')
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='update time')

    @classmethod
    def get_sublob_data_by_rollup(cls, fiscal_week: str, sub_lob: str, version: int = None, resellser_id: str = '', rtm: str = '') -> list[DemandForecastItem]:
        s = MyBusinessSession()

        def get_sum_value(query_result):
            '''转换 字符串输出为int'''
            return int(query_result) if isinstance(query_result, decimal.Decimal) else query_result

        try:
            q = (s.query(
                cls.lob, cls.sub_lob,
                func.max(cls.update_time).label('update_time'),
                # case([(func.grouping(cls.mpn_type) == 1, 'All')], else_=cls.mpn_type).label('nand'),
                case([(func.grouping(cls.mpn_desc) == 1, 'All')], else_=cls.mpn_desc).label('color'),
                # case([(func.grouping(cls.nand) == 1, 'All')], else_=cls.nand).label('nand'),
                # case([(func.grouping(cls.color) == 1, 'All')], else_=cls.color).label('color'),
                cls.mpn_id,
                # cls.mpn_order,
                # case([(func.grouping(cls.mpn_desc) == 1, 0)], else_=cls.mpn_order).label('mpn_order'),  # All 汇总时排第一位
                case([(func.grouping(cls.mpn_desc) == 1, 0)], else_=func.max(cls.mpn_order)).label('mpn_order'), # All 汇总时排第一位， 切换视图后，得用max去取mpn_order的原值
                func.sum(cls.ub_eoh_lw).label('ub_eoh_lw'),
                func.sum(cls.shipment_plan_cw).label('shipment_plan_cw'),
                func.sum(cls.shipment_plan_cw1).label('shipment_plan_cw1'),
                func.sum(cls.shipment_plan_cw2).label('shipment_plan_cw2'),
                func.sum(cls.final_demand_cw1).label('final_demand_cw1'),
                func.sum(cls.trial_demand_cw1).label('trial_demand_cw1'),
                func.sum(cls.final_demand_cw2).label('final_demand_cw2'),
                func.sum(cls.trial_demand_cw2).label('trial_demand_cw2'),
                func.sum(cls.po_needed_cw1).label('po_needed_cw1'),
                func.sum(cls.trial_po_needed_cw1).label('trial_po_needed_cw1'),
                func.sum(cls.po_needed_cw2).label('po_needed_cw2'),
                func.sum(cls.trial_po_needed_cw2).label('trial_po_needed_cw2'),
                func.sum(cls.forecast_cw).label('forecast_cw'),
                func.sum(cls.adjusted_forecast_cw).label('adjusted_forecast_cw'),
                func.sum(cls.forecast_cw1).label('forecast_cw1'),
                func.sum(cls.adjusted_forecast_cw1).label('adjusted_forecast_cw1'),
                func.sum(cls.forecast_cw2).label('forecast_cw2'),
                func.sum(cls.adjusted_forecast_cw2).label('adjusted_forecast_cw2'),
                func.sum(cls.forecast_cw3).label('forecast_cw3'),
                func.sum(cls.adjusted_forecast_cw3).label('adjusted_forecast_cw3'),
                func.sum(cls.forecast_cw4).label('forecast_cw4'),
                func.sum(cls.adjusted_forecast_cw4).label('adjusted_forecast_cw4'),
                func.sum(cls.forecast_cw5).label('forecast_cw5'),
                func.sum(cls.adjusted_forecast_cw5).label('adjusted_forecast_cw5'),
                func.sum(cls.forecast_cw6).label('forecast_cw6'),
                func.sum(cls.adjusted_forecast_cw6).label('adjusted_forecast_cw6'),
                func.sum(cls.forecast_cw7).label('forecast_cw7'),
                func.sum(cls.adjusted_forecast_cw7).label('adjusted_forecast_cw7'),
                func.sum(cls.forecast_cw8).label('forecast_cw8'),
                func.sum(cls.adjusted_forecast_cw8).label('adjusted_forecast_cw8'),
                func.sum(cls.forecast_cw9).label('forecast_cw9'),
                func.sum(cls.adjusted_forecast_cw9).label('adjusted_forecast_cw9'),
                func.sum(cls.forecast_cw10).label('forecast_cw10'),
                func.sum(cls.adjusted_forecast_cw10).label('adjusted_forecast_cw10'),
                func.sum(cls.forecast_cw11).label('forecast_cw11'),
                func.sum(cls.adjusted_forecast_cw11).label('adjusted_forecast_cw11'),
                func.sum(cls.forecast_cw12).label('forecast_cw12'),
                func.sum(cls.adjusted_forecast_cw12).label('adjusted_forecast_cw12')
            ).filter(cls.fiscal_qtr_week_name == fiscal_week))
            if resellser_id:
                q = q.filter(cls.reseller_id == resellser_id)
            if rtm:
                q = q.filter(cls.rtm == rtm)
            if version:
                q = q.filter(cls.version == version)
            if sub_lob:
                q = q.filter(cls.sub_lob == sub_lob)
            # .group_by(text('sub_lob, nand, color WITH ROLLUP')).having(func.grouping(cls.sub_lob) == 0)
            q = q.group_by(text('sub_lob, mpn_desc WITH ROLLUP')).having(func.grouping(cls.sub_lob) == 0)

            # having(func.grouping(cls.sub_lob) == 0) 过滤掉 sub_lob 字段为汇总行的记录
            # print(f'>>>{q.statement.compile(compile_kwargs={"literal_binds": True}).string}')
            ret = q.all()
            return [
                DemandForecastItem(
                    update_time=item.update_time.strftime('%Y-%m-%d %H:%M:%S'),
                    lob=item.lob,
                    sub_lob=item.sub_lob,
                    nand='',
                    color=item.color,
                    mpn=item.mpn_id,
                    mpn_order=item.mpn_order,
                    ub_eoh=get_sum_value(item.ub_eoh_lw),
                    shipment_plan_cw=get_sum_value(item.shipment_plan_cw),
                    shipment_plan_cw1=get_sum_value(item.shipment_plan_cw1),
                    shipment_plan_cw2=get_sum_value(item.shipment_plan_cw2),
                    demand_origin_cw1=get_sum_value(item.final_demand_cw1),
                    demand_target_cw1=get_sum_value(item.trial_demand_cw1),
                    demand_origin_cw2=get_sum_value(item.final_demand_cw2),
                    demand_target_cw2=get_sum_value(item.trial_demand_cw2),
                    po_needed_cw1=get_sum_value(item.po_needed_cw1),
                    target_po_needed_cw1=get_sum_value(item.trial_po_needed_cw1),
                    po_needed_cw2=get_sum_value(item.po_needed_cw2),
                    target_po_needed_cw2=get_sum_value(item.trial_po_needed_cw2),
                    forecast_origin_cw=get_sum_value(item.forecast_cw),
                    forecast_target_cw=get_sum_value(item.adjusted_forecast_cw),
                    forecast_origin_cw1=get_sum_value(item.forecast_cw1),
                    forecast_target_cw1=get_sum_value(item.adjusted_forecast_cw1),
                    forecast_origin_cw2=get_sum_value(item.forecast_cw2),
                    forecast_target_cw2=get_sum_value(item.adjusted_forecast_cw2),
                    forecast_origin_cw3=get_sum_value(item.forecast_cw3),
                    forecast_target_cw3=get_sum_value(item.adjusted_forecast_cw3),
                    forecast_origin_cw4=get_sum_value(item.forecast_cw4),
                    forecast_target_cw4=get_sum_value(item.adjusted_forecast_cw4),
                    forecast_origin_cw5=get_sum_value(item.forecast_cw5),
                    forecast_target_cw5=get_sum_value(item.adjusted_forecast_cw5),
                    forecast_origin_cw6=get_sum_value(item.forecast_cw6),
                    forecast_target_cw6=get_sum_value(item.adjusted_forecast_cw6),
                    forecast_origin_cw7=get_sum_value(item.forecast_cw7),
                    forecast_target_cw7=get_sum_value(item.adjusted_forecast_cw7),
                    forecast_origin_cw8=get_sum_value(item.forecast_cw8),
                    forecast_target_cw8=get_sum_value(item.adjusted_forecast_cw8),
                    forecast_origin_cw9=get_sum_value(item.forecast_cw9),
                    forecast_target_cw9=get_sum_value(item.adjusted_forecast_cw9),
                    forecast_origin_cw10=get_sum_value(item.forecast_cw10),
                    forecast_target_cw10=get_sum_value(item.adjusted_forecast_cw10),
                    forecast_origin_cw11=get_sum_value(item.forecast_cw11),
                    forecast_target_cw11=get_sum_value(item.adjusted_forecast_cw11),
                    forecast_origin_cw12=get_sum_value(item.forecast_cw12),
                    forecast_target_cw12=get_sum_value(item.adjusted_forecast_cw12),
                ) for item in ret]
        except Exception as e:
            logger.exception(e)
            raise ErrorExcept(ErrCode.DBQueryError, '查询数据库失败')
        finally:
            s.close()
