from datetime import datetime

from sqlalchemy import Column, Integer, String, DateTime

from util.conf import logger
from util.mybusiness_base import MyBusinessBase, MyBusinessSession


class MybusinessModuleSwitch(MyBusinessBase):
    __tablename__ = 'module_switch'
    __table_args__ = {"schema": "mybusiness"}

    id = Column(Integer, primary_key=True, autoincrement=True, comment='ID')
    module = Column(String(255), comment='')
    begin_date = Column(String(255), comment='')
    end_date = Column(String(255), comment='')
    create_time = Column(DateTime, default=datetime.now, comment='create time')
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='update time')
    description = Column(String(255), comment='')

    @classmethod
    def query_date_by_module(cls, module: str):
        s = MyBusinessSession()
        try:
            result = s.execute(f'''
                                   SELECT *
                                   FROM {cls.__tablename__}
                                   WHERE module = '{module}';
                                   ''').first()
            return result
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
