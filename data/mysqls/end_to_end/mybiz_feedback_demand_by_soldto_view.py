from typing import Optional
import pandas as pd
from sqlalchemy import Column, Integer, String, Float, PrimaryKeyConstraint, DateTime

from util.conf import logger
from util.const import ErrorExcept, ErrCode
from util.mybusiness_base import MyBusinessBase, MyBusinessSession


class MybizDemandFeedbackBySoldToView(MyBusinessBase):
    __tablename__ = 'feedback_demand_by_soldto_view'
    __table_args__ = (
        PrimaryKeyConstraint('fiscal_qtr_week_name', 'sold_to_id', 'mpn_id'),  # 定义组合主键
        {"schema": "mybusiness"}
    )

    fiscal_week_year = Column(Integer, comment='fiscal_week_year')
    fiscal_qtr_week_name = Column(String(32), comment='fiscal_qtr_week_name')
    reseller_id = Column(Integer, comment='reseller_id')
    reseller_name = Column(String(256), comment='reseller_name')
    reseller_tier = Column(String(32), comment='reseller_tier')
    sold_to_id = Column(Integer, comment='sold_to_id')
    sold_to_name = Column(String(256), comment='sold_to_name')
    rtm = Column(String(32), comment='rtm')
    sub_rtm = Column(String(32), comment='sub_rtm')
    lob = Column(String(32), comment='lob')
    sub_lob = Column(String(32), comment='sub_lob')
    mpn_id = Column(String(32), comment='mpn_id')
    mpn_type = Column(String(32), comment='mpn_type')
    mpn_desc = Column(String(256), comment='mpn_desc')
    mpn_order = Column(Integer, comment='mpn_order')
    nand = Column(String(32), comment='nand')
    color = Column(String(32), comment='color')
    shipment_plan_cw = Column(Float, comment='shipment_plan_cw')
    shipment_plan_cw1 = Column(Float, comment='shipment_plan_cw1')
    shipment_plan_cw2 = Column(Float, comment='shipment_plan_cw2')
    ub_eoh_lw = Column(Integer, comment='ub_eoh_lw')
    trial_demand_cw1 = Column(Float, comment='trial_demand_cw1') # Demand 2.0 CW1
    trial_demand_cw2 = Column(Float, comment='trial_demand_cw2') # Demand 2.0 CW1
    finalized_demand_v2_cw1 = Column(Float, comment='finalized_demand_v2_cw1') # Demand 3.0 CW1
    finalized_demand_v2_cw2 = Column(Float, comment='finalized_demand_v2_cw2') # Demand 3.0 CW2
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    publish_status = Column(Integer)
    publish_time = Column(DateTime)

    @classmethod
    def get_data_df(cls, fiscal_week_name: str, lob: str = 'iPhone', sub_lobs: list[str] = None, nand: list[str] = None,
                    color: list[str] = None, rtm: str = None, published_status: int = None,
                    reseller_ids: Optional[list[str]] = None) -> pd.DataFrame:
        s = MyBusinessSession()
        try:
            filter_params = [cls.fiscal_qtr_week_name == fiscal_week_name, cls.lob == lob]
            if sub_lobs:
                filter_params.append(cls.sub_lob.in_(sub_lobs))
            if nand:
                filter_params.append(cls.nand.in_(nand))
            if color:
                filter_params.append(cls.color.in_(color))
            if rtm:
                filter_params.append(cls.rtm == rtm)
            if published_status is not None:
                filter_params.append(cls.publish_status == published_status)
            if reseller_ids is not None:
                filter_params.append(cls.reseller_id.in_(reseller_ids))
            q = (s.query(cls)
                 .filter(*filter_params))
            return pd.read_sql(q.statement, s.bind)
        except Exception as e:
            logger.exception(e)
            raise ErrorExcept(ErrCode.DBQueryError, f'{cls.__tablename__}查询数据库失败')
        finally:
            s.close()
