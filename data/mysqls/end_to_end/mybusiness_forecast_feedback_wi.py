import decimal
from datetime import datetime

from sqlalchemy import Column, Integer, String, Float, case, func, text, DateTime, literal, union_all

from data.databend.gc_dmp_directship_databend_base import DirectshipDatabendSession
from data.databend.gc_dmp_mystore_mybiz import MyStoreMyBizDatabendSession
from domain.end_to_end.entity.demand_forecast_entity import DemandForecastItem
from util.conf import logger
from util.conf import Base
from util.util import env_dev


class MybusinessForecastFeedbackWi(Base):
    __tablename__ = 'app_mybiz_fast_forecast_feedback_reference_wi'    # 时间窗关闭后， RTM Leader页面的汇总使用数据组兜底的feedback数据
    __table_args__ = {"schema": "test_db" if env_dev() else "gc_dmp_mystore_mybiz"}

    fiscal_week_year = Column(Integer,  primary_key=True, comment='')
    fiscal_qtr_week_name = Column(String(16), comment='')
    rtm = Column(String(32), comment='')
    lob = Column(String(16), comment='')
    sub_lob = Column(String(32), comment='')
    mpn_id = Column(String(32), comment='')
    mpn_desc = Column(String(255), comment='')
    mpn_order = Column(Integer, comment='')
    forecast_cw = Column(Integer, comment='')
    forecast_cw1 = Column(Integer, comment='')
    forecast_cw2 = Column(Integer, comment='')
    forecast_cw3 = Column(Integer, comment='')
    forecast_cw4 = Column(Integer, comment='')
    forecast_cw5 = Column(Integer, comment='')
    forecast_cw6 = Column(Integer, comment='')
    forecast_cw7 = Column(Integer, comment='')
    forecast_cw8 = Column(Integer, comment='')
    forecast_cw9 = Column(Integer, comment='')
    forecast_cw10 = Column(Integer, comment='')
    forecast_cw11 = Column(Integer, comment='')
    forecast_cw12 = Column(Integer, comment='')
    final_demand_cw1 = Column(Float, comment='')
    final_demand_cw2 = Column(Float, comment='')
    po_needed_cw1 = Column(Float, comment='')
    po_needed_cw2 = Column(Float, comment='')
    ub_eoh_lw = Column(Integer, comment='')
    shipment_plan_cw = Column(Float, comment='')
    shipment_plan_cw1 = Column(Float, comment='')
    shipment_plan_cw2 = Column(Float, comment='')
    adjusted_forecast_cw = Column(Integer, comment='')
    adjusted_forecast_cw1 = Column(Integer, comment='')
    adjusted_forecast_cw2 = Column(Integer, comment='')
    adjusted_forecast_cw3 = Column(Integer, comment='')
    adjusted_forecast_cw4 = Column(Integer, comment='')
    adjusted_forecast_cw5 = Column(Integer, comment='')
    adjusted_forecast_cw6 = Column(Integer, comment='')
    adjusted_forecast_cw7 = Column(Integer, comment='')
    adjusted_forecast_cw8 = Column(Integer, comment='')
    adjusted_forecast_cw9 = Column(Integer, comment='')
    adjusted_forecast_cw10 = Column(Integer, comment='')
    adjusted_forecast_cw11 = Column(Integer, comment='')
    adjusted_forecast_cw12 = Column(Integer, comment='')
    trial_demand_cw1 = Column(Float, comment='')
    trial_demand_cw2 = Column(Float, comment='')
    trial_po_needed_cw1 = Column(Float, comment='')
    trial_po_needed_cw2 = Column(Float, comment='')
    publish_time = Column(DateTime)
    create_time = Column(DateTime, default=datetime.now, comment='create time')
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='update time')

    @classmethod
    def __get_session(cls):
        if env_dev():
            return DirectshipDatabendSession()
        else:
            return MyStoreMyBizDatabendSession()

    @classmethod
    def get_sublob_data_by_rollup(cls, fiscal_week: str, sub_lob: str, rtm: str = '') -> list[DemandForecastItem]:
        s = cls.__get_session()

        def get_sum_value(query_result):
            '''转换 字符串输出为int, 针对的是decimal类型的'''
            return int(float(query_result)) if isinstance(query_result, str) else query_result

        try:
            filter_params = [cls.fiscal_qtr_week_name == fiscal_week]
            if rtm:
                filter_params.append(cls.rtm == rtm)
            if sub_lob:
                filter_params.append(cls.sub_lob == sub_lob)
            q = s.query(
                    cls.lob.label('lob'),
                    cls.sub_lob.label('sub_lob'),
                    cls.update_time.label('update_time'),
                    cls.mpn_desc.label('color'),
                    cls.mpn_id.label('mpn_id'),
                    cls.mpn_order.label('mpn_order'),
                    cls.ub_eoh_lw.label('ub_eoh_lw'),
                    cls.shipment_plan_cw.label('shipment_plan_cw'),
                    cls.shipment_plan_cw1.label('shipment_plan_cw1'),
                    cls.shipment_plan_cw2.label('shipment_plan_cw2'),
                    cls.final_demand_cw1.label('final_demand_cw1'),
                    cls.trial_demand_cw1.label('trial_demand_cw1'),
                    cls.final_demand_cw2.label('final_demand_cw2'),
                    cls.trial_demand_cw2.label('trial_demand_cw2'),
                    cls.po_needed_cw1.label('po_needed_cw1'),
                    cls.trial_po_needed_cw1.label('trial_po_needed_cw1'),
                    cls.po_needed_cw2.label('po_needed_cw2'),
                    cls.trial_po_needed_cw2.label('trial_po_needed_cw2'),
                    cls.forecast_cw.label('forecast_cw'),
                    cls.adjusted_forecast_cw.label('adjusted_forecast_cw'),
                    cls.forecast_cw1.label('forecast_cw1'),
                    cls.adjusted_forecast_cw1.label('adjusted_forecast_cw1'),
                    cls.forecast_cw2.label('forecast_cw2'),
                    cls.adjusted_forecast_cw2.label('adjusted_forecast_cw2'),
                    cls.forecast_cw3.label('forecast_cw3'),
                    cls.adjusted_forecast_cw3.label('adjusted_forecast_cw3'),
                    cls.forecast_cw4.label('forecast_cw4'),
                    cls.adjusted_forecast_cw4.label('adjusted_forecast_cw4'),
                    cls.forecast_cw5.label('forecast_cw5'),
                    cls.adjusted_forecast_cw5.label('adjusted_forecast_cw5'),
                    cls.forecast_cw6.label('forecast_cw6'),
                    cls.adjusted_forecast_cw6.label('adjusted_forecast_cw6'),
                    cls.forecast_cw7.label('forecast_cw7'),
                    cls.adjusted_forecast_cw7.label('adjusted_forecast_cw7'),
                    cls.forecast_cw8.label('forecast_cw8'),
                    cls.adjusted_forecast_cw8.label('adjusted_forecast_cw8'),
                    cls.forecast_cw9.label('forecast_cw9'),
                    cls.adjusted_forecast_cw9.label('adjusted_forecast_cw9'),
                    cls.forecast_cw10.label('forecast_cw10'),
                    cls.adjusted_forecast_cw10.label('adjusted_forecast_cw10'),
                    cls.forecast_cw11.label('forecast_cw11'),
                    cls.adjusted_forecast_cw11.label('adjusted_forecast_cw11'),
                    cls.forecast_cw12.label('forecast_cw12'),
                    cls.adjusted_forecast_cw12.label('adjusted_forecast_cw12')
            ).filter(*filter_params)

            ret = q.query_list()
            return [
                DemandForecastItem(
                    update_time=item["update_time"].strftime('%Y-%m-%d %H:%M:%S'),
                    lob=item["lob"],
                    sub_lob=item["sub_lob"],
                    nand='',
                    color=item["color"],
                    mpn=item["mpn_id"],
                    mpn_order=item["mpn_order"],
                    ub_eoh=get_sum_value(item["ub_eoh_lw"]),
                    shipment_plan_cw=get_sum_value(item["shipment_plan_cw"]),
                    shipment_plan_cw1=get_sum_value(item["shipment_plan_cw1"]),
                    shipment_plan_cw2=get_sum_value(item["shipment_plan_cw2"]),
                    demand_origin_cw1=get_sum_value(item["final_demand_cw1"]),
                    demand_target_cw1=get_sum_value(item["trial_demand_cw1"]),
                    demand_origin_cw2=get_sum_value(item["final_demand_cw2"]),
                    demand_target_cw2=get_sum_value(item["trial_demand_cw2"]),
                    po_needed_cw1=get_sum_value(item["po_needed_cw1"]),
                    target_po_needed_cw1=get_sum_value(item["trial_po_needed_cw1"]),
                    po_needed_cw2=get_sum_value(item["po_needed_cw2"]),
                    target_po_needed_cw2=get_sum_value(item["trial_po_needed_cw2"]),
                    forecast_origin_cw=get_sum_value(item["forecast_cw"]),
                    forecast_target_cw=get_sum_value(item["adjusted_forecast_cw"]),
                    forecast_origin_cw1=get_sum_value(item["forecast_cw1"]),
                    forecast_target_cw1=get_sum_value(item["adjusted_forecast_cw1"]),
                    forecast_origin_cw2=get_sum_value(item["forecast_cw2"]),
                    forecast_target_cw2=get_sum_value(item["adjusted_forecast_cw2"]),
                    forecast_origin_cw3=get_sum_value(item["forecast_cw3"]),
                    forecast_target_cw3=get_sum_value(item["adjusted_forecast_cw3"]),
                    forecast_origin_cw4=get_sum_value(item["forecast_cw4"]),
                    forecast_target_cw4=get_sum_value(item["adjusted_forecast_cw4"]),
                    forecast_origin_cw5=get_sum_value(item["forecast_cw5"]),
                    forecast_target_cw5=get_sum_value(item["adjusted_forecast_cw5"]),
                    forecast_origin_cw6=get_sum_value(item["forecast_cw6"]),
                    forecast_target_cw6=get_sum_value(item["adjusted_forecast_cw6"]),
                    forecast_origin_cw7=get_sum_value(item["forecast_cw7"]),
                    forecast_target_cw7=get_sum_value(item["adjusted_forecast_cw7"]),
                    forecast_origin_cw8=get_sum_value(item["forecast_cw8"]),
                    forecast_target_cw8=get_sum_value(item["adjusted_forecast_cw8"]),
                    forecast_origin_cw9=get_sum_value(item["forecast_cw9"]),
                    forecast_target_cw9=get_sum_value(item["adjusted_forecast_cw9"]),
                    forecast_origin_cw10=get_sum_value(item["forecast_cw10"]),
                    forecast_target_cw10=get_sum_value(item["adjusted_forecast_cw10"]),
                    forecast_origin_cw11=get_sum_value(item["forecast_cw11"]),
                    forecast_target_cw11=get_sum_value(item["adjusted_forecast_cw11"]),
                    forecast_origin_cw12=get_sum_value(item["forecast_cw12"]),
                    forecast_target_cw12=get_sum_value(item["adjusted_forecast_cw12"]),
                ) for item in ret]
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
