from typing import Dict, Any, List

from sqlalchemy import Column, Integer, String, Float, DateTime

from domain.end_to_end.entity.response_data import UserSetting
from util.conf import logger
from util.const import ErrorExcept, ErrCode
from util.fast_lite_base import FASTLiteBase, FASTLiteSession


class RangeLevelSetting(FASTLiteBase):
    __tablename__ = 'range_level_setting'
    __table_args__ = {"schema": "fast_lite"}

    id = Column(Integer, primary_key=True, autoincrement=True, comment='ID')
    rtm = Column(String(32), comment='')
    sub_rtm = Column(String(32), comment='')
    fiscal_week = Column(Integer, comment='')
    fiscal_week_name = Column(String(16), comment='')
    lob = Column(String(16), comment='')
    sub_lob = Column(String(32), comment='')
    range_level_cw = Column(Float, comment='')
    range_level_cw1 = Column(Float, comment='')
    range_level_cw2 = Column(Float, comment='')
    range_level_cw3 = Column(Float, comment='')
    range_level_cw4 = Column(Float, comment='')
    range_level_cw5 = Column(Float, comment='')
    range_level_cw6 = Column(Float, comment='')
    range_level_cw7 = Column(Float, comment='')
    range_level_cw8 = Column(Float, comment='')
    range_level_cw9 = Column(Float, comment='')
    range_level_cw10 = Column(Float, comment='')
    range_level_cw11 = Column(Float, comment='')
    range_level_cw12 = Column(Float, comment='')
    quantile_created_by = Column(String(64), comment='')
    quantile_updated_by = Column(String(64), comment='')
    quantile_create_time = Column(DateTime, comment='')
    quantile_update_time = Column(DateTime, comment='')
    quantile_is_published = Column(Integer, comment='是否发布 0:未发布 1:已发布')
    quantile_publish_time = Column(DateTime, comment='')
    variance_created_by = Column(String(64), comment='')
    variance_updated_by = Column(String(64), comment='')
    variance_create_time = Column(DateTime, comment='')
    variance_update_time = Column(DateTime, comment='')
    variance_is_published = Column(Integer, comment='是否发布 0:未发布 1:已发布')
    variance_publish_time = Column(DateTime, comment='')

    @classmethod
    def get_settings(cls, fiscal_week_name: str, lob: str = "iPhone", quantile_is_published: int = None,
                     variance_is_published: int = None, sub_lob: str = None) -> List[UserSetting]:
        s = FASTLiteSession()
        try:
            res = s.query(cls).filter(cls.fiscal_week_name == fiscal_week_name, cls.lob == lob)
            if sub_lob:
                res = res.filter(cls.sub_lob == sub_lob)
            if quantile_is_published is not None:
                res = res.filter(cls.quantile_is_published == quantile_is_published)
            if variance_is_published is not None:
                res = res.filter(cls.variance_is_published == variance_is_published)
            ret = res.all()
            return [
                UserSetting(
                    fiscal_week_name=item.fiscal_week_name,
                    rtm=item.rtm,
                    sub_rtm=item.sub_rtm,
                    sub_lob=item.sub_lob,
                    range_level_cw=item.range_level_cw,
                    range_level_cw1=item.range_level_cw1,
                    range_level_cw2=item.range_level_cw2,
                    range_level_cw3=item.range_level_cw3,
                    range_level_cw4=item.range_level_cw4,
                    range_level_cw5=item.range_level_cw5,
                    range_level_cw6=item.range_level_cw6,
                    range_level_cw7=item.range_level_cw7,
                    range_level_cw8=item.range_level_cw8,
                    range_level_cw9=item.range_level_cw9,
                    range_level_cw10=item.range_level_cw10,
                    range_level_cw11=item.range_level_cw11,
                    range_level_cw12=item.range_level_cw12,
                    update_time=item.quantile_publish_time.strftime('%Y-%m-%d %H:%M:%S') if item.quantile_publish_time else None
                ) for item in ret]
        except Exception as e:
            logger.exception(e)
            raise ErrorExcept(ErrCode.DBQueryError, '查询数据库失败')
        finally:
            s.close()

    @classmethod
    def get_quantile_publish_time(cls, fiscal_week_name: str, lob: str):
        s = FASTLiteSession()
        try:
            # 查询最大publish_time
            latest_publish = s.query(cls.quantile_publish_time).filter(cls.fiscal_week_name == fiscal_week_name,
                                                              cls.lob == lob,
                                                              cls.quantile_is_published == 1).order_by(cls.quantile_publish_time.desc()).first()
            latest_publish = latest_publish[0] if latest_publish else None
            return latest_publish
        except Exception as e:
            logger.exception(e)
            raise ErrorExcept(ErrCode.DBQueryError, '查询数据库失败')
        finally:
            s.close()

    @classmethod
    def get_variance_publish_time(cls, fiscal_week_name: str, lob: str):
        s = FASTLiteSession()
        try:
            # 查询最大publish_time
            latest_publish = s.query(cls.variance_publish_time).filter(cls.fiscal_week_name == fiscal_week_name,
                                                              cls.lob == lob,
                                                              cls.variance_is_published == 1).order_by(cls.variance_publish_time.desc()).first()
            latest_publish = latest_publish[0] if latest_publish else None
            return latest_publish
        except Exception as e:
            logger.exception(e)
            raise ErrorExcept(ErrCode.DBQueryError, '查询数据库失败')
        finally:
            s.close()

    @classmethod
    def update_setting(cls, objs: List[Dict[str, Any]], fields_to_insert: List[str], fields_to_update: List[str]):
        s = FASTLiteSession()
        try:
            insert_parts = [f"{field}" for field in fields_to_insert]
            value_parts = [f":{field}" for field in fields_to_insert]
            key_parts = [f"{field} = VALUES({field})" for field in fields_to_update]
            insert_stmt = f"""
                                INSERT INTO {cls.__tablename__} 
                                (fiscal_week_name, sub_lob, sub_rtm,  {', '.join(insert_parts)})
                                 VALUES 
                                    (:fiscal_week_name, :sub_lob, :sub_rtm,  {', '.join(value_parts)})
                                ON DUPLICATE KEY UPDATE 
                                     {', '.join(key_parts)}
                            """
            s.execute(insert_stmt, objs)
            s.commit()
        except Exception as e:
            s.rollback()
            logger.exception(e)
            raise ErrorExcept(ErrCode.DBInsert, '更新数据库失败')
        finally:
            s.close()
