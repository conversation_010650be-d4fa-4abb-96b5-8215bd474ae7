import traceback

from sqlalchemy import desc

from domain.datasource.entity.automatic.file_info import AutomaticFileInfoEntity
from util.const import ErrorExcept, ErrCode
from util.fast_lite_base import FASTLiteBase, FASTLiteSession
from util.mono_allocation_base import *


class AutomaticFileInfo(FASTLiteBase):
    __tablename__ = "automatic_file_info"
    __table_args__ = {'schema': 'fast_lite'}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    bus_key = Column(String(255), comment='')
    data_type = Column(String(256))
    db_name = Column(String(256))
    snapshot_dt = Column(Date)
    sync_time = Column(DateTime)
    rtm_view = Column(String(256))
    fiscal_qtr_week_name = Column(String(256))
    table_name = Column(String(256))
    snapshot_ts = Column(DateTime)
    file_name = Column(String(255), comment='')
    file_path = Column(String(255), comment='')
    create_time = Column(DateTime)
    update_time = Column(DateTime)

    @classmethod
    def bulk_save(cls, objs: list):
        # 一次写入所有数据
        s = FASTLiteSession()
        ret = []
        try:
            s.execute(cls.__table__.insert(), objs)
            s.commit()
        except Exception as e:
            logger.exception(e)
            raise ErrorExcept(ErrCode.DBInsert, "insert to db failed" +
                              traceback.format_exc() + "params:" + str(objs))
        finally:
            s.close()
        return ret

    @classmethod
    def query_file_info_by_bus_key(cls, bus_key: str):
        s = FASTLiteSession()
        try:
            # 重新计算的话，会生成bus_key一样的文件, 所以取id最大的那一个
            ret = s.query(cls).filter(cls.bus_key == bus_key).order_by(desc(cls.id)).all()
            if ret:
                ret = ret[0]
        except Exception as e:
            logger.exception(str(e))
            raise ErrorExcept(ErrCode.DBQueryParams, "query file info failed" + str(e))
        finally:
            s.close()
        return ret

    @classmethod
    def query_sync_status_records(cls,
                                  table_name: str = None,
                                  snapshot_ts: str = None,
                                  snapshot_dt: str = None,
                                  fiscal_qtr_week_name: str = None,
                                  data_type: str = None,
                                  db_name: str = None,
                                  rtm_view: str = None) -> list[AutomaticFileInfoEntity]:
        s = FASTLiteSession()
        res = []
        try:
            filter_params = []
            if table_name:
                filter_params.append(cls.table_name == table_name)
            if snapshot_ts:
                filter_params.append(cls.snapshot_ts == snapshot_ts)
            if data_type:
                filter_params.append(cls.data_type == data_type)
            if db_name:
                filter_params.append(cls.db_name == db_name)
            if rtm_view:
                filter_params.append(cls.rtm_view == rtm_view)
            if snapshot_dt:
                filter_params.append(cls.snapshot_dt == snapshot_dt)
            if fiscal_qtr_week_name:
                filter_params.append(cls.fiscal_qtr_week_name == fiscal_qtr_week_name)
            query_data = s.query(cls).filter(*filter_params).order_by(cls.sync_time.desc()).all()
            for item in query_data:
                res.append(
                    AutomaticFileInfoEntity(
                        bus_key=item.bus_key,
                        fiscal_qtr_week_name=item.fiscal_qtr_week_name,
                        table_name=item.table_name,
                        snapshot_ts=item.snapshot_ts,
                        file_name=item.file_name,
                        file_path=item.file_path,
                        data_type=item.data_type,
                        db_name=item.db_name,
                        snapshot_dt=item.snapshot_dt,
                        sync_time=item.sync_time,
                        rtm_view=item.rtm_view
                    )
                )

        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return res

    @classmethod
    def query_records_exist(cls, table_name: str = None, snapshot_ts: str = None, snapshot_dt: str = None,
                            fiscal_qtr_week_name: str = None, data_type: str = None, db_name: str = None,
                            rtm_view: str = None,
                            sync_time: str = None):
        s = FASTLiteSession()
        try:
            res = None
            filter_params = []
            if table_name:
                filter_params.append(cls.table_name == table_name)
            if snapshot_ts:
                filter_params.append(cls.snapshot_ts == snapshot_ts)
            if data_type:
                filter_params.append(cls.data_type == data_type)
            if db_name:
                filter_params.append(cls.db_name == db_name)
            if rtm_view:
                filter_params.append(cls.rtm_view == rtm_view)
            if snapshot_dt:
                filter_params.append(cls.snapshot_dt == snapshot_dt)
            if sync_time:
                filter_params.append(cls.sync_time == sync_time)
            if fiscal_qtr_week_name:
                filter_params.append(cls.fiscal_qtr_week_name == fiscal_qtr_week_name)
            item = s.query(cls).filter(*filter_params).order_by(cls.snapshot_ts.desc()).first()
            if item:
                res = AutomaticFileInfoEntity(
                        bus_key=item.bus_key,
                        fiscal_qtr_week_name=item.fiscal_qtr_week_name,
                        table_name=item.table_name,
                        snapshot_ts=item.snapshot_ts,
                        file_name=item.file_name,
                        file_path=item.file_path,
                        data_type=item.data_type,
                        db_name=item.db_name,
                        snapshot_dt=item.snapshot_dt,
                        sync_time=item.sync_time,
                        rtm_view=item.rtm_view
                    )
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return res

    @classmethod
    def query_group_records(cls):
        s = FASTLiteSession()
        res = []
        try:
            records = (s.query(func.max(cls.sync_time).label("sync_time"),
                               cls.bus_key,
                               cls.fiscal_qtr_week_name,
                               cls.data_type,
                               cls.table_name,
                               cls.rtm_view,
                               cls.file_name,
                               cls.file_path,
                               cls.db_name,
                               cls.snapshot_dt,
                               cls.snapshot_ts)
                    .group_by(cls.data_type, cls.table_name, cls.rtm_view)
                    .all())
            for record in records:
                res.append(
                    AutomaticFileInfoEntity(
                        bus_key=record.bus_key,
                        fiscal_qtr_week_name=record.fiscal_qtr_week_name,
                        table_name=record.table_name,
                        snapshot_ts=record.snapshot_ts,
                        file_name=record.file_name,
                        file_path=record.file_path,
                        data_type=record.data_type,
                        db_name=record.db_name,
                        snapshot_dt=record.snapshot_dt,
                        sync_time=record.sync_time,
                        rtm_view=record.rtm_view
                    )
                )
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return res

    @classmethod
    def query_file_info_by_file_path(cls, file_path: str):
        s = FASTLiteSession()
        try:
            ret = s.query(cls).filter(cls.file_path == file_path).order_by(desc(cls.id)).all()
            if ret:
                ret = ret[0]
        except Exception as e:
            logger.exception(str(e))
            raise ErrorExcept(ErrCode.DBQueryParams, "query file info failed" + str(e))
        finally:
            s.close()
        return ret
