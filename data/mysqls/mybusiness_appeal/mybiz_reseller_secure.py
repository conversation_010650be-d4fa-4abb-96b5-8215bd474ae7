from util.const import ErrorExcept, ErrCode
from util.mybusiness_reseller_base import *


class MybizResellerSecure(MybusinessSecureBase):
    __tablename__ = "mybiz_reseller_secure"
    __table_args__ = {"schema": "mybusiness_secure"}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    reseller_id = Column(String(16))
    email = Column(String(256))

    @classmethod
    def get_email_by_reseller_id(cls, reseller_id: str):
        s = MybusinessSecureSession()
        ret = None
        try:
            email_list = s.query(cls.reseller_id, cls.email) .filter(cls.reseller_id == reseller_id).all()
            if email_list:
                ret = email_list[0]
        except Exception as e:
            logger.error(e)
            raise ErrorExcept(ErrCode.DBQueryError, "db query error: " + str(e))
        finally:
            s.close()
        return ret

    @classmethod
    def get_email_list_by_reseller_id(cls, reseller_id: str) -> list:
        s = MybusinessSecureSession()
        try:
            email_list = s.query(cls.reseller_id, cls.email).filter(cls.reseller_id == reseller_id).all()
        except Exception as e:
            logger.error(e)
            raise ErrorExcept(ErrCode.DBQueryError, "db query error: " + str(e))
        finally:
            s.close()
        return email_list
