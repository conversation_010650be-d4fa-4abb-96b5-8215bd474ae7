from sqlalchemy import UniqueConstraint

from util.fast_lite_base import *
from util.const import ErrorExcept, ErrCode
from domain.dashboard.entity.const import DASHBOARD_FILE_CATEGORY_UPLOAD


class DemandFileRecord(FASTLiteBase):
    __tablename__ = "demand_file_record"
    __table_args__ = {"schema": "fast_lite"}

    id = Column(Integer, autoincrement=True, primary_key=True, comment="ID")
    fiscal_qtr_week_name = Column(String(32), comment="FY23Q1W12")
    category = Column(SmallInteger, comment="0:template; 1:upload")
    module = Column(String(256), comment="mpn_mix;")
    file_path = Column(String(256))
    file_name = Column(String(256))
    unique_name = Column(String(256))
    operator = Column(String(256))
    file_version = Column(Integer, server_default="1")
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    UniqueConstraint(fiscal_qtr_week_name, category, module,  name="UK_week_category_module")

    def __init__(
        self,
        fiscal_qtr_week_name: str,
        category: int,
        module: str,
        file_path: str,
        file_name: str,
        unique_name: str,
        operator: str,
    ):
        self.fiscal_qtr_week_name = fiscal_qtr_week_name
        self.category = category
        self.module = module
        self.file_path = file_path
        self.file_name = file_name
        self.unique_name = unique_name
        self.operator = operator

    def save(self) -> int:
        s = FASTLiteSession()
        file_name = ""
        try:
            s.add(self)
            s.commit()
            file_name = self.file_name
        except IntegrityError as e:
            file_name = self.update(
                {
                    "fiscal_qtr_week_name": self.fiscal_qtr_week_name,
                    "category": self.category,
                    "module": self.module,
                    "file_name": self.file_name,
                    "file_path": self.file_path,
                    "unique_name": self.unique_name,
                    "operator": self.operator,
                }
            )
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return file_name

    @classmethod
    def update(cls, update_data: dict):
        s = FASTLiteSession()
        file_name = ""
        try:
            raw_record = (
                s.query(cls)
                .filter(
                    cls.fiscal_qtr_week_name == update_data.get("fiscal_qtr_week_name")
                )
                .filter(cls.category == update_data.get("category"))
                .filter(cls.module == update_data.get("module"))
                .one()
            )
            file_version = raw_record.file_version
            file_name = update_data.get("file_name")
            if update_data.get("category") == DASHBOARD_FILE_CATEGORY_UPLOAD and file_name:
                file_version = raw_record.file_version + 1
                # 暂不需要文件名中加version后缀
                # file_name = f"_{file_version}.".join(file_name.split("."))

            s.query(cls).filter(cls.id == raw_record.id).update(
                {
                    "file_path": update_data.get("file_path"),
                    "file_name": file_name,
                    "unique_name": update_data.get("unique_name"),
                    "file_version": file_version,
                    "operator": update_data.get("operator"),
                }
            )
            s.commit()
            logger.info(f"update {cls.__tablename__}")
        except Exception as e:
            logger.info(e)
        finally:
            s.close()

        return file_name

    @classmethod
    def get_file_by_week_category_module(
        cls, fiscal_qtr_week_name: str, category: int, module: str
    ) -> list:
        s = FASTLiteSession()
        ret = []
        try:
            ret = (
                s.query(cls)
                .filter(cls.fiscal_qtr_week_name == fiscal_qtr_week_name)
                .filter(cls.category == category)
                .filter(cls.module == module)
                .all()
            )
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

    @classmethod
    def get_file_by_id(cls, file_id: int):
        s = FASTLiteSession()
        ret = {}
        try:
            ret = s.query(cls).filter(cls.id == file_id).one_or_none()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret


class MixUpload(FASTLiteBase):
    __tablename__ = "dashboard_mix_upload"
    __table_args__ = {"schema": "fast_lite"}

    id = Column(Integer, autoincrement=True, primary_key=True, comment="ID")
    fiscal_qtr_week_name = Column(String(256), comment="FY23Q1W12")
    region = Column(String(256))
    lob = Column(String(256))
    sub_lob = Column(String(256))
    nand = Column(String(256))
    color = Column(String(256))
    mix = Column(Float)
    mpn = Column(String(16))
    create_time = Column(DateTime)
    update_time = Column(DateTime)

    @classmethod
    def bulk_save(cls, datas: list):
        s = FASTLiteSession()
        try:
            s.execute(cls.__table__.insert(), datas)
            s.commit()
        except Exception as e:
            logger.info(str(e))
            raise ErrorExcept(ErrCode.DBInsert, "insert to db failed" + str(e))
        finally:
            s.close()
        return True

    @classmethod
    def delete_by_week(cls, fiscal_qtr_week_name: str):
        s = FASTLiteSession()
        try:
            q = s.query(cls).filter(cls.fiscal_qtr_week_name == fiscal_qtr_week_name)
            q.delete()
            s.commit()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()

    @classmethod
    def get_adjust_mix(cls, fiscal_week: str, region: str, lob: str, sub_lobs)->list:
        try:
            s = FASTLiteSession()
            obj = s.query(
                cls.fiscal_qtr_week_name,
                cls.region,
                cls.lob,
                cls.sub_lob,
                cls.color,
                cls.nand,
                cls.mix,
                cls.mpn,
            )
            obj = obj.filter(cls.fiscal_qtr_week_name == fiscal_week)
            obj = obj.filter(cls.region == region)
            if lob:
                obj = obj.filter(cls.lob == lob)
            if sub_lobs and len(sub_lobs) > 0:
                obj = obj.filter(cls.sub_lob.in_(sub_lobs))

            mixs = (obj.all())
            ret = []
            for item in mixs:
                ret.append(
                    {
                        "Fiscal Week": item.fiscal_qtr_week_name,
                        "Region": item.region,
                        "LOB": item.lob,
                        "Sub-LOB": item.sub_lob,
                        "Color": item.color,
                        "Nand": item.nand,
                        "Adjusted Mix%": item.mix,
                        "Mpn": item.mpn
                    }
                )
            return ret
        except Exception as e:
            logger.error(f"get_adjust_mix error {e}")
            return []
