from domain.dashboard.entity.forecast_vs_actual import Condition, get_obj_attr_by_str
from util.fast_lite_base import *


class ForecastAccuracyThreshold(FASTLiteBase):
    __tablename__ = "forecast_accuracy_threshold"
    __table_args__ = {"schema": "fast_lite"}

    id = Column(Integer, autoincrement=True, primary_key=True, comment="ID")
    forecast_version = Column(String(16))
    sub_lob = Column(String(32))
    nand = Column(String(16))
    color = Column(String(64))
    rtm = Column(String(16))
    sub_rtm = Column(String(64))
    threshold = Column(Float)

    @classmethod
    def get_all_threshold(cls) -> list:
        s = FASTLiteSession()
        try:
            ret = s.query(cls).all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

    @classmethod
    def get_threshold_by_codition(cls, condition: dict) -> list:
        s = FASTLiteSession()
        ret = []
        try:
            q = s.query(cls)
            for field, value in condition.items():
                if value is None:
                    continue
                q = q.filter(get_obj_attr_by_str(cls, field) == value)
            ret = q.all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

    @classmethod
    def get_threshold(
        cls,
        forecast_version: str,
        sub_lob: str,
        nand: str,
        color: str,
        rtm: str,
        sub_rtm: str,
    ) -> list:
        s = FASTLiteSession()
        ret = []
        try:
            ret = (
                s.query(cls)
                .filter(
                    cls.forecast_version == forecast_version,
                    cls.sub_lob == sub_lob,
                    cls.nand == nand,
                    cls.color == color,
                    cls.rtm == rtm,
                    cls.sub_rtm == sub_rtm,
                )
                .all()
            )
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret
