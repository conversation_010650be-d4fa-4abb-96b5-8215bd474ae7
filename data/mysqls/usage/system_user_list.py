from domain.mono.pos_allocation.entity.pos_eoh import Pos<PERSON>oh
from domain.usage.entity.user_info import UserInfo
from util.const import ErrorExcept, ErrCode
from util.gc_dmp_systemusage_base import UsageSession
from util.mono_allocation_base import *


class SystemUserList(MonoAllocationBase):
    __tablename__ = "app_system_user_list_wa"
    __table_args__ = {"schema": "gc_dmp_systemusage",
                      "extend_existing": True}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    fiscal_qtr_week_name = Column(String(255), comment='')
    product = Column(String(255), comment='')
    person_id = Column(Integer, comment='')
    user_name = Column(String(255), comment='')
    email = Column(String(255), comment='')
    rtm = Column(String(255), comment='')
    is_key_user = Column(Integer, comment='')
    account_type = Column(String(255), comment='')
    create_time = Column(DateTime, default=datetime.now, comment='create time')
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='update time')

    @classmethod
    def query_by_fiscal_week(cls, fiscal_week, product_list) -> list[UserInfo]:
        s = UsageSession()
        ret = []
        try:
            query_result = s.query(cls).filter(cls.fiscal_qtr_week_name == fiscal_week).filter(cls.product.in_(product_list)).all()
            for item in query_result:
                ret.append(UserInfo(user_name=item.user_name,
                                    email=item.email,
                                    rtm=item.rtm,
                                    is_key_user=item.is_key_user,
                                    product=item.product,
                                    person_id=item.person_id))
        except Exception as e:
            logger.exception(e)
            raise ErrorExcept(ErrCode.DBQueryError, "db query error: " + str(e))
        finally:
            s.close()
        return ret

