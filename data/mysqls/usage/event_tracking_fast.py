from operator import and_

from sqlalchemy import and_

from domain.usage.entity.step_info import StepInfo
from util.common_service_db import CommonServiceSession
from util.db import *


class EventTrackingFastDaily(ReadBase):
    __tablename__ = 'event_tracking_fast'
    __table_args__ = {'schema': 'common_service'}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    fiscal_dt = Column(Date, comment='')
    fiscal_week = Column(String(64), comment='')
    begin_day_of_week = Column(Date, comment='')
    end_day_of_week = Column(Date, comment='')
    calendar_month = Column(Integer, comment='')
    product_name = Column(String(64), comment='')
    step = Column(String(64), comment='')
    au = Column(Float, comment='')
    au_conversion = Column(Float, comment='')
    au_key = Column(Float, comment='')
    au_key_conversion = Column(Float, comment='')
    date_type = Column(Integer, comment='')

    create_time = Column(DateTime, default=datetime.now, comment='create time')
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='update time')

    @classmethod
    def bulk_insert(cls, data: list[dict]):
        s = CommonServiceSession()
        try:
            s.bulk_insert_mappings(cls, data)
            s.commit()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()

    @classmethod
    def get_week_data(cls, begin_date, end_date) -> list[StepInfo]:
        s = CommonServiceSession()
        ret = []
        try:
            query_result = s.query(cls).filter(and_(cls.begin_day_of_week >= begin_date, cls.begin_day_of_week <= end_date)).filter(cls.date_type==2).all()
            for item in query_result:
                ret.append(StepInfo(
                    product_name=item.product_name,
                    step=item.step,
                    au=item.au,
                    au_key=item.au_key)
                )
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

    @classmethod
    def remove_data(cls, date_type, fiscal_dt: str = None, begin_day_of_week: str = None, calendar_month: int = None):
        s = CommonServiceSession()
        try:
            query = s.query(cls).filter(cls.date_type == date_type)
            if fiscal_dt:
                query = query.filter(cls.fiscal_dt == fiscal_dt)
            if begin_day_of_week:
                query = query.filter(cls.begin_day_of_week == begin_day_of_week)
            if calendar_month:
                query = query.filter(cls.calendar_month == calendar_month)
            query.delete()
            s.commit()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()

