from domain.usage.entity.user_info import UserInfo
from util.common_service_db import CommonServiceSession
from util.const import ErrorExcept, ErrCode
from util.db import *


class EventTrackingInternal(ReadBase):
    __tablename__ = 'event_tracking_internal'
    __table_args__ = {'schema': 'common_service'}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    fiscal_dt = Column(String(64), comment='')
    is_deleted = Column(Integer, comment='')
    create_time = Column(DateTime, comment='')
    update_time = Column(DateTime, comment='')
    uuid = Column(String(64), comment='')
    event_name = Column(String(64), comment='')
    event_type = Column(String(64), comment='')
    app_name = Column(String(64), comment='')
    app_version = Column(String(64), comment='')
    os = Column(String(64), comment='')
    client_type = Column(String(64), comment='')
    client_time = Column(String(64), comment='')
    store_id = Column(String(64), comment='')
    page_name = Column(String(64), comment='')
    customized_type = Column(String(64), comment='')
    customized_lob = Column(String(64), comment='')
    customized_id = Column(String(64), comment='')
    person_id = Column(String(64), comment='')
    prs_type_code = Column(String(64), comment='')
    host_url = Column(String(64), comment='')
    enter_from = Column(String(64), comment='')
    value = Column(Integer, comment='')
    event_time = Column(Integer, comment='')
    extra = Column(String(64), comment='')
    rtm = Column(String(64), comment='')
    sub_rtm = Column(String(64), comment='')
    status = Column(Integer, comment='')
    rtm_type = Column(String(64), comment='')

    @classmethod
    def query_by_date(cls, app_name: str, event_names: list[str], start_date, end_date, user_list: list[UserInfo]) -> list[UserInfo]:
        s = CommonServiceSession()
        try:
            query = (s.query(cls).filter(cls.app_name == app_name)
                     .filter(cls.event_name.in_(event_names))
                     .filter(cls.create_time.between(start_date, end_date)))
            query_result = query.all()
            for user in user_list:
                for item in query_result:
                    if str(user.person_id) == item.person_id:
                        user.event_times += 1
        except Exception as e:
            logger.exception(e)
            raise ErrorExcept(ErrCode.DBQueryError, "db query error: " + str(e))
        finally:
            s.close()
        # 返回user_list中的event_times不为0的元素
        return [user for user in user_list if user.event_times != 0]
