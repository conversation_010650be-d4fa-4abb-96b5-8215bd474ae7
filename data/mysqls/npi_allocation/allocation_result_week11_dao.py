from sqlalchemy import Column, Integer, Float, String, DateTime, BigInteger, text
from util.const import ErrorExcept, ErrCode
import logging
from typing import List, Dict, Any

from util.npi_secure_base import FASTNPISecureBase, FASTNPISecureSession

logger = logging.getLogger(__name__)


class AllocationResultWeek11Dao(FASTNPISecureBase):
    __tablename__ = 'allocation_result_week11'
    __table_args__ = {'schema': 'fast_npi_secure'}

    id = Column(Integer, primary_key=True, autoincrement=True)
    pos_id = Column(String(32))
    soldto_id = Column(String(32))
    fiscal_week = Column(String(32))
    mpn_id = Column(String(32))
    rtm = Column(String(32))
    sub_rtm = Column(String(32))
    mpn = Column(String(32))
    allocation = Column(Integer)
    tier = Column(String(16))
    version = Column(BigInteger)
    cw = Column(String(32))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    total_mpn_supply = Column(Integer, comment='Total supply of all MPN')
    pos_mix_cap_by_woi = Column(Float, comment='Maximum mix percentage allowed by WOI constraints')

    @classmethod
    def bulk_save(cls, objs: List[Dict[str, Any]]) -> bool:
        """
        Bulk save allocation results to the database

        Args:
            objs: List of dictionaries where each dictionary represents a row to insert
                  with keys matching column names (including create_time and update_time)

        Returns:
            bool: True if successful, raises exception otherwise

        Raises:
            ErrorExcept: If database operation fails
        """
        session = FASTNPISecureSession()
        try:
            insert_stmt = text(f"""
                INSERT INTO {cls.__tablename__} 
                (pos_id, soldto_id, fiscal_week, rtm, sub_rtm, mpn, allocation, 
                 tier, version, create_time, update_time, 
                 total_mpn_supply, pos_mix_cap_by_woi, cw)
                VALUES 
                (:pos_id, :soldto_id, :fiscal_week, :rtm, :sub_rtm, :mpn, :allocation, 
                 :tier, :version, :create_time, :update_time, 
                 :total_mpn_supply, :pos_mix_cap_by_woi, :cw)
            """)

            session.execute(insert_stmt, objs)
            session.commit()
            return True

        except Exception as e:
            logger.error(f"Failed to bulk save to {cls.__tablename__}: {str(e)}")
            logger.exception(f"Failed to bulk save {len(objs)} records: {str(e)}")
            session.rollback()
            raise ErrorExcept(ErrCode.DBInsert,
                              f"Bulk insert into {cls.__tablename__} failed: {str(e)}")
        finally:
            session.close()