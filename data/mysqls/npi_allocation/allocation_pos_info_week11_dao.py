from sqlalchemy import Column, Integer, Numeric, text
from util.npi_secure_base import FASTNPISecureBase, FASTNPISecureSession
from util.const import ErrorExcept, ErrCode
import logging
from typing import List

logger = logging.getLogger(__name__)


class PosInfoWeek11Dto:
    """Data Transfer Object for POS information"""

    def __init__(self, id: int, pos_id: int, pos_mix: float, soldto_id: int):
        self.id = id
        self.pos_id = pos_id
        self.pos_mix = pos_mix  # Decimal value (e.g., 0.30 for 30%)
        self.soldto_id = soldto_id

    def __repr__(self):
        return f"PosInfoWeek11Dto(id={self.id}, pos_id={self.pos_id}, pos_mix={self.pos_mix:.2f})"


class PosInfoWeek11Dao(FASTNPISecureBase):
    """Data Access Object for POS information"""
    __tablename__ = 'pos_info_week11'
    __table_args__ = {'schema': 'fast_npi_secure'}

    id = Column(Integer, primary_key=True, autoincrement=True)
    pos_id = Column(Integer, nullable=False, comment='Position ID')
    pos_mix = Column(Numeric(5, 4), nullable=False, comment='Mix percentage in decimal form')
    soldto_id = Column(Integer, comment='Soldto ID')

    @classmethod
    def load_pos_info_week11(cls) -> List[PosInfoWeek11Dto]:
        """Load all position info records from the database

        Returns:
            List[PosInfoWeek11Dto]: List of POS information DTOs
        """
        session = FASTNPISecureSession()
        try:
            query = text('''
                         SELECT id, pos_id, pos_mix, soldto_id
                         FROM fast_npi_secure.pos_info_week11
                         ORDER BY id
                         ''')

            result = session.execute(query)
            return [
                PosInfoWeek11Dto(
                    id=row.id,
                    pos_id=row.pos_id,
                    pos_mix=float(row.pos_mix) , # Convert Decimal to float
                    soldto_id=row.soldto_id
                ) for row in result
            ]
        except Exception as e:
            logger.exception(f"Failed to load POS info: {str(e)}")
            raise ErrorExcept(ErrCode.DBQuery, "Failed to load POS information")
        finally:
            session.close()