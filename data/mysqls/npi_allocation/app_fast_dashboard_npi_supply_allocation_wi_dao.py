from sqlalchemy import Column, Integer, String, Float, select

from domain.npi_allocation.dto.npi_allocation_dtos import DashboardNpiSupplyAllocationDto, SoldToMappingDto
from util.conf import logger
from util.const import ErrorExcept, ErrCode
from util.npi_secure_base import FASTNPISecureSession, FASTNPISecureBase


class AppFastDashboardNpiSupplyAllocationWiDao(FASTNPISecureBase):
    __tablename__ = 'app_fast_dashboard_npi_supply_allocation_wi'
    __table_args__ = {'schema': 'fast_npi_secure'}
    # 基础字段
    version = Column(String, primary_key=True)
    tier = Column(String)
    rtm = Column(String)
    sub_rtm = Column(String)
    sold_to_id = Column(String)
    sold_to_name = Column(String)
    sold_to_name_abbre = Column(String)
    pos_authorized_cnt = Column(Integer)
    pos_suspension_cnt = Column(Integer)
    pos_authorized_minus_suspension = Column(Integer)
    authorized_pos_suspension_pct = Column(Float)
    allocation_mix = Column(Float)
    allocation_mix_woi_cap = Column(Float)
    allocation_mix_woi_cap_vs_reward = Column(Float)
    allocation_mix_ly = Column(Float)
    allocation_qty = Column(Integer)
    allocation_qty_ly = Column(Integer)
    allocation_yoy = Column(Float)
    avg_allocation_per_pos = Column(Float)
    npi_weekly_runrate = Column(Float)
    woi = Column(Float)
    tier_bau_mix = Column(Float)

    @classmethod
    def find_by_tier(cls, tier: str) -> list[DashboardNpiSupplyAllocationDto]:
        try:
            with FASTNPISecureSession() as session:
                stmt = select(*cls.__table__.columns).where(cls.tier == tier)
                result = session.execute(stmt).all()
                if not result:
                    logger.warning("No app_fast_dashboard_npi_supply_allocation_wi found")
                    return []
                return [DashboardNpiSupplyAllocationDto.from_dict(dict(row._mapping)) for row in result]
        except Exception as e:
            logger.exception(f"Failed to query app_fast_dashboard_npi_supply_allocation_wi: {str(e)}")
            raise ErrorExcept(ErrCode.DBQueryError, "Failed to query app_fast_dashboard_npi_supply_allocation_wi")

    @classmethod
    def find_distinct_rtm_subrtm_sold_to(cls, tier=None) -> list[SoldToMappingDto]:
        try:
            with FASTNPISecureSession() as session:
                stmt = (
                    select(
                        cls.rtm, cls.sub_rtm, cls.sold_to_id, cls.sold_to_name, cls.sold_to_name_abbre.label('short_name')
                    )
                    .distinct()
                    .where(cls.sold_to_id.isnot(None))
                    .where(cls.sold_to_id != 'All').where(cls.tier == tier)
                )
                result = session.execute(stmt)
                return [SoldToMappingDto.from_dict(dict(row._mapping)) for row in result]
        except Exception as e:
            logger.exception(f"Failed to query distinct sold_to info: {str(e)}")
            raise ErrorExcept(ErrCode.DBQueryError, "Failed to query distinct sold_to info")
