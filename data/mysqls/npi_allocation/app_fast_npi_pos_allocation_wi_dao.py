import pandas as pd
from sqlalchemy import select, Column, String, Float, DateTime, literal

from util.conf import logger
from util.const import ErrorExcept, ErrCode
from util.npi_secure_base import FASTNPISecureBase, FASTNPISecureSession


class AppFastNpiPosAllocationWiDao(FASTNPISecureBase):
    __tablename__ = 'app_fast_npi_pos_allocation_wi'
    __table_args__ = {'schema': 'fast_npi_secure'}

    version = Column(String(64),primary_key=True)
    tier = Column(String(64))
    supply_from = Column(String(64))
    lob = Column(String(64))
    rtm = Column(String(64))
    sub_rtm = Column(String(64))
    sold_to_id = Column(String(64))
    sold_to_name = Column(String(128))
    sold_to_name_abbre = Column(String(64))
    apple_id = Column(String(64))
    pos_name = Column(String(128))
    allocation_qty = Column(Float)
    allocation_mix_reward = Column(Float)
    allocation_mix_woi_cap = Column(Float)
    allocation_mix_for_rtm = Column(Float)
    ship_to_name = Column(String)
    ship_to_id = Column(String)
    remark = Column(String)
    update_time = Column(DateTime)

    @classmethod
    def find_by_rtm(cls, rtm) -> pd.DataFrame:
        try:
            with FASTNPISecureSession() as session:
                query = select(cls.rtm, cls.sub_rtm, cls.sold_to_id, cls.sold_to_name,
                               cls.apple_id, cls.pos_name,cls.supply_from,
                               cls.lob, cls.ship_to_id, cls.ship_to_name,cls.remark,
                               cls.tier, cls.allocation_mix_woi_cap,
                               cls.allocation_mix_for_rtm,
                               cls.update_time)
                if rtm:
                    query = query.where(cls.rtm == rtm)
                df = pd.read_sql(query, session.bind)
                return df
        except Exception as e:
            logger.exception(f"Failed to load strategic result as DataFrame: {str(e)}")
            raise ErrorExcept(ErrCode.DBQueryError, "Failed to load eng_allocation_strategic_result")
