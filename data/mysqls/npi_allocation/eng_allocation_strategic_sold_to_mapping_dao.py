from sqlalchemy import Column, Integer, String, select
from domain.npi_allocation.dto.npi_allocation_dtos import SoldToMappingDto
from util.conf import logger
from util.const import ErrorExcept, ErrCode
from util.npi_secure_base import FASTNPISecureSession, FASTNPISecureBase


class EngAllocationStrategicSoldToMappingDao(FASTNPISecureBase):
    __tablename__ = 'eng_allocation_strategic_sold_to_mapping'
    __table_args__ = {'schema': 'fast_npi_secure'}

    id = Column(Integer, primary_key=True, comment='Primary key')
    rtm = Column(String(64))
    sub_rtm = Column(String(64))
    sold_to_id = Column(String(64), comment='Sold-to ID')
    sold_to_name = Column(String(64))
    short_name = Column(String(64))

    @classmethod
    def find_all(cls) -> list[SoldToMappingDto]:
        try:
            with FASTNPISecureSession() as session:
                stmt = select(*cls.__table__.columns)
                result = session.execute(stmt).all()
                if not result:
                    logger.warning("No eng_allocation_strategic_sold_to_mapping found")
                    return []

                return [SoldToMappingDto(
                    rtm=row.rtm,
                    sub_rtm=row.sub_rtm,
                    sold_to_id=row.sold_to_id,
                    sold_to_name=row.sold_to_name,
                    short_name=row.short_name
                ) for row in result]
        except Exception as e:
            logger.exception(f"Failed to load supply configs: {str(e)}")
            raise ErrorExcept(ErrCode.DBQueryError, "Failed to load eng_allocation_strategic_sold_to_mapping")
