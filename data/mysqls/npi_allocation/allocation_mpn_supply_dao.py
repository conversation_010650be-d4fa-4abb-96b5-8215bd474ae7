from sqlalchemy import Column, Integer, String, DateTime, text
from domain.npi_allocation.dto.npi_allocation_dtos import AllocationMpnSupplyDto
from util.conf import logger
from util.const import ErrorExcept, ErrCode
from util.npi_secure_base import FAST<PERSON>ISecureBase, FASTNPISecureSession


class AllocationMpnSupplyDao(FASTNPISecureBase):
    __tablename__ = 'ods_npi_ly_1st_batch_published_supply_yi'
    __table_args__ = {'schema': 'fast_npi_secure'}  # Adjust schema if needed

    fiscal_qtr_week_name = Column(String(64), primary_key=True, comment='Fiscal quarter week name')
    region = Column(String(64), comment='Region')
    rtm = Column(String(64), comment='Route to market')
    sub_rtm = Column(String(64), comment='Sub route to market')
    sold_to_id = Column(String(64), comment='Sold-to ID')
    ops_sub_class = Column(String(64), comment='Ops sub class')
    mpn = Column(String(64), comment='MPN')
    sub_lob = Column(String(64), comment='Model')
    nand = Column(String(64), comment='NAND')
    color = Column(String(64), comment='Color')
    shipment_plan_cw = Column(Integer, comment='Shipment plan current week')
    shipment_plan_cw1 = Column(Integer, comment='Shipment plan current week +1')
    shipment_plan_cw2 = Column(Integer, comment='Shipment plan current week +2')
    create_time = Column(DateTime, comment='Create time')
    update_time = Column(DateTime, comment='Update time')

    @classmethod
    def load_all(cls) -> list[AllocationMpnSupplyDto]:
        """Load all data from the NPI LY 1st Batch Published Supply table"""
        s = FASTNPISecureSession()
        try:
            query = text(f'''
                SELECT 
                    fiscal_qtr_week_name,
                    region,
                    rtm,
                    sub_rtm,
                    sold_to_id,
                    ops_sub_class,
                    mpn,
                    model,
                    nand,
                    color,
                    shipment_plan_cw,
                    shipment_plan_cw1,
                    shipment_plan_cw2,
                    create_time,
                    update_time
                FROM {cls.__tablename__}
            ''')
            result = s.execute(query)

            return [AllocationMpnSupplyDto(
                fiscal_qtr_week_name=row.fiscal_qtr_week_name,
                region=row.region,
                rtm=row.rtm,
                sub_rtm=row.sub_rtm,
                sold_to_id=row.sold_to_id,
                ops_sub_class=row.ops_sub_class,
                mpn=row.mpn,
                sub_lob=row.model,
                nand=row.nand,
                color=row.color,
                shipment_plan_cw=row.shipment_plan_cw,
                shipment_plan_cw1=row.shipment_plan_cw1,
                shipment_plan_cw2=row.shipment_plan_cw2,
                create_time=row.create_time,
                update_time=row.update_time,
                current_allocation_week_supply=0
            ) for row in result]

        except Exception as e:
            logger.exception(f"Failed to load NPI LY 1st Batch Published Supply data: {str(e)}")
            raise ErrorExcept(ErrCode.DBQueryError, "Failed to load NPI LY 1st Batch Published Supply data")
        finally:
            s.close()