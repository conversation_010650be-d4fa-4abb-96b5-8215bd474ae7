from sqlalchemy import Column, Integer, Float, String, DateTime, text, select

from domain.npi_allocation.dto.npi_allocation_dtos import AllocationPosInfoDto
from util.conf import logger
from util.const import ErrorExcept, ErrCode

from util.npi_secure_base import FASTNPISecureBase, FASTNPISecureSession


class AllocationPosInfoDao(FASTNPISecureBase):
    __tablename__ = 'app_rewardsystem_pos_allocation_for_fast_wi'
    __table_args__ = {'schema': 'fast_npi_secure'}

    platform = Column(String(64), comment='Platform', primary_key=True)
    tier = Column(String(64), comment='Tier')
    apple_id = Column(String(64), comment='Apple ID')
    pos_name = Column(String(255), comment='POS name')
    rtm = Column(String(64), comment='Route to market')
    sub_rtm = Column(String(64), comment='Sub route to market')
    sold_to_id = Column(String(64), comment='Sold-to ID')
    sold_to_name = Column(String(255), comment='Sold-to name')
    allocation_mix = Column(Float, comment='Allocation mix')
    tier_allocation_mix = Column(Float, comment='Allocation mix')
    bau_mix = Column(Float, comment='BAU mix')
    tier_bau_mix = Column(Float, comment='BAU mix')
    create_time = Column(DateTime, comment='Create time')
    version = Column(String(64), comment='Version identifier')
    suspension = Column(Integer)


    @classmethod
    def get_pos_infos(cls, version: str) -> list[AllocationPosInfoDto]:
        """Load all POS information data"""
        s = FASTNPISecureSession()
        try:
            query = text(f'''
                SELECT platform, tier, apple_id, pos_name, rtm, sub_rtm,
                       sold_to_id, sold_to_name, allocation_mix, bau_mix,tier_allocation_mix,tier_bau_mix,
                       create_time
                FROM {cls.__tablename__} 
                WHERE version = :version and suspension=0
            ''')
            result = s.execute(query, {'version': version})
            return [AllocationPosInfoDto(
                platform=row.platform,
                tier=row.tier,
                pos_id=row.apple_id,
                pos_name=row.pos_name,
                rtm=row.rtm,
                sub_rtm=row.sub_rtm,
                soldto_id=row.sold_to_id,
                soldto_name=row.sold_to_name,
                allocation_mix=row.allocation_mix,
                bau_mix=row.tier_bau_mix,
                create_time=row.create_time
            ) for row in result]
        except Exception as e:
            logger.exception(f"Failed to load POS infos: {str(e)}")
            raise ErrorExcept(ErrCode.DBQueryError, "Failed to load POS information")
        finally:
            s.close()

    @classmethod
    def find_all(cls) -> list[AllocationPosInfoDto]:
        """Load all POS information data"""
        s = FASTNPISecureSession()
        try:
            query = text(f'''
                SELECT platform, tier, apple_id, pos_name, rtm, sub_rtm,
                       sold_to_id, sold_to_name, allocation_mix, bau_mix,tier_allocation_mix,tier_bau_mix,
                       create_time,version
                FROM {cls.__tablename__} 
                WHERE suspension=0
            ''')
            result = s.execute(query)
            return [AllocationPosInfoDto(
                platform=row.platform,
                tier=row.tier,
                pos_id=row.apple_id,
                pos_name=row.pos_name,
                rtm=row.rtm,
                sub_rtm=row.sub_rtm,
                soldto_id=row.sold_to_id,
                soldto_name=row.sold_to_name,
                allocation_mix=row.allocation_mix,
                bau_mix=row.tier_bau_mix,
                create_time=row.create_time,
                version=row.version
            ) for row in result]
        except Exception as e:
            logger.exception(f"Failed to load POS infos: {str(e)}")
            raise ErrorExcept(ErrCode.DBQueryError, "Failed to load POS information")
        finally:
            s.close()