from sqlalchemy import Column, Integer, Float, String, DateTime, text, select, update, func

from domain.npi_allocation.dto.npi_allocation_dtos import AllocationResultDto
from util.const import ErrorExcept, ErrCode
import logging

from util.npi_secure_base import FASTNPISecureBase, FASTNPISecureSession

logger = logging.getLogger(__name__)


class AllocationResultDao(FASTNPISecureBase):
    __tablename__ = 'allocation_result'
    __table_args__ = {'schema': 'fast_npi_secure'}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    pos_id = Column(String(64), comment='POS ID')
    soldto_id = Column(String(64), comment='Sold-to ID')
    rtm = Column(String(64), comment='Route to Market')
    sub_rtm = Column(String(64), comment='Sub Route to Market')
    supply_from = Column(String(64), comment='Supply source')
    allocation = Column(Float, comment='Allocated quantity')
    tier = Column(String(64), comment='Tier type')
    version = Column(String(64), comment='Allocation version')
    create_time = Column(DateTime, comment='Create time')
    update_time = Column(DateTime, comment='Update time')
    pos_mix = Column(Float, comment='POS mix')
    tier_platform_supply = Column(Integer, comment='current tier and platform total supply')
    publish_status = Column(Integer)
    tier_supply = Column(Integer, comment='Total supply available for this tier')
    pos_mix_cap_by_woi = Column(Float, comment='Maximum mix percentage allowed by WOI constraints')
    remark = Column(String, comment='Remark')

    @classmethod
    def bulk_save(cls, objs: list) -> bool:
        """Save multiple allocation results to database"""
        s = FASTNPISecureSession()
        try:
            query = text(f'''
                INSERT INTO {cls.__tablename__} 
                (pos_id, soldto_id, rtm, sub_rtm, supply_from, allocation, 
                 tier, version, create_time, update_time, pos_mix, tier_platform_supply, tier_supply, pos_mix_cap_by_woi,
                 remark)
                VALUES (:pos_id, :soldto_id, :rtm, :sub_rtm, :supply_from, :allocation, 
                        :tier, :version, :create_time, :update_time, :pos_mix, :tier_platform_supply, :tier_supply, :pos_mix_cap_by_woi,
                        :remark)
            ''')
            s.execute(query, objs)
            s.commit()
            return True
        except Exception as e:
            logger.exception(str(e))
            raise ErrorExcept(ErrCode.DBInsert, f"Insert into allocation_result failed: {str(e)}")
        finally:
            s.close()

    @classmethod
    def update_publish_status_for_latest_versions(cls, status_value: int = 1) -> None:
        """
        更新 tier='Pro' 和 tier='Consumer' 的最大 version 的 publish_status 字段
        :param status_value: 要设置的发布状态值，默认 1
        """
        try:
            with FASTNPISecureSession() as session:
                # 查询 Pro 最大版本
                pro_max_version = session.execute(
                    select(func.max(cls.version)).where(cls.tier == 'Pro')
                ).scalar()

                # 查询 Consumer 最大版本
                consumer_max_version = session.execute(
                    select(func.max(cls.version)).where(cls.tier == 'Consumer')
                ).scalar()

                # 更新 Pro 最大版本的 publish_status
                if pro_max_version:
                    session.execute(
                        update(cls)
                        .where(cls.tier == 'Pro', cls.version == pro_max_version)
                        .values(publish_status=status_value)
                    )

                # 更新 Consumer 最大版本的 publish_status
                if consumer_max_version:
                    session.execute(
                        update(cls)
                        .where(cls.tier == 'Consumer', cls.version == consumer_max_version)
                        .values(publish_status=status_value)
                    )

                session.commit()
        except Exception as e:
            logger.exception(str(e))
            raise ErrorExcept(ErrCode.DBInsert, f"Update publish_status failed: {str(e)}")

    @classmethod
    def get_all_tier_version_dict(cls) -> dict[str, list[int]]:
        """
        查询 allocation_result 表中所有唯一的 (tier, version) 组合，并按 tier 聚合成字典，version 升序排序
        :return: dict 格式 {tier: [version1, version2, ...]}
        """
        try:
            with FASTNPISecureSession() as session:
                results = session.execute(
                    select(cls.tier, cls.version).distinct()
                ).all()

                tier_version_dict: dict[str, list[int]] = {}
                for tier, version in results:
                    if tier not in tier_version_dict:
                        tier_version_dict[tier] = []
                    tier_version_dict[tier].append(version)

                # 对每个 tier 下的 version 进行排序
                for versions in tier_version_dict.values():
                    versions.sort()  # 如需降序改为 versions.sort(reverse=True)

                return tier_version_dict
        except Exception as e:
            logger.exception(str(e))
            raise ErrorExcept(ErrCode.DBQueryError, f"Query tier-version combinations failed: {str(e)}")

    @classmethod
    def delete_by_version_and_tier(cls, version, tier) -> None:

        try:
            with FASTNPISecureSession() as session:
                session.execute(
                    cls.__table__.delete().where(cls.version == version).where(cls.tier==tier)
                )
                session.commit()
        except Exception as e:
            logger.exception(str(e))
            raise ErrorExcept(ErrCode.DBQueryError, f"Delete by version failed: {str(e)}")

    @classmethod
    def has_published_records(cls) -> bool:
        try:
            with FASTNPISecureSession() as session:
                result = session.execute(
                    select(1).where(cls.publish_status == 1).limit(1)
                ).scalar()
                return result is not None
        except Exception as e:
            logger.exception(str(e))
            raise ErrorExcept(ErrCode.DBQueryError, f"Check for published records failed: {str(e)}")

    @classmethod
    def load_all(cls) -> list[AllocationResultDto]:
        """Load all allocation results from the database as DTO instances

        Args:
            version: Optional version filter. If provided, only returns records matching this version.

        Returns:
            List of AllocationResultDto objects
        """
        s = FASTNPISecureSession()
        try:
            base_query = f'''
                SELECT 
                    ar1.id,
                    ar1.pos_id,
                    ar1.soldto_id,
                    ar1.rtm,
                    ar1.sub_rtm,
                    ar1.supply_from,
                    ar1.allocation,
                    ar1.tier,
                    ar1.version,
                    ar1.create_time,
                    ar1.update_time,
                    ar1.pos_mix,
                    ar1.soldto_supply,
                    ar1.publish_status,
                    ar1.tier_platform_supply,
                    ar1.tier_supply,
                    ar1.pos_mix_cap_by_woi,
                    ar1.remark
                FROM {cls.__tablename__} ar1
                INNER JOIN (
                    SELECT pos_id, MAX(id) as max_id
                    FROM {cls.__tablename__}
                    WHERE version = (SELECT MAX(version) FROM allocation_result)
                    GROUP BY pos_id
                ) ar2 ON ar1.id = ar2.max_id
            '''

            query = text(base_query)
            result = s.execute(query)

            return [AllocationResultDto(
                id=row.id,
                pos_id=row.pos_id,
                soldto_id=row.soldto_id,
                rtm=row.rtm,
                sub_rtm=row.sub_rtm,
                supply_from=row.supply_from,
                allocation=row.allocation,
                tier=row.tier,
                version=row.version,
                pos_mix=row.pos_mix,
                soldto_supply=row.soldto_supply,
                publish_status=row.publish_status,
                tier_platform_supply=row.tier_platform_supply,
                tier_supply=row.tier_supply,
                pos_mix_cap_by_woi=row.pos_mix_cap_by_woi,
                remark=row.remark,
                create_time=row.create_time,
                update_time=row.update_time
            ) for row in result]

        except Exception as e:
            logger.exception(f"Failed to load allocation results: {str(e)}")
            raise ErrorExcept(ErrCode.DBQueryError, "Failed to load allocation results")
        finally:
            s.close()