from typing import List

from sqlalchemy import Column, Integer, Float, String, DateTime, select, func

from domain.npi_allocation.dto.npi_allocation_dtos import AllocationSupplyConfigDto
from util.conf import logger
from util.const import ErrorExcept, ErrCode
from util.npi_secure_base import FASTNPISecureBase, FASTNPISecureSession


class AllocationSupplyConfigDao(FASTNPISecureBase):
    __tablename__ = 'allocation_supply_config'
    __table_args__ = {'schema': 'fast_npi_secure'}

    id = Column(Integer, primary_key=True, comment='Primary key')
    supply_from = Column(String(64), comment='Supply source platform')
    tier = Column(String(64), comment='Tier classification')
    supply_qty = Column(Integer, comment='Supply quantity')
    rtm_mix = Column(Float, comment='Route to market mix')
    ly_supply = Column(Integer, comment='Last year supply')
    soldto_id = Column(String(64), comment='Sold-to ID')
    extra = Column(String(1024), comment='Additional data')
    remark = Column(String(1024), comment='remark')
    npi_weekly_so_forecast = Column(Integer, comment='NPI weekly sales forecast')
    woi_cap_adjustment = Column(Float, comment='WOI cap adjustment')
    rtm = Column(String(64))
    sub_rtm = Column(String(64))
    bau_mix = Column(Float)
    is_calculated = Column(Integer)
    ly_npi_mix = Column(Float)

    version = Column(Integer)
    cal_status = Column(Integer)
    create_time = Column(DateTime, comment='Create timestamp')
    update_time = Column(DateTime, comment='Update timestamp')


    @classmethod
    def load_supply_configs_bak(cls) -> list[AllocationSupplyConfigDto]:
        """Load all supply configuration data"""
        try:
            with FASTNPISecureSession() as session:
                # 子查询获取每个 tier 的最大 version
                max_version_pro = select(func.max(cls.version)).where(cls.tier == "Pro").scalar_subquery()
                max_version_consumer = select(func.max(cls.version)).where(cls.tier == "Consumer").scalar_subquery()

                # 构建两个查询
                stmt_pro = select(*cls.__table__.columns).where(
                    cls.tier == "Pro",
                    cls.version == 2
                )
                stmt_consumer = select(*cls.__table__.columns).where(
                    cls.tier == "Consumer",
                    cls.version == 2
                )
                # 合并查询（UNION）
                stmt = stmt_pro.union_all(stmt_consumer)
                result = session.execute(stmt).all()

                if not result:
                    logger.warning("No supply configs found")
                    return []

                return [AllocationSupplyConfigDto(
                    supply_from=row.supply_from,
                    tier=row.tier,
                    supply_qty=row.supply_qty,
                    rtm_mix=row.rtm_mix,
                    ly_supply=row.ly_supply,
                    soldto_id=row.soldto_id,
                    extra=row.extra,
                    npi_weekly_so_forecast=row.npi_weekly_so_forecast,
                    woi_cap_adjustment=row.woi_cap_adjustment,
                    remark=row.remark,
                    rtm=row.rtm,
                    sub_rtm=row.sub_rtm,
                    bau_mix=row.bau_mix,
                    is_calculated=row.is_calculated,
                    ly_npi_mix=row.ly_npi_mix
                ) for row in result]
        except Exception as e:
            logger.exception(f"Failed to load supply configs: {str(e)}")
            raise ErrorExcept(ErrCode.DBQueryError, "Failed to load supply configurations")

    @classmethod
    def load_supply_configs(cls) -> list[AllocationSupplyConfigDto]:
        """Load all supply configuration data"""
        try:
            with FASTNPISecureSession() as session:
                # 子查询获取每个 tier 的最大 version
                max_version_pro = select(func.max(cls.version)).where(cls.tier == "Pro").scalar_subquery()
                max_version_consumer = select(func.max(cls.version)).where(cls.tier == "Consumer").scalar_subquery()

                # 构建两个查询
                stmt_pro = select(*cls.__table__.columns).where(
                    cls.tier == "Pro",
                    cls.version == max_version_pro
                )
                stmt_consumer = select(*cls.__table__.columns).where(
                    cls.tier == "Consumer",
                    cls.version == max_version_consumer
                )
                # 合并查询（UNION）
                stmt = stmt_pro.union_all(stmt_consumer)
                result = session.execute(stmt).all()

                if not result:
                    logger.warning("No supply configs found")
                    return []

                return [AllocationSupplyConfigDto(
                    supply_from=row.supply_from,
                    tier=row.tier,
                    supply_qty=row.supply_qty,
                    rtm_mix=row.rtm_mix,
                    ly_supply=row.ly_supply,
                    soldto_id=row.soldto_id,
                    extra=row.extra,
                    npi_weekly_so_forecast=row.npi_weekly_so_forecast,
                    woi_cap_adjustment=row.woi_cap_adjustment,
                    remark=row.remark,
                    rtm=row.rtm,
                    sub_rtm=row.sub_rtm,
                    bau_mix=row.bau_mix,
                    is_calculated=row.is_calculated,
                    ly_npi_mix=row.ly_npi_mix
                ) for row in result]
        except Exception as e:
            logger.exception(f"Failed to load supply configs: {str(e)}")
            raise ErrorExcept(ErrCode.DBQueryError, "Failed to load supply configurations")

    @classmethod
    def batch_insert(cls, objs: List, fields_to_insert: List[str]):
        try:
            with FASTNPISecureSession() as s:
                insert_parts = [f"{field}" for field in fields_to_insert]
                value_parts = [f":{field}" for field in fields_to_insert]
                insert_stmt = f"""
                                        INSERT INTO {cls.__tablename__} 
                                        ({', '.join(insert_parts)})
                                         VALUES 
                                            ({', '.join(value_parts)})
                                    """
                s.execute(insert_stmt, objs)
                s.commit()
        except Exception as e:
            s.rollback()
            logger.exception(e)
            raise ErrorExcept(ErrCode.DBInsert, '更新数据库失败')
        finally:
            s.close()

    @classmethod
    def get_max_version_by_tier(cls, tier: str) -> int:
        try:
            with FASTNPISecureSession() as session:
                stmt = select(func.max(cls.version)).where(cls.tier == tier)
                result = session.execute(stmt).scalar()
                return result if result is not None else 0
        except Exception as e:
            logger.exception(f"Failed to get max version for tier {tier}: {str(e)}")
            raise ErrorExcept(ErrCode.DBQueryError, f"获取 tier={tier} 的最大版本失败")

    @classmethod
    def update_china_total_config(cls, tier: str, version: int,
                                  forecast: int, woi_adjustment: float, supply_qty: int):
        try:
            with FASTNPISecureSession() as session:
                stmt = (
                    cls.__table__.update()
                    .where(
                        cls.tier == tier,
                        cls.version == version,
                        cls.supply_from == 'China_Total'
                    )
                    .values(
                        npi_weekly_so_forecast=forecast,
                        woi_cap_adjustment=woi_adjustment,
                        supply_qty=supply_qty,
                        update_time=func.now()
                    )
                )
                result = session.execute(stmt)
                session.commit()
                logger.info(f"Updated {result.rowcount} rows for tier={tier}, version={version}")
        except Exception as e:
            logger.exception(f"Failed to update China_Total config for tier {tier}, version {version}: {str(e)}")
            raise ErrorExcept(ErrCode.DBInsert, "更新 China_Total 配置失败")
