import pandas as pd
from sqlalchemy import select, Column, String, Float, DateTime
from util.conf import logger
from util.const import ErrorExcept, ErrCode
from util.npi_secure_base import FASTNPISecureBase, FASTNPISecureSession


class AppFastNpiCdcAllocationWiDao(FASTNPISecureBase):
    __tablename__ = 'app_fast_npi_cdc_allocation_wi'
    __table_args__ = {'schema': 'fast_npi_secure'}

    rtm = Column(String(64), primary_key=True)
    sub_rtm = Column(String(64))
    reseller_id = Column(String(64))
    apple_id = Column(String(64))
    pos_name = Column(String(128))
    province = Column(String(64))
    city = Column(String(64))
    cdc_city = Column(String(64))
    ship_to_id = Column(String(64))
    ship_to_name = Column(String(128))
    ship_to_pos_qty = Column(Float)
    ship_to_pos_mix = Column(Float)
    logistics_vendor = Column(String(128))
    pickup_time = Column(String)
    eta = Column(String)

    @classmethod
    def find_by_rtm(cls, rtm) -> pd.DataFrame:
        try:
            with FASTNPISecureSession() as session:
                query = select(
                    cls.rtm, cls.sub_rtm, cls.reseller_id, cls.apple_id,
                    cls.pos_name, cls.province, cls.city, cls.cdc_city,
                    cls.ship_to_id, cls.ship_to_name,
                    cls.ship_to_pos_qty, cls.ship_to_pos_mix,
                    cls.logistics_vendor, cls.pickup_time, cls.eta
                )
                if rtm:
                    query = query.where(cls.rtm == rtm)
                df = pd.read_sql(query, session.bind)
                return df
        except Exception as e:
            logger.exception(f"Failed to load CDC allocation data as DataFrame: {str(e)}")
            raise ErrorExcept(ErrCode.DBQueryError, "Failed to load app_fast_npi_cdc_allocation_wi")
