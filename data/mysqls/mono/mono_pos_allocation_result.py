import traceback
from sqlalchemy import literal
from util.const import ErrCode, ErrorExcept
from util.fast_lite_base import *


class MonoPosAllocationResult(FASTLiteBase):
    __tablename__ = "mono_pos_allocation_result"
    __table_args__ = {"schema": "fast_lite",
                      "extend_existing": True}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    fiscal_week = Column(String(16), comment='FY23Q1W12')
    plan_id = Column(Integer)
    hq_id = Column(String(128))
    reseller_name = Column(String(128))
    sold_to_id = Column(String(128))
    sold_to_name = Column(String(128))
    pos_id = Column(String(128))
    pos_name = Column(String(128))
    category = Column(String(128))
    district_group = Column(Integer)
    location_type = Column(String(64))
    pos_type = Column(String(32))
    lob = Column(String(32))
    sub_lob = Column(String(32))
    cdcs = Column(String(128))
    mpn_id = Column(String(32))
    project_code = Column(String(32))
    description = Column(String(32))
    ship_to_id = Column(String(32))
    warehouse_name = Column(String(32))
    sustaining_or_npi = Column(String(32))
    eoh = Column(Integer)
    fix_quantity = Column(Integer)
    instock_quantity = Column(Integer)
    woi_quantity = Column(Integer)
    woi = Column(Float)
    twoi_fcst = Column(Float)
    total_inv = Column(Integer)
    npp_min_inventory = Column(Integer)
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    @classmethod
    def bulk_save(cls, objs: list):
        # 一次写入所有数据
        s = FASTLiteSession()
        ret = []
        try:
            s.execute(cls.__table__.insert(), objs)
            s.commit()
        except Exception as e:
            logger.exception(e)
            raise ErrorExcept(ErrCode.DBInsert, "insert to db failed" +
                              traceback.format_exc() + "params:" + str(objs))
        finally:
            s.close()
        return ret

    @classmethod
    def batch_update(cls, objs: list, fields_to_update):
        s = FASTLiteSession()
        try:
            now = datetime.now()
            for obj in objs:
                obj['update_time'] = now
            insert_parts = [f"{field}" for field in fields_to_update]
            value_parts = [f":{field}" for field in fields_to_update]
            key_parts = [f"{field} = VALUES({field})" for field in fields_to_update]
            insert_stmt = f"""
                INSERT INTO {cls.__tablename__} 
                (fiscal_week, pos_id, ship_to_id, mpn_id, {', '.join(insert_parts)})
                VALUES 
                    (:fiscal_week, :pos_id, :ship_to_id, :mpn_id, {', '.join(value_parts)})
                ON DUPLICATE KEY UPDATE 
                    {', '.join(key_parts)}
            """
            s.execute(insert_stmt, objs)
            s.commit()
        except Exception:
            logger.exception(traceback.format_exc())
            raise ErrorExcept(ErrCode.DBInsert, "insert to db failed" +
                              traceback.format_exc() + "params:" + str(objs))
        finally:
            s.close()
        return True

    @classmethod
    def get_dataframe_result(cls, fiscal_week: str):
        s = FASTLiteSession()
        ret = pd.DataFrame()
        try:
            q = s.query(cls).filter(cls.fiscal_week == fiscal_week).order_by(cls.id.asc())
            ret = pd.read_sql_query(q.statement, fast_lite_engine)
        except Exception as e:
            logger.exception(str(e))
        finally:
            s.close()
        return ret

    @classmethod
    def get_dataframe_by_week_sold_to_name(cls, fiscal_week: str, sold_to_name: str):
        s = FASTLiteSession()
        ret = pd.DataFrame()
        try:
            q = s.query(cls.category,
                        literal("Mono").label("rtm"),
                        cls.sold_to_name,
                        cls.sold_to_id,
                        cls.hq_id.label("hq_id"),
                        cls.reseller_name.label("reseller_name"),
                        cls.pos_id,
                        cls.pos_name,
                        cls.pos_type,
                        literal("").label("stop_flag"),
                        cls.lob,
                        cls.project_code.label("project_code"),
                        cls.sub_lob,
                        cls.mpn_id.label("mpn"),
                        cls.description.label("description"),
                        cls.sustaining_or_npi,
                        cls.fix_quantity,
                        cls.instock_quantity,
                        cls.woi_quantity,
                        (cls.fix_quantity+cls.instock_quantity+cls.woi_quantity).label("final_num"),
                        cls.ship_to_id,
                        cls.warehouse_name
                        ).filter(cls.fiscal_week == fiscal_week, cls.sold_to_name == sold_to_name).order_by(cls.id.asc())
            ret = pd.read_sql_query(q.statement, fast_lite_engine)
        except Exception as e:
            logger.exception(str(e))
        finally:
            s.close()
        return ret

    @classmethod
    def batch_update_with_plan_id(cls, objs: list, fields_to_update):
        s = FASTLiteSession()
        try:
            now = datetime.now()
            for obj in objs:
                obj['update_time'] = now
            insert_parts = [f"{field}" for field in fields_to_update]
            value_parts = [f":{field}" for field in fields_to_update]
            key_parts = [f"{field} = VALUES({field})" for field in fields_to_update]
            insert_stmt = f"""
                    INSERT INTO {cls.__tablename__} 
                    (plan_id, fiscal_week, pos_id, ship_to_id, mpn_id, {', '.join(insert_parts)})
                    VALUES 
                        (:plan_id, :fiscal_week, :pos_id, :ship_to_id, :mpn_id, {', '.join(value_parts)})
                    ON DUPLICATE KEY UPDATE 
                        {', '.join(key_parts)}
                """
            s.execute(insert_stmt, objs)
            s.commit()
        except Exception:
            logger.exception(traceback.format_exc())
            raise ErrorExcept(ErrCode.DBInsert, "insert to db failed" +
                              traceback.format_exc() + "params:" + str(objs))
        finally:
            s.close()
        return True

    @classmethod
    def get_dataframe_by_plan_id_sold_to_name(cls, plan_id: int, sold_to_name: str):
        s = FASTLiteSession()
        ret = pd.DataFrame()
        try:
            q = s.query(cls.category,
                        literal("Mono").label("rtm"),
                        cls.sold_to_name,
                        cls.sold_to_id,
                        cls.hq_id.label("hq_id"),
                        cls.reseller_name.label("reseller_name"),
                        cls.pos_id,
                        cls.pos_name,
                        cls.pos_type,
                        literal("").label("stop_flag"),
                        cls.lob,
                        cls.project_code.label("project_code"),
                        cls.sub_lob,
                        cls.mpn_id.label("mpn"),
                        cls.description.label("description"),
                        cls.sustaining_or_npi,
                        cls.fix_quantity,
                        cls.instock_quantity,
                        cls.woi_quantity,
                        (cls.fix_quantity + cls.instock_quantity + cls.woi_quantity).label("final_num"),
                        cls.ship_to_id,
                        cls.warehouse_name
                        ).filter(cls.plan_id == plan_id, cls.sold_to_name == sold_to_name).order_by(
                cls.id.asc())
            ret = pd.read_sql_query(q.statement, fast_lite_engine)
        except Exception as e:
            logger.exception(str(e))
        finally:
            s.close()
        return ret

    @classmethod
    def get_sold_to_name_by_plan_id(cls, plan_id: int):
        s = FASTLiteSession()
        ret = []
        try:
            q = s.query(cls.sold_to_name).filter(cls.plan_id == plan_id).distinct().all()
            for item in q:
                ret.append(item.sold_to_name)
        except Exception as e:
            logger.exception(str(e))
        finally:
            s.close()
        return ret

    @classmethod
    def remove_data_by_plan_id(cls, plan_id: int):
        s = FASTLiteSession()
        try:
            s.query(cls).filter(cls.plan_id == plan_id).delete()
            s.commit()
        except Exception as e:
            logger.exception(str(e))
            raise ErrorExcept(ErrCode.DBInsert, "delete db failed" + traceback.format_exc())
        finally:
            s.close()
