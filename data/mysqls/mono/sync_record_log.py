from domain.mono.pos_allocation.entity.mpn import Mpn
from util.mono_allocation_base import *


class SyncRecordLog(MonoAllocationBase):
    __tablename__ = "sync_record_log"
    __table_args__ = {"schema": "Allocation",
                      "extend_existing": True}

    id = Column(Integer, autoincrement=True, primary_key=True)
    sync_version = Column(String(100), comment="同步版本号")
    sync_total = Column(Integer, comment="同步数量")
    sync_type = Column(String(100), comment="同步业务功能类型")
    created_time = Column(DateTime)

    @classmethod
    def subquery_sync_version(cls, session, sync_type: str = "sync_stock_info"):
        return session.query(cls.sync_version).filter(cls.sync_type == sync_type).order_by(cls.id.desc()).limit(1).subquery()
