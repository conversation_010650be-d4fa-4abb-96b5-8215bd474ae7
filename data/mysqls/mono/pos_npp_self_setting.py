import traceback

from util.const import ErrorExcept, ErrCode
from util.mono_allocation_base import *


class PosNppSelfSetting(MonoAllocationBase):
    __tablename__ = "pos_npp_self_setting"
    __table_args__ = {"extend_existing": True}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    plan_id = Column(Integer)
    supply_file_name = Column(String(256))
    special_supply_file_name = Column(String(256))
    file_status = Column(Integer)

    @classmethod
    def update_status_by_plan_id(cls, plan_id: int, status: int):
        # 一次写入所有数据
        s = MonoAllocationSession()
        try:
            ret = s.query(cls).filter(cls.plan_id == plan_id).one_or_none()
            if ret:
                ret.file_status = status
                s.commit()
            else:
                logger.info(f"query npp_self_setting no data, plan_id: {str(plan_id)}, status: {status}")
        except Exception as e:
            logger.exception(e)
            raise ErrorExcept(ErrCode.DBQueryNoData, "query npp_self_setting failed" +
                              traceback.format_exc() + "plan_id:" + str(plan_id))
        finally:
            s.close()
