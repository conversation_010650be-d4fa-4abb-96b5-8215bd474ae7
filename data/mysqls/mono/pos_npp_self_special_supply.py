from domain.mono.pos_allocation.entity.pos_special_plan_supply import PosSpecialPlanSupplier
from util.const import <PERSON><PERSON>rExcept, ErrCode
from util.mono_allocation_base import *


class PosNppSelfSpecialSupply(MonoAllocationBase):
    __tablename__ = "pos_npp_self_special_supply"
    __table_args__ = {"schema": "Allocation",
                      "extend_existing": True}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    plan_id = Column(Integer)
    rtm = Column(String(32))
    reseller_name = Column(String(256))
    customer_sold_to_id = Column(String(64))
    ship_to_id = Column(String(64))
    pos_id = Column(Integer)
    pos_name = Column(String(128))
    lob = Column(String(32))
    model = Column(String(32))
    project_code = Column(String(32))
    mpn = Column(String(16))
    mpn_description = Column(String(128))
    allocation = Column(Integer)

    @classmethod
    def get_special_supply_record(cls, plan_id: int):
        s = MonoAllocationSession()
        pos_special_supply = []
        try:
            ret = s.query(cls).filter(cls.plan_id == plan_id).order_by(cls.id.asc()).all()
            for item in ret:
                pos_special_supply.append(PosSpecialPlanSupplier(
                    sold_to_id=item.customer_sold_to_id,
                    pos_id=item.pos_id,
                    mpn=item.mpn,
                    quantity=item.allocation
                ))
        except Exception as e:
            logger.exception(str(e))
            raise ErrorExcept(ErrCode.DBQueryParams, "query special supply failed" + str(e))
        finally:
            s.close()
        return pos_special_supply
