from sqlalchemy import or_
import traceback

from util.const import ErrCode, ErrorExcept
from util.mono_allocation_base import *
from data.mysqls.mono.pos_plan import PosPlan


class PosAllocationResultExcel(MonoAllocationBase):
    __tablename__ = "pos_allocation_result_excel"
    __table_args__ = {"schema": "Allocation",
                      "extend_existing": True}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    plan_id = Column(Integer)
    pos_status = Column(String(16), comment='Update Category')
    rtm = Column(String(16))
    supplier_name = Column(String(128))
    customer_sold_to_id = Column(String(128))
    hq_id = Column(String(128))
    reseller_name = Column(String(128))
    pos_id = Column(String(128))
    pos_name = Column(String(128))
    pos_type = Column(String(128))
    stop_flag = Column(String(16))
    lob = Column(String(32))
    project_code = Column(String(32))
    model = Column(String(32))
    mpn = Column(String(32), comment="Apple Part #")
    description = Column(String(256))
    mpn_type = Column(String(32), comment="Sustaining/NPI")
    special_distribute_num = Column(Integer, comment="特殊项目分货")
    zero_inventory_replenishment_num = Column(Integer, comment="0库存补货")
    twoi_distribute_num = Column(Integer, comment="TWOI分货")
    final_num = Column(Integer, comment="最终分货")
    current_stock_num = Column(Integer, comment="eoh")
    update_date_str = Column(String(32))
    is_online = Column(Integer, comment="0: 线下，默认空为线上")

    @classmethod
    def get_dataframe_result(cls, fiscal_week: str, supplier_name: str):
        s = MonoAllocationSession()
        ret = pd.DataFrame()
        sub_query = PosPlan.subquery_plan_id(s, fiscal_week)
        try:
            q = s.query(cls.pos_status,
                        cls.rtm,
                        cls.supplier_name,
                        cls.customer_sold_to_id,
                        cls.hq_id,
                        cls.reseller_name,
                        cls.pos_id,
                        cls.pos_name,
                        cls.pos_type,
                        cls.stop_flag,
                        cls.lob,
                        cls.project_code,
                        cls.model,
                        cls.mpn,
                        cls.description,
                        cls.mpn_type,
                        cls.special_distribute_num,
                        cls.zero_inventory_replenishment_num,
                        cls.twoi_distribute_num,
                        cls.final_num
                        ).filter(cls.plan_id == sub_query, cls.supplier_name == supplier_name)\
                        .filter(or_(cls.is_online == None, cls.is_online == 1)).order_by(cls.id.asc())
            ret = pd.read_sql_query(q.statement, mono_allocation_engine)
        except Exception as e:
            logger.exception(str(e))
        finally:
            s.close()
        return ret

    @classmethod
    def bulk_save(cls, objs: list):
        # 一次写入所有数据
        s = MonoAllocationSession()
        ret = []
        try:
            s.execute(cls.__table__.insert(), objs)
            s.commit()
        except Exception as e:
            logger.exception(e)
            raise ErrorExcept(ErrCode.DBInsert, "insert to db failed" +
                              traceback.format_exc() + "params:" + str(objs))
        finally:
            s.close()
        return ret

    @classmethod
    def remove_offline_npp_data(cls, plan_id: int):
        s = MonoAllocationSession()
        try:
            s.query(cls).filter(cls.is_online == 0).filter(cls.plan_id == plan_id).delete()
            s.commit()
        except Exception as e:
            logger.exception(e)
            raise ErrorExcept(ErrCode.DBInsert, "delete db failed" + traceback.format_exc())
        finally:
            s.close()

    @classmethod
    def get_dataframe_result_by_plan_id(cls, plan_id: int, supplier_name: str):
        s = MonoAllocationSession()
        ret = pd.DataFrame()
        try:
            q = s.query(cls.pos_status,
                        cls.rtm,
                        cls.supplier_name,
                        cls.customer_sold_to_id,
                        cls.hq_id,
                        cls.reseller_name,
                        cls.pos_id,
                        cls.pos_name,
                        cls.pos_type,
                        cls.stop_flag,
                        cls.lob,
                        cls.project_code,
                        cls.model,
                        cls.mpn,
                        cls.description,
                        cls.mpn_type,
                        cls.special_distribute_num,
                        cls.zero_inventory_replenishment_num,
                        cls.twoi_distribute_num,
                        cls.final_num
                        ).filter(cls.plan_id == plan_id, cls.supplier_name == supplier_name) \
                .filter(or_(cls.is_online == None, cls.is_online == 1)).order_by(cls.id.asc())
            ret = pd.read_sql_query(q.statement, mono_allocation_engine)
        except Exception as e:
            logger.exception(str(e))
        finally:
            s.close()
        return ret