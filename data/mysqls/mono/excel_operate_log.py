from util.mono_allocation_base import *


class ExcelOperateLog(MonoAllocationBase):
    __tablename__ = "excel_operate_log"
    __table_args__ = {"extend_existing": True}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    operate_excel_key = Column(String(64), comment='')
    version_id = Column(String(128), comment='')

    @classmethod
    def query_max_version(cls, operate_key:str):
        s = MonoAllocationSession()
        try:
            q = s.query(cls).filter(cls.operate_excel_key == operate_key).order_by(cls.id.desc())
            ret = q.first()
            if ret is not None:
                return ret.version_id
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return None
