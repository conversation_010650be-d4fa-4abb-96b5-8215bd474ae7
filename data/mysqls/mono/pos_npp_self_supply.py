from domain.mono.pos_allocation.entity.mpn_info import MpnInfo
from domain.mono.pos_allocation.entity.shipment_plan_supply import ShipmentPlanSupplier, get_supplier_by_sold_to
from util.mono_allocation_base import *


class PosNppSelfSupply(MonoAllocationBase):
    __tablename__ = "pos_npp_self_supply"
    __table_args__ = {"schema": "Allocation",
                      "extend_existing": True}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    plan_id = Column(Integer)
    rtm = Column(String(32))
    sales_org = Column(String(32))
    reseller_name = Column(String(256))
    customer_sold_to_id = Column(String(64))
    lob = Column(String(32))
    model = Column(String(32))
    project_code = Column(String(32))
    revenue_demo = Column(String(32))
    mpn = Column(String(64))
    lifecycle = Column(String(16))
    mpn_description = Column(String(128))
    cw_supply = Column(Integer)

    @classmethod
    def query_supply_by_plan_id(cls, plan_id: int) -> list[ShipmentPlanSupplier] and list[MpnInfo]:
        s = MonoAllocationSession()
        suppliers = []
        mpn_infos = []
        try:
            result = (s.query(cls).filter(cls.plan_id == plan_id).all())

            for item in result:
                sold_to = int(item.customer_sold_to_id)
                supplier = get_supplier_by_sold_to(suppliers, sold_to)
                if supplier is None:
                    supplier = ShipmentPlanSupplier(sold_to)
                    suppliers.append(supplier)
                supplier.add_supply(item.mpn, int(item.cw_supply))

                mpn_info = MpnInfo(mpn_id=item.mpn,
                                   sold_to_id=item.customer_sold_to_id,
                                   sold_to_name=item.reseller_name,
                                   lob=item.lob,
                                   sub_lob=item.model,
                                   project_code=item.project_code,
                                   mpn_description=item.mpn_description)
                mpn_infos.append(mpn_info)
            # 去除mpn_infos中的重复数据
            mpn_infos = list({mpn_info.id: mpn_info for mpn_info in mpn_infos}.values())

        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return suppliers, mpn_infos
