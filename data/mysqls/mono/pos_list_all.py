from domain.mono.pos_allocation.entity.pos import Pos, sort_pos
from domain.mono.pos_allocation.entity.pos_hq_info import PosHqInfo
from util.mono_allocation_base import *


class PosListAll(MonoAllocationBase):
    __tablename__ = "excel_pos_list_all"
    __table_args__ = {"extend_existing": True}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    rtm = Column(String(16), comment='')
    hq_id = Column(String(128), comment='')
    reseller_name = Column(String(128), comment='')
    customer_sold_to_name = Column(String(128), comment='')
    customer_sold_to_id = Column(String(128), comment='')
    pos_id = Column(String(128), comment='')
    pos_name = Column(String(128), comment='')
    update_category = Column(String(16), comment='')
    pos_type = Column(String(16), comment='')
    pos_location_type = Column(String(16), comment='')
    version_id = Column(String(128), comment='')
    ship_to_id = Column(String(128), comment='')
    district_group = Column(String(256), comment='')

    @classmethod
    def query_by_version(cls, pos_list_version_id: str, reseller_mapping_version_id) -> list[PosHqInfo]:
        s = MonoAllocationSession()
        hq_infos = []
        try:
            sql_statement = f'''
                            select a.pos_id,a.customer_sold_to_id,b.hq_id,b.reseller_chinese_name,b.supplier_hq_id,b.rtm,b.supplier_name
                            from excel_pos_list_all a
                            left join
                            (select * from excel_reseller_mapping 
                            where version_id = '{reseller_mapping_version_id}') b
                            on a.hq_id =b.hq_id
                            where a.version_id = '{pos_list_version_id}';
                        '''
            ret = s.execute(sql_statement).fetchall()
            for item in ret:
                hq_infos.append(PosHqInfo(pos_id=item.pos_id, hq_id=item.hq_id,sold_to_id=item.customer_sold_to_id, supplier_hq_id=item.supplier_hq_id, reseller_chinese_name=item.reseller_chinese_name, supplier_name=item.supplier_name, rtm=item.rtm))
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return hq_infos

    @classmethod
    def query_all_by_version(cls, pos_list_version_id: str, reseller_mapping_version_id) -> list[Pos]:
        s = MonoAllocationSession()
        pos_list = []
        try:
            sql_statement = f'''
                                select a.pos_id,a.customer_sold_to_id,a.pos_name,
                                b.npp_type,b.reseller_simp_name,b.reseller_type,
                                b.hq_id,b.reseller_chinese_name,b.supplier_hq_id,b.rtm,b.supplier_name, b.npp_supplier_sold_to_id
                                from excel_pos_list_all a
                                left join
                                (select * from excel_reseller_mapping 
                                where version_id = '{reseller_mapping_version_id}') b
                                on a.hq_id =b.hq_id
                                where a.version_id = '{pos_list_version_id}';
                            '''
            ret = s.execute(sql_statement).fetchall()
            for item in ret:
                pos = Pos(sold_to_id=None, hq_id=item.hq_id, reseller_name=item.supplier_name,
                          sold_to_name=None, pos_id=item.pos_id, pos_name=item.pos_name, cdcs=None, category=None,
                          pos_type=None, location_type=None,district_group=None
                          )
                pos.set_hq_info(sub_rtm=item.rtm,
                                npp_type=item.npp_type,
                                reseller_simp_name=item.reseller_simp_name,
                                reseller_type=item.reseller_type,
                                npp_supplier_sold_to_id=item.npp_supplier_sold_to_id,
                                supplier_hq_id=item.supplier_hq_id,
                                customer_sold_to_id=item.customer_sold_to_id)
                pos_list.append(pos)
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return pos_list

    @classmethod
    def query_pos_ship_to_mapping(cls, version_id) -> pd.DataFrame:
        s = MonoAllocationSession()
        res = pd.DataFrame()
        try:
            q = s.query(
                cls.pos_id,
                cls.ship_to_id.label("ship_to_id")) \
                .filter(cls.version_id == version_id) \
                .all()
            tmp = []
            for item in q:
                tmp.append({"pos_id": item.pos_id, "ship_to_id": item.ship_to_id})
            res = pd.DataFrame(tmp)
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return res

    @classmethod
    def query_pos_hq_info_by_pos_version_reseller_version(cls, pos_list_version_id: str, reseller_mapping_version_id) -> list[Pos]:
        s = MonoAllocationSession()
        res = []
        try:
            sql_statement = f'''
                                select 
                                    a.update_category as category,
                                    a.hq_id,
                                    a.reseller_name,
                                    a.pos_id,
                                    a.pos_name,
                                    a.customer_sold_to_id as sold_to_id,
                                    a.customer_sold_to_name as sold_to_name,
                                    a.pos_type,
                                    a.pos_location_type,
                                    a.district_group,
                                    a.ship_to_id,
                                    a.rtm,
                                    b.reseller_chinese_name,
                                    b.supplier_hq_id, 
                                    b.supplier_name
                                from excel_pos_list_all a
                                left join
                                (select * from excel_reseller_mapping 
                                where version_id = '{reseller_mapping_version_id}') b
                                on a.hq_id =b.hq_id
                                where a.version_id = '{pos_list_version_id}';
                            '''
            ret = s.execute(sql_statement).fetchall()
            for item in ret:
                # 以前这里的cdcs取的是app_fast_mono_ds_pos_list_wi中的1～6的信息, 兼容代码也给一个列表
                if item.ship_to_id is None:     # 主要是为了在序列化入库的时候, None不可以, 所以给一个空字符串
                    cdcs = [""]
                else:
                    cdcs = [item.ship_to_id]
                pos = Pos(
                    pos_id=int(item.pos_id),
                    hq_id=item.hq_id,
                    sold_to_id=str(int(item.sold_to_id)) if item.sold_to_id else None,
                    reseller_name=item.reseller_name,
                    sold_to_name=item.sold_to_name,
                    pos_name=item.pos_name,
                    pos_type=item.pos_type,
                    location_type=item.pos_location_type,
                    category=item.category,
                    district_group=None if item.district_group is None else int(item.district_group),
                    cdcs=cdcs
                )
                pos.set_hq_info(sub_rtm=item.rtm,
                                supplier_hq_id=item.supplier_hq_id,
                                reseller_chinese_name=item.reseller_chinese_name,
                                supplier_name=item.supplier_name)
                res.append(pos)
            res = sort_pos(res)  # 排序，保证每次读到的都是有序的pos列表
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return res
