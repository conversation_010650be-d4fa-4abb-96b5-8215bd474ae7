import traceback

from domain.mono.pos_allocation.entity.mpn import Mpn
from util.const import ErrorExcept, ErrCode
from util.mono_allocation_base import *


class HighRunner(MonoAllocationBase):
    __tablename__ = "excel_high_runner"
    __table_args__ = {"extend_existing": True}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    rtm = Column(String(16), comment='')
    lob = Column(String(16), comment='')
    model = Column(String(64))
    project_code = Column(String(32), comment='')
    mpn = Column(String(32), comment='')
    description = Column(String(256), comment='')
    high_runner = Column(String(16), comment='')
    sustaining_npi = Column(String(16), comment='')
    minimum_inv_qty = Column(Integer, comment='')
    twoi = Column(Integer, comment='')
    mpn_type = Column(String(64), comment='')
    version_id = Column(String(64), comment='')

    @classmethod
    def query_npp(cls, version_id) -> list[Mpn]:
        s = MonoAllocationSession()
        mpns = []
        try:
            ret = s.query(cls).filter(cls.high_runner == "NPP").filter(cls.version_id == version_id).filter(cls.rtm == 'Lifestyle').order_by(cls.lob, cls.model, cls.mpn).all()
            if len(ret) == 0:
                return mpns
            for item in ret:
                lob = item.lob
                if lob == "CPU":
                    lob = "Mac"
                mpns.append(Mpn(mpn_id=item.mpn, lob=lob, sub_lob=item.model, is_npp=True,
                                npp_min_inventory=item.minimum_inv_qty, twoi=item.twoi))
        except Exception as e:
            logger.exception(e)
            raise ErrorExcept(ErrCode.DBQueryError, "query excel_high_runner failed" + traceback.format_exc())
        finally:
            s.close()
        return mpns
