from domain.mono.pos_allocation.entity.mpn import Mpn
from domain.mono.pos_allocation.entity.pos import Pos
from domain.mono.pos_allocation.entity.pos_eoh import PosEoh
from util.mono_allocation_base import *
from data.mysqls.mono.sync_record_log import SyncRecordLog


class DsPosEoh(MonoAllocationBase):
    __tablename__ = "stock_info_sync_record"
    __table_args__ = {"schema": "Allocation",
                      "extend_existing": True}

    id = Column(Integer, autoincrement=True, primary_key=True)
    pos_id = Column(String(256))
    mpn = Column(String(256))
    fiscal_week_year = Column(Integer)
    lob = Column(String(16))
    remain_num = Column(Integer)
    version_id = Column(String(64))
    create_time = Column(DateTime)
    last_week_sellout_num = Column(Integer)
    last_five_week_avg = Column(Float)

    @classmethod
    def query_pos_eoh(cls, mpns: list[Mpn], pos_list: list[Pos]) -> list[PosEoh]:
        """
        1. pos_id 2000, 每次一个mpn
        查询最新的version的所有pos，所有lob，所有mpn的剩余库存
        select * from stock_info_sync_record where version_id =
        (select sync_version from sync_record_log where sync_type = 'sync_stock_info' order by id desc)
        """
        s = MonoAllocationSession()
        ret = []
        try:
            mpn_ids = [mpn.id for mpn in mpns]
            pos_ids = [pos.pos_id for pos in pos_list]
            # 因为数据量比较大，耗时较长，只查需要的字段即可
            query_result = (s.query(cls.pos_id, cls.lob, cls.mpn, cls.remain_num.label("eoh"))
                   .filter(cls.version_id ==
                           SyncRecordLog.subquery_sync_version(s))
                   .filter(cls.mpn.in_(mpn_ids))
                   .filter(cls.pos_id.in_(pos_ids))
                   .all())
            for item in query_result:
                ret.append(
                    PosEoh(
                        item.pos_id,
                        item.lob,
                        item.mpn,
                        item.eoh,
                    )
                )
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

    @classmethod
    def query_stock_by_fiscal_week_year(cls, fiscal_week_year: int) -> list[PosEoh]:
        s = MonoAllocationSession()
        ret = []
        try:
            # 因为数据量比较大，耗时较长，只查需要的字段即可
            query_result = (s.query(cls.pos_id, cls.lob, cls.mpn, cls.remain_num.label("eoh"),
                                    cls.last_five_week_avg.label("sell_out_five_week_avg"))
                            .filter(cls.fiscal_week_year == fiscal_week_year).all()
                            )
            for item in query_result:
                stock = PosEoh(
                    item.pos_id,
                    item.lob,
                    item.mpn,
                    item.eoh,
                )
                stock.set_sell_out_five_week_avg(item.sell_out_five_week_avg)
                ret.append(stock)
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret
