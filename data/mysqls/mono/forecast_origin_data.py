from sqlalchemy import desc

from domain.mono.pos_allocation.entity.mpn import Mpn
from domain.mono.pos_allocation.entity.pos import Pos
from util.mono_allocation_base import *
from util.mono_allocation_base import MonoAllocationBase, MonoAllocationSession


class ForecastOriginData(MonoAllocationBase):
    __tablename__ = "forecast_origin_data"
    __table_args__ = {"schema": "Allocation",
                      "extend_existing": True}
    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    fiscal_qtr_week_name = Column(String(64))
    customer_sold_to_id = Column(String(64), comment='')
    reseller_name = Column(String(100), comment='')
    rtm = Column(String(64), comment='')
    measure_cd = Column(String(64), comment='')
    mpn = Column(String(64), comment='')
    lob = Column(String(64), comment='')
    sub_lob = Column(String(64), comment='')
    hq_id = Column(String(64), comment='')
    t2_hq_id = Column(String(64), comment='')
    t2_reseller_name = Column(String(64), comment='')
    pos_id = Column(Integer, comment='')
    pos_name = Column(String(100), comment='')
    model = Column(String(45), comment='')
    prediction_wk1 = Column(Float, comment='')
    prediction_wk2 = Column(Float, comment='')
    prediction_wk3 = Column(Float, comment='')
    prediction_wk4 = Column(Float, comment='')
    prediction_wk5 = Column(Float, comment='')
    prediction_wk6 = Column(Float, comment='')
    prediction_wk7 = Column(Float, comment='')
    prediction_wk8 = Column(Float, comment='')
    prediction_wk9 = Column(Float, comment='')
    prediction_wk10 = Column(Float, comment='')
    prediction_wk11 = Column(Float, comment='')
    prediction_wk12 = Column(Float, comment='')
    prediction_wk13 = Column(Float, comment='')
    sell_out_five_week_avg = Column(Float, comment='')
    high_low_runner = Column(String(45), comment='')
    npp_reseller = Column(String(45), comment='')
    reseller_npp_type = Column(String(45), comment='')
    query_date = Column(String(64), comment='')
    create_time = Column(DateTime, comment='create time')
    customer_sold_to_id_origin = Column(String(100), comment='')
    reseller_name_origin = Column(String(100), comment='')

    @classmethod
    def query_pos_mpn_fcst(cls, mpns: list[Mpn], pos_list: list[Pos]) -> list:
        s = MonoAllocationSession()
        ret = []
        try:
            sub_query = s.query(cls.fiscal_qtr_week_name).order_by(desc(cls.id)).limit(1).subquery()
            mpn_ids = [mpn.id for mpn in mpns]
            pos_ids = [pos.pos_id for pos in pos_list]

            ret = (s.query(cls).filter(cls.fiscal_qtr_week_name == sub_query)
                   .filter(cls.pos_id.in_(pos_ids))
                   .filter(cls.mpn.in_(mpn_ids))
                   .all())
            # for item in ret:
            #     if item.prediction_wk1 + item.prediction_wk2 + item.prediction_wk3 >0:
            #         print(item.pos_id,item.prediction_wk1, item.prediction_wk2, item.prediction_wk3)
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return ret
