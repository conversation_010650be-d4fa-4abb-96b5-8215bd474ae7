from domain.mono.pos_allocation.entity.mpn import Mpn
from util.mono_allocation_base import *


class PosPlan(MonoAllocationBase):
    __tablename__ = "pos_plan"
    __table_args__ = {"schema": "Allocation",
                      "extend_existing": True}

    id = Column(Integer, autoincrement=True, primary_key=True)
    allocation_week = Column(String(100), comment="用户所选择的Supply周，即本次分货是分的Reseller哪一周的货")
    status = Column(Integer, comment="表示当前分货计划所处状态，会有以下几种情况\n1. Draft：草稿状态，用户在提交前保存下来的表单处于此状态\n2. Computing：计算中状态，用户提交计算任务，后台正在计算过程中时\n3. Waiting for publish：待发布状态，计算任务产出，但用户未发布最终报告时\n4. Published：已发布状态，用户生成最终报告后的状态\n5.error\n6. Publishing:发布中状态")

    @classmethod
    def subquery_plan_id(cls, session, allocation_week: str, status: int = 4):
        # (SELECT merged_plan_id FROM pos_plan WHERE STATUS = 4 AND allocation_week='FY24Q4W1' ORDER BY id DESC LIMIT 1
        return (session.query(cls.id)
                .filter(cls.allocation_week == allocation_week)
                .filter(cls.status == status)
                .order_by(cls.id.desc()).limit(1).subquery())

    @classmethod
    def subquery_plan_id_no_session(cls, allocation_week: str, status: int = 4):
        s = MonoAllocationSession()
        try:
            ret = (s.query(cls.id)
                    .filter(cls.allocation_week == allocation_week)
                    .filter(cls.status == status)
                    .order_by(cls.id.desc()).all())
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return ret

    @classmethod
    def update_status_by_plan_id(cls, plan_id: int, status: int):
        s = MonoAllocationSession()
        try:
            s.query(cls).filter(cls.id == plan_id).update({"status": status})
            s.commit()
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
