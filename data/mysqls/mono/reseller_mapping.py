from util.mono_allocation_base import *


class ResellerMapping(MonoAllocationBase):
    __tablename__ = "excel_reseller_mapping"
    __table_args__ = {"extend_existing": True}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    hq_id = Column(String(128), comment='')
    sold_to_id = Column(String(128), comment='')
    rtm = Column(String(16), comment='')
    reseller_type = Column(String(16), comment='')
    reseller_name = Column(String(128), comment='')
    reseller_simp_name = Column(String(128), comment='')
    reseller_chinese_name = Column(String(128), comment='')
    region = Column(String(32), comment='')
    am = Column(String(32), comment='')
    supplier_name = Column(String(128), comment='')
    supplier = Column(String(128), comment='')
    supplier_hq_id = Column(String(128), comment='')
    customer_sold_to_id = Column(String(128), comment='')
    npp_type = Column(String(16), comment='')
    npp_supplier_sold_to_id = Column(String(128), comment='')
    npp_supplier_name = Column(String(128), comment='')
    version_id = Column(String(128), comment='')

    @classmethod
    def query_by_version(cls, version_id:str) -> pd.DataFrame:
        s = MonoAllocationSession()
        try:
            q = s.query(cls).filter(cls.version_id == version_id).order_by(cls.id.desc())
            ret = pd.read_sql_query(q.statement, MonoAllocationSession)
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return ret
