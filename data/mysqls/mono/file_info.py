import traceback

from sqlalchemy import desc

from util.const import ErrorExcept, ErrCode
from util.mono_allocation_base import *


class FileInfo(MonoAllocationBase):
    __tablename__ = "file_info"
    __table_args__ = {"extend_existing": True}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    bus_key = Column(String(255), comment='')
    file_name = Column(String(255), comment='')
    file_path = Column(String(255), comment='')
    start_time = Column(DateTime)
    create_time = Column(DateTime)
    status = Column(Integer)

    @classmethod
    def bulk_save(cls, objs: list):
        # 一次写入所有数据
        s = MonoAllocationSession()
        ret = []
        try:
            s.execute(cls.__table__.insert(), objs)
            s.commit()
        except Exception as e:
            logger.exception(e)
            raise ErrorExcept(ErrCode.DBInsert, "insert to db failed" +
                              traceback.format_exc() + "params:" + str(objs))
        finally:
            s.close()
        return ret

    @classmethod
    def query_file_info_by_bus_key(cls, bus_key: str):
        s = MonoAllocationSession()
        try:
            # 重新计算的话，会生成bus_key一样的文件, 所以取id最大的那一个
            ret = s.query(cls).filter(cls.bus_key == bus_key).order_by(desc(cls.id)).all()
            if ret:
                ret = ret[0]
        except Exception as e:
            logger.exception(str(e))
            raise ErrorExcept(ErrCode.DBQueryParams, "query file info failed" + str(e))
        finally:
            s.close()
        return ret
