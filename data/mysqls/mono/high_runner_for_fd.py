from domain.mono.pos_allocation.entity.mpn import Mpn
from util.mono_allocation_base import *


class HighRunnerForFd(MonoAllocationBase):
    __tablename__ = "excel_high_runner_for_fd_new"
    __table_args__ = {"extend_existing": True}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    lob = Column(String(16), comment='')
    fph3 = Column(String(64))
    mpn = Column(String(32), comment='')
    mpn_description = Column(String(256), comment='')
    npp_mpn = Column(String(16), comment='')
    npp_minimum_inv = Column(Integer, comment='')
    version_id = Column(String(64), comment='')
    lifestyle_high_runner = Column(String(64), comment='')
    mono_high_runner = Column(String(64), comment='')
    ecpp_mpn = Column(String(64), comment='')

    @classmethod
    def query_npp(cls, version_id)->list[Mpn]:
        s = MonoAllocationSession()
        mpns = []
        try:
            ret = s.query(cls).filter(cls.npp_mpn == "Y").filter(cls.version_id==version_id).order_by(cls.lob, cls.fph3, cls.mpn).all() # todo 筛选最大的版本
            if len(ret) == 0:
                return mpns
            for item in ret:
                lob = item.lob
                if lob == "CPU":
                    lob = "Mac"
                mpns.append(Mpn(mpn_id=item.mpn, lob=lob, sub_lob=item.fph3, is_npp=True, npp_min_inventory=item.npp_minimum_inv))
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return mpns

    @classmethod
    def query_all(cls, version_id) -> list[Mpn]:
        s = MonoAllocationSession()
        mpns = []
        try:
            sql_statement = f'''
                            SELECT 
                                mpn, 
                                lob,
                                fph3,
                                npp_minimum_inv,
                                npp_mpn,
                                case when lifestyle_high_runner ='Y' then 'H' when npp_mpn ='Y' then 'NPP' when ecpp_mpn ='Y' then 'eCPP' else 'L' end as lifestyle_type, 
                                case when mono_high_runner ='Y' then 'H' when npp_mpn ='Y' then 'NPP' when ecpp_mpn ='Y' then 'eCPP' else 'L' end as mono_type
                                FROM excel_high_runner_for_fd_new  
                            where lob in ('iPad','CPU','Watch','iPhone')
                            and version_id ='{version_id}'
                         '''
            ret = s.execute(sql_statement).fetchall()
            if len(ret) == 0:
                return mpns

            for item in ret:
                lob = item.lob
                if lob == "CPU":
                    lob = "Mac"
                is_npp = True if item.npp_mpn is not None and item.npp_mpn == 'Y' else False
                mpns.append(Mpn(mpn_id=item.mpn, lob=lob, sub_lob=item.fph3, is_npp=is_npp,
                                npp_min_inventory=item.npp_minimum_inv, lifestyle_type=item.lifestyle_type,
                                mono_type=item.mono_type))

        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return mpns
