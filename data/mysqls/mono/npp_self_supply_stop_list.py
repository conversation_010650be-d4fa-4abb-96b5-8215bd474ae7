from util.mono_allocation_base import *
from domain.mono.pos_allocation.entity.pos_sublob_stop import PosSublobStop


class ExcelNppSelfSupplyStopList(MonoAllocationBase):
    __tablename__ = "excel_npp_self_supply_stop_list"
    __table_args__ = {"extend_existing": True}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    customer_sold_to_id = Column(String(64), comment='')
    customer_sold_to_name = Column(String(128), comment='')
    hq_id = Column(String(64), comment='')
    reseller_name = Column(String(128), comment='')
    pos_id = Column(Integer, comment='')
    pos_name = Column(String(128), comment='')
    model = Column(String(128), comment='')
    version_id = Column(String(128), comment='')

    @classmethod
    def query_stop_records(cls, version_id: str, fiscal_week: str = None):
        s = MonoAllocationSession()
        ret = []
        try:
            query_result = s.query(cls.customer_sold_to_id.label("sold_to_id"),
                                   cls.pos_id,
                                   cls.model.label("sub_lob")
                                   ).filter(cls.version_id == version_id).all()
            for item in query_result:
                ret.append(
                    PosSublobStop(
                        fiscal_week,
                        item.pos_id,
                        item.sold_to_id,
                        item.sub_lob,
                    )
                )
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return ret
