from domain.mono.pos_allocation.entity.mpn import Mpn
from domain.mono.pos_allocation.entity.pos import Pos
from util.gc_dmp_base import *


class MonoForecastResult(GcDmpBase):
    __tablename__ = 'app_fast_mono_forecasting_result_wi'
    __table_args__ = {'schema': 'gc_dmp_fast'}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    apple_id = Column(String(64), comment='') # 数据库中类型是Int
    RTM = Column(String(64), comment='')
    measure_cd = Column(String(64), comment='')
    prod_id = Column(String(64), comment='')
    prod_level_1_desc = Column(String(64), comment='')
    prod_level_3_desc = Column(String(64), comment='')
    prediction_wk1 = Column(Float, comment='')
    prediction_wk2 = Column(Float, comment='')
    prediction_wk3 = Column(Float, comment='')
    prediction_wk4 = Column(Float, comment='')
    prediction_wk5 = Column(Float, comment='')
    prediction_wk6 = Column(Float, comment='')
    prediction_wk7 = Column(Float, comment='')
    prediction_wk8 = Column(Float, comment='')
    prediction_wk9 = Column(Float, comment='')
    prediction_wk10 = Column(Float, comment='')
    prediction_wk11 = Column(Float, comment='')
    prediction_wk12 = Column(Float, comment='')
    prediction_wk13 = Column(Float, comment='')
    query_date = Column(String(64), comment='')
    week_begin_dt = Column(String(64), comment='')
    week_date = Column(String(64), comment='')
    is_high_runner = Column(SmallInteger, comment='')
    inference_type = Column(String(64), comment='')
    create_time_og = Column(String(64), comment='原表创建时间')
    create_time = Column(DateTime, comment='create time')
    update_time = Column(DateTime, comment='update time')

    @classmethod
    def query_pos_mpn_fcst(cls, fiscal_week: str, mpns: list[Mpn], pos_list: list[Pos]) -> list:
        s = GcDmpSession()
        ret = []
        try:
            mpn_ids = [mpn.id for mpn in mpns]
            pos_ids = [pos.pos_id for pos in pos_list]

            ret = (s.query(cls.apple_id.label("pos_id"),
                           cls.prod_id.label("mpn"),
                           cls.prediction_wk1,
                           cls.prediction_wk2,
                           cls.prediction_wk3,
                           )
                   .filter(cls.week_date == fiscal_week)
                   .filter(cls.apple_id.in_(pos_ids))
                   .filter(cls.prod_id.in_(mpn_ids))
                   .all())
            for item in ret:
                if item.prediction_wk1 + item.prediction_wk2 + item.prediction_wk3 >0:
                    print(item.pos_id,item.prediction_wk1, item.prediction_wk2, item.prediction_wk3)
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return ret
