import traceback

import numpy

from domain.demand.entity.const import RTMS, RTMS_NO_RETAIL
from util.const import <PERSON><PERSON>rExcept, ErrCode
from util.fast_lite_base import *


class DemandBySoldtoPool(FASTLiteBase):
    __tablename__ = "demand_by_soldto_pool"

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    fiscal_week = Column(String(16))
    region = Column(String(32))
    rtm = Column(String(32))
    sub_rtm = Column(String(64))
    sold_to_id = Column(String(32))
    sold_to_name = Column(String(128))
    lob = Column(String(16))
    sub_lob = Column(String(16))
    nand = Column(String(16))
    color = Column(String(16))
    mpn = Column(String(16))
    hr_lr = Column(String(32))
    shipment_plan_cw = Column(Integer)
    shipment_plan_cw1 = Column(Integer)
    shipment_plan_cw2 = Column(Integer)
    avg_sales_fcst_2_4 = Column(Float)
    avg_sales_fcst_3_5 = Column(Float)
    ub_eoh = Column(Integer)
    forecast_cw_ml = Column(Integer)
    forecast_cw1_ml = Column(Integer)
    forecast_cw_sales = Column(Integer)
    forecast_cw1_sales = Column(Integer)
    forecast_cw2_sales = Column(Integer)
    forecast_cw3_sales = Column(Integer)
    forecast_cw4_sales = Column(Integer)
    forecast_cw5_sales = Column(Integer)
    forecast_cw6_sales = Column(Integer)
    forecast_cw7_sales = Column(Integer)
    forecast_cw8_sales = Column(Integer)
    base = Column(Float, comment='CW_ML_FORECAST + CW1_ML_FORECAST - UB_EOH - CW_SHIPMENT_PLAN')
    cw1_ideal_demand = Column(Float)
    cw2_ideal_demand = Column(Float)
    cw1_ideal_demand_origin = Column(Float)
    cw2_ideal_demand_origin = Column(Float)
    avg_ub_1_5 = Column(Float)
    normalized_fcst_cw2 = Column(Float)
    normalized_fcst_cw3 = Column(Float)
    normalized_fcst_cw6 = Column(Float)
    normalized_fcst_cw4 = Column(Float)
    normalized_fcst_cw5 = Column(Float)
    dn_cw1 = Column(Float)
    dn_cw2 = Column(Float)
    final_dn_cw1 = Column(Float)
    final_dn_cw2 = Column(Float)
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    cw1_update_time = Column(DateTime)
    cw2_update_time = Column(DateTime)
    df_cw1_adjusted = Column(Float)
    df_cw2_adjusted = Column(Float)
    dn_cw1_adjusted = Column(Float)
    dn_cw2_adjusted = Column(Float)
    df_cw1 = Column(Float)
    df_cw2 = Column(Float)
    base_demand_cw1 = Column(Float)
    base_demand_cw2 = Column(Float)
    base_demand_cw1_adjusted = Column(Float)
    base_demand_cw2_adjusted = Column(Float)

    @classmethod
    def batch_insert(cls, objs: list):
        s = FASTLiteSession()
        try:
            now = datetime.now()
            for obj in objs:
                obj['create_time'] = now
                obj['update_time'] = now
            insert_stmt = f"""
                INSERT INTO {cls.__tablename__} 
                (fiscal_week, region, rtm, sub_rtm, sold_to_id, sold_to_name, lob, sub_lob, nand, color, mpn, hr_lr, twos, create_time, update_time) 
                 VALUES 
                    (:fiscal_week, :region, :rtm, :sub_rtm, :sold_to_id, :sold_to_name, :lob, :sub_lob, 
                    :nand, :color, :mpn, :hr_lr, :twos, :create_time, :update_time)
                ON DUPLICATE KEY UPDATE 
                 region = VALUES(region), rtm = VALUES(rtm), sub_rtm = VALUES(sub_rtm), 
                 sold_to_name = VALUES(sold_to_name), lob = VALUES(lob), sub_lob = VALUES(sub_lob), 
                 nand = VALUES(nand), color = VALUES(color),  hr_lr = VALUES(hr_lr),  twos = VALUES(twos), update_time = VALUES(update_time)
            """
            s.execute(insert_stmt, objs)
            s.commit()
        except Exception as e:
            logger.exception(traceback.format_exc())
            raise ErrorExcept(ErrCode.DBInsert, "insert to db failed" + traceback.format_exc())
        finally:
            s.close()
        return True

    @classmethod
    def batch_update(cls, objs: list, fields_to_update):
        s = FASTLiteSession()
        try:
            now = datetime.now()
            for obj in objs:
                obj['update_time'] = now
            insert_parts = [f"{field}" for field in fields_to_update]
            value_parts = [f":{field}" for field in fields_to_update]
            key_parts = [f"{field} = VALUES({field})" for field in fields_to_update]
            insert_stmt = f"""
                            INSERT INTO {cls.__tablename__} 
                            (fiscal_week, sold_to_id, mpn,  {', '.join(insert_parts)})
                             VALUES 
                                (:fiscal_week, :sold_to_id, :mpn,  {', '.join(value_parts)})
                            ON DUPLICATE KEY UPDATE 
                                 {', '.join(key_parts)}
                        """
            s.execute(insert_stmt, objs)
            s.commit()
        except Exception as e:
            logger.exception(traceback.format_exc())
            raise ErrorExcept(ErrCode.DBInsert, "insert to db failed" + traceback.format_exc())
        finally:
            s.close()
        return True

    @classmethod
    def batch_update_(cls, fields_to_update: list, objs: list):
        s = FASTLiteSession()
        try:
            now = datetime.now()
            for obj in objs:
                obj['create_time'] = now
                obj['update_time'] = now
            # forecast_cw2_dfa = :forecast_cw2_dfa,forecast_cw3_dfa = :forecast_cw3_dfa,
            # forecast_cw4_dfa = :forecast_cw4_dfa,forecast_cw5_dfa = :forecast_cw5_dfa,
            # forecast_cw6_dfa = :forecast_cw6_dfa,forecast_cw7_dfa = :forecast_cw7_dfa,
            # forecast_cw8_dfa = :forecast_cw8_dfa
            set_parts = [f"{field} = :{field}" for field in fields_to_update]
            insert_stmt = f"""
                    UPDATE
                    {cls.__tablename__}
                    SET
                    {', '.join(set_parts)}
                    where fiscal_week = :fiscal_week and region = :region and mpn = :mpn
                """
            s.execute(insert_stmt, objs)
            s.commit()
        except Exception as e:
            logger.exception(e)
            raise ErrorExcept(ErrCode.DBInsert, "insert to db failed" + e)
        finally:
            s.close()
        return True

    @classmethod
    def get_dataframe_result(cls, fiscal_week: str, rtms: list = RTMS_NO_RETAIL):
        s = FASTLiteSession()
        ret = pd.DataFrame()
        try:
            q = s.query(cls).filter(cls.fiscal_week == fiscal_week,
                                    cls.create_time.isnot(None)).order_by(cls.id.asc())
            if rtms:
                q = q.filter(cls.rtm.in_(rtms))
            ret = pd.read_sql_query(q.statement, fast_lite_engine)
        except Exception as e:
            logger.exception(str(e))
        finally:
            s.close()
        return ret

    @classmethod
    def get_miss_result(cls, fiscal_week: str, sold_to_ids: list[str], sales_fcst_flag=False, ml_fcst_flag=False,
                        eoh_flag=False, rtms: list = RTMS_NO_RETAIL):
        s = FASTLiteSession()
        ret = pd.DataFrame()
        try:
            q = s.query(cls.sold_to_id, cls.sold_to_name, cls.rtm, cls.mpn).filter(cls.fiscal_week == fiscal_week).filter(
                cls.sold_to_id.notin_(sold_to_ids))
            if rtms:
                q = q.filter(cls.rtm.in_(rtms))
            if sales_fcst_flag:
                q = q.filter(cls.forecast_cw_sales.is_(None))
            elif ml_fcst_flag:
                q = q.filter(cls.forecast_cw_ml.is_(None)).filter(cls.rtm != 'Online')
            elif eoh_flag:
                q = q.filter(cls.ub_eoh.is_(None))
            else:
                return ret
            ret = pd.read_sql_query(q.statement, fast_lite_engine)
        except Exception as e:
            logger.exception(str(e))
        finally:
            s.close()
        return ret

    @classmethod
    def batch_update_fields(cls, fields_to_update: list, objs: list):
        s = FASTLiteSession()
        try:
            now = datetime.now()
            for obj in objs:
                obj['update_time'] = now
            set_parts = [f"{field} = :{field}" for field in fields_to_update]
            update_stmt = f"""
                UPDATE
                {cls.__tablename__}
                SET
                {', '.join(set_parts)}
                WHERE fiscal_week = :fiscal_week AND sold_to_id = :sold_to_id AND mpn = :mpn
            """
            s.execute(update_stmt, objs)
            s.commit()
        except Exception as e:
            logger.exception(e)
            raise ErrorExcept(ErrCode.DBInsert, "update db demand_by_soldto_pool" + e)
        finally:
            s.close()
        return True

    @classmethod
    def get_by_fiscal_week(cls, fiscal_week: str, rtms: list = RTMS_NO_RETAIL, sub_lobs: list = [], 
                           soldto_ids: Optional[list[str]] = None) -> pd.DataFrame:
        s = FASTLiteSession()
        res = pd.DataFrame()
        try:
            query = (s.query(*cls.__table__.columns)
                     .filter(cls.fiscal_week == fiscal_week).order_by(cls.id.asc()))
            if rtms:
                query = query.filter(cls.rtm.in_(rtms))
            if sub_lobs:
                query = query.filter(cls.sub_lob.in_(sub_lobs))
            if soldto_ids is not None:
                query = query.filter(cls.sold_to_id.in_(soldto_ids))
            res = pd.read_sql_query(query.statement, fast_lite_engine)
            return res
        except Exception as e:
            logger.error(e)
            raise ErrorExcept(ErrCode.DBQueryError, "db query error: " + str(e))
        finally:
            s.close()

    @classmethod
    def get_sublob_adjust_info_by_fiscal_week(cls, fiscal_week: str, rtms: list = RTMS_NO_RETAIL) -> list[dict]:
        s = FASTLiteSession()
        ret = []
        try:
            query = (s.query(cls.sub_lob,
                             func.max(cls.cw1_update_time).label('cw1_update_time'),
                             func.max(cls.cw2_update_time).label('cw2_update_time'))
                     .filter(cls.fiscal_week == fiscal_week).order_by(cls.id.asc()).group_by(cls.sub_lob))
            if rtms:
                query = query.filter(cls.rtm.in_(rtms))
            query_all = query.all()
            ret = [{
                'sub_lob': x.sub_lob,
                'cw1_update_time': x.cw1_update_time,
                'cw2_update_time': x.cw2_update_time} for x in query_all]
            return ret
        except Exception as e:
            logger.error(e)
            raise ErrorExcept(ErrCode.DBQueryError, "db query error: " + str(e))
        finally:
            s.close()

    @classmethod
    def sum_by_sub_rtm(cls, fiscal_week: str, rtms: list = RTMS_NO_RETAIL, sub_lobs: list = []) -> pd.DataFrame:
        s = FASTLiteSession()
        res = pd.DataFrame()
        try:
            query = (s.query(cls.rtm, cls.sub_rtm,
                             func.sum(cls.final_dn_cw1).label('final_dn_cw1'), func.sum(cls.dn_cw1_adjusted).label('dn_cw1_adjusted'),
                             func.sum(cls.final_dn_cw2).label('final_dn_cw2'), func.sum(cls.dn_cw2_adjusted).label('dn_cw2_adjusted'),
                             func.sum(cls.df_cw1).label('df_cw1'), func.sum(cls.df_cw1_adjusted).label('df_cw1_adjusted'),
                             func.sum(cls.df_cw2).label('df_cw2'), func.sum(cls.df_cw2_adjusted).label('df_cw2_adjusted'),
                             func.sum(cls.cw1_ideal_demand).label('cw1_ideal_demand'), func.sum(cls.cw2_ideal_demand).label('cw2_ideal_demand'),
                             func.sum(cls.base_demand_cw1).label('base_demand_cw1'), func.sum(cls.base_demand_cw1_adjusted).label('base_demand_cw1_adjusted'),
                             func.sum(cls.base_demand_cw2).label('base_demand_cw2'), func.sum(cls.base_demand_cw2_adjusted).label('base_demand_cw2_adjusted'),
                             func.sum(cls.dn_cw1).label('dn_cw1'), func.sum(cls.dn_cw2).label('dn_cw2'),
                             func.sum(cls.forecast_cw_sales).label('forecast_cw_sales'),
                             func.sum(cls.forecast_cw1_sales).label('forecast_cw1_sales'),
                             func.sum(cls.forecast_cw2_sales).label('forecast_cw2_sales'),
                             func.sum(cls.forecast_cw3_sales).label('forecast_cw3_sales'),
                             func.sum(cls.forecast_cw4_sales).label('forecast_cw4_sales'),
                             func.sum(cls.forecast_cw5_sales).label('forecast_cw5_sales'),
                             func.sum(cls.forecast_cw6_sales).label('forecast_cw6_sales'),
                             func.sum(cls.forecast_cw_ml).label('forecast_cw_ml'),
                             func.sum(cls.forecast_cw1_ml).label('forecast_cw1_ml'),
                             func.sum(cls.normalized_fcst_cw2).label('normalized_fcst_cw2'),
                             func.sum(cls.normalized_fcst_cw3).label('normalized_fcst_cw3'),
                             func.sum(cls.normalized_fcst_cw4).label('normalized_fcst_cw4'),
                             func.sum(cls.normalized_fcst_cw5).label('normalized_fcst_cw5'),
                             func.sum(cls.normalized_fcst_cw6).label('normalized_fcst_cw6'),
                             func.sum(cls.avg_ub_1_5).label('avg_ub_1_5'),
                             func.sum(cls.base).label('base'),
                             func.sum(cls.shipment_plan_cw1).label('shipment_plan_cw1'),
                             func.sum(cls.shipment_plan_cw2).label('shipment_plan_cw2'),
                             )
                     .filter(cls.fiscal_week == fiscal_week).order_by(cls.id.asc()).group_by(cls.rtm, cls.sub_rtm))
            if rtms:
                query = query.filter(cls.rtm.in_(rtms))
            if sub_lobs:
                query = query.filter(cls.sub_lob.in_(sub_lobs))
            res = pd.read_sql_query(query.statement, fast_lite_engine)
            return res
        except Exception as e:
            logger.error(e)
            raise ErrorExcept(ErrCode.DBQueryError, "db query error: " + str(e))
        finally:
            s.close()

    @classmethod
    def get_distinct_mpn_by_week(cls, fiscal_week: str) -> list[str]:
        s = FASTLiteSession()
        ret = []
        try:
            query = (s.query(cls.mpn).distinct()
                     .filter(cls.fiscal_week == fiscal_week).order_by(cls.id.asc()))
            res = query.all()
            ret = [item.mpn for item in res]
            return ret
        except Exception as e:
            logger.error(e)
            raise ErrorExcept(ErrCode.DBQueryError, "db query error: " + str(e))
        finally:
            s.close()
    
    @classmethod
    def get_jd_self_run_shipment_by_week(cls, fiscal_week: str, sub_rtm: str = 'JD self-run') -> pd.DataFrame:
        s = FASTLiteSession()
        res = pd.DataFrame()
        try:
            # SELECT mpn, `shipment_plan_cw1` AS shipment_plan_cw1_total, `shipment_plan_cw2` AS shipment_plan_cw2_total,`df_cw1` as df_cw1_origin, df_cw2 as df_cw2_origin FROM demand_by_soldto_pool WHERE fiscal_week='FY24Q4W9' AND sub_rtm='JD self-run';
            query = (s.query(cls.mpn,
                             cls.shipment_plan_cw1.label("shipment_plan_cw1_total"),
                             cls.shipment_plan_cw2.label("shipment_plan_cw2_total"),
                             cls.df_cw1.label("df_cw1_origin"),
                             cls.df_cw2.label("df_cw2_origin"),
                             cls.df_cw1_adjusted.label("df_cw1_adjusted_origin"),
                             cls.df_cw2_adjusted.label("df_cw2_adjusted_origin"),
                             cls.cw1_ideal_demand.label("di_cw1_origin"),
                             cls.cw2_ideal_demand.label("di_cw2_origin"),
                             cls.dn_cw1.label("dn_cw1_origin"),
                             cls.dn_cw2.label("dn_cw2_origin"),
                             cls.dn_cw1_adjusted.label("dn_cw1_adjusted_origin"),
                             cls.dn_cw2_adjusted.label("dn_cw2_adjusted_origin"),
                             cls.base_demand_cw1.label("base_demand_cw1_origin"),
                             cls.base_demand_cw2.label("base_demand_cw2_origin"),
                             cls.base_demand_cw1_adjusted.label("base_demand_cw1_adjusted_origin"),
                             cls.base_demand_cw2_adjusted.label("base_demand_cw2_adjusted_origin"),
                            )
                     .filter(cls.fiscal_week == fiscal_week, cls.sub_rtm == sub_rtm)
                    )
            res = pd.read_sql_query(query.statement, fast_lite_engine)
            return res
        except Exception as e:
            logger.error(e)
            raise ErrorExcept(ErrCode.DBQueryError, "db query error: " + str(e))
        finally:
            s.close()

    @classmethod
    def get_distinct_sub_lob_by_week(cls, fiscal_week: str, sub_rtm: str = 'JD self-run') -> list:
        s = FASTLiteSession()
        res = []
        try:

            query = (s.query(cls.sub_lob).distinct()
                     .filter(cls.fiscal_week == fiscal_week, cls.sub_rtm == sub_rtm)
                    )
            res = query.all()
            res = [item.sub_lob for item in res]
            return res
        except Exception as e:
            logger.error(e)
            raise ErrorExcept(ErrCode.DBQueryError, "db query error: " + str(e))
        finally:
            s.close()
