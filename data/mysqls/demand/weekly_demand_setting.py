import traceback
from datetime import datetime

from domain.demand.entity.demand_data_config import DemandDataConfig
from util.const import ErrorExcept, ErrCode
from util.mybusiness_base import Column, String, Float, DateTime, MyBusinessBase, MyBusinessSession, logger


class WeeklyDemandSetting(MyBusinessBase):
    __tablename__ = "weekly_demand_setting"
    __table_args__ = {'schema': 'mybusiness'}

    fiscal_week = Column(String(32), primary_key=True)
    sold_to_id = Column(String(128))
    lob = Column(String(32))
    sub_lob = Column(String(64))
    jd_self_run_mix_cw1 = Column(Float)
    jd_self_run_mix_cw2 = Column(Float)
    adjusted_demand_cw1 = Column(String(256))
    adjusted_demand_cw2 = Column(String(256))
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    @classmethod
    def batch_insert(cls, objs: list, fields_to_update, fiscal_week: str = None):
        s = MyBusinessSession()
        try:
            now = datetime.now()
            for obj in objs:
                if fiscal_week:
                    obj['fiscal_week'] = fiscal_week
                obj['create_time'] = now
                obj['update_time'] = now
            insert_parts = [f"{field}" for field in fields_to_update]
            value_parts = [f":{field}" for field in fields_to_update]
            key_parts = [f"{field} = VALUES({field})" for field in fields_to_update]
            insert_stmt = f"""
                INSERT INTO {cls.__tablename__} 
                (fiscal_week, sold_to_id, sub_lob, create_time, {', '.join(insert_parts)})
                VALUES 
                    (:fiscal_week, :sold_to_id, :sub_lob, :create_time, {', '.join(value_parts)})
                ON DUPLICATE KEY UPDATE 
                    {', '.join(key_parts)}
            """
            s.execute(insert_stmt, objs)
            s.commit()
        except Exception as e:
            logger.exception(traceback.format_exc())
            raise ErrorExcept(ErrCode.DBInsert, f"insert to {cls.__tablename__} failed" + traceback.format_exc())
        finally:
            s.close()
        return True

    @classmethod
    def get_mix_setting(cls, name_type: int) -> tuple[list[DemandDataConfig], datetime]:
        s = MyBusinessSession()
        res = []
        update_time = None
        try:
            # 获取最大 start_fiscal_week 的值
            max_week = s.execute(f'''
                                            SELECT fiscal_week
                                            FROM {cls.__tablename__}
                                            ORDER BY create_time DESC
                                            limit 1;
                                            ''').one_or_none()
            if max_week:
                records = s.execute(f'''
                                            SELECT *
                                            FROM {cls.__tablename__}
                                            WHERE fiscal_week = '{max_week.fiscal_week}'
                                            ''').all()
                for record in records:
                    if not update_time:
                        update_time = record.update_time
                    demand_data_config = DemandDataConfig(fiscal_week=record.fiscal_week,
                                                          sold_to_id=record.sold_to_id,
                                                          lob=record.lob,
                                                          sub_lob=record.sub_lob,
                                                          jd_self_run_mix_cw1=record.jd_self_run_mix_cw1,
                                                          jd_self_run_mix_cw2=record.jd_self_run_mix_cw2,
                                                          adjusted_demand_cw1=record.adjusted_demand_cw1,
                                                          adjusted_demand_cw2=record.adjusted_demand_cw2,
                                                          name_type=name_type)
                    res.append(demand_data_config)
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return res, update_time

    @classmethod
    def get_mix_setting_by_fiscal_week(cls, fiscal_week, name_type: int) -> list[DemandDataConfig]:
        s = MyBusinessSession()
        res = []
        try:
            records = s.execute(f'''
                                        SELECT *
                                        FROM {cls.__tablename__}
                                        WHERE fiscal_week = '{fiscal_week}'
                                        ''').all()
            for record in records:
                demand_data_config = DemandDataConfig(fiscal_week=record.fiscal_week,
                                                      sold_to_id=record.sold_to_id,
                                                      lob=record.lob,
                                                      sub_lob=record.sub_lob,
                                                      jd_self_run_mix_cw1=record.jd_self_run_mix_cw1,
                                                      jd_self_run_mix_cw2=record.jd_self_run_mix_cw2,
                                                      adjusted_demand_cw1=record.adjusted_demand_cw1,
                                                      adjusted_demand_cw2=record.adjusted_demand_cw2,
                                                      name_type=name_type)
                res.append(demand_data_config)
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return res
