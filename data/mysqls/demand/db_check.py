from util.fast_lite_base import FASTLiteSession

def test_db_connection():
    try:
        session = FASTLiteSession()
        # Simple query to check connectivity
        result = session.execute("SELECT 1")
        print("✅ Database connection successful!")
        print(f"Test query result: {result.fetchone()}")
        session.close()
        return True
    except Exception as e:
        print("❌ Database connection failed!")
        print(f"Error: {str(e)}")
        return False

# Run the check
test_db_connection()