from sqlalchemy import desc

from util.fast_lite_base import *
from util.const import *


class FastLiteRTMSalesForecastUploadOrigin(FASTLiteBase):
    __tablename__ = f"fast_lite_rtm_sales_forecast_upload_origin"

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    rtm = Column(String(32), comment='')
    fiscal_week_year = Column(Integer, comment='')
    week_date = Column(String(16), comment='FY23Q1W12')
    sold_to_id = Column(Integer, comment='customer sold to id')
    sold_to_name = Column(String(256), comment='customer sold to name')
    lob = Column(String(64), comment='')
    model = Column(String(128), comment='')
    mpn = Column(String(128), comment='')
    region = Column(String(32))
    sub_rtm = Column(String(64))
    sub_lob = Column(String(256))
    nand = Column(String(256))
    color = Column(String(256))
    uploader = Column(String(256), comment='')
    forecast_cw = Column(Integer, comment='')
    forecast_cw1 = Column(Integer, comment='')
    forecast_cw2 = Column(Integer, comment='')
    forecast_cw3 = Column(Integer, comment='')
    forecast_cw4 = Column(Integer, comment='')
    forecast_cw5 = Column(Integer, comment='')
    forecast_cw6 = Column(Integer, comment='')
    forecast_cw7 = Column(Integer, comment='')
    forecast_cw8 = Column(Integer, comment='')
    forecast_cw9 = Column(Integer, comment='')
    forecast_cw10 = Column(Integer, comment='')
    forecast_cw11 = Column(Integer, comment='')
    forecast_cw12 = Column(Integer, comment='')
    forecast_type = Column(String(16), comment='')
    remark = Column(Text)
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    @classmethod
    def bulk_insert_or_update(cls, objs: list):
        s = FASTLiteSession()
        try:
            insert_stmt = f"""
                INSERT INTO {cls.__tablename__} 
                (rtm, fiscal_week_year, week_date, sold_to_id, sold_to_name, lob, model, 
                mpn, region, sub_rtm, sub_lob, nand, color, uploader, forecast_cw, forecast_cw1, 
                forecast_cw2, forecast_cw3, forecast_cw4, forecast_cw5, forecast_cw6, forecast_cw7, forecast_cw8, 
                forecast_cw9, forecast_cw10, forecast_cw11, forecast_cw12, remark, create_time, update_time) 
                 VALUES 
                 (:rtm, :fiscal_week_year, :week_date, :sold_to_id, :sold_to_name, :lob, :model, :mpn, :region,
                 :sub_rtm, :sub_lob, :nand,:color,:uploader,:forecast_cw, :forecast_cw1,:forecast_cw2,:forecast_cw3,
                 :forecast_cw4,:forecast_cw5,:forecast_cw6,:forecast_cw7,:forecast_cw8,:forecast_cw9,:forecast_cw10,
                 :forecast_cw11,:forecast_cw12,:remark,:create_time,:update_time)
                 ON DUPLICATE KEY UPDATE 
                 lob = VALUES(lob), model = VALUES(model), region = VALUES(region), sub_rtm = VALUES(sub_rtm), 
                 sub_lob = VALUES(sub_lob),nand = VALUES(nand),color = VALUES(color),uploader = VALUES(uploader),
                 forecast_cw = VALUES(forecast_cw),forecast_cw1 = VALUES(forecast_cw1),
                 forecast_cw2 = VALUES(forecast_cw2),forecast_cw3 = VALUES(forecast_cw3),
                 forecast_cw4 = VALUES(forecast_cw4),forecast_cw5 = VALUES(forecast_cw5),
                 forecast_cw6 = VALUES(forecast_cw6),forecast_cw7 = VALUES(forecast_cw7),
                 forecast_cw8 = VALUES(forecast_cw8),forecast_cw9 = VALUES(forecast_cw9),
                 forecast_cw10 = VALUES(forecast_cw10),forecast_cw11 = VALUES(forecast_cw11),
                 forecast_cw12 = VALUES(forecast_cw12),
                 remark = VALUES(remark),update_time = VALUES(update_time)
            """
            s.execute(insert_stmt, objs)
            s.commit()
        except Exception as e:
            logger.exception(e)
            raise ErrorExcept(ErrCode.DBInsert, "insert to db failed" + e)
        finally:
            s.close()
        return True

    @classmethod
    def delete_by_rtm_week(cls, rtm: str, fiscal_week_year: int):
        s = FASTLiteSession()
        delete_success = True
        try:
            s.query(cls) \
                .filter(cls.rtm == rtm) \
                .filter(cls.fiscal_week_year == fiscal_week_year) \
                .delete()
            s.commit()
        except Exception as e:
            delete_success = False
            logger.exception(str(e))
        finally:
            s.close()
        return delete_success
