import traceback
from typing import Optional

from sqlalchemy import case

from domain.dashboard.entity.demand_comparison import DemandComparisonDetail
from domain.dashboard.entity.fiscal_week import FiscalWeek
from domain.demand.entity.const import RTMS
from util.const import ErrorExcept, ErrCode
from util.fast_lite_base import *


class DemandByRegionPool(FASTLiteBase):
    __tablename__ = "demand_by_region_pool"

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    fiscal_week = Column(String(16))
    region = Column(String(32))
    lob = Column(String(16))
    sub_lob = Column(String(16))
    nand = Column(String(16))
    color = Column(String(16))
    mpn = Column(String(16))
    hr_lr = Column(String(32))
    shipment_plan_cw = Column(Integer)
    shipment_plan_cw1 = Column(Integer)
    shipment_plan_cw2 = Column(Integer)
    avg_dfa_fcst_2_4 = Column(Float)
    avg_dfa_fcst_3_5 = Column(Float)
    ub_eoh = Column(Integer)
    forecast_cw_ml = Column(Integer)
    forecast_cw1_ml = Column(Integer)
    forecast_cw2_dfa = Column(Float)
    forecast_cw3_dfa = Column(Float)
    forecast_cw4_dfa = Column(Float)
    forecast_cw5_dfa = Column(Float)
    forecast_cw6_dfa = Column(Float)
    forecast_cw7_dfa = Column(Float)
    forecast_cw8_dfa = Column(Float)
    base = Column(Float, comment='CW_ML_FORECAST + CW1_ML_FORECAST - UB_EOH - CW_SHIPMENT_PLAN')
    cw1_ideal_demand = Column(Float)
    cw2_ideal_demand = Column(Float)
    dt_cw1 = Column(Float)
    dt_cw2 = Column(Float)
    dt_cw1_origin = Column(Float)
    dt_cw2_origin = Column(Float)
    avg_ub_1_5 = Column(Float)
    dn_cw1 = Column(Float)
    dn_cw2 = Column(Float)
    woi_by_mpn_min = Column(Float)
    woi_by_mpn_max = Column(Float)
    woi_by_sublob_max = Column(Float)
    woi_by_mpn_min_cw2 = Column(Float)
    woi_by_mpn_max_cw2 = Column(Float)
    woi_by_sublob_max_cw2 = Column(Float)
    woi_by_mpn_min_cw3 = Column(Float)
    woi_by_mpn_max_cw3 = Column(Float)
    woi_by_sublob_max_cw3 = Column(Float)
    ds_cw1 = Column(Float)
    ds_cw2 = Column(Float)
    ds_cw1_woi = Column(Float)
    ds_cw2_woi = Column(Float)
    final_dn_cw1 = Column(Float)
    final_dn_cw2 = Column(Float)
    df_cw1 = Column(Float)
    df_cw2 = Column(Float)
    df_cw1_adjusted = Column(Float)
    df_cw2_adjusted = Column(Float)
    base_demand_cw1 = Column(Float)
    base_demand_cw2 = Column(Float)
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    @classmethod
    def batch_insert(cls, objs: list):
        s = FASTLiteSession()
        try:
            now = datetime.now()
            for obj in objs:
                obj['create_time'] = now
                obj['update_time'] = now
            insert_stmt = f"""
                INSERT INTO {cls.__tablename__} 
                (fiscal_week, region, lob, sub_lob, nand, color, mpn, hr_lr, create_time, update_time) 
                 VALUES 
                    (:fiscal_week, :region, :lob, :sub_lob, :nand, :color, :mpn, :hr_lr, :create_time, :update_time)
                ON DUPLICATE KEY UPDATE 
                 region = VALUES(region), lob = VALUES(lob), sub_lob = VALUES(sub_lob), nand = VALUES(nand), 
                 color = VALUES(color), hr_lr = VALUES(hr_lr), update_time = VALUES(update_time)
            """
            s.execute(insert_stmt, objs)
            s.commit()
        except Exception as e:
            logger.exception(e)
            raise ErrorExcept(ErrCode.DBInsert, "insert to db failed" + e)
        finally:
            s.close()
        return True

    @classmethod
    def batch_update(cls, objs: list, fields_to_update):
        s = FASTLiteSession()
        try:
            now = datetime.now()
            for obj in objs:
                obj['update_time'] = now
            insert_parts = [f"{field}" for field in fields_to_update]
            value_parts = [f":{field}" for field in fields_to_update]
            key_parts = [f"{field} = VALUES({field})" for field in fields_to_update]
            insert_stmt = f"""
                                INSERT INTO {cls.__tablename__} 
                                (fiscal_week, region, mpn,  {', '.join(insert_parts)})
                                 VALUES 
                                    (:fiscal_week, :region, :mpn,  {', '.join(value_parts)})
                                ON DUPLICATE KEY UPDATE 
                                     {', '.join(key_parts)}
                            """
            s.execute(insert_stmt, objs)
            s.commit()
        except Exception as e:
            logger.exception(traceback.format_exc())
            raise ErrorExcept(ErrCode.DBInsert, "insert to db failed" + traceback.format_exc() + "params:" + str(objs))
        finally:
            s.close()
        return True

    @classmethod
    def bulk_update(cls, fields_to_update: list, objs: list):
        s = FASTLiteSession()
        try:
            now = datetime.now()
            for obj in objs:
                obj['update_time'] = now
            # forecast_cw2_dfa = :forecast_cw2_dfa,forecast_cw3_dfa = :forecast_cw3_dfa,
            # forecast_cw4_dfa = :forecast_cw4_dfa,forecast_cw5_dfa = :forecast_cw5_dfa,
            set_parts = [f"{field} = :{field}" for field in fields_to_update]
            update_stmt = f"""
                UPDATE
                {cls.__tablename__}
                SET
                {', '.join(set_parts)}
                WHERE fiscal_week = :fiscal_week AND region = :region AND mpn = :mpn
            """
            s.execute(update_stmt, objs)
            s.commit()
        except Exception as e:
            logger.exception(e)
            raise ErrorExcept(ErrCode.DBInsert, "update db demand_by_region_pool" + e)
        finally:
            s.close()
        return True

    @classmethod
    def update_sub_lob_woi(cls, objs: list):
        s = FASTLiteSession()
        try:
            now = datetime.now()
            for obj in objs:
                obj['update_time'] = now
            update_stmt = f"""
                UPDATE
                {cls.__tablename__}
                SET
                woi_by_sublob_max = :woi_by_sublob_max, update_time = :update_time
                WHERE fiscal_week = :fiscal_week AND sub_lob = :sub_lob
            """
            s.execute(update_stmt, objs)
            s.commit()
        except Exception as e:
            logger.exception(e)
            raise ErrorExcept(ErrCode.DBInsert, "update db demand_by_region_pool" + e)
        finally:
            s.close()
        return True

    @classmethod
    def get_dataframe_result(cls, fiscal_week: str):
        s = FASTLiteSession()
        ret = pd.DataFrame()
        try:
            q = s.query(cls).filter(cls.fiscal_week == fiscal_week,
                                    cls.create_time.isnot(None)).order_by(cls.id.asc())
            ret = pd.read_sql_query(q.statement, fast_lite_engine)
        except Exception as e:
            logger.exception(str(e))
        finally:
            s.close()
        return ret

    @classmethod
    def get_woi_menu(cls, fiscal_week, lob: str) -> list:
        s = FASTLiteSession()
        ret = []
        try:
            query = s.query(cls.lob, cls.sub_lob, cls.nand, cls.color, cls.mpn).filter(cls.fiscal_week == fiscal_week,
                                                                  cls.lob == lob)
            ret = query.all()
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return ret

    @classmethod
    def get_by_fiscal_week(cls, fiscal_week: str) -> pd.DataFrame:
        s = FASTLiteSession()
        res = pd.DataFrame()
        try:
            query = s.query(*cls.__table__.columns) \
                .filter(cls.fiscal_week == fiscal_week).order_by(cls.id.asc())
            res = pd.read_sql_query(query.statement, fast_lite_engine)
            return res
        except Exception as e:
            logger.error(e)
            raise ErrorExcept(ErrCode.DBQueryError, "db query error: " + str(e))
        finally:
            s.close()

    @classmethod
    def get_update_time_by_fiscal_week(cls, fiscal_week: str) -> str:
        s = FASTLiteSession()
        ret = ""
        try:
            query = (s.query(func.max(cls.update_time))
                     .filter(cls.fiscal_week == fiscal_week))
            ret = query.scalar()
        except Exception as e:
            logger.error(e)
            raise ErrorExcept(ErrCode.DBQueryError, "db query error: " + str(e))
        finally:
            s.close()
        return ret if not ret else ret.strftime("%Y-%m-%d %H:%M:%S")

    @classmethod
    def get_fiscal_weeks(cls) -> list:
        s = FASTLiteSession()
        ret = []
        try:
            fiscal_weeks = s.query(cls.fiscal_week).filter(cls.fiscal_week != 'F').distinct().all()
            ret = [item["fiscal_week"] for item in fiscal_weeks]
            ret = sorted(ret, key=lambda x: FiscalWeek(x).fiscal_week_int, reverse=True)
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

    @classmethod
    def get_demand_comparison_detail(cls, fiscal_week: str, region: str, lob: str, sub_lobs: list, hr_lr: Optional[str]) -> list[DemandComparisonDetail]:
        s = FASTLiteSession()
        ret = []
        try:
            filter_params = []
            if fiscal_week:
                filter_params.append(cls.fiscal_week == fiscal_week)
            if region:
                filter_params.append(cls.region == region)
            if lob:
                filter_params.append(cls.lob == lob)
            if sub_lobs:
                filter_params.append(cls.sub_lob.in_(sub_lobs))
            if hr_lr:
                filter_params.append(cls.hr_lr == hr_lr)
            obj_list = s.query(
                    cls.nand,
                    cls.color,
                    func.sum(cls.ub_eoh).label('ub_eoh'),
                    func.sum(cls.shipment_plan_cw).label('shipment_plan_cw'),
                    func.sum(cls.forecast_cw_ml).label('forecast_cw_ml'),
                    func.sum(cls.forecast_cw1_ml).label('forecast_cw1_ml'),
                    func.sum(cls.forecast_cw2_dfa).label('forecast_cw2_dfa'),
                    func.sum(cls.forecast_cw3_dfa).label('forecast_cw3_dfa'),
                    func.sum(cls.forecast_cw4_dfa).label('forecast_cw4_dfa'),
                    func.sum(cls.forecast_cw5_dfa).label('forecast_cw5_dfa'),
                    func.sum(cls.cw1_ideal_demand).label('cw1_ideal_demand'),
                    func.sum(cls.cw2_ideal_demand).label('cw2_ideal_demand'),
                    func.sum(cls.dt_cw1).label('dt_cw1'),
                    func.sum(cls.dt_cw2).label('dt_cw2'),
                    func.sum(                              # df 未调整则使用原值即可
                        case(
                            [(cls.df_cw1_adjusted.isnot(None), cls.df_cw1_adjusted)],
                            else_=cls.df_cw1
                        )
                    ).label('df_cw1'),
                    func.sum(
                        case(
                            [(cls.df_cw2_adjusted.isnot(None), cls.df_cw2_adjusted)],
                            else_=cls.df_cw2
                        )
                    ).label('df_cw2')
            ).filter(*filter_params).group_by(cls.nand, cls.color).all()

            for item in obj_list:
                ret.append(DemandComparisonDetail(**item))
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return ret

    @classmethod
    def sum_by_fiscal_week(cls, fiscal_week: str, sub_lobs: list = []) -> dict:
        s = FASTLiteSession()
        res = {"avg_dfa_fcst_2_4": None, "avg_dfa_fcst_3_5": None, "avg_ub_1_5": None, "base": None}
        try:
            query = (s.query(
                             func.sum(cls.avg_dfa_fcst_2_4).label('avg_dfa_fcst_2_4'),
                             func.sum(cls.avg_dfa_fcst_3_5).label('avg_dfa_fcst_3_5'),
                             func.sum(cls.avg_ub_1_5).label('avg_ub_1_5'),
                             func.sum(cls.base).label('base')
                             )
                     .filter(cls.fiscal_week == fiscal_week).group_by(cls.fiscal_week))
            if sub_lobs:
                query = query.filter(cls.sub_lob.in_(sub_lobs))
            one = query.one_or_none()
            res = {
                "avg_dfa_fcst_2_4": one['avg_dfa_fcst_2_4'],
                "avg_dfa_fcst_3_5": one['avg_dfa_fcst_3_5'],
                "avg_ub_1_5": one['avg_ub_1_5'],
                "base": one['base'],
            }
            return res
        except Exception as e:
            logger.error(e)
            raise ErrorExcept(ErrCode.DBQueryError, "db query error: " + str(e))
        finally:
            s.close()
