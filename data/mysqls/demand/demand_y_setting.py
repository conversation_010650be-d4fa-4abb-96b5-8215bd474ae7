import traceback

from sqlalchemy import text

from domain.dashboard.entity.fiscal_week import FiscalWeek
from util.fast_lite_base import *
from util.const import ErrorExcept, ErrCode


class IdealDemandYValueSetting(FASTLiteBase):
    __tablename__ = 'ideal_demand_y_value_setting'
    __table_args__ = {'schema': 'fast_lite'}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    fiscal_week = Column(String(16), comment='FY23Q1W12')
    hr_lr_type = Column(String(16), comment='')
    lob = Column(String(16), comment='')
    sub_lob = Column(String(16), comment='')
    region = Column(String(32), comment='')
    rtm = Column(String(32), comment='')
    sub_rtm = Column(String(64), comment='')
    sold_to_id = Column(Integer, comment='customer sold to id')
    sold_to_name = Column(String(128), comment='customer sold to name')
    cw1_min = Column(Float, comment='')
    cw1_max = Column(Float, comment='')
    cw2_min = Column(Float, comment='')
    cw2_max = Column(Float, comment='')
    twos = Column(Float, comment='')
    is_published = Column(Integer, comment='')
    publish_time = Column(DateTime)
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    mpn = Column(String(32), comment='')
    nand = Column(String(32), comment='')
    color = Column(String(32), comment='')
    cw1_y = Column(String(32), comment='')
    cw2_y = Column(String(32), comment='')

    @classmethod
    def bulk_insert_or_update(cls, objs: list):
        s = FASTLiteSession()
        try:
            now = datetime.now()
            for obj in objs:
                obj['create_time'] = now
                obj['update_time'] = now
            insert_stmt = f"""
                INSERT INTO {cls.__tablename__} 
                (fiscal_week, hr_lr_type, lob, sub_lob, region, rtm, sub_rtm,sold_to_id,sold_to_name,cw1_min, cw1_max, cw2_min, cw2_max, 
                twos,create_time,update_time,mpn,nand,color,cw1_y,cw2_y) 
                 VALUES 
                 (:fiscal_week, :hr_lr_type, :lob, :sub_lob, :region, :rtm, :sub_rtm, :sold_to_id, :sold_to_name,:cw1_min, :cw1_max, :cw2_min,:cw2_max,:twos, :create_time, :update_time,
                 :mpn, :nand, :color, :cw1_y, :cw2_y)
                 ON DUPLICATE KEY UPDATE 
                 cw1_min = VALUES(cw1_min), cw1_max = VALUES(cw1_max), cw2_min = VALUES(cw2_min), cw2_max = VALUES(cw2_max),update_time = VALUES(update_time),
                 cw1_y = VALUES(cw1_y), cw2_y = VALUES(cw2_y)
            """
            s.execute(insert_stmt, objs)
            s.commit()
        except Exception as e:
            logger.exception(e)
            raise ErrorExcept(ErrCode.DBInsert, "insert to db failed" + e)
        finally:
            s.close()
        return True

    @classmethod
    def batch_update(cls, objs: list, fields_to_update):
        s = FASTLiteSession()
        try:
            now = datetime.now()
            for obj in objs:
                obj['update_time'] = now
            insert_parts = [f"{field}" for field in fields_to_update]
            value_parts = [f":{field}" for field in fields_to_update]
            key_parts = [f"{field} = VALUES({field})" for field in fields_to_update]
            insert_stmt = f"""
                            INSERT INTO {cls.__tablename__} 
                            (fiscal_week, hr_lr_type, sub_lob, rtm, sub_rtm, sold_to_id, mpn,  {', '.join(insert_parts)})
                             VALUES 
                                (:fiscal_week, :hr_lr_type ,:sub_lob, :rtm, :sub_rtm, :sold_to_id, :mpn,  {', '.join(value_parts)})
                            ON DUPLICATE KEY UPDATE 
                                 {', '.join(key_parts)}
                        """
            s.execute(insert_stmt, objs)
            s.commit()
        except Exception as e:
            logger.exception(traceback.format_exc())
            raise ErrorExcept(ErrCode.DBInsert, "insert to db failed" + traceback.format_exc())
        finally:
            s.close()
        return True

    @classmethod
    def update_publish_status_by_fiscal_week(cls, fiscal_week: str):
        s = FASTLiteSession()
        update = 0
        try:
            update = (s.query(cls).filter(cls.fiscal_week == fiscal_week)
                      .update({'is_published': True, 'publish_time': datetime.now()}))
            s.commit()
            logger.info(f"update {cls.__tablename__}")
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return update

    @classmethod
    def query_ideal_demand_y_setting(cls, fiscal_week: str,
                                     region: str, high_low_runner: str, rtm: str,
                                     sub_rtm: str, sold_to_id=None, nand=None, color=None, sub_lobs=None) -> list:
        s = FASTLiteSession()
        ret = []
        try:
            query = s.query(*cls.__table__.columns) \
                .filter(cls.fiscal_week == fiscal_week) \
                .filter(cls.region == region).order_by(cls.id.asc())
            if rtm:
                query = query.filter(cls.rtm == rtm)
            if sub_rtm:
                query = query.filter(cls.sub_rtm == sub_rtm)
            if high_low_runner:
                query = query.filter(cls.hr_lr_type == high_low_runner)
            if sold_to_id:
                query = query.filter(cls.sold_to_id == sold_to_id)
            if nand:
                query = query.filter(cls.nand == nand)
            if color:
                query = query.filter(cls.color == color)
            if sub_lobs:
                query = query.filter(cls.sub_lob.in_(sub_lobs))

            query_all = query.all()
            ret = [{
                "fiscal_week": x.fiscal_week,
                'hr_lr_type': x.hr_lr_type,
                'lob': x.lob,
                'sub_lob': x.sub_lob,
                'region': x.region,
                'rtm': x.rtm,
                'sub_rtm': x.sub_rtm,
                'sold_to_id': x.sold_to_id,
                'sold_to_name': x.sold_to_name,
                'mpn': x.mpn,
                'nand': x.nand,
                'color': x.color,
                'cw1_y': x.cw1_y,
                'cw2_y': x.cw2_y,
                'cw1_min': x.cw1_min,  # 兼容目前的x
                'cw1_max': x.cw1_max,
                'cw2_min': x.cw2_min,
                'cw2_max': x.cw2_max,
                'twos': x.twos} for x in query_all]
        except Exception as e:
            logger.exception(str(e))
            raise ErrorExcept(ErrCode.DBQueryError, "select failed" + str(e))
        finally:
            s.close()
        return ret

    @classmethod
    def get_region_rtm(cls, fiscal_week: str, region: str, rtm: str) -> list:

        s = FASTLiteSession()
        ret = []
        try:
            region_rtms_obj = s.query(cls.region, cls.rtm, cls.sub_rtm, cls.lob, cls.sub_lob,
                                      cls.sold_to_id, cls.sold_to_name,
                                      cls.nand, cls.color).filter(cls.fiscal_week == fiscal_week)
            if region:
                region_rtms_obj = region_rtms_obj.filter(cls.region == region)
            if rtm:
                region_rtms_obj = region_rtms_obj.filter(cls.rtm == rtm)
            ret = region_rtms_obj.all()
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return ret

    @classmethod
    def check_is_published(cls, fiscal_week: str):
        s = FASTLiteSession()
        ret = False
        try:
            query = s.query(cls.is_published).filter(cls.fiscal_week == fiscal_week).filter(cls.is_published == 0)
            count = query.count()
            ret = count == 0
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return ret

    @classmethod
    def query_distinct_sold_to(cls, fiscal_week: str):
        s = FASTLiteSession()
        ret = []
        try:
            sold_to_names = (s.query(cls.sold_to_id, cls.sold_to_name).
                             distinct(cls.sold_to_id, cls.sold_to_name).filter(cls.fiscal_week == fiscal_week).order_by(cls.id.asc()).all())
            ret = [(x.sold_to_id, x.sold_to_name) for x in sold_to_names]
        except Exception as e:
            logger.exception(e)
            raise ErrorExcept(ErrCode.DBQueryError, "select failed" + str(e))
        finally:
            s.close()
        return ret

    @classmethod
    def query_distinct_fiscal_weeks(cls):
        s = FASTLiteSession()
        ret = []
        try:
            fiscal_weeks = s.query(cls.fiscal_week).distinct(cls.fiscal_week).all()
            fiscal_week_strs = [x.fiscal_week for x in fiscal_weeks]
            ret = sorted(fiscal_week_strs, key=lambda x: FiscalWeek(x).fiscal_week_int, reverse=True)
        except Exception as e:
            logger.exception(e)
            raise ErrorExcept(ErrCode.DBQueryError, "select failed" + str(e))
        finally:
            s.close()
        return ret

    @classmethod
    def query_by_fiscal_week(cls, fiscal_week: str) -> list:
        s = FASTLiteSession()
        ret = []
        try:
            ret = s.execute(f'''
                                SELECT fiscal_week, hr_lr_type, lob,sub_lob,
                                region, rtm, sub_rtm, sold_to_id, sold_to_name,
                                cw1_min, cw1_max, cw2_min, cw2_max,twos,mpn,nand,color,cw1_y,cw2_y
                                FROM {cls.__tablename__}
                                WHERE fiscal_week = '{fiscal_week}';
                                ''').fetchall()
            ret = [{
                "fiscal_week": x.fiscal_week,
                'hr_lr_type': x.hr_lr_type,
                'lob': x.lob,
                'sub_lob': x.sub_lob,
                'region': x.region,
                'rtm': x.rtm,
                'sub_rtm': x.sub_rtm,
                'sold_to_id': x.sold_to_id,
                'sold_to_name': x.sold_to_name,
                'cw1_min': x.cw1_min,
                'cw1_max': x.cw1_max,
                'cw2_min': x.cw2_min,
                'cw2_max': x.cw2_max,
                'mpn': x.mpn,
                'nand': x.nand,
                'color': x.color,
                'cw1_y': x.cw1_y,
                'cw2_y': x.cw2_y,
                'twos': x.twos} for x in ret]

        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return ret