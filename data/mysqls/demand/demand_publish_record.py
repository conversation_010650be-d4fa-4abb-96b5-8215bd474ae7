import traceback

from domain.demand.entity.const import RTMS
from domain.demand.entity.demand_sublob_publish_record import DemandSublobPublishRecord
from util.const import ErrorExcept, ErrCode
from util.fast_lite_base import *


class DemandPublishRecord(FASTLiteBase):
    __tablename__ = "tbl_demand_publish_record"

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    fiscal_week = Column(String(16))
    demand = Column(String(32))
    sub_lob = Column(String(16))
    publish_time = Column(DateTime)
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    @classmethod
    def batch_insert(cls, objs: list):
        s = FASTLiteSession()
        try:
            now = datetime.now()
            for obj in objs:
                obj['create_time'] = now
                obj['update_time'] = now
            insert_stmt = f"""
                INSERT INTO {cls.__tablename__} 
                (fiscal_week, demand, sub_lob,publish_time, create_time, update_time) 
                 VALUES 
                    (:fiscal_week, :demand, :sub_lob, :publish_time, :create_time, :update_time)
                ON DUPLICATE KEY UPDATE 
                 fiscal_week = VALUES(fiscal_week), demand= VALUES(demand), sub_lob = VALUES(sub_lob), publish_time = VALUES(publish_time), update_time = VALUES(update_time)
            """
            s.execute(insert_stmt, objs)
            s.commit()
        except Exception as e:
            logger.exception(traceback.format_exc())
            raise ErrorExcept(ErrCode.DBInsert, "insert to tbl_demand_publish_record failed" + traceback.format_exc())
        finally:
            s.close()
        return True

    @classmethod
    def get_by_fiscal_week(cls, fiscal_week: str) -> list[DemandSublobPublishRecord]:
        s = FASTLiteSession()
        res = []
        try:
            query = (s.query(*cls.__table__.columns)
                     .filter(cls.fiscal_week == fiscal_week).order_by(cls.id.asc()))
            records = query.all()
            for record in records:
                sublob_publish_record = DemandSublobPublishRecord(fiscal_week=record.fiscal_week,
                                                                  demand=record.demand,
                                                                  sub_lob=record.sub_lob,
                                                                  publish_time=record.publish_time)
                res.append(sublob_publish_record)
            return res
        except Exception as e:
            logger.error(e)
            raise ErrorExcept(ErrCode.DBQueryError, "query publish record fail. " + str(e))
        finally:
            s.close()
