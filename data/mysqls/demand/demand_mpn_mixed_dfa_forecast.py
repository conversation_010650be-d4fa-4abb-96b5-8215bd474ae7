from util.fast_lite_base import *
from util.const import ErrorExcept, ErrCode


class DemandMpnMixedDfaForecast(FASTLiteBase):
    __tablename__ = 'demand_mpn_mixed_dfa_forecast'
    __table_args__ = {'schema': 'fast_lite'}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    fiscal_week = Column(String(16), comment='FY23Q1W12')
    lob = Column(String(64))
    sub_lob = Column(String(64))
    mpn = Column(String(64))
    cw = Column(Float)
    cw1 = Column(Float)
    cw2 = Column(Float)
    cw3 = Column(Float)
    cw4 = Column(Float)
    cw5 = Column(Float)
    cw6 = Column(Float)
    cw7 = Column(Float)
    cw8 = Column(Float)
    create_time = Column(DateTime)
    update_time = Column(DateTime)

    @classmethod
    def bulk_insert_or_update(cls, objs: list):
        s = FASTLiteSession()
        try:
            now = datetime.now()
            for obj in objs:
                obj['create_time'] = now
                obj['update_time'] = now
            insert_stmt = f"""
                INSERT INTO {cls.__tablename__} 
                (fiscal_week, sub_lob,mpn,cw,cw1,cw2,cw3,cw4,cw5,cw6,cw7,cw8,create_time,update_time) 
                 VALUES 
                 (:fiscal_week,:sub_lob,:mpn,:cw,:cw1,:cw2,:cw3,:cw4,:cw5,:cw6,:cw7,:cw8,:create_time, :update_time)
                 ON DUPLICATE KEY UPDATE 
                 cw = VALUES(cw), cw1 = VALUES(cw1), cw2 = VALUES(cw2), cw3 = VALUES(cw3),cw4 = VALUES(cw4),
                 cw5 = VALUES(cw5),cw6 = VALUES(cw6),cw7 = VALUES(cw7),cw8 = VALUES(cw8),update_time = VALUES(update_time)
            """
            s.execute(insert_stmt, objs)
            s.commit()
        except Exception as e:
            logger.exception(e)
            raise ErrorExcept(ErrCode.DBInsert, "insert to db failed" + e)
        finally:
            s.close()
        return True

    @classmethod
    def query_by_week_and_lob(cls, fiscal_week: str, lob: str):
        s = FASTLiteSession()
        ret = pd.DataFrame()
        try:
            q = s.query(cls.mpn, cls.cw2, cls.cw3,
                        cls.cw4, cls.cw5, cls.cw6, cls.cw7, cls.cw8) \
                .filter(cls.fiscal_week == fiscal_week)
            ret = pd.read_sql_query(q.statement, fast_lite_engine)
        except Exception as e:
            logger.exception(str(e))
        finally:
            s.close()
        return ret
