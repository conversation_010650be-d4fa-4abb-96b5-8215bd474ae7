from domain.demand.entity.woi_by_mpn import WoiByMpn
from domain.demand.entity.woi_by_sublob import WoiBySublob
from util.const import ErrorExcept, ErrCode
from util.fast_lite_base import *


class SellinDemandWoiSetting(FASTLiteBase):
    __tablename__ = 'sellin_demand_woi_setting'
    __table_args__ = {'schema': 'fast_lite'}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    fiscal_week = Column(String(16), comment='FY23Q1W12')
    woi_type = Column(String(16), comment='final_demand/feedback_demand')
    woi_by_mpn = Column(Text, comment='')
    woi_by_sublob = Column(Text, comment='')
    publish_timestamp = Column(Integer, comment='')
    creator = Column(String(128), comment='')
    editor = Column(String(128), comment='')
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    @classmethod
    def bulk_insert_or_update(cls, obj: dict):
        s = FASTLiteSession()
        try:
            now = datetime.now()
            obj['create_time'] = now
            obj['update_time'] = now
            insert_stmt = f"""
                INSERT INTO {cls.__tablename__} 
                (fiscal_week, woi_type, woi_by_mpn, woi_by_sublob, creator, editor, create_time, update_time) 
                 VALUES 
                 (:fiscal_week, :woi_type, :woi_by_mpn, :woi_by_sublob, :creator, :editor, :create_time, :update_time)
                 ON DUPLICATE KEY UPDATE 
                 woi_by_mpn = VALUES(woi_by_mpn),
                 woi_by_sublob = VALUES(woi_by_sublob), 
                 creator = VALUES(creator), editor = VALUES(editor), update_time = VALUES(update_time)
            """
            s.execute(insert_stmt, obj)
            s.commit()
        except Exception as e:
            logger.exception(e)
            raise ErrorExcept(ErrCode.DBInsert, "insert to db failed" + e)
        finally:
            s.close()
        return True

    @classmethod
    def query_by_fiscal_week(cls, fiscal_week: str, woi_type: str):
        s = FASTLiteSession()
        try:
            woi_by_mpns = []
            woi_by_sublobs = []
            ret = s.execute(f'''
                                SELECT fiscal_week, woi_by_mpn,
                                woi_by_sublob
                                FROM {cls.__tablename__}
                                WHERE fiscal_week = '{fiscal_week}'
                                AND woi_type = '{woi_type}'
                                ORDER BY id DESC
                                limit 1;
                                ''').one_or_none()

            if ret:
                woi_by_mpns = [WoiByMpn(**i) for i in json.loads(ret['woi_by_mpn'])]
                woi_by_sublobs = [WoiBySublob(**i) for i in json.loads(ret['woi_by_sublob'])]
            return woi_by_mpns, woi_by_sublobs
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()

    @classmethod
    def update_publish_timestamp_by_fiscal_week(cls, fiscal_week: str, woi_type: str, editor: str):
        s = FASTLiteSession()
        update = 0
        try:
            # 获取当前时间戳
            now = int(time.time())
            update = (s.query(cls).filter(cls.fiscal_week == fiscal_week, cls.publish_timestamp==0, cls.woi_type==woi_type)
                      .update({'publish_timestamp': now, 'update_time': datetime.now(), 'editor': editor}))
            s.commit()
            logger.info(f"update {cls.__tablename__}")
        except Exception as e:
           logger.exception(e)
           raise ErrorExcept(ErrCode.DBInsert, "insert to db failed" + e)
        finally:
            s.close()
        return update

    @classmethod
    def query_publish_flag_by_fiscal_week(cls, fiscal_week: str, woi_type: str):
        s = FASTLiteSession()
        try:
            ret = s.execute(f'''
                                    SELECT publish_timestamp, update_time
                                    FROM {cls.__tablename__}
                                    WHERE fiscal_week = '{fiscal_week}'
                                    AND woi_type = '{woi_type}'
                                    ORDER BY id DESC
                                    limit 1;
                                    ''').one_or_none()

            if ret:
                if ret['publish_timestamp'] != 0:
                    return True
            return False
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()

    @classmethod
    def query_publish_woi_by_fiscal_week(cls, fiscal_week: str, woi_type: str):
        s = FASTLiteSession()
        try:
            woi_by_mpns = []
            woi_by_sublobs = []
            ret = s.execute(f'''
                                SELECT fiscal_week, woi_by_mpn, woi_by_sublob
                                FROM {cls.__tablename__}
                                WHERE fiscal_week = '{fiscal_week}'
                                AND woi_type = '{woi_type}'
                                AND publish_timestamp != 0
                                ORDER BY publish_timestamp DESC
                                limit 1;
                                ''').one_or_none()

            if ret:
                woi_by_mpns = [WoiByMpn(**i) for i in json.loads(ret['woi_by_mpn'])]
                woi_by_sublobs = [WoiBySublob(**i) for i in json.loads(ret['woi_by_sublob'])]
            return woi_by_mpns, woi_by_sublobs
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()

    @classmethod
    def query_un_publish_woi_by_fiscal_week(cls, fiscal_week: str, woi_type: str):
        s = FASTLiteSession()
        try:
            woi_by_mpns = []
            woi_by_sublobs = []
            ret = s.execute(f'''
                                SELECT fiscal_week, woi_by_mpn, woi_by_sublob
                                FROM {cls.__tablename__}
                                WHERE fiscal_week = '{fiscal_week}'
                                AND woi_type = '{woi_type}'
                                AND publish_timestamp = 0
                                ORDER BY id DESC
                                limit 1;
                                ''').one_or_none()

            if ret:
                woi_by_mpns = [WoiByMpn(**i) for i in json.loads(ret['woi_by_mpn'])]
                woi_by_sublobs = [WoiBySublob(**i) for i in json.loads(ret['woi_by_sublob'])]
            return woi_by_mpns, woi_by_sublobs
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()

    @classmethod
    def query_woi_by_fiscal_week(cls, fiscal_week: str, woi_type: str):
        s = FASTLiteSession()
        try:
            woi_by_mpns = []
            woi_by_sublobs = []
            ret = s.execute(f'''
                                SELECT fiscal_week, woi_by_mpn, woi_by_sublob
                                FROM {cls.__tablename__}
                                WHERE fiscal_week = '{fiscal_week}'
                                AND woi_type = '{woi_type}'
                                ORDER BY id DESC
                                limit 1;
                                ''').one_or_none()

            if ret:
                woi_by_mpns = [WoiByMpn(**i) for i in json.loads(ret['woi_by_mpn'])]
                woi_by_sublobs = [WoiBySublob(**i) for i in json.loads(ret['woi_by_sublob'])]
            return woi_by_mpns, woi_by_sublobs
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()