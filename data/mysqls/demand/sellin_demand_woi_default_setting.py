from util.fast_lite_base import *


class SellinDemandWoiDefaultSetting(FASTLiteBase):
    __tablename__ = 'sellin_demand_woi_default_setting'
    __table_args__ = {'schema': 'fast_lite'}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    setting_by = Column(String(16), comment='mpn/sublob')
    mpn_or_sublob = Column(String(64), comment='')
    woi_min = Column(Float, comment='')
    woi_max = Column(Float, comment='')
    woi_min_cw2 = Column(Float, comment='')
    woi_max_cw2 = Column(Float, comment='')
    woi_min_cw3 = Column(Float, comment='')
    woi_max_cw3 = Column(Float, comment='')

    @classmethod
    def query_all(cls):
        s = FASTLiteSession()
        try:
            ret = pd.read_sql_query(f'''
                                SELECT 
                                    setting_by, mpn_or_sublob, 
                                    woi_min as 'default_woi_min', woi_max as 'default_woi_max',
                                    woi_min_cw2 as 'default_woi_min_cw2', woi_max_cw2 as 'default_woi_max_cw2',
                                    woi_min_cw3 as 'default_woi_min_cw3', woi_max_cw3 as 'default_woi_max_cw3'
                                FROM {cls.__tablename__}
                                ''', fast_lite_engine)
            return ret
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
