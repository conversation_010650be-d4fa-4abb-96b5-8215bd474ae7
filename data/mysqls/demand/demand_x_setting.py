from util.const import ErrorExcept, ErrCode
from util.fast_lite_base import *


class IdealDemandXValueSetting(FASTLiteBase):
    __tablename__ = 'ideal_demand_x_value_setting'

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    fiscal_week = Column(String(16))
    hr_lr_type = Column(String(16))
    lob = Column(String(16))
    sub_lob = Column(String(32))
    region = Column(String(32))
    rtm = Column(String(32))
    sub_rtm = Column(String(64))
    sold_to_id = Column(String(32))
    sold_to_name = Column(String(128))
    cw1 = Column(Float)
    twos = Column(Float)
    cw2 = Column(Float)
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    @classmethod
    def get_ideal_demand_x_value(cls,
                                 fiscal_week: str,
                                 high_low_runner: str,
                                 region: str,
                                 rtm: str,
                                 sub_rtm: str) -> list:
        s = FASTLiteSession()
        ret = []
        try:
            x_value_obj = s.query(cls.hr_lr_type,
                                  cls.rtm,
                                  cls.sub_rtm,
                                  cls.sub_lob,
                                  cls.sold_to_name,
                                  cls.twos,
                                  cls.cw1,
                                  cls.cw2) \
                .filter(cls.fiscal_week == fiscal_week) \
                .filter(cls.region == region)

            if rtm:
                x_value_obj = x_value_obj.filter(cls.rtm == rtm)

            if sub_rtm:
                x_value_obj = x_value_obj.filter(cls.sub_rtm == sub_rtm)

            if high_low_runner:
                x_value_obj = x_value_obj.filter(cls.hr_lr_type == high_low_runner)
            if x_value_obj:
                # 排序 sub_rtm 自然排序(升序), sub_lob 自然排序(倒序)
                x_value_obj = x_value_obj.order_by(cls.sub_rtm.asc(), cls.sub_lob.desc())
            ret = x_value_obj.all()
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return ret

    @classmethod
    def get_x_by_week_and_region(cls,
                                 fiscal_week: str,
                                 region: str) -> list:
        s = FASTLiteSession()
        ret = []
        try:
            x_value_obj = s.query(cls.hr_lr_type,
                                  cls.rtm,
                                  cls.sub_rtm,
                                  cls.lob,
                                  cls.sub_lob,
                                  cls.sold_to_name,
                                  cls.twos,
                                  cls.cw1,
                                  cls.cw2) \
                .filter(cls.fiscal_week == fiscal_week) \
                .filter(cls.region == region)

            x_values = x_value_obj.all()
            ret = [{
                'rtm': x.rtm,
                'sub_rtm': x.sub_rtm,
                'hr_lr': x.hr_lr_type,
                'lob': x.lob,
                'sub_lob': x.sub_lob,
                'twos': x.twos,
                'cw1': x.cw1,
                'cw2': x.cw2} for x in x_values]
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return ret

    @classmethod
    def bulk_insert_or_update(cls, objs: list):
        s = FASTLiteSession()
        try:
            now = datetime.now()
            for obj in objs:
                obj['create_time'] = now
                obj['update_time'] = now
            insert_stmt = f"""
                INSERT INTO {cls.__tablename__} 
                (fiscal_week, hr_lr_type, lob, sub_lob, region, rtm, sub_rtm,sold_to_id,sold_to_name,cw1, cw2, twos,create_time,update_time) 
                 VALUES 
                 (:fiscal_week, :hr_lr_type, :lob, :sub_lob, :region, :rtm, :sub_rtm, :sold_to_id, :sold_to_name,:cw1,:cw2,:twos,:create_time,:update_time)
                 ON DUPLICATE KEY UPDATE 
                 cw1 = VALUES(cw1), cw2 = VALUES(cw2),update_time = VALUES(update_time)
            """
            s.execute(insert_stmt, objs)
            s.commit()
        except Exception as e:
            logger.exception(e)
            raise ErrorExcept(ErrCode.DBInsert, "insert to db failed" + e)
        finally:
            s.close()
        return True

    @classmethod
    def get_ideal_demand_x_value_by_fiscal_week(cls, fiscal_week: str, lob):
        s = FASTLiteSession()
        res = []
        try:
            query = s.query(cls.region, cls.sub_rtm, cls.twos, cls.cw1.label("cw1_x"), cls.cw2.label("cw2_x")) \
                .filter(cls.fiscal_week == fiscal_week) \
                .filter(cls.lob == lob)
            res = pd.read_sql_query(query.statement, fast_lite_engine)
            return res
        except Exception as e:
            logger.error(e)
            raise ErrorExcept(ErrCode.DBQueryError, "db query error: " + str(e))
        finally:
            s.close()

    @classmethod
    def get_region_rtm(cls, fiscal_week: str, region: str, rtm: str) -> list:
        s = FASTLiteSession()
        ret = []
        try:
            region_rtms_obj = s.query(cls.region, cls.rtm, cls.sub_rtm).filter(cls.fiscal_week == fiscal_week)
            if region:
                region_rtms_obj = region_rtms_obj.filter(cls.region == region)
            if rtm:
                region_rtms_obj = region_rtms_obj.filter(cls.rtm == rtm)
            ret = region_rtms_obj.all()
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return ret

    @classmethod
    def get_x_value_by_region_fiscal_week_rtms(cls, region: region, fiscal_week: str, rtms: list) -> list:
        s = FASTLiteSession()
        res = []
        try:
            query = s.query(cls.fiscal_week, cls.hr_lr_type, cls.region, cls.lob, cls.sub_rtm,
                            cls.twos, cls.cw1.label("cw1_x"), cls.cw2.label("cw2_x")) \
                .filter(cls.fiscal_week == fiscal_week, cls.region == region, cls.rtm.in_(rtms))
            res = pd.read_sql_query(query.statement, fast_lite_engine)
            return res
        except Exception as e:
            logger.error(e)
            raise ErrorExcept(ErrCode.DBQueryError, "db query error: " + str(e))
        finally:
            s.close()
