from util.fast_lite_base import *


class IdealDemandTwos(FASTLiteBase):
    __tablename__ = 'ideal_demand_twos'
    __table_args__ = {'schema': 'fast_lite'}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    fiscal_week = Column(String(16), comment='FY23Q1W12')
    hr_lr_type = Column(String(16), comment='')
    lob = Column(String(16), comment='')
    region = Column(String(32), comment='')
    rtm = Column(String(32), comment='')
    sub_rtm = Column(String(64), comment='')
    twos = Column(Float, comment='')
    version = Column(Integer, comment='')
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    @classmethod
    def query_latest_twos_by_lob(cls, lob: str) -> list:
        s = FASTLiteSession()
        ret = []
        try:
            ret = s.execute(f'''
                                SELECT 
                                hr_lr_type, lob,region, rtm, sub_rtm, twos
                                FROM {cls.__tablename__}
                                WHERE lob = '{lob}'
                                AND version = (SELECT MAX(version) FROM ideal_demand_twos);
                                ''').fetchall()
            ret = [{
                'hr_lr_type': x.hr_lr_type,
                'lob': x.lob,
                'region': x.region,
                'rtm': x.rtm,
                'sub_rtm': x.sub_rtm,
                'twos': x.twos} for x in ret]
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return ret
