from util.const import ErrorExcept, ErrCode
from util.fast_lite_base import *


class DemandProcessor(FASTLiteBase):
    __tablename__ = 'demand_processor'
    __table_args__ = {'schema': 'fast_lite'}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    fiscal_week = Column(String(16), comment='FY23Q1W12')
    processor = Column(String(32))
    create_time = Column(DateTime)
    update_time = Column(DateTime)

    @classmethod
    def bulk_save(cls, objs: list):
        s = FASTLiteSession()
        try:
            query = f"""
                            INSERT INTO {cls.__tablename__} 
                            (fiscal_week,processor,create_time, update_time)
                            VALUES (:fiscal_week, :processor,:create_time,:update_time)
                            ON DUPLICATE KEY UPDATE update_time = VALUES(update_time)
                        """
            s.execute(query, objs)
            s.commit()
        except Exception as e:
            logger.exception(str(e))
            raise ErrorExcept(ErrCode.DBInsert, "insert to db failed" + str(e))
        finally:
            s.close()
        return True

    @classmethod
    def query_by_fiscal_week(cls, fiscal_week: str) -> list:
        s = FASTLiteSession()
        ret = []
        try:
            ret = s.execute(f'''
                                SELECT processor
                                FROM {cls.__tablename__}
                                WHERE fiscal_week = '{fiscal_week}';
                                ''').fetchall()
            ret = [x['processor'] for x in ret]
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return ret

    @classmethod
    def delete_by_fiscal_week(cls, fiscal_week: str, processor: str):
        s = FASTLiteSession()
        try:
            q = s.query(cls).filter(cls.fiscal_week == fiscal_week).filter(cls.processor == processor)
            q.delete()
            s.commit()
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
