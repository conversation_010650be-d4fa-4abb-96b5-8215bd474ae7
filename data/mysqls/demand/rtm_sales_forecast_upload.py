from sqlalchemy import desc

from util.fast_lite_base import *
from util.const import *


class FastLiteRTMSalesForecastUpload(FASTLiteBase):
    __tablename__ = f"fast_lite_rtm_sales_forecast_upload"

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    rtm = Column(String(32), comment='')
    fiscal_week_year = Column(Integer, comment='')
    week_date = Column(String(16), comment='FY23Q1W12')
    sold_to_id = Column(Integer, comment='customer sold to id')
    sold_to_name = Column(String(256), comment='customer sold to name')
    lob = Column(String(64), comment='')
    model = Column(String(128), comment='')
    mpn = Column(String(128), comment='')
    region = Column(String(32))
    sub_rtm = Column(String(64))
    sub_lob = Column(String(256))
    nand = Column(String(256))
    color = Column(String(256))
    uploader = Column(String(256), comment='')
    forecast_cw = Column(Integer, comment='')
    forecast_cw1 = Column(Integer, comment='')
    forecast_cw2 = Column(Integer, comment='')
    forecast_cw3 = Column(Integer, comment='')
    forecast_cw4 = Column(Integer, comment='')
    forecast_cw5 = Column(Integer, comment='')
    forecast_cw6 = Column(Integer, comment='')
    forecast_cw7 = Column(Integer, comment='')
    forecast_cw8 = Column(Integer, comment='')
    forecast_cw9 = Column(Integer, comment='')
    forecast_cw10 = Column(Integer, comment='')
    forecast_cw11 = Column(Integer, comment='')
    forecast_cw12 = Column(Integer, comment='')
    forecast_type = Column(String(16), comment='')
    remark = Column(Text)
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    @classmethod
    def query_by_rtm_fiscal_week(cls, rtm: str, fiscal_week: str):

        s = FASTLiteSession()
        upload_record = None
        try:
            rtm_forecast_upload_obj = s.query(cls.uploader,
                                              cls.update_time).filter(cls.week_date == fiscal_week, cls.rtm == rtm)
            upload_record = rtm_forecast_upload_obj.first()
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return upload_record
    
    @classmethod
    def query_rtm_records_by_fiscal_week(cls, fiscal_week: str, rtms: list):

        s = FASTLiteSession()
        rtm_records = None
        try:
            rtm_records = (s.query(cls.rtm, cls.uploader).distinct()
                           .filter(cls.week_date == fiscal_week)
                           .filter(cls.rtm.in_(rtms))
                           .all())
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return rtm_records

    @classmethod
    def query_dataframe_by_rtm_fiscal_week(cls, rtm: str, fiscal_week: str):
        s = FASTLiteSession()
        ret = pd.DataFrame()
        try:
            q = s.query(cls) \
                .filter(cls.rtm == rtm) \
                .filter(cls.week_date == fiscal_week)
            ret = pd.read_sql_query(q.statement, fast_lite_engine)
        except Exception as e:
            logger.exception(str(e))
        finally:
            s.close()
        return ret

    @classmethod
    def delete_by_rtm_week(cls, rtm: str, fiscal_week_year: int):
        s = FASTLiteSession()
        delete_success = True
        try:
            s.query(cls) \
                .filter(cls.rtm == rtm) \
                .filter(cls.fiscal_week_year == fiscal_week_year) \
                .delete()
            s.commit()
        except Exception as e:
            delete_success = False
            logger.exception(str(e))
        finally:
            s.close()
        return delete_success

    @classmethod
    def bulk_save(cls, objs: list):
        s = FASTLiteSession()
        try:
            s.execute(cls.__table__.insert(), objs)
            s.commit()
        except Exception as e:
            logger.exception(str(e))
            raise ErrorExcept(ErrCode.DBInsert, "insert to db failed" + str(e))
        finally:
            s.close()
        return True

    @classmethod
    def bulk_insert_or_update(cls, objs: list):
        s = FASTLiteSession()
        try:
            insert_stmt = f"""
                INSERT INTO {cls.__tablename__} 
                (rtm, fiscal_week_year, week_date, sold_to_id, sold_to_name, lob, model, 
                mpn, region, sub_rtm, sub_lob, nand, color, uploader, forecast_cw, forecast_cw1, 
                forecast_cw2, forecast_cw3, forecast_cw4, forecast_cw5, forecast_cw6, remark, create_time, update_time) 
                 VALUES 
                 (:rtm, :fiscal_week_year, :week_date, :sold_to_id, :sold_to_name, :lob, :model, :mpn, :region,
                 :sub_rtm, :sub_lob, :nand,:color,:uploader,:forecast_cw, :forecast_cw1,:forecast_cw2,:forecast_cw3,
                 :forecast_cw4,:forecast_cw5,:forecast_cw6,:remark,:create_time,:update_time)
                 ON DUPLICATE KEY UPDATE 
                 lob = VALUES(lob), model = VALUES(model), region = VALUES(region), sub_rtm = VALUES(sub_rtm), 
                 sub_lob = VALUES(sub_lob),nand = VALUES(nand),color = VALUES(color),uploader = VALUES(uploader),
                 forecast_cw = VALUES(forecast_cw),forecast_cw1 = VALUES(forecast_cw1),
                 forecast_cw2 = VALUES(forecast_cw2),forecast_cw3 = VALUES(forecast_cw3),
                 forecast_cw4 = VALUES(forecast_cw4),forecast_cw5 = VALUES(forecast_cw5),
                 forecast_cw6 = VALUES(forecast_cw6),
                 remark = VALUES(remark),update_time = VALUES(update_time)
            """
            s.execute(insert_stmt, objs)
            s.commit()
        except Exception as e:
            logger.exception(e)
            raise ErrorExcept(ErrCode.DBInsert, "insert to db failed" + e)
        finally:
            s.close()
        return True

    @classmethod
    def query_sales_by_region_rtm_fiscal_week(cls, region: str, fiscal_week: str, rtms: list):
        s = FASTLiteSession()
        res = []
        try:
            query = s.query(
                cls.week_date.label('fiscal_week'), cls.region, cls.sold_to_id, cls.mpn,
                cls.lob, cls.rtm, cls.sub_rtm, cls.forecast_cw.label('forecast_cw_sales'),
                cls.forecast_cw1.label('forecast_cw1_sales'), cls.forecast_cw2.label('forecast_cw2_sales'),
                cls.forecast_cw3.label('forecast_cw3_sales'), cls.forecast_cw4.label('forecast_cw4_sales'),
                cls.forecast_cw5.label('forecast_cw5_sales'), cls.forecast_cw6.label('forecast_cw6_sales'),
                cls.forecast_cw7.label('forecast_cw7_sales'), cls.forecast_cw8.label('forecast_cw8_sales')) \
                .filter(cls.week_date == fiscal_week, cls.rtm.in_(rtms), cls.region == region)
            res = pd.read_sql_query(query.statement, fast_lite_engine)
            return res
        except Exception as e:
            logger.error(e)
            raise ErrorExcept(ErrCode.DBQueryError, "db query error: " + str(e))
        finally:
            s.close()

    @classmethod
    def get_menu_data(cls, fiscal_week_name: str, lob: str, sub_lob: str = None):
        s = FASTLiteSession()
        try:
            filter_params = [cls.week_date == fiscal_week_name, cls.lob == lob]
            if sub_lob:
                filter_params.append(cls.sub_lob == sub_lob)
            res = (s.query(cls.sub_lob, cls.rtm, cls.sub_rtm)
                   .filter(*filter_params).distinct().all())
            return res
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()

    @classmethod
    def query_by_fiscal_week_and_lob(cls, fiscal_week_name: str, lob: str, sub_lob: str = None, rtms: list[str] = None):
        s = FASTLiteSession()
        try:
            filter_params = [cls.week_date == fiscal_week_name, cls.lob == lob, cls.rtm.in_(rtms)]
            if sub_lob:
                filter_params.append(cls.sub_lob == sub_lob)
            res = s.query(cls).filter(*filter_params).all()
            return res
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()

