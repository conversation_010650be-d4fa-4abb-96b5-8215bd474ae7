import traceback

from util.const import ErrorExcept, ErrCode
from util.fast_lite_base import *


class JdSelfRunActualSoldToRepository(FASTLiteBase):
    __tablename__ = "jd_self_run_actual_sold_to"
    __table_args__ = {"schema": "fast_lite"}

    id = Column(String(32), primary_key=True)
    fiscal_week = Column(String(128))
    sold_to_id = Column(String(128))
    sold_to_name = Column(String(128))
    sub_lob = Column(String(128))
    mpn = Column(String(128))
    df_cw1 = Column(Float)
    df_cw2 = Column(Float)
    df_cw1_adjusted = Column(Float)
    df_cw2_adjusted = Column(Float)
    shipment_plan_cw = Column(Float)
    shipment_plan_cw1 = Column(Float)
    shipment_plan_cw2 = Column(Float)
    eoh_lw = Column(Integer)
    di_cw1 = Column(Float)
    di_cw2 = Column(Float)
    dn_cw1 = Column(Float)
    dn_cw2 = Column(Float)
    dn_cw1_adjusted = Column(Float)
    dn_cw2_adjusted = Column(Float)
    base_demand_cw1 = Column(Float)
    base_demand_cw2 = Column(Float)
    base_demand_cw1_adjusted = Column(Float)
    base_demand_cw2_adjusted = Column(Float)

    @classmethod
    def get_jd_actual_sold_to(cls, fiscal_week) -> list[dict]:
        s = FASTLiteSession()
        res = []
        try:
            result = s.execute(f'''
                                SELECT *
                                FROM {cls.__tablename__}
                                WHERE fiscal_week = '{fiscal_week}'
                                ''').all()
            for item in result:
                res.append({
                    "fiscal_week": item.fiscal_week,
                    "sold_to_id": item.sold_to_id,
                    "sold_to_name": item.sold_to_name,
                    "sub_lob": item.sub_lob,
                    "mpn": item.mpn,
                    "df_cw1": item.df_cw1,
                    "df_cw2": item.df_cw2,
                    "df_cw1_adjusted": item.df_cw1_adjusted,
                    "df_cw2_adjusted": item.df_cw2_adjusted,
                    "shipment_plan_cw": item.shipment_plan_cw,
                    "shipment_plan_cw1": item.shipment_plan_cw1,
                    "shipment_plan_cw2": item.shipment_plan_cw2,
                    "eoh_lw": item.eoh_lw,
                    "di_cw1": item.di_cw1,
                    "di_cw2": item.di_cw2,
                    "dn_cw1": item.dn_cw1,
                    "dn_cw2": item.dn_cw2,
                    "dn_cw1_adjusted": item.dn_cw1_adjusted,
                    "dn_cw2_adjusted": item.dn_cw2_adjusted,
                    "base_demand_cw1": item.base_demand_cw1,
                    "base_demand_cw2": item.base_demand_cw2,
                    "base_demand_cw1_adjusted": item.base_demand_cw1_adjusted,
                    "base_demand_cw2_adjusted": item.base_demand_cw2_adjusted,
                })
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return res

    @classmethod
    def batch_update(cls, objs: list, fields_to_update):
        if objs is None or len(objs) == 0:
            return
        s = FASTLiteSession()
        try:
            insert_parts = [f"{field}" for field in fields_to_update]
            value_parts = [f":{field}" for field in fields_to_update]
            key_parts = [f"{field} = VALUES({field})" for field in fields_to_update]
            insert_stmt = f"""
                            INSERT INTO {cls.__tablename__} 
                            (fiscal_week, sold_to_id, mpn,  {', '.join(insert_parts)})
                             VALUES 
                                (:fiscal_week, :sold_to_id, :mpn,  {', '.join(value_parts)})
                            ON DUPLICATE KEY UPDATE 
                                 {', '.join(key_parts)}
                        """
            s.execute(insert_stmt, objs)
            s.commit()
        except Exception as e:
            logger.exception(traceback.format_exc())
            raise ErrorExcept(ErrCode.DBInsert, "insert to db failed" + traceback.format_exc())
        finally:
            s.close()
        return True

