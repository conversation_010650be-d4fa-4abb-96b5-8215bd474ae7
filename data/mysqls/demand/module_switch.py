from util.fast_lite_base import *


class ModuleSwitch(FASTLiteBase):
    __tablename__ = 'module_switch'
    __table_args__ = {'schema': 'fast_lite'}

    id = Column(Integer, autoincrement=True, primary_key=True)
    module = Column(String(16))
    week_date = Column(String(32))
    create_time = Column(DateTime)
    update_time = Column(DateTime)

    @classmethod
    def query_date_by_module(cls, module: str) -> str:
        s = FASTLiteSession()
        ret = ''
        try:
            result = s.execute(f'''
                                SELECT week_date
                                FROM {cls.__tablename__}
                                WHERE module = '{module}';
                                ''').first()
            ret = result['week_date']
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return ret
