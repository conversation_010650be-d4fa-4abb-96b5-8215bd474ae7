from util.fast_lite_base import *
from util.const import ErrorExcept, ErrCode


class IdealDemandCPFUploadDFA(FASTLiteBase):
    __tablename__ = "ideal_demand_cpf_upload_dfa"

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    fiscal_week = Column(String(16), comment='FY23Q1W12')
    sub_lob = Column(String(64))
    cw = Column(Integer)
    cw1 = Column(Integer)
    cw2 = Column(Integer)
    cw3 = Column(Integer)
    cw4 = Column(Integer)
    cw5 = Column(Integer)
    cw6 = Column(Integer)
    cw7 = Column(Integer)
    cw8 = Column(Integer)
    cw9 = Column(Integer)
    
    uploader = Column(String(64))
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    is_published = Column(SmallInteger, server_default='0', comment="是否已发布")

    @classmethod
    def query_by_week(cls, fiscal_week: str) -> pd.DataFrame:
        s = FASTLiteSession()
        ret = pd.DataFrame()
        try:
            q = s.query(cls.fiscal_week,
                    cls.sub_lob,
                    cls.cw,
                    cls.cw1,
                    cls.cw2,
                    cls.cw3,
                    cls.cw4,
                    cls.cw5,
                    cls.cw6,
                    cls.cw7,
                    cls.cw8,
                ).filter(cls.fiscal_week == fiscal_week)
            ret = pd.read_sql_query(q.statement, fast_lite_engine)
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

    @classmethod
    def query_list_by_week(cls, fiscal_week: str) -> list:
        s = FASTLiteSession()
        ret = []
        try:
            query_all = s.query(cls.fiscal_week,
                        cls.sub_lob,
                        cls.cw,
                        cls.cw1,
                        cls.cw2,
                        cls.cw3,
                        cls.cw4,
                        cls.cw5,
                        cls.cw6,
                        cls.cw7,
                        cls.cw8,
                        ).filter(cls.fiscal_week == fiscal_week, cls.is_published== True).all()
            ret = [{
                'fiscal_week': x.fiscal_week,
                'sub_lob': x.sub_lob,
                'cw': x.cw,
                'cw1': x.cw1,
                'cw2': x.cw2,
                'cw3': x.cw3,
                'cw4': x.cw4,
                'cw5': x.cw5,
                'cw6': x.cw6,
                'cw7': x.cw7,
                'cw8': x.cw8} for x in query_all]
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

    @classmethod
    def batch_save(cls, datas: list):
        s = FASTLiteSession()
        try:
            s.bulk_insert_mappings(cls, datas)
            s.commit()
        except Exception as e:
            logger.debug(str(e))
            raise ErrorExcept(ErrCode.DBInsert, "insert to db failed" + str(e))
        finally:
            s.close()
        return True

    @classmethod
    def bulk_insert_or_update(cls, objs: list):
        s = FASTLiteSession()
        try:
            insert_stmt = f"""
                INSERT INTO {cls.__tablename__} 
                (fiscal_week, sub_lob, cw, cw1, cw2, cw3, cw4, cw5, cw6, cw7, cw8, uploader, create_time, update_time) 
                 VALUES 
                 (:fiscal_week, :sub_lob, :cw, :cw1, :cw2, :cw3, :cw4, :cw5, :cw6, :cw7, :cw8, :uploader, :create_time, :update_time)
                 ON DUPLICATE KEY UPDATE 
                 cw1 = VALUES(cw1), cw2 = VALUES(cw2), cw3 = VALUES(cw3), cw4 = VALUES(cw4), cw5 = VALUES(cw5), cw6 = VALUES(cw6), cw7 = VALUES(cw7), cw8 = VALUES(cw8), uploader = VALUES(uploader), update_time = VALUES(update_time)
            """
            s.execute(insert_stmt, objs)
            s.commit()
        except Exception as e:
            logger.exception(e)
            raise ErrorExcept(ErrCode.DBInsert, "insert to db failed" + e)
        finally:
            s.close()
        return True

    @classmethod
    def delete_by_week(cls, fiscal_week: str):
        s = FASTLiteSession()
        try:
            s.query(cls).filter(cls.fiscal_week == fiscal_week).delete()
            s.commit()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()

    @classmethod
    def query_upload_by_week(cls, fiscal_week: str) -> list:
        s = FASTLiteSession()
        ret = []
        try:
            ret = s.query(cls.uploader,
                    cls.create_time,
                    cls.is_published
                ).filter(cls.fiscal_week == fiscal_week).all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret
    
    @classmethod
    def publish_by_week(cls, fiscal_week: str) -> bool:
        s = FASTLiteSession()
        try:
            s.query(cls).filter(cls.fiscal_week == fiscal_week) \
                .update({'is_published': True, 'update_time': datetime.now()})
            s.commit()
            logger.info(f"publish {cls.__tablename__}")
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return True

    @classmethod
    def query_count_by_week(cls, fiscal_week: str) -> int:
        s = FASTLiteSession()
        ret = 0
        try:
            ret = s.query(func.count(cls.id)).filter(cls.fiscal_week == fiscal_week).scalar()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

    @classmethod
    def is_published_by_week(cls, fiscal_week: str) -> bool:
        s = FASTLiteSession()
        ret = 0
        try:
            ret = s.query(func.count(cls.id)).filter(cls.fiscal_week == fiscal_week, cls.is_published == True).scalar()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return True if ret else False
