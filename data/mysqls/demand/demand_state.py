from typing import Optional

from sqlalchemy import UniqueConstraint, or_

from domain.dashboard.entity.fiscal_week import FiscalWeek
from domain.demand.entity.demand_record import DemandRecord
from domain.demand.entity.state import DemandState
from util.fast_lite_base import *


class TblDemandState(FASTLiteBase):
    __tablename__ = "tbl_demand_state"
    __table_args__ = {"schema": "fast_lite"}

    id = Column(Integer, autoincrement=True, primary_key=True, comment="ID")
    fiscal_week = Column(String(16), comment="FY23Q1W12")
    demand = Column(String(32), comment="ideal; topdown; normalized")
    rtm_state = Column(Integer, comment="比特位 0001:Mono,0010:Multi,0100:Carrier,1000:Online")
    state = Column(Integer, server_default="0", comment="0,1,2,10,20")
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    def __init__(self, fiscal_week: str, demand: str,
                 state: int = 0, rtm_state: int = 0):
        self.fiscal_week = fiscal_week
        self.demand = demand
        self.state = state
        self.rtm_state = rtm_state

    def save(self) -> int:
        s = FASTLiteSession()
        try:
            s.add(self)
            s.commit()
            v_id = self.id
        except Exception as e:
            logger.exception(e)
            v_id = 0
        finally:
            s.close()
        return v_id

    @classmethod
    def update_by_id(cls, id, update_data: dict):
        s = FASTLiteSession()
        try:
            s.query(cls) \
                .filter(cls.id == id) \
                .update(update_data)
            s.commit()
        except Exception as e:
            logger.exception(e)
            raise f"update {cls.__tablename__} error: {e}"
        finally:
            s.close()

    @classmethod
    def query_state(cls, fiscal_week: str, demand: str):
        s = FASTLiteSession()
        ret = None
        try:
            ret = (
                s.query(cls)
                .filter(cls.fiscal_week == fiscal_week)
                .filter(cls.demand == demand)
                .one_or_none()
            )
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return ret

    @classmethod
    def query_by_fiscal_week(cls, fiscal_week: str) -> list[DemandRecord]:
        s = FASTLiteSession()
        states = []
        try:
            ret = (
                s.query(cls)
                .filter(cls.fiscal_week == fiscal_week)
                .all()
            )
            for demand_state in ret:
                record = DemandRecord(
                    fiscal_week=fiscal_week,
                    demand=demand_state.demand,
                    state=DemandState(demand_state.state),
                    rtm_state=demand_state.rtm_state,
                    update_time=demand_state.update_time
                )
                states.append(record)
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return states

    @classmethod
    def query_distinct_fiscal_weeks_by_demand(cls, demand: str, demand_state: Optional[int] = None):
        s = FASTLiteSession()
        ret = []
        try:
            filter_params = [cls.demand == demand]
            if demand_state is not None:
                filter_params.append(cls.state >= demand_state)
            fiscal_weeks = s.query(cls).filter(*filter_params).distinct().all()
            fiscal_week_strs = [x.fiscal_week for x in fiscal_weeks]
            ret = sorted(fiscal_week_strs, key=lambda x: FiscalWeek(x).fiscal_week_int, reverse=True)
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return ret
