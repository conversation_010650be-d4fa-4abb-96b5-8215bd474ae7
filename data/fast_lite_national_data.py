from math import ceil
import pandas as pd


from util.const import *
from util.gc_dmp_algo_base import *
from util.util import get_weeks_info, list_to_str, gen_dynamic_key, get_rolling_13_weeks
from data.fiscal_year_week import FiscalYearWeek
 
    
class NationalForecastingResultDtl(GcDmpAlgoBase):
    __tablename__ = 'national_forecasting_result_dtl'
    __table_args__ = {'schema': 'gc_dmp_algo'}
    
    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    rtm = Column(String(256), comment='')
    type = Column(String(256), comment='')
    prod_id = Column(String(256), comment='')
    sku = Column(String(256), comment='')
    fph4 = Column(String(256), comment='')
    fph3 = Column(String(256), comment='')
    fph1 = Column(String(256), comment='')
    measure_cd = Column(String(256), comment='')
    fcst_prod_level = Column(String(256), comment='')
    prediction_cw = Column(Float, comment='')
    prediction_cw1 = Column(Float, comment='')
    prediction_cw2 = Column(Float, comment='')
    prediction_cw3 = Column(Float, comment='')
    prediction_cw4 = Column(Float, comment='')
    prediction_cw5 = Column(Float, comment='')
    prediction_cw6 = Column(Float, comment='')
    prediction_cw7 = Column(Float, comment='')
    prediction_cw8 = Column(Float, comment='')
    prediction_cw9 = Column(Float, comment='')
    prediction_cw10 = Column(Float, comment='')
    prediction_cw11 = Column(Float, comment='')
    prediction_cw12 = Column(Float, comment='')
    is_high_runner = Column(String(256), comment='')
    inference_type = Column(String(256), comment='')
    query_date = Column(String(256), comment='')
    week_begin_dt = Column(String(256), comment='')
    week_date = Column(String(256), comment='FY23Q1W12')

    @classmethod
    def gen_data_by_week(self, fiscal_qtr_week_name: str, lob: str, model: list, sku: list):
        s = GcDmpAlgoSession()
        try:
            if fiscal_qtr_week_name is None:
                return
            weeks_info = get_weeks_info(fiscal_qtr_week_name)
            # 拼接sql
            single_week_sql = f"SELECT sum(prediction_cw) as prediction_cw FROM national_forecasting_result_dtl WHERE `type`='model'"
            current_week_sql = f"SELECT sum(prediction_cw) as prediction_cw, sum(prediction_cw1) as prediction_cw1, sum(prediction_cw2) as prediction_cw2, sum(prediction_cw3) as prediction_cw3, sum(prediction_cw4) as prediction_cw4, sum(prediction_cw5) as prediction_cw5, sum(prediction_cw6) as prediction_cw6, sum(prediction_cw7) as prediction_cw7, sum(prediction_cw8) as prediction_cw8, sum(prediction_cw9) as prediction_cw9, sum(prediction_cw10) as prediction_cw10, sum(prediction_cw11) as prediction_cw11, sum(prediction_cw12) as prediction_cw12 FROM national_forecasting_result_dtl WHERE `type`='model'"
            
            condition_lob = ''
            if lob is not None:
                condition_lob = f" AND fph1='{lob}' "
                
            condition_model = ''
            if model is not None and len(model) != 0:
                condition_model = f" AND fph3 in ({list_to_str(model)}) "
            
            condition_sku = ''
            if sku is not None and len(sku) != 0:
                condition_sku = f" AND sku in ({list_to_str(sku)}) "
            
            condition_date = f" AND query_date = (SELECT max(query_date) FROM national_forecasting_result_dtl WHERE `type`='model' AND week_date='{fiscal_qtr_week_name}')"
            
            # 查询历史周的数据
            history_weeks = weeks_info.get('quarter_history_weeks')
            history_data = []
            if history_weeks is not None:
                for i in history_weeks:
                    single_condition_date = f" AND query_date = (SELECT max(query_date) FROM national_forecasting_result_dtl WHERE `type`='model' AND week_date='{i}')"
                    data = s.execute(single_week_sql+condition_lob+condition_model+condition_sku+single_condition_date)
                    # [(None,)] or [(18645.0,)]
                    ret = data.fetchall()
                    for k in ret:
                        history_data.append(k[0])

            data_query = s.execute(current_week_sql+condition_lob+condition_model+condition_sku+condition_date)
            data_list = data_query.fetchall()
            current_data = list(data_list[0])
            total_weeks = weeks_info.get('total_weeks')
            current_week = weeks_info.get('current_week')
            current_future_weeks_data = current_data[:total_weeks-current_week+1]
            current_future_five_weeks_data = current_data[:5+total_weeks-current_week+1]
            
            cq_total = []
            add_result = 0
            for j in history_data+current_future_weeks_data:
                if j is not None:
                    add_result += j
            cq_total.append(add_result)
            
            
            dynamic_key = gen_dynamic_key(total_weeks)
            national_data = cq_total+history_data+current_future_five_weeks_data
            fixed_data = {
                "rtm": 'National Model Total',
                "business_type": '',
                "sold_to": '',
            }
            dynamic_data = {}
            len_national_data = len(national_data)
            for index, value in enumerate(dynamic_key):
                if len_national_data > index and national_data[index] is not None:
                    national_value = int(national_data[index])
                else:
                    national_value = '-'
                dynamic_data[value] = national_value
            return [{**fixed_data, **dynamic_data, '*CW-1': None}]
        except Exception as e:
            logger.exception(str(e))
        finally:
            s.close()

    @classmethod
    def get_download_national_data(self, fiscal_qtr_week_name: str):
        s = GcDmpAlgoSession()
        results = pd.DataFrame()
        try:
            if fiscal_qtr_week_name is None:
                return
            # 获取滚动13周的fiscal_week, fiscal_quarter
            rolling_13_weeks = get_rolling_13_weeks(fiscal_qtr_week_name)
            for i, v in enumerate(rolling_13_weeks):
                fiscal_year_quarter_week = v.split('W')
                fiscal_year_quarter = fiscal_year_quarter_week[0]
                week = int(fiscal_year_quarter_week[1])
                fiscal_week_year_and_quarter = FiscalYearWeek.get_fiscal_week_year_and_quarter(fiscal_year_quarter, week)
                fiscal_quarter = fiscal_week_year_and_quarter.get('fiscal_quarter')
                fiscal_week = fiscal_week_year_and_quarter.get('fiscal_week_year')
                query_sql = ''
                if i == 0:
                    query_sql = f"SELECT week_date as Week_Date, fph1 AS LOB, fph3 AS Model, fph4 AS FPH4, sku AS SKU, prod_id AS MPN, '{fiscal_quarter}' AS Fiscal_Quarter, '{fiscal_week}' AS Fiscal_Week, prediction_cw AS Fcst FROM national_forecasting_result_dtl WHERE `type`='model' AND week_date='{fiscal_qtr_week_name}' AND query_date = (SELECT max(query_date) FROM national_forecasting_result_dtl WHERE `type`='model' AND week_date='{fiscal_qtr_week_name}');"
                else:
                    query_sql = f"SELECT week_date as Week_Date, fph1 AS LOB, fph3 AS Model, fph4 AS FPH4, sku AS SKU, prod_id AS MPN, '{fiscal_quarter}' AS Fiscal_Quarter, '{fiscal_week}' AS Fiscal_Week, prediction_cw{i} AS Fcst FROM national_forecasting_result_dtl WHERE `type`='model' AND week_date='{fiscal_qtr_week_name}' AND query_date = (SELECT max(query_date) FROM national_forecasting_result_dtl WHERE `type`='model' AND week_date='{fiscal_qtr_week_name}');"
                    
                ret = pd.read_sql_query(query_sql, GcDmpAlgoEngine)
                results = pd.concat([results, ret], ignore_index=False)
            return results
        except Exception as e:
            logger.exception(str(e))
        finally:
            s.close()
        return results
