from operator import and_
from sqlalchemy import or_, and_, func, desc, asc, union_all, literal_column, case
import pandas as pd

from util.const import *
from util.gc_dmp_base import *
from util.fast_lite_base import *
from data.cascade_filter_data import DimFastBusinessSoldtoMapping
from data.fiscal_year_week import FiscalYearWeek
from data.fast_lite_demand_data import FastLiteAppFastDemandSummaryWa
from util.generate_sub_query import generate_sold_to_sub_query


class AppFastDemandOnlineSummaryWa(GcDmpBase):
    __tablename__ = 'app_fast_demand_online_summary_wa'
    __table_args__ = {'schema': 'gc_dmp_fast'}
    
    fiscal_qtr_week_name = Column(String(256), primary_key=True, comment='FY23Q1W12')
    fiscal_week_year = Column(Integer, comment='')
    week_begin_dt = Column(Date, comment='')
    week_end_dt = Column(Date, comment='')
    rtm = Column(String(256), comment='')
    sold_to_id = Column(Integer, comment='')
    lob = Column(String(256), comment='')
    sub_lob = Column(String(256), comment='')
    fph4 = Column(String(256), comment='')
    project_code = Column(String(256), comment='')
    sku = Column(String(256), comment='')
    mpn_id = Column(String(256), comment='')
    demand_cw = Column(Float, comment='')
    demand_cw1 = Column(Float, comment='')
    demand_cw2 = Column(Float, comment='')
    demand_cw3 = Column(Float, comment='')
    shipment_plan_cw = Column(Float, comment='')
    shipment_plan_cw1 = Column(Float, comment='')
    shipment_plan_cw2 = Column(Float, comment='')
    shipment_plan_cw3 = Column(Float, comment='')
    gross_billing_units_cw = Column(Float, comment='')
    sp_remaining_cw1 = Column(Float, comment='')
    sp_remaining_cw2 = Column(Float, comment='')
    top_up_demand_cw1 = Column(Float, comment='')
    top_up_demand_cw2 = Column(Float, comment='')
    top_up_demand_cw3 = Column(Float, comment='')
    top_up_demand_cw4 = Column(Float, comment='')
    po_needed_cw = Column(Float, comment='')
    po_needed_cw1 = Column(Float, comment='')
    po_needed_cw2 = Column(Float, comment='')
    po_needed_cw3 = Column(Float, comment='')

    @classmethod
    def gen_data_by_week(self, fiscal_qtr_week_name: str):
        ret = query_and_insert_data(self.__tablename__, fiscal_qtr_week_name)
        return ret

class AppFastDemandCarrierSummaryWa(GcDmpBase):
    __tablename__ = 'app_fast_demand_carrier_summary_wa'
    __table_args__ = {'schema': 'gc_dmp_fast'}
    
    fiscal_qtr_week_name = Column(String(256), primary_key=True, comment='FY23Q1W12')
    fiscal_week_year = Column(Integer, comment='')
    week_begin_dt = Column(Date, comment='')
    week_end_dt = Column(Date, comment='')
    rtm = Column(String(256), comment='')
    sold_to_id = Column(Integer, comment='')
    lob = Column(String(256), comment='')
    sub_lob = Column(String(256), comment='')
    fph4 = Column(String(256), comment='')
    project_code = Column(String(256), comment='')
    sku = Column(String(256), comment='')
    mpn_id = Column(String(256), comment='')
    demand_cw = Column(Float, comment='')
    demand_cw1 = Column(Float, comment='')
    demand_cw2 = Column(Float, comment='')
    demand_cw3 = Column(Float, comment='')
    shipment_plan_cw = Column(Float, comment='')
    shipment_plan_cw1 = Column(Float, comment='')
    shipment_plan_cw2 = Column(Float, comment='')
    shipment_plan_cw3 = Column(Float, comment='')
    gross_billing_units_cw = Column(Float, comment='')
    sp_remaining_cw1 = Column(Float, comment='')
    sp_remaining_cw2 = Column(Float, comment='')
    top_up_demand_cw1 = Column(Float, comment='')
    top_up_demand_cw2 = Column(Float, comment='')
    top_up_demand_cw3 = Column(Float, comment='')
    top_up_demand_cw4 = Column(Float, comment='')
    po_needed_cw = Column(Float, comment='')
    po_needed_cw1 = Column(Float, comment='')
    po_needed_cw2 = Column(Float, comment='')
    po_needed_cw3 = Column(Float, comment='')

    @classmethod
    def gen_data_by_week(self, fiscal_qtr_week_name: str):
        ret = query_and_insert_data(self.__tablename__, fiscal_qtr_week_name)
        return ret

class AppFastDemandMonoSummaryWa(GcDmpBase):
    __tablename__ = 'app_fast_demand_mono_summary_wa'
    __table_args__ = {'schema': 'gc_dmp_fast'}
    
    fiscal_qtr_week_name = Column(String(256), primary_key=True, comment='FY23Q1W12')
    fiscal_week_year = Column(Integer, comment='')
    week_begin_dt = Column(Date, comment='')
    week_end_dt = Column(Date, comment='')
    rtm = Column(String(256), comment='')
    sold_to_id = Column(Integer, comment='')
    lob = Column(String(256), comment='')
    sub_lob = Column(String(256), comment='')
    fph4 = Column(String(256), comment='')
    project_code = Column(String(256), comment='')
    sku = Column(String(256), comment='')
    mpn_id = Column(String(256), comment='')
    demand_cw = Column(Float, comment='')
    demand_cw1 = Column(Float, comment='')
    demand_cw2 = Column(Float, comment='')
    demand_cw3 = Column(Float, comment='')
    shipment_plan_cw = Column(Float, comment='')
    shipment_plan_cw1 = Column(Float, comment='')
    shipment_plan_cw2 = Column(Float, comment='')
    shipment_plan_cw3 = Column(Float, comment='')
    gross_billing_units_cw = Column(Float, comment='')
    sp_remaining_cw1 = Column(Float, comment='')
    sp_remaining_cw2 = Column(Float, comment='')
    top_up_demand_cw1 = Column(Float, comment='')
    top_up_demand_cw2 = Column(Float, comment='')
    top_up_demand_cw3 = Column(Float, comment='')
    top_up_demand_cw4 = Column(Float, comment='')
    po_needed_cw = Column(Float, comment='')
    po_needed_cw1 = Column(Float, comment='')
    po_needed_cw2 = Column(Float, comment='')
    po_needed_cw3 = Column(Float, comment='')

    @classmethod
    def get_model_list(cls, lob, rtm, fiscal_week_year):
        s = GcDmpSession()
        res = []
        try:
            query = s.query(AppFastDemandMonoSummaryWa.sub_lob).distinct() \
                .filter(AppFastDemandMonoSummaryWa.fiscal_week_year == fiscal_week_year)
            if lob is not None and len(lob) != 0:
                query = query.filter(AppFastDemandMonoSummaryWa.lob.in_(lob))

            query1 = s.query(AppFastDemandMultiSummaryAmWa.sub_lob).distinct() \
                .filter(AppFastDemandMultiSummaryAmWa.fiscal_week_year == fiscal_week_year)
            if lob is not None and len(lob) != 0:
                query1 = query1.filter(AppFastDemandMultiSummaryAmWa.lob.in_(lob))

            query2 = s.query(AppFastDemandOnlineSummaryAmWa.sub_lob).distinct() \
                .filter(AppFastDemandOnlineSummaryAmWa.fiscal_week_year == fiscal_week_year)
            if lob is not None and len(lob) != 0:
                query2 = query2.filter(AppFastDemandOnlineSummaryAmWa.lob.in_(lob))

            query3 = s.query(AppFastDemandCarrierSummaryAmWa.sub_lob).distinct() \
                .filter(AppFastDemandCarrierSummaryAmWa.fiscal_week_year == fiscal_week_year)
            if lob is not None and len(lob) != 0:
                query3 = query3.filter(AppFastDemandCarrierSummaryAmWa.lob.in_(lob))

            res = query.union(query1).union(query2).union(query3).order_by(desc(cls.sub_lob)).all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return res
    
    @classmethod
    def get_week_list(cls, offset: int, limit: int):
        s = GcDmpSession()
        ret = []
        count = 0
        try:
        
            q = s.query(func.distinct(cls.fiscal_qtr_week_name),
                        cls.fiscal_week_year)\
                        .filter(cls.fiscal_week_year >= CPF_BEGIN_WEEK)
            
            count = q.count()
            q = q.order_by(cls.fiscal_week_year.desc())
            
            if offset is not None and limit is not None:
                q = q.limit(limit).offset(offset)
            
            ret = q.all()
            
            if count >= SHOW_MAX_WEEKS:
                count = SHOW_MAX_WEEKS
            
            if (offset + len(ret)) > SHOW_MAX_WEEKS:
                ret = ret[:SHOW_MAX_WEEKS - offset]
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret, count

    @classmethod
    def gen_data_by_week(self, fiscal_qtr_week_name: str):
        ret = query_and_insert_data(self.__tablename__, fiscal_qtr_week_name)
        return ret

    @classmethod
    def get_version_1_by_week(cls, fiscal_qtr_week_name: str):
        s = GcDmpSession()
        count = 0
        try:
            count = s.query(func.count(cls.fiscal_qtr_week_name).label('count'))\
                .filter(cls.fiscal_qtr_week_name == fiscal_qtr_week_name)\
                .scalar()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return count


    @classmethod
    def get_latest_version_by_week(cls, fiscal_qtr_week_name: str):
        version = 0
        v1 = AppFastDemandMonoSummaryWa.get_version_1_by_week(fiscal_qtr_week_name)
        v2 = AppFastAdvanceDemandMonoSummaryMondayWa.get_version_2_by_week(fiscal_qtr_week_name)
        v3 = AppFastAdvanceDemandMonoSummaryTuesdayWa.get_version_3_by_week(fiscal_qtr_week_name)
        if v3 > 0:
            version = DEMAND_THIRD_VERSION
        elif v2 > 0:
            version = DEMAND_SECOND_VERSION
        elif v1 > 0:
            version = DEMAND_FIRST_VERSION
        return version
    

    @classmethod
    def get_total_union(cls, fiscal_week_year: int, fiscal_qtr_week_name: str, lob: str, model: list, sku: list, rtm: list, business_type: list, sold_to: list, orders: list):
        ret = []
        try:
            latest_version = cls.get_latest_version_by_week(fiscal_qtr_week_name)
            s, tbl_union_all = get_multi_version_data_by_week(fiscal_qtr_week_name, latest_version)

            sub_query_union_all = s.query(tbl_union_all)\
                    .filter(tbl_union_all.c.lob == lob)\
                    .filter(tbl_union_all.c.sub_lob.in_(model))
            if sku is not None and len(sku) != 0:
                sub_query_union_all = sub_query_union_all.filter(tbl_union_all.c.sku.in_(sku))
            sub_query_union_all = sub_query_union_all.subquery()
            
            sub_query_mapping =  generate_sold_to_sub_query(s, DimFastBusinessSoldtoMapping, fiscal_week_year)
            
            q = s.query(func.sum(sub_query_union_all.c.top_up_demand_cw1).label('top_up_demand_cw1'),
                        func.sum(sub_query_union_all.c.top_up_demand_cw2).label('top_up_demand_cw2'),
                        func.sum(sub_query_union_all.c.top_up_demand_cw3).label('top_up_demand_cw3'),
                        func.sum(sub_query_union_all.c.top_up_demand_cw4).label('top_up_demand_cw4'),
                        func.sum(sub_query_union_all.c.shipment_plan_cw).label('shipment_plan_cw'),
                        func.sum(sub_query_union_all.c.shipment_plan_cw1).label('shipment_plan_cw1'),
                        func.sum(sub_query_union_all.c.shipment_plan_cw2).label('shipment_plan_cw2'),
                        func.sum(sub_query_union_all.c.shipment_plan_cw3).label('shipment_plan_cw3'),
                        func.sum(sub_query_union_all.c.po_needed_cw).label('po_needed_cw'),
                        func.sum(sub_query_union_all.c.po_needed_cw1).label('po_needed_cw1'),
                        func.sum(sub_query_union_all.c.po_needed_cw2).label('po_needed_cw2'),
                        func.sum(sub_query_union_all.c.po_needed_cw3).label('po_needed_cw3'),
                        )\
                    .join(sub_query_mapping,
                          func.cast(sub_query_mapping.c.sold_to_id, Integer) == func.cast(sub_query_union_all.c.sold_to_id, Integer))
            if rtm is not None and len(rtm) != 0:
                q = q.filter(sub_query_mapping.c.rtm.in_(rtm))
            if business_type is not None and len(business_type) != 0:
                q = q.filter(sub_query_mapping.c.business_type.in_(business_type))
            if sold_to is not None and len(sold_to) != 0:
                q = q.filter(sub_query_mapping.c.abbre.in_(sold_to))
            
            if orders != []:
                for i in orders:
                    if i['type']:
                        q = q.order_by(desc(i['name']))
                    else:
                        q = q.order_by(asc(i['name']))
                
            data_list = q.all()
            
            data_structure = {}
            if len(data_list) > 0:
                basic_data = gen_demand_basic_data(data_list[0])
                data_structure = {
                    "rtm": 'Total',
                    "business_type": '',
                    "sold_to_id": '',
                    "sold_to_name": '',
                    **basic_data
                }
                ret.append(data_structure)
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret, data_structure.keys()
   

    @classmethod
    def get_sold_to_total_union(cls, fiscal_week_year: int, fiscal_qtr_week_name: str, lob: str, model: list, sku: list, 
                        rtm: list, business_type: list, sold_to: list, orders: list,
                        page_num: int, page_size: int):
        ret = []
        count = 0
        try:
            
            latest_version = cls.get_latest_version_by_week(fiscal_qtr_week_name)
            s, tbl_union_all = get_multi_version_data_by_week(fiscal_qtr_week_name, latest_version)
            
            sub_query_union_all = s.query(tbl_union_all)\
                    .filter(tbl_union_all.c.lob == lob)\
                    .filter(tbl_union_all.c.sub_lob.in_(model))
            if sku is not None and len(sku) != 0:
                sub_query_union_all = sub_query_union_all.filter(tbl_union_all.c.sku.in_(sku))
            sub_query_union_all = sub_query_union_all.subquery()
            
            sub_query_mapping = generate_sold_to_sub_query(s, DimFastBusinessSoldtoMapping, fiscal_week_year)

            q = s.query(sub_query_mapping.c.rtm.label('rtm'),
                        sub_query_mapping.c.business_type.label('business_type'),
                        func.cast(sub_query_mapping.c.sold_to_id, Integer).label('sold_to_id'),
                        sub_query_mapping.c.abbre.label('sold_to_name'),
                        func.sum(sub_query_union_all.c.top_up_demand_cw1).label('top_up_demand_cw1'),
                        func.sum(sub_query_union_all.c.top_up_demand_cw2).label('top_up_demand_cw2'),
                        func.sum(sub_query_union_all.c.top_up_demand_cw3).label('top_up_demand_cw3'),
                        func.sum(sub_query_union_all.c.top_up_demand_cw4).label('top_up_demand_cw4'),
                        func.sum(sub_query_union_all.c.shipment_plan_cw).label('shipment_plan_cw'),
                        func.sum(sub_query_union_all.c.shipment_plan_cw1).label('shipment_plan_cw1'),
                        func.sum(sub_query_union_all.c.shipment_plan_cw2).label('shipment_plan_cw2'),
                        func.sum(sub_query_union_all.c.shipment_plan_cw3).label('shipment_plan_cw3'),
                        func.sum(sub_query_union_all.c.po_needed_cw).label('po_needed_cw'),
                        func.sum(sub_query_union_all.c.po_needed_cw1).label('po_needed_cw1'),
                        func.sum(sub_query_union_all.c.po_needed_cw2).label('po_needed_cw2'),
                        func.sum(sub_query_union_all.c.po_needed_cw3).label('po_needed_cw3'),
                        )\
                    .outerjoin(sub_query_union_all,
                          func.cast(sub_query_mapping.c.sold_to_id, Integer) == func.cast(sub_query_union_all.c.sold_to_id, Integer))
            if rtm is not None and len(rtm) != 0:
                q = q.filter(sub_query_mapping.c.rtm.in_(rtm))
            if business_type is not None and len(business_type) != 0:
                q = q.filter(sub_query_mapping.c.business_type.in_(business_type))
            if sold_to is not None and len(sold_to) != 0:
                q = q.filter(sub_query_mapping.c.abbre.in_(sold_to))
            
            q = q.group_by(sub_query_mapping.c.rtm,
                           sub_query_mapping.c.business_type,
                           sub_query_mapping.c.sold_to_id,
                           sub_query_mapping.c.abbre)
            count = q.count()
            if orders != []:
                for i in orders:
                    if i['type']:
                        q = q.order_by(desc(i['name']))
                    else:
                        q = q.order_by(asc(i['name']))
            else:
                q = q.order_by(func.field(sub_query_mapping.c.rtm, *rtm),
                               asc(sub_query_mapping.c.business_type),
                               asc(func.cast(sub_query_mapping.c.sold_to_id, Integer)))
                
            data_list = q.limit(page_size).offset(page_num).all()

            for j in data_list:
                basic_data = gen_demand_basic_data(j)
                data_structure = {
                    "rtm": j['rtm'],
                    "business_type": j['business_type'],
                    "sold_to_id": j['sold_to_id'],
                    "sold_to_name": j['sold_to_name'],
                    **basic_data
                }
                ret.append(data_structure)
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret, count


    @classmethod
    def get_download_data(cls, fiscal_week_year: int, fiscal_qtr_week_name: str, download_version: int):
        ret = []
        try:
            s, tbl_union_all = get_multi_version_data_by_week(fiscal_qtr_week_name, download_version)
            
            sub_query_mapping = generate_sold_to_sub_query(s, DimFastBusinessSoldtoMapping, fiscal_week_year)
            
            q = s.query(literal_column(f"'{fiscal_qtr_week_name}'").label('Week_Date'),
                        sub_query_mapping.c.rtm.label('RTM'),
                        sub_query_mapping.c.business_type.label('Business Type'),
                        sub_query_mapping.c.sold_to_id.label('Customer Sold-to ID'),
                        sub_query_mapping.c.sold_to_name_en.label('Sold-to Name'),
                        sub_query_mapping.c.abbre.label('Abbre.'),
                        tbl_union_all.c.lob.label('LOB'),
                        tbl_union_all.c.sub_lob.label('Model'),
                        tbl_union_all.c.fph4.label('FPH4'),
                        tbl_union_all.c.project_code.label('Project Code'),
                        tbl_union_all.c.sku.label('SKU'),
                        tbl_union_all.c.mpn_id.label('MPN'),
                        tbl_union_all.c.top_up_demand_cw1.label('Top Up Demand CW+1'),
                        tbl_union_all.c.top_up_demand_cw2.label('Top Up Demand CW+2'),
                        tbl_union_all.c.top_up_demand_cw3.label('Top Up Demand CW+3'),
                        tbl_union_all.c.top_up_demand_cw4.label('Top Up Demand CW+4'),
                        tbl_union_all.c.shipment_plan_cw.label('Shipment Plan CW'),
                        tbl_union_all.c.shipment_plan_cw1.label('Shipment Plan CW+1'),
                        tbl_union_all.c.shipment_plan_cw2.label('Shipment Plan CW+2'),
                        tbl_union_all.c.shipment_plan_cw3.label('Shipment Plan CW+3'),
                        tbl_union_all.c.po_needed_cw.label('PO Needed CW'),
                        tbl_union_all.c.po_needed_cw1.label('PO Needed CW+1'),
                        tbl_union_all.c.po_needed_cw2.label('PO Needed CW+2'),
                        tbl_union_all.c.po_needed_cw3.label('PO Needed CW+3'),
                        )\
                .outerjoin(tbl_union_all,
                           func.cast(sub_query_mapping.c.sold_to_id, Integer) == func.cast(tbl_union_all.c.sold_to_id, Integer))\
                .order_by(sub_query_mapping.c.rtm, 
                          sub_query_mapping.c.business_type,
                          func.cast(sub_query_mapping.c.sold_to_id, Integer),
                          sub_query_mapping.c.abbre,
                          tbl_union_all.c.lob,
                          tbl_union_all.c.sub_lob)
            ret = pd.read_sql_query(q.statement, GcDmpEngine)
            
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret


class AppFastDemandMultiSummaryWa(GcDmpBase):
    __tablename__ = 'app_fast_demand_multi_summary_wa'
    __table_args__ = {'schema': 'gc_dmp_fast'}
    
    fiscal_qtr_week_name = Column(String(256), primary_key=True, comment='FY23Q1W12')
    fiscal_week_year = Column(Integer, comment='')
    week_begin_dt = Column(Date, comment='')
    week_end_dt = Column(Date, comment='')
    rtm = Column(String(256), comment='')
    sold_to_id = Column(Integer, comment='')
    lob = Column(String(256), comment='')
    sub_lob = Column(String(256), comment='')
    fph4 = Column(String(256), comment='')
    project_code = Column(String(256), comment='')
    sku = Column(String(256), comment='')
    mpn_id = Column(String(256), comment='')
    demand_cw = Column(Float, comment='')
    demand_cw1 = Column(Float, comment='')
    demand_cw2 = Column(Float, comment='')
    demand_cw3 = Column(Float, comment='')
    shipment_plan_cw = Column(Float, comment='')
    shipment_plan_cw1 = Column(Float, comment='')
    shipment_plan_cw2 = Column(Float, comment='')
    shipment_plan_cw3 = Column(Float, comment='')
    gross_billing_units_cw = Column(Float, comment='')
    sp_remaining_cw1 = Column(Float, comment='')
    sp_remaining_cw2 = Column(Float, comment='')
    top_up_demand_cw1 = Column(Float, comment='')
    top_up_demand_cw2 = Column(Float, comment='')
    top_up_demand_cw3 = Column(Float, comment='')
    top_up_demand_cw4 = Column(Float, comment='')
    po_needed_cw = Column(Float, comment='')
    po_needed_cw1 = Column(Float, comment='')
    po_needed_cw2 = Column(Float, comment='')
    po_needed_cw3 = Column(Float, comment='')
    
    @classmethod
    def gen_data_by_week(self, fiscal_qtr_week_name: str):
        ret = query_and_insert_data(self.__tablename__, fiscal_qtr_week_name)
        return ret

    @classmethod
    def get_fiscal_year_quarter_week_name(self, page_num: int, page_size: int):
        s = GcDmpSession()
        ret = []
        count = 0
        try:
        
            q = s.query(AppFastDemandMultiSummaryWa.fiscal_qtr_week_name.distinct())
            
            count = q.count()
            q = q.order_by(AppFastDemandMultiSummaryWa.fiscal_week_year.desc())
            
            if page_num is not None and page_size is not None:
                q = q.limit(page_size).offset(page_num)
                
            ret = q.all()
            
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret, count
    
    @classmethod
    def get_total(self, fiscal_qtr_week_name: str, lob: str, model: list, sku: list, rtm: list, business_type: list, sold_to: list, orders: list):
        s = GcDmpSession()
        ret = []
        try:
            q = s.query(func.sum(AppFastDemandMultiSummaryWa.top_up_demand_cw1).label('top_up_demand_cw1'),
                        func.sum(AppFastDemandMultiSummaryWa.top_up_demand_cw2).label('top_up_demand_cw2'),
                        func.sum(AppFastDemandMultiSummaryWa.top_up_demand_cw3).label('top_up_demand_cw3'),
                        func.sum(AppFastDemandMultiSummaryWa.top_up_demand_cw4).label('top_up_demand_cw4'),
                        func.sum(AppFastDemandMultiSummaryWa.shipment_plan_cw).label('shipment_plan_cw'),
                        func.sum(AppFastDemandMultiSummaryWa.shipment_plan_cw1).label('shipment_plan_cw1'),
                        func.sum(AppFastDemandMultiSummaryWa.shipment_plan_cw2).label('shipment_plan_cw2'),
                        func.sum(AppFastDemandMultiSummaryWa.shipment_plan_cw3).label('shipment_plan_cw3'),
                        func.sum(AppFastDemandMultiSummaryWa.po_needed_cw).label('po_needed_cw'),
                        func.sum(AppFastDemandMultiSummaryWa.po_needed_cw1).label('po_needed_cw1'),
                        func.sum(AppFastDemandMultiSummaryWa.po_needed_cw2).label('po_needed_cw2'),
                        func.sum(AppFastDemandMultiSummaryWa.po_needed_cw3).label('po_needed_cw3'),
                        )\
                    .join(DimFastBusinessSoldtoMapping,
                          func.cast(DimFastBusinessSoldtoMapping.sold_to_id, Integer) == AppFastDemandMultiSummaryWa.sold_to_id)\
                    .filter(AppFastDemandMultiSummaryWa.fiscal_qtr_week_name == fiscal_qtr_week_name)\
                    .filter(AppFastDemandMultiSummaryWa.lob == lob)\
                    .filter(AppFastDemandMultiSummaryWa.sub_lob.in_(model))
            if sku is not None and len(sku) != 0:
                q = q.filter(AppFastDemandMultiSummaryWa.sku.in_(sku))
            if rtm is not None and len(rtm) != 0:
                q = q.filter(AppFastDemandMultiSummaryWa.rtm.in_(rtm))
            if business_type is not None and len(business_type) != 0:
                q = q.filter(DimFastBusinessSoldtoMapping.business_type.in_(business_type))
            if sold_to is not None and len(sold_to) != 0:
                q = q.filter(DimFastBusinessSoldtoMapping.abbre.in_(sold_to))
            
            q = q.group_by(AppFastDemandMultiSummaryWa.fiscal_qtr_week_name)
            
            if orders != []:
                for i in orders:
                    if i['type']:
                        q = q.order_by(desc(i['name']))
                    else:
                        q = q.order_by(asc(i['name']))
                
            data_list = q.all()
            data_structure = {}
            if len(data_list) > 0:
                basic_data = gen_demand_basic_data(data_list[0])
                data_structure = {
                    "rtm": 'Total',
                    "business_type": '',
                    "sold_to_id": '',
                    "sold_to_name": '',
                    **basic_data
                }
                ret.append(data_structure)
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret, data_structure.keys()
    
    @classmethod
    def get_total_union(self, fiscal_qtr_week_name: str, lob: str, model: list, sku: list, rtm: list, business_type: list, sold_to: list, orders: list):
        s = GcDmpSession()
        ret = []
        try:
            tbl_union_all = gen_union_all_table()
            q = s.query(func.sum(tbl_union_all.c.top_up_demand_cw1).label('top_up_demand_cw1'),
                        func.sum(tbl_union_all.c.top_up_demand_cw2).label('top_up_demand_cw2'),
                        func.sum(tbl_union_all.c.top_up_demand_cw3).label('top_up_demand_cw3'),
                        func.sum(tbl_union_all.c.top_up_demand_cw4).label('top_up_demand_cw4'),
                        func.sum(tbl_union_all.c.shipment_plan_cw).label('shipment_plan_cw'),
                        func.sum(tbl_union_all.c.shipment_plan_cw1).label('shipment_plan_cw1'),
                        func.sum(tbl_union_all.c.shipment_plan_cw2).label('shipment_plan_cw2'),
                        func.sum(tbl_union_all.c.shipment_plan_cw3).label('shipment_plan_cw3'),
                        func.sum(tbl_union_all.c.po_needed_cw).label('po_needed_cw'),
                        func.sum(tbl_union_all.c.po_needed_cw1).label('po_needed_cw1'),
                        func.sum(tbl_union_all.c.po_needed_cw2).label('po_needed_cw2'),
                        func.sum(tbl_union_all.c.po_needed_cw3).label('po_needed_cw3'),
                        )\
                    .join(DimFastBusinessSoldtoMapping,
                          func.cast(DimFastBusinessSoldtoMapping.sold_to_id, Integer) == func.cast(tbl_union_all.c.sold_to_id, Integer))\
                    .filter(tbl_union_all.c.fiscal_qtr_week_name == fiscal_qtr_week_name)\
                    .filter(tbl_union_all.c.lob == lob)\
                    .filter(tbl_union_all.c.sub_lob.in_(model))
            if sku is not None and len(sku) != 0:
                q = q.filter(tbl_union_all.c.sku.in_(sku))
            if rtm is not None and len(rtm) != 0:
                q = q.filter(tbl_union_all.c.rtm.in_(rtm))
            if business_type is not None and len(business_type) != 0:
                q = q.filter(DimFastBusinessSoldtoMapping.business_type.in_(business_type))
            if sold_to is not None and len(sold_to) != 0:
                q = q.filter(DimFastBusinessSoldtoMapping.abbre.in_(sold_to))
            
            q = q.group_by(tbl_union_all.c.fiscal_qtr_week_name)
            
            if orders != []:
                for i in orders:
                    if i['type']:
                        q = q.order_by(desc(i['name']))
                    else:
                        q = q.order_by(asc(i['name']))
                
            data_list = q.all()
            
            data_structure = {}
            if len(data_list) > 0:
                basic_data = gen_demand_basic_data(data_list[0])
                data_structure = {
                    "rtm": 'Total',
                    "business_type": '',
                    "sold_to_id": '',
                    "sold_to_name": '',
                    **basic_data
                }
                ret.append(data_structure)
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret, data_structure.keys()
    
    @classmethod
    def get_sold_to_total(self, fiscal_qtr_week_name: str, lob: str, model: list, sku: list, 
                          rtm: list, business_type: list, sold_to: list, orders: list,
                          page_num: int, page_size: int):
        s = GcDmpSession()
        ret = []
        count = 0
        try:
            q = s.query(AppFastDemandMultiSummaryWa.rtm.label('rtm'),
                        DimFastBusinessSoldtoMapping.business_type.label('business_type'),
                        AppFastDemandMultiSummaryWa.sold_to_id.label('sold_to_id'),
                        DimFastBusinessSoldtoMapping.abbre.label('sold_to_name'),
                        func.sum(AppFastDemandMultiSummaryWa.top_up_demand_cw1).label('top_up_demand_cw1'),
                        func.sum(AppFastDemandMultiSummaryWa.top_up_demand_cw2).label('top_up_demand_cw2'),
                        func.sum(AppFastDemandMultiSummaryWa.top_up_demand_cw3).label('top_up_demand_cw3'),
                        func.sum(AppFastDemandMultiSummaryWa.top_up_demand_cw4).label('top_up_demand_cw4'),
                        func.sum(AppFastDemandMultiSummaryWa.shipment_plan_cw).label('shipment_plan_cw'),
                        func.sum(AppFastDemandMultiSummaryWa.shipment_plan_cw1).label('shipment_plan_cw1'),
                        func.sum(AppFastDemandMultiSummaryWa.shipment_plan_cw2).label('shipment_plan_cw2'),
                        func.sum(AppFastDemandMultiSummaryWa.shipment_plan_cw3).label('shipment_plan_cw3'),
                        func.sum(AppFastDemandMultiSummaryWa.po_needed_cw).label('po_needed_cw'),
                        func.sum(AppFastDemandMultiSummaryWa.po_needed_cw1).label('po_needed_cw1'),
                        func.sum(AppFastDemandMultiSummaryWa.po_needed_cw2).label('po_needed_cw2'),
                        func.sum(AppFastDemandMultiSummaryWa.po_needed_cw3).label('po_needed_cw3'),
                        )\
                    .join(DimFastBusinessSoldtoMapping,
                          func.cast(DimFastBusinessSoldtoMapping.sold_to_id, Integer) == AppFastDemandMultiSummaryWa.sold_to_id)\
                    .filter(AppFastDemandMultiSummaryWa.fiscal_qtr_week_name == fiscal_qtr_week_name)\
                    .filter(AppFastDemandMultiSummaryWa.lob == lob)\
                    .filter(AppFastDemandMultiSummaryWa.sub_lob.in_(model))
            if sku is not None and len(sku) != 0:
                q = q.filter(AppFastDemandMultiSummaryWa.sku.in_(sku))
            if rtm is not None and len(rtm) != 0:
                q = q.filter(AppFastDemandMultiSummaryWa.rtm.in_(rtm))
            if business_type is not None and len(business_type) != 0:
                q = q.filter(DimFastBusinessSoldtoMapping.business_type.in_(business_type))
            if sold_to is not None and len(sold_to) != 0:
                q = q.filter(DimFastBusinessSoldtoMapping.abbre.in_(sold_to))
            
            q = q.group_by(AppFastDemandMultiSummaryWa.rtm,
                           DimFastBusinessSoldtoMapping.business_type,
                           AppFastDemandMultiSummaryWa.sold_to_id,
                           DimFastBusinessSoldtoMapping.abbre,)
            count = q.count()
            if orders != []:
                for i in orders:
                    if i['type']:
                        q = q.order_by(desc(i['name']))
                    else:
                        q = q.order_by(asc(i['name']))
                
            data_list = q.limit(page_size).offset(page_num).all()
            for i in data_list:
                basic_data = gen_demand_basic_data(i)
                data_structure = {
                    "rtm": i['rtm'],
                    "business_type": i['business_type'],
                    "sold_to_id": i['sold_to_id'],
                    "sold_to_name": i['sold_to_name'],
                    **basic_data
                }
                ret.append(data_structure)
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret, count
    
    @classmethod
    def get_sold_to_total_union(self, fiscal_qtr_week_name: str, lob: str, model: list, sku: list, 
                          rtm: list, business_type: list, sold_to: list, orders: list,
                          page_num: int, page_size: int):
        s = GcDmpSession()
        ret = []
        count = 0
        try:
            tbl_union_all = gen_union_all_table()

            q = s.query(tbl_union_all.c.rtm.label('rtm'),
                        DimFastBusinessSoldtoMapping.business_type.label('business_type'),
                        tbl_union_all.c.sold_to_id.label('sold_to_id'),
                        DimFastBusinessSoldtoMapping.abbre.label('sold_to_name'),
                        func.sum(tbl_union_all.c.top_up_demand_cw1).label('top_up_demand_cw1'),
                        func.sum(tbl_union_all.c.top_up_demand_cw2).label('top_up_demand_cw2'),
                        func.sum(tbl_union_all.c.top_up_demand_cw3).label('top_up_demand_cw3'),
                        func.sum(tbl_union_all.c.top_up_demand_cw4).label('top_up_demand_cw4'),
                        func.sum(tbl_union_all.c.shipment_plan_cw).label('shipment_plan_cw'),
                        func.sum(tbl_union_all.c.shipment_plan_cw1).label('shipment_plan_cw1'),
                        func.sum(tbl_union_all.c.shipment_plan_cw2).label('shipment_plan_cw2'),
                        func.sum(tbl_union_all.c.shipment_plan_cw3).label('shipment_plan_cw3'),
                        func.sum(tbl_union_all.c.po_needed_cw).label('po_needed_cw'),
                        func.sum(tbl_union_all.c.po_needed_cw1).label('po_needed_cw1'),
                        func.sum(tbl_union_all.c.po_needed_cw2).label('po_needed_cw2'),
                        func.sum(tbl_union_all.c.po_needed_cw3).label('po_needed_cw3'),
                        )\
                    .join(DimFastBusinessSoldtoMapping,
                          func.cast(DimFastBusinessSoldtoMapping.sold_to_id, Integer) == func.cast(tbl_union_all.c.sold_to_id, Integer))\
                    .filter(tbl_union_all.c.lob == lob)\
                    .filter(tbl_union_all.c.sub_lob.in_(model))\
                    .filter(tbl_union_all.c.fiscal_qtr_week_name == fiscal_qtr_week_name)
            if sku is not None and len(sku) != 0:
                q = q.filter(tbl_union_all.c.sku.in_(sku))
            if rtm is not None and len(rtm) != 0:
                q = q.filter(tbl_union_all.c.rtm.in_(rtm))
            if business_type is not None and len(business_type) != 0:
                q = q.filter(DimFastBusinessSoldtoMapping.business_type.in_(business_type))
            if sold_to is not None and len(sold_to) != 0:
                q = q.filter(DimFastBusinessSoldtoMapping.abbre.in_(sold_to))
            
            q = q.group_by(tbl_union_all.c.rtm,
                           DimFastBusinessSoldtoMapping.business_type,
                           tbl_union_all.c.sold_to_id,
                           DimFastBusinessSoldtoMapping.abbre)
            count = q.count()
            if orders != []:
                for i in orders:
                    if i['type']:
                        q = q.order_by(desc(i['name']))
                    else:
                        q = q.order_by(asc(i['name']))
                
            data_list = q.limit(page_size).offset(page_num).all()

            for j in data_list:
                basic_data = gen_demand_basic_data(j)
                data_structure = {
                    "rtm": j['rtm'],
                    "business_type": j['business_type'],
                    "sold_to_id": j['sold_to_id'],
                    "sold_to_name": j['sold_to_name'],
                    **basic_data
                }
                ret.append(data_structure)
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret, count


    @classmethod
    def get_title(self, fiscal_qtr_week_name: str):
        titles = []
        try:
            # FY23Q1W13
            fiscal_year_quarter_week = fiscal_qtr_week_name.split('W')
            fiscal_year_quarter = fiscal_year_quarter_week[0]
            week = int(fiscal_year_quarter_week[1])
            fiscal_year = fiscal_year_quarter.split('Q')[0]
            quarter = int(fiscal_year_quarter.split('Q')[1])
            year = int(fiscal_year.split('FY')[1])
            
            next_quarter = quarter + 1
            if next_quarter > 4:
                next_quarter = 1
                next_year = year + 1
            else:
                next_year = year
            next_fiscal_year_quarter = 'FY'+str(next_year)+'Q'+str(next_quarter)
            total_weeks = FiscalYearWeek.get_total_weeks_in_quarter(fiscal_year_quarter, week)
            
            next_quarter = quarter + 1
            if next_quarter > 4:
                next_quarter = 1
                next_year = year + 1
            else:
                next_year = year
            
            # 固定标题
            fixed_title = ['RTM', 'Business\nType', 'Customer\nSold-to ID', 'Sold-to Name']

            fiscal_weeks = [] 
            # cw+1/2/3/4
            for k in range(5):
                if k > 0:
                    if week + k > total_weeks:
                        fiscal_weeks.append(f"CW+{k}\n{next_fiscal_year_quarter}W{week + k - total_weeks}")
                    else:
                        fiscal_weeks.append(f"CW+{k}\n{fiscal_year_quarter}W{week + k}")
                else:
                    fiscal_weeks.append(f"CW\n{fiscal_qtr_week_name}")
            
            top_up_title = []
            shipment_plan_title = []
            po_needed_title = []
            for m in range(4):
                top_up_title.append(f"Top-up Demand {fiscal_weeks[m+1]}")
                shipment_plan_title.append(f"Shipment Plan {fiscal_weeks[m]}")
                po_needed_title.append(f"PO Needed {fiscal_weeks[m]}")

            titles = fixed_title+top_up_title+shipment_plan_title+po_needed_title
                
        except Exception as e:
            logger.exception(e)
        return titles

    @classmethod
    def get_download_data(self, fiscal_qtr_week_name: str):
        s = GcDmpSession()
        ret = []
        try:
            tbl_union_all = gen_union_all_table()
            q = s.query(tbl_union_all.c.fiscal_qtr_week_name.label('Week_Date'),
                        tbl_union_all.c.rtm.label('RTM'),
                        DimFastBusinessSoldtoMapping.business_type.label('Business Type'),
                        tbl_union_all.c.sold_to_id.label('Customer Sold-to ID'),
                        DimFastBusinessSoldtoMapping.sold_to_name_en.label('Sold-to Name'),
                        DimFastBusinessSoldtoMapping.abbre.label('Abbre.'),
                        tbl_union_all.c.lob.label('LOB'),
                        tbl_union_all.c.sub_lob.label('Model'),
                        tbl_union_all.c.fph4.label('FPH4'),
                        tbl_union_all.c.project_code.label('Project Code'),
                        tbl_union_all.c.sku.label('SKU'),
                        tbl_union_all.c.mpn_id.label('MPN'),
                        tbl_union_all.c.top_up_demand_cw1.label('Top Up Demand CW+1'),
                        tbl_union_all.c.top_up_demand_cw2.label('Top Up Demand CW+2'),
                        tbl_union_all.c.top_up_demand_cw3.label('Top Up Demand CW+3'),
                        tbl_union_all.c.top_up_demand_cw4.label('Top Up Demand CW+4'),
                        tbl_union_all.c.shipment_plan_cw.label('Shipment Plan CW'),
                        tbl_union_all.c.shipment_plan_cw1.label('Shipment Plan CW+1'),
                        tbl_union_all.c.shipment_plan_cw2.label('Shipment Plan CW+2'),
                        tbl_union_all.c.shipment_plan_cw3.label('Shipment Plan CW+3'),
                        tbl_union_all.c.po_needed_cw.label('PO Needed CW'),
                        tbl_union_all.c.po_needed_cw1.label('PO Needed CW+1'),
                        tbl_union_all.c.po_needed_cw2.label('PO Needed CW+2'),
                        tbl_union_all.c.po_needed_cw3.label('PO Needed CW+3'),
                        )\
                .filter(tbl_union_all.c.fiscal_qtr_week_name == fiscal_qtr_week_name)\
                .join(DimFastBusinessSoldtoMapping,
                      func.cast(DimFastBusinessSoldtoMapping.sold_to_id, Integer) == func.cast(tbl_union_all.c.sold_to_id, Integer))\
                .order_by(tbl_union_all.c.rtm, 
                          DimFastBusinessSoldtoMapping.business_type,
                          tbl_union_all.c.sold_to_id,
                          DimFastBusinessSoldtoMapping.abbre,
                          tbl_union_all.c.lob,
                          tbl_union_all.c.sub_lob)
            ret = pd.read_sql_query(q.statement, GcDmpEngine)
            
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret
    
def gen_union_all_table():
    s = GcDmpSession()
    ret = None
    try:
        q1 = s.query(AppFastDemandMultiSummaryWa.fiscal_qtr_week_name.label('fiscal_qtr_week_name'),
                        AppFastDemandMultiSummaryWa.fiscal_week_year.label('fiscal_week_year'),
                        AppFastDemandMultiSummaryWa.rtm.label('rtm'),
                        AppFastDemandMultiSummaryWa.sold_to_id.label('sold_to_id'),
                        AppFastDemandMultiSummaryWa.lob.label('lob'),
                        AppFastDemandMultiSummaryWa.sub_lob.label('sub_lob'),
                        AppFastDemandMultiSummaryWa.fph4.label('fph4'),
                        AppFastDemandMultiSummaryWa.project_code.label('project_code'),
                        AppFastDemandMultiSummaryWa.sku.label('sku'),
                        AppFastDemandMultiSummaryWa.mpn_id.label('mpn_id'),
                        AppFastDemandMultiSummaryWa.demand_cw.label('demand_cw'),
                        AppFastDemandMultiSummaryWa.demand_cw1.label('demand_cw1'),
                        AppFastDemandMultiSummaryWa.demand_cw2.label('demand_cw2'),
                        AppFastDemandMultiSummaryWa.demand_cw3.label('demand_cw3'),
                        AppFastDemandMultiSummaryWa.top_up_demand_cw1.label('top_up_demand_cw1'),
                        AppFastDemandMultiSummaryWa.top_up_demand_cw2.label('top_up_demand_cw2'),
                        AppFastDemandMultiSummaryWa.top_up_demand_cw3.label('top_up_demand_cw3'),
                        AppFastDemandMultiSummaryWa.top_up_demand_cw4.label('top_up_demand_cw4'),
                        AppFastDemandMultiSummaryWa.shipment_plan_cw.label('shipment_plan_cw'),
                        AppFastDemandMultiSummaryWa.shipment_plan_cw1.label('shipment_plan_cw1'),
                        AppFastDemandMultiSummaryWa.shipment_plan_cw2.label('shipment_plan_cw2'),
                        AppFastDemandMultiSummaryWa.shipment_plan_cw3.label('shipment_plan_cw3'),
                        AppFastDemandMultiSummaryWa.gross_billing_units_cw.label('gross_billing_units_cw'),
                        AppFastDemandMultiSummaryWa.sp_remaining_cw1.label('sp_remaining_cw1'),
                        AppFastDemandMultiSummaryWa.sp_remaining_cw2.label('sp_remaining_cw2'),
                        AppFastDemandMultiSummaryWa.po_needed_cw.label('po_needed_cw'),
                        AppFastDemandMultiSummaryWa.po_needed_cw1.label('po_needed_cw1'),
                        AppFastDemandMultiSummaryWa.po_needed_cw2.label('po_needed_cw2'),
                        AppFastDemandMultiSummaryWa.po_needed_cw3.label('po_needed_cw3'))
        q2 = s.query(AppFastDemandCarrierSummaryWa.fiscal_qtr_week_name.label('fiscal_qtr_week_name'),
                        AppFastDemandCarrierSummaryWa.fiscal_week_year.label('fiscal_week_year'),
                        AppFastDemandCarrierSummaryWa.rtm.label('rtm'),
                        AppFastDemandCarrierSummaryWa.sold_to_id.label('sold_to_id'),
                        AppFastDemandCarrierSummaryWa.lob.label('lob'),
                        AppFastDemandCarrierSummaryWa.sub_lob.label('sub_lob'),
                        AppFastDemandCarrierSummaryWa.fph4.label('fph4'),
                        AppFastDemandCarrierSummaryWa.project_code.label('project_code'),
                        AppFastDemandCarrierSummaryWa.sku.label('sku'),
                        AppFastDemandCarrierSummaryWa.mpn_id.label('mpn_id'),
                        AppFastDemandCarrierSummaryWa.demand_cw.label('demand_cw'),
                        AppFastDemandCarrierSummaryWa.demand_cw1.label('demand_cw1'),
                        AppFastDemandCarrierSummaryWa.demand_cw2.label('demand_cw2'),
                        AppFastDemandCarrierSummaryWa.demand_cw3.label('demand_cw3'),
                        AppFastDemandCarrierSummaryWa.top_up_demand_cw1.label('top_up_demand_cw1'),
                        AppFastDemandCarrierSummaryWa.top_up_demand_cw2.label('top_up_demand_cw2'),
                        AppFastDemandCarrierSummaryWa.top_up_demand_cw3.label('top_up_demand_cw3'),
                        AppFastDemandCarrierSummaryWa.top_up_demand_cw4.label('top_up_demand_cw4'),
                        AppFastDemandCarrierSummaryWa.shipment_plan_cw.label('shipment_plan_cw'),
                        AppFastDemandCarrierSummaryWa.shipment_plan_cw1.label('shipment_plan_cw1'),
                        AppFastDemandCarrierSummaryWa.shipment_plan_cw2.label('shipment_plan_cw2'),
                        AppFastDemandCarrierSummaryWa.shipment_plan_cw3.label('shipment_plan_cw3'),
                        AppFastDemandCarrierSummaryWa.gross_billing_units_cw.label('gross_billing_units_cw'),
                        AppFastDemandCarrierSummaryWa.sp_remaining_cw1.label('sp_remaining_cw1'),
                        AppFastDemandCarrierSummaryWa.sp_remaining_cw2.label('sp_remaining_cw2'),
                        AppFastDemandCarrierSummaryWa.po_needed_cw.label('po_needed_cw'),
                        AppFastDemandCarrierSummaryWa.po_needed_cw1.label('po_needed_cw1'),
                        AppFastDemandCarrierSummaryWa.po_needed_cw2.label('po_needed_cw2'),
                        AppFastDemandCarrierSummaryWa.po_needed_cw3.label('po_needed_cw3'))
        q3 = s.query(AppFastDemandOnlineSummaryWa.fiscal_qtr_week_name.label('fiscal_qtr_week_name'),
                        AppFastDemandOnlineSummaryWa.fiscal_week_year.label('fiscal_week_year'),
                        AppFastDemandOnlineSummaryWa.rtm.label('rtm'),
                        AppFastDemandOnlineSummaryWa.sold_to_id.label('sold_to_id'),
                        AppFastDemandOnlineSummaryWa.lob.label('lob'),
                        AppFastDemandOnlineSummaryWa.sub_lob.label('sub_lob'),
                        AppFastDemandOnlineSummaryWa.fph4.label('fph4'),
                        AppFastDemandOnlineSummaryWa.project_code.label('project_code'),
                        AppFastDemandOnlineSummaryWa.sku.label('sku'),
                        AppFastDemandOnlineSummaryWa.mpn_id.label('mpn_id'),
                        AppFastDemandOnlineSummaryWa.demand_cw.label('demand_cw'),
                        AppFastDemandOnlineSummaryWa.demand_cw1.label('demand_cw1'),
                        AppFastDemandOnlineSummaryWa.demand_cw2.label('demand_cw2'),
                        AppFastDemandOnlineSummaryWa.demand_cw3.label('demand_cw3'),
                        AppFastDemandOnlineSummaryWa.top_up_demand_cw1.label('top_up_demand_cw1'),
                        AppFastDemandOnlineSummaryWa.top_up_demand_cw2.label('top_up_demand_cw2'),
                        AppFastDemandOnlineSummaryWa.top_up_demand_cw3.label('top_up_demand_cw3'),
                        AppFastDemandOnlineSummaryWa.top_up_demand_cw4.label('top_up_demand_cw4'),
                        AppFastDemandOnlineSummaryWa.shipment_plan_cw.label('shipment_plan_cw'),
                        AppFastDemandOnlineSummaryWa.shipment_plan_cw1.label('shipment_plan_cw1'),
                        AppFastDemandOnlineSummaryWa.shipment_plan_cw2.label('shipment_plan_cw2'),
                        AppFastDemandOnlineSummaryWa.shipment_plan_cw3.label('shipment_plan_cw3'),
                        AppFastDemandOnlineSummaryWa.gross_billing_units_cw.label('gross_billing_units_cw'),
                        AppFastDemandOnlineSummaryWa.sp_remaining_cw1.label('sp_remaining_cw1'),
                        AppFastDemandOnlineSummaryWa.sp_remaining_cw2.label('sp_remaining_cw2'),
                        AppFastDemandOnlineSummaryWa.po_needed_cw.label('po_needed_cw'),
                        AppFastDemandOnlineSummaryWa.po_needed_cw1.label('po_needed_cw1'),
                        AppFastDemandOnlineSummaryWa.po_needed_cw2.label('po_needed_cw2'),
                        AppFastDemandOnlineSummaryWa.po_needed_cw3.label('po_needed_cw3'))
        q4 = s.query(AppFastDemandMonoSummaryWa.fiscal_qtr_week_name.label('fiscal_qtr_week_name'),
                        AppFastDemandMonoSummaryWa.fiscal_week_year.label('fiscal_week_year'),
                        AppFastDemandMonoSummaryWa.rtm.label('rtm'),
                        AppFastDemandMonoSummaryWa.sold_to_id.label('sold_to_id'),
                        AppFastDemandMonoSummaryWa.lob.label('lob'),
                        AppFastDemandMonoSummaryWa.sub_lob.label('sub_lob'),
                        AppFastDemandMonoSummaryWa.fph4.label('fph4'),
                        AppFastDemandMonoSummaryWa.project_code.label('project_code'),
                        AppFastDemandMonoSummaryWa.sku.label('sku'),
                        AppFastDemandMonoSummaryWa.mpn_id.label('mpn_id'),
                        AppFastDemandMonoSummaryWa.demand_cw.label('demand_cw'),
                        AppFastDemandMonoSummaryWa.demand_cw1.label('demand_cw1'),
                        AppFastDemandMonoSummaryWa.demand_cw2.label('demand_cw2'),
                        AppFastDemandMonoSummaryWa.demand_cw3.label('demand_cw3'),
                        AppFastDemandMonoSummaryWa.top_up_demand_cw1.label('top_up_demand_cw1'),
                        AppFastDemandMonoSummaryWa.top_up_demand_cw2.label('top_up_demand_cw2'),
                        AppFastDemandMonoSummaryWa.top_up_demand_cw3.label('top_up_demand_cw3'),
                        AppFastDemandMonoSummaryWa.top_up_demand_cw4.label('top_up_demand_cw4'),
                        AppFastDemandMonoSummaryWa.shipment_plan_cw.label('shipment_plan_cw'),
                        AppFastDemandMonoSummaryWa.shipment_plan_cw1.label('shipment_plan_cw1'),
                        AppFastDemandMonoSummaryWa.shipment_plan_cw2.label('shipment_plan_cw2'),
                        AppFastDemandMonoSummaryWa.shipment_plan_cw3.label('shipment_plan_cw3'),
                        AppFastDemandMonoSummaryWa.gross_billing_units_cw.label('gross_billing_units_cw'),
                        AppFastDemandMonoSummaryWa.sp_remaining_cw1.label('sp_remaining_cw1'),
                        AppFastDemandMonoSummaryWa.sp_remaining_cw2.label('sp_remaining_cw2'),
                        AppFastDemandMonoSummaryWa.po_needed_cw.label('po_needed_cw'),
                        AppFastDemandMonoSummaryWa.po_needed_cw1.label('po_needed_cw1'),
                        AppFastDemandMonoSummaryWa.po_needed_cw2.label('po_needed_cw2'),
                        AppFastDemandMonoSummaryWa.po_needed_cw3.label('po_needed_cw3'))
        q5 = q1.union_all(q2)
        q6 = q3.union_all(q5)
        ret = q4.union_all(q6).cte()
        
    except Exception as e:
        logger.exception(e)
    finally:
        s.close()
    return ret


def gen_demand_basic_data(data: dict):
    return {
        "top_up_demand_cw1": data['top_up_demand_cw1'],
        "top_up_demand_cw2": data['top_up_demand_cw2'],
        "top_up_demand_cw3": data['top_up_demand_cw3'],
        "top_up_demand_cw4": data['top_up_demand_cw4'],
        "shipment_plan_cw": data['shipment_plan_cw'],
        "shipment_plan_cw1": data['shipment_plan_cw1'],
        "shipment_plan_cw2": data['shipment_plan_cw2'],
        "shipment_plan_cw3": data['shipment_plan_cw3'],
        "po_needed_cw": data['po_needed_cw'],
        "po_needed_cw1": data['po_needed_cw1'],
        "po_needed_cw2": data['po_needed_cw2'],
        "po_needed_cw3": data['po_needed_cw3'],
    }


def query_and_insert_data(table_name: str, fiscal_qtr_week_name: str):
        s = FASTLiteSession()
        s1 = GcDmpSession()
        try:
            if fiscal_qtr_week_name is None:
                return ''
            results = []
            if table_name == 'app_fast_demand_mono_summary_wa':
                mono_data = s1.execute(f'''SELECT fiscal_qtr_week_name,fiscal_week_year,week_begin_dt,week_end_dt,
                                  rtm,sold_to_id,lob,sub_lob,fph4,project_code,sku,mpn_id,demand_cw,demand_cw1,
                                  demand_cw2,demand_cw3,shipment_plan_cw,shipment_plan_cw1,shipment_plan_cw2,
                                  shipment_plan_cw3,gross_billing_units_cw,sp_remaining_cw1,sp_remaining_cw2,
                                  top_up_demand_cw1,top_up_demand_cw2,top_up_demand_cw3, NULL as 'top_up_demand_cw4',
                                  NULL as 'po_needed_cw' ,po_needed_cw1, NULL as 'po_needed_cw2', NULL as 'po_needed_cw3' 
                                  FROM {table_name} WHERE fiscal_qtr_week_name = '{fiscal_qtr_week_name}'
                                ''')
                mono_data_list = mono_data.fetchall()
                results = [dict(item) for item in mono_data_list]
            else:
                new_data = s1.execute(f'''SELECT fiscal_qtr_week_name,fiscal_week_year,week_begin_dt,week_end_dt,
                                    rtm,sold_to_id,lob,sub_lob,fph4,project_code,sku,mpn_id,demand_cw,demand_cw1,
                                    demand_cw2,demand_cw3,shipment_plan_cw,shipment_plan_cw1,shipment_plan_cw2,
                                    shipment_plan_cw3,gross_billing_units_cw,sp_remaining_cw1,sp_remaining_cw2,
                                    top_up_demand_cw1,top_up_demand_cw2,top_up_demand_cw3, NULL as 'top_up_demand_cw4',
                                    po_needed_cw,po_needed_cw1,po_needed_cw2,po_needed_cw3 
                                    FROM {table_name} WHERE fiscal_qtr_week_name = '{fiscal_qtr_week_name}'
                                    ''')
                data_list = new_data.fetchall()
                results = [dict(item) for item in data_list]
            s.execute(FastLiteAppFastDemandSummaryWa.__table__.insert(), results)
            s.commit()
            
            return f"{fiscal_qtr_week_name}: insert new data [ {len(results)} ] to [ {FastLiteAppFastDemandSummaryWa.__tablename__} ] from [ {table_name} ].{DOUBLE_LINE_FEED}"
        except Exception as e:
            logger.exception(str(e))
            return ''
        finally:
            s.close()
            s1.close()
        
def gen_ent_edu_sold_to_data(fiscal_qtr_week_name: str):
    s = FASTLiteSession()
    s1 = GcDmpSession()
    try:
        if fiscal_qtr_week_name is None:
            return ''
        new_data = s1.execute(f'''SELECT rtm, sold_to_id
                                FROM dim_fast_business_soldto_mapping 
                                WHERE rtm in ('ENT', 'EDU')
                            ''')
        data_list = new_data.fetchall()
        sku_data = s1.execute(f'''SELECT lob, model, sku
                                FROM dim_fast_model_sku_list 
                                WHERE model in ('iPhone 14 Pro Max', 'iPhone 14 Pro', 'iPhone 14 Plus', 
                                'iPhone 14', 'iPhone 13', 'iPhone 13 mini', 'iPhone 12', 'iPhone 11', 'iPhone SE (3rd Gen)')
                            ''')
        sku_list = sku_data.fetchall()
        results = [dict(item) for item in data_list]
        fill_data = {
            'fiscal_qtr_week_name': fiscal_qtr_week_name,
            'fiscal_week_year': None, 
            'week_begin_dt': None, 
            'week_end_dt': None,
            'fph4': None, 
            'project_code': None, 
            'mpn_id': None, 
            'demand_cw': None, 
            'demand_cw1': None, 
            'demand_cw2': None, 
            'demand_cw3': None, 
            'shipment_plan_cw': None, 
            'shipment_plan_cw1': None, 
            'shipment_plan_cw2': None, 
            'shipment_plan_cw3': None, 
            'gross_billing_units_cw': None, 
            'sp_remaining_cw1': None, 
            'sp_remaining_cw2': None, 
            'top_up_demand_cw1': None, 
            'top_up_demand_cw2': None, 
            'top_up_demand_cw3': None, 
            'top_up_demand_cw4': None, 
            'po_needed_cw': None, 
            'po_needed_cw1': None, 
            'po_needed_cw2': None, 
            'po_needed_cw3': None
        }
        insert_data = []
        for i in results:
            sold_to_data = {
                'rtm': i['rtm'],
                'sold_to_id': int(i['sold_to_id'])
            }
            for m in sku_list:
                sku_insert = {
                    'lob': m['lob'], 
                    'sub_lob': m['model'], 
                    'sku': m['sku']
                }
                insert_data.append({**sold_to_data, **fill_data, **sku_insert})
        s.execute(FastLiteAppFastDemandSummaryWa.__table__.insert(), insert_data)
        s.commit()
        return f"{fiscal_qtr_week_name}: insert new data [ {len(insert_data)} ] to [ {FastLiteAppFastDemandSummaryWa.__tablename__} ] by [ backend generate ].{DOUBLE_LINE_FEED}"
    except Exception as e:
        logger.exception(str(e))
        return ''
    finally:
        s.close()
        s1.close()


# 生成union之前的字查询, 如果不使用label, 在后续使用中会报错‘没有对应的字段’
def generate_union_sub_query(db_session, clazz, fiscal_qtr_week_name):
    return db_session.query(
            clazz.fiscal_qtr_week_name.label('fiscal_qtr_week_name'),
            clazz.fiscal_week_year.label('fiscal_week_year'),
            clazz.rtm.label('rtm'),
            clazz.sold_to_id.label('sold_to_id'),
            clazz.lob.label('lob'),
            clazz.sub_lob.label('sub_lob'),
            clazz.fph4.label('fph4'),
            clazz.project_code.label('project_code'),
            clazz.sku.label('sku'),
            clazz.mpn_id.label('mpn_id'),
            clazz.demand_cw.label('demand_cw'),
            clazz.demand_cw1.label('demand_cw1'),
            clazz.demand_cw2.label('demand_cw2'),
            clazz.demand_cw3.label('demand_cw3'),
            clazz.top_up_demand_cw1.label('top_up_demand_cw1'),
            clazz.top_up_demand_cw2.label('top_up_demand_cw2'),
            clazz.top_up_demand_cw3.label('top_up_demand_cw3'),
            clazz.top_up_demand_cw4.label('top_up_demand_cw4'),
            clazz.shipment_plan_cw.label('shipment_plan_cw'),
            clazz.shipment_plan_cw1.label('shipment_plan_cw1'),
            clazz.shipment_plan_cw2.label('shipment_plan_cw2'),
            clazz.shipment_plan_cw3.label('shipment_plan_cw3'),
            clazz.gross_billing_units_cw.label('gross_billing_units_cw'),
            clazz.sp_remaining_cw1.label('sp_remaining_cw1'),
            clazz.sp_remaining_cw2.label('sp_remaining_cw2'),
            clazz.po_needed_cw.label('po_needed_cw'),
            clazz.po_needed_cw1.label('po_needed_cw1'),
            clazz.po_needed_cw2.label('po_needed_cw2'),
            clazz.po_needed_cw3.label('po_needed_cw3')
        ).filter(clazz.fiscal_qtr_week_name == fiscal_qtr_week_name)
    
# 组合不同版本下表的union_all逻辑
def get_multi_version_data_by_week(fiscal_qtr_week_name: str, version: int):
    s = GcDmpSession()
    if version == DEMAND_FIRST_VERSION:
        sub_query_multi = generate_union_sub_query(s, AppFastDemandMultiSummaryAmWa, fiscal_qtr_week_name)
        sub_query_online = generate_union_sub_query(s, AppFastDemandOnlineSummaryAmWa, fiscal_qtr_week_name)
        sub_query_carrier = generate_union_sub_query(s, AppFastDemandCarrierSummaryAmWa, fiscal_qtr_week_name)
        sub_query_mono = generate_union_sub_query(s, AppFastDemandMonoSummaryWa, fiscal_qtr_week_name)
        q1 = sub_query_multi.union_all(sub_query_online)
        q2 = sub_query_carrier.union_all(q1)
        union_table = sub_query_mono.union_all(q2).cte()
    elif version == DEMAND_SECOND_VERSION:
        sub_query_multi_afternoon = generate_union_sub_query(s, AppFastDemandMultiSummaryWa, fiscal_qtr_week_name)
        sub_query_online_afternoon = generate_union_sub_query(s, AppFastDemandOnlineSummaryWa, fiscal_qtr_week_name)
        sub_query_carrier_afternoon = generate_union_sub_query(s, AppFastDemandCarrierSummaryWa, fiscal_qtr_week_name)
        sub_query_mono_monday = generate_union_sub_query(s, AppFastAdvanceDemandMonoSummaryMondayWa, fiscal_qtr_week_name)
        q1 = sub_query_multi_afternoon.union_all(sub_query_online_afternoon)
        q2 = sub_query_carrier_afternoon.union_all(q1)
        union_table = sub_query_mono_monday.union_all(q2).cte()
    elif version == DEMAND_THIRD_VERSION:
        sub_query_multi_afternoon = generate_union_sub_query(s, AppFastDemandMultiSummaryWa, fiscal_qtr_week_name)
        sub_query_online_afternoon = generate_union_sub_query(s, AppFastDemandOnlineSummaryWa, fiscal_qtr_week_name)
        sub_query_carrier_afternoon = generate_union_sub_query(s, AppFastDemandCarrierSummaryWa, fiscal_qtr_week_name)
        sub_query_mono_tuesday = generate_union_sub_query(s, AppFastAdvanceDemandMonoSummaryTuesdayWa, fiscal_qtr_week_name)
        q1 = sub_query_multi_afternoon.union_all(sub_query_online_afternoon)
        q2 = sub_query_carrier_afternoon.union_all(q1)
        union_table = sub_query_mono_tuesday.union_all(q2).cte()
    return s, union_table

class AppFastDemandBasic():
    
    fiscal_qtr_week_name = Column(String(256), primary_key=True, comment='FY23Q1W12')
    fiscal_week_year = Column(Integer, comment='')
    week_begin_dt = Column(Date, comment='')
    week_end_dt = Column(Date, comment='')
    rtm = Column(String(256), comment='')
    sold_to_id = Column(Integer, comment='')
    lob = Column(String(256), comment='')
    sub_lob = Column(String(256), comment='')
    fph4 = Column(String(256), comment='')
    project_code = Column(String(256), comment='')
    sku = Column(String(256), comment='')
    mpn_id = Column(String(256), comment='')
    demand_cw = Column(Float, comment='')
    demand_cw1 = Column(Float, comment='')
    demand_cw2 = Column(Float, comment='')
    demand_cw3 = Column(Float, comment='')
    shipment_plan_cw = Column(Float, comment='')
    shipment_plan_cw1 = Column(Float, comment='')
    shipment_plan_cw2 = Column(Float, comment='')
    shipment_plan_cw3 = Column(Float, comment='')
    gross_billing_units_cw = Column(Float, comment='')
    sp_remaining_cw1 = Column(Float, comment='')
    sp_remaining_cw2 = Column(Float, comment='')
    top_up_demand_cw1 = Column(Float, comment='')
    top_up_demand_cw2 = Column(Float, comment='')
    top_up_demand_cw3 = Column(Float, comment='')
    top_up_demand_cw4 = Column(Float, comment='')
    po_needed_cw = Column(Float, comment='')
    po_needed_cw1 = Column(Float, comment='')
    po_needed_cw2 = Column(Float, comment='')
    po_needed_cw3 = Column(Float, comment='')
    

class AppFastDemandMultiSummaryAmWa(AppFastDemandBasic, GcDmpBase):
    __tablename__ = 'app_fast_demand_multi_summary_am_wa'
    __table_args__ = {'schema': 'gc_dmp_fast'}
    

class AppFastDemandOnlineSummaryAmWa(AppFastDemandBasic, GcDmpBase):
    __tablename__ = 'app_fast_demand_online_summary_am_wa'
    __table_args__ = {'schema': 'gc_dmp_fast'}
    
  
class AppFastDemandCarrierSummaryAmWa(AppFastDemandBasic, GcDmpBase):
    __tablename__ = 'app_fast_demand_carrier_summary_am_wa'
    __table_args__ = {'schema': 'gc_dmp_fast'}
    
    
class AppFastAdvanceDemandMonoSummaryMondayWa(AppFastDemandBasic, GcDmpBase):
    __tablename__ = 'app_fast_advance_demand_mono_summary_monday_wa'
    __table_args__ = {'schema': 'gc_dmp_fast'}
    
    @classmethod
    def get_version_2_by_week(cls, fiscal_qtr_week_name: str) -> int:
        s = GcDmpSession()
        count = 0
        try:
            count = s.query(func.count(cls.fiscal_qtr_week_name).label('count'))\
                .filter(cls.fiscal_qtr_week_name == fiscal_qtr_week_name)\
                .scalar()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return count
    
class AppFastAdvanceDemandMonoSummaryTuesdayWa(AppFastDemandBasic, GcDmpBase):
    __tablename__ = 'app_fast_advance_demand_mono_summary_tuesday_wa'
    __table_args__ = {'schema': 'gc_dmp_fast'}
    
    @classmethod
    def get_version_3_by_week(cls, fiscal_qtr_week_name: str) -> int:
        s = GcDmpSession()
        count = 0
        try:
            count = s.query(func.count(cls.fiscal_qtr_week_name).label('count'))\
                .filter(cls.fiscal_qtr_week_name == fiscal_qtr_week_name)\
                .scalar()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return count
    