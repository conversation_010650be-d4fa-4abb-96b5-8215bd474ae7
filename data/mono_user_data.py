from domain.usage.entity.user_info import UserInfo
from util.const import <PERSON>rror<PERSON>xcept, ErrCode
from util.fast_lite_base import *


class FastMonoUser(FASTMonoBase):
    __tablename__ = "fast_user"
    __table_args__ = {"extend_existing": True}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    rtm = Column(String(256), comment='')
    reseller_id = Column(String(256), comment='')
    prs_id = Column(String(256), comment='')
    version = Column(Integer, comment='')

    @classmethod
    def find_by_prs_id(cls, prs_id):
        s = FASTMonoSession()
        user = []
        try:
            user = s.query(cls).filter(cls.prs_id == prs_id).all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        if len(user) == 0:
            return None
        return user[0]

    @classmethod
    def get_all_user_list(cls) -> list[UserInfo]:
        s = FASTMonoSession()
        ret = []
        try:
            query_result = s.query(cls).all()
            for item in query_result:
                ret.append(UserInfo(
                    person_id=item.prs_id,
                    rtm=item.rtm,
                    is_key_user=1
                ))
        except Exception as e:
            logger.exception(e)
            raise ErrorExcept(ErrCode.DBQueryError, "db query error: " + str(e))
        finally:
            s.close()
        return ret