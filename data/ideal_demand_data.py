import pandas as pd


from util.const import *
from util.gc_dmp_fast_write_base import *
from util.fast_lite_base import *
from util.gc_dmp_algo_base import *
from util.gc_dmp_base import *


class FastLiteIdealDemandRTMForecastUpload(GcDmpFastWriteBase):
    __tablename__ = f"fast_lite_ideal_demand_rtm_forecast_upload{'_test' if env_dev() else ''}"
    __table_args__ = {'schema': 'gc_dmp_fast_write'}
    
    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    rtm = Column(String(32), comment='')
    fiscal_week_year = Column(Integer, comment='')
    week_date = Column(String(16), comment='FY23Q1W12')
    sold_to_id = Column(Integer, comment='customer sold to id')
    sold_to_name = Column(String(256), comment='customer sold to name')
    lob = Column(String(64), comment='')
    model = Column(String(128), comment='')
    mpn = Column(String(128), comment='')
    forecast_cw = Column(Integer, comment='')
    forecast_cw1 = Column(Integer, comment='')
    forecast_cw2 = Column(Integer, comment='')
    forecast_cw3 = Column(Integer, comment='')
    forecast_cw4 = Column(Integer, comment='')
    forecast_cw5 = Column(Integer, comment='')
    forecast_cw6 = Column(Integer, comment='')
    forecast_cw7 = Column(Integer, comment='')
    forecast_cw8 = Column(Integer, comment='')
    forecast_cw9 = Column(Integer, comment='')
    forecast_type = Column(String(16), comment='')
    
    create_time = Column(DateTime, server_default=text('CURRENT_TIMESTAMP'))
    update_time = Column(DateTime, server_default=text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'))

    @classmethod
    def bulk_save(cls, objs: list):
        s = GcDmpFastWriteSession()
        try:
            s.execute(cls.__table__.insert(), objs)
            s.commit()
        except Exception as e:
            logger.exception(str(e))
            raise ErrorExcept(ErrCode.DBInsert, "insert to db failed" + str(e))
        finally:
            s.close()
        return True

    @classmethod
    def delete_by_rtm_week(cls, rtm: str, fiscal_week_year: int):
        s = GcDmpFastWriteSession()
        try:
            s.query(cls)\
                .filter(cls.rtm == rtm)\
                .filter(cls.fiscal_week_year == fiscal_week_year)\
                .delete()
            s.commit()
        except Exception as e:
            logger.exception(str(e))
        finally:
            s.close()
        return True

    @classmethod
    def query_by_rtm_week(cls, rtm: str, week_date: str):
        s = GcDmpFastWriteSession()
        ret = pd.DataFrame()
        try:
            q = s.query(cls)\
                .filter(cls.rtm == rtm)\
                .filter(cls.week_date == week_date)
            ret = pd.read_sql_query(q.statement, GcDmpFastWriteEngine)
        except Exception as e:
            logger.exception(str(e))
        finally:
            s.close()
        return ret

    @classmethod
    def count_by_rtm_week(cls, rtm: str, week_date: str):
        s = GcDmpFastWriteSession()
        ret = 0
        try:
            ret = s.query(func.count(cls.id))\
                .filter(cls.rtm == rtm)\
                .filter(cls.week_date == week_date)\
                .scalar()
        except Exception as e:
            logger.exception(str(e))
        finally:
            s.close()
        return ret

    @classmethod
    def query_by_week(cls, week_date: str):
        s = GcDmpFastWriteSession()
        ret = pd.DataFrame()
        try:
            q = s.query(cls)\
                .filter(cls.week_date == week_date)
            ret = pd.read_sql_query(q.statement, GcDmpFastWriteEngine)
        except Exception as e:
            logger.exception(str(e))
        finally:
            s.close()
        return ret

    @classmethod
    def query_by_week_and_lob(cls, week_date: str, lob: str, rtms: list[str]):
        s = GcDmpFastWriteSession()
        ret = pd.DataFrame()
        try:
            q = s.query(cls.sold_to_id, cls.mpn, cls.forecast_cw, cls.forecast_cw1,
                        cls.forecast_cw2, cls.forecast_cw3, cls.forecast_cw4, cls.forecast_cw5,
                        cls.forecast_cw6, cls.forecast_cw7, cls.forecast_cw8, cls.forecast_cw9) \
                .filter(cls.week_date == week_date) \
                .filter(cls.lob == lob) \
                .filter(cls.rtm.in_(rtms))
            ret = pd.read_sql_query(q.statement, GcDmpFastWriteEngine)
        except Exception as e:
            logger.exception(str(e))
        finally:
            s.close()
        return ret


class FastLiteIdealDemandFinalForecastRTM(GcDmpFastWriteBase):
    __tablename__ = f"fast_lite_ideal_demand_final_forecast_rtm{'_test' if env_dev() else ''}"
    __table_args__ = {'schema': 'gc_dmp_fast_write'}
    
    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    rtm = Column(String(32), comment='')
    fiscal_week_year = Column(Integer, comment='')
    week_date = Column(String(16), comment='FY23Q1W12')
    business_type = Column(String(128), comment='')
    sold_to_id = Column(Integer, comment='customer sold to id')
    sold_to_name = Column(String(256), comment='customer sold to name')
    lob = Column(String(64), comment='')
    model = Column(String(128), comment='')
    fph4 = Column(String(128), comment='')
    project_code = Column(String(128), comment='')
    sku = Column(String(128), comment='')
    mpn = Column(String(128), comment='')
    forecast_cw = Column(Integer, comment='')
    forecast_cw1 = Column(Integer, comment='')
    forecast_cw2 = Column(Integer, comment='')
    forecast_cw3 = Column(Integer, comment='')
    forecast_cw4 = Column(Integer, comment='')
    forecast_cw5 = Column(Integer, comment='')
    forecast_cw6 = Column(Integer, comment='')
    forecast_cw7 = Column(Integer, comment='')
    forecast_cw8 = Column(Integer, comment='')
    forecast_cw9 = Column(Integer, comment='')
    
    create_time = Column(DateTime, server_default=text('CURRENT_TIMESTAMP'))
    update_time = Column(DateTime, server_default=text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'))

    @classmethod
    def bulk_save(cls, objs: list):
        s = GcDmpFastWriteSession()
        try:
            s.execute(cls.__table__.insert(), objs)
            s.commit()
        except Exception as e:
            logger.exception(str(e))
            raise ErrorExcept(ErrCode.DBInsert, "insert to db failed" + str(e))
        finally:
            s.close()
        return True

    @classmethod
    def count_by_rtm_week(cls, rtm: str, week_date: str):
        s = GcDmpFastWriteSession()
        ret = 0
        try:
            ret = s.query(func.count(cls.id))\
                .filter(cls.rtm == rtm)\
                .filter(cls.week_date == week_date)\
                .scalar()
        except Exception as e:
            logger.exception(str(e))
        finally:
            s.close()
        return ret

    @classmethod
    def query_by_rtm_week(cls, rtm: str, week_date: str):
        s = GcDmpFastWriteSession()
        ret = pd.DataFrame()
        try:
            q = s.query(cls)\
                .filter(cls.rtm == rtm)\
                .filter(cls.week_date == week_date)
            ret = pd.read_sql_query(q.statement, GcDmpFastWriteEngine)
        except Exception as e:
            logger.exception(str(e))
        finally:
            s.close()
        return ret

    @classmethod
    def delete_by_week(cls, fiscal_week_year: int):
        s = GcDmpFastWriteSession()
        try:
            s.query(cls)\
                .filter(cls.fiscal_week_year == fiscal_week_year)\
                .delete()
            s.commit()
        except Exception as e:
            logger.exception(str(e))
        finally:
            s.close()
        return True

    @classmethod
    def query_by_week(cls, week_date: str):
        s = GcDmpFastWriteSession()
        ret = pd.DataFrame()
        try:
            q = s.query(cls)\
                .filter(cls.week_date == week_date)
            ret = pd.read_sql_query(q.statement, GcDmpFastWriteEngine)
        except Exception as e:
            logger.exception(str(e))
        finally:
            s.close()
        return ret


class FastLiteIdealDemandTWOSAdjustmentX(GcDmpFastWriteBase):
    __tablename__ = f"fast_lite_ideal_demand_twos_adjustment_x{'_test' if env_dev() else ''}"
    __table_args__ = {'schema': 'gc_dmp_fast_write'}
    
    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    fiscal_week_year = Column(Integer, comment='')
    week_date = Column(String(16), comment='FY23Q1W12')
    model = Column(String(128), comment='')
    rtm = Column(String(32), comment='')
    business_type = Column(String(128), comment='')
    sold_to_id = Column(Integer, comment='customer sold to id')
    sold_to_name = Column(String(256), comment='customer sold to name')
    x_in_cw1 = Column(Numeric(18,1), comment='')
    x_in_cw2 = Column(Numeric(18,1), comment='')
    x_in_cw3 = Column(Numeric(18,1), comment='')
    x_in_cw4 = Column(Numeric(18,1), comment='')
    forecast_type = Column(String(16), comment='')
    
    create_time = Column(DateTime, server_default=text('CURRENT_TIMESTAMP'))
    update_time = Column(DateTime, server_default=text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'))

    @classmethod
    def bulk_save(cls, objs: list):
        s = GcDmpFastWriteSession()
        try:
            s.execute(cls.__table__.insert(), objs)
            s.commit()
        except Exception as e:
            logger.exception(str(e))
            raise ErrorExcept(ErrCode.DBInsert, "insert to db failed" + str(e))
        finally:
            s.close()
        return True
    
    @classmethod
    def count_by_week(cls, week_date: str):
        s = GcDmpFastWriteSession()
        ret = 0
        try:
            ret = s.query(func.count(cls.id))\
                .filter(cls.week_date == week_date)\
                .scalar()
        except Exception as e:
            logger.exception(str(e))
        finally:
            s.close()
        return ret

    @classmethod
    def delete_by_week(cls, fiscal_week_year: int):
        s = GcDmpFastWriteSession()
        try:
            s.query(cls)\
                .filter(cls.fiscal_week_year == fiscal_week_year)\
                .delete()
            s.commit()
        except Exception as e:
            logger.exception(str(e))
        finally:
            s.close()
        return True

    @classmethod
    def query_by_week(cls, week_date: str):
        s = GcDmpFastWriteSession()
        ret = pd.DataFrame()
        try:
            q = s.query(cls)\
                .filter(cls.week_date == week_date)
            ret = pd.read_sql_query(q.statement, GcDmpFastWriteEngine)
        except Exception as e:
            logger.exception(str(e))
        finally:
            s.close()
        return ret


class FastLiteIdealDemandTWOSAdjustmentY(GcDmpFastWriteBase):
    __tablename__ = f"fast_lite_ideal_demand_twos_adjustment_y{'_test' if env_dev() else ''}"
    __table_args__ = {'schema': 'gc_dmp_fast_write'}
    
    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    fiscal_week_year = Column(Integer, comment='')
    week_date = Column(String(16), comment='FY23Q1W12')
    model = Column(String(128), comment='')
    rtm = Column(String(32), comment='')
    business_type = Column(String(128), comment='')
    y_in_cw1 = Column(Numeric(18,1), comment='')
    y_in_cw2 = Column(Numeric(18,1), comment='')
    y_in_cw3 = Column(Numeric(18,1), comment='')
    y_in_cw4 = Column(Numeric(18,1), comment='')
    forecast_type = Column(String(16), comment='')
    
    create_time = Column(DateTime, server_default=text('CURRENT_TIMESTAMP'))
    update_time = Column(DateTime, server_default=text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'))

    @classmethod
    def bulk_save(cls, objs: list):
        s = GcDmpFastWriteSession()
        try:
            s.execute(cls.__table__.insert(), objs)
            s.commit()
        except Exception as e:
            logger.exception(str(e))
            raise ErrorExcept(ErrCode.DBInsert, "insert to db failed" + str(e))
        finally:
            s.close()
        return True

    @classmethod
    def count_by_week(cls, week_date: str):
        s = GcDmpFastWriteSession()
        ret = 0
        try:
            ret = s.query(func.count(cls.id))\
                .filter(cls.week_date == week_date)\
                .scalar()
        except Exception as e:
            logger.exception(str(e))
        finally:
            s.close()
        return ret

    @classmethod
    def delete_by_week(cls, fiscal_week_year: int):
        s = GcDmpFastWriteSession()
        try:
            s.query(cls)\
                .filter(cls.fiscal_week_year == fiscal_week_year)\
                .delete()
            s.commit()
        except Exception as e:
            logger.exception(str(e))
        finally:
            s.close()
        return True

    @classmethod
    def query_by_week(cls, week_date: str):
        s = GcDmpFastWriteSession()
        ret = pd.DataFrame()
        try:
            q = s.query(cls)\
                .filter(cls.week_date == week_date)
            ret = pd.read_sql_query(q.statement, GcDmpFastWriteEngine)
        except Exception as e:
            logger.exception(str(e))
        finally:
            s.close()
        return ret


class FastLiteIdealDemandRTMWeekRecord(FASTLiteBase):
    __tablename__ = 'fast_lite_ideal_demand_rtm_week_record'
    __table_args__ = {'schema': 'fast_lite'}
    
    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    rtm = Column(String(32), comment='')
    fiscal_week_year = Column(Integer, comment='')
    week_date = Column(String(16), comment='FY23Q1W12')
    
    create_time = Column(DateTime, server_default=text('CURRENT_TIMESTAMP'))
    update_time = Column(DateTime, server_default=text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'))

    @classmethod
    def bulk_save(cls, objs: list):
        s = FASTLiteSession()
        try:
            s.execute(cls.__table__.insert(), objs)
            s.commit()
        except Exception as e:
            logger.exception(str(e))
            raise ErrorExcept(ErrCode.DBInsert, "insert to db failed" + str(e))
        finally:
            s.close()
        return True

    @classmethod
    def get_list_by_rtm(cls, rtm, limit, offset):
        s = FASTLiteSession()
        ret = []
        total = 0
        try:
            query = s.query(cls) \
                .filter(cls.rtm == rtm)
            total = query.count()
            ret = query.order_by(desc(cls.fiscal_week_year)) \
                .limit(limit) \
                .offset(offset) \
                .all()
        except Exception as e:
            logger.exception(str(e))
        finally:
            s.close()
        return ret, total

    @classmethod
    def get_detail_by_rtm_week(cls, rtm, week_date):
        s = FASTLiteSession()
        ret = []
        try:
            ret = s.query(cls) \
                .filter(cls.rtm == rtm) \
                .filter(cls.week_date == week_date) \
                .all()
        except Exception as e:
            logger.exception(str(e))
        finally:
            s.close()
        return ret


class FastLiteIdealDemandCPFWeekRecord(FASTLiteBase):
    __tablename__ = 'fast_lite_ideal_demand_cpf_week_record'
    __table_args__ = {'schema': 'fast_lite'}
    
    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    fiscal_week_year = Column(Integer, comment='')
    week_date = Column(String(16), comment='FY23Q1W12')
    
    create_time = Column(DateTime, server_default=text('CURRENT_TIMESTAMP'))
    update_time = Column(DateTime, server_default=text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'))

    @classmethod
    def bulk_save(cls, objs: list):
        s = FASTLiteSession()
        try:
            s.execute(cls.__table__.insert(), objs)
            s.commit()
        except Exception as e:
            logger.exception(str(e))
            raise ErrorExcept(ErrCode.DBInsert, "insert to db failed" + str(e))
        finally:
            s.close()
        return True
    
    @classmethod
    def get_list(cls, limit, offset):
        s = FASTLiteSession()
        ret = []
        total = 0
        try:
            query = s.query(cls)
            total = query.count()
            ret = query.order_by(desc(cls.fiscal_week_year)) \
                .limit(limit) \
                .offset(offset) \
                .all()
        except Exception as e:
            logger.exception(str(e))
        finally:
            s.close()
        return ret, total


    @classmethod
    def get_detail_by_week(cls, week_date):
        s = FASTLiteSession()
        ret = []
        try:
            ret = s.query(cls) \
                .filter(cls.week_date == week_date) \
                .all()
        except Exception as e:
            logger.exception(str(e))
        finally:
            s.close()
        return ret


class FastLiteIdealDemandDeadlineConfig(FASTLiteBase):
    __tablename__ = 'fast_lite_ideal_demand_deadline_config'
    __table_args__ = {'schema': 'fast_lite'}
    
    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    name = Column(String(128), comment='config name')
    week = Column(Integer, comment='1-7, monday-sunday')
    time = Column(String(16), comment='12:00:00')
    is_apply_history = Column(SmallInteger, server_default='0', comment='0: not apply to history data; 1: apply to history data')
    is_active = Column(SmallInteger, server_default='1', comment='0: 未启用; 1: 启用')
    remarks = Column(String(256), comment='')
    
    create_time = Column(DateTime, server_default=text('CURRENT_TIMESTAMP'))
    update_time = Column(DateTime, server_default=text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'))

    @classmethod
    def query_config_by_name(cls, name: str):
        s = FASTLiteSession()
        ret = []
        try:
            ret = s.query(cls) \
                    .filter(cls.name == name) \
                    .filter(cls.is_active == 1) \
                    .all()
        except Exception as e:
            logger.exception(str(e))
        finally:
            s.close()
        return ret


class FastLiteIdealDemandFile(FASTLiteBase):
    __tablename__ = "fast_lite_ideal_demand_file"
    __table_args__ = {"extend_existing": True}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    week_date = Column(String(16), comment='FY23Q1W12')
    fiscal_week_year = Column(Integer, comment='202312')
    rtm = Column(String(64))
    lob = Column(String(64))
    file_name = Column(String(256))
    file_path = Column(String(256))
    file_version = Column(Integer)
    uploader = Column(String(64), comment='Nick Name + Last Name')
    uploader_email = Column(String(256), comment='xxx.apple.com')
    category = Column(Integer)
    upload_date = Column(Date)
    
    create_time = Column(DateTime, server_default=text('CURRENT_TIMESTAMP'))
    update_time = Column(DateTime, server_default=text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'))

    @classmethod
    def get_file_list_by_rtm_week(cls, rtm, week_date):
        s = FASTLiteSession()
        ret = []
        try:
            ret = s.query(cls) \
                .filter(cls.rtm == rtm) \
                .filter(cls.week_date == week_date) \
                .all()
        except Exception as e:
            logger.exception(str(e))
        finally:
            s.close()
        return ret


class ForecatingIdealDemandDtl(GcDmpAlgoBase):
    __tablename__ = f"forecasting_ideal_demand_dtl{'_test' if env_dev() else ''}"
    __table_args__ = {'schema': 'gc_dmp_algo'}
    
    fiscal_week_year = Column(Integer, primary_key=True)
    week_date = Column(String(16))
    rtm = Column(String(32))
    business_type = Column(String(128))
    sold_to_id = Column(Integer)
    sold_to_name = Column(String(256))
    fph1 = Column(String(64))
    fph3 = Column(String(128))
    fph4 = Column(String(128))
    sku = Column(String(128))
    mpn = Column(String(128))
    inv_eoh = Column(Integer)
    demand_cw = Column(Integer)
    demand_cw1 = Column(Integer)
    demand_cw2 = Column(Integer)
    demand_cw3 = Column(Integer)
    demand_cw4 = Column(Integer)
    create_time = Column(DateTime)

    @classmethod
    def query_by_rtm_week(cls, rtm: str, week_date: str):
        s = GcDmpAlgoSession()
        ret = pd.DataFrame()
        try:
            q = s.query(cls)\
                .filter(cls.rtm == rtm)\
                .filter(cls.week_date == week_date)
            ret = pd.read_sql_query(q.statement, GcDmpAlgoEngine)
        except Exception as e:
            logger.exception(str(e))
        finally:
            s.close()
        return ret

    @classmethod
    def count_by_rtm_week(cls, rtm: str, week_date: str):
        s = GcDmpAlgoSession()
        ret = 0
        try:
            ret = s.query(func.count(cls.week_date))\
                .filter(cls.rtm == rtm)\
                .filter(cls.week_date == week_date)\
                .scalar()
        except Exception as e:
            logger.exception(str(e))
        finally:
            s.close()
        return ret

    @classmethod
    def query_by_week_order_rtm(cls, week_date: str, order_rtm: list):
        s = GcDmpAlgoSession()
        ret = pd.DataFrame()
        try:
            q = s.query(cls)\
                .filter(cls.week_date == week_date)\
                .order_by(func.field(cls.rtm, *order_rtm))
            ret = pd.read_sql_query(q.statement, GcDmpAlgoEngine)
        except Exception as e:
            logger.exception(str(e))
        finally:
            s.close()
        return ret


class BaseForecastingResultDtl:
    __table_args__ = {'schema': 'gc_dmp_algo'}
    
    cust_id = Column(Integer, primary_key=True)
    disti_id = Column(Integer)
    apple_hq_id = Column(Integer)
    apple_id = Column(Integer)
    rtm = Column(String(64), comment='')
    rtm_level_4 = Column(String(64), comment='')
    measure_cd = Column(String(64), comment='')
    fcst_hier_level = Column(String(64), comment='')
    fcst_prod_level = Column(String(64), comment='')
    prod_id = Column(String(256), comment='')
    sku = Column(String(256), comment='')
    fph4 = Column(String(256), comment='')
    fph3 = Column(String(256), comment='')
    fph1 = Column(String(256), comment='')
    prediction_cw = Column(Float, comment='')
    prediction_cw1 = Column(Float, comment='')
    prediction_cw2 = Column(Float, comment='')
    prediction_cw3 = Column(Float, comment='')
    prediction_cw4 = Column(Float, comment='')
    prediction_cw5 = Column(Float, comment='')
    prediction_cw6 = Column(Float, comment='')
    prediction_cw7 = Column(Float, comment='')
    prediction_cw8 = Column(Float, comment='')
    prediction_cw9 = Column(Float, comment='')
    prediction_cw10 = Column(Float, comment='')
    prediction_cw11 = Column(Float, comment='')
    prediction_cw12 = Column(Float, comment='')
    is_high_runner = Column(String(64), comment='')
    inference_type = Column(String(64), comment='')
    query_date = Column(String(64), comment='')
    week_begin_dt = Column(String(64), comment='')
    week_date = Column(String(64), comment='FY23Q1W12')
    create_time = Column(String(64), comment='')

    @classmethod
    def query_fcst(cls, rtm, week_date):
        ret = pd.DataFrame()
        try:
            sql_statement = f'''
                                     SELECT '{rtm}' AS rtm,
                                     cust_id AS sold_to_id,
                                     prod_id AS mpn,
                                     fph3 AS fph3,
                                     prediction_cw AS forecast_cw,
                                     prediction_cw1 AS forecast_cw1 
                                     FROM {cls.__tablename__}
                                     WHERE week_date='{week_date}'
                                     AND create_time=(SELECT max(create_time) FROM {cls.__tablename__} WHERE week_date='{week_date}')
                                     '''
            # data = s.execute(sql_statement)
            # ret = data.fetchall()
            ret = pd.read_sql_query(sql_statement, GcDmpAlgoEngine)
            return ret
        except ErrorExcept as e:
            logger.error(f"query error, {e}")
        return ret


    @classmethod
    def query_forecast_by_rtm_week_latest(cls, rtm, week_date):
        ret = pd.DataFrame()
        try:
            sql_statement = f'''
                             SELECT '{rtm}' AS rtm,
                             cust_id AS sold_to_id,
                             prod_id AS mpn,
                             prediction_cw AS forecast_cw,
                             prediction_cw1 AS forecast_cw1 
                             FROM {cls.__tablename__}
                             WHERE week_date='{week_date}'
                             AND create_time=(SELECT max(create_time) FROM {cls.__tablename__} WHERE week_date='{week_date}')
                             '''
            # data = s.execute(sql_statement)
            # ret = data.fetchall()
            ret = pd.read_sql_query(sql_statement, GcDmpAlgoEngine)
            return ret
        except ErrorExcept as e:
            logger.error(f"query error, {e}")
        return ret

    @classmethod
    def query_all_forecast_by_rtm_week_latest(cls, rtm, week_date):
        ret = pd.DataFrame()
        try:
            sql_statement = f'''
                             SELECT '{rtm}' AS rtm,
                             cust_id AS sold_to_id,
                             prod_id AS mpn,
                             prediction_cw AS forecast_cw,
                             prediction_cw1 AS forecast_cw1,
                             prediction_cw2 AS forecast_cw2,
                             prediction_cw3 AS forecast_cw3,
                             prediction_cw4 AS forecast_cw4,
                             prediction_cw5 AS forecast_cw5,
                             prediction_cw6 AS forecast_cw6,
                             prediction_cw7 AS forecast_cw7,
                             prediction_cw8 AS forecast_cw8,
                             prediction_cw9 AS forecast_cw9
                             FROM {cls.__tablename__}
                             WHERE week_date='{week_date}'
                             AND create_time=(SELECT max(create_time) FROM {cls.__tablename__} WHERE week_date='{week_date}')
                             '''
            ret = pd.read_sql_query(sql_statement, GcDmpAlgoEngine)
            return ret
        except ErrorExcept as e:
            logger.error(f"query error, {e}")
        return ret


    @classmethod
    def query_all_forecast_by_week_and_lob(cls, rtm, week_date, lob):
        ret = pd.DataFrame()
        try:
            sql_statement = f'''
                             SELECT '{rtm}' AS rtm,
                             cust_id AS sold_to_id,
                             prod_id AS mpn,
                             prediction_cw AS forecast_cw,
                             prediction_cw1 AS forecast_cw1,
                             prediction_cw2 AS forecast_cw2,
                             prediction_cw3 AS forecast_cw3,
                             prediction_cw4 AS forecast_cw4,
                             prediction_cw5 AS forecast_cw5,
                             prediction_cw6 AS forecast_cw6,
                             prediction_cw7 AS forecast_cw7,
                             prediction_cw8 AS forecast_cw8,
                             prediction_cw9 AS forecast_cw9
                             FROM {cls.__tablename__}
                             WHERE week_date='{week_date}'
                             AND fph1 = '{lob}'
                             AND create_time=(SELECT max(create_time) FROM {cls.__tablename__} WHERE week_date='{week_date}')
                             '''
            ret = pd.read_sql_query(sql_statement, GcDmpAlgoEngine)
            return ret
        except ErrorExcept as e:
            logger.error(f"query error, {e}")
        return ret


class MonoForecastingResultDtl(GcDmpAlgoBase, BaseForecastingResultDtl):
    __tablename__ = 'mono_forecasting_result_dtl'


class MultiForecastingResultDtl(GcDmpAlgoBase, BaseForecastingResultDtl):
    __tablename__ = 'multi_forecasting_result_dtl'


class CarrierForecastingResultDtl(GcDmpAlgoBase, BaseForecastingResultDtl):
    __tablename__ = 'carrier_forecasting_result_dtl'



class AppFastIdealDemandRTMEOHWiBase:
    __table_args__ = {'schema': 'gc_dmp_fast'}
    
    id = Column(Integer, primary_key=True)
    fiscal_year = Column(Integer)
    fiscal_quarter = Column(String(255))
    fiscal_qtr_week_name = Column(String(255))
    fiscal_week_year = Column(Integer)
    rtm = Column(String(255))
    business_type = Column(String(255))
    sold_to_id = Column(String(255))
    sold_to_name_en = Column(String(255))
    mpn = Column(String(255))
    lob = Column(String(255))
    model = Column(String(255))
    nand = Column(String(255))
    color = Column(String(255))
    project_code = Column(String(255))
    so_eoh = Column(Integer)
    create_time = Column(DateTime)
    update_time = Column(DateTime)

    @classmethod
    def count_by_lob_week(cls, fiscal_week_year: int, lob: str):
        s = GcDmpSession()
        ret = 0
        try:
            ret = s.query(func.count(cls.id))\
                .filter(cls.lob == lob)\
                .filter(cls.fiscal_week_year == fiscal_week_year)\
                .scalar()
        except Exception as e:
            logger.exception(str(e))
        finally:
            s.close()
        return ret

    @classmethod
    def query_by_lob_week(cls, fiscal_week_year: int, lob: str):
        s = GcDmpSession()
        ret = pd.DataFrame()
        try:
            q = s.query(cls)\
                .filter(cls.lob == lob)\
                .filter(cls.fiscal_week_year == fiscal_week_year)
            ret = pd.read_sql_query(q.statement, GcDmpEngine)
        except Exception as e:
            logger.exception(str(e))
        finally:
            s.close()
        return ret


class AppFastIdealDemandMonoSoEohWi(GcDmpBase, AppFastIdealDemandRTMEOHWiBase):
    __tablename__ = f"app_fast_ideal_demand_mono_so_eoh_wi{'_test' if env_dev() else ''}"


class AppFastIdealDemandOnlineSoEohWi(GcDmpBase, AppFastIdealDemandRTMEOHWiBase):
    __tablename__ = f"app_fast_ideal_demand_gdv_online_eoh_wi{'_test' if env_dev() else ''}"


class AppFastIdealDemandTemplateWi(GcDmpBase):
    __tablename__ = f"app_fast_ideal_demand_template_wi{'_test' if env_dev() else ''}"
    __table_args__ = {'schema': 'gc_dmp_fast'}
    
    id = Column(Integer, primary_key=True)
    fiscal_year = Column(Integer)
    fiscal_quarter = Column(String(255))
    fiscal_qtr_week_name = Column(String(255))
    fiscal_week_year = Column(Integer)
    rtm = Column(String(255))
    business_type = Column(String(255))
    sold_to_id = Column(String(255))
    sold_to_name_en = Column(String(255))
    mpn = Column(String(255))
    lob = Column(String(255))
    fph4 = Column(String(255))
    model = Column(String(255))
    nand = Column(String(255))
    color = Column(String(255))
    project_code = Column(String(255))
    inv_eoh = Column(Integer)
    qtw_shipment_plan = Column(Integer)
    shipment_plan_cw = Column(Integer)
    shipment_plan_cw_1 = Column(Integer)
    shipment_plan_cw_2 = Column(Integer)
    shipment_plan_cw_3 = Column(Integer)
    shipment_plan_cw_4 = Column(Integer)
    top_up_demand_cw_1 = Column(Integer)
    top_up_demand_cw_2 = Column(Integer)
    top_up_demand_cw_3 = Column(Integer)
    top_up_demand_cw_4 = Column(Integer)
    new_shipment_plan_cw_1 = Column(Integer)
    new_shipment_plan_cw_2 = Column(Integer)
    new_shipment_plan_cw_3 = Column(Integer)
    new_shipment_plan_cw_4 = Column(Integer)
    create_time = Column(DateTime)
    update_time = Column(DateTime)

    @classmethod
    def query_by_week_order_rtm(cls, fiscal_qtr_week_name: str, order_rtm: list):
        s = GcDmpSession()
        ret = pd.DataFrame()
        try:
            q = s.query(cls)\
                .filter(cls.fiscal_qtr_week_name == fiscal_qtr_week_name)\
                .order_by(func.field(cls.rtm, *order_rtm))
            ret = pd.read_sql_query(q.statement, GcDmpEngine)
        except Exception as e:
            logger.exception(str(e))
        finally:
            s.close()
        return ret

    @classmethod
    def count_by_week(cls, fiscal_qtr_week_name: str):
        s = GcDmpSession()
        ret = 0
        try:
            ret = s.query(func.count(cls.id))\
                .filter(cls.fiscal_qtr_week_name == fiscal_qtr_week_name)\
                .scalar()
        except Exception as e:
            logger.exception(str(e))
        finally:
            s.close()
        return ret

    @classmethod
    def query_by_rtm_week(cls, rtm: str, week_date: str):
        s = GcDmpSession()
        ret = pd.DataFrame()
        try:
            q = s.query(cls)\
                .filter(cls.rtm == rtm)\
                .filter(cls.fiscal_qtr_week_name == week_date)
            ret = pd.read_sql_query(q.statement, GcDmpEngine)
        except Exception as e:
            logger.exception(str(e))
        finally:
            s.close()
        return ret

    @classmethod
    def count_by_rtm_week(cls, rtm: str, week_date: str):
        s = GcDmpAlgoSession()
        ret = 0
        try:
            ret = s.query(func.count(cls.fiscal_qtr_week_name))\
                .filter(cls.rtm == rtm)\
                .filter(cls.fiscal_qtr_week_name == week_date)\
                .scalar()
        except Exception as e:
            logger.exception(str(e))
        finally:
            s.close()
        return ret


class DimIdealRtmSoldtoMappingA(GcDmpBase):
    __tablename__ = 'dim_ideal_rtm_sold_to_mapping_a'
    __table_args__ = {'schema': 'gc_dmp_fast'}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    rtm = Column(String(256), comment='')
    business_type = Column(String(256), comment='')
    sold_to_id = Column(String(256), comment='')
    sold_to_name_en = Column(String(256), comment='')
    abbre = Column(String(256), comment='')
    start_week = Column(Integer)
    end_week = Column(Integer)
    create_time = Column(DateTime, comment='create time')
    update_time = Column(DateTime, comment='update time')

    @classmethod
    def get_rtm_business_type(cls, fiscal_week_year: int) -> list:
        s = GcDmpSession()
        ret = []
        if fiscal_week_year is None:
            return ret
        try:
            data = s.execute(f'''
                             SELECT DISTINCT rtm, 
                             business_type
                             FROM {cls.__tablename__}
                             WHERE start_week <= {fiscal_week_year} and end_week >= {fiscal_week_year};
                             ''')
            ret = data.fetchall()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

    @classmethod
    def get_business_type_relation(cls, fiscal_week_year: int):
        ret = pd.DataFrame()
        if fiscal_week_year is None:
            return ret
        try:
            sql_statement = f'''
                SELECT DISTINCT rtm,
                business_type, sold_to_id, abbre
                FROM {cls.__tablename__}
                WHERE start_week <= {fiscal_week_year} and end_week >= {fiscal_week_year};
            '''
            ret = pd.read_sql_query(sql_statement, GcDmpEngine)
        except Exception as e:
            logger.exception(e)
        return ret

    @classmethod
    def get_sold_to_id(cls, fiscal_week_year: int, rtm: str) -> list:
        s = GcDmpSession()
        ret = []
        if fiscal_week_year is None:
            return ret
        try:
            data = s.execute(f'''
                             SELECT DISTINCT sold_to_id
                             FROM {cls.__tablename__}
                             WHERE start_week <= {fiscal_week_year} and end_week >= {fiscal_week_year}
                             AND rtm = '{rtm}';
                             ''')
            fetch_data = data.fetchall()
            ret = [i[0] for i in fetch_data]
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

