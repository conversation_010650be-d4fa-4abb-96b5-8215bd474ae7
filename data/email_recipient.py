from sqlalchemy import text

from util.fast_lite_base import *


class TblEmailRecipient(FASTLiteBase):
    __tablename__ = "tbl_email_recipient"
    
    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    name = Column(String(256))
    email = Column(String(256))
    category = Column(SmallInteger, server_default='0', comment='类别，默认0是fast-lite-allocation')
    group = Column(String(256))
    is_cc = Column(SmallInteger, server_default='0', comment='是否是抄送人，默认0是收件人，1是抄送人')
    active = Column(SmallInteger, server_default='1', comment='是否启用，默认1是开启，0是停用')
    remarks = Column(String(256))
    create_time = Column(DateTime, server_default=text('CURRENT_TIMESTAMP'))
    update_time = Column(DateTime, server_default=text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'))

    @classmethod
    def get_to_cc_list(cls, category, group):
        '''
        return: to_list, cc_list
        '''
        s = FASTLiteSession()
        ret = None
        try:
            q_to = s.query(cls) \
                .filter(cls.is_cc == 0) \
                .filter(cls.active == 1)
            if category is not None:
                q_to = q_to.filter(cls.category == category)
            if group is not None:
                q_to = q_to.filter(cls.group == group)
            to = q_to.all()

            q_cc = s.query(cls) \
                .filter(cls.is_cc == 1) \
                .filter(cls.active == 1)
            if category is not None:
                q_cc = q_cc.filter(cls.category == category)
            if group is not None:
                q_cc = q_cc.filter(cls.group == group)
            cc = q_cc.all()

            to_list = [t.email for t in to]
            cc_list = [c.email for c in cc]
            ret = list(set(to_list)), list(set(cc_list))
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return ret
