from typing import Optional

import pandas as pd
from sqlalchemy import text

from domain.dashboard.entity.series_tier_sublob import SeriesTierSublob
from util.const import ErrorExcept, ErrCode
from util.gc_dmp_base import *
from util.gc_dmp_datasource import *
from util.util import env_dev


class OdsFastCPFActiveSKUiPad(ExDmpBase):
    __tablename__ = f"ods_fast_cpf_active_sku_ipad{'_test' if env_dev() else ''}"
    __table_args__ = {"extend_existing": True}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    fiscal_qtr_week_name = Column(String(256), comment='FY23Q1W12')
    fiscal_week_year = Column(Integer, comment='202312')
    upload_by = Column(String(256), comment='')
    upload_at = Column(DateTime, comment='')

    sales_org_id = Column(Integer, comment='')
    sales_org = Column(String(256), comment='')
    model = Column(String(256), comment='')
    project_code = Column(String(256), comment='')
    mpn_id = Column(String(256), comment='')
    nand = Column(String(256), comment='')
    color = Column(String(256), comment='')
    odq = Column(Integer, comment='')
    hr_lr = Column(String(256), comment='')

    version = Column(Integer, default=0, comment='')
    create_date = Column(DateTime, default=datetime.now, comment='create time')
    update_date = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='update time')

    @classmethod
    def query_by_version(cls, version):
        s = ExDmpSession()
        try:
            return s.query(func.count(cls.id).label('count')).filter(cls.version == version).scalar()
        except Exception as e:
            logger.debug(str(e))
            raise ErrorExcept(ErrCode.DBQueryError, "db query error: " + str(e))
        finally:
            s.close()

    @classmethod
    def batch_save(cls, datas: list):
        s = ExDmpSession()
        try:
            s.bulk_insert_mappings(cls, datas)
            s.commit()
        except Exception as e:
            logger.debug(str(e))
            raise ErrorExcept(ErrCode.DBInsert, "insert to db failed" + str(e))
        finally:
            s.close()
        return True

    @classmethod
    def query_snapshot_version(cls, fiscal_week_year: int):
        s = ExDmpSession()
        results = []
        try:
            res = s.execute(f'''
                SELECT * FROM {cls.__tablename__} AS a
                WHERE fiscal_week_year = {fiscal_week_year} AND version = 
                (SELECT MAX(version) FROM {cls.__tablename__} 
                WHERE fiscal_week_year = {fiscal_week_year});
            ''').fetchall()
            results = [dict(item) for item in res]
        except Exception as e:
            logger.debug(str(e))
            raise ErrorExcept(ErrCode.DBQueryError, "db query error: " + str(e))
        finally:
            s.close()
        return results

    @classmethod
    def query_snapshot_data(cls, fiscal_week_year: int, fiscal_qtr_week_name: str):
        s = ExDmpSession()
        results = []
        try:
            r = s.query(cls.fiscal_week_year, cls.version) \
                .order_by(cls.fiscal_week_year.desc()) \
                .order_by(cls.version.desc()) \
                .first()

            res = s.execute(f'''
                SELECT "{fiscal_qtr_week_name}" AS fiscal_qtr_week_name, {fiscal_week_year} AS fiscal_week_year,
                upload_by,upload_at,sales_org_id,sales_org,
                model,project_code,mpn_id,nand,color,odq,hr_lr
                FROM {cls.__tablename__} AS a
                WHERE a.fiscal_week_year = {r.fiscal_week_year} AND a.version = {r.version}
            ''').fetchall()
            results = [dict(item) for item in res]
        except Exception as e:
            logger.debug(str(e))
            raise ErrorExcept(ErrCode.DBQueryError, "db query error: " + str(e))
        finally:
            s.close()
        return results


class OdsFastCPFActiveSKUiPadSnapshot(ExDmpBase):
    __tablename__ = f"ods_fast_cpf_active_sku_ipad_snapshot{'_test' if env_dev() else ''}"
    __table_args__ = {"extend_existing": True}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    fiscal_qtr_week_name = Column(String(256), comment='FY23Q1W12')
    fiscal_week_year = Column(Integer, comment='202312')
    upload_by = Column(String(256), comment='')

    sales_org_id = Column(Integer, comment='')
    sales_org = Column(String(256), comment='')
    model = Column(String(256), comment='')
    project_code = Column(String(256), comment='')
    mpn_id = Column(String(256), comment='')
    nand = Column(String(256), comment='')
    color = Column(String(256), comment='')
    odq = Column(Integer, comment='')
    hr_lr = Column(String(256), comment='')
    version = Column(Integer, default=0, comment='')

    create_date = Column(DateTime, default=datetime.now, comment='create time')
    update_date = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='update time')

    @classmethod
    def bulk_save(cls, objs: list):
        s = ExDmpSession()
        try:
            s.execute(cls.__table__.insert(), objs)
            s.commit()
        except Exception as e:
            logger.exception(str(e))
            raise ErrorExcept(ErrCode.DBInsert, "insert to db failed" + str(e))
        finally:
            s.close()
        return True

    @classmethod
    def query_data_by_week(cls, fiscal_week_year: int):
        s = ExDmpSession()
        res = []
        try:
            res = s.execute(f'''
                SELECT update_date as last_upload_date, 
                "Active SKU List - iPad" as name, 
                upload_by as uploader_name, fiscal_week_year
                FROM {cls.__tablename__} AS a
                WHERE a.fiscal_week_year = {fiscal_week_year} limit 1;
            ''').fetchall()
        except Exception as e:
            logger.debug(str(e))
            raise ErrorExcept(ErrCode.DBQueryError, "db query error: " + str(e))
        finally:
            s.close()
        return res

    @classmethod
    def delete_by_week(cls, fiscal_week_year: int):
        s = ExDmpSession()
        count_delete = 0
        try:
            count_delete = s.query(cls) \
                .filter(cls.fiscal_week_year == fiscal_week_year) \
                .delete()
            s.commit()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return count_delete


class OdsFastCPFTWOSiPhone(ExDmpBase):
    __tablename__ = f"ods_fast_cpf_twos_iphone{'_test' if env_dev() else ''}"
    __table_args__ = {"extend_existing": True}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    fiscal_qtr_week_name = Column(String(256), comment='FY23Q1W12')
    fiscal_week_year = Column(Integer, comment='202312')
    upload_by = Column(String(256), comment='')
    upload_at = Column(DateTime, comment='')

    model = Column(String(256), comment='')
    mpn = Column(String(64), comment='')
    rtm = Column(String(256), comment='')
    business_type = Column(String(256), comment='')
    sold_to_id = Column(String(64), comment='')
    sold_to_name = Column(String(128), comment='')
    twos = Column(String(256), comment='')
    type = Column(String(256), comment='')
    hr_lr = Column(String(16), comment='')

    version = Column(Integer, server_default='0', comment='')
    create_time = Column(DateTime, server_default=text('CURRENT_TIMESTAMP'))
    update_time = Column(DateTime, server_default=text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'))

    @classmethod
    def bulk_save(cls, objs: list):
        s = ExDmpSession()
        try:
            s.execute(cls.__table__.insert(), objs)
            s.commit()
        except Exception as e:
            logger.exception(str(e))
            raise ErrorExcept(ErrCode.DBInsert, "insert to db failed" + str(e))
        finally:
            s.close()
        return True

    @classmethod
    def query_by_version(cls, version):
        s = ExDmpSession()
        try:
            return s.query(func.count(cls.id).label('count')).filter(cls.version == version).scalar()
        except Exception as e:
            logger.debug(str(e))
            raise ErrorExcept(ErrCode.DBQueryError, "db query error: " + str(e))
        finally:
            s.close()

    def query_max_version_twos(cls) -> list:
        s = ExDmpSession()
        ret = []
        try:
            ret = s.execute(f'''
                                SELECT model
                                , rtm
                                , business_type
                                , hr_lr
                                , twos
                                , mpn
                                , sold_to_id
                                FROM {cls.__tablename__}
                                WHERE version = 
                                (SELECT max(version) FROM {cls.__tablename__});
                                ''').fetchall()
            ret = [{
                "sub_lob": x.model,
                "rtm": x.rtm,
                'sub_rtm': x.business_type,
                'hr_lr': x.hr_lr,
                'twos': x.twos,
                'mpn': x.mpn,
                'sold_to_id': x.sold_to_id,
            } for x in ret]
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return ret

class OdsFastCPFSoldToMappingIPhone(ExDmpBase):
    __tablename__ = f"ods_fast_cpf_sold_to_mapping_list_iphone{'_test' if env_dev() else ''}"
    __table_args__ = {"extend_existing": True}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    fiscal_qtr_week_name = Column(String(256), comment='FY23Q1W12')
    fiscal_week_year = Column(Integer, comment='202312')
    upload_by = Column(String(256), comment='')
    upload_at = Column(DateTime, comment='')

    region = Column(String(256), comment='')
    rtm = Column(String(256), comment='')
    business_type = Column(String(256), comment='')
    sold_to_id = Column(String(256), comment='')
    sold_to_name = Column(String(256), comment='')
    sold_to_name_abbre = Column(String(100), comment='')
    remark = Column(String(256), comment='')

    version = Column(Integer, server_default='0', comment='')
    create_time = Column(DateTime, server_default=text('CURRENT_TIMESTAMP'))
    update_time = Column(DateTime, server_default=text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'))

    @classmethod
    def bulk_save(cls, objs: list):
        s = ExDmpSession()
        try:
            s.execute(cls.__table__.insert(), objs)
            s.commit()
        except Exception as e:
            logger.exception(str(e))
            raise ErrorExcept(ErrCode.DBInsert, "insert to db failed" + str(e))
        finally:
            s.close()
        return True

    @classmethod
    def query_by_version(cls, version):
        s = ExDmpSession()
        try:
            return s.query(func.count(cls.id).label('count')).filter(cls.version == version).scalar()
        except Exception as e:
            logger.debug(str(e))
            raise ErrorExcept(ErrCode.DBQueryError, "db query error: " + str(e))
        finally:
            s.close()

    @classmethod
    def get_sold_to_id(cls, rtm: str) -> list:
        s = ExDmpSession()
        ret = []
        try:
            data = s.execute(f'''
                                SELECT DISTINCT sold_to_id
                                FROM {cls.__tablename__}
                                WHERE  version in (select max(version) from {cls.__tablename__})
                                AND rtm = '{rtm}';
                                ''')
            fetch_data = data.fetchall()
            ret = [i[0] for i in fetch_data]
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

    @classmethod
    def get_all_rtm_sold_to_id(cls) -> list:
        s = ExDmpSession()
        ret = []
        try:
            data = s.execute(f'''
                                SELECT DISTINCT rtm, sold_to_id
                                FROM {cls.__tablename__}
                                WHERE  version in (select max(version) from {cls.__tablename__});
                                ''')
            ret = data.fetchall()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

    @classmethod
    def query_sub_rtm_by_max_version(cls) -> list:
        s = ExDmpSession()
        ret = []
        try:
            ret = s.execute(f'''
                                SELECT fiscal_qtr_week_name, rtm ,business_type ,region
                                FROM {cls.__tablename__}
                                WHERE  version = 
                                (SELECT max(version) FROM {cls.__tablename__})
                                 GROUP BY region, business_type;
                                ''').fetchall()
            ret = [{
                "fiscal_week": x.fiscal_qtr_week_name,
                "rtm": x.rtm,
                'sub_rtm': x.business_type,
                'region': x.region} for x in ret]

        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return ret


    @classmethod
    def get_business_type_relation(cls):
        ret = pd.DataFrame()
        try:
            sql_statement = f'''
                   SELECT DISTINCT rtm,
                   business_type, sold_to_id, remark as abbre
                   FROM {cls.__tablename__}
                   WHERE version in (select max(version) from {cls.__tablename__});
               '''
            ret = pd.read_sql_query(sql_statement, ExDmpEngine)
        except Exception as e:
            logger.exception(e)
        return ret

    @classmethod
    def list_sold_to_ids_df(cls, rtms, region):
        s = ExDmpSession()
        res = []
        try:
            sub_query = s.query(func.max(cls.version))
            query = s.query(
                cls.region, cls.rtm, cls.business_type.label('sub_rtm'), cls.sold_to_id, cls.sold_to_name
                            ).distinct() \
                .filter(cls.version == sub_query)
            if rtms:
                query = query.filter(cls.rtm.in_(rtms))
            if region:
                query = query.filter(cls.region == region)
            res = pd.read_sql_query(query.statement, ExDmpEngine)
            return res
        except Exception as e:
            logger.error(e)
            raise ErrorExcept(ErrCode.DBQueryError, "db query error: " + str(e))
        finally:
            s.close()

    @classmethod
    def get_rtm_business_type(cls) -> list:
        s = ExDmpSession()
        ret = []
        try:
            data = s.execute(f'''
                              SELECT DISTINCT rtm, 
                              business_type
                              FROM {cls.__tablename__}
                              WHERE version in (select max(version) from {cls.__tablename__});
                              ''')
            ret = data.fetchall()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret
    
    @classmethod
    def get_region_rtm_subrtm(cls) -> list:
        s = ExDmpSession()
        ret = []
        try:
            data = s.execute(f'''
                              SELECT DISTINCT region,
                              rtm, 
                              business_type
                              FROM {cls.__tablename__}
                              WHERE version in (select max(version) from {cls.__tablename__});
                              ''')
            ret = data.fetchall()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret
    
    @classmethod
    def get_max_version_basic(cls) -> list:
        s = ExDmpSession()
        ret = []
        try:
            sub_query = s.query(func.max(cls.version)).subquery()
            ret = s.query(cls.fiscal_qtr_week_name, cls.fiscal_week_year, cls.version) \
                   .filter(cls.version == sub_query) \
                   .limit(1).all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

    @classmethod
    def query_data_by_version(cls, version: int) -> pd.DataFrame:
        s = ExDmpSession()
        ret = pd.DataFrame()
        try:
            query = s.query(cls.region.label("Region"), cls.rtm.label("RTM"), cls.business_type.label("Business Type"), 
                          cls.sold_to_id.label("Sold-to ID"), cls.sold_to_name.label("Name"), 
                          cls.sold_to_name_abbre.label("Abbre."), cls.remark.label("Remark")) \
                   .filter(cls.version == version)
            ret = pd.read_sql_query(query.statement, ExDmpEngine)
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret
    
    @classmethod
    def query_soldto_by_max_version(cls) -> list:
        s = ExDmpSession()
        ret = []
        try:
            ret = s.execute(f'''
                                SELECT fiscal_qtr_week_name,region,rtm,business_type,sold_to_id,sold_to_name,sold_to_name_abbre
                                FROM {cls.__tablename__}
                                WHERE version in (select max(version) from {cls.__tablename__})
                                ''').fetchall()
            ret = [{
                "fiscal_qtr_week_name": x.fiscal_qtr_week_name,
                'region': x.region,
                "rtm": x.rtm,
                'sub_rtm': x.business_type,
                'sold_to_id': x.sold_to_id,
                'sold_to_name': x.sold_to_name,
                'sold_to_name_abbre': x.sold_to_name_abbre,
                } for x in ret]

        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return ret



class OdsFastCPFActiveSKULob(ExDmpBase):
    # __tablename__ = f"ods_fast_cpf_active_sku_lob"
    __tablename__ = f"ods_fast_cpf_active_sku_lob{'_test' if env_dev() else ''}"
    __table_args__ = {"extend_existing": True}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    fiscal_qtr_week_name = Column(String(32), comment='FY23Q1W12')
    fiscal_week_year = Column(Integer, comment='202312')
    upload_by = Column(String(32), comment='')
    upload_at = Column(DateTime, comment='')

    sales_org_id = Column(Integer, comment='')
    sales_org = Column(String(256), comment='')
    lob = Column(String(32), comment='')
    model = Column(String(256), comment='')
    project_code = Column(String(256), comment='')
    mpn_id = Column(String(256), comment='')
    nand = Column(String(256), comment='')
    color = Column(String(256), comment='')
    odq = Column(Integer, comment='')
    hr_lr = Column(String(256), comment='')
    rp_mpn_mapped_from_carrier = Column(String(256), comment='')
    tier = Column(String(128), comment='')
    series = Column(String(128), comment='')

    version = Column(Integer, default=0, comment='')
    create_date = Column(DateTime, default=datetime.now, comment='create time')
    update_date = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='update time')

    @classmethod
    def get_series_and_tier_sublob(cls, region: str, lob: str, fiscal_week_year: Optional[str] = None) -> list[SeriesTierSublob]:
        s = ExDmpSession()
        res = []
        try:
            filter_params = [cls.sales_org == region, cls.lob == lob]
            if fiscal_week_year:
                filter_params.append(cls.fiscal_qtr_week_name == fiscal_week_year)
            sub_query = s.query(func.max(cls.version)).filter(cls.lob == lob)
            obj_list = (
                s.query(cls,
                        cls.model,
                        cls.tier,
                        cls.series).filter(cls.version == sub_query, *filter_params).all()
            )
            for item in obj_list:
                res.append(SeriesTierSublob(model=item.model, tier=item.tier, series=item.series))
        except Exception as e:
            logger.error(e)
            raise ErrorExcept(ErrCode.DBQueryError, "db query error: " + str(e))
        finally:
            s.close()
        return res

    @classmethod
    def list_mpns(cls, lob) -> list[str]:
        s = ExDmpSession()
        res = []
        try:
            sub_query = s.query(func.max(cls.version)).filter(cls.lob == lob)
            mpns = s.query(cls.mpn_id).distinct() \
                .filter(cls.lob == lob, cls.version == sub_query) \
                .all()
            for mpn in mpns:
                res.append(mpn.mpn_id)
            return res
        except Exception as e:
            logger.error(e)
            raise ErrorExcept(ErrCode.DBQueryError, "db query error: " + str(e))
        finally:
            s.close()

    @classmethod
    def query_by_version(cls, version, lob):
        s = ExDmpSession()
        try:
            return (s.query(func.count(cls.id).label('count'))
                    .filter(cls.version == version).filter(cls.lob == lob)
                    .scalar())
        except Exception as e:
            logger.debug(str(e))
            raise ErrorExcept(ErrCode.DBQueryError, "db query error: " + str(e))
        finally:
            s.close()

    @classmethod
    def batch_save(cls, datas: list):
        s = ExDmpSession()
        try:
            s.bulk_insert_mappings(cls, datas)
            s.commit()
        except Exception as e:
            logger.debug(str(e))
            raise ErrorExcept(ErrCode.DBInsert, "insert to db failed" + str(e))
        finally:
            s.close()
        return True

    @classmethod
    def get_rtm_business_type(cls) -> list:
        s = ExDmpSession()
        ret = []
        try:
            data = s.execute(f'''
                              SELECT DISTINCT rtm, 
                              business_type
                              FROM {cls.__tablename__}
                              WHERE version in (select max(version) from {cls.__tablename__});
                              ''')
            ret = data.fetchall()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

    @classmethod
    def get_data_by_lob_and_region(cls, lob, region):
        s = ExDmpSession()
        res = []
        try:
            sub_query = s.query(func.max(cls.version)).filter(cls.lob == lob)
            query = s.query(cls.lob, cls.model.label('sub_lob'),
                            cls.mpn_id.label("mpn"), cls.hr_lr,
                            cls.nand, cls.color, cls.rp_mpn_mapped_from_carrier) \
                .filter(cls.version == sub_query, cls.model != 'iPhone SE (3rd Gen)')
            if region:
                query = query.filter(cls.sales_org == region)
            if lob:
                query = query.filter(cls.lob == lob)
            res = pd.read_sql_query(query.statement, ExDmpEngine)
            return res
        except Exception as e:
            logger.error(e)
            raise ErrorExcept(ErrCode.DBQueryError, "db query error: " + str(e))
        finally:
            s.close()

    @classmethod
    def list_sub_lobs(cls, region: str, lob: str) -> list[str]:
        s = ExDmpSession()
        res = []
        try:
            sub_query = s.query(func.max(cls.version)).filter(cls.lob == lob)
            sub_lobs = (
                s.query(cls.model.label("sub_lob"))
                .distinct()
                .filter(cls.sales_org == region)
                .filter(cls.lob == lob, cls.version == sub_query)
                .order_by(cls.model)
                .all()
            )
            res = [item.sub_lob for item in sub_lobs]
        except Exception as e:
            logger.error(e)
            raise ErrorExcept(ErrCode.DBQueryError, "db query error: " + str(e))
        finally:
            s.close()
        return res
    
    @classmethod
    def list_lob_sub_lob_mpn(cls, lob: str) -> list[str]:
        s = ExDmpSession()
        res = []
        try:
            sub_query = s.query(func.max(cls.version)).filter(cls.lob == lob)
            res = (
                s.query(cls.lob, cls.model.label("sub_lob"), cls.mpn_id.label("mpn"))
                .distinct()
                .filter(cls.lob == lob, cls.version == sub_query)
                .all()
            )
        except Exception as e:
            logger.error(e)
            raise ErrorExcept(ErrCode.DBQueryError, "db query error: " + str(e))
        finally:
            s.close()
        return res
    
    @classmethod
    def list_lob_sublob(cls, lob: str) -> list[str]:
        s = ExDmpSession()
        res = []
        try:
            sub_query = s.query(func.max(cls.version)).filter(cls.lob == lob)
            res = (
                s.query(cls.lob, cls.model.label("sub_lob"))
                .distinct()
                .filter(cls.lob == lob, cls.version == sub_query)
                .all()
            )
        except Exception as e:
            logger.error(e)
            raise ErrorExcept(ErrCode.DBQueryError, "db query error: " + str(e))
        finally:
            s.close()
        return res

    @classmethod
    def list_mpn(cls, lob: str) -> list[str]:
        s = ExDmpSession()
        res = []
        try:
            sub_query = s.query(func.max(cls.version)).filter(cls.lob == lob)
            res = (
                s.query(cls.mpn_id.label("mpn"))
                .distinct()
                .filter(cls.lob == lob, cls.version == sub_query)
                .all()
            )
        except Exception as e:
            logger.error(e)
            raise ErrorExcept(ErrCode.DBQueryError, "db query error: " + str(e))
        finally:
            s.close()
        return res


class AppFastIdealEsrSoldToMappingWi(GcDmpBase):
    __tablename__ = 'app_fast_ideal_esr_sold_to_mapping_wi'
    __table_args__ = {'schema': 'gc_dmp_fast'}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    rtm = Column(String(255), comment='')
    rtmL4 = Column(String(255), comment='')
    sold_to_id = Column(String(255), comment='')
    sold_to_name = Column(String(255), comment='')
    sold_to_name_en = Column(String(255), comment='')

    version = Column(Integer, server_default='0', comment='')
    create_time = Column(DateTime, server_default=text('CURRENT_TIMESTAMP'))

    @classmethod
    def query_all_sold_id(cls):
        s = GcDmpSession()
        ret = []
        try:
            q = s.query(AppFastIdealEsrSoldToMappingWi.sold_to_id).distinct() \
                .all()
            ret = [i[0] for i in q]
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

    @classmethod
    def get_business_type_relation(cls, fiscal_week_year: int):
        ret = pd.DataFrame()
        if fiscal_week_year is None:
            return ret
        try:
            sql_statement = f'''
                   SELECT DISTINCT rtm,
                   business_type, sold_to_id, abbre
                   FROM {cls.__tablename__}
                   WHERE start_week <= {fiscal_week_year} and end_week >= {fiscal_week_year};
               '''
            ret = pd.read_sql_query(sql_statement, GcDmpEngine)
        except Exception as e:
            logger.exception(e)
        return ret


class OdsFastCPFMixiPad(ExDmpBase):
    __tablename__ = f"ods_fast_cpf_sold_to_mix_ipad{'_test' if env_dev() else ''}"
    __table_args__ = {"extend_existing": True}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    fiscal_qtr_week_name = Column(String(256), comment='FY23Q1W12')
    fiscal_week_year = Column(Integer, comment='202312')
    upload_by = Column(String(256), comment='')
    upload_at = Column(DateTime, comment='')

    sales_org = Column(String(256), comment='')
    rtml4 = Column(String(256), comment='')
    fph1 = Column(String(256), comment='')
    fph3 = Column(String(256), comment='')
    project_code = Column(String(256), comment='')
    nand = Column(String(256), comment='')
    color = Column(String(256), comment='')
    mpn = Column(String(256), comment='')
    odq = Column(String(256), comment='')
    sold_to_id = Column(String(256), comment='')
    sold_to_name = Column(String(256), comment='')
    mix = Column(String(256), comment='')

    version = Column(Integer, server_default='0', comment='')
    create_time = Column(DateTime, server_default=text('CURRENT_TIMESTAMP'))
    update_time = Column(DateTime, server_default=text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'))

    @classmethod
    def bulk_save(cls, objs: list):
        s = ExDmpSession()
        try:
            s.execute(cls.__table__.insert(), objs)
            s.commit()
        except Exception as e:
            logger.exception(str(e))
            raise ErrorExcept(ErrCode.DBInsert, "insert to db failed" + str(e))
        finally:
            s.close()
        return True

    @classmethod
    def query_by_version(cls, version):
        s = ExDmpSession()
        try:
            return s.query(func.count(cls.id).label('count')).filter(cls.version == version).scalar()
        except Exception as e:
            logger.debug(str(e))
            raise ErrorExcept(ErrCode.DBQueryError, "db query error: " + str(e))
        finally:
            s.close()
    
    @classmethod
    def get_data_by_max_version(cls):
        ret = pd.DataFrame()
        try:
            sql_statement = f'''
                   SELECT sales_org, mpn, sold_to_id, mix as value
                   FROM {cls.__tablename__}
                   WHERE version in (select max(version) from {cls.__tablename__});
               '''
            ret = pd.read_sql_query(sql_statement, ExDmpEngine)
        except Exception as e:
            logger.exception(e)
        return ret


class OdsFastCPFTWOSiPad(ExDmpBase):
    __tablename__ = f"ods_fast_cpf_twos_ipad{'_test' if env_dev() else ''}"
    __table_args__ = {"extend_existing": True}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    fiscal_qtr_week_name = Column(String(256), comment='FY23Q1W12')
    fiscal_week_year = Column(Integer, comment='202312')
    upload_by = Column(String(256), comment='')
    upload_at = Column(DateTime, comment='')

    sales_org = Column(String(256), comment='')
    rtml4 = Column(String(256), comment='')
    fph1 = Column(String(256), comment='')
    fph3 = Column(String(256), comment='')
    project_code = Column(String(256), comment='')
    nand = Column(String(256), comment='')
    color = Column(String(256), comment='')
    mpn = Column(String(256), comment='')
    odq = Column(String(256), comment='')
    sold_to_id = Column(String(256), comment='')
    sold_to_name = Column(String(256), comment='')
    twos = Column(String(256), comment='')
    type = Column(String(256), comment='')

    version = Column(Integer, server_default='0', comment='')
    create_time = Column(DateTime, server_default=text('CURRENT_TIMESTAMP'))
    update_time = Column(DateTime, server_default=text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'))

    @classmethod
    def bulk_save(cls, objs: list):
        s = ExDmpSession()
        try:
            s.execute(cls.__table__.insert(), objs)
            s.commit()
        except Exception as e:
            logger.exception(str(e))
            raise ErrorExcept(ErrCode.DBInsert, "insert to db failed" + str(e))
        finally:
            s.close()
        return True

    @classmethod
    def query_by_version(cls, version):
        s = ExDmpSession()
        try:
            return s.query(func.count(cls.id).label('count')).filter(cls.version == version).scalar()
        except Exception as e:
            logger.debug(str(e))
            raise ErrorExcept(ErrCode.DBQueryError, "db query error: " + str(e))
        finally:
            s.close()

    
    @classmethod
    def get_data_by_max_version(cls):
        ret = pd.DataFrame()
        try:
            sql_statement = f'''
                   SELECT sales_org, mpn, sold_to_id, twos as value
                   FROM {cls.__tablename__}
                   WHERE version in (select max(version) from {cls.__tablename__});
               '''
            ret = pd.read_sql_query(sql_statement, ExDmpEngine)
        except Exception as e:
            logger.exception(e)
        return ret

ExDmpBase.metadata.create_all(ExDmpEngine)
