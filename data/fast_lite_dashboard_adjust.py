from sqlalchemy import insert
from domain.dashboard.entity.fiscal_week import FiscalWeek
from domain.dashboard.impl.fiscal_week_container import FiscalWeekContainer
from util.const import *
from util.fast_lite_base import *


class FastLiteDashboardAdjust(FASTLiteBase):
    __tablename__ = 'dashboard_adjusted_mix'
    # __table_args__ = 

    start_fiscal_week = Column(Integer, primary_key=True, comment='2024112')
    start_fiscal_week_name = Column(String(256))
    adjusted_mix = Column(Float)
    create_time = Column(DateTime)
    update_time = Column(DateTime)

    @classmethod
    def get_adjusted_mix(cls, fiscal_week: str) -> float:
        fiscal_week_int = FiscalWeek(fiscal_week).fiscal_week_int
        s = FASTLiteSession()
        ret = ""
        try:
            adjusted_mix = s.query(cls.adjusted_mix).filter(cls.start_fiscal_week == fiscal_week_int).first()
            ret = adjusted_mix.adjusted_mix if adjusted_mix else ""
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return ret
