from math import ceil
from sqlalchemy import or_, and_, case, desc

from util.const import *
from util.fast_lite_base import *
from util.util import get_weeks_info, gen_basic_data, gen_dynamic_key
from util.generate_sub_query import generate_sold_to_sub_query
 
    
class LiteNewAppFastForecastTrmSoWa(FASTLiteBase):
    __tablename__ = 'new_app_fast_forecast_rtm_so_wa'
    __table_args__ = {'schema': 'fast_lite'}
    
    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    week_date = Column(String(256), comment='FY23Q1W12')
    rtm = Column(String(256), comment='')
    business_type = Column(String(256), comment='')
    sold_to_id = Column(String(256), comment='')
    sold_to_name = Column(String(256), comment='')
    lob = Column(String(256), comment='')
    model = Column(String(256), comment='')
    sku = Column(String(256), comment='')
    week_in_fiscal_quarter = Column(Integer, comment='')
    total_weeks_in_quarter = Column(Integer, comment='')
    cq_total = Column(Integer, comment='')
    cq_w1 = Column(Integer, comment='')
    cq_w2 = Column(Integer, comment='')
    cq_w3 = Column(Integer, comment='')
    cq_w4 = Column(Integer, comment='')
    cq_w5 = Column(Integer, comment='')
    cq_w6 = Column(Integer, comment='')
    cq_w7 = Column(Integer, comment='')
    cq_w8 = Column(Integer, comment='')
    cq_w9 = Column(Integer, comment='')
    cq_w10 = Column(Integer, comment='')
    cq_w11 = Column(Integer, comment='')
    cq_w12 = Column(Integer, comment='')
    cq_w13 = Column(Integer, comment='')
    cq_w14 = Column(Integer, comment='')
    nq_w1 = Column(Integer, comment='')
    nq_w2 = Column(Integer, comment='')
    nq_w3 = Column(Integer, comment='')
    nq_w4 = Column(Integer, comment='')
    nq_w5 = Column(Integer, comment='')
    nq_w6 = Column(Integer, comment='')
    nq_w7 = Column(Integer, comment='')
    nq_w8 = Column(Integer, comment='')
    nq_w9 = Column(Integer, comment='')
    nq_w10 = Column(Integer, comment='')
    nq_w11 = Column(Integer, comment='')
    nq_w12 = Column(Integer, comment='')
    nq_w13 = Column(Integer, comment='')
    nq_w14 = Column(Integer, comment='') 
   
    @classmethod
    def bulk_save(cls, objs: list):
        s = FASTLiteSession()
        try:
            data_length = len(objs)
            step_size = 500
            for i in range(ceil(data_length/step_size)):
                if data_length - i * step_size > step_size:
                    s.bulk_save_objects(objs[i*step_size:(i+1)*step_size], return_defaults=True)
                    s.commit()
                    logger.info(f'insert into {step_size}.')
                else:
                    s.bulk_save_objects(objs[i*step_size:data_length], return_defaults=True)
                    s.commit()
                    logger.info(f'insert into {data_length-i*step_size}.')
                
        except Exception as e:
            logger.exception(str(e))
        finally:
            s.close()
    
    @classmethod
    def get_details(cls, fiscal_qtr_week_name: str, lob: str, model: list, sku: list, orders: list):
        s = FASTLiteSession()
        ret = []
        try:
            q = s.query(func.sum(cls.cq_total).label('cq_total'),
                        func.sum(cls.cq_w1).label('cq_w1'),
                        func.sum(cls.cq_w2).label('cq_w2'),
                        func.sum(cls.cq_w3).label('cq_w3'),
                        func.sum(cls.cq_w4).label('cq_w4'),
                        func.sum(cls.cq_w5).label('cq_w5'),
                        func.sum(cls.cq_w6).label('cq_w6'),
                        func.sum(cls.cq_w7).label('cq_w7'),
                        func.sum(cls.cq_w8).label('cq_w8'),
                        func.sum(cls.cq_w9).label('cq_w9'),
                        func.sum(cls.cq_w10).label('cq_w10'),
                        func.sum(cls.cq_w11).label('cq_w11'),
                        func.sum(cls.cq_w12).label('cq_w12'),
                        func.sum(cls.cq_w13).label('cq_w13'),
                        func.sum(cls.cq_w14).label('cq_w14'),
                        func.sum(cls.nq_w1).label('nq_w1'),
                        func.sum(cls.nq_w2).label('nq_w2'),
                        func.sum(cls.nq_w3).label('nq_w3'),
                        func.sum(cls.nq_w4).label('nq_w4'),
                        func.sum(cls.nq_w5).label('nq_w5'),)\
                .filter(cls.week_date == fiscal_qtr_week_name)
            if lob is not None and lob.strip() != '':
                q = q.filter(cls.lob == lob)
            if model is not None and len(model) != 0:
                q = q.filter(cls.model.in_(model))
            if sku is not None and len(sku) != 0:
                q = q.filter(cls.sku.in_(sku))
                
            # 需要进行统计总数,以及分 RTM/business_type 的汇总
            q = q.group_by(cls.week_date)
            data_list = q.all()
            data_structure = {}
            dynamic_key= []
            fixed_dict = {
                "rtm": 'RTM Model Total',
                "business_type": '',
                "sold_to": "",
            }
            weeks_info = get_weeks_info(fiscal_qtr_week_name)
            total_weeks = weeks_info.get('total_weeks')
            dynamic_key = gen_dynamic_key(total_weeks)
            data_structure = {}
            for i in data_list:
                basic_data = gen_basic_data(i, total_weeks)
                data_structure = {
                    **fixed_dict,
                    **basic_data
                }
                ret.append(data_structure)
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret, list(fixed_dict.keys())+dynamic_key


    @classmethod
    def get_title(self, fiscal_qtr_week_name: str):
        s = FASTLiteSession()
        titles = []
        try:
            weeks_info = get_weeks_info(fiscal_qtr_week_name)
            total_weeks = weeks_info.get('total_weeks')
            week_in_current_quarter = weeks_info.get('current_week')
            # FY23Q1W13
            fiscal_year_quarter_week = fiscal_qtr_week_name.split('W')
            fiscal_year_quarter = fiscal_year_quarter_week[0]
            week = fiscal_year_quarter_week[1]
            fiscal_year = fiscal_year_quarter.split('Q')[0]
            quarter = int(fiscal_year_quarter.split('Q')[1])
            year = int(fiscal_year.split('FY')[1])
            
            next_quarter = quarter + 1
            if next_quarter > 4:
                next_quarter = 1
                next_year = year + 1
            else:
                next_year = year
            next_fiscal_year_quarter = 'FY'+str(next_year)+'Q'+str(next_quarter)
            # 固定标题
            fixed_title = ['Model / RTM', 'Business Type', 'Sold-to']
            # 季度汇总
            quarter_total = [f"CQ Total\n{fiscal_year_quarter}"]
            fiscal_weeks = [] 
            # 本季度所有周
            for k in range(total_weeks):
                if k+1-week_in_current_quarter < 0:
                    fiscal_weeks.append(f"CW{k+1-week_in_current_quarter}\n{fiscal_year_quarter}W{k+1}")
                elif k+1-week_in_current_quarter == 0:
                    fiscal_weeks.append(f"CW\n{fiscal_year_quarter}W{k+1}")
                else:
                    fiscal_weeks.append(f"CW+{k+1-week_in_current_quarter}\n{fiscal_year_quarter}W{k+1}")
            # 下季度5周
            for n in range(5):
                fiscal_weeks.append(f"CW+{n+1+total_weeks-week_in_current_quarter}\n{next_fiscal_year_quarter}W{n+1}")
            titles = fixed_title+quarter_total+fiscal_weeks
                
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return titles

    @classmethod
    def get_details_by_rtm(cls, fiscal_qtr_week_name: str, lob: str, model: list, sku: list, orders: list):
        s = FASTLiteSession()
        ret = {}
        try:
            q = s.query(cls.rtm,
                        func.sum(cls.cq_total).label('cq_total'),
                        func.sum(cls.cq_w1).label('cq_w1'),
                        func.sum(cls.cq_w2).label('cq_w2'),
                        func.sum(cls.cq_w3).label('cq_w3'),
                        func.sum(cls.cq_w4).label('cq_w4'),
                        func.sum(cls.cq_w5).label('cq_w5'),
                        func.sum(cls.cq_w6).label('cq_w6'),
                        func.sum(cls.cq_w7).label('cq_w7'),
                        func.sum(cls.cq_w8).label('cq_w8'),
                        func.sum(cls.cq_w9).label('cq_w9'),
                        func.sum(cls.cq_w10).label('cq_w10'),
                        func.sum(cls.cq_w11).label('cq_w11'),
                        func.sum(cls.cq_w12).label('cq_w12'),
                        func.sum(cls.cq_w13).label('cq_w13'),
                        func.sum(cls.cq_w14).label('cq_w14'),
                        func.sum(cls.nq_w1).label('nq_w1'),
                        func.sum(cls.nq_w2).label('nq_w2'),
                        func.sum(cls.nq_w3).label('nq_w3'),
                        func.sum(cls.nq_w4).label('nq_w4'),
                        func.sum(cls.nq_w5).label('nq_w5'),)\
                .filter(cls.week_date == fiscal_qtr_week_name)
            if lob is not None and lob.strip() != '':
                q = q.filter(cls.lob == lob)
            if model is not None and len(model) != 0:
                q = q.filter(cls.model.in_(model))
            if sku is not None and len(sku) != 0:
                q = q.filter(cls.sku.in_(sku))
                
            # 需要进行统计总数,以及分 RTM/business_type 的汇总
            q = q.group_by(cls.rtm)
            data_list = q.all()
            weeks_info = get_weeks_info(fiscal_qtr_week_name)
            data_structure = {}
            for i in data_list:
                basic_data = gen_basic_data(i, weeks_info.get('total_weeks'))
                data_structure = {
                    "rtm": i['rtm'],
                    "business_type": 'All',
                    "sold_to": 'All',
                    **basic_data
                }
                ret[i['rtm']] = data_structure
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

    @classmethod
    def get_details_by_business_type(cls, fiscal_qtr_week_name: str, lob: str, model: list, sku: list, orders: list):
        s = FASTLiteSession()
        ret = {}
        try:
            q = s.query(cls.rtm,
                        cls.business_type,
                        func.sum(cls.cq_total).label('cq_total'),
                        func.sum(cls.cq_w1).label('cq_w1'),
                        func.sum(cls.cq_w2).label('cq_w2'),
                        func.sum(cls.cq_w3).label('cq_w3'),
                        func.sum(cls.cq_w4).label('cq_w4'),
                        func.sum(cls.cq_w5).label('cq_w5'),
                        func.sum(cls.cq_w6).label('cq_w6'),
                        func.sum(cls.cq_w7).label('cq_w7'),
                        func.sum(cls.cq_w8).label('cq_w8'),
                        func.sum(cls.cq_w9).label('cq_w9'),
                        func.sum(cls.cq_w10).label('cq_w10'),
                        func.sum(cls.cq_w11).label('cq_w11'),
                        func.sum(cls.cq_w12).label('cq_w12'),
                        func.sum(cls.cq_w13).label('cq_w13'),
                        func.sum(cls.cq_w14).label('cq_w14'),
                        func.sum(cls.nq_w1).label('nq_w1'),
                        func.sum(cls.nq_w2).label('nq_w2'),
                        func.sum(cls.nq_w3).label('nq_w3'),
                        func.sum(cls.nq_w4).label('nq_w4'),
                        func.sum(cls.nq_w5).label('nq_w5'),)\
                .filter(cls.week_date == fiscal_qtr_week_name)
            if lob is not None and lob.strip() != '':
                q = q.filter(cls.lob == lob)
            if model is not None and len(model) != 0:
                q = q.filter(cls.model.in_(model))
            if sku is not None and len(sku) != 0:
                q = q.filter(cls.sku.in_(sku))
                
            q = q.group_by(cls.rtm,
                           cls.business_type)\
                        .order_by(cls.business_type)
            data_list = q.all()
            weeks_info = get_weeks_info(fiscal_qtr_week_name)
            data_structure = {}
            for i in data_list:
                basic_data = gen_basic_data(i, weeks_info.get('total_weeks'))
                data_structure = {
                    "rtm": '',
                    "business_type": i['business_type'],
                    "sold_to": "All",
                    **basic_data
                }
                if i['rtm'] not in ret.keys():
                    ret[i['rtm']] = [data_structure]
                else:
                    temp = ret[i['rtm']]
                    temp.append(data_structure)
                    ret[i['rtm']] = temp
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

    @classmethod
    def get_details_by_sold_to(cls, fiscal_qtr_week_name: str, lob: str, model: list, sku: list, orders: list):
        s = FASTLiteSession()
        ret = {}
        try:
            q = s.query(cls.rtm,
                        cls.business_type,
                        cls.sold_to_name,
                        func.sum(cls.cq_total).label('cq_total'),
                        func.sum(cls.cq_w1).label('cq_w1'),
                        func.sum(cls.cq_w2).label('cq_w2'),
                        func.sum(cls.cq_w3).label('cq_w3'),
                        func.sum(cls.cq_w4).label('cq_w4'),
                        func.sum(cls.cq_w5).label('cq_w5'),
                        func.sum(cls.cq_w6).label('cq_w6'),
                        func.sum(cls.cq_w7).label('cq_w7'),
                        func.sum(cls.cq_w8).label('cq_w8'),
                        func.sum(cls.cq_w9).label('cq_w9'),
                        func.sum(cls.cq_w10).label('cq_w10'),
                        func.sum(cls.cq_w11).label('cq_w11'),
                        func.sum(cls.cq_w12).label('cq_w12'),
                        func.sum(cls.cq_w13).label('cq_w13'),
                        func.sum(cls.cq_w14).label('cq_w14'),
                        func.sum(cls.nq_w1).label('nq_w1'),
                        func.sum(cls.nq_w2).label('nq_w2'),
                        func.sum(cls.nq_w3).label('nq_w3'),
                        func.sum(cls.nq_w4).label('nq_w4'),
                        func.sum(cls.nq_w5).label('nq_w5'),)\
                .filter(cls.week_date == fiscal_qtr_week_name)
            if lob is not None and lob.strip() != '':
                q = q.filter(or_(cls.lob == lob,cls.lob == None))
            if model is not None and len(model) != 0:
                q = q.filter(or_(cls.model.in_(model), cls.model == None))
            if sku is not None and len(sku) != 0:
                q = q.filter(or_(cls.sku.in_(sku), cls.model == None))
                
            q = q.group_by(cls.rtm,
                           cls.business_type,
                           cls.sold_to_name)\
                        .order_by(func.cast(cls.sold_to_id, Integer))
            data_list = q.all()
            data_structure = {}
            weeks_info = get_weeks_info(fiscal_qtr_week_name)
            for i in data_list:
                basic_data = gen_basic_data(i, weeks_info.get('total_weeks'))
                data_structure = {
                    "rtm": '',
                    "business_type": '',
                    "sold_to": i['sold_to_name'],
                    **basic_data
                }
                business_type_data = {}
                if i['rtm'] not in ret.keys():
                    if i['business_type'] not in business_type_data.keys():
                        business_type_data[i['business_type']] = [data_structure]
                    else:
                        biz_temp = business_type_data[i['business_type']]
                        biz_temp.append(data_structure)
                        business_type_data[i['business_type']] = biz_temp
                    ret[i['rtm']] = business_type_data
                else:
                    business_type_data = ret[i['rtm']]
                    if i['business_type'] not in business_type_data.keys():
                        business_type_data[i['business_type']] = [data_structure]
                    else:
                        biz_temp = business_type_data[i['business_type']]
                        biz_temp.append(data_structure)
                        business_type_data[i['business_type']] = biz_temp
                    ret[i['rtm']] = business_type_data
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

    @classmethod
    def gen_data_by_week(self, fiscal_qtr_week_name: str):
        s = FASTLiteSession()
        try:
            if fiscal_qtr_week_name is None:
                return ''
            new_data = s.execute(f'''
                        SELECT week_date,rtm,business_type, cust_id as sold_to_id, abbre as sold_to_name,lob,model,sku,week_in_fiscal_quarter, total_weeks_in_quarter, (ifnull(cq_w1,0)+ifnull(cq_w2,0)+ifnull(cq_w3,0)+ifnull(cq_w4,0)+ifnull(cq_w5,0)+ifnull(cq_w6,0)+ifnull(cq_w7,0)+ifnull(cq_w8,0)+ifnull(cq_w9,0)+ifnull(cq_w10,0)+ifnull(cq_w11,0)+ifnull(cq_w12,0)+ifnull(cq_w13,0)+ifnull(cq_w14,0)) AS cq_total, cq_w1, cq_w2, cq_w3, cq_w4, cq_w5, cq_w6, cq_w7, cq_w8, cq_w9, cq_w10, cq_w11, cq_w12, cq_w13, cq_w14, nq_w1, nq_w2, nq_w3, nq_w4, nq_w5, nq_w6, nq_w7, nq_w8, nq_w9, nq_w10, nq_w11, nq_w12, nq_w13, nq_w14 FROM 
                        (SELECT a.week_date, a.rtm, a.cust_id, d.business_type, d.abbre, a.lob, a.model, a.sku, c.wifq AS week_in_fiscal_quarter, c.total_weeks_in_quarter, max(CASE WHEN a.fiscal_quarter = c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=1 THEN fcst ELSE NULL END) cq_w1, max(CASE WHEN a.fiscal_quarter = c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=2 THEN fcst ELSE NULL END) cq_w2, max(CASE WHEN a.fiscal_quarter = c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=3 THEN fcst ELSE NULL END) cq_w3, max(CASE WHEN a.fiscal_quarter = c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=4 THEN fcst ELSE NULL END) cq_w4, max(CASE WHEN a.fiscal_quarter = c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=5 THEN fcst ELSE NULL END) cq_w5, max(CASE WHEN a.fiscal_quarter = c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=6 THEN fcst ELSE NULL END) cq_w6, max(CASE WHEN a.fiscal_quarter = c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=7 THEN fcst ELSE NULL END) cq_w7, max(CASE WHEN a.fiscal_quarter = c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=8 THEN fcst ELSE NULL END) cq_w8, max(CASE WHEN a.fiscal_quarter = c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=9 THEN fcst ELSE NULL END) cq_w9, max(CASE WHEN a.fiscal_quarter = c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=10 THEN fcst ELSE NULL END) cq_w10, max(CASE WHEN a.fiscal_quarter = c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=11 THEN fcst ELSE NULL END) cq_w11, max(CASE WHEN a.fiscal_quarter = c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=12 THEN fcst ELSE NULL END) cq_w12, max(CASE WHEN a.fiscal_quarter = c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=13 THEN fcst ELSE NULL END) cq_w13, max(CASE WHEN a.fiscal_quarter = c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=14 THEN fcst ELSE NULL END) cq_w14, max(CASE WHEN a.fiscal_quarter > c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=1 THEN fcst ELSE NULL END) nq_w1, max(CASE WHEN a.fiscal_quarter > c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=2 THEN fcst ELSE NULL END) nq_w2, max(CASE WHEN a.fiscal_quarter > c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=3 THEN fcst ELSE NULL END) nq_w3, max(CASE WHEN a.fiscal_quarter > c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=4 THEN fcst ELSE NULL END) nq_w4, max(CASE WHEN a.fiscal_quarter > c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=5 THEN fcst ELSE NULL END) nq_w5, max(CASE WHEN a.fiscal_quarter > c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=6 THEN fcst ELSE NULL END) nq_w6, max(CASE WHEN a.fiscal_quarter > c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=7 THEN fcst ELSE NULL END) nq_w7, max(CASE WHEN a.fiscal_quarter > c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=8 THEN fcst ELSE NULL END) nq_w8, max(CASE WHEN a.fiscal_quarter > c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=9 THEN fcst ELSE NULL END) nq_w9, max(CASE WHEN a.fiscal_quarter > c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=10 THEN fcst ELSE NULL END) nq_w10, max(CASE WHEN a.fiscal_quarter > c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=11 THEN fcst ELSE NULL END) nq_w11, max(CASE WHEN a.fiscal_quarter > c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=12 THEN fcst ELSE NULL END) nq_w12, max(CASE WHEN a.fiscal_quarter > c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=13 THEN fcst ELSE NULL END) nq_w13, max(CASE WHEN a.fiscal_quarter > c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=14 THEN fcst ELSE NULL END) nq_w14
                        FROM (SELECT * FROM app_fast_forecast_rtm_so_wa WHERE week_date = '{fiscal_qtr_week_name}') AS a 
                        LEFT JOIN (SELECT week_in_fiscal_quarter, fiscal_week_year AS bf FROM fiscal_week_year) AS b ON a.show_week = b.bf 
                        LEFT JOIN (SELECT week_in_fiscal_quarter AS wifq, total_weeks_in_quarter, fiscal_quarter AS current_week_fiscal_quarter, fiscal_week_year AS cf FROM fiscal_week_year) AS c ON a.fiscal_week_year = c.cf 
                        RIGHT JOIN dim_fast_business_soldto_mapping AS d ON a.cust_id = d.sold_to_id 
                        GROUP BY a.week_date,a.rtm,a.cust_id,a.lob, a.model, a.sku, a.mpn_id, d.business_type, d.abbre) as d;
                      ''')
            data_list = new_data.fetchall()
            results = [dict(item) for item in data_list]
            s.execute(LiteNewAppFastForecastTrmSoWa.__table__.insert(), results)
            s.commit()
            # 插入列表页中对应的周信息
            get_fiscal_year_week = s.execute(f"SELECT fiscal_week_year FROM app_fast_forecast_rtm_so_wa WHERE week_date='{fiscal_qtr_week_name}' LIMIT 1;")
            fiscal_year_week = get_fiscal_year_week.fetchone()
            if fiscal_year_week is not None:
                fcst = FastCPFForecastWeekList(fiscal_qtr_week_name, fiscal_year_week[0])
                fcst.insert_week_data()
            return f"{fiscal_qtr_week_name}: insert new data [ {len(results)} to [ {LiteNewAppFastForecastTrmSoWa.__tablename__} ].{DOUBLE_LINE_FEED}"
        except Exception as e:
            logger.exception(str(e))
            return ''
        finally:
            s.close() 


class NewFiscalWeekYear(FASTLiteBase):
    __tablename__ = 'fiscal_week_year'
    __table_args__ = {'schema': 'fast_lite'}
    
    region_cd = Column(String(256), primary_key=True)
    type_cd = Column(String(256), primary_key=True)
    
    fiscal_dt = Column(String(256), primary_key=True)
    fiscal_ts = Column(String(256))
    day_name = Column(String(256))
    day_in_fiscal_week = Column(Integer)
    fiscal_week_year = Column(Integer)
    fiscal_year = Column(Integer)
    fiscal_quarter = Column(Integer)
    fiscal_period = Column(Integer)
    week_in_fiscal_quarter = Column(Integer)
    fiscal_period_name = Column(String(256))
    fiscal_qtr_year_name = Column(String(256))
    fiscal_week = Column(Integer)
    week_begin_dt = Column(String(256))
    week_end_dt = Column(String(256))
    period_begin_dt = Column(String(256))
    period_end_dt = Column(String(256))
    total_weeks_in_period = Column(Integer)
    quarter_begin_dt = Column(String(256))
    quarter_end_dt = Column(String(256))
    total_weeks_in_quarter = Column(Integer)
    
    @classmethod
    def bulk_save(cls, objs: list):
        s = FASTLiteSession()
        try:
            # data_length = len(objs)
            # step_size = 500
            # for i in range(ceil(data_length/step_size)):
            #     if data_length - i * step_size > step_size:
            #         s.bulk_save_objects(objs[i*step_size:(i+1)*step_size], return_defaults=True)
            #         s.commit()
            #         logger.info(f'insert into {step_size}.')
            #     else:
            #         s.bulk_save_objects(objs[i*step_size:data_length], return_defaults=True)
            #         s.commit()
            #         logger.info(f'insert into {data_length-i*step_size}.')
            s.execute(cls.__table__.insert(), objs)
            s.commit()
        except Exception as e:
            logger.exception(str(e))
        finally:
            s.close()


class NewAppFastForecastTrmSoWa(FASTLiteBase):
    __tablename__ = 'app_fast_forecast_rtm_so_wa'
    __table_args__ = {'schema': 'fast_lite'}
    
    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    rtm = Column(String(256), comment='')
    week_date = Column(String(256), comment='FY23Q1W12')
    fiscal_week_year = Column(Integer, comment='202312')
    fiscal_quarter = Column(Integer, comment='')
    fiscal_week = Column(Integer, comment='')
    week_begin_dt = Column(String(256), comment='')
    week_end_dt = Column(String(256), comment='')
    project_cd = Column(String(256), comment='')
    cust_id = Column(String(256), comment='')
    lob = Column(String(256), comment='')
    model = Column(String(256), comment='')
    sku = Column(String(256), comment='')
    fph4 = Column(String(256), comment='')
    mpn_id = Column(String(256), comment='')
    show_week = Column(Integer, comment='对应的是哪一周的数据')
    data_type = Column(String(256), comment='')
    fcst = Column(Integer, comment='')

    @classmethod
    def get_model_list(cls, lob, rtm, fiscal_week_year):
        s = FASTLiteSession()
        res = []
        try:
            query = s.query(cls.model).distinct() \
                .filter(cls.fiscal_week_year == fiscal_week_year)
            if lob is not None and len(lob) != 0:
                query = query.filter(cls.lob.in_(lob))
            query = query.order_by(desc(cls.model))
            res = query.all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return res
    
    @classmethod
    def bulk_save(cls, objs: list):
        s = FASTLiteSession()
        try:
            s.execute(cls.__table__.insert(), objs)
            s.commit()
        except Exception as e:
            logger.exception(str(e))
        finally:
            s.close()

    @classmethod
    def get_fiscal_year_quarter_week_name(cls, page_num: int, page_size: int):
        s = FASTLiteSession()
        ret = []
        count = 0
        try:
            
            q = s.query(distinct(cls.week_date))\
                .filter(cls.week_date != None)
            
            count = q.count()
            q = q.order_by(cls.fiscal_week_year.desc())
            
            if page_num is not None and page_size is not None:
                q = q.limit(page_size).offset(page_num)
                
            ret = q.all()
            
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret, count

    @classmethod
    def gen_version1_data_by_week(cls, fiscal_qtr_week_name: str, version: int = 1):
        query_and_insert_forecast_multi_version(fiscal_qtr_week_name, cls.__tablename__, version)

    @classmethod
    def check_current_week_data(cls, week_data: str):
        s = FASTLiteSession()
        count = 0
        try:
            count = s.query(func.count(cls.id))\
                .filter(cls.week_date == week_data)\
                .scalar()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return count

    @classmethod
    def download_first_version_data(cls, fiscal_week_year: int, fiscal_qtr_week_name: str, model:list):
        s = FASTLiteSession()
        ret = []
        try:
            
            sub_query_mapping = generate_sold_to_sub_query(s, NewDimFastBusinessSoldtoMapping, fiscal_week_year)
            
            q = s.query(cls.week_date.label('Week_Date'),
                        cls.rtm.label('RTM'),
                        cls.cust_id.label('Sold-to ID'),
                        sub_query_mapping.c.business_type.label('Business Type'),
                        sub_query_mapping.c.sold_to_name_en.label('Sold-to Name'),
                        sub_query_mapping.c.abbre.label('Abbre.'),
                        cls.lob.label('LOB'),
                        cls.model.label('Model'),
                        cls.fph4.label('FPH4'),
                        cls.project_cd.label('Project Code'),
                        cls.sku.label('SKU'),
                        cls.mpn_id.label('MPN'),
                        cls.fiscal_quarter.label('Fiscal_Quarter'),
                        cls.show_week.label('Fiscal_Week'),
                        case([(and_(or_(cls.rtm == 'ENT', cls.rtm == 'EDU'), cls.data_type == 'fcst'), None), ], else_=cls.fcst).label('Fcst')
                        )\
                .filter(cls.week_date == fiscal_qtr_week_name)\
                .filter(cls.data_type == 'fcst')\
                .filter(or_(cls.model.in_(model), cls.model == None))\
                .outerjoin(cls,
                      sub_query_mapping.c.sold_to_id == cls.cust_id)\
                .order_by(cls.rtm, 
                          sub_query_mapping.c.business_type,
                          cls.cust_id,
                          sub_query_mapping.c.abbre,
                          cls.lob,
                          cls.model
                          )
            
            ret = pd.read_sql_query(q.statement, fast_lite_engine)
            
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret
    
    @classmethod
    def download_second_version_data(cls, fiscal_week_year: int, fiscal_qtr_week_name: str,model:list):
        s = FASTLiteSession()
        ret = []
        try:
            sub_query_no_mono = s.query(
                                        cls.rtm.label("rtm"),
                                        cls.week_date.label("week_date"),
                                        cls.fiscal_week_year.label("fiscal_week_year"),
                                        cls.fiscal_quarter.label("fiscal_quarter"),
                                        cls.fiscal_week.label("fiscal_week"),
                                        cls.week_begin_dt.label("week_begin_dt"),
                                        cls.week_end_dt.label("week_end_dt"),
                                        cls.project_cd.label("project_cd"),
                                        cls.cust_id.label("cust_id"),
                                        cls.lob.label("lob"),
                                        cls.model.label("model"),
                                        cls.sku.label("sku"),
                                        cls.fph4.label("fph4"),
                                        cls.mpn_id.label("mpn_id"),
                                        cls.show_week.label("show_week"),
                                        cls.data_type.label("data_type"),
                                        cls.fcst.label("fcst")
                                        )\
                .filter(cls.week_date == fiscal_qtr_week_name)\
                .filter(~cls.rtm.in_(MONO_RTM))
                
            sub_query_mono = s.query(
                                    AppFastForecastMonoAdvanceWa.rtm.label("rtm"),
                                    AppFastForecastMonoAdvanceWa.week_date.label("week_date"),
                                    AppFastForecastMonoAdvanceWa.fiscal_week_year.label("fiscal_week_year"),
                                    AppFastForecastMonoAdvanceWa.fiscal_quarter.label("fiscal_quarter"),
                                    AppFastForecastMonoAdvanceWa.fiscal_week.label("fiscal_week"),
                                    AppFastForecastMonoAdvanceWa.week_begin_dt.label("week_begin_dt"),
                                    AppFastForecastMonoAdvanceWa.week_end_dt.label("week_end_dt"),
                                    AppFastForecastMonoAdvanceWa.project_cd.label("project_cd"),
                                    AppFastForecastMonoAdvanceWa.cust_id.label("cust_id"),
                                    AppFastForecastMonoAdvanceWa.lob.label("lob"),
                                    AppFastForecastMonoAdvanceWa.model.label("model"),
                                    AppFastForecastMonoAdvanceWa.sku.label("sku"),
                                    AppFastForecastMonoAdvanceWa.fph4.label("fph4"),
                                    AppFastForecastMonoAdvanceWa.mpn_id.label("mpn_id"),
                                    AppFastForecastMonoAdvanceWa.show_week.label("show_week"),
                                    AppFastForecastMonoAdvanceWa.data_type.label("data_type"),
                                    AppFastForecastMonoAdvanceWa.fcst.label("fcst")
                                     )\
                .filter(AppFastForecastMonoAdvanceWa.week_date == fiscal_qtr_week_name)
            
            forecast_second_union = sub_query_no_mono.union_all(sub_query_mono).cte()
            
            sub_query_mapping = generate_sold_to_sub_query(s, NewDimFastBusinessSoldtoMapping, fiscal_week_year)
               
            q = s.query(forecast_second_union.c.week_date.label('Week_Date'),
                        forecast_second_union.c.rtm.label('RTM'),
                        forecast_second_union.c.cust_id.label('Sold-to ID'),
                        sub_query_mapping.c.business_type.label('Business Type'),
                        sub_query_mapping.c.sold_to_name_en.label('Sold-to Name'),
                        sub_query_mapping.c.abbre.label('Abbre.'),
                        forecast_second_union.c.lob.label('LOB'),
                        forecast_second_union.c.model.label('Model'),
                        forecast_second_union.c.fph4.label('FPH4'),
                        forecast_second_union.c.project_cd.label('Project Code'),
                        forecast_second_union.c.sku.label('SKU'),
                        forecast_second_union.c.mpn_id.label('MPN'),
                        forecast_second_union.c.fiscal_quarter.label('Fiscal_Quarter'),
                        forecast_second_union.c.show_week.label('Fiscal_Week'),
                        case([(and_(or_(forecast_second_union.c.rtm == 'ENT', 
                                             forecast_second_union.c.rtm == 'EDU'), 
                                         forecast_second_union.c.data_type == 'fcst'), None), ], 
                                  else_=forecast_second_union.c.fcst).label('Fcst')
                        )\
                .filter(forecast_second_union.c.week_date == fiscal_qtr_week_name)\
                .filter(forecast_second_union.c.data_type == 'fcst')\
                .filter(or_(forecast_second_union.c.model.in_(model), forecast_second_union.c.model == None))\
                .outerjoin(forecast_second_union,
                           sub_query_mapping.c.sold_to_id == forecast_second_union.c.cust_id)\
                .order_by(forecast_second_union.c.rtm, 
                          sub_query_mapping.c.business_type,
                          forecast_second_union.c.cust_id,
                          sub_query_mapping.c.abbre,
                          forecast_second_union.c.lob,
                          forecast_second_union.c.model
                          )
            
            ret = pd.read_sql_query(q.statement, fast_lite_engine)
            
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret


class NewDimFastBusinessSoldtoMapping(FASTLiteBase):
    __tablename__ = 'dim_fast_business_soldto_mapping'
    __table_args__ = {'schema': 'fast_lite'}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    rtm = Column(String(256), comment='')
    business_type = Column(String(256), comment='')
    sold_to_id = Column(String(256), comment='')
    sold_to_name_en = Column(String(256), comment='')
    abbre = Column(String(256), comment='')
    start_week = Column(Integer)
    end_week = Column(Integer)
    create_time = Column(DateTime, comment='create time')
    update_time = Column(DateTime, comment='update time')

    @classmethod
    def bulk_save(cls, objs: list):
        s = FASTLiteSession()
        try:
            s.execute(cls.__table__.insert(), objs)
            s.commit()
        except Exception as e:
            logger.exception(str(e))
        finally:
            s.close()
    

class FastCPFForecastWeekList(FASTLiteBase):
    __tablename__ = 'fast_cpf_forecast_week_list'
    __table_args__ = {'schema': 'fast_lite'}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    week_date = Column(String(256), unique=True, comment='FY23Q2W1')
    fiscal_week_year = Column(Integer, comment='202312')
    create_time = Column(DateTime, default=datetime.now, comment='create time')
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='update time')

    def __init__(self,  week_date: str, fiscal_week_year: int):
        self.week_date = week_date
        self.fiscal_week_year = fiscal_week_year
    
    def insert_week_data(self):
        s = FASTLiteSession()
        try:
            s.add(self)
            s.commit()
        except IntegrityError as e:
            logger.info(f"{self.week_date} has already exist in {self.__tablename__}.")
        finally:
            s.close()
            
    @classmethod
    def get_week_list(cls, page_num: int, page_size: int):
        s = FASTLiteSession()
        ret = []
        count = 0
        try:
            q = s.query(cls.week_date, cls.fiscal_week_year).order_by(cls.fiscal_week_year.desc())
            
            count = q.count()
            
            if page_num is not None and page_size is not None:
                q = q.limit(page_size).offset(page_num)
            
            ret = q.all()
            
        except Exception as e:
            logger.exception(str(e))
        finally:
            s.close()
        return ret, count

    @classmethod
    def check_week_list(cls, week_date: str):
        s = FASTLiteSession()
        count = 0
        try:
            count = s.query(func.count(cls.id))\
                .filter(cls.week_date == week_date)\
                .scalar()
            
        except Exception as e:
            logger.exception(str(e))
        finally:
            s.close()
        return count
    
    
class AppFastForecastMultiVersion(FASTLiteBase):
    __tablename__ = 'app_fast_forecast_multi_version'
    __table_args__ = {'schema': 'fast_lite'}
    
    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    week_date = Column(String(256), comment='FY23Q1W12')
    fiscal_week_year = Column(Integer, comment='202312')
    rtm = Column(String(256), comment='')
    sold_to_id = Column(String(256), comment='')
    project_cd = Column(String(256), comment='')
    mpn_id = Column(String(256), comment='')
    lob = Column(String(256), comment='')
    model = Column(String(256), comment='')
    sku = Column(String(256), comment='')
    fph4 = Column(String(256), comment='')
    week_in_fiscal_quarter = Column(Integer, comment='')
    total_weeks_in_quarter = Column(Integer, comment='')
    cq_total = Column(Integer, comment='')
    cq_w1 = Column(Integer, comment='')
    cq_w2 = Column(Integer, comment='')
    cq_w3 = Column(Integer, comment='')
    cq_w4 = Column(Integer, comment='')
    cq_w5 = Column(Integer, comment='')
    cq_w6 = Column(Integer, comment='')
    cq_w7 = Column(Integer, comment='')
    cq_w8 = Column(Integer, comment='')
    cq_w9 = Column(Integer, comment='')
    cq_w10 = Column(Integer, comment='')
    cq_w11 = Column(Integer, comment='')
    cq_w12 = Column(Integer, comment='')
    cq_w13 = Column(Integer, comment='')
    cq_w14 = Column(Integer, comment='')
    nq_w1 = Column(Integer, comment='')
    nq_w2 = Column(Integer, comment='')
    nq_w3 = Column(Integer, comment='')
    nq_w4 = Column(Integer, comment='')
    nq_w5 = Column(Integer, comment='')
    nq_w6 = Column(Integer, comment='')
    nq_w7 = Column(Integer, comment='')
    nq_w8 = Column(Integer, comment='')
    nq_w9 = Column(Integer, comment='')
    nq_w10 = Column(Integer, comment='')
    nq_w11 = Column(Integer, comment='')
    nq_w12 = Column(Integer, comment='')
    nq_w13 = Column(Integer, comment='')
    nq_w14 = Column(Integer, comment='')
    business_version =Column(Integer, comment='forecast区分不同版本, 1:上午版, 2:下午版.')
    create_time = Column(DateTime, default=datetime.now, comment='create time')
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='update time')

    @classmethod
    def check_data_by_week_version(cls, fiscal_qtr_week_name: str, version: int):
        s = FASTLiteSession()
        count = 0
        try:
            count = s.query(func.count(cls.id))\
                .filter(cls.week_date == fiscal_qtr_week_name)\
                .filter(cls.business_version == version)\
                .scalar()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return count

    @classmethod
    def get_latest_version(cls, fiscal_qtr_week_name) -> tuple[int, int]:
        s = FASTLiteSession()
        no_mono_version, mono_version = 0, 0
        try:
            latest_version = s.query(func.max(cls.business_version))\
                .filter(cls.week_date == fiscal_qtr_week_name)\
                .scalar()
            if latest_version == FORECAST_SECOND_VERSION:
                no_mono_version = FORECAST_FIRST_VERSION
            else:
                no_mono_version = latest_version
                
            mono_version = latest_version
            
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return no_mono_version, mono_version

    @classmethod
    def get_details_total(cls, fiscal_qtr_week_name: str, lob: str, model: list, sku: list, orders: list, no_mono_version: int, mono_version: int):
        s = FASTLiteSession()
        ret = []
        try:
            weeks_info = get_weeks_info(fiscal_qtr_week_name)
            q = s.query(func.sum(cls.cq_total).label('cq_total'),
                        func.sum(cls.cq_w1).label('cq_w1'),
                        func.sum(cls.cq_w2).label('cq_w2'),
                        func.sum(cls.cq_w3).label('cq_w3'),
                        func.sum(cls.cq_w4).label('cq_w4'),
                        func.sum(cls.cq_w5).label('cq_w5'),
                        func.sum(cls.cq_w6).label('cq_w6'),
                        func.sum(cls.cq_w7).label('cq_w7'),
                        func.sum(cls.cq_w8).label('cq_w8'),
                        func.sum(cls.cq_w9).label('cq_w9'),
                        func.sum(cls.cq_w10).label('cq_w10'),
                        func.sum(cls.cq_w11).label('cq_w11'),
                        func.sum(cls.cq_w12).label('cq_w12'),
                        func.sum(cls.cq_w13).label('cq_w13'),
                        func.sum(cls.cq_w14).label('cq_w14'),
                        func.sum(cls.nq_w1).label('nq_w1'),
                        func.sum(cls.nq_w2).label('nq_w2'),
                        func.sum(cls.nq_w3).label('nq_w3'),
                        func.sum(cls.nq_w4).label('nq_w4'),
                        func.sum(cls.nq_w5).label('nq_w5'),)\
                .filter(cls.week_date == fiscal_qtr_week_name)
            # 获取上周的数据
            last_fiscal_qtr_week_name = weeks_info.get('last_fiscal_qtr_week_name')
            last_week_data_list = []
            if last_fiscal_qtr_week_name != '':
                lq = s.query(last_week_sum_fields.get(f"cq_w{weeks_info.get('last_week_in_quarter')}"))\
                    .filter(cls.week_date == last_fiscal_qtr_week_name)
                if no_mono_version < mono_version:
                    lq = lq.filter(or_(and_(~cls.rtm.in_(MONO_RTM), cls.business_version == no_mono_version),
                                    and_(cls.rtm.in_(MONO_RTM), cls.business_version == mono_version)))
                else:
                    lq = lq.filter(cls.business_version == no_mono_version)
                    
                if lob is not None and lob.strip() != '':
                    lq = lq.filter(cls.lob == lob)
                if model is not None and len(model) != 0:
                    lq = lq.filter(cls.model.in_(model))
                if sku is not None and len(sku) != 0:
                    lq = lq.filter(cls.sku.in_(sku))
                # [(Decimal('1648557'),)]
                last_week_data_list = lq.all()
                
            if no_mono_version < mono_version:
                q = q.filter(or_(and_(~cls.rtm.in_(MONO_RTM), cls.business_version == no_mono_version),
                                and_(cls.rtm.in_(MONO_RTM), cls.business_version == mono_version)))
            else:
                q = q.filter(cls.business_version == no_mono_version)
                
            if lob is not None and lob.strip() != '':
                q = q.filter(cls.lob == lob)
            if model is not None and len(model) != 0:
                q = q.filter(cls.model.in_(model))
            if sku is not None and len(sku) != 0:
                q = q.filter(cls.sku.in_(sku))
                
            # 需要进行统计总数,以及分 RTM/business_type 的汇总
            # q = q.group_by(cls.week_date)
            data_list = q.all()
            data_structure = {}
            dynamic_key= []
            fixed_dict = {
                "rtm": 'RTM Model Total',
                "business_type": '',
                "sold_to": "",
            }
            total_weeks = weeks_info.get('total_weeks')
            dynamic_key = gen_dynamic_key(total_weeks)
            data_structure = {}
            basic_data = {}
            if len(data_list) > 0:
                basic_data = gen_basic_data(data_list[0], total_weeks)
            data_structure = {
                **fixed_dict,
                **basic_data
            }
            if len(last_week_data_list) > 0:
                if last_week_data_list[0][0] is not None:
                    data_structure['*CW-1'] = int(last_week_data_list[0][0])
                else:
                    data_structure['*CW-1'] = last_week_data_list[0][0]
                    
            ret.append(data_structure)
            # for i in data_list:
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret, list(fixed_dict.keys())+dynamic_key

    @classmethod
    def get_title(cls, fiscal_qtr_week_name: str):
        s = FASTLiteSession()
        titles = []
        try:
            weeks_info = get_weeks_info(fiscal_qtr_week_name)
            total_weeks = weeks_info.get('total_weeks')
            week_in_current_quarter = weeks_info.get('current_week')
            fiscal_year_quarter = weeks_info.get('fiscal_year_quarter')
            last_fiscal_qtr_week_name = weeks_info.get('last_fiscal_qtr_week_name')
            next_fiscal_year_quarter = weeks_info.get('next_fiscal_year_quarter')

            # 固定标题
            fixed_title = ['Model / RTM', 'Business Type', 'Sold-to']
            # 增加*cw-1, 取上周当周的数据
            special_title = []
            if last_fiscal_qtr_week_name != '':
                special_title.append(f'*CW-1\n{last_fiscal_qtr_week_name}')
            # 季度汇总
            quarter_total = [f"CQ Total\n{fiscal_year_quarter}"]
            fiscal_weeks = [] 
            # 本季度所有周
            for k in range(total_weeks):
                week_difference = k+1-week_in_current_quarter
                if week_difference < 0:
                    fiscal_weeks.append(f"CW{week_difference}\n{fiscal_year_quarter}W{k+1}")
                elif week_difference == 0:
                    fiscal_weeks += special_title
                    fiscal_weeks.append(f"CW\n{fiscal_year_quarter}W{k+1}")
                else:
                    fiscal_weeks.append(f"CW+{week_difference}\n{fiscal_year_quarter}W{k+1}")
            # 下季度5周
            for n in range(5):
                fiscal_weeks.append(f"CW+{n+1+total_weeks-week_in_current_quarter}\n{next_fiscal_year_quarter}W{n+1}")
            titles = fixed_title+quarter_total+fiscal_weeks

        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return titles

    @classmethod
    def get_details_by_rtm(cls, fiscal_qtr_week_name: str, lob: str, model: list, sku: list, orders: list, no_mono_version: int, mono_version: int):
        s = FASTLiteSession()
        ret = {}
        try:
            weeks_info = get_weeks_info(fiscal_qtr_week_name)
            q = s.query(cls.rtm,
                        func.sum(cls.cq_total).label('cq_total'),
                        func.sum(cls.cq_w1).label('cq_w1'),
                        func.sum(cls.cq_w2).label('cq_w2'),
                        func.sum(cls.cq_w3).label('cq_w3'),
                        func.sum(cls.cq_w4).label('cq_w4'),
                        func.sum(cls.cq_w5).label('cq_w5'),
                        func.sum(cls.cq_w6).label('cq_w6'),
                        func.sum(cls.cq_w7).label('cq_w7'),
                        func.sum(cls.cq_w8).label('cq_w8'),
                        func.sum(cls.cq_w9).label('cq_w9'),
                        func.sum(cls.cq_w10).label('cq_w10'),
                        func.sum(cls.cq_w11).label('cq_w11'),
                        func.sum(cls.cq_w12).label('cq_w12'),
                        func.sum(cls.cq_w13).label('cq_w13'),
                        func.sum(cls.cq_w14).label('cq_w14'),
                        func.sum(cls.nq_w1).label('nq_w1'),
                        func.sum(cls.nq_w2).label('nq_w2'),
                        func.sum(cls.nq_w3).label('nq_w3'),
                        func.sum(cls.nq_w4).label('nq_w4'),
                        func.sum(cls.nq_w5).label('nq_w5'),)\
                .filter(cls.week_date == fiscal_qtr_week_name)

            if no_mono_version < mono_version:
                q = q.filter(or_(and_(~cls.rtm.in_(MONO_RTM), cls.business_version == no_mono_version),
                                and_(cls.rtm.in_(MONO_RTM), cls.business_version == mono_version)))
            else:
                q = q.filter(cls.business_version == no_mono_version)
                
            if lob is not None and lob.strip() != '':
                q = q.filter(cls.lob == lob)
            if model is not None and len(model) != 0:
                q = q.filter(cls.model.in_(model))
            if sku is not None and len(sku) != 0:
                q = q.filter(cls.sku.in_(sku))
            # 需要进行统计总数,以及分 RTM/business_type 的汇总
            q = q.group_by(cls.rtm)
            data_list = q.all()
            
            # 获取上周的数据
            last_fiscal_qtr_week_name = weeks_info.get('last_fiscal_qtr_week_name')
            no_mono_version_lw, mono_version_lw = AppFastForecastMultiVersion.get_latest_version(last_fiscal_qtr_week_name)
            last_week_data_dict = {}
            if last_fiscal_qtr_week_name != '':
                lq = s.query(cls.rtm,
                             last_week_sum_fields.get(f"cq_w{weeks_info.get('last_week_in_quarter')}"))\
                    .filter(cls.week_date == last_fiscal_qtr_week_name)
                if no_mono_version_lw < mono_version_lw:
                    lq = lq.filter(or_(and_(~cls.rtm.in_(MONO_RTM), cls.business_version == no_mono_version_lw),
                                    and_(cls.rtm.in_(MONO_RTM), cls.business_version == mono_version_lw)))
                else:
                    lq = lq.filter(cls.business_version == no_mono_version_lw)
                    
                if lob is not None and lob.strip() != '':
                    lq = lq.filter(cls.lob == lob)
                if model is not None and len(model) != 0:
                    lq = lq.filter(cls.model.in_(model))
                if sku is not None and len(sku) != 0:
                    lq = lq.filter(cls.sku.in_(sku))
                
                lq = lq.group_by(cls.rtm)
                # [(Decimal('1648557'),)]
                last_week_data_list = lq.all()
                for l in last_week_data_list:
                    if l[1] is not None:
                        last_week_data_dict[l[0]] = int(l[1])
                    else:
                        last_week_data_dict[l[0]] = l[1]

            data_structure = {}
            for i in data_list:
                basic_data = gen_basic_data(i, weeks_info.get('total_weeks'))
                data_structure = {
                    "rtm": i['rtm'],
                    "business_type": 'All',
                    "sold_to": 'All',
                    **basic_data
                }
                if len(last_week_data_dict) > 0:
                    if i['rtm'] in last_week_data_dict.keys():
                        data_structure['*CW-1'] = last_week_data_dict[i['rtm']]
                    else:
                        data_structure['*CW-1'] = None
                        
                ret[i['rtm']] = data_structure
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

    @classmethod
    def get_details_by_business_type(cls, fiscal_week_year: int, fiscal_qtr_week_name: str, lob: str, model: list, sku: list, orders: list, no_mono_version: int, mono_version: int):
        s = FASTLiteSession()
        ret = {}
        try:
            weeks_info = get_weeks_info(fiscal_qtr_week_name)
            sub_query_forecast = s.query(cls)\
                .filter(cls.week_date == fiscal_qtr_week_name)
            
            if no_mono_version < mono_version:
                sub_query_forecast = sub_query_forecast.filter(or_(and_(~cls.rtm.in_(MONO_RTM), cls.business_version == no_mono_version),
                                                                    and_(cls.rtm.in_(MONO_RTM), cls.business_version == mono_version)))
            else:
                sub_query_forecast = sub_query_forecast.filter(cls.business_version == no_mono_version)
                
            if lob is not None and lob.strip() != '':
                sub_query_forecast = sub_query_forecast.filter(cls.lob == lob)
            if model is not None and len(model) != 0:
                sub_query_forecast = sub_query_forecast.filter(cls.model.in_(model))
            if sku is not None and len(sku) != 0:
                sub_query_forecast = sub_query_forecast.filter(cls.sku.in_(sku))
            sub_query_forecast = sub_query_forecast.subquery()
            
            sub_query_mapping = generate_sold_to_sub_query(s, NewDimFastBusinessSoldtoMapping, fiscal_week_year)
            
            q = s.query(sub_query_mapping.c.rtm,
                        sub_query_mapping.c.business_type.label('business_type'),
                        func.sum(sub_query_forecast.c.cq_total).label('cq_total'),
                        func.sum(sub_query_forecast.c.cq_w1).label('cq_w1'),
                        func.sum(sub_query_forecast.c.cq_w2).label('cq_w2'),
                        func.sum(sub_query_forecast.c.cq_w3).label('cq_w3'),
                        func.sum(sub_query_forecast.c.cq_w4).label('cq_w4'),
                        func.sum(sub_query_forecast.c.cq_w5).label('cq_w5'),
                        func.sum(sub_query_forecast.c.cq_w6).label('cq_w6'),
                        func.sum(sub_query_forecast.c.cq_w7).label('cq_w7'),
                        func.sum(sub_query_forecast.c.cq_w8).label('cq_w8'),
                        func.sum(sub_query_forecast.c.cq_w9).label('cq_w9'),
                        func.sum(sub_query_forecast.c.cq_w10).label('cq_w10'),
                        func.sum(sub_query_forecast.c.cq_w11).label('cq_w11'),
                        func.sum(sub_query_forecast.c.cq_w12).label('cq_w12'),
                        func.sum(sub_query_forecast.c.cq_w13).label('cq_w13'),
                        func.sum(sub_query_forecast.c.cq_w14).label('cq_w14'),
                        func.sum(sub_query_forecast.c.nq_w1).label('nq_w1'),
                        func.sum(sub_query_forecast.c.nq_w2).label('nq_w2'),
                        func.sum(sub_query_forecast.c.nq_w3).label('nq_w3'),
                        func.sum(sub_query_forecast.c.nq_w4).label('nq_w4'),
                        func.sum(sub_query_forecast.c.nq_w5).label('nq_w5'),
                        )\
                .outerjoin(sub_query_forecast,
                           func.cast(sub_query_mapping.c.sold_to_id, Integer) == sub_query_forecast.c.sold_to_id)
            q = q.group_by(sub_query_mapping.c.rtm,
                           sub_query_mapping.c.business_type)
            data_list = q.all()
            
            # 获取上周的数据
            last_fiscal_qtr_week_name = weeks_info.get('last_fiscal_qtr_week_name')
            no_mono_version_lw, mono_version_lw = AppFastForecastMultiVersion.get_latest_version(last_fiscal_qtr_week_name)
            last_week_data_dict = {}
            if last_fiscal_qtr_week_name != '':
                last_sub_query_forecast = s.query(cls.rtm,
                                                  cls.sold_to_id.label('sold_to_id'),
                             last_week_fields.get(f"cq_w{weeks_info.get('last_week_in_quarter')}"))\
                                 .filter(cls.week_date == last_fiscal_qtr_week_name)
                
                if no_mono_version_lw < mono_version_lw:
                    last_sub_query_forecast = last_sub_query_forecast.filter(or_(and_(~cls.rtm.in_(MONO_RTM), cls.business_version == no_mono_version_lw),
                                                                        and_(cls.rtm.in_(MONO_RTM), cls.business_version == mono_version_lw)))
                else:
                    last_sub_query_forecast = last_sub_query_forecast.filter(cls.business_version == no_mono_version_lw)
                    
                if lob is not None and lob.strip() != '':
                    last_sub_query_forecast = last_sub_query_forecast.filter(cls.lob == lob)
                if model is not None and len(model) != 0:
                    last_sub_query_forecast = last_sub_query_forecast.filter(cls.model.in_(model))
                if sku is not None and len(sku) != 0:
                    last_sub_query_forecast = last_sub_query_forecast.filter(cls.sku.in_(sku))
                last_sub_query_forecast = last_sub_query_forecast.subquery()
                
                lq = s.query(sub_query_mapping.c.rtm,
                            sub_query_mapping.c.business_type.label('business_type'),
                            func.sum(last_sub_query_forecast.c[f"cq_w{weeks_info.get('last_week_in_quarter')}"]).label(f"cq_w{weeks_info.get('last_week_in_quarter')}"),
                            )\
                    .outerjoin(last_sub_query_forecast,
                            func.cast(sub_query_mapping.c.sold_to_id, Integer) == last_sub_query_forecast.c.sold_to_id)
                lq = lq.group_by(sub_query_mapping.c.rtm,
                            sub_query_mapping.c.business_type)
                # [(Decimal('1648557'),)]
                last_week_data_list = lq.all()
                # 因为不同的rtm会有相同的business_type,需要加上rtm前缀进行区分
                for l in last_week_data_list:
                    if l[2] is not None:
                        last_week_data_dict[f"{l[0]}_{l[1]}"] = int(l[2])
                    else:
                        last_week_data_dict[f"{l[0]}_{l[1]}"] = l[2]
            data_structure = {}
            for i in data_list:
                basic_data = gen_basic_data(i, weeks_info.get('total_weeks'))
                data_structure = {
                    "rtm": '',
                    "business_type": i['business_type'],
                    "sold_to": "All",
                    **basic_data
                }
                if len(last_week_data_dict) > 0:
                    if f"{i['rtm']}_{i['business_type']}" in last_week_data_dict.keys():
                        data_structure['*CW-1'] = last_week_data_dict[f"{i['rtm']}_{i['business_type']}"]
                    else:
                        data_structure['*CW-1'] = None
                if i['rtm'] not in ret.keys():
                    ret[i['rtm']] = [data_structure]
                else:
                    temp = ret[i['rtm']]
                    temp.append(data_structure)
                    ret[i['rtm']] = temp
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

    @classmethod
    def get_details_by_sold_to(cls, fiscal_week_year: int, fiscal_qtr_week_name: str, lob: str, model: list, sku: list, orders: list, no_mono_version: int, mono_version: int):
        s = FASTLiteSession()
        ret = {}
        try:
            weeks_info = get_weeks_info(fiscal_qtr_week_name)
            sub_query_forecast = s.query(cls)\
                .filter(cls.week_date == fiscal_qtr_week_name)
            
            if no_mono_version < mono_version:
                sub_query_forecast = sub_query_forecast.filter(or_(and_(~cls.rtm.in_(MONO_RTM), cls.business_version == no_mono_version),
                                                                    and_(cls.rtm.in_(MONO_RTM), cls.business_version == mono_version)))
            else:
                sub_query_forecast = sub_query_forecast.filter(cls.business_version == no_mono_version)
                
            if lob is not None and lob.strip() != '':
                sub_query_forecast = sub_query_forecast.filter(cls.lob == lob)
            if model is not None and len(model) != 0:
                sub_query_forecast = sub_query_forecast.filter(cls.model.in_(model))
            if sku is not None and len(sku) != 0:
                sub_query_forecast = sub_query_forecast.filter(cls.sku.in_(sku))
            sub_query_forecast = sub_query_forecast.subquery()
            
            sub_query_mapping = generate_sold_to_sub_query(s, NewDimFastBusinessSoldtoMapping, fiscal_week_year)
            
            q = s.query(sub_query_mapping.c.rtm,
                        sub_query_mapping.c.business_type.label('business_type'),
                        sub_query_mapping.c.abbre.label('sold_to_name'),
                        func.sum(sub_query_forecast.c.cq_total).label('cq_total'),
                        func.sum(sub_query_forecast.c.cq_w1).label('cq_w1'),
                        func.sum(sub_query_forecast.c.cq_w2).label('cq_w2'),
                        func.sum(sub_query_forecast.c.cq_w3).label('cq_w3'),
                        func.sum(sub_query_forecast.c.cq_w4).label('cq_w4'),
                        func.sum(sub_query_forecast.c.cq_w5).label('cq_w5'),
                        func.sum(sub_query_forecast.c.cq_w6).label('cq_w6'),
                        func.sum(sub_query_forecast.c.cq_w7).label('cq_w7'),
                        func.sum(sub_query_forecast.c.cq_w8).label('cq_w8'),
                        func.sum(sub_query_forecast.c.cq_w9).label('cq_w9'),
                        func.sum(sub_query_forecast.c.cq_w10).label('cq_w10'),
                        func.sum(sub_query_forecast.c.cq_w11).label('cq_w11'),
                        func.sum(sub_query_forecast.c.cq_w12).label('cq_w12'),
                        func.sum(sub_query_forecast.c.cq_w13).label('cq_w13'),
                        func.sum(sub_query_forecast.c.cq_w14).label('cq_w14'),
                        func.sum(sub_query_forecast.c.nq_w1).label('nq_w1'),
                        func.sum(sub_query_forecast.c.nq_w2).label('nq_w2'),
                        func.sum(sub_query_forecast.c.nq_w3).label('nq_w3'),
                        func.sum(sub_query_forecast.c.nq_w4).label('nq_w4'),
                        func.sum(sub_query_forecast.c.nq_w5).label('nq_w5'),
                        )\
                .outerjoin(sub_query_forecast,
                           func.cast(sub_query_mapping.c.sold_to_id, Integer) == sub_query_forecast.c.sold_to_id)
                
            q = q.group_by(sub_query_mapping.c.rtm,
                           sub_query_mapping.c.business_type,
                           sub_query_mapping.c.abbre)\
                        .order_by(func.cast(sub_query_forecast.c.sold_to_id, Integer))
            data_list = q.all()
            
            # 获取上周的数据
            last_fiscal_qtr_week_name = weeks_info.get('last_fiscal_qtr_week_name')
            no_mono_version_lw, mono_version_lw = AppFastForecastMultiVersion.get_latest_version(last_fiscal_qtr_week_name)
            last_week_data_dict = {}
            if last_fiscal_qtr_week_name != '':
                last_sub_query_forecast = s.query(cls.rtm,
                                                  cls.sold_to_id.label('sold_to_id'),
                             last_week_fields.get(f"cq_w{weeks_info.get('last_week_in_quarter')}"))\
                                 .filter(cls.week_date == last_fiscal_qtr_week_name)
                
                if no_mono_version_lw < mono_version_lw:
                    last_sub_query_forecast = last_sub_query_forecast.filter(or_(and_(~cls.rtm.in_(MONO_RTM), cls.business_version == no_mono_version_lw),
                                                                        and_(cls.rtm.in_(MONO_RTM), cls.business_version == mono_version_lw)))
                else:
                    last_sub_query_forecast = last_sub_query_forecast.filter(cls.business_version == no_mono_version_lw)
                    
                if lob is not None and lob.strip() != '':
                    last_sub_query_forecast = last_sub_query_forecast.filter(cls.lob == lob)
                if model is not None and len(model) != 0:
                    last_sub_query_forecast = last_sub_query_forecast.filter(cls.model.in_(model))
                if sku is not None and len(sku) != 0:
                    last_sub_query_forecast = last_sub_query_forecast.filter(cls.sku.in_(sku))
                last_sub_query_forecast = last_sub_query_forecast.subquery()
                
                lq = s.query(
                            sub_query_mapping.c.abbre,
                            func.sum(last_sub_query_forecast.c[f"cq_w{weeks_info.get('last_week_in_quarter')}"]).label(f"cq_w{weeks_info.get('last_week_in_quarter')}"),
                            )\
                    .outerjoin(last_sub_query_forecast,
                            func.cast(sub_query_mapping.c.sold_to_id, Integer) == last_sub_query_forecast.c.sold_to_id)

                lq = lq.group_by(sub_query_mapping.c.rtm,
                           sub_query_mapping.c.business_type,
                           sub_query_mapping.c.abbre)\
                        .order_by(func.cast(last_sub_query_forecast.c.sold_to_id, Integer))
                # [(Decimal('1648557'),)]
                last_week_data_list = lq.all()
                for l in last_week_data_list:
                    if l[1] is not None:
                        last_week_data_dict[l[0]] = int(l[1])
                    else:
                        last_week_data_dict[l[0]] = l[1]
            data_structure = {}
            for i in data_list:
                basic_data = gen_basic_data(i, weeks_info.get('total_weeks'))
                data_structure = {
                    "rtm": '',
                    "business_type": '',
                    "sold_to": i['sold_to_name'],
                    **basic_data
                }
                if len(last_week_data_dict) > 0:
                    if i['sold_to_name'] in last_week_data_dict.keys():
                        data_structure['*CW-1'] = last_week_data_dict[i['sold_to_name']]
                    else:
                        data_structure['*CW-1'] = None
                business_type_data = {}
                if i['rtm'] not in ret.keys():
                    if i['business_type'] not in business_type_data.keys():
                        business_type_data[i['business_type']] = [data_structure]
                    else:
                        biz_temp = business_type_data[i['business_type']]
                        biz_temp.append(data_structure)
                        business_type_data[i['business_type']] = biz_temp
                    ret[i['rtm']] = business_type_data
                else:
                    business_type_data = ret[i['rtm']]
                    if i['business_type'] not in business_type_data.keys():
                        business_type_data[i['business_type']] = [data_structure]
                    else:
                        biz_temp = business_type_data[i['business_type']]
                        biz_temp.append(data_structure)
                        business_type_data[i['business_type']] = biz_temp
                    ret[i['rtm']] = business_type_data
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret


class AppFastForecastMonoAdvanceWa(FASTLiteBase):
    __tablename__ = 'app_fast_forecast_mono_advance_wa'
    __table_args__ = {'schema': 'fast_lite'}
    
    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    rtm = Column(String(256), comment='')
    week_date = Column(String(256),comment='FY23Q1W12')
    fiscal_week_year = Column(Integer, comment='202312')
    fiscal_quarter = Column(Integer, comment='')
    fiscal_week = Column(Integer, comment='')
    week_begin_dt = Column(String(256), comment='')
    week_end_dt = Column(String(256), comment='')
    project_cd = Column(String(256), comment='')
    cust_id = Column(String(256), comment='')
    lob = Column(String(256), comment='')
    model = Column(String(256), comment='')
    sku = Column(String(256), comment='')
    fph4 = Column(String(256), comment='')
    mpn_id = Column(String(256), comment='')
    show_week = Column(Integer, comment='对应的是哪一周的数据')
    data_type = Column(String(256), comment='')
    fcst = Column(Integer, comment='')
    create_time = Column(DateTime, default=datetime.now, comment='create time')
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='update time')
    
    @classmethod
    def bulk_save(cls, objs: list):
        s = FASTLiteSession()
        try:
            s.execute(cls.__table__.insert(), objs)
            s.commit()
        except Exception as e:
            logger.exception(str(e))
            s.rollback()
        finally:
            s.close()

    @classmethod
    def check_current_week_data(cls, week_date):
        s = FASTLiteSession()
        ret = 0
        try:
            data = s.execute(f'''
                             SELECT count(id) as count
                             FROM {cls.__tablename__}
                             WHERE week_date='{week_date}';
                             ''')
            raw_data = data.fetchone()
            ret = raw_data.count
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

    @classmethod
    def gen_version2_data_by_week(cls, fiscal_qtr_week_name: str, version: int = 2):
        query_and_insert_forecast_multi_version(fiscal_qtr_week_name, cls.__tablename__, version)


def query_and_insert_forecast_multi_version(fiscal_qtr_week_name: str, table_name: str, version: int):
    s = FASTLiteSession()
    try:
        if fiscal_qtr_week_name is None:
            return ''
        raw_sql = generate_multi_version_sql(fiscal_qtr_week_name, table_name, version)
        raw_data = s.execute(raw_sql)
        data_list = raw_data.fetchall()
        results = [dict(item) for item in data_list]
        s.execute(AppFastForecastMultiVersion.__table__.insert(), results)
        s.commit()
    except Exception as e:
        logger.exception(str(e))
        s.rollback()
        return ''
    finally:
        s.close() 


def generate_multi_version_sql(fiscal_qtr_week_name: str, table_name: str, version: int)->str:
    return f'''
            SELECT week_date,fiscal_week_year,rtm,cust_id AS sold_to_id,lob,model,sku,mpn_id,project_cd,fph4,
            week_in_fiscal_quarter,total_weeks_in_quarter,
            (ifnull(cq_w1,0)+ifnull(cq_w2,0)+ifnull(cq_w3,0)+ifnull(cq_w4,0)+ifnull(cq_w5,0)+ifnull(cq_w6,0)+ifnull(cq_w7,0)+ifnull(cq_w8,0)+ifnull(cq_w9,0)+ifnull(cq_w10,0)+ifnull(cq_w11,0)+ifnull(cq_w12,0)+ifnull(cq_w13,0)+ifnull(cq_w14,0)) AS cq_total, 
            cq_w1, cq_w2, cq_w3, cq_w4, cq_w5, cq_w6, cq_w7, cq_w8, cq_w9, cq_w10, cq_w11, cq_w12, cq_w13, cq_w14, 
            nq_w1, nq_w2, nq_w3, nq_w4, nq_w5, nq_w6, nq_w7, nq_w8, nq_w9, nq_w10, nq_w11, nq_w12, nq_w13, nq_w14, 
            {version} as business_version 
            FROM 
            (SELECT a.week_date,a.fiscal_week_year,a.rtm,a.cust_id,a.project_cd,a.mpn_id,a.lob,a.model,a.sku,a.fph4,
            c.wifq AS week_in_fiscal_quarter, c.total_weeks_in_quarter, 
            max(CASE WHEN a.fiscal_quarter = c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=1 THEN fcst ELSE NULL END) cq_w1, 
            max(CASE WHEN a.fiscal_quarter = c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=2 THEN fcst ELSE NULL END) cq_w2, 
            max(CASE WHEN a.fiscal_quarter = c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=3 THEN fcst ELSE NULL END) cq_w3, 
            max(CASE WHEN a.fiscal_quarter = c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=4 THEN fcst ELSE NULL END) cq_w4, 
            max(CASE WHEN a.fiscal_quarter = c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=5 THEN fcst ELSE NULL END) cq_w5, 
            max(CASE WHEN a.fiscal_quarter = c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=6 THEN fcst ELSE NULL END) cq_w6, 
            max(CASE WHEN a.fiscal_quarter = c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=7 THEN fcst ELSE NULL END) cq_w7, 
            max(CASE WHEN a.fiscal_quarter = c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=8 THEN fcst ELSE NULL END) cq_w8, 
            max(CASE WHEN a.fiscal_quarter = c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=9 THEN fcst ELSE NULL END) cq_w9, 
            max(CASE WHEN a.fiscal_quarter = c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=10 THEN fcst ELSE NULL END) cq_w10, 
            max(CASE WHEN a.fiscal_quarter = c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=11 THEN fcst ELSE NULL END) cq_w11, 
            max(CASE WHEN a.fiscal_quarter = c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=12 THEN fcst ELSE NULL END) cq_w12, 
            max(CASE WHEN a.fiscal_quarter = c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=13 THEN fcst ELSE NULL END) cq_w13, 
            max(CASE WHEN a.fiscal_quarter = c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=14 THEN fcst ELSE NULL END) cq_w14, 
            max(CASE WHEN a.fiscal_quarter > c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=1 THEN fcst ELSE NULL END) nq_w1, 
            max(CASE WHEN a.fiscal_quarter > c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=2 THEN fcst ELSE NULL END) nq_w2, 
            max(CASE WHEN a.fiscal_quarter > c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=3 THEN fcst ELSE NULL END) nq_w3, 
            max(CASE WHEN a.fiscal_quarter > c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=4 THEN fcst ELSE NULL END) nq_w4, 
            max(CASE WHEN a.fiscal_quarter > c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=5 THEN fcst ELSE NULL END) nq_w5, 
            max(CASE WHEN a.fiscal_quarter > c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=6 THEN fcst ELSE NULL END) nq_w6, 
            max(CASE WHEN a.fiscal_quarter > c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=7 THEN fcst ELSE NULL END) nq_w7, 
            max(CASE WHEN a.fiscal_quarter > c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=8 THEN fcst ELSE NULL END) nq_w8, 
            max(CASE WHEN a.fiscal_quarter > c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=9 THEN fcst ELSE NULL END) nq_w9, 
            max(CASE WHEN a.fiscal_quarter > c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=10 THEN fcst ELSE NULL END) nq_w10, 
            max(CASE WHEN a.fiscal_quarter > c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=11 THEN fcst ELSE NULL END) nq_w11, 
            max(CASE WHEN a.fiscal_quarter > c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=12 THEN fcst ELSE NULL END) nq_w12, 
            max(CASE WHEN a.fiscal_quarter > c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=13 THEN fcst ELSE NULL END) nq_w13, 
            max(CASE WHEN a.fiscal_quarter > c.current_week_fiscal_quarter AND b.week_in_fiscal_quarter=14 THEN fcst ELSE NULL END) nq_w14 
            FROM (SELECT * FROM {table_name} WHERE week_date = '{fiscal_qtr_week_name}') AS a 
            LEFT JOIN (SELECT week_in_fiscal_quarter, fiscal_week_year AS bf FROM fiscal_week_year) AS b 
            ON a.show_week = b.bf 
            LEFT JOIN (SELECT week_in_fiscal_quarter AS wifq, total_weeks_in_quarter, fiscal_quarter AS current_week_fiscal_quarter, fiscal_week_year AS cf FROM fiscal_week_year) AS c 
            ON a.fiscal_week_year = c.cf 
            GROUP BY a.rtm, a.cust_id, a.mpn_id) AS d;
        '''


last_week_sum_fields = {
    'cq_w1': func.sum(AppFastForecastMultiVersion.cq_w1).label('cq_w1'),
    'cq_w2': func.sum(AppFastForecastMultiVersion.cq_w2).label('cq_w2'),
    'cq_w3': func.sum(AppFastForecastMultiVersion.cq_w3).label('cq_w3'),
    'cq_w4': func.sum(AppFastForecastMultiVersion.cq_w4).label('cq_w4'),
    'cq_w5': func.sum(AppFastForecastMultiVersion.cq_w5).label('cq_w5'),
    'cq_w6': func.sum(AppFastForecastMultiVersion.cq_w6).label('cq_w6'),
    'cq_w7': func.sum(AppFastForecastMultiVersion.cq_w7).label('cq_w7'),
    'cq_w8': func.sum(AppFastForecastMultiVersion.cq_w8).label('cq_w8'),
    'cq_w9': func.sum(AppFastForecastMultiVersion.cq_w9).label('cq_w9'),
    'cq_w10': func.sum(AppFastForecastMultiVersion.cq_w10).label('cq_w10'),
    'cq_w11': func.sum(AppFastForecastMultiVersion.cq_w11).label('cq_w11'),
    'cq_w12': func.sum(AppFastForecastMultiVersion.cq_w12).label('cq_w12'),
    'cq_w13': func.sum(AppFastForecastMultiVersion.cq_w13).label('cq_w13'),
    'cq_w14': func.sum(AppFastForecastMultiVersion.cq_w14).label('cq_w14'),
}
last_week_fields = {
    'cq_w1': AppFastForecastMultiVersion.cq_w1.label('cq_w1'),
    'cq_w2': AppFastForecastMultiVersion.cq_w2.label('cq_w2'),
    'cq_w3': AppFastForecastMultiVersion.cq_w3.label('cq_w3'),
    'cq_w4': AppFastForecastMultiVersion.cq_w4.label('cq_w4'),
    'cq_w5': AppFastForecastMultiVersion.cq_w5.label('cq_w5'),
    'cq_w6': AppFastForecastMultiVersion.cq_w6.label('cq_w6'),
    'cq_w7': AppFastForecastMultiVersion.cq_w7.label('cq_w7'),
    'cq_w8': AppFastForecastMultiVersion.cq_w8.label('cq_w8'),
    'cq_w9': AppFastForecastMultiVersion.cq_w9.label('cq_w9'),
    'cq_w10': AppFastForecastMultiVersion.cq_w10.label('cq_w10'),
    'cq_w11': AppFastForecastMultiVersion.cq_w11.label('cq_w11'),
    'cq_w12': AppFastForecastMultiVersion.cq_w12.label('cq_w12'),
    'cq_w13': AppFastForecastMultiVersion.cq_w13.label('cq_w13'),
    'cq_w14': AppFastForecastMultiVersion.cq_w14.label('cq_w14'),
}
