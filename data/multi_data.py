from util.gc_dmp_base import *
from .forecast_data import AppFastForecastTrmSoWa
from sqlalchemy import case, cast, and_, or_, desc
from util.const import MODEL, ErrorExcept, ErrCode, StrRTMMulti, MultiBusinessTypeOrder, SoldToNameKey, SoldToIdKey
from util.const import ExternalViewBeginWeek, SpecialSoldToId
import pandas as pd
from flask import g



class AppFastDemandMultiSummary:
    __table_args__ = {"extend_existing": True}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    fiscal_qtr_week_name = Column(String(256), comment='FY23Q1W8')
    fiscal_week_year = Column(Integer, comment='202312')
    week_begin_dt = Column(String(256), comment='')
    week_end_dt = Column(String(256), comment='')
    rtm = Column(String(256), comment='')
    sold_to_id = Column(Integer, comment='')
    lob = Column(String(256), comment='')
    sub_lob = Column(String(256), comment='')
    fph4 = Column(String(256), comment='')
    project_code = Column(String(256), comment='')
    sku = Column(String(256), comment='')
    mpn_id = Column(String(256), comment='')
    demand_cw = Column(Float, comment='')
    demand_cw1 = Column(Float, comment='')
    demand_cw2 = Column(Float, comment='')
    demand_cw3 = Column(Float, comment='')
    external_demand_cw = Column(Float, comment='')
    external_demand_cw1 = Column(Float, comment='')
    external_demand_cw2 = Column(Float, comment='')
    external_demand_cw3 = Column(Float, comment='')
    shipment_plan_cw = Column(Float, comment='')
    shipment_plan_cw1 = Column(Float, comment='')
    shipment_plan_cw2 = Column(Float, comment='')
    shipment_plan_cw3 = Column(Float, comment='')
    shippable_backlog = Column(Float, comment='')
    gross_billing_units_cw = Column(Float, comment='')
    sp_remaining_cw1 = Column(Float, comment='')
    sp_remaining_cw2 = Column(Float, comment='')
    top_up_demand_cw1 = Column(Float, comment='')
    top_up_demand_cw2 = Column(Float, comment='')
    top_up_demand_cw3 = Column(Float, comment='')
    top_up_demand_cw4 = Column(Float, comment='')
    po_needed_cw = Column(Float, comment='')
    po_needed_cw1 = Column(Float, comment='')
    po_needed_cw2 = Column(Float, comment='')
    po_needed_cw3 = Column(Float, comment='')

    DOWNLOAD_COLUMN_NAME = [
        "Week_Date",
        "Business Type",
        "Customer Sold-to ID",
        "Sold-to Name",
        "LOB",
        "Model",
        "FPH4",
        "Project Code",
        "SKU",
        "MPN",
        "Top Up Demand CW+1",
        "Top Up Demand CW+2",
        "Top Up Demand CW+3",
        "Top Up Demand CW+4",
        "Shipment Plan CW",
        "Shipment Plan CW+1",
        "Shipment Plan CW+2",
        "Shipment Plan CW+3",
        "PO Needed CW",
        "PO Needed CW+1",
        "PO Needed CW+2",
        "PO Needed CW+3",
    ]

    EXTERNAL_DOWNLOAD_NAME = [
        "财年周",
        "产品",
        "机型",
        "MPN",
        "SKU",
        "Demand CW+1",
        "Demand CW+2",
        "Demand CW+3",
        "Demand CW+4",
        "Shipment Plan CW",
        "Shipment Plan CW+1",
        "PO Needed CW",
        "PO Needed CW+1",
    ]

    @classmethod
    def get_fiscal_qtr_by_fiscal_week_year(cls, fiscal_week_year: int):
        s = GcDmpSession()
        fiscal_qtr = ""
        try:
            res = s.query(cls) \
                .filter(cls.fiscal_week_year == fiscal_week_year) \
                .first()
            if res is None:
                s.close()
                return []
            fiscal_qtr = res.fiscal_qtr_week_name
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return fiscal_qtr

    @classmethod
    def get_multi_data(
            cls,
            fiscal_week_year: int, business_type: list, sold_to_id: list,
            sold_to_name: list, lob: list, model: list, sku: list,
            order_by: list, page_num: int, page_size: int
    ):
        s = GcDmpSession()
        res = []
        total = None
        count = 0
        try:
            query = s.query(
                DimFastSoldToMappingMulti.business_type,
                DimFastSoldToMappingMulti.sold_to_name,
                cls.lob,
                cls.sold_to_id,
                cls.sub_lob.label("model"),
                func.sum(cls.shipment_plan_cw).label("shipment_plan_cw"),
                func.sum(cls.shipment_plan_cw1).label("shipment_plan_cw1"),
                func.sum(cls.shipment_plan_cw2).label("shipment_plan_cw2"),
                func.sum(cls.shipment_plan_cw3).label("shipment_plan_cw3"),
                func.sum(cls.top_up_demand_cw1).label("top_up_demand_cw1"),
                func.sum(cls.top_up_demand_cw2).label("top_up_demand_cw2"),
                func.sum(cls.top_up_demand_cw3).label("top_up_demand_cw3"),
                func.sum(cls.top_up_demand_cw4).label("top_up_demand_cw4"),
                func.sum(cls.po_needed_cw).label("po_needed_cw"),
                func.sum(cls.po_needed_cw1).label("po_needed_cw1"),
                func.sum(cls.po_needed_cw2).label("po_needed_cw2"),
                func.sum(cls.po_needed_cw3).label("po_needed_cw3"),
                # cls.demand_cw,
                # cls.demand_cw1,
                # cls.demand_cw2,
                # cls.demand_cw3,
                # cls.shippable_backlog,
                # cls.gross_billing_units_cw,
                # cls.sp_remaining_cw1,
                # cls.sp_remaining_cw2,
            )\
                .filter(cls.fiscal_week_year == fiscal_week_year)\
                .filter(DimFastSoldToMappingMulti.start_week <= fiscal_week_year)\
                .filter(DimFastSoldToMappingMulti.end_week >= fiscal_week_year)\
                .join(DimFastSoldToMappingMulti,
                      cls.sold_to_id == DimFastSoldToMappingMulti.sold_to_id)\
                .group_by(cls.sold_to_id)\
                .group_by(cls.sub_lob)
            summary = s.query(
                func.sum(cls.shipment_plan_cw).label("shipment_plan_cw"),
                func.sum(cls.shipment_plan_cw1).label("shipment_plan_cw1"),
                func.sum(cls.shipment_plan_cw2).label("shipment_plan_cw2"),
                func.sum(cls.shipment_plan_cw3).label("shipment_plan_cw3"),
                func.sum(cls.top_up_demand_cw1).label("top_up_demand_cw1"),
                func.sum(cls.top_up_demand_cw2).label("top_up_demand_cw2"),
                func.sum(cls.top_up_demand_cw3).label("top_up_demand_cw3"),
                func.sum(cls.top_up_demand_cw4).label("top_up_demand_cw4"),
                func.sum(cls.po_needed_cw).label("po_needed_cw"),
                func.sum(cls.po_needed_cw1).label("po_needed_cw1"),
                func.sum(cls.po_needed_cw2).label("po_needed_cw2"),
                func.sum(cls.po_needed_cw3).label("po_needed_cw3"),
                # func.sum(cls.demand_cw).label("demand_cw"),
                # func.sum(cls.demand_cw1).label("demand_cw1"),
                # func.sum(cls.demand_cw2).label("demand_cw2"),
                # func.sum(cls.demand_cw3).label("demand_cw3"),
                # func.sum(cls.shippable_backlog).label("shippable_backlog"),
                # func.sum(cls.gross_billing_units_cw).label("gross_billing_units_cw"),
                # func.sum(cls.sp_remaining_cw1).label("sp_remaining_cw1"),
                # func.sum(cls.sp_remaining_cw2).label("sp_remaining_cw2"),
            )\
                .filter(cls.fiscal_week_year == fiscal_week_year)\
                .filter(DimFastSoldToMappingMulti.start_week <= fiscal_week_year)\
                .filter(DimFastSoldToMappingMulti.end_week >= fiscal_week_year)\
                .join(DimFastSoldToMappingMulti, cls.sold_to_id == DimFastSoldToMappingMulti.sold_to_id)
            sold_to_names = g.get(SoldToNameKey)
            if len(sold_to_names) != 1 or sold_to_names[0] != "All":
                query = query.filter(DimFastSoldToMappingMulti.sold_to_name.in_(sold_to_names))
                summary = summary.filter(DimFastSoldToMappingMulti.sold_to_name.in_(sold_to_names))
            if business_type is not None and len(business_type) != 0:
                query = query.filter(DimFastSoldToMappingMulti.business_type.in_(business_type))
                summary = summary.filter(DimFastSoldToMappingMulti.business_type.in_(business_type))
            if sold_to_name is not None and len(sold_to_name) != 0:
                query = query.filter(DimFastSoldToMappingMulti.sold_to_name.in_(sold_to_name))
                summary = summary.filter(DimFastSoldToMappingMulti.sold_to_name.in_(sold_to_name))
            if sold_to_id is not None and len(sold_to_id) != 0:
                query = query.filter(cls.sold_to_id.in_(sold_to_id))
                summary = summary.filter(cls.sold_to_id.in_(sold_to_id))
            if lob is not None and len(lob) != 0:
                query = query.filter(cls.lob.in_(lob))
                summary = summary.filter(cls.lob.in_(lob))
            if model is not None and len(model) != 0:
                query = query.filter(cls.sub_lob.in_(model))
                summary = summary.filter(cls.sub_lob.in_(model))
            else:
                # Multi 仅取这些model的数据
                query = query.filter(cls.sub_lob.in_(model))
                summary = summary.filter(cls.sub_lob.in_(model))
            if sku is not None and len(sku) != 0:
                query = query.filter(cls.sku.in_(sku))
                summary = summary.filter(cls.sku.in_(sku))
            if order_by is None or len(order_by) == 0:
                query = query\
                    .order_by(case(value=DimFastSoldToMappingMulti.business_type,
                                   whens={x: MultiBusinessTypeOrder.index(x)+1 for x in MultiBusinessTypeOrder})) \
                    .order_by(DimFastSoldToMappingMulti.business_type.asc())\
                    .order_by(cast(cls.sold_to_id, Integer))\
                    .order_by(cls.lob.asc())\
                    .order_by(case(value=cls.sub_lob,
                                   whens={x: model.index(x) + 1 for x in model}))
            else:
                for item in order_by:
                    query = query.order_by(item)
            count = query.count()
            total = summary.first()
            res = query.limit(page_size).offset((page_num - 1) * page_size).all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return res, total, count

    @classmethod
    def get_multi_data_external(cls, fiscal_week_year: int, lob: list, model: list, sku: list):
        s = GcDmpSession()
        res = []
        total = None
        count = 0
        try:
            sold_to_id = g.get(SoldToIdKey)
            if sold_to_id in SpecialSoldToId:
                for item in SpecialSoldToId.get(sold_to_id):
                    if int(item.get("start_week")) <= int(fiscal_week_year) <= int(item.get("end_week")):
                        sold_to_id = item.get("sold_to_id")
            query = s.query(
                cls.lob,
                cls.sold_to_id,
                cls.sub_lob.label("model"),
                func.sum(cls.shipment_plan_cw).label("shipment_plan_cw"),
                func.sum(cls.shipment_plan_cw1).label("shipment_plan_cw1"),
                func.sum(cls.po_needed_cw).label("po_needed_cw"),
                func.sum(cls.po_needed_cw1).label("po_needed_cw1"),
                func.sum(cls.top_up_demand_cw1).label("demand_cw1"),
                func.sum(cls.top_up_demand_cw2).label("demand_cw2"),
                func.sum(cls.top_up_demand_cw3).label("demand_cw3"),
                func.sum(cls.top_up_demand_cw4).label("demand_cw4"),
            )\
                .filter(cls.fiscal_week_year == fiscal_week_year)\
                .filter(cls.sold_to_id == sold_to_id)\
                .group_by(cls.sold_to_id)\
                .group_by(cls.sub_lob)
            summary = s.query(
                func.sum(cls.shipment_plan_cw).label("shipment_plan_cw"),
                func.sum(cls.shipment_plan_cw1).label("shipment_plan_cw1"),
                func.sum(cls.po_needed_cw).label("po_needed_cw"),
                func.sum(cls.po_needed_cw1).label("po_needed_cw1"),
                func.sum(cls.top_up_demand_cw1).label("demand_cw1"),
                func.sum(cls.top_up_demand_cw2).label("demand_cw2"),
                func.sum(cls.top_up_demand_cw3).label("demand_cw3"),
                func.sum(cls.top_up_demand_cw4).label("demand_cw4"),
            )\
                .filter(cls.fiscal_week_year == fiscal_week_year)\
                .filter(cls.sold_to_id == sold_to_id)
            if lob is not None and len(lob) != 0:
                query = query.filter(cls.lob.in_(lob))
                summary = summary.filter(cls.lob.in_(lob))
            if model is not None and len(model) != 0:
                query = query.filter(cls.sub_lob.in_(model))
                summary = summary.filter(cls.sub_lob.in_(model))
            else:
                # Multi 仅取这些model的数据
                query = query.filter(cls.sub_lob.in_(model))
                summary = summary.filter(cls.sub_lob.in_(model))
            if sku is not None and len(sku) != 0:
                query = query.filter(cls.sku.in_(sku))
                summary = summary.filter(cls.sku.in_(sku))
            query = query\
                .order_by(cls.lob.asc())\
                .order_by(case(value=cls.sub_lob, whens={x: model.index(x) + 1 for x in model}))
            count = query.count()
            total = summary.first()
            res = query.all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return res, total, count

    @classmethod
    def get_download_data(cls, fiscal_week_year: int,model:list):
        s = GcDmpSession()
        res = []
        try:
            query = s.query(
                cls.fiscal_qtr_week_name.label(cls.DOWNLOAD_COLUMN_NAME[0]),
                DimFastSoldToMappingMulti.business_type.label(cls.DOWNLOAD_COLUMN_NAME[1]),
                cls.sold_to_id.label(cls.DOWNLOAD_COLUMN_NAME[2]),
                DimFastSoldToMappingMulti.sold_to_name.label(cls.DOWNLOAD_COLUMN_NAME[3]),
                cls.lob.label(cls.DOWNLOAD_COLUMN_NAME[4]),
                cls.sub_lob.label(cls.DOWNLOAD_COLUMN_NAME[5]),
                cls.fph4.label(cls.DOWNLOAD_COLUMN_NAME[6]),
                cls.project_code.label(cls.DOWNLOAD_COLUMN_NAME[7]),
                cls.sku.label(cls.DOWNLOAD_COLUMN_NAME[8]),
                cls.mpn_id.label(cls.DOWNLOAD_COLUMN_NAME[9]),
                cls.top_up_demand_cw1.label(cls.DOWNLOAD_COLUMN_NAME[10]),
                cls.top_up_demand_cw2.label(cls.DOWNLOAD_COLUMN_NAME[11]),
                cls.top_up_demand_cw3.label(cls.DOWNLOAD_COLUMN_NAME[12]),
                cls.top_up_demand_cw4.label(cls.DOWNLOAD_COLUMN_NAME[13]),
                cls.shipment_plan_cw.label(cls.DOWNLOAD_COLUMN_NAME[14]),
                cls.shipment_plan_cw1.label(cls.DOWNLOAD_COLUMN_NAME[15]),
                cls.shipment_plan_cw2.label(cls.DOWNLOAD_COLUMN_NAME[16]),
                cls.shipment_plan_cw3.label(cls.DOWNLOAD_COLUMN_NAME[17]),
                cls.po_needed_cw.label(cls.DOWNLOAD_COLUMN_NAME[18]),
                cls.po_needed_cw1.label(cls.DOWNLOAD_COLUMN_NAME[19]),
                cls.po_needed_cw2.label(cls.DOWNLOAD_COLUMN_NAME[20]),
                cls.po_needed_cw3.label(cls.DOWNLOAD_COLUMN_NAME[21]),
            )\
                .filter(cls.fiscal_week_year == fiscal_week_year)\
                .filter(DimFastSoldToMappingMulti.start_week <= fiscal_week_year)\
                .filter(DimFastSoldToMappingMulti.end_week >= fiscal_week_year)\
                .filter(cls.sub_lob.in_(model))\
                .join(DimFastSoldToMappingMulti,
                      cls.sold_to_id == DimFastSoldToMappingMulti.sold_to_id)\
                .order_by(DimFastSoldToMappingMulti.business_type.asc())\
                .order_by(cast(cls.sold_to_id, Integer))\
                .order_by(cls.lob.asc())\
                .order_by(case(value=cls.sub_lob,
                               whens={x: model.index(x) + 1 for x in model}))
            sold_to_names = g.get(SoldToNameKey)
            if len(sold_to_names) != 1 or sold_to_names[0] != "All":
                query = query.filter(DimFastSoldToMappingMulti.sold_to_name.in_(sold_to_names))
            res = pd.read_sql_query(query.statement, GcDmpEngine)
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return res

    @classmethod
    def get_download_data_external(cls, fiscal_week_year: int,model:list):
        s = GcDmpSession()
        res = []
        try:
            sold_to_id = g.get(SoldToIdKey)
            if sold_to_id in SpecialSoldToId:
                for item in SpecialSoldToId.get(sold_to_id):
                    if int(item.get("start_week")) <= int(fiscal_week_year) <= int(item.get("end_week")):
                        sold_to_id = item.get("sold_to_id")
            query = s.query(
                cls.fiscal_qtr_week_name.label(cls.EXTERNAL_DOWNLOAD_NAME[0]),
                cls.lob.label(cls.EXTERNAL_DOWNLOAD_NAME[1]),
                cls.sub_lob.label(cls.EXTERNAL_DOWNLOAD_NAME[2]),
                cls.mpn_id.label(cls.EXTERNAL_DOWNLOAD_NAME[3]),
                cls.sku.label(cls.EXTERNAL_DOWNLOAD_NAME[4]),
                cls.top_up_demand_cw1.label(cls.EXTERNAL_DOWNLOAD_NAME[5]),
                cls.top_up_demand_cw2.label(cls.EXTERNAL_DOWNLOAD_NAME[6]),
                cls.top_up_demand_cw3.label(cls.EXTERNAL_DOWNLOAD_NAME[7]),
                cls.top_up_demand_cw4.label(cls.EXTERNAL_DOWNLOAD_NAME[8]),
                cls.shipment_plan_cw.label(cls.EXTERNAL_DOWNLOAD_NAME[9]),
                cls.shipment_plan_cw1.label(cls.EXTERNAL_DOWNLOAD_NAME[10]),
                cls.po_needed_cw.label(cls.EXTERNAL_DOWNLOAD_NAME[11]),
                cls.po_needed_cw1.label(cls.EXTERNAL_DOWNLOAD_NAME[12]),
            )\
                .filter(cls.fiscal_week_year == fiscal_week_year)\
                .filter(cls.sold_to_id == sold_to_id) \
                .filter(cls.sub_lob.in_(model)) \
                .order_by(cls.lob.asc())\
                .order_by(case(value=cls.sub_lob,
                               whens={x: model.index(x) + 1 for x in model}))
            res = pd.read_sql_query(query.statement, GcDmpEngine)
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return res

    @classmethod
    def get_fiscal_qtr_week_data(cls, page_num: int, page_size: int):
        s = GcDmpSession()
        res = []
        count = 0
        try:
            query = s.query(
                cls.fiscal_qtr_week_name,
                cls.fiscal_week_year
            ) \
                .group_by(cls.fiscal_week_year) \
                .order_by(cls.fiscal_week_year.desc())
            count = query.count()
            res = query.limit(page_size) \
                .offset((page_num - 1) * page_size) \
                .all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return res, count

    @classmethod
    def get_fiscal_qtr_week_data_external(cls, page_num: int, page_size: int):
        s = GcDmpSession()
        res = []
        count = 0
        try:
            if g.get(SoldToIdKey) in SpecialSoldToId.keys():
                conditions = []
                for item in SpecialSoldToId.get(g.get(SoldToIdKey)):
                    conditions.append(and_(
                        cls.sold_to_id == item.get("sold_to_id"),
                        cls.fiscal_week_year >= item.get("start_week"),
                        cls.fiscal_week_year <= item.get("end_week"),
                    ))
                query = s.query(
                    cls.fiscal_qtr_week_name,
                    cls.fiscal_week_year
                ) \
                    .filter(or_(*conditions)) \
                    .filter(cls.fiscal_week_year >= ExternalViewBeginWeek) \
                    .group_by(cls.fiscal_week_year) \
                    .order_by(cls.fiscal_week_year.desc())
                count = query.count()
                res = query.limit(page_size) \
                    .offset((page_num - 1) * page_size) \
                    .all()
            else:
                query = s.query(
                    cls.fiscal_qtr_week_name,
                    cls.fiscal_week_year
                )\
                    .filter(cls.sold_to_id == g.get(SoldToIdKey)) \
                    .filter(cls.fiscal_week_year >= ExternalViewBeginWeek) \
                    .group_by(cls.fiscal_week_year) \
                    .order_by(cls.fiscal_week_year.desc())
                count = query.count()
                res = query.limit(page_size) \
                    .offset((page_num - 1) * page_size) \
                    .all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        # 外部系统最多只能看到14周数据
        if count > 14:
            count = 14
        if ((page_num - 1) * page_size + len(res)) > 14:
            res = res[:14-((page_num - 1) * page_size)]
        return res, count

    @classmethod
    def get_sold_to_data(cls):
        s = GcDmpSession()
        res = []
        try:
            query = s.query(
                DimFastSoldToMappingMulti.business_type,
                DimFastSoldToMappingMulti.sold_to_id,
                DimFastSoldToMappingMulti.sold_to_name,
            )\
                .filter(DimFastSoldToMappingMulti.rtm == StrRTMMulti)\
                .order_by(DimFastSoldToMappingMulti.business_type)
            sold_to_names = g.get(SoldToNameKey)
            if len(sold_to_names) != 1 or sold_to_names[0] != "All":
                query = query.filter(DimFastSoldToMappingMulti.sold_to_name.in_(sold_to_names))
            res = query.all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return res

    @classmethod
    def get_sku_data(cls):
        s = GcDmpSession()
        res = []
        try:
            res = s.query(
                cls.lob,
                cls.sub_lob,
                cls.sku,
            ) \
                .filter(cls.sub_lob.in_(MODEL)) \
                .group_by(cls.sku) \
                .order_by(cls.lob) \
                .order_by(case(value=cls.sub_lob, whens={x: MODEL.index(x) + 1 for x in MODEL})) \
                .order_by(cls.sku.asc()) \
                .all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return res


class AppFastDemandMultiSummaryWa(GcDmpBase, AppFastDemandMultiSummary):
    __tablename__ = "app_fast_demand_multi_summary_wa"

    @classmethod
    def get_model_list(cls, lob, rtm, fiscal_week_year):
        s = GcDmpSession()
        res = []
        try:
            query = s.query(AppFastDemandMultiSummaryWa.sub_lob).distinct()
            query = query.filter(cls.rtm == rtm)
            if lob is not None and len(lob) != 0:
                query = query.filter(cls.lob.in_(lob))
            query = query.order_by(desc(cls.sub_lob))
            res = query.all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return res


class AppFastDemandMultiSummaryAmWa(GcDmpBase, AppFastDemandMultiSummary):
    __tablename__ = "app_fast_demand_multi_summary_am_wa"

    @classmethod
    def get_model_list(cls, lob, rtm, fiscal_week_year):
        s = GcDmpSession()
        res = []
        try:
            query = s.query(AppFastDemandMultiSummaryAmWa.sub_lob).distinct()
            query = query.filter(cls.rtm == rtm) \
                .filter(cls.fiscal_week_year == fiscal_week_year)
            if lob is not None and len(lob) != 0:
                query = query.filter(cls.lob.in_(lob))
            query = query.order_by(desc(cls.sub_lob))
            res = query.all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return res


class AppFastForecastMultiSoWa(AppFastForecastTrmSoWa):
    @classmethod
    def get_fiscal_qtr_by_fiscal_week_year(cls, fiscal_week_year: int):
        s = GcDmpSession()
        fiscal_qtr = ""
        try:
            res = s.query(cls) \
                .filter(cls.rtm == StrRTMMulti) \
                .filter(cls.fiscal_week_year == fiscal_week_year) \
                .first()
            if res is None:
                s.close()
                raise ErrorExcept(ErrCode.DBQueryError, "fiscal_week_year data not found: " + str(fiscal_week_year))
            fiscal_qtr = res.week_date
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return fiscal_qtr

    @classmethod
    def get_fiscal_qtr_week_data(cls, page_num: int, page_size: int, rtm: str = StrRTMMulti):
        s = GcDmpSession()
        res = []
        count = 0
        try:
            query = s.query(
                cls.fiscal_week_year,
                cls.week_date
            ) \
                .filter(cls.rtm == rtm) \
                .group_by(cls.fiscal_week_year) \
                .order_by(cls.fiscal_week_year.desc())\
                .all()
            # 不在数据库侧分页是为了减少数据库查询次数，这个查询太慢了。不用distinct是由于排序问题
            count = len(query)
            start = (page_num - 1) * page_size
            if start > count:
                return []
            end = page_num * page_size
            if end > count:
                end = count
            res = query[start: end]
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return res, count

    @classmethod
    def get_data_by_fiscal_week_year_new(
            cls,
            fiscal_week_year: int, last_week_year: int, sold_to_id: list, rtm: str, lob: list, model: list,
    ):
        if not rtm:
            rtm = StrRTMMulti
        s = GcDmpSession()
        res = []
        last_res = []
        try:
            res = s.query(
                cls.cust_id.label("sold_to_id"),
                cls.fiscal_quarter,
                cls.lob,
                cls.model,
                cls.fiscal_week_year,
                cls.week_date,
                cls.data_type,
                cls.show_week,
                func.sum(cls.fcst).label("fcst"),
            ) \
                .filter(cls.fiscal_week_year == fiscal_week_year) \
                .filter(cls.rtm == rtm)\
                .filter(cls.cust_id.in_(sold_to_id)) \
                .group_by(cls.cust_id) \
                .group_by(cls.model) \
                .group_by(cls.show_week)
            last_res = s.query(
                cls.cust_id.label("sold_to_id"),
                cls.fiscal_quarter,
                cls.lob,
                cls.model,
                cls.fiscal_week_year,
                cls.week_date,
                cls.data_type,
                cls.show_week,
                func.sum(cls.fcst).label("fcst"),
            ) \
                .filter(cls.fiscal_week_year == last_week_year) \
                .filter(cls.show_week == last_week_year) \
                .filter(cls.rtm == rtm) \
                .filter(cls.cust_id.in_(sold_to_id)) \
                .group_by(cls.cust_id) \
                .group_by(cls.model) \
                .group_by(cls.show_week)
            if lob is not None and len(lob) != 0:
                res = res.filter(cls.lob.in_(lob))
                last_res = last_res.filter(cls.lob.in_(lob))
            if model is not None and len(model) != 0:
                res = res.filter(cls.model.in_(model))
                last_res = last_res.filter(cls.model.in_(model))
            else:
                res = res.filter(cls.model.in_(model))
                last_res = last_res.filter(cls.model.in_(model))
            res = res.all()
            last_res = last_res.all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return res, last_res

    @classmethod
    def get_data_by_fiscal_week_year_external(
            cls,
            fiscal_week_year: int, rtm: str, lob: list, model: list,
    ):
        if not rtm:
            rtm = StrRTMMulti
        s = GcDmpSession()
        res = []
        try:
            sold_to_id = g.get(SoldToIdKey)
            if sold_to_id in SpecialSoldToId:
                for item in SpecialSoldToId.get(sold_to_id):
                    if int(item.get("start_week")) <= int(fiscal_week_year) <= int(item.get("end_week")):
                        sold_to_id = item.get("sold_to_id")
            res = s.query(
                cls.fiscal_quarter,
                cls.lob,
                cls.model,
                cls.fiscal_week_year,
                cls.week_date,
                cls.data_type,
                cls.show_week,
                func.sum(cls.fcst).label("fcst"),
            ) \
                .filter(cls.fiscal_week_year == fiscal_week_year) \
                .filter(cls.rtm == rtm)\
                .filter(cls.cust_id == sold_to_id) \
                .filter(cls.data_type == "fcst")\
                .group_by(cls.model) \
                .group_by(cls.show_week)\
                .order_by(desc(cls.model))\
                .order_by(cls.show_week)
            if lob is not None and len(lob) != 0:
                res = res.filter(cls.lob.in_(lob))
            if model is not None and len(model) != 0:
                res = res.filter(cls.model.in_(model))
            else:
                res = res.filter(cls.model.in_(model))
            res = res.all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return res

    @classmethod
    def get_data_by_fiscal_week_year(
            cls,
            fiscal_week_year: int, last_week_year: int, rtm: str,
            business_type: list, sold_to_id: list, sold_to_name: list, lob: list, model: list,
    ):
        if not rtm:
            rtm = StrRTMMulti
        s = GcDmpSession()
        res = []
        last_res = []
        try:
            res = s.query(
                DimFastSoldToMappingMulti.business_type,
                DimFastSoldToMappingMulti.sold_to_name,
                cls.cust_id.label("sold_to_id"),
                cls.fiscal_quarter,
                cls.lob,
                cls.model,
                cls.fiscal_week_year,
                cls.week_date,
                cls.data_type,
                cls.show_week,
                func.sum(cls.fcst).label("fcst"),
            )\
                .filter(cls.fiscal_week_year == fiscal_week_year)\
                .filter(cls.rtm == rtm)\
                .join(DimFastSoldToMappingMulti, cls.cust_id == DimFastSoldToMappingMulti.sold_to_id)\
                .group_by(cls.cust_id)\
                .group_by(cls.model)\
                .group_by(cls.show_week)\
                .order_by(case(value=DimFastSoldToMappingMulti.business_type,
                               whens={x: MultiBusinessTypeOrder.index(x)+1 for x in MultiBusinessTypeOrder})) \
                .order_by(cast(cls.cust_id, Integer))\
                .order_by(case(value=cls.model, whens={x: MODEL.index(x)+1 for x in MODEL}))\
                .order_by(cls.show_week.asc())
            last_res = s.query(
                DimFastSoldToMappingMulti.business_type,
                DimFastSoldToMappingMulti.sold_to_name,
                cls.cust_id.label("sold_to_id"),
                cls.fiscal_quarter,
                cls.lob,
                cls.model,
                cls.fiscal_week_year,
                cls.week_date,
                cls.data_type,
                cls.show_week,
                func.sum(cls.fcst).label("fcst"),
            )\
                .filter(cls.fiscal_week_year == last_week_year)\
                .filter(cls.show_week == last_week_year)\
                .filter(cls.rtm == rtm)\
                .join(DimFastSoldToMappingMulti, cls.cust_id == DimFastSoldToMappingMulti.sold_to_id)\
                .group_by(cls.cust_id)\
                .group_by(cls.model)\
                .group_by(cls.show_week)
            sold_to_names = g.get(SoldToNameKey)
            if len(sold_to_names) != 1 or sold_to_names[0] != "All":
                res = res.filter(DimFastSoldToMappingMulti.sold_to_name.in_(sold_to_names))
                last_res = last_res.filter(DimFastSoldToMappingMulti.sold_to_name.in_(sold_to_names))
            if business_type is not None and len(business_type) != 0:
                res = res.filter(DimFastSoldToMappingMulti.business_type.in_(business_type))
                last_res = last_res.filter(DimFastSoldToMappingMulti.business_type.in_(business_type))
            if sold_to_name is not None and len(sold_to_name) != 0:
                res = res.filter(DimFastSoldToMappingMulti.sold_to_name.in_(sold_to_name))
                last_res = last_res.filter(DimFastSoldToMappingMulti.sold_to_name.in_(sold_to_name))
            if sold_to_id is not None and len(sold_to_id) != 0:
                res = res.filter(cls.cust_id.in_(sold_to_id))
                last_res = last_res.filter(cls.cust_id.in_(sold_to_id))
            if lob is not None and len(lob) != 0:
                res = res.filter(cls.lob.in_(lob))
                last_res = last_res.filter(cls.lob.in_(lob))
            if model is not None and len(model) != 0:
                res = res.filter(cls.model.in_(model))
                last_res = last_res.filter(cls.model.in_(model))
            else:
                res = res.filter(cls.model.in_(model))
                last_res = last_res.filter(cls.model.in_(model))
            res = res.all()
            last_res = last_res.all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return res, last_res

    DOWNLOAD_COLUMN_NAME = [
        "Week_Date",
        "Business Type",
        "Sold-to ID",
        "Sold-to Name",
        "LOB",
        "Model",
        "FPH4",
        "Project Code",
        "SKU",
        "MPN",
    ]

    EXTERNAL_COLUMN_NAME = [
        "财年周",
        "产品",
        "机型",
        "MPN",
        "SKU",
    ]

    @classmethod
    def get_download_data_by_fiscal_week_year(cls, fiscal_week_year: int, rtm: str,model:list):
        if not rtm:
            rtm = StrRTMMulti
        s = GcDmpSession()
        res = []
        try:
            query = s.query(
                cls.week_date,
                DimFastSoldToMappingMulti.business_type,
                cls.cust_id,
                DimFastSoldToMappingMulti.sold_to_name,
                cls.lob,
                cls.model,
                cls.fph4,
                cls.project_cd,
                cls.sku,
                cls.mpn_id,
                cls.show_week,
                cls.data_type,
                cls.fcst,
            )\
                .filter(cls.fiscal_week_year == fiscal_week_year)\
                .filter(DimFastSoldToMappingMulti.start_week <= fiscal_week_year)\
                .filter(DimFastSoldToMappingMulti.end_week >= fiscal_week_year)\
                .filter(cls.rtm == rtm)\
                .filter(cls.data_type == "fcst")\
                .filter(cls.model.in_(model))\
                .join(DimFastSoldToMappingMulti, cls.cust_id == DimFastSoldToMappingMulti.sold_to_id)\
                .order_by(cls.cust_id.asc())\
                .order_by(case(value=cls.model, whens={x: model.index(x)+1 for x in model}))\
                .order_by(cls.mpn_id)\
                .order_by(cls.show_week.asc())
            sold_to_names = g.get(SoldToNameKey)
            if len(sold_to_names) != 1 or sold_to_names[0] != "All":
                query = query.filter(DimFastSoldToMappingMulti.sold_to_name.in_(sold_to_names))
            res = query.all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return res

    @classmethod
    def get_download_data_by_fiscal_week_year_external(cls, fiscal_week_year: int, rtm: str,model):
        if not rtm:
            rtm = StrRTMMulti
        s = GcDmpSession()
        res = []
        try:
            sold_to_id = g.get(SoldToIdKey)
            if sold_to_id in SpecialSoldToId:
                for item in SpecialSoldToId.get(sold_to_id):
                    if int(item.get("start_week")) <= int(fiscal_week_year) <= int(item.get("end_week")):
                        sold_to_id = item.get("sold_to_id")
            query = s.query(
                cls.week_date,
                cls.lob,
                cls.model,
                cls.mpn_id,
                cls.sku,
                cls.show_week,
                cls.data_type,
                cls.fcst,
            )\
                .filter(cls.fiscal_week_year == fiscal_week_year)\
                .filter(cls.rtm == rtm)\
                .filter(cls.cust_id == sold_to_id)\
                .filter(cls.data_type == "fcst") \
                .filter(cls.model.in_(model)) \
                .order_by(case(value=cls.model, whens={x: model.index(x)+1 for x in model}))\
                .order_by(cls.mpn_id)\
                .order_by(cls.show_week.asc())
            res = query.all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return res

    @classmethod
    def get_sold_to_data(cls):
        s = GcDmpSession()
        res = []
        try:
            query = s.query(
                DimFastSoldToMappingMulti.business_type,
                DimFastSoldToMappingMulti.sold_to_id,
                DimFastSoldToMappingMulti.sold_to_name,
            )\
                .filter(DimFastSoldToMappingMulti.rtm == StrRTMMulti)\
                .order_by(DimFastSoldToMappingMulti.business_type)
            sold_to_names = g.get(SoldToNameKey)
            if len(sold_to_names) != 1 or sold_to_names[0] != "All":
                query = query.filter(DimFastSoldToMappingMulti.sold_to_name.in_(sold_to_names))
            res = query.all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return res

    @classmethod
    def get_model_data(cls):
        s = GcDmpSession()
        res = []
        try:
            res = s.query(
                cls.lob,
                cls.model,
            ) \
                .filter(cls.model.in_(MODEL)) \
                .group_by(cls.model) \
                .order_by(cls.lob) \
                .order_by(case(value=cls.model, whens={x: MODEL.index(x) + 1 for x in MODEL})) \
                .all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return res


class DimFastSoldToMappingMulti(GcDmpBase):
    __tablename__ = "dim_fast_business_soldto_mapping_multi"
    __table_args__ = {"extend_existing": True}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    rtm = Column(String(256), comment='')
    account_manager = Column(String(256), comment='')
    business_type = Column(String(256), comment='')
    sold_to_id = Column(String(256), comment='')
    sold_to_name = Column(String(256), comment='')
    sold_to_name_cn = Column(String(256), comment='')
    start_week = Column(Integer, comment='')
    end_week = Column(Integer, comment='')

    @classmethod
    def get_by_role(cls, business_type: list, sold_to_id: list, sold_to_name: list, fiscal_week_year: int):
        s = GcDmpSession()
        res = []
        try:
            query = s.query(cls.business_type, cls.sold_to_id, cls.sold_to_name)\
                .filter(cls.start_week <= fiscal_week_year)\
                .filter(cls.end_week >= fiscal_week_year)\
                .order_by(case(value=cls.business_type,
                               whens={x: MultiBusinessTypeOrder.index(x) + 1 for x in MultiBusinessTypeOrder})) \
                .order_by(cast(cls.sold_to_id, Integer))
            sold_to_names = g.get(SoldToNameKey)
            if len(sold_to_names) != 1 or sold_to_names[0] != "All":
                query = query.filter(cls.sold_to_name.in_(sold_to_names))
            if business_type is not None and len(business_type) != 0:
                query = query.filter(cls.business_type.in_(business_type))
            if sold_to_name is not None and len(sold_to_name) != 0:
                query = query.filter(cls.sold_to_name.in_(sold_to_name))
            if sold_to_id is not None and len(sold_to_id) != 0:
                query = query.filter(cls.sold_to_id.in_(sold_to_id))
            res = query.all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return res

    @classmethod
    def get_cn_name(cls):
        s = GcDmpSession()
        res = []
        try:
            query = s.query(cls.sold_to_id, cls.sold_to_name_cn).filter(cls.sold_to_id == g.get(SoldToIdKey))
            res = query.all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        if len(res) == 0:
            return None
        return res[0]

    @classmethod
    def get_business_type_list(cls, fiscal_week_year: int):
        s = GcDmpSession()
        res = []
        try:
            query = s.query(cls.business_type) \
                .filter(cls.start_week <= fiscal_week_year) \
                .filter(cls.end_week >= fiscal_week_year) \
                .group_by(cls.business_type) \
                .order_by(case(value=cls.business_type,
                               whens={x: MultiBusinessTypeOrder.index(x) + 1 for x in MultiBusinessTypeOrder}))
            sold_to_names = g.get(SoldToNameKey)
            if len(sold_to_names) != 1 or sold_to_names[0] != "All":
                query = query.filter(DimFastSoldToMappingMulti.sold_to_name.in_(sold_to_names))
            res = query.all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return res

    @classmethod
    def get_sold_to_list(cls, business_type: list, sold_to_id=None, sold_to_name=None, fiscal_week_year=None):
        s = GcDmpSession()
        res = []
        try:
            query = s.query(cls.business_type, cls.sold_to_id, cls.sold_to_name)\
                .filter(cls.start_week <= fiscal_week_year)\
                .filter(cls.end_week >= fiscal_week_year)
            sold_to_names = g.get(SoldToNameKey)
            if len(sold_to_names) != 1 or sold_to_names[0] != "All":
                query = query.filter(DimFastSoldToMappingMulti.sold_to_name.in_(sold_to_names))
            if business_type is not None and len(business_type) != 0:
                query = query.filter(cls.business_type.in_(business_type))
            if sold_to_id is not None:
                query = query.filter(cls.sold_to_id.like(f"%{sold_to_id}%"))
            if sold_to_name is not None:
                query = query.filter(cls.sold_to_name.like(f"%{sold_to_name}%"))
            res = query.group_by(cls.sold_to_id) \
                .order_by(case(value=cls.business_type,
                               whens={x: MultiBusinessTypeOrder.index(x) + 1 for x in MultiBusinessTypeOrder})) \
                .order_by(cast(cls.sold_to_id, Integer)) \
                .all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return res
