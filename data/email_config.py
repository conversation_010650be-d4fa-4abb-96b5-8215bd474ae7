from sqlalchemy import Enum, text
from sqlalchemy.dialects.mysql import LON<PERSON><PERSON>XT

from domain.email.entity.email_config import EmailConfig
from util.const import ErrorExcept, ErrCode, DataSourceFileStatus
from util.fast_lite_base import *


class EmailConfigRepository(FASTLiteBase):
    __tablename__ = 'email_config'
    __table_args__ = {"extend_existing": True}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    cmd = Column(String(256), comment='')
    content_type = Column(Enum('plain', 'html'), comment='plain/html')
    subject = Column(String(128), comment='')
    content = Column(Text, comment='')
    params = Column(String(256), comment='')
    attachments = Column(String(512), comment='')
    recipients = Column(String(512), comment='')
    cc = Column(String(256), comment='')
    bcc = Column(String(512), comment='')
    is_base_template = Column(SmallInteger, comment='')
    frequency = Column(String(512), comment='')
    template = Column(String(128), comment='')
    from_email = Column(String(128), comment='')

    def save(self):
        s = FASTLiteSession()
        try:
            s.add(self)
            s.commit()
        except Exception as e:
            raise ErrorExcept(ErrCode.DBInsert, "insert to database failed" + str(e))
        finally:
            s.close()
        return self.id

    @classmethod
    def query_email_config(cls, cmd):
        s = FASTLiteSession()
        try:
            return s.query(cls).filter(cls.cmd == cmd).first()
        except Exception as e:
            raise ErrorExcept(ErrCode.DBQueryError, "db query failed")
        finally:
            s.close()

    @classmethod
    def update_email_config(cls, cmd, update_map: dict):
        s = FASTLiteSession()
        try:
            s.query(cls).filter(cls.cmd == cmd).update(update_map)
            s.commit()
        except Exception as e:
            raise ErrorExcept(ErrCode.DBQueryError, "db update failed")
        finally:
            s.close()


    @classmethod
    def get_email_config(cls, cmd: str):
        s = FASTLiteSession()
        try:
            ret = s.query(cls).filter(cls.cmd == cmd).first()
            return EmailConfig(
                ret.cmd, ret.content_type,
                ret.subject, ret.content,
                ret.params, ret.attachments,
                ret.recipients,ret.cc,
                ret.bcc, ret.is_base_template,
                ret.frequency
            )
        except Exception as e:
            logger.exception(e)
            raise ErrorExcept(ErrCode.DBQueryError,
                              f"query email config failed, {str(e)}")
        finally:
            s.close()

    @classmethod
    def query_email_config_with_template(cls, cmd):
        s = FASTLiteSession()
        try:
            return (s.query(cls.cmd, cls.subject, cls.params, cls.attachments,
                          cls.recipients, cls.cc, cls.bcc, cls.is_base_template,
                          cls.frequency, EmailTemplateRepository.content_type,
                          EmailTemplateRepository.content, cls.from_email)
                    .join(EmailTemplateRepository, cls.template == EmailTemplateRepository.cmd)
                    .filter(cls.cmd == cmd).first())
        except Exception as e:
            raise ErrorExcept(ErrCode.DBQueryError, "db query failed")
        finally:
            s.close()

    @classmethod
    def get_email_config_with_template(cls, cmd: str):
        s = FASTLiteSession()
        try:
            ret = (s.query(cls.cmd, cls.subject, cls.params, cls.attachments,
                          cls.recipients, cls.cc, cls.bcc, cls.is_base_template,
                          cls.frequency, EmailTemplateRepository.content_type,
                          EmailTemplateRepository.content)
                   .join(EmailTemplateRepository, cls.template == EmailTemplateRepository.cmd)
                   .filter(cls.cmd == cmd).first())
            return EmailConfig(
                ret.cmd, ret.content_type,
                ret.subject, ret.content,
                ret.params, ret.attachments,
                ret.recipients,ret.cc,
                ret.bcc, ret.is_base_template,
                ret.frequency
            )
        except Exception as e:
            logger.exception(e)
            raise ErrorExcept(ErrCode.DBQueryError,
                              f"query email config failed, {str(e)}")
        finally:
            s.close()


class EmailTemplateRepository(FASTLiteBase):
    __tablename__ = 'email_template'
    __table_args__ = {"extend_existing": True}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    cmd = Column(String(128), comment='')
    description = Column(String(256), comment='')
    content_type = Column(Enum('plain', 'html'), comment='plain/html')
    content = Column(LONGTEXT, comment='邮件内容为整个html文件内容')
    create_time = Column(DateTime,  server_default=text('CURRENT_TIMESTAMP'))
    update_time = Column(DateTime, server_default=text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'))
