from sqlalchemy import *
from datetime import datetime
from util.conf import logger, Session
from util.fast_lite_base import ChannelComplianceBase, ChannelComplianceSession


class ScanAssistInfoRepository(ChannelComplianceBase):
    __tablename__ = 'hitl_verification_result'
    __table_args__ = {'schema': "channel_compliance"}

    id = Column(Integer, primary_key=True, autoincrement=True, nullable=False, comment='ID')
    ml_percentage = Column(Integer)
    human_verification = Column(String(16))
    rtm = Column(String(16))
    sub_rtm = Column(String(16))
    disti_id = Column(String(16))
    disti_name = Column(String(64))
    reseller_id = Column(String(16))
    reseller_name = Column(String(64))
    pos_id = Column(String(16))
    pos_name = Column(String(64))
    lob = Column(String(16))
    sub_lob = Column(String(16))
    mpn = Column(String(16))
    original_image_url = Column(String(511))
    efs_image_url = Column(String(511))
    source = Column(String)
    scan_type = Column(String(16))
    scan_time = Column(DateTime)
    verified_status = Column(String(16))
    is_audited = Column(Boolean)
    verifier = Column(String(32))
    verifier_id = Column(String(16))
    verified_time = Column(DateTime)
    classification_result = Column(String)
    comment = Column(String)
    retry_times = Column(Integer, default=0)
    random_id = Column(String)
    create_time = Column(DateTime)
    update_time = Column(DateTime, default=datetime.now)
    
    inventory_status = Column(String(32))
    error_code = Column(String(32))
    is_kiosk_marked = Column(Integer)

    @classmethod
    def get_scan_list(cls, limit: int, offset: int, source: str) -> list:
        ret = []
        s = ChannelComplianceSession()
        try:
            q = s.query(cls.id, cls.retry_times, cls.original_image_url) \
                .filter(cls.source == source) \
                .filter(func.trim(cls.original_image_url) != "") \
                .filter(cls.efs_image_url == "") \
                .filter(cls.retry_times < 3) \
                .filter(cls.ml_percentage >= 0)
                
            
            q = q.order_by(cls.scan_time.desc()) \
                .order_by(cls.pos_id.desc()) \
                .limit(limit).offset(offset)
            ret = q.all()
            # logger.info({"sql": str(q)})
        except Exception as e:
            logger.error(e)
        finally:
            s.close()
        return ret

    @classmethod
    def get_gbi_data_count(cls, source: str = "GBI") -> list:
        ret = []
        s = ChannelComplianceSession()
        try:
            q = s.query(cls.id) \
                .filter(cls.source == source) \
                .filter(func.trim(cls.original_image_url) != "") \
                .filter(cls.efs_image_url == "") \
                .filter(cls.retry_times < 3) \
                .filter(cls.ml_percentage >= 0)
                
            ret = q.count()
        except Exception as e:
            logger.error(e)
        finally:
            s.close()
        return ret

    @classmethod
    def get_bz_data_count(cls, source: str) -> int:
        ret = 0
        s = ChannelComplianceSession()
        try:
            ret = s.query(cls) \
                .filter(cls.source == source) \
                .count()
        except Exception as e:
            logger.error(e)
        finally:
            s.close()
        return ret

    @classmethod
    def bulk_update_scan_data(cls, save_list: list) -> str:
        s = ChannelComplianceSession()
        err = ""
        try:
            s.bulk_update_mappings(ScanAssistInfoRepository, save_list)
            s.commit()
        except Exception as e:
            logger.error({"execute sql error": e})
            err = str(e)
        finally:
            s.close()
        return err

    @classmethod
    def bulk_insert_or_update(cls, objs: list):
        s = ChannelComplianceSession()
        try:
            now = datetime.now()
            for obj in objs:
                obj['create_time'] = now
                obj['update_time'] = now
            insert_stmt = f"""
                INSERT INTO {cls.__tablename__} 
                                (ml_percentage, rtm, sub_rtm, disti_id, disti_name, reseller_id, reseller_name, pos_id, pos_name, lob, sub_lob, 
                                mpn, original_image_url, source, scan_type, scan_time, random_id, final_result, create_time, update_time, inventory_status, error_code, is_kiosk_marked)
                                VALUES 
                                (:ml_percentage, :rtm, :sub_rtm, :disti_id, :disti_name, :reseller_id, :reseller_name, :pos_id, :pos_name, :lob, :sub_lob, 
                                :mpn, :original_image_url, :source, :scan_type, :scan_time, :random_id, :final_result, :create_time, :update_time, :inventory_status, :error_code, :is_kiosk_marked)
            """
            result = s.execute(insert_stmt, objs)
            s.commit()
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return result

    @classmethod
    def get_max_random_id_by_scan_time(cls, scan_time: str, end_time: str) -> str:
        result = ""
        try:
            with ChannelComplianceSession() as s:
                data = s.query(cls.random_id).filter(cls.scan_time >= scan_time).filter(cls.scan_time <= end_time).order_by(cls.random_id.desc()).first()
                if data:
                    result = data[0]
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return result

    @classmethod
    def count_by_scan_time(cls, scan_time: str, end_time: str) -> int:
        count = 0
        try:
            with ChannelComplianceSession() as s:
                count = s.query(func.count(1)).filter(
                    cls.scan_time >= scan_time,
                    cls.scan_time <= end_time
                ).scalar()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return count

    @classmethod
    def count_not_sync_data(cls, scan_time: str, end_time: str) -> list:
        ret = []
        s = ChannelComplianceSession()
        try:
            q = s.query(cls.id) \
                .filter(func.trim(cls.original_image_url) != "") \
                .filter(cls.efs_image_url == "") \
                .filter(cls.retry_times < 3) \
                .filter(cls.ml_percentage >= 0) \
                .filter(
                    cls.scan_time >= scan_time,
                    cls.scan_time <= end_time
                )
            ret = q.count()
        except Exception as e:
            logger.error(e)
            raise e
        finally:
            s.close()
        return ret
