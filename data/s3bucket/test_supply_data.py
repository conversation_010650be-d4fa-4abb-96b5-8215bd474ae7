import csv
import pandas as pd

from data.s3bucket.supply_data import download_parquet_file_from_s3bucket, list_dir, list_files
from domain.supply.entity import SupplyDataFile, ConfirmFlow
import re
from data.cpf_data_source import OdsFastCPFActiveSKULob
from domain.supply.supply import reread
from kit.save_as_file import save_as_zip, save_as_csv
import pyarrow as pa
import pyarrow.parquet as pq


def test_download_file():
    lob = "iPhone"
    files = list_files(prefix=f"databend-dumps/FAST/PHOEBE/{lob}/")
    print("---------")
    for file in files:
        print(file.file_name)

    df = download_parquet_file_from_s3bucket(
        object_key='databend-dumps/FAST/PHOEBE/iPad/FY24Q2W5/2024-02-0412:02:48/data_2e095c98-318f-404c-87bb-d5fbf1e1da57_0000_00000000.parquet'
                                 )

    # 将DataFrame转换为pyarrow.Table对象
    table = pa.Table.from_pandas(df)

    # 指定要写入的Parquet文件路径
    output_path = 'output.parquet'

    # 将Table对象写入Parquet文件
    pq.write_table(table, output_path)

    #df = df[df['ops_line_desc'] in ['Series 8 GPS', 'Series 8 Cell']]
    mpns = {}

    for index, row in df.iterrows():
        if row['ops_line_desc'] == 'Series 8 GPS' or row['ops_line_desc']  == 'Series 8 Cell':
            mpns[row['prod_id']] = '1'
    print("mpns:",mpns)

    wanted_mpns = OdsFastCPFActiveSKULob.list_mpns('Watch')
    print("wanted_mpns",wanted_mpns)
    result_mpns = [mpn for mpn in mpns if mpn in wanted_mpns]
    print("result_mpns", result_mpns)
    #print(df.loc[:, ['prod_id', 'ops_line_desc']])



def test_process_data_frame():
    supply_data = SupplyDataFile()
    case1 = {
        'fiscal_week': ['FY24W16', 'FY24W16', 'FY24W16'],
        'sales_org': ['China mainland', 'HK', 'China mainland'],
        'ops_line_desc': ['airTag', 'airTag', 'airTag'],
        'created_ts': ['2024-01-16 16:24:55.073', '2024-01-17 16:24:55.073', '2024-01-19 7:34:55.073'],
        'updated_ts': ['2024-01-16 16:24:55.073', '2024-01-17 16:24:55.073', '2024-01-19 7:34:55.073'],
    }
    expected = {
        'fiscal week': ['FY24W16', 'FY24W16', 'FY24W16'],
        'sales_org': ['China mainland', 'HK', 'China mainland'],
        'ops_line_desc': ['airTag', 'airTag', 'airTag'],
        'created_ts': [
            pd.to_datetime('2024-01-16 16:24:55.073'),
            pd.to_datetime('2024-01-17 16:24:55.073'),
            pd.to_datetime('2024-01-19 7:34:55.073')],
        'updated_ts': [
            pd.to_datetime('2024-01-16 16:24:55.073'),
            pd.to_datetime('2024-01-17 16:24:55.073'),
            pd.to_datetime('2024-01-19 07:34:55.073')
        ],
        'max_updated_ts': [
            pd.to_datetime('2024-01-19 07:34:55.073'),
            pd.to_datetime('2024-01-17 16:24:55.073'),
            pd.to_datetime('2024-01-19 07:34:55.073')]
    }
    new_df = supply_data.process_data_frame(pd.DataFrame(case1))
    print(new_df)
    expected_df = pd.DataFrame(expected)
    print(expected_df)
    assert new_df.equals(expected_df)


def test_merge_confirm_status():
    supply_data = SupplyDataFile()
    case1 = {
        'fiscal week': ['FY24W15', 'FY24W15', 'FY24W15','FY24W15'],
        'sales_org': ['China mainland', 'HK', 'China mainland', 'China mainland'],
        'ops_line_desc': ['airTag', 'airTag', 'airTag', 'iPad (10th Gen)'],
        'updated_ts': [pd.to_datetime('2024-01-16 16:24:55.073'),
                       pd.to_datetime('2024-01-17 16:24:55.073'),
                       pd.to_datetime('2024-01-19 07:34:55.073'),
                       pd.to_datetime('2024-01-19 07:34:55.073')],
        'max_updated_ts': [pd.to_datetime('2024-01-19 07:34:55.073'),
                           pd.to_datetime('2024-01-17 16:24:55.073'),
                           pd.to_datetime('2024-01-19 07:34:55.073'),
                           pd.to_datetime('2024-01-19 07:34:55.073')]
    }

    confirm_flows = [
        ConfirmFlow('FY24W15', 'China mainland', 'airTag', pd.to_datetime('2024-01-17 16:24:55.073')),
        ConfirmFlow('FY24W15', 'China mainland', 'HK', pd.to_datetime('2024-01-16 16:24:55.073')),
    ]

    expected = {
        'fiscal week': ['FY24W15', 'FY24W15', 'FY24W15', 'FY24W15'],
        'sales_org': ['China mainland', 'HK', 'China mainland','China mainland'],
        'ops_line_desc': ['airTag', 'airTag', 'airTag','iPad (10th Gen)'],
        'updated_ts': [pd.to_datetime('2024-01-16 16:24:55.073'),
                       pd.to_datetime('2024-01-17 16:24:55.073'),
                       pd.to_datetime('2024-01-19 07:34:55.073'),
                       pd.to_datetime('2024-01-19 07:34:55.073')],
        'max_updated_ts': [pd.to_datetime('2024-01-19 07:34:55.073'),
                           pd.to_datetime('2024-01-17 16:24:55.073'),
                           pd.to_datetime('2024-01-19 07:34:55.073'),
                           pd.to_datetime('2024-01-19 07:34:55.073')],
        supply_data.CONFIRM_AT: ['2024-01-17 16:24:55','','2024-01-17 16:24:55',''],
        supply_data.CONFIRM_STATUS: ['', '', '', ''],
    }
    new_df = supply_data.merge_confirm_status(pd.DataFrame(case1), confirm_flows)
    new_df[supply_data.CONFIRM_STATUS] = new_df[supply_data.CONFIRM_STATUS].astype(str)

    res = save_as_csv(new_df,'/','cancanjin.csv')
    print("file:::",res)
    print(new_df)
    print(pd.DataFrame(expected))
    assert new_df.equals(pd.DataFrame(expected))

    # 有确认
    case2 = {
        'fiscal week': ['FY24W16', 'FY24W16', 'FY24W16'],
        'sales_org': ['China mainland', 'HK', 'China mainland'],
        'ops_line_desc': ['airTag', 'airTag', 'airTag'],
        'max_updated_ts': [pd.to_datetime('2024-01-19 07:34:55.073'),
                           pd.to_datetime('2024-01-17 16:24:55.073'),
                           pd.to_datetime('2024-01-19 07:34:55.073')]
    }
    confirm_flows = [
        ConfirmFlow('FY24W16', 'China mainland', 'airTag', pd.to_datetime('2024-01-19 7:35:55.073')),
    ]
    expected = {
        'fiscal week': ['FY24W16', 'FY24W16', 'FY24W16'],
        'sales_org': ['China mainland', 'HK', 'China mainland'],
        'ops_line_desc': ['airTag', 'airTag', 'airTag'],
        'max_updated_ts': [pd.to_datetime('2024-01-19 07:34:55.073'),
                           pd.to_datetime('2024-01-17 16:24:55.073'),
                           pd.to_datetime('2024-01-19 07:34:55.073')],
        supply_data.CONFIRM_AT: ['2024-01-19 07:35:55', '', '2024-01-19 07:35:55'],
        supply_data.CONFIRM_STATUS: ['confirm', '', 'confirm'],
    }
    new_df = supply_data.merge_confirm_status(pd.DataFrame(case2), confirm_flows)
    new_df[supply_data.CONFIRM_STATUS] = new_df[supply_data.CONFIRM_STATUS]
    print("---------get---------")
    print(new_df)
    print("---------get---------")
    print("---------expected---------")
    print(pd.DataFrame(expected))
    print("---------expected---------")
    assert new_df.equals(pd.DataFrame(expected))


def test_save_as_zip():
    case1 = {
        'fiscal_week': ['FY24W16', 'FY24W16', 'FY24W16'],
        'sales_org': ['China mainland', 'HK', 'China mainland'],
        'ops_line_desc': ['airTag', 'airTag', 'airTag'],
        'updated_ts': ['2024-01-16 16:24:55.073', '2024-01-17 16:24:55.073', '2024-01-19 7:34:55.073'],
    }
    new_df = pd.DataFrame(case1)
    path = f'/uploads/datasource/supply/'
    csv_name = 'test.csv'
    zip_name = 'test.zip'
    csv_path, path = save_as_csv(new_df, path, csv_name)
    reread(csv_path)
    save_as_zip(csv_path, csv_name, path,  zip_name)

