import io
import pandas as pd
import boto3
from util.const import ErrorExcept, ErrCode
from domain.supply.entity import SupplyFileName
from util.conf import logger

BUCKET_NAME = "dmpdata-aurora-bucket"


def list_files(bucket_name=BUCKET_NAME, prefix="") -> tuple[list[SupplyFileName], list[str]]:
    #  周目录
    week_names = list_dir_by_delimiter(bucket_name, prefix, delimiter="/")
    weeks = list_fiscal_weeks(week_names)

    # 最近两周下所有对象
    files: list[SupplyFileName] = []
    for index, week in enumerate(weeks):
        if index > 1: # 最多取最新两周
            break
        files.extend(list_dir(bucket_name, f"{prefix}{week}/"))

    return files, weeks


# list_dir_by_delimiter prefix下一级目录
def list_dir_by_delimiter(bucket_name=BUCKET_NAME, prefix="", delimiter="/") -> list[SupplyFileName]:
    s3 = boto3.client("s3", region_name='us-west-2')

    response = s3.list_objects_v2(Bucket=bucket_name, Prefix=prefix, Delimiter=delimiter)

    if "CommonPrefixes" not in response:
        raise ErrorExcept(ErrCode.S3BucketNoContentInResponse,
                          f"no CommonPrefixes in response from Bucket {bucket_name}, prefix {prefix} and delimiter {delimiter}")
    files = response["CommonPrefixes"]
    return [SupplyFileName(f.get('Prefix')) for f in files]


def list_dir(bucket_name=BUCKET_NAME, prefix="") -> list[SupplyFileName]:
    s3 = boto3.client("s3", region_name='us-west-2')
    response = s3.list_objects_v2(Bucket=bucket_name, Prefix=prefix)
    key = "Contents"
    if key not in response:
        raise ErrorExcept(ErrCode.S3BucketNoContentInResponse,
                          f"no {key} in response from Bucket {bucket_name}, prefix {prefix}")
    files = response[key]
    new_files = [SupplyFileName(f.get('Key')) for f in files]
    # for file in new_files:
    #     print(f"list_dir file_name:{file.file_name} update_at:{file.updated_at} fiscal_qtr_week_name:{file.fiscal_qtr_week_name}")
    return new_files


def latest_fiscal_week(files: list[SupplyFileName]) -> str:
    weeks = list_fiscal_weeks(files)
    return weeks[0]


def list_fiscal_weeks(files: list[SupplyFileName]) -> list[str]:
    def sort_by_fiscal_week(file: SupplyFileName) -> str:
        return file.fiscal_week

    files = sorted(files, key=sort_by_fiscal_week, reverse=True)
    if len(files) == 0:
        raise ErrorExcept(ErrCode.Param, "no files found.")
    fiscal_weeks = [file.fiscal_qtr_week_name for file in files]
    # 去重
    distinct_fiscal_weeks = []
    for fiscal_week in fiscal_weeks:
        if fiscal_week not in distinct_fiscal_weeks:
            distinct_fiscal_weeks.append(fiscal_week)
    return distinct_fiscal_weeks


def download_parquet_file_from_s3bucket(bucket_name=BUCKET_NAME, object_key="", is_debug=False) -> pd.DataFrame:
    if is_debug:
        df = pd.read_parquet('/tmp/output.parquet')
    else:
        s3 = boto3.resource("s3")
        buffer = io.BytesIO()
        s3.Object(bucket_name, object_key).download_fileobj(buffer)
        df = pd.read_parquet(buffer)

    # parquet文件解析出来有二进制前缀，这里处理下
    def decode_byte_string(value):
        if isinstance(value, bytes):
            return value.decode("utf-8")
        else:
            return value

    df = df.applymap(decode_byte_string)
    return df
