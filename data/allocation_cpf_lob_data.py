from util.fast_lite_base import *
from util.gc_dmp_datasource import *
from sqlalchemy import desc


class TblAllocationCpfLob(FASTLiteBase):
    __tablename__ = "tbl_allocation_cpf_lob"
    __table_args__ = {'schema': 'fast_lite'}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    fiscal_qtr_week_name = Column(String(256), comment='FY23Q1W12')
    fiscal_week_year = Column(Integer)
    lob = Column(String(256))
    phase = Column(SmallInteger)
    last_operator = Column(String(256))
    last_updated = Column(DateTime)
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    def __init__(self, fiscal_qtr_week_name: str, lob: str, phase: int, fiscal_week_year: int, last_operator: str = None,
                 last_updated: DateTime = None):
        self.fiscal_qtr_week_name = fiscal_qtr_week_name
        self.fiscal_week_year = fiscal_week_year
        self.lob = lob
        self.phase = phase
        self.last_operator = last_operator
        self.last_updated = last_updated

    @classmethod
    def get_allocation_cpf_lob_list(cls, fiscal_qtr_week_name):
        s = FASTLiteSession()
        ret = []
        try:
            ret = s.query(cls) \
                .filter(cls.fiscal_qtr_week_name == fiscal_qtr_week_name) \
                .all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

    @classmethod
    def get_by_week_lob(cls, fiscal_week_year, lob):
        s = FASTLiteSession()
        ret = []
        try:
            ret = s.query(cls) \
                .filter(cls.fiscal_week_year == fiscal_week_year) \
                .filter(cls.lob == lob) \
                .all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

    @classmethod
    def bulk_save(cls, objs: list):
        s = FASTLiteSession()
        ret = []
        try:
            s.bulk_save_objects(objs, return_defaults=True)
            s.commit()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

    @classmethod
    def update_data(cls, fiscal_qtr_week_name: str, lob: str, update_data: dict):
        s = FASTLiteSession()
        try:
            s.query(cls) \
                .filter(cls.fiscal_qtr_week_name == fiscal_qtr_week_name) \
                .filter(cls.lob == lob) \
                .update(update_data)
            s.commit()
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()

    @classmethod
    def update_by_fiscal_week_year(cls, fiscal_week_year: int, lob: str, update_data: dict):
        s = FASTLiteSession()
        try:
            s.query(cls) \
                .filter(cls.fiscal_week_year == fiscal_week_year) \
                .filter(cls.lob == lob) \
                .update(update_data)
            s.commit()
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()

    @classmethod
    def get_by_id(cls, idx: int) -> dict:
        s = FASTLiteSession()
        ret = {}
        try:
            ret = s.query(cls) \
                .filter(cls.id == idx) \
                .one_or_none()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

    @classmethod
    def get_cpf_lob_list(cls, lob):
        s = FASTLiteSession()
        ret = []
        try:
            ret = s.query(cls) \
                .filter(cls.lob == lob) \
                .order_by(desc(cls.fiscal_week_year)) \
                .limit(14).all()
            ret = [{
                "fiscal_qtr_week_name": x.fiscal_qtr_week_name,
                "fiscal_week_year": x.fiscal_week_year} for x in ret]
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret


class TblAllocationCpfConfig(FASTLiteBase):
    __tablename__ = "tbl_allocation_cpf_config"
    __table_args__ = {'schema': 'fast_lite'}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    config_type = Column(String(50), comment='唯一标识')
    week = Column(SmallInteger)
    time = Column(String(20))
    sort = Column(Integer)
    group_by = Column(String(50), comment='demand_collection_sales_input , demand_collection_sell_in_demand')
    remark = Column(String(256))

    @classmethod
    def get_by_config_type(cls, config_type: str):
        s = FASTLiteSession()
        ret = None
        try:
            ret = s.query(cls) \
                .filter(cls.config_type == config_type) \
                .one_or_none()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret


class OdsFastCpfSalesInputUpload(ExDmpBase):
    __tablename__ = f"ods_fast_cpf_sales_input_upload{'_test' if env_dev() else ''}"
    __table_args__ = {"schema": "gc_dmp_datasource"}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    fiscal_qtr_week_name = Column(String(256))
    fiscal_week_year = Column(Integer)
    week_begin_dt = Column(Date)
    week_end_dt = Column(Date)
    sales_org = Column(String(256))
    rtm = Column(String(256))
    rtml4 = Column(String(256))
    lob = Column(String(256))
    prod = Column(String(256))
    project_code = Column(String(256))
    nand = Column(String(256))
    color = Column(String(256))
    mpn = Column(String(256))
    odq = Column(Integer)
    sold_to_id = Column(Integer)
    sold_to_name_en = Column(String(256))
    qtw_shipment_plan = Column(Integer)
    cw_shipment_plan = Column(Integer)
    sni_cw_minus_1 = Column(Integer)
    shipment_plan_cw1 = Column(Integer)
    shipment_plan_cw2 = Column(Integer)
    eoh = Column(Integer)
    st_ub_qty_cw_minus1 = Column(Integer)
    st_ub_5wk_bwd_avg = Column(Integer)
    open_backlog_over_published_sp_cw3 = Column(Integer)
    priority = Column(String(256))
    sales_input_qty_cw1 = Column(Float)
    sales_input_qty_cw2 = Column(Float)
    sales_input_qty_cw3 = Column(Float)
    sales_input_qty_cw4 = Column(Float)
    reason_cw1 = Column(String(256))
    reason_cw2 = Column(String(256))
    reason_cw3 = Column(String(256))
    reason_cw4 = Column(String(256))
    comments = Column(String(256))
    version = Column(Integer)
    create_time = Column(DateTime)
    update_time = Column(DateTime)

    @classmethod
    def bulk_save(cls, objs: list):
        s = ExDmpSession()
        ret = []
        try:
            s.bulk_insert_mappings(cls, objs)
            s.commit()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret


    @classmethod
    def delete_by_week(cls, fiscal_qtr_week_name: str, rtm: str, lob: str):
        s = ExDmpSession()
        try:
            q = s.query(cls) \
                .filter(cls.fiscal_qtr_week_name == fiscal_qtr_week_name) \
                .filter(cls.rtm == rtm) \
                .filter(cls.lob == lob)
            q.delete()
            s.commit()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()

    @classmethod
    def find_by_rtm_and_lob(cls, rtm: str, fiscal_week_year: int, lob: str) -> pd.DataFrame:
        s = ExDmpSession()
        ret = pd.DataFrame()
        try:
            query = s.query(cls) \
                .filter(cls.fiscal_week_year == fiscal_week_year) \
                .filter(cls.rtm == rtm) \
                .filter(cls.lob == lob)
            ret = pd.read_sql_query(query.statement, ExDmpEngine)
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret
