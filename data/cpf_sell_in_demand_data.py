from util.const import Error<PERSON>xcept, ErrCode, DataSourceFileStatus
from util.fast_lite_base import *
from sqlalchemy import or_, and_, func, desc, asc, union_all
from util.gc_dmp_base import *


class TblAllocationPrepareFile(FASTLiteBase):
    __tablename__ = 'tbl_allocation_prepare_file'
    __table_args__ = {"extend_existing": True,
                      "schema": "fast_lite"}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    fiscal_qtr_week_name = Column(String(256))
    fiscal_week_year = Column(Integer)
    rtm = Column(String(64))
    lob = Column(String(32))
    operate_phase = Column(String(16))
    upload_file_name = Column(String(256))
    upload_file_path = Column(String(256))
    upload_file_version = Column(Integer)
    upload_by = Column(String(256), comment='Nick Name + Last Name')
    upload_at = Column(DateTime)
    update_by = Column(String(256), comment='Nick Name + Last Name')
    uploader_email = Column(String(256))
    category = Column(Integer)
    create_time = Column(DateTime)
    update_time = Column(DateTime)

    @classmethod
    def get_allocation_prepare_file_list(cls, fiscal_week_year, operate_phase, lob):
        s = FASTLiteSession()
        ret = []
        try:
            ret = s.query(cls) \
                .filter(cls.fiscal_week_year == fiscal_week_year) \
                .filter(cls.operate_phase == operate_phase) \
                .filter(cls.lob == lob) \
                .all()
            ret = [{
                "id": x.id,
                "rtm": x.rtm,
                'category': x.category,
                'upload_file_name': x.upload_file_name,
                'upload_file_path': x.upload_file_path,
                'upload_file_version': x.upload_file_version,
                'upload_at': x.upload_at,
                'update_by': x.update_by} for x in ret]
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

    @classmethod
    def get_allocation_prepare_file(cls, fiscal_week_year, operate_phase, lob, category, rtm):
        s = FASTLiteSession()
        ret = None
        try:
            ret = s.query(cls) \
                .filter(cls.fiscal_week_year == fiscal_week_year) \
                .filter(cls.category == category) \
                .filter(cls.operate_phase == operate_phase) \
                .filter(cls.rtm == rtm) \
                .filter(cls.lob == lob) \
                .one_or_none()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

    @classmethod
    def get_allocation_prepare_file_by_rtm_list(cls, fiscal_week_year, operate_phase, lob, category, rtm_list):
        s = FASTLiteSession()
        ret = []
        try:
            ret = s.query(cls) \
                .filter(cls.fiscal_week_year == fiscal_week_year) \
                .filter(cls.operate_phase == operate_phase) \
                .filter(cls.category == category) \
                .filter(cls.rtm.in_(rtm_list)) \
                .filter(cls.lob == lob) \
                .all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

    @classmethod
    def update_data(cls, fiscal_week_year, operate_phase, lob, rtm, category, update_data):
        s = FASTLiteSession()
        try:
            s.query(cls) \
                .filter(cls.fiscal_week_year == fiscal_week_year) \
                .filter(cls.operate_phase == operate_phase) \
                .filter(cls.category == category) \
                .filter(cls.rtm == rtm) \
                .filter(cls.lob == lob) \
                .update(update_data)
            s.commit()
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()

    @classmethod
    def bulk_save(cls, datas: list):
        s = FASTLiteSession()
        try:
            s.bulk_insert_mappings(cls, datas)
            s.commit()
        except Exception as e:
            s.rollback()
            raise ErrorExcept(ErrCode.DBInsert, "insert to database failed" + str(e))
        finally:
            s.close()
        return cls.id

    @classmethod
    def delete(cls, fiscal_week_year, operate_phase, rtm, lob, category):
        s = FASTLiteSession()
        try:
            q = s.query(TblAllocationPrepareFile) \
                .filter(TblAllocationPrepareFile.fiscal_week_year == fiscal_week_year) \
                .filter(TblAllocationPrepareFile.category == category) \
                .filter(TblAllocationPrepareFile.operate_phase == operate_phase) \
                .filter(TblAllocationPrepareFile.lob == lob)
            if rtm:
                q = q.filter(TblAllocationPrepareFile.rtm == rtm)
            q.delete()
            s.commit()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()

    @classmethod
    def insert_and_update_unique(cls, data: dict):
        s = FASTLiteSession()
        try:
            s.bulk_insert_mappings(cls, [data])
            s.commit()
        except IntegrityError as e:
            s1 = FASTLiteSession()
            s1.query(cls).filter(cls.fiscal_week_year == data['fiscal_week_year'])\
                .filter(cls.rtm == data['rtm'])\
                    .filter(cls.lob == data['lob'])\
                        .filter(cls.operate_phase == data['operate_phase'])\
                            .filter(cls.category == data['category']) \
                                .update(data)
            s1.commit()
            s1.close()
        finally:
            s.close()
        return cls.id


class AppFastAllocationDemandSellInMonoWi(GcDmpBase):
    __tablename__ = f"app_fast_allocation_demand_sell_in_mono_wi{'_test' if env_dev() else ''}"
    __table_args__ = {"extend_existing": True,
                      "schema": "gc_dmp_fast"}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    fiscal_qtr_week_name = Column(String(256))
    fiscal_week_year = Column(Integer)
    sales_org = Column(String(256))
    rtm = Column(String(256))
    rtml4 = Column(String(256))
    lob = Column(String(256))
    prod = Column(String(256))
    project_code = Column(String(256))
    nand = Column(String(256))
    color = Column(String(256))
    mpn = Column(String(256))
    hr_lr = Column(String(256))
    odq = Column(Integer)
    sold_to_id = Column(Integer)
    sold_to_name_en = Column(String(256))
    qtw_shipment_plan = Column(Integer)
    shipment_plan_cw = Column(Integer)
    sni_cw_minus_1 = Column(Integer)
    shipment_plan_cw1 = Column(Integer)
    shipment_plan_cw2 = Column(Integer)
    eoh = Column(Integer)
    st_ub_qty_cw_minus1 = Column(Integer)
    st_ub_5wk_bwd_avg = Column(Integer)
    open_backlog_over_published_sp_cw3 = Column(Integer)
    priority = Column(String(256))
    sales_input_qty_cw1 = Column(Float)
    sales_input_qty_cw2 = Column(Float)
    sales_input_qty_cw3 = Column(Float)
    sales_input_qty_cw4 = Column(Float)
    reason_cw1 = Column(String(256))
    reason_cw2 = Column(String(256))
    reason_cw3 = Column(String(256))
    reason_cw4 = Column(String(256))
    comments = Column(String(256))
    create_time = Column(DateTime)
    update_time = Column(DateTime)

    @classmethod
    def get_sell_in_demand_list(cls, fiscal_qtr_week_name, lob, hr_lr):
        s = GcDmpSession()
        ret = []
        try:
            ret = s.query(cls) \
                .filter(cls.fiscal_qtr_week_name == fiscal_qtr_week_name) \
                .filter(cls.lob == lob)
            if hr_lr:
                ret = ret.filter(cls.hr_lr == hr_lr)
            ret = ret.all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

    @classmethod
    def get_publish_summary(cls, fiscal_qtr_week_name: str, lob: str):
        s = GcDmpSession()
        ret = []
        try:
            res = s.query(cls.prod.label('prod'), cls.project_code.label('project_code'),
                          func.sum(cls.qtw_shipment_plan).label('qtw_shipment_plan'),
                          func.sum(cls.shipment_plan_cw).label('shipment_plan_cw'),
                          func.sum(cls.shipment_plan_cw1).label('shipment_plan_cw1'),
                          func.sum(cls.shipment_plan_cw2).label('shipment_plan_cw2'), ) \
                .filter(cls.lob == lob) \
                .filter(cls.fiscal_qtr_week_name == fiscal_qtr_week_name) \
                .filter(cls.hr_lr == 'HR') \
                .filter(cls.priority == 'P0') \
                .group_by(cls.prod) \
                .group_by(cls.project_code) \
                .all()
            ret = [{'prod': x['prod'], 'qtw_shipment_plan': x['qtw_shipment_plan'],
                    'shipment_plan_cw': x['shipment_plan_cw'],
                    'shipment_plan_cw1': x['shipment_plan_cw1'], 'shipment_plan_cw2': x['shipment_plan_cw2'],
                    'project_code': x['project_code']} for x in res]
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

    @classmethod
    def get_demand_with_tag_data(cls, fiscal_week_year, rtm):
        s = GcDmpSession()
        ret = pd.DataFrame()
        try:
            ret = pd.read_sql_query(f"select * from {cls.__tablename__} where fiscal_week_year={fiscal_week_year} and rtm='{rtm}';", GcDmpEngine)
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret


class AppFastAllocationDemandSellInMultiOnlineWi(GcDmpBase):
    __tablename__ = f"app_fast_allocation_demand_sell_in_multi_online_wi{'_test' if env_dev() else ''}"
    __table_args__ = {"extend_existing": True,
                      "schema": "gc_dmp_fast"}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    fiscal_qtr_week_name = Column(String(256))
    fiscal_week_year = Column(Integer)
    sales_org = Column(String(256))
    rtm = Column(String(256))
    rtml4 = Column(String(256))
    lob = Column(String(256))
    prod = Column(String(256))
    project_code = Column(String(256))
    nand = Column(String(256))
    color = Column(String(256))
    mpn = Column(String(256))
    hr_lr = Column(String(256))
    odq = Column(Integer)
    sold_to_id = Column(Integer)
    sold_to_name_en = Column(String(256))
    qtw_shipment_plan = Column(Integer)
    shipment_plan_cw = Column(Integer)
    sni_cw_minus_1 = Column(Integer)
    shipment_plan_cw1 = Column(Integer)
    shipment_plan_cw2 = Column(Integer)
    eoh = Column(Integer)
    st_ub_qty_cw_minus1 = Column(Integer)
    st_ub_5wk_bwd_avg = Column(Integer)
    open_backlog_over_published_sp_cw3 = Column(Integer)
    priority = Column(String(256))
    sales_input_qty_cw1 = Column(Float)
    sales_input_qty_cw2 = Column(Float)
    sales_input_qty_cw3 = Column(Float)
    sales_input_qty_cw4 = Column(Float)
    reason_cw1 = Column(String(256))
    reason_cw2 = Column(String(256))
    reason_cw3 = Column(String(256))
    reason_cw4 = Column(String(256))
    comments = Column(String(256))
    create_time = Column(DateTime)
    update_time = Column(DateTime)

    @classmethod
    def get_sell_in_demand_list(cls, fiscal_qtr_week_name, lob, rtm):
        s = GcDmpSession()
        ret = []
        try:
            ret = s.query(cls) \
                .filter(cls.fiscal_qtr_week_name == fiscal_qtr_week_name) \
                .filter(cls.lob == lob)
            if rtm:
                ret = ret.filter(cls.rtm == rtm)
            ret = ret.all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

    @classmethod
    def get_publish_summary(cls, fiscal_qtr_week_name: str, lob: str):
        s = GcDmpSession()
        ret = []
        try:
            res = s.query(cls.prod.label('prod'), cls.project_code.label('project_code'),
                          func.sum(cls.qtw_shipment_plan).label('qtw_shipment_plan'),
                          func.sum(cls.shipment_plan_cw).label('shipment_plan_cw'),
                          func.sum(cls.shipment_plan_cw1).label('shipment_plan_cw1'),
                          func.sum(cls.shipment_plan_cw2).label('shipment_plan_cw2'), ) \
                .filter(cls.lob == lob) \
                .filter(cls.fiscal_qtr_week_name == fiscal_qtr_week_name) \
                .filter(cls.hr_lr == 'HR') \
                .filter(cls.priority == 'P0') \
                .group_by(cls.prod) \
                .group_by(cls.project_code) \
                .all()
            ret = [{'prod': x['prod'], 'qtw_shipment_plan': x['qtw_shipment_plan'],
                    'shipment_plan_cw': x['shipment_plan_cw'],
                    'shipment_plan_cw1': x['shipment_plan_cw1'], 'shipment_plan_cw2': x['shipment_plan_cw2'],
                    'project_code': x['project_code']} for x in res]
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

    @classmethod
    def get_demand_with_tag_data(cls, fiscal_week_year, rtm):
        s = GcDmpSession()
        ret = pd.DataFrame()
        try:
            ret = pd.read_sql_query(f"select * from {cls.__tablename__} where fiscal_week_year={fiscal_week_year} and rtm='{rtm}';", GcDmpEngine)
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret


class AppFastAllocationDemandSellInOtherWi(GcDmpBase):
    __tablename__ = f"app_fast_allocation_demand_sell_in_other_wi{'_test' if env_dev() else ''}"
    __table_args__ = {"extend_existing": True,
                      "schema": "gc_dmp_fast"}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    fiscal_qtr_week_name = Column(String(256))
    fiscal_week_year = Column(Integer)
    sales_org = Column(String(256))
    rtm = Column(String(256))
    rtml4 = Column(String(256))
    lob = Column(String(256))
    prod = Column(String(256))
    project_code = Column(String(256))
    nand = Column(String(256))
    color = Column(String(256))
    mpn = Column(String(256))
    hr_lr = Column(String(256))
    odq = Column(Integer)
    sold_to_id = Column(Integer)
    sold_to_name_en = Column(String(256))
    qtw_shipment_plan = Column(Integer)
    shipment_plan_cw = Column(Integer)
    sni_cw_minus_1 = Column(Integer)
    shipment_plan_cw1 = Column(Integer)
    shipment_plan_cw2 = Column(Integer)
    eoh = Column(Integer)
    st_ub_qty_cw_minus1 = Column(Integer)
    st_ub_5wk_bwd_avg = Column(Integer)
    open_backlog_over_published_sp_cw3 = Column(Integer)
    priority = Column(String(256))
    sales_input_qty_cw1 = Column(Float)
    sales_input_qty_cw2 = Column(Float)
    sales_input_qty_cw3 = Column(Float)
    sales_input_qty_cw4 = Column(Float)
    reason_cw1 = Column(String(256))
    reason_cw2 = Column(String(256))
    reason_cw3 = Column(String(256))
    reason_cw4 = Column(String(256))
    comments = Column(String(256))
    create_time = Column(DateTime)
    update_time = Column(DateTime)

    @classmethod
    def get_sell_in_demand_list(cls, fiscal_qtr_week_name, lob, rtm):
        s = GcDmpSession()
        ret = []
        try:
            ret = s.query(cls) \
                .filter(cls.fiscal_qtr_week_name == fiscal_qtr_week_name) \
                .filter(cls.lob == lob)
            if rtm:
                ret = ret.filter(cls.rtm == rtm)
            ret = ret.all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

    @classmethod
    def get_demand_with_tag_data(cls, fiscal_week_year, rtm):
        s = GcDmpSession()
        ret = pd.DataFrame()
        try:
            ret = pd.read_sql_query(f"select * from {cls.__tablename__} where fiscal_week_year={fiscal_week_year} and rtm='{rtm}';", GcDmpEngine)
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret


class AppFastaAllocationDemandSubmissionMonoWi(GcDmpBase):
    __tablename__ = f"app_fast_allocation_demand_submission_mono_wi{'_test' if env_dev() else ''}"
    __table_args__ = {"extend_existing": True,
                      "schema": "gc_dmp_fast"}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    fiscal_qtr_week_name = Column(String(256))
    fiscal_week_year = Column(Integer)
    sales_org = Column(String(256))
    rtm = Column(String(256))
    rtml4 = Column(String(256))
    lob = Column(String(256))
    prod = Column(String(256))
    project_code = Column(String(256))
    nand = Column(String(256))
    color = Column(String(256))
    mpn = Column(String(256))
    hr_lr = Column(String(256))
    odq = Column(Integer)
    sold_to_id = Column(Integer)
    sold_to_name_en = Column(String(256))
    exceed_sales_input = Column(String(256))
    top_up_demand_cw1 = Column(Integer)
    top_up_demand_cw2 = Column(Integer)
    top_up_demand_cw3 = Column(Integer)
    top_up_demand_cw4 = Column(Integer)
    
    qtw_shipment_plan = Column(Integer)
    shipment_plan_cw = Column(Integer)
    shipment_plan_cw1 = Column(Integer)
    shipment_plan_cw2 = Column(Integer)
    shipment_plan_cw3 = Column(Integer)
    cw_backlog_gap = Column(Integer)
    discrete_cw1 = Column(Integer)
    discrete_cw2 = Column(Integer)
    discrete_cw3 = Column(Integer)
    discrete_cw4 = Column(Integer)

    @classmethod
    def get_sell_in_demand_list(cls, fiscal_qtr_week_name, lob, hr_lr):
        s = GcDmpSession()
        ret = pd.DataFrame()
        try:
            query_data = s.query(cls) \
                .filter(cls.fiscal_qtr_week_name == fiscal_qtr_week_name) \
                .filter(cls.lob == lob)
            if hr_lr:
                query_data = query_data.filter(cls.hr_lr == hr_lr)
            ret = pd.read_sql_query(query_data.statement, GcDmpEngine)
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

    @classmethod
    def get_sell_in_demand_count(cls, fiscal_qtr_week_name, lob, hr_lr, exceed_sales_input):
        s = GcDmpSession()
        num = 0
        try:
            ret = s.query(cls) \
                .filter(cls.fiscal_qtr_week_name == fiscal_qtr_week_name) \
                .filter(cls.lob == lob)
            if exceed_sales_input:
                ret = ret.filter(cls.exceed_sales_input == exceed_sales_input)
            if hr_lr:
                ret = ret.filter(cls.hr_lr == hr_lr)
            num = ret.count()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return num
