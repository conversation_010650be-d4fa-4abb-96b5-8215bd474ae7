from sqlalchemy import UniqueConstraint, or_

from util.gc_dmp_base import *
from util.fast_lite_base import *
from util.util import env_dev
from util.const import AllocationRunFileType, ErrorExcept, ErrCode, AllocationRunCalculateStatus


class SupplyDataFields:
    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    fiscal_qtr_week_name = Column(String(256), comment='FY23Q1W12')
    fiscal_week_year = Column(Integer)
    week_begin_dt = Column(Date)
    sales_org = Column(String(256))
    rtml4 = Column(String(256))
    fph1 = Column(String(256))
    fph3 = Column(String(256))
    project_code = Column(String(256))
    nand = Column(String(256))
    color = Column(String(256))
    mpn = Column(String(256))
    odq = Column(String(256))
    sold_to_id = Column(String(256))
    sold_to_name = Column(String(256))
    max_cw_shipment_plan_consumption_cq_cw = Column(Float)
    shipment_plan_solver_landed_cw = Column(Float)
    shipment_plan_solver_landed_cw1 = Column(Float)
    shipment_plan_solver_landed_cw2 = Column(Float)
    shipment_plan_solver_landed_cw3 = Column(Float)
    shipment_plan_solver_landed_cw4 = Column(Float)
    last_week_shipment_plan_cw = Column(Float)
    last_week_shipment_plan_cw1 = Column(Float)
    last_week_shipment_plan_cw2 = Column(Float)
    last_week_shipment_plan_cw3 = Column(Float)
    last_week_shipment_plan_cw4 = Column(Float)
    last_week_por_discrete_cw1 = Column(Float)
    last_week_por_discrete_cw2 = Column(Float)
    last_week_por_discrete_cw3 = Column(Float)
    last_week_por_discrete_cw4 = Column(Float)
    create_time = Column(DateTime)
    update_time = Column(DateTime)


class ExcessSupplyFields:
    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    fiscal_qtr_week_name = Column(String(256), comment='FY23Q1W12')
    fiscal_week_year = Column(Integer)
    week_begin_dt = Column(Date)
    fph1 = Column(String(256))
    fph3 = Column(String(256))
    project_code = Column(String(256))
    nand = Column(String(256))
    color = Column(String(256))
    mpn = Column(String(256))
    odq = Column(String(256))
    excess_supply_cw1 = Column(Float)
    excess_supply_cw2 = Column(Float)
    excess_supply_cw3 = Column(Float)
    excess_supply_cw4 = Column(Float)
    create_time = Column(DateTime)
    update_time = Column(DateTime)

class AppFastAllocationRunSupplyDataTemplateWi(GcDmpBase, SupplyDataFields):
    __tablename__ = f"app_fast_allocation_run_supply_data_template_wi{'_test' if env_dev() else ''}"
    __table_args__ = {"schema": "gc_dmp_fast"}

    @classmethod
    def find_by_week(cls, fiscal_week_year: int) -> pd.DataFrame:
        s = GcDmpSession()
        ret = pd.DataFrame()
        try:
            query = s.query(cls) \
                .filter(cls.fiscal_week_year == fiscal_week_year) \
                .order_by(cls.rtml4) \
                .order_by(cls.fph3) \
                .order_by(cls.sold_to_id)
            ret = pd.read_sql_query(query.statement, GcDmpEngine)
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret


class AppFastAllocationRunExcessSupplyDataTemplateWi(GcDmpBase, ExcessSupplyFields):
    __tablename__ = f"app_fast_allocation_run_excess_supply_data_template_wi{'_test' if env_dev() else ''}"
    __table_args__ = {"schema": "gc_dmp_fast"}

    @classmethod
    def find_by_week(cls, fiscal_week_year: int) -> pd.DataFrame:
        s = GcDmpSession()
        ret = pd.DataFrame()
        try:
            query = s.query(cls) \
                .filter(cls.fiscal_week_year == fiscal_week_year) \
                .order_by(cls.fph3)
            ret = pd.read_sql_query(query.statement, GcDmpEngine)
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret


class AllocationRun(FASTLiteBase):
    __tablename__ = "allocation_run"
    __table_args__ = {"schema": "fast_lite"}
    
    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    fiscal_qtr_week_name = Column(String(256), comment='FY23Q1W12')
    fiscal_week_year = Column(Integer)
    lob = Column(String(16))
    process_type = Column(SmallInteger, comment="0:automatic; 1:manual")
    latest_step = Column(SmallInteger, comment="1:supply acquisition; 2:supply protection;\
                         3:special supply; 4:true incremental allocation; 5: mannul adjustment")
    
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    
    def __init__(self, fiscal_qtr_week_name: str, fiscal_week_year: int,
                 lob: str, process_type: int, latest_step: int):
        self.fiscal_qtr_week_name = fiscal_qtr_week_name
        self.fiscal_week_year = fiscal_week_year
        self.lob = lob
        self.process_type = process_type
        self.latest_step = latest_step
    
    def save(self) -> int:
        s = FASTLiteSession()
        try:
            s.add(self)
            s.commit()
            v_id = self.id
        except Exception as e:
            logger.exception(e)
            v_id = 0
        finally:
            s.close()
        return v_id
    
    @classmethod
    def update_by_week_lob(cls, fiscal_qtr_week_name, lob, update_data: dict):
        s = FASTLiteSession()
        try:
            s.query(cls) \
                .filter(cls.fiscal_qtr_week_name == fiscal_qtr_week_name) \
                .filter(cls.lob == lob) \
                .update(update_data)
            s.commit()
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()

    @classmethod
    def query_by_week(cls, fiscal_qtr_week_name: str):
        s = FASTLiteSession()
        ret = []
        try:
            ret = s.query(cls) \
                .filter(cls.fiscal_qtr_week_name == fiscal_qtr_week_name) \
                .all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret


class AllocationRunFile(FASTLiteBase):
    __tablename__ = "allocation_run_file"
    __table_args__ = {"schema": "fast_lite"}
    
    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    fiscal_qtr_week_name = Column(String(256), comment='FY23Q1W12')
    fiscal_week_year = Column(Integer)
    lob = Column(String(16))
    category = Column(SmallInteger, comment="0:template; 1:upload")
    group = Column(SmallInteger, comment="0:supply_data; 1:excess supply; 2: stop supply")
    run_step = Column(SmallInteger, comment="1:supply acquisition; 2:supply protection;\
                         3:special supply; 4:true incremental allocation; 5: mannul adjustment")
    file_path = Column(String(256))
    file_name = Column(String(256))
    unique_name = Column(String(256))
    file_version = Column(Integer, server_default='1')
    person_id = Column(String(256))
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    UniqueConstraint(fiscal_week_year, category, group, run_step, name='week_step_category')
    
    def __init__(self, fiscal_qtr_week_name: str, fiscal_week_year: int,
                 category: int, group: int,
                 run_step: int,file_path: str, file_name: str,
                 unique_name: str, person_id: str, lob: str='iPad'):
        self.fiscal_qtr_week_name = fiscal_qtr_week_name
        self.fiscal_week_year = fiscal_week_year
        self.lob = lob
        self.category = category
        self.group = group
        self.run_step = run_step
        self.file_path = file_path
        self.file_name = file_name
        self.unique_name = unique_name
        self.person_id = person_id
    
    def save(self) -> int:
        s = FASTLiteSession()
        try:
            s.add(self)
            s.commit()
            v_id = self.id
        except IntegrityError as e:
            v_id = self.update({
                "fiscal_week_year": self.fiscal_week_year,
                "fiscal_qtr_week_name": self.fiscal_qtr_week_name,
                "category": self.category,
                "group": self.group,
                "run_step": self.run_step,
                "file_name": self.file_name,
                "file_path": self.file_path,
                "unique_name": self.unique_name,
                "person_id": self.person_id,
            })
        except Exception as e:
            logger.exception(e)
            v_id = 0
        finally:
            s.close()
        return v_id

    @classmethod
    def update(cls, update_data: dict):
        s1 = FASTLiteSession()
        s2 = FASTLiteSession()
        try:
            raw_record = s1.query(cls)\
                            .filter(cls.fiscal_week_year == update_data.get('fiscal_week_year'))\
                            .filter(cls.category == update_data.get('category'))\
                            .filter(cls.group == update_data.get('group'))\
                            .filter(cls.run_step == update_data.get('run_step'))\
                            .one()
            file_version = raw_record.file_version + 1
            file_name = update_data.get('file_name')
            if update_data.get('category') == AllocationRunFileType.Upload:
                file_name = f"_{file_version}.".join(file_name.split("."))
            
            s2.query(cls)\
                .filter(cls.id == raw_record.id)\
                .update({
                    "file_path": update_data.get('file_path'),
                    "file_name": file_name,
                    "unique_name": update_data.get('unique_name'),
                    "file_version": file_version,
                    "person_id": update_data.get('person_id')})
            s2.commit()
            logger.info(f"update {cls.__tablename__}")
        except Exception as e:
            logger.info(e)
        finally:
            s1.close()
            s2.close()
        
        return raw_record.id

    @classmethod
    def get_file_by_week_category_group_step(cls, fiscal_qtr_week_name, category, group, step):
        s = FASTLiteSession()
        ret = []
        try:
            ret = s.query(cls) \
                    .filter(cls.fiscal_qtr_week_name == fiscal_qtr_week_name) \
                    .filter(cls.category == category) \
                    .filter(cls.group == group) \
                    .filter(cls.run_step == step) \
                    .all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret
    
    @classmethod
    def get_file_by_week_category_step(cls, fiscal_qtr_week_name, category, step):
        s = FASTLiteSession()
        ret = []
        try:
            ret = s.query(cls) \
                    .filter(cls.fiscal_qtr_week_name == fiscal_qtr_week_name) \
                    .filter(cls.category == category) \
                    .filter(cls.run_step == step) \
                    .all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret
    
    @classmethod
    def update_file_by_week_category_group_step(cls, fiscal_qtr_week_name, category, group, step, update_data: dict):
        s = FASTLiteSession()
        ret = []
        try:
            s.query(cls) \
                .filter(cls.fiscal_qtr_week_name == fiscal_qtr_week_name) \
                .filter(cls.category == category) \
                .filter(cls.group == group) \
                .filter(cls.run_step == step) \
                .update(update_data)
            s.commit()
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
        return ret

    @classmethod
    def delete_by_id(cls, id: int):
        s = FASTLiteSession()
        try:
            q = s.query(cls) \
                .filter(cls.id == id)
            q.delete()
            s.commit()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
    
    @classmethod
    def query_by_id(cls, id: int):
        s = FASTLiteSession()
        ret = None
        try:
            ret = s.query(cls) \
                .filter(cls.id == id) \
                .one()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret


class SupplyAcquisitionCalculateRecord(FASTLiteBase):
    __tablename__ = "supply_acquisition_calculate_record"
    __table_args__ = {"schema": "fast_lite"}
    
    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    fiscal_qtr_week_name = Column(String(256), comment='FY23Q1W12')
    fiscal_week_year = Column(Integer)
    calculate_step = Column(SmallInteger, server_default="1",
                            comment="1:supply_upload; 2:supply_protect; 3:special_supply;")
    calculate_status = Column(SmallInteger, server_default="0",
                         comment="0:未计算; 1:计算中; 2:计算成功; 3:计算失败")
    failure_reason =  Column(Text)
    start_time = Column(DateTime, default=datetime.now)
    finish_time = Column(DateTime)
    person_id = Column(String(32))
    
    def __init__(self, fiscal_qtr_week_name: str, fiscal_week_year: int,
                 person_id: str, calculate_status: int, calculate_step: int = 1):
        self.fiscal_qtr_week_name = fiscal_qtr_week_name
        self.fiscal_week_year = fiscal_week_year
        self.person_id = person_id
        self.calculate_status = calculate_status
        self.calculate_step = calculate_step
    
    def save(self) -> int:
        s = FASTLiteSession()
        try:
            s.add(self)
            s.commit()
            v_id = self.id
        except Exception as e:
            logger.exception(e)
            v_id = 0
        finally:
            s.close()
        return v_id
    
    @classmethod
    def update_by_id(cls, id, update_data: dict):
        s = FASTLiteSession()
        try:
            s.query(cls) \
                .filter(cls.id == id) \
                .update(update_data)
            s.commit()
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()
    
    @classmethod
    def query_calculate_status(cls, id):
        s = FASTLiteSession()
        ret = None
        try:
            ret = s.query(cls.calculate_status, cls.failure_reason).filter(cls.id == id).one_or_none()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

    @classmethod
    def check_calculate_success_by_week(cls, fiscal_qtr_week_name: str, calculate_step: int = 1):
        s = FASTLiteSession()
        ret = 0
        try:
            ret = s.query(func.count(cls.id)).filter(cls.fiscal_qtr_week_name == fiscal_qtr_week_name)\
                .filter(cls.calculate_step == calculate_step)\
                .filter(cls.calculate_status == AllocationRunCalculateStatus.CalculationSuccessful)\
                .scalar()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return bool(ret)


class AllocationRunSupplyAcquisition(FASTLiteBase):
    __tablename__ = "allocation_run_supply_acquisition"
    __table_args__ = {"schema": "fast_lite"}
    
    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    fiscal_qtr_week_name = Column(String(256), comment='FY23Q1W12')
    fiscal_week_year = Column(Integer)
    supply_data_upload_status = Column(SmallInteger, server_default="0",
                                       comment="0:未上传; 1:已上传, 2:已删除")
    calculate_id = Column(Integer)
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    
    def __init__(self, fiscal_qtr_week_name: str, fiscal_week_year: int):
        self.fiscal_qtr_week_name = fiscal_qtr_week_name
        self.fiscal_week_year = fiscal_week_year
    
    def save(self) -> int:
        s = FASTLiteSession()
        try:
            s.add(self)
            s.commit()
            v_id = self.id
        except Exception as e:
            logger.exception(e)
            v_id = 0
        finally:
            s.close()
        return v_id
    
    @classmethod
    def update_by_week(cls, fiscal_qtr_week_name, update_data: dict):
        s = FASTLiteSession()
        try:
            s.query(cls) \
                .filter(cls.fiscal_qtr_week_name == fiscal_qtr_week_name) \
                .update(update_data)
            s.commit()
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()

    @classmethod
    def query_by_week(cls, fiscal_qtr_week_name: str):
        s = FASTLiteSession()
        ret = []
        try:
            ret = s.query(cls) \
                .filter(cls.fiscal_qtr_week_name == fiscal_qtr_week_name) \
                .all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret


class AllocationRunSupplyDataUpload(FASTLiteBase, SupplyDataFields):
    __tablename__ = "allocation_run_supply_data_upload"
    __table_args__ = {"schema": "fast_lite"}
    
    @classmethod
    def bulk_save(cls, datas: list):
        s = FASTLiteSession()
        try:
            s.execute(cls.__table__.insert(), datas)
            s.commit()
        except Exception as e:
            logger.info(str(e))
            raise ErrorExcept(ErrCode.DBInsert, "insert to db failed" + str(e))
        finally:
            s.close()
        return True
    
    @classmethod
    def delete_by_week(cls, fiscal_qtr_week_name: str):
        s = FASTLiteSession()
        try:
            q = s.query(cls) \
                .filter(cls.fiscal_qtr_week_name == fiscal_qtr_week_name)
            q.delete()
            s.commit()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()

    @classmethod
    def get_supply_by_week(cls, fiscal_qtr_week_name: str):
        s = FASTLiteSession()
        ret = []
        try:
            ret = s.query(cls) \
                .filter(cls.fiscal_qtr_week_name == fiscal_qtr_week_name) \
                .all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret


class AllocationRunExcessSupplyUpload(FASTLiteBase, ExcessSupplyFields):
    __tablename__ = "allocation_run_excess_supply_upload"
    __table_args__ = {"schema": "fast_lite"}
    
    @classmethod
    def bulk_save(cls, datas: list):
        s = FASTLiteSession()
        try:
            s.execute(cls.__table__.insert(), datas)
            s.commit()
        except Exception as e:
            logger.info(str(e))
            raise ErrorExcept(ErrCode.DBInsert, "insert to db failed" + str(e))
        finally:
            s.close()
        return True
    
    @classmethod
    def delete_by_week(cls, fiscal_qtr_week_name: str):
        s = FASTLiteSession()
        try:
            q = s.query(cls) \
                .filter(cls.fiscal_qtr_week_name == fiscal_qtr_week_name)
            q.delete()
            s.commit()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()


class AllocationRunStopSupplyUpload(FASTLiteBase):
    __tablename__ = "allocation_run_stop_supply_upload"
    __table_args__ = {"schema": "fast_lite"}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    fiscal_qtr_week_name = Column(String(16), comment='FY23Q1W12')
    fiscal_week_year = Column(Integer)
    sold_to_id = Column(String(32))
    project_code = Column(String(32))
    current_week = Column(String(16))
    start_week = Column(String(16))
    end_week = Column(String(16))
    stop_weeks = Column(SmallInteger)
    create_time = Column(DateTime)
    update_time = Column(DateTime)

    @classmethod
    def bulk_save(cls, datas: list):
        s = FASTLiteSession()
        try:
            s.execute(cls.__table__.insert(), datas)
            s.commit()
        except Exception as e:
            logger.info(str(e))
            raise ErrorExcept(ErrCode.DBInsert, "insert to db failed" + str(e))
        finally:
            s.close()
        return True
    
    @classmethod
    def delete_by_week(cls, fiscal_qtr_week_name: str):
        s = FASTLiteSession()
        try:
            q = s.query(cls) \
                .filter(cls.fiscal_qtr_week_name == fiscal_qtr_week_name)
            q.delete()
            s.commit()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()

    @classmethod
    def find_by_week(cls, fiscal_week_year: str) -> pd.DataFrame:
        s = FASTLiteSession()
        ret = pd.DataFrame()
        try:
            query = s.query(cls) \
                .filter(cls.fiscal_week_year == fiscal_week_year)
            ret = pd.read_sql_query(query.statement, fast_lite_engine)
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret


class AllocationRunSupplyPreview(FASTLiteBase):
    __tablename__ = "allocation_run_supply_preview"
    __table_args__ = {"schema": "fast_lite"}
    
    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    fiscal_qtr_week_name = Column(String(256), comment='FY23Q1W12')
    fiscal_week_year = Column(Integer)
    sales_org = Column(String(256))
    fph1 = Column(String(256))
    fph3 = Column(String(256))
    project_code = Column(String(256))
    nand = Column(String(256))
    color = Column(String(256))
    mpn = Column(String(256))
    max_cw_shipment_plan_consumption_cq_cw = Column(Float)
    shipment_plan_solver_landed_cw = Column(Float)
    shipment_plan_solver_landed_cw1 = Column(Float)
    shipment_plan_solver_landed_cw2 = Column(Float)
    shipment_plan_solver_landed_cw3 = Column(Float)
    shipment_plan_solver_landed_cw4 = Column(Float)
    last_week_shipment_plan_cw = Column(Float)
    last_week_shipment_plan_cw1 = Column(Float)
    last_week_shipment_plan_cw2 = Column(Float)
    last_week_shipment_plan_cw3 = Column(Float)
    last_week_shipment_plan_cw4 = Column(Float)
    last_week_por_discrete_cw1 = Column(Float)
    last_week_por_discrete_cw2 = Column(Float)
    last_week_por_discrete_cw3 = Column(Float)
    last_week_por_discrete_cw4 = Column(Float)
    cw_pull_in_qty = Column(Float)
    incremental_cw1 = Column(Float)
    incremental_cw2 = Column(Float)
    incremental_cw3 = Column(Float)
    incremental_cw4 = Column(Float)
    need_protect_cw1_cw_pull_in = Column(Float)
    need_protect_cw2_cw_pull_in = Column(Float)
    need_protect_cw3_cw_pull_in = Column(Float)
    need_protect_cw4_cw_pull_in = Column(Float)
    need_protect_cw1 = Column(Float)
    need_protect_cw2 = Column(Float)
    need_protect_cw3 = Column(Float)
    need_protect_cw4 = Column(Float)
    true_incremental_cw1 = Column(Float)
    true_incremental_cw2 = Column(Float)
    true_incremental_cw3 = Column(Float)
    true_incremental_cw4 = Column(Float)
    excess_supply_cw1 = Column(Float)
    excess_supply_cw2 = Column(Float)
    excess_supply_cw3 = Column(Float)
    excess_supply_cw4 = Column(Float)
    total_top_up_demand_cw1 = Column(Float)
    total_top_up_demand_cw2 = Column(Float)
    total_top_up_demand_cw3 = Column(Float)
    total_top_up_demand_cw4 = Column(Float)
    p0_top_up_demand_cw1 = Column(Float)
    p0_top_up_demand_cw2 = Column(Float)
    p0_top_up_demand_cw3 = Column(Float)
    p0_top_up_demand_cw4 = Column(Float)
    p1_top_up_demand_cw1 = Column(Float)
    p1_top_up_demand_cw2 = Column(Float)
    p1_top_up_demand_cw3 = Column(Float)
    p1_top_up_demand_cw4 = Column(Float)
    p2_top_up_demand_cw1 = Column(Float)
    p2_top_up_demand_cw2 = Column(Float)
    p2_top_up_demand_cw3 = Column(Float)
    p2_top_up_demand_cw4 = Column(Float)
    cum_gap_cw1 = Column(Float)
    cum_gap_cw2 = Column(Float)
    cum_gap_cw3 = Column(Float)
    cum_gap_cw4 = Column(Float)
    create_time = Column(DateTime)
    update_time = Column(DateTime)

    SALES_ORG_ORDER = ["China mainland", "HONG KONG", "Taiwan"]
    DOWNLOAD_COLUMN_NAME = [
        "Sales Org",
        "LOB / FPH L1",
        "Prod / FPH L3",
        "Project Code",
        "Nand",
        "Color",
        "MPN / Apple Part #",
        "Max_CW_Shipment_Plan_and_Consumption_CQ_CW",
        "Shipment_Plan_Solver_Landed_CW",
        "Shipment_Plan_Solver_Landed_CW1",
        "Shipment_Plan_Solver_Landed_CW2",
        "Shipment_Plan_Solver_Landed_CW3",
        "Shipment_Plan_Solver_Landed_CW4",
        "Last_week_POR_discrete_CW1",
        "Last_week_POR_discrete_CW2",
        "Last_week_POR_discrete_CW3",
        "Last_week_POR_discrete_CW4",
        "CW_Pull In Qty",
        "Incremental CW+1",
        "Incremental CW+2",
        "Incremental CW+3",
        "Incremental CW+4",
        "Need Protect CW+1_CW Pull-in",
        "Need Protect CW+2_CW Pull-in",
        "Need Protect CW+3_CW Pull-in",
        "Need Protect CW+4_CW Pull-in",
        "Need Protect CW+1",
        "Need Protect CW+2",
        "Need Protect CW+3",
        "Need Protect CW+4",
        "true Incremental CW+1",
        "true Incremental CW+2",
        "true Incremental CW+3",
        "true Incremental CW+4",
        "Excess Supply in CW+1",
        "Excess Supply in CW+2",
        "Excess Supply in CW+3",
        "Excess Supply in CW+4",
        "Total Top up Demand CW+1",
        "Total Top up Demand CW+2",
        "Total Top up Demand CW+3",
        "Total Top up Demand CW+4",
        "P0 Top up Demand CW+1",
        "P0 Top up Demand CW+2",
        "P0Top up Demand CW+3",
        "P0 Top up Demand CW+4",
        "P1 Top up Demand CW+1",
        "P1 Top up Demand CW+2",
        "P1Top up Demand CW+3",
        "P1 Top up Demand CW+4",
        "P2 Top up Demand CW+1",
        "P2 Top up Demand CW+2",
        "P2Top up Demand CW+3",
        "P2 Top up Demand CW+4",
        "Cum Gap CW+1",
        "Cum Gap CW+2",
        "Cum Gap CW+3",
        "Cum Gap CW+4"
    ]

    @classmethod
    def get_preview_data(cls, fiscal_week_year, project_code, mpn):
        s = FASTLiteSession()
        res = []
        try:
            query = s.query(
                cls.sales_org,
                cls.project_code,
                cls.mpn,
                cls.nand,
                cls.color,
                cls.true_incremental_cw1,
                cls.true_incremental_cw2,
                cls.true_incremental_cw3,
                cls.true_incremental_cw4,
                cls.total_top_up_demand_cw1,
                cls.total_top_up_demand_cw2,
                cls.total_top_up_demand_cw3,
                cls.total_top_up_demand_cw4,
                cls.cum_gap_cw1,
                cls.cum_gap_cw2,
                cls.cum_gap_cw3,
                cls.cum_gap_cw4,
            )\
                .filter(cls.fiscal_week_year == fiscal_week_year)
            if project_code is not None and len(project_code) != 0:
                query = query.filter(cls.project_code.in_(project_code))
            if mpn is not None and len(mpn) != 0:
                query = query.filter(cls.mpn.in_(mpn))
            res = query.all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return res

    @classmethod
    def get_project_code(cls, fiscal_week_year):
        s = FASTLiteSession()
        res = []
        try:
            query = s.query(cls.project_code) \
                .filter(cls.fiscal_week_year == fiscal_week_year) \
                .group_by(cls.project_code) \
                .order_by(cls.project_code)
            res = query.all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return res

    @classmethod
    def get_mpn_id(cls, fiscal_week_year, project_code):
        s = FASTLiteSession()
        res = []
        try:
            query = s.query(cls.mpn) \
                .filter(cls.fiscal_week_year == fiscal_week_year) \
                .group_by(cls.mpn) \
                .order_by(cls.project_code)
            if project_code is not None and len(project_code) != 0:
                query = query.filter(cls.project_code.in_(project_code))
            res = query.all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return res

    @classmethod
    def get_fiscal_qtr_by_fiscal_week_year(cls, fiscal_week_year: int):
        s = FASTLiteSession()
        fiscal_qtr = ""
        try:
            res = s.query(cls.fiscal_qtr_week_name) \
                .filter(cls.fiscal_week_year == fiscal_week_year) \
                .first()
            if res is None:
                s.close()
                raise ErrorExcept(ErrCode.DBQueryError, "fiscal_week_year data not found: " + str(fiscal_week_year))
            fiscal_qtr = res.fiscal_qtr_week_name
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return fiscal_qtr

    @classmethod
    def get_download_data(cls, fiscal_week_year):
        s = FASTLiteSession()
        res = []
        try:
            query = s.query(
                cls.sales_org,
                cls.fph1,
                cls.fph3,
                cls.project_code,
                cls.nand,
                cls.color,
                cls.mpn,
                cls.max_cw_shipment_plan_consumption_cq_cw,
                cls.shipment_plan_solver_landed_cw,
                cls.shipment_plan_solver_landed_cw1,
                cls.shipment_plan_solver_landed_cw2,
                cls.shipment_plan_solver_landed_cw3,
                cls.shipment_plan_solver_landed_cw4,
                cls.last_week_por_discrete_cw1,
                cls.last_week_por_discrete_cw2,
                cls.last_week_por_discrete_cw3,
                cls.last_week_por_discrete_cw4,
                cls.cw_pull_in_qty,
                cls.incremental_cw1,
                cls.incremental_cw2,
                cls.incremental_cw3,
                cls.incremental_cw4,
                cls.need_protect_cw1_cw_pull_in,
                cls.need_protect_cw2_cw_pull_in,
                cls.need_protect_cw3_cw_pull_in,
                cls.need_protect_cw4_cw_pull_in,
                cls.need_protect_cw1,
                cls.need_protect_cw2,
                cls.need_protect_cw3,
                cls.need_protect_cw4,
                cls.true_incremental_cw1,
                cls.true_incremental_cw2,
                cls.true_incremental_cw3,
                cls.true_incremental_cw4,
                cls.excess_supply_cw1,
                cls.excess_supply_cw2,
                cls.excess_supply_cw3,
                cls.excess_supply_cw4,
                cls.total_top_up_demand_cw1,
                cls.total_top_up_demand_cw2,
                cls.total_top_up_demand_cw3,
                cls.total_top_up_demand_cw4,
                cls.p0_top_up_demand_cw1,
                cls.p0_top_up_demand_cw2,
                cls.p0_top_up_demand_cw3,
                cls.p0_top_up_demand_cw4,
                cls.p1_top_up_demand_cw1,
                cls.p1_top_up_demand_cw2,
                cls.p1_top_up_demand_cw3,
                cls.p1_top_up_demand_cw4,
                cls.p2_top_up_demand_cw1,
                cls.p2_top_up_demand_cw2,
                cls.p2_top_up_demand_cw3,
                cls.p2_top_up_demand_cw4,
                cls.cum_gap_cw1,
                cls.cum_gap_cw2,
                cls.cum_gap_cw3,
                cls.cum_gap_cw4,
            ) \
                .filter(cls.fiscal_week_year == fiscal_week_year)
            res = query.all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return res

    @classmethod
    def bulk_save(cls, datas: list):
        s = FASTLiteSession()
        try:
            s.execute(cls.__table__.insert(), datas)
            s.commit()
        except Exception as e:
            logger.info(str(e))
            raise ErrorExcept(ErrCode.DBInsert, "insert to db failed" + str(e))
        finally:
            s.close()
        return True
    
    @classmethod
    def delete_by_week(cls, fiscal_qtr_week_name: str):
        s = FASTLiteSession()
        try:
            q = s.query(cls) \
                .filter(cls.fiscal_qtr_week_name == fiscal_qtr_week_name)
            q.delete()
            s.commit()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()

    @classmethod
    def get_by_ids(cls, fiscal_week_year, data: list):
        s = FASTLiteSession()
        try:
            q = s.query(cls) \
                .filter(cls.fiscal_week_year == fiscal_week_year)\
                .filter(cls.id.in_(data))
            return q.all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
    
    @classmethod
    def get_week_data(cls, fiscal_week_year) -> pd.DataFrame:
        s = FASTLiteSession()
        res = []
        try:
            query = s.query(cls) \
                .filter(cls.fiscal_week_year == fiscal_week_year)
            res = pd.read_sql_query(query.statement, fast_lite_engine)
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return res


class AllocationRunSupplyProtection(FASTLiteBase):
    __tablename__ = "allocation_run_supply_protection"
    __table_args__ = {"schema": "fast_lite"}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    fiscal_week_year = Column(Integer)
    preview_id = Column(Integer)
    strategy = Column(Integer)
    create_time = Column(DateTime, default=datetime.now())
    update_time = Column(DateTime, default=datetime.now(), onupdate=datetime.now)

    @classmethod
    def get_protection_data(cls, fiscal_week_year, strategy, project_code=None, mpn=None):
        s = FASTLiteSession()
        res = []
        not_add = None
        try:
            query = s.query(
                AllocationRunSupplyPreview.id,
                AllocationRunSupplyPreview.fiscal_week_year,
                AllocationRunSupplyPreview.fiscal_qtr_week_name,
                AllocationRunSupplyPreview.sales_org,
                AllocationRunSupplyPreview.project_code,
                AllocationRunSupplyPreview.mpn,
                AllocationRunSupplyPreview.nand,
                AllocationRunSupplyPreview.color,
                AllocationRunSupplyPreview.need_protect_cw1,
                AllocationRunSupplyPreview.need_protect_cw2,
                AllocationRunSupplyPreview.need_protect_cw3,
                AllocationRunSupplyPreview.need_protect_cw4,
            ) \
                .filter(AllocationRunSupplyPreview.fiscal_week_year == fiscal_week_year) \
                .join(cls, cls.preview_id == AllocationRunSupplyPreview.id, isouter=True)
            not_add = query.filter(cls.id == None) \
                .filter(or_(or_(
                    AllocationRunSupplyPreview.need_protect_cw1 != 0,
                    AllocationRunSupplyPreview.need_protect_cw2 != 0
                ), or_(
                    AllocationRunSupplyPreview.need_protect_cw3 != 0,
                    AllocationRunSupplyPreview.need_protect_cw4 != 0
                ))) \
                .count()
            if project_code is not None:
                query = query.filter(AllocationRunSupplyPreview.project_code.in_(project_code))
            if mpn is not None:
                query = query.filter(AllocationRunSupplyPreview.mpn.in_(mpn))
            res = query.filter(cls.strategy == strategy).filter(cls.id != None).all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return res, not_add

    @classmethod
    def get_to_add_data(cls, fiscal_week_year, project_code=None, mpn=None):
        s = FASTLiteSession()
        res = []
        try:
            query = s.query(
                AllocationRunSupplyPreview.id,
                AllocationRunSupplyPreview.fiscal_week_year,
                AllocationRunSupplyPreview.fiscal_qtr_week_name,
                AllocationRunSupplyPreview.sales_org,
                AllocationRunSupplyPreview.project_code,
                AllocationRunSupplyPreview.mpn,
                AllocationRunSupplyPreview.nand,
                AllocationRunSupplyPreview.color,
                AllocationRunSupplyPreview.need_protect_cw1,
                AllocationRunSupplyPreview.need_protect_cw2,
                AllocationRunSupplyPreview.need_protect_cw3,
                AllocationRunSupplyPreview.need_protect_cw4,
            ) \
                .filter(AllocationRunSupplyPreview.fiscal_week_year == fiscal_week_year) \
                .filter(cls.id == None) \
                .filter(or_(or_(
                    AllocationRunSupplyPreview.need_protect_cw1 != 0,
                    AllocationRunSupplyPreview.need_protect_cw2 != 0
                ), or_(
                    AllocationRunSupplyPreview.need_protect_cw3 != 0,
                    AllocationRunSupplyPreview.need_protect_cw4 != 0
                ))) \
                .join(cls, cls.preview_id == AllocationRunSupplyPreview.id, isouter=True)
            if project_code is not None:
                query = query.filter(AllocationRunSupplyPreview.project_code.in_(project_code))
            if mpn is not None:
                query = query.filter(AllocationRunSupplyPreview.mpn.in_(mpn))
            res = query.all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return res

    @classmethod
    def bulk_save(cls, datas: list):
        s = FASTLiteSession()
        try:
            s.execute(cls.__table__.insert(), datas)
            s.commit()
        except Exception as e:
            logger.info(str(e))
            raise ErrorExcept(ErrCode.DBInsert, "insert to db failed" + str(e))
        finally:
            s.close()
        return True

    @classmethod
    def delete_by_week(cls, fiscal_week_year: str):
        s = FASTLiteSession()
        try:
            q = s.query(cls) \
                .filter(cls.fiscal_week_year == fiscal_week_year)
            q.delete()
            s.commit()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()

    @classmethod
    def delete_by_ids(cls, fiscal_week_year: str, strategy: int, id_list: list):
        s = FASTLiteSession()
        try:
            q = s.query(cls) \
                .filter(cls.fiscal_week_year == fiscal_week_year) \
                .filter(cls.strategy == strategy) \
                .filter(cls.preview_id.in_(id_list))
            q.delete()
            s.commit()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()

    @classmethod
    def get_data_by_week(cls, fiscal_week_year):
        s = FASTLiteSession()
        res = pd.DataFrame()
        try:
            query = s.query(
                AllocationRunSupplyPreview.id,
                cls.strategy,
                AllocationRunSupplyPreview.fiscal_week_year,
                AllocationRunSupplyPreview.fiscal_qtr_week_name,
                AllocationRunSupplyPreview.sales_org,
                AllocationRunSupplyPreview.project_code,
                AllocationRunSupplyPreview.mpn,
                AllocationRunSupplyPreview.nand,
                AllocationRunSupplyPreview.color,
                AllocationRunSupplyPreview.need_protect_cw1,
                AllocationRunSupplyPreview.need_protect_cw2,
                AllocationRunSupplyPreview.need_protect_cw3,
                AllocationRunSupplyPreview.need_protect_cw4,
                AllocationRunSupplyPreview.true_incremental_cw1,
                AllocationRunSupplyPreview.true_incremental_cw2,
                AllocationRunSupplyPreview.true_incremental_cw3,
                AllocationRunSupplyPreview.true_incremental_cw4,
            ) \
                .filter(AllocationRunSupplyPreview.fiscal_week_year == fiscal_week_year) \
                .join(cls, cls.preview_id == AllocationRunSupplyPreview.id, isouter=True)
            query = query.filter(cls.id != None)
            res = pd.read_sql_query(query.statement, fast_lite_engine)
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return res


class AllocationRunSupplyOperateResult(FASTLiteBase):
    __tablename__ = "allocation_run_supply_operate_result"
    __table_args__ = {"schema": "fast_lite"}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    fiscal_week_year = Column(Integer)
    calculate_step2_id = Column(Integer)
    calculate_step3_id = Column(Integer)
    create_time = Column(DateTime, default=datetime.now())
    update_time = Column(DateTime, default=datetime.now(), onupdate=datetime.now)

    @classmethod
    def get_by_week(cls, fiscal_week_year):
        s = FASTLiteSession()
        res = None
        try:
            res = s.query(cls).filter(cls.fiscal_week_year == fiscal_week_year).first()
            if res is None:
                res = cls()
                res.fiscal_week_year = fiscal_week_year
                res.calculate_step2_id = None
                res.calculate_step3_id = None
                s.execute(cls.__table__.insert(), [{
                    "fiscal_week_year": fiscal_week_year,
                    "calculate_step2_id": None,
                    "calculate_step3_id": None
                }])
                s.commit()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return res

    @classmethod
    def update_by_week(cls, fiscal_week_year, update_data: dict):
        s = FASTLiteSession()
        try:
            s.query(cls) \
                .filter(cls.fiscal_week_year == fiscal_week_year) \
                .update(update_data)
            s.commit()
        except Exception as e:
            logger.exception(e)
            raise e
        finally:
            s.close()

    @classmethod
    def reset_operate_status(cls, fiscal_week_year, step: int):
        if step == 2:
            cls.update_by_week(fiscal_week_year, {"calculate_step2_id": None})
        elif step == 3:
            cls.update_by_week(fiscal_week_year, {"calculate_step3_id": None})
        else:
            raise ErrorExcept(ErrCode.Param, "invalid step")


class AllocationRunSpecialSupplyUpload(FASTLiteBase):
    __tablename__ = f"allocation_run_special_supply_upload"
    __table_args__ = {"extend_existing": True,
                      "schema": "fast_lite"}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    fiscal_qtr_week_name = Column(String(256), comment='FY23Q1W12')
    fiscal_week_year = Column(Integer, comment='202312')
    upload_by = Column(String(256), comment='')
    upload_at = Column(DateTime, comment='')

    sales_org = Column(String(256), comment='')
    rtml4 = Column(String(256), comment='')
    fph1 = Column(String(256), comment='')
    fph3 = Column(String(256), comment='')
    project_code = Column(String(256), comment='')
    nand = Column(String(256), comment='')
    color = Column(String(256), comment='')
    mpn = Column(String(256), comment='')
    odq = Column(String(256), comment='')
    sold_to_id = Column(String(256), comment='')
    sold_to_name = Column(String(256), comment='')
    special_supply_cw1 = Column(Float, comment='Special Supply CW+1')
    special_supply_cw2 = Column(Float, comment='Special Supply CW+2')
    special_supply_cw3 = Column(Float, comment='Special Supply CW+3')
    special_supply_cw4 = Column(Float, comment='Special Supply CW+4')

    @classmethod
    def bulk_save(cls, objs: list):
        s = FASTLiteSession()
        try:
            s.execute(cls.__table__.insert(), objs)
            s.commit()
        except Exception as e:
            logger.exception(str(e))
            raise ErrorExcept(ErrCode.DBInsert, "insert to db failed" + str(e))
        finally:
            s.close()
        return True

    @classmethod
    def delete_by_week(cls, fiscal_week_year: str):
        s = FASTLiteSession()
        try:
            q = s.query(cls) \
                .filter(cls.fiscal_week_year == fiscal_week_year)
            q.delete()
            s.commit()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()

    @classmethod
    def get_data_by_week(cls, fiscal_qtr_week_name: str) -> pd.DataFrame:
        s = FASTLiteSession()
        ret = pd.DataFrame()
        try:
            query = s.query(cls) \
                .filter(cls.fiscal_qtr_week_name == fiscal_qtr_week_name)
            ret = pd.read_sql_query(query.statement, fast_lite_engine)
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

class AppFastAllocationRunSpecialSupplyTemplateWi(GcDmpBase):
    __tablename__ = f"app_fast_special_supply_template_wi{'_test' if env_dev() else ''}"
    __table_args__ = {"schema": "gc_dmp_fast"}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    fiscal_qtr_week_name = Column(String(256), comment='FY23Q1W12')
    fiscal_week_year = Column(Integer)
    week_begin_dt = Column(Date)

    sales_org = Column(String(256), comment='')
    rtml4 = Column(String(256), comment='')
    fph1 = Column(String(256), comment='')
    fph3 = Column(String(256), comment='')
    project_code = Column(String(256), comment='')
    nand = Column(String(256), comment='')
    color = Column(String(256), comment='')
    mpn = Column(String(256), comment='')
    odq = Column(String(256), comment='')
    sold_to_id = Column(String(256), comment='')
    sold_to_name = Column(String(256), comment='')
    special_supply_cw1 = Column(Float, comment='Special Supply CW+1')
    special_supply_cw2 = Column(Float, comment='Special Supply CW+2')
    special_supply_cw3 = Column(Float, comment='Special Supply CW+3')
    special_supply_cw4 = Column(Float, comment='Special Supply CW+4')

    @classmethod
    def find_by_week(cls, fiscal_week_year: int) -> pd.DataFrame:
        s = GcDmpSession()
        ret = pd.DataFrame()
        try:
            query = s.query(
                cls.sales_org,
                cls.rtml4,
                cls.fph1,
                cls.fph3,
                cls.project_code,
                cls.nand,
                cls.color,
                cls.mpn,
                cls.odq,
                cls.sold_to_id,
                cls.sold_to_name,
                cls.special_supply_cw1,
                cls.special_supply_cw2,
                cls.special_supply_cw3,
                cls.special_supply_cw4,
            ) \
                .filter(cls.fiscal_week_year == fiscal_week_year) \
                .order_by(cls.rtml4) \
                .order_by(cls.fph3) \
                .order_by(cls.sold_to_id)
            ret = pd.read_sql_query(query.statement, GcDmpEngine)
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret
