from util.gc_dmp_base import *
import pandas as pd
from util.util import env_dev

class AppFastDemandRtmSalesTemplateWi(GcDmpBase):
    __tablename__ = f"app_fast_allocation_cpf_sales_template_wi{'_test' if env_dev() else ''}"
    __table_args__ = {"schema": "gc_dmp_fast"}
    
    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    fiscal_qtr_week_name = Column(String(256))
    fiscal_week_year = Column(Integer)
    week_begin_dt = Column(Date)
    week_end_dt = Column(Date)
    sales_org = Column(String(256))
    rtm = Column(String(256))
    rtml4 = Column(String(256))
    lob = Column(String(256))
    prod = Column(String(256))
    project_code = Column(String(256))
    nand = Column(String(256))
    color = Column(String(256))
    mpn = Column(String(256))
    odq = Column(Integer)
    sold_to_id = Column(Integer)
    sold_to_name_en = Column(String(256))
    qtw_shipment_plan = Column(Integer)
    cw_shipment_plan = Column(Integer)
    sni_cw_minus_1 = Column(Integer)
    shipment_plan_cw1 = Column(Integer)
    shipment_plan_cw2 = Column(Integer)
    eoh = Column(Integer)
    st_ub_qty_cw_minus1 = Column(Integer)
    st_ub_5wk_bwd_avg = Column(Integer)
    open_backlog_over_published_sp_cw3 = Column(Integer)
    priority = Column(String(256))
    sales_input_qty_cw1 = Column(Float)
    sales_input_qty_cw2 = Column(Float)
    sales_input_qty_cw3 = Column(Float)
    sales_input_qty_cw4 = Column(Float)
    reason_cw1 = Column(Float)
    reason_cw2 = Column(Float)
    reason_cw3 = Column(Float)
    reason_cw4 = Column(Float)
    comments = Column(String(256))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    
    # open_backlog_over_published_sp_cw3 周二 减 周一
    open_backlog_over_published_sp_cw3_gap = Column(Integer)
    
    @classmethod
    def get_template_by_rtm_lob(cls, rtm: str, fiscal_week_year: int, lob: str = 'iPad') -> pd.DataFrame:
        s = GcDmpSession()
        ret = pd.DataFrame()
        try:
            query = s.query(cls)\
                .filter(cls.fiscal_week_year == fiscal_week_year)\
                .filter(cls.rtm == rtm)\
                .filter(cls.lob == lob)\
                .order_by(cls.sales_org, cls.sold_to_id, cls.mpn, cls.priority)
            ret = pd.read_sql_query(query.statement, GcDmpEngine)
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

    @classmethod
    def query_open_backlog_decrease(cls, rtm: str, fiscal_week_year: int, lob: str = 'iPad'):
        '''return [(sold_to_id, mpn, open_backlog_over_published_sp_cw3), ...] or []'''
        s = GcDmpSession()
        ret = []
        try:
            ret = s.query(distinct(cls.sold_to_id), cls.mpn, cls.open_backlog_over_published_sp_cw3)\
                .filter(cls.fiscal_week_year == fiscal_week_year)\
                .filter(cls.rtm == rtm)\
                .filter(cls.lob == lob)\
                .filter(cls.open_backlog_over_published_sp_cw3_gap < 0)\
                .all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret
    

class AppFastTaskStatusWi(GcDmpBase):
    __tablename__ = f"app_fast_task_status_wi{'_test' if env_dev() else ''}"
    __table_args__ = {"schema": "gc_dmp_fast"}
    
    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    task_name = Column(String(256))
    task_status = Column(String(256))
    task_date = Column(Date)
    task_week = Column(Integer)
    create_time = Column(DateTime)
    update_time = Column(DateTime)

    @classmethod
    def get_whether_ready(cls, fiscal_week_year: int) -> list:
        s = GcDmpSession()
        ret = 0
        try:
            ret = s.query(func.count(cls.id))\
                .filter(cls.task_week == fiscal_week_year)\
                .filter(cls.task_name.in_(["app_fast_allocation_cpf_sales_template_wi","app_fast_esr_wi"]))\
                .filter(cls.task_status == "SUCCESS")\
                .scalar()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return True if ret==2 else False

    @classmethod
    def check_wednesday_esr_status(cls, fiscal_week_year: int) -> bool:
        s = GcDmpSession()
        ret = []
        try:
            ret = s.query(cls.id)\
                .filter(cls.task_week == fiscal_week_year)\
                .filter(cls.task_name == "app_fast_esr_wed_wi")\
                .filter(cls.task_status == "SUCCESS")\
                .all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return True if len(ret) else False
    
    @classmethod
    def check_task_status(cls, fiscal_week_year: int, task_name: str) -> bool:
        s = GcDmpSession()
        ret = []
        try:
            ret = s.query(cls.id)\
                .filter(cls.task_week == fiscal_week_year)\
                .filter(cls.task_name == task_name)\
                .filter(cls.task_status == "SUCCESS")\
                .all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return True if len(ret) else False
    
    @classmethod
    def get_esr_record_list(cls, limit=1) -> bool:
        s = GcDmpSession()
        ret = []
        try:
            q = s.query(cls)\
                .filter(cls.task_name.in_(["app_fast_esr_wed_wi", "app_fast_esr_wi", "app_fast_esr_tue_am_wi"]))\
                .filter(cls.task_status == "SUCCESS")\
                .filter(cls.task_week >= 202336) \
                .order_by(cls.task_week.desc()) \
                .order_by(cls.create_time.desc())
            if limit:
                q = q.limit(limit)
            ret = q.all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret


class AppFastAllocationDemandAdjustmentTemplateWi(GcDmpBase):
    __tablename__ = f"app_fast_allocation_demand_adjustment_template_wi{'_test' if env_dev() else ''}"
    __table_args__ = {"schema": "gc_dmp_fast"}
    
    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    fiscal_qtr_week_name = Column(String(256))
    fiscal_week_year = Column(Integer)
    sales_org = Column(String(256))
    rtm = Column(String(256))
    rtml4 = Column(String(256))
    lob = Column(String(256))
    prod = Column(String(256))
    project_code = Column(String(256))
    nand = Column(String(256))
    color = Column(String(256))
    mpn = Column(String(256))
    hr_lr = Column(String(256), comment='high runner还是low runner')
    odq = Column(Integer)
    sold_to_id = Column(Integer)
    customer_name = Column(String(256))
    qtw_shipment_plan = Column(Integer)
    cw_shipment_plan = Column(Integer)
    sni_cw_minus_1 = Column(Integer)
    shipment_plan_cw1 = Column(Integer)
    shipment_plan_cw2 = Column(Integer)
    eoh = Column(Integer)
    st_ub_qty_cw_minus1 = Column(Integer)
    st_ub_5wk_bwd_avg = Column(Integer)
    open_backlog_over_published_sp_cw3 = Column(Integer)
    priority = Column(String(256))
    sales_input_qty_cw1 = Column(Float)
    reason_cw1 = Column(String(256))
    sales_input_qty_cw2 = Column(Float)
    reason_cw2 = Column(String(256))
    sales_input_qty_cw3 = Column(Float)
    reason_cw3 = Column(String(256))
    sales_input_qty_cw4 = Column(Float)
    reason_cw4 = Column(String(256))
    comments = Column(String(256))
    delta_adjustment_cw1 = Column(Float)
    reason_adjustment_cw1 = Column(String(256))
    delta_adjustment_cw2 = Column(Float)
    reason_adjustment_cw2 = Column(String(256))
    delta_adjustment_cw3 = Column(Float)
    reason_adjustment_cw3 = Column(String(256))
    delta_adjustment_cw4 = Column(Float)
    reason_adjustment_cw4 = Column(String(256))
    comments_adjustment = Column(String(256))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    
    @classmethod
    def get_template_by_week_rtm_lob(cls, rtm: str, fiscal_week_year: int, lob: str) -> pd.DataFrame:
        s = GcDmpSession()
        ret = pd.DataFrame()
        try:
            query = s.query(cls)\
                .filter(cls.fiscal_week_year == fiscal_week_year)\
                .filter(cls.rtm == rtm)\
                .filter(cls.lob == lob)\
                .order_by(cls.sales_org, cls.sold_to_id, cls.mpn, cls.priority)
            ret = pd.read_sql_query(query.statement, GcDmpEngine)
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret


class AppFastAllocationDemandSubmissionMono(GcDmpBase):
    __tablename__ = f"app_fast_allocation_demand_submission_mono_wi{'_test' if env_dev() else ''}"
    __table_args__ = {"extend_existing": True,
                      "schema": "gc_dmp_fast"}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    fiscal_qtr_week_name = Column(String(256))
    fiscal_week_year = Column(Integer)
    week_begin_dt = Column(Date)
    week_end_dt = Column(Date)
    sales_org = Column(String(256))
    rtm = Column(String(256))
    rtml4 = Column(String(256))
    lob = Column(String(256))
    prod = Column(String(256))
    project_code = Column(String(256))
    nand = Column(String(256))
    color = Column(String(256))
    mpn = Column(String(256))
    hr_lr = Column(String(256))
    odq = Column(Integer)
    sold_to_id = Column(Integer)
    sold_to_name_en = Column(String(256))
    top_up_demand_cw1 = Column(Float)
    top_up_demand_cw2 = Column(Float)
    top_up_demand_cw3 = Column(Float)
    top_up_demand_cw4 = Column(Float)
    exceed_sales_input = Column(String(256))
    create_time = Column(DateTime)
    update_time = Column(DateTime)
    
    qtw_shipment_plan = Column(Integer)
    shipment_plan_cw = Column(Integer)
    shipment_plan_cw1 = Column(Integer)
    shipment_plan_cw2 = Column(Integer)
    shipment_plan_cw3 = Column(Integer)
    cw_backlog_gap = Column(Integer)
    discrete_cw1 = Column(Integer)
    discrete_cw2 = Column(Integer)
    discrete_cw3 = Column(Integer)
    discrete_cw4 = Column(Integer)

    @classmethod
    def get_by_rtm_lob(cls, rtm: str, fiscal_week_year: int, lob: str = 'iPad') -> pd.DataFrame:
        s = GcDmpSession()
        ret = pd.DataFrame()
        try:
            query = s.query(cls) \
                .filter(cls.fiscal_week_year == fiscal_week_year) \
                .filter(cls.rtm == rtm) \
                .filter(cls.lob == lob) \
                .order_by(cls.sales_org, cls.sold_to_id, cls.mpn)
            ret = pd.read_sql_query(query.statement, GcDmpEngine)
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret


class AppFastAllocationDemandSubmissionTemplate(GcDmpBase):
    __tablename__ = f"app_fast_allocation_demand_submission_template_wi{'_test' if env_dev() else ''}"
    __table_args__ = {"schema": "gc_dmp_fast"}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    fiscal_qtr_week_name = Column(String(256))
    fiscal_week_year = Column(Integer)
    week_begin_dt = Column(Date)
    week_end_dt = Column(Date)
    sales_org = Column(String(256))
    rtm = Column(String(256))
    rtml4 = Column(String(256))
    lob = Column(String(256))
    prod = Column(String(256))
    project_code = Column(String(256))
    nand = Column(String(256))
    color = Column(String(256))
    mpn = Column(String(256))
    hr_lr = Column(String(256))
    odq = Column(Integer)
    sold_to_id = Column(Integer)
    sold_to_name_en = Column(String(256))
    qtw_shipment_plan = Column(Integer)
    shipment_plan_cw = Column(Integer)
    shipment_plan_cw1 = Column(Integer)
    shipment_plan_cw2 = Column(Integer)
    shipment_plan_cw3 = Column(Integer)
    cw_backlog_gap = Column(Integer)
    top_up_demand_cw1 = Column(Float)
    top_up_demand_cw2 = Column(Float)
    top_up_demand_cw3 = Column(Float)
    top_up_demand_cw4 = Column(Float)
    create_time = Column(DateTime)
    update_time = Column(DateTime)

    @classmethod
    def get_by_rtm_lob(cls, rtm: str, fiscal_week_year: int, lob: str = 'iPad') -> pd.DataFrame:
        s = GcDmpSession()
        ret = pd.DataFrame()
        try:
            query = s.query(cls) \
                .filter(cls.fiscal_week_year == fiscal_week_year) \
                .filter(cls.rtm == rtm) \
                .filter(cls.lob == lob) \
                .order_by(cls.sales_org, cls.sold_to_id, cls.mpn)
            ret = pd.read_sql_query(query.statement, GcDmpEngine)
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret


class AppFastAllocationDemandSubmissionOther(GcDmpBase):
    __tablename__ = f"app_fast_allocation_demand_submission_other_wi{'_test' if env_dev() else ''}"
    __table_args__ = {"schema": "gc_dmp_fast"}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    fiscal_qtr_week_name = Column(String(256))
    fiscal_week_year = Column(Integer)
    week_begin_dt = Column(Date)
    week_end_dt = Column(Date)
    sales_org = Column(String(256))
    rtm = Column(String(256))
    rtml4 = Column(String(256))
    lob = Column(String(256))
    prod = Column(String(256))
    project_code = Column(String(256))
    nand = Column(String(256))
    color = Column(String(256))
    mpn = Column(String(256))
    odq = Column(Integer)
    sold_to_id = Column(Integer)
    sold_to_name_en = Column(String(256))
    open_backlog_over_published_sp_cw3 = Column(Integer)
    create_time = Column(DateTime)
    update_time = Column(DateTime)

    @classmethod
    def get_by_rtm_lob(cls, rtm: str, fiscal_week_year: int, lob: str = 'iPad') -> pd.DataFrame:
        s = GcDmpSession()
        ret = pd.DataFrame()
        try:
            query = s.query(cls) \
                .filter(cls.fiscal_week_year == fiscal_week_year) \
                .filter(cls.rtm == rtm) \
                .filter(cls.lob == lob) \
                .order_by(cls.sales_org, cls.sold_to_id, cls.mpn)
            ret = pd.read_sql_query(query.statement, GcDmpEngine)
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret
