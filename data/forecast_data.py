from operator import and_
from sqlalchemy import or_, and_, case, desc
import pandas as pd

from util.const import *
from util.gc_dmp_base import *
from data.cascade_filter_data import DimFastBusinessSoldtoMapping


class AppFastForecastTrmSoWa(GcDmpBase):
    __tablename__ = 'app_fast_forecast_rtm_so_wa'
    __table_args__ = {'schema': 'gc_dmp_fast'}
    
    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    rtm = Column(String(256), comment='')
    week_date = Column(String(256), comment='FY23Q1W12')
    fiscal_week_year = Column(Integer, comment='202312')
    fiscal_quarter = Column(Integer, comment='')
    fiscal_week = Column(Integer, comment='')
    week_begin_dt = Column(String(256), comment='')
    week_end_dt = Column(String(256), comment='')
    project_cd = Column(String(256), comment='')
    cust_id = Column(String(256), comment='')
    lob = Column(String(256), comment='')
    model = Column(String(256), comment='')
    sku = Column(String(256), comment='')
    fph4 = Column(String(256), comment='')
    mpn_id = Column(String(256), comment='')
    show_week = Column(Integer, comment='对应的是哪一周的数据')
    data_type = Column(String(256), comment='')
    fcst = Column(Integer, comment='')
    
    @classmethod
    def get_fiscal_year_quarter_week_name(self, page_num: int, page_size: int):
        s = GcDmpSession()
        ret = []
        count = 0
        try:
        
            q = s.query(AppFastForecastTrmSoWa.week_date.distinct())
            
            count = q.count()
            q = q.order_by(AppFastForecastTrmSoWa.fiscal_week_year.desc())
            
            if page_num is not None and page_size is not None:
                q = q.limit(page_size).offset(page_num)
                
            ret = q.all()
            
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret, count
    
    
    @classmethod
    def get_download_data(self, fiscal_qtr_week_name: str,model:list):
        s = GcDmpSession()
        ret = []
        try:
        
            q = s.query(AppFastForecastTrmSoWa.week_date.label('Week_Date'),
                        AppFastForecastTrmSoWa.rtm.label('RTM'),
                        AppFastForecastTrmSoWa.cust_id.label('Sold-to ID'),
                        DimFastBusinessSoldtoMapping.business_type.label('Business Type'),
                        DimFastBusinessSoldtoMapping.sold_to_name_en.label('Sold-to Name'),
                        DimFastBusinessSoldtoMapping.abbre.label('Abbre.'),
                        AppFastForecastTrmSoWa.lob.label('LOB'),
                        AppFastForecastTrmSoWa.model.label('Model'),
                        AppFastForecastTrmSoWa.fph4.label('FPH4'),
                        AppFastForecastTrmSoWa.project_cd.label('Project Code'),
                        AppFastForecastTrmSoWa.sku.label('SKU'),
                        AppFastForecastTrmSoWa.mpn_id.label('MPN'),
                        AppFastForecastTrmSoWa.fiscal_quarter.label('Fiscal_Quarter'),
                        AppFastForecastTrmSoWa.show_week.label('Fiscal_Week'),
                        case([(and_(or_(AppFastForecastTrmSoWa.rtm == 'ENT', AppFastForecastTrmSoWa.rtm == 'EDU'), AppFastForecastTrmSoWa.data_type == 'fcst'), None), ], else_=AppFastForecastTrmSoWa.fcst).label('Fcst')
                        )\
                .filter(AppFastForecastTrmSoWa.week_date == fiscal_qtr_week_name)\
                .filter(AppFastForecastTrmSoWa.data_type == 'fcst')\
                .filter(or_(AppFastForecastTrmSoWa.model.in_(model), AppFastForecastTrmSoWa.model == None))\
                .outerjoin(AppFastForecastTrmSoWa,
                      DimFastBusinessSoldtoMapping.sold_to_id == AppFastForecastTrmSoWa.cust_id)\
                .order_by(AppFastForecastTrmSoWa.rtm, 
                          DimFastBusinessSoldtoMapping.business_type,
                          AppFastForecastTrmSoWa.cust_id,
                          DimFastBusinessSoldtoMapping.abbre,
                          AppFastForecastTrmSoWa.lob,
                          AppFastForecastTrmSoWa.model
                          )
            
            ret = pd.read_sql_query(q.statement, GcDmpEngine)
            
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret
    
    # week_date: FY23Q1W12
    @classmethod
    def get_data_by_week(self, week_date: str):
        s = GcDmpSession()
        ret = []
        try:
            data = s.execute(f'''
                             SELECT id,rtm,week_date,fiscal_week_year,fiscal_quarter,fiscal_week,
                             week_begin_dt,week_end_dt,project_cd,cust_id,lob,model,sku,fph4,
                             mpn_id,show_week,data_type,CASE WHEN (rtm='ENT' OR rtm='EDU') AND data_type='fcst' THEN NULL ELSE fcst END AS fcst 
                             FROM app_fast_forecast_rtm_so_wa WHERE rtm IS NOT NULL and week_date='{week_date}'
                             ''')
            ret = data.fetchall()

        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret

    @classmethod
    def get_model_list(cls, lob, rtm, fiscal_week_year):
        s = GcDmpSession()
        res = []
        try:
            query = s.query(AppFastForecastTrmSoWa.model).distinct() \
              .filter(cls.fiscal_week_year == fiscal_week_year) \
              .filter(cls.rtm == rtm)
            if lob is not None and len(lob) != 0:
                query = query.filter(cls.lob.in_(lob))
            query = query.order_by(desc(cls.model))
            res = query.all()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return res


# forecast mono(Lifesyle,Mono) 第二版本
class AppFastForecastMonoAdvanceWaRaw():
    
    table_name = 'app_fast_forecast_mono_advance_wa'
    
    @classmethod
    def get_version2_data_by_week(cls, week_date):
        s = GcDmpSession()
        ret = []
        try:
            data = s.execute(f'''
                             SELECT rtm,week_date,fiscal_week_year,fiscal_quarter,fiscal_week,
                                week_begin_dt,week_end_dt,project_cd,cust_id,lob,model,sku,fph4,
                                mpn_id,show_week,data_type,fcst
                             FROM {cls.table_name}
                             WHERE week_date='{week_date}';
                             ''')
            raw_data = data.fetchall()
            ret = [dict(item) for item in raw_data]
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
        return ret
    
