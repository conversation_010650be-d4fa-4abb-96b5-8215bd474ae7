from util.const import ErrorExcept, ErrCode
from util.gc_dmp_datasource import *
from util.util import env_dev


class OdsFastCpfDemandSubmissionMultiOnlineUpload(ExDmpBase):
    __tablename__ = f"ods_fast_cpf_demand_submission_multi_online_upload{'_test' if env_dev() else ''}"
    __table_args__ = {"extend_existing": True,
                      "schema": "gc_dmp_datasource"}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    fiscal_qtr_week_name = Column(String(256))
    fiscal_week_year = Column(Integer)
    week_begin_dt = Column(Date)
    week_end_dt = Column(Date)
    sales_org = Column(String(256))
    rtm = Column(String(256))
    rtml4 = Column(String(256))
    lob = Column(String(256))
    prod = Column(String(256))
    project_code = Column(String(256))
    nand = Column(String(256))
    color = Column(String(256))
    mpn = Column(String(256))
    odq = Column(Integer)
    sold_to_id = Column(Integer, comment='')
    sold_to_name_en = Column(String(256), comment='')
    qtw_shipment_plan = Column(Integer)
    shipment_plan_cw = Column(Integer)
    shipment_plan_cw1 = Column(Integer)
    shipment_plan_cw2 = Column(Integer)
    shipment_plan_cw3 = Column(Integer)
    cw_backlog_gap = Column(Integer)
    top_up_demand_cw1 = Column(Float, comment='')
    top_up_demand_cw2 = Column(Float, comment='')
    top_up_demand_cw3 = Column(Float, comment='')
    top_up_demand_cw4 = Column(Float, comment='')
    create_time = Column(DateTime, default=datetime.now, comment='create time')
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='update time')

    @classmethod
    def batch_save(cls, datas: list):
        s = ExDmpSession()
        try:
            s.bulk_insert_mappings(cls, datas)
            s.commit()
        except Exception as e:
            logger.debug(str(e))
            raise ErrorExcept(ErrCode.DBInsert, "insert to db failed" + str(e))
        finally:
            s.close()
        return True

    @classmethod
    def delete_by_week_rtm(cls, fiscal_qtr_week_name: str, rtm: str):
        s = ExDmpSession()
        try:
            q = s.query(cls) \
                .filter(cls.fiscal_qtr_week_name == fiscal_qtr_week_name) \
                .filter(cls.rtm == rtm)
            q.delete()
            s.commit()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()

class OdsFastCpfDemandAdjustUploadDelta(ExDmpBase):
    __tablename__ = f"ods_fast_cpf_demand_adjustment_upload_delta{'_test' if env_dev() else ''}"
    __table_args__ = {"extend_existing": True,
                      "schema": "gc_dmp_datasource"}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    fiscal_qtr_week_name = Column(String(256))
    fiscal_week_year = Column(Integer)
    sales_org = Column(String(256))
    rtm = Column(String(256))
    rtml4 = Column(String(256))
    lob = Column(String(256))
    prod = Column(String(256))
    project_code = Column(String(256))
    nand = Column(String(256))
    color = Column(String(256))
    mpn = Column(String(256))
    odq = Column(Integer)
    sold_to_id = Column(Integer, comment='')
    customer_name = Column(String(256), comment='')
    qtw_shipment_plan = Column(Integer)
    cw_shipment_plan = Column(Integer)
    sni_cw_minus_1 = Column(Integer)
    shipment_plan_cw1 = Column(Integer)
    shipment_plan_cw2 = Column(Integer)
    eoh = Column(Integer)
    st_ub_qty_cw_minus1 = Column(Integer)
    st_ub_5wk_bwd_avg = Column(Integer)
    open_backlog_over_published_sp_cw3 = Column(Integer)
    priority = Column(String(256), comment='')
    sales_input_qty_cw1 = Column(Float, comment='')
    reason_cw1 = Column(String(256), comment='')
    sales_input_qty_cw2 = Column(Float, comment='')
    reason_cw2 = Column(String(256), comment='')
    sales_input_qty_cw3 = Column(Float, comment='')
    reason_cw3 = Column(String(256), comment='')
    sales_input_qty_cw4 = Column(Float, comment='')
    reason_cw4 = Column(String(256), comment='')
    comments = Column(String(256), comment='')
    delta_adjustment_cw1 = Column(Float, comment='')
    reason_adjustment_cw1 = Column(String(256), comment='')
    delta_adjustment_cw2 = Column(Float, comment='')
    reason_adjustment_cw2 = Column(String(256), comment='')
    delta_adjustment_cw3 = Column(Float, comment='')
    reason_adjustment_cw3 = Column(String(256), comment='')
    delta_adjustment_cw4 = Column(Float, comment='')
    reason_adjustment_cw4 = Column(String(256), comment='')
    comments_adjustment = Column(String(256))
    version = Column(Integer, comment='')
    create_time = Column(DateTime, default=datetime.now, comment='create time')
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='update time')

    @classmethod
    def batch_save(cls, datas: list):
        s = ExDmpSession()
        try:
            # s.bulk_insert_mappings(cls, datas)
            s.execute(cls.__table__.insert(), datas)
            s.commit()
        except Exception as e:
            logger.debug(str(e))
            raise ErrorExcept(ErrCode.DBInsert, "insert to db failed" + str(e))
        finally:
            s.close()
        return True

    @classmethod
    def delete_by_week_rtm(cls, fiscal_qtr_week_name: str, rtm: str):
        s = ExDmpSession()
        try:
            q = s.query(cls) \
                .filter(cls.fiscal_qtr_week_name == fiscal_qtr_week_name) \
                .filter(cls.rtm == rtm)
            q.delete()
            s.commit()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()


class OdsFastCpfDemandAdjustResult(ExDmpBase):
    __tablename__ = f"ods_fast_cpf_demand_adjustment_result{'_test' if env_dev() else ''}"
    __table_args__ = {"extend_existing": True,
                      "schema": "gc_dmp_datasource"}

    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    fiscal_qtr_week_name = Column(String(256))
    fiscal_week_year = Column(Integer)
    week_begin_dt = Column(Date)
    week_end_dt = Column(Date)
    sales_org = Column(String(256))
    rtm = Column(String(256))
    rtml4 = Column(String(256))
    lob = Column(String(256))
    prod = Column(String(256))
    project_code = Column(String(256))
    nand = Column(String(256))
    color = Column(String(256))
    mpn = Column(String(256))
    odq = Column(Integer)
    sold_to_id = Column(Integer, comment='')
    sold_to_name_en = Column(String(256), comment='')
    qtw_shipment_plan = Column(Integer)
    cw_shipment_plan = Column(Integer)
    sni_cw_minus_1 = Column(Integer)
    shipment_plan_cw1 = Column(Integer)
    shipment_plan_cw2 = Column(Integer)
    eoh = Column(Integer)
    st_ub_qty_cw_minus1 = Column(Integer)
    st_ub_5wk_bwd_avg = Column(Integer)
    open_backlog_over_published_sp_cw3 = Column(Integer)
    priority = Column(String(256), comment='')
    sales_input_qty_cw1 = Column(Float, comment='')
    reason_cw1 = Column(String(256), comment='')
    sales_input_qty_cw2 = Column(Float, comment='')
    reason_cw2 = Column(String(256), comment='')
    sales_input_qty_cw3 = Column(Float, comment='')
    reason_cw3 = Column(String(256), comment='')
    sales_input_qty_cw4 = Column(Float, comment='')
    reason_cw4 = Column(String(256), comment='')
    comments = Column(String(256), comment='')
    version = Column(Integer, comment='')
    create_time = Column(DateTime, default=datetime.now, comment='create time')
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='update time')

    @classmethod
    def batch_save(cls, datas: list):
        s = ExDmpSession()
        try:
            # s.bulk_insert_mappings(cls, datas)
            s.execute(cls.__table__.insert(), datas)
            s.commit()
        except Exception as e:
            logger.debug(str(e))
            raise ErrorExcept(ErrCode.DBInsert, "insert to db failed" + str(e))
        finally:
            s.close()
        return True
    
    @classmethod
    def delete_by_week_rtm(cls, fiscal_qtr_week_name: str, rtm: str):
        s = ExDmpSession()
        try:
            q = s.query(cls) \
                .filter(cls.fiscal_qtr_week_name == fiscal_qtr_week_name) \
                .filter(cls.rtm == rtm)
            q.delete()
            s.commit()
        except Exception as e:
            logger.exception(e)
        finally:
            s.close()
