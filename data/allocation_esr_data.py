from util.gc_dmp_base import *
import pandas as pd
from util.const import *

class TableESRBasic():
    
    id = Column(Integer, autoincrement=True, primary_key=True, comment='ID')
    snapshot_date = Column(Date)
    fiscal_week_year = Column(Integer)
    fiscal_qtr_week_name = Column(String(256))
    week_begin_dt = Column(String(256))
    week_end_dt = Column(String(256))
    hq_segment_cd = Column(String(256))
    sold_to_id = Column(String(256))
    alt_hier_level_3_desc = Column(String(256))
    roc_cust_desc = Column(String(256))
    lob = Column(String(256))
    sub_lob = Column(String(256))
    fph4 = Column(String(256))
    sku = Column(String(256))
    mpn_id = Column(String(256))
    project_cd = Column(String(256))
    type_desc = Column(String(256))
    lifecycle = Column(String(256))
    prod_description = Column(String(256))
    display_size = Column(String(256))
    color_desc = Column(String(256))
    wireless_desc = Column(String(256))
    color_shrt_desc = Column(String(256))
    storage_size = Column(String(256))
    qtw_shipment_plan = Column(Integer)
    qtd_gross_billing = Column(Integer)
    cw_consumed_qty = Column(Integer)
    gatp_allocation = Column(Integer)
    delta_sni = Column(Integer)
    sni_cw_minus_1 = Column(Integer)
    last_week_open_dn = Column(Integer)
    rollover = Column(Integer)
    shipment_plan_cw = Column(Integer)
    shipment_plan_cw1 = Column(Integer)
    shipment_plan_cw2 = Column(Integer)
    shipment_plan_cw3 = Column(Integer)
    gross_billing_units_cw = Column(Integer)
    cw_shipped_units = Column(Integer)
    shipment_attainment = Column(Integer)
    po_attainment = Column(Integer)
    cw_backlog_gap = Column(Integer)
    cw_supply_gap = Column(Integer)
    dns_on_credit_hold = Column(Integer)
    dns_not_on_credit_hold = Column(Integer)
    sni_qty = Column(Integer)
    tot_backlog = Column(Integer)
    backlog_gap_cum_cw1 = Column(Integer)
    shippable_backlog = Column(Integer)
    scheduled_backlog = Column(Integer)
    sif_actualised = Column(Integer)
    pod_landed_qty = Column(Integer)
    eoh_qty = Column(Integer)
    st_ub_qty_cw_minus1 = Column(Integer)
    st_ub_qty_cw_minus2 = Column(Integer)
    st_ub_qty_cw_minus3 = Column(Integer)
    st_ub_qty_cw_minus4 = Column(Integer)
    st_ub_qty_cw_minus5 = Column(Integer)
    st_ub_5wk_bwd_avg = Column(Integer)
    woi_before_allocatn = Column(Integer)
    wos_after_allocation = Column(Integer)
    cum_shipment_plan_cw2 = Column(Integer)
    billing_cw_minus1 = Column(Integer)
    billing_cw_minus2 = Column(Integer)
    backlog_gap_cum_cw2 = Column(Integer)
    backlog_gap_cum_cw3 = Column(Integer)
    open_backlog_over_published_sp = Column(Integer)
    open_backlog_over_published_sp_cw1 = Column(Integer)
    open_backlog_over_published_sp_cw2 = Column(Integer)
    open_backlog_over_published_sp_cw3 = Column(Integer)
    open_backlog_over_published_sp_gt_2w = Column(Integer)
    open_backlog_over_published_sp_gt_3w = Column(Integer)
    open_backlog_over_published_sp_gt_9w = Column(Integer)
    rtml4 = Column(String(256))
    batch_id = Column(Integer)
    create_time = Column(DateTime)
    update_time = Column(DateTime)


# 周一版ESR表
class AppFastESRMonWi(TableESRBasic, GcDmpBase):
    __tablename__ = 'app_fast_esr_wi'
    __table_args__ = {"schema": "gc_dmp_fast"}
    
    @classmethod
    def get_esr_list(cls):
        s = GcDmpSession()
        ret = []
        start_week = 202329
        try:
            ret = s.execute(f'''
                SELECT t.fiscal_week_year AS fiscal_week_year, max(t.update_time) AS update_time FROM 
                (SELECT fiscal_week_year, update_time FROM {cls.__tablename__} WHERE fiscal_week_year >= {start_week}
                UNION ALL
                SELECT fiscal_week_year, update_time FROM {AppFastESRWedWi.__tablename__} WHERE fiscal_week_year >= {start_week}) AS t
                GROUP BY t.fiscal_week_year ORDER BY t.fiscal_week_year DESC;
                      ''').fetchall()
        except Exception as e:
            logger.info(f"query error: {e}")
        finally:
            s.close()
        return ret
    
    @classmethod
    def get_esr_record_list(cls, fiscal_week_year: int):
        s = GcDmpSession()
        ret = []
        try:
            ret_mon = s.execute(f'''
                            SELECT t.fiscal_week_year AS fiscal_week_year, max(t.update_time) AS update_time, {ESRMondayVersion} AS version
                            FROM {cls.__tablename__} as t 
                            WHERE t.fiscal_week_year = {fiscal_week_year} GROUP BY t.fiscal_week_year
                      ''').fetchall()
            ret_wed = s.execute(f'''
                            SELECT t.fiscal_week_year AS fiscal_week_year, max(t.update_time) AS update_time, {ESRWednesdayVersion} AS version
                            FROM {AppFastESRWedWi.__tablename__} as t 
                            WHERE t.fiscal_week_year = {fiscal_week_year} GROUP BY t.fiscal_week_year
                      ''').fetchall()
            ret = ret_wed + ret_mon
        except Exception as e:
            logger.info(f"query error: {e}")
        finally:
            s.close()
        return ret

    @classmethod
    def download_esr_content_by_week(cls, fiscal_week_year: int) -> pd.DataFrame:
        s = GcDmpSession()
        ret = pd.DataFrame()
        try:
            ret = pd.read_sql_query(f"select * from {cls.__tablename__} where fiscal_week_year={fiscal_week_year}", GcDmpEngine)
        except Exception as e:
            logger.info(e)
            raise e
        finally:
            s.close()
        return ret
            
    
# 周三版ESR表
class AppFastESRWedWi(TableESRBasic, GcDmpBase):
    __tablename__ = "app_fast_esr_wed_wi"
    __table_args__ = {"schema": "gc_dmp_fast"}

    @classmethod
    def download_esr_content_by_week(cls, fiscal_week_year: int) -> pd.DataFrame:
        s = GcDmpSession()
        ret = pd.DataFrame()
        try:
            ret = pd.read_sql_query(f"select * from {cls.__tablename__} where fiscal_week_year={fiscal_week_year}", GcDmpEngine)
        except Exception as e:
            logger.info(e)
            raise e
        finally:
            s.close()
        return ret


# 周二上午版ESR表
class AppFastESRTuAMWi(TableESRBasic, GcDmpBase):
    __tablename__ = "app_fast_esr_tue_am_wi"
    __table_args__ = {"schema": "gc_dmp_fast"}

    @classmethod
    def download_esr_content_by_week(cls, fiscal_week_year: int) -> pd.DataFrame:
        s = GcDmpSession()
        ret = pd.DataFrame()
        try:
            ret = pd.read_sql_query(f"select * from {cls.__tablename__} where fiscal_week_year={fiscal_week_year}", GcDmpEngine)
        except Exception as e:
            logger.info(e)
            raise e
        finally:
            s.close()
        return ret
