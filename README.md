# fast_lite_server

## 0. 准备
### 本地调试时添加证书
 - 访问以下两个url下载两个pem文件
   - https://gcdmp-eng.corp.apple.com/static/display/download_files/client.gcdmp-eng.corp.apple.com.chain.pem
   - https://gcdmp-eng.corp.apple.com/static/display/download_files/client.gcdmp-eng.corp.apple.com.open.key.pem
 - 在项目运行时目录下新建cert目录，将两个pem文件复制到该cert目录下

## 1. 本地环境调试:

1. 环境变量

```bash 
export ENV=dev 
export DB_ENV=apple 
```
调试模式：
```
export FLASK_APP=wsgi FLASK_DEBUG=1
```

1.1 配置aws账号（follow the link to setup: https://cloudtech.apple.com/documentation/aws/getting-started/lessons/lesson-0
```
<NAME_EMAIL>:CloudTech/reference-architecture-examples.git
    AWS_APPLE=$(pwd)/reference-architecture-examples/aws
    cd $AWS_APPLE/setup
    cp responses.template my_responses
    vim my_responses
        appleconnect_username=jin_sunny # 替换成自己的账号
        mascot_role=developer_role
        aws_dev_account_id=************
        aws_prod_account_id=************
        profile_prefix=   // 模版给的是SKIP，但会导致 ~/.aws/config 中的profile 配置为 [profile SKIPprod]
        use_aws_ps1=true
    ./setup.sh -f my_responses
    source ~/.profile # 我这边本地没有这个文件，提示的是这个： source /Users/<USER>/.aws_profile
    
    aws-profile prod # flask服务启动前，执行这个
    [prod] aws sts get-caller-identity
    aws-login
```
1.2  RUN pip install apple-dmp-databend-sqlalchemy --index https://pypi.apple.com/simple 

2. `flask run` 会在默认端口5000中启动，如果需要指定端口请使用`flask run --port=9090`

3. 也可以使用启动脚本./start_local.sh (端口号9091)

4.celery配置：
首先需要去对应的方法中添加装饰器@celery.task(name="task_name")，然后在项目中，找到celery_config.py文件，添加对应的任务文件，如下：
```
celery = Celery("celery_task", include=[
    "......",
    "......",
    "service.datasource_service"
])
```
注意：service是任务文件夹，datasource_service是任务文件，配置后，会自动扫描任务文件下所有的任务，然后在启动celery的时候，会自动加载所有的任务

5.本地调试，如果需要用到celery，则需要启动celery worker，启动的时候需要单开一个命令窗口
```
celery -A celery_config  worker -l DEBUG --logfile="./logs/celery_%n%I.log" --concurrency=1
```
同时，由于本地和测试环境使用同一个redis，为了避免冲突，本地的celery需要指定一个不同的队列，可以在celery_config.py指定队列，如下
```
   celery_redis_name = 5
```
只要不跟测试环境或其他开发的配置相同即可

```
> 本地调试时出现https证书问题
> * 访问以下两个url下载两个pem文件
>   * https://gcdmp-eng.corp.apple.com/static/display/download_files/client.gcdmp-eng.corp.apple.com.chain.pem
>   * https://gcdmp-eng.corp.apple.com/static/display/download_files/client.gcdmp-eng.corp.apple.com.open.key.pem
> * 在项目运行时目录下新建cert目录，将两个pem文件复制到该cert目录下 



## 2. dev / prod docker 

* dev: make && make push 
* prod: make env=prod && make push env=prod