from data.allocation_run.gc_dmp_app_fast_allocation_run import *
from data.allocation_run.fast_lite_allocation_run import *
from data.allocation_cpf_run_data import SupplyAcquisitionCalculateRecord, \
    AllocationRunSupplyProtection, AllocationRunSupplyDataUpload, \
    AllocationRunSpecialSupplyUpload, AllocationRunSupplyPreview
from data.cpf_data_source import OdsFastCPFMixiPad, OdsFastCPFTWOSiPad
from data.fiscal_year_week import FiscalYearWeek
from data.allocation_cpf_run_data import AllocationRunSupplyOperateResult
from data.allocation_demand_collection_overview_data import OdsFastCpfDemandCollectionConfirmResult
from util.const import AllocationRunCalculateStatus, ErrorExcept, DateTimeFormat, ErrMsg
from util.cpf_const import AllocationRunErrorMsg
from service.allocation_cpf_run_service import save_unique_file
from util.util import _async
from service.allocation_rules import get_deduction_week_index,\
    get_allcaotion_rules, RuleEnum


STR_MPN = "mpn"
STR_SOLD_TO_ID = "sold_to_id"
LIST_NEED_PROTECT_FEILD = [
    "need_protect_cw1",
    "need_protect_cw2",
    "need_protect_cw3",
    "need_protect_cw4",
]


# allocation run 第二小步supply protect计算逻辑

def get_data_from_datasource(filetype: str) -> pd.DataFrame:
    '''
    mix / twos
    '''
    if filetype == 'mix':
        return OdsFastCPFMixiPad.get_data_by_max_version()
    elif filetype == 'twos':
        return OdsFastCPFTWOSiPad.get_data_by_max_version()
    else:
        return pd.DataFrame(columns=[
            'sales_org', 'mpn', 'sold_to_id', 'value'])


def get_user_specified_data(fiscal_week_year: int):
    return AllocationRunSupplyProtection.get_data_by_week(
        fiscal_week_year)


def get_uploaded_supply(week_date: str):
    uploaded_supply = AllocationRunSupplyDataUpload.get_supply_by_week(
        week_date)
    return pd.DataFrame([vars(entity) for entity in uploaded_supply])


def pull_in_or_push_out(need_protect_weeks_data: list):
    '''返回扣减周的下标值
    pull in: 小于扣减周下标的周
    push out: 大于扣减周下标的周
    '''
    # 确定扣减周位置，来区分哪些是pull in，哪些是push out deduction_week
    # 现在的数据只包含一个扣减周的，如果有多个扣减周，则不予计算
    for deduction_week, v in enumerate(need_protect_weeks_data):
        if v < 0:
            return deduction_week
    else:
        return None


def get_woi_divide_twos(woi: int, twos: int):
    if not twos:
        return 0
    return woi / twos


def get_ub_from_data_side(week_date: str):
    return AppFastAllocationRunUbEohWi.get_ub_eoh_by_week(week_date)


def get_woi(ub_data: dict, shipment_plan: int):
    '''
    sni_last_week (仅台湾): Sales Org == Taiwan
    计算公式: 
    WOI_CW_X = (UB_EOH_Last_Week + Shipment_Plan_CW_X + SNI_Last_Week(仅台湾) - Fcst_CW_X) / Avg.[Sum(Fcst_CW_X1 ~ Fcst_CW_X5)]

    注意: 因暂无iPad的FCST，所以本公式中的：
        FCST_CW_X现阶段用“过去5周的总UB除5”来代替；
        Avg.[Sum(Fcst_CW_X1 ~ Fcst_CW_X5)] 现阶段也用“过去5周的总UB除5”来代替
    '''
    avg_ub_lw5 = ub_data.get('avg_ub_lw5') or 0
    sni_last_week = ub_data.get('sni_last_week') or 0 if ub_data.get(
        'sales_org') == 'Taiwan' else 0
    ub_eoh_last_week = ub_data.get('ub_eoh') or 0
    woi_numberator = ub_eoh_last_week + shipment_plan + sni_last_week - avg_ub_lw5

    if not avg_ub_lw5:
        return 0

    return woi_numberator / avg_ub_lw5


def get_max_or_min(need_protect: int, need_protect_index: int, deduction_week_index: int):
    if need_protect == 0:
        return None
    if need_protect_index < deduction_week_index:
        return 'min'
    if need_protect_index > deduction_week_index:
        return 'max'


class SupplyProtectRatio():
    def __init__(self, week_date) -> None:
        self.week_date = week_date
        self.ratio_week = [
            'calculate_ratio_w1',
            'calculate_ratio_w2',
            'calculate_ratio_w3',
            'calculate_ratio_w4',
        ]
    
    def get_ratio_by_mix(self):
        df_ratio = get_data_from_datasource('mix')
        
        for _, val in enumerate(self.ratio_week):
            df_ratio[val] = df_ratio['value'].str.rstrip('%').astype('float')
        
        return df_ratio[['mpn', 'sold_to_id'] + self.ratio_week]
    
    def get_ratio_by_shipment_plan(self, supply_data: pd.DataFrame):
        df_ratio = supply_data.copy()
        
        for idx, val in enumerate(self.ratio_week):
            shipment_plan_week = f"last_week_por_discrete_cw{idx+1}"
            group_sums = df_ratio.groupby(STR_MPN)[shipment_plan_week].transform('sum')
            df_ratio[val] = df_ratio[shipment_plan_week] / group_sums
        
        return df_ratio[['mpn', 'sold_to_id'] + self.ratio_week]

class SupplyProtector:
    '''
    策略1: 确定pull in还是push out,根据 WOI/TWOS 最大值/最小值 来确定sold-to的分配, 步长是1ODQ, 余数随机分配
    策略2: 同1
    策略3: default-按照自有比例分配
    策略4: 按照用户提供的比例分配
    '''

    def __init__(self, week_date):
        self.week_date = week_date
        self.cw_supply_data = get_uploaded_supply(week_date)

    def get_strategy_1_2_data(self, allocation_data: pd.DataFrame):
        df_result = pd.DataFrame()
        df_ub = get_ub_from_data_side(self.week_date)
        if df_ub.empty:
            err_info = f'no ub data in {self.week_date}'
            logger.error(err_info)
            raise ErrorExcept(
                ErrCode.DBQueryNoData, err_info)
        
        df_twos = get_data_from_datasource('twos')
        if df_twos.empty:
            err_info = f'no twos data in {self.week_date}'
            logger.error(err_info)
            raise ErrorExcept(
                ErrCode.DBQueryNoData, err_info)
        # 分货过程记录
        allocation_process = []
        # 记录分货失败的mpn
        mpns_failed_to_allocation = set()
        
        # 策略1和策略2，方式相同
        for _, row in allocation_data.iterrows():
            allocation_process.append(f'分货MPN: {row[STR_MPN]} \n\n')
            
            # 获取该mpn下所有sold-to的数据
            df_mpn_ub = df_ub[df_ub[STR_MPN] == row[STR_MPN]]
            df_mpn_twos = df_twos[df_twos[STR_MPN] == row[STR_MPN]]
            df_mpn_supply_data = self.cw_supply_data[self.cw_supply_data[STR_MPN]
                                                     == row[STR_MPN]]
            df_woi_formula = pd.merge(
                df_mpn_ub[[STR_SOLD_TO_ID,'sni_cw_minus_1','ub_eoh','avg_ub_lw5']],
                df_mpn_twos[[STR_SOLD_TO_ID, 'value']],
                how='left', on=[STR_SOLD_TO_ID]
            )
            df_woi_formula = pd.merge(
                df_woi_formula, df_mpn_supply_data,
                how='left', on=[STR_SOLD_TO_ID]
            )
            
            # 新增四列默认值0
            df_woi_formula[LIST_NEED_PROTECT_FEILD] = 0

            need_protect_weeks_data = list(row[LIST_NEED_PROTECT_FEILD])
            
            # 深拷贝
            process_list = need_protect_weeks_data[:]
            deduction_week_index = get_deduction_week_index(process_list)
            allocation_rule = get_allcaotion_rules(deduction_week_index, process_list)
            for deduction_index, rule in allocation_rule.items():
                for r in rule:
                    value = r.get('value')
                    mode = r.get('mode')
                    if not value:
                        continue
                    for m in value:
                        for i,allocation_quantity in m.items():
                            # 处理对应周的数据
                            deduction_week_field = f"last_week_por_discrete_cw{deduction_index+1}"
                            deduction_protect_week_field = f"need_protect_cw{deduction_index+1}"
                            shipment_plan_field = f"last_week_por_discrete_cw{i+1}"
                            need_protect_field = f"need_protect_cw{i+1}"
                            if mode == RuleEnum.PULL_IN.value:
                                # pull in 使用正数周
                                formula_result_field = f'formula_result_cw{i+1}'
                                woi_shipment_plan_field = shipment_plan_field
                            elif mode == RuleEnum.PUSH_OUT.value:
                                # push out 使用负数周
                                formula_result_field = f'formula_result_cw{deduction_index+1}'
                                woi_shipment_plan_field = deduction_week_field
                            df_woi_formula[formula_result_field] = 0
                            
                            while allocation_quantity > 0:
                                # 进行对woi/twos进行排序
                                for _, row_woi in df_woi_formula.iterrows():
                                    r_woi = get_woi(row_woi, row_woi[woi_shipment_plan_field])
                                    df_woi_formula.loc[df_woi_formula[STR_SOLD_TO_ID] == row_woi[STR_SOLD_TO_ID],
                                                    formula_result_field] = get_woi_divide_twos(r_woi, float(row_woi['value']))
                                
                                # 过滤扣减周shipment_plan >= odq的数据进行分货
                                # avg_ub_lw5 != 0, 则参与分货
                                filter_df_allocation = df_woi_formula.copy()
                                filter_df_allocation = filter_df_allocation[
                                    (filter_df_allocation[deduction_week_field] >= filter_df_allocation['odq'].astype(float)) & 
                                    (filter_df_allocation['avg_ub_lw5'] != 0)]
                                # 如果没有可分货的sold-to，则记录下来进行报错提醒
                                if filter_df_allocation.empty:
                                    mpns_failed_to_allocation.add(row[STR_MPN])
                                    break
                                
                                # 从大到小排序
                                df_sorted = filter_df_allocation.sort_values(
                                    by=formula_result_field, ascending=False)
                                if mode == RuleEnum.PULL_IN.value:
                                    # pull in 需要按照最小值来进行分配
                                    base_row = df_sorted.iloc[-1]
                                elif mode == RuleEnum.PUSH_OUT.value:
                                    # push out 需要按照最大值来进行分配
                                    base_row = df_sorted.iloc[0]

                                # 如果余数不够一个ODQ，则随机分配到某个sold-to上
                                odq_value = int(base_row['odq'])
                                step_size = 1 * odq_value
                                remainder = allocation_quantity
                                if remainder < step_size:
                                    random_row = df_sorted.sample(n=1)
                                    random_sold_to_id = random_row[STR_SOLD_TO_ID].values[0]
                                    allocation_process.append(f'分货方式: {mode}, sold-to-id: {random_sold_to_id}, 数量: {remainder}, 排序从大到小: {df_sorted[[STR_SOLD_TO_ID,formula_result_field]]}\n\n')
                                    df_woi_formula.loc[df_woi_formula[STR_SOLD_TO_ID] == random_sold_to_id,
                                                    shipment_plan_field] += remainder
                                    df_woi_formula.loc[df_woi_formula[STR_SOLD_TO_ID] == random_sold_to_id,
                                                    need_protect_field] += remainder
                                    df_woi_formula.loc[df_woi_formula[STR_SOLD_TO_ID] == random_sold_to_id,
                                                    deduction_week_field] -= remainder
                                    df_woi_formula.loc[df_woi_formula[STR_SOLD_TO_ID] == random_sold_to_id,
                                                    deduction_protect_week_field] -= remainder
                                    allocation_quantity -= remainder
                                    break
                                allocation_process.append(f'分货方式: {mode}, sold-to-id: {base_row[STR_SOLD_TO_ID]}, 数量: {step_size}, 排序从大到小: {df_sorted[[STR_SOLD_TO_ID,formula_result_field]]}\n\n')
                                # 修改df_woi_formula的shipment plan数据
                                df_woi_formula.loc[df_woi_formula[STR_SOLD_TO_ID] == base_row[STR_SOLD_TO_ID],
                                                shipment_plan_field] += step_size
                                df_woi_formula.loc[df_woi_formula[STR_SOLD_TO_ID] == base_row[STR_SOLD_TO_ID],
                                                need_protect_field] += step_size
                                df_woi_formula.loc[df_woi_formula[STR_SOLD_TO_ID] == base_row[STR_SOLD_TO_ID],
                                                deduction_week_field] -= step_size
                                df_woi_formula.loc[df_woi_formula[STR_SOLD_TO_ID] == base_row[STR_SOLD_TO_ID],
                                                deduction_protect_week_field] -= step_size
                                allocation_quantity -= step_size
            
            df_result = pd.concat(
                [df_result, df_woi_formula], ignore_index=False)
        
        data_process = {
            'allocation_process': allocation_process
        }
        df_process = pd.DataFrame(data_process)
        allocation_process_file = save_unique_file(df_process)
        logger.info(f'strategy_1_2_allocation_process_file>>>: {allocation_process_file}')
        
        if mpns_failed_to_allocation:
            raise ErrorExcept(ErrCode.System, ','.join(list(mpns_failed_to_allocation)))
        
        return df_result

    def get_strategy_ratio_data(self, df_ratio: pd.DataFrame,
                                allocation_data: pd.DataFrame):
        df_result = pd.DataFrame()
        # 记录分货失败的mpns
        mpns_failed_to_allocation = set()
        # 策略: 按照比例分配
        for _, row in allocation_data.iterrows():
            df_mpn_supply_data = self.cw_supply_data[self.cw_supply_data[STR_MPN]
                                                     == row[STR_MPN]]

            # 增加比例字段
            df_mpn_supply_data = pd.merge(df_mpn_supply_data,
                                          df_ratio,
                                          how='left', on=[STR_MPN, STR_SOLD_TO_ID])

            # 新增四列默认值0
            df_mpn_supply_data[LIST_NEED_PROTECT_FEILD] = 0

            need_protect_weeks_data = list(row[LIST_NEED_PROTECT_FEILD])

            # 深拷贝
            process_list = need_protect_weeks_data[:]
            deduction_week_index = get_deduction_week_index(process_list)
            allocation_rule = get_allcaotion_rules(deduction_week_index, process_list)
            for deduction_index, rule in allocation_rule.items():
                for r in rule:
                    value = r.get('value')
                    if not value:
                        continue
                    for m in value:
                        for i,allocation_quantity in m.items():
                            # 处理对应周的数据
                            deduction_week_field = f"last_week_por_discrete_cw{deduction_index+1}"
                            deduction_protect_week_field = f"need_protect_cw{deduction_index+1}"
                            shipment_plan_field = f"last_week_por_discrete_cw{i+1}"
                            need_protect_field = f"need_protect_cw{i+1}"
                            # 使用扣减周的比例进行分货
                            ratio_field = f'calculate_ratio_w{deduction_index+1}'
                            
                            # 过滤扣减周shipment_plan > 0的数据进行分货
                            filter_df_allocation = df_mpn_supply_data.copy()
                            filter_df_allocation = filter_df_allocation[
                                filter_df_allocation[deduction_week_field] > 0]
                            # 如果没有可分货的sold-to，则记录下来进行报错提醒
                            if filter_df_allocation.empty:
                                mpns_failed_to_allocation.add(row[STR_MPN])
                                break
                            
                            df_allocation_quantity = (allocation_quantity * df_mpn_supply_data[ratio_field].fillna(0)).astype(int)
                            df_mpn_supply_data[need_protect_field] += df_allocation_quantity
                            df_mpn_supply_data[shipment_plan_field] += df_allocation_quantity
                            # 扣减周逻辑
                            df_mpn_supply_data[deduction_week_field] -= df_allocation_quantity
                            df_mpn_supply_data[deduction_protect_week_field] -= df_allocation_quantity
                            
                            # 余数随机分配
                            remainder = allocation_quantity - df_allocation_quantity.sum()
                            if remainder > 0:
                                # 使用过滤后的sold_to，防止随机到0的被扣减为负数
                                random_row = filter_df_allocation.sample(n=1)
                                random_sold_to_id = random_row[STR_SOLD_TO_ID].values[0]
                                df_mpn_supply_data.loc[df_mpn_supply_data[STR_SOLD_TO_ID] == random_sold_to_id,
                                                       need_protect_field] += remainder
                                df_mpn_supply_data.loc[df_mpn_supply_data[STR_SOLD_TO_ID] == random_sold_to_id,
                                                       shipment_plan_field] += remainder
                                df_mpn_supply_data.loc[df_mpn_supply_data[STR_SOLD_TO_ID] == random_sold_to_id,
                                                       deduction_week_field] -= remainder
                                df_mpn_supply_data.loc[df_mpn_supply_data[STR_SOLD_TO_ID] == random_sold_to_id,
                                                       deduction_protect_week_field] -= remainder
            
            df_result = pd.concat(
                [df_result, df_mpn_supply_data], ignore_index=False)
        
        if mpns_failed_to_allocation:
            raise ErrorExcept(ErrCode.System, ','.join(list(mpns_failed_to_allocation)))
        wrong_allocation = df_result.loc[
            (df_result['last_week_por_discrete_cw1'] < 0) |
            (df_result['last_week_por_discrete_cw2'] < 0) | 
            (df_result['last_week_por_discrete_cw3'] < 0) | 
            (df_result['last_week_por_discrete_cw4'] < 0), STR_MPN].unique().tolist()
        if wrong_allocation:
            raise ErrorExcept(ErrCode.System, ','.join(wrong_allocation))
        return df_result

    def calculate_supply_protect(self, strategy, allocation_data: pd.DataFrame):
        try:
            if strategy == 0 or strategy == 1:
                return self.get_strategy_1_2_data(allocation_data)
            elif strategy == 2:
                # 使用各自占比比例，处理shipment plan
                o_ratio = SupplyProtectRatio(self.week_date)
                df_ratio = o_ratio.get_ratio_by_shipment_plan(self.cw_supply_data)
                
                return self.get_strategy_ratio_data(df_ratio, allocation_data)
            elif strategy == 3:
                # 使用用户自定义比例，mix
                o_ratio = SupplyProtectRatio(self.week_date)
                df_ratio = o_ratio.get_ratio_by_mix()
                
                return self.get_strategy_ratio_data(df_ratio, allocation_data)
            else:
                raise ErrorExcept(
                    ErrCode.DBQueryParams, ErrMsg.get(ErrCode.DBQueryParams))
        except ErrorExcept as e:
            raise ErrorExcept(
                ErrCode.System,
                AllocationRunErrorMsg.SupplyProtectionStrategyCalculateError[strategy] + \
                str(e.err_msg)
            )

@_async
def async_calculate_supply_protection(week_date: str,
                                      fiscal_week_year: int,
                                      calculate_id: int):
    
    # 点击 calculate.py 按钮后就要清空上次的计算结果
    AllocationRunSupplyProtectCalculateResult.delete_by_week(week_date)
    
    calculate_status = AllocationRunCalculateStatus.CalculationSuccessful
    failure_reason = ''
    try:
        supply_protector = SupplyProtector(week_date)
        
        # 获取用户指定策略的数据
        user_specified_data = get_user_specified_data(fiscal_week_year)
        strategy_mpn_data = user_specified_data.groupby('strategy')
        result_df = pd.DataFrame()
        # 根据指定策略进行计算
        for strategy, strategy_data in strategy_mpn_data:
            df_allocation = supply_protector.calculate_supply_protect(strategy, strategy_data)
            strategy_data_file = save_unique_file(df_allocation)
            logger.info(f'strategy: {strategy}, strategy_data_file>>>: {strategy_data_file}')
            result_df = pd.concat([result_df, df_allocation], ignore_index=True)
        
        # 生成文件
        calculate_result_file = save_unique_file(result_df)
        logger.info(f'calculate_result_file_path: {calculate_result_file}')
        
        # 处理数据存储
        table_fields = AllocationRunSupplyProtectCalculateResult.get_table_fields_list()
        save_df = result_df[table_fields].copy()
        save_df.replace({np.nan: None}, inplace=True)
        # 保存数据
        # 删除数据时机变更为点击 calculate.py 按钮后就要清空上次的计算结果
        # AllocationRunSupplyProtectCalculateResult.delete_by_week(week_date)
        AllocationRunSupplyProtectCalculateResult.bulk_save(save_df.to_dict("records"))
        
    except Exception as e:
        logger.error(f"allocation-run-calculate.py-failed: {str(e)}",
                     exc_info=True)
        calculate_status = AllocationRunCalculateStatus.CalculationFailed
        failure_reason = 'system error'
        AllocationRunSupplyOperateResult.reset_operate_status(
            fiscal_week_year, 2
        )
    except ErrorExcept as e:
        logger.error("allocation-run-calculate.py-failed",
                     exc_info=True)
        failure_reason = e.err_msg
        calculate_status = AllocationRunCalculateStatus.CalculationFailed
        AllocationRunSupplyOperateResult.reset_operate_status(
            fiscal_week_year, 2
        )
    finally:
        # 计算结束，更改计算状态
        SupplyAcquisitionCalculateRecord.update_by_id(
            calculate_id,
            {
                "calculate_status": calculate_status,
                "finish_time": datetime.now().strftime(DateTimeFormat),
                "failure_reason": failure_reason
            }
        )


def calculate_supply_protection_service(week_date: str, operator_id: int):
    fiscal_week_year = FiscalYearWeek.get_fiscal_week_year_by_week_date(
        week_date)
    # 生成第二步计算记录
    calculate_record = SupplyAcquisitionCalculateRecord(
        week_date, fiscal_week_year, operator_id,
        AllocationRunCalculateStatus.InCalculation, 2)
    calculate_id = calculate_record.save()
    logger.info(
        f"{week_date} supply protection start calculating, id is {calculate_id}")
    
    # 保存计算记录ID
    AllocationRunSupplyOperateResult.update_by_week(
        fiscal_week_year, {"calculate_step2_id": calculate_id}
    )

    async_calculate_supply_protection(week_date,
                                      fiscal_week_year,
                                      calculate_id)

    return calculate_id


# allocation run 第三小步 special supply计算逻辑

def get_uploaded_special_supply(week_date: str):
    return AllocationRunSpecialSupplyUpload.get_data_by_week(week_date)


def get_supply_protection_result(week_date: str):
    return AllocationRunSupplyProtectCalculateResult.get_supply_by_week(week_date)

def get_supply_preview_result(fiscal_week_year: int):
    return AllocationRunSupplyPreview.get_week_data(fiscal_week_year)

@_async
def async_calculate_special_supply(week_date: str,
                                   fiscal_week_year: int,
                                   calculate_id: int):
    calculate_status = AllocationRunCalculateStatus.CalculationSuccessful
    failure_reason = ''
    try:
        # 获取第一步计算后的preview结果
        df_supply_preivew_result = get_supply_preview_result(fiscal_week_year)

        # 获取用户上传special supply的数据
        df_special_supply = get_uploaded_special_supply(week_date)
        
        # 计算true incremental
        agg_rules = {
            "special_supply_cw1": "sum",
            "special_supply_cw2": "sum",
            "special_supply_cw3": "sum",
            "special_supply_cw4": "sum",
        }
        df_special_supply_sum = df_special_supply.groupby(
            [STR_MPN]).agg(agg_rules)

        df_true_incremental = pd.merge(df_supply_preivew_result,
                                       df_special_supply_sum,
                                       how='left', on=[STR_MPN])
        for i in range(4):
            df_true_incremental[f'true_incremental_cw{i+1}'] -= df_true_incremental[f'special_supply_cw{i+1}'].fillna(0)

        # 新增边界情况：如Ture Incremental CW+X的量小于 Special Supply CW+X的量，弹窗报错
        wrong_true_incremental = df_true_incremental.loc[
            (df_true_incremental['true_incremental_cw1'] < 0) |
            (df_true_incremental['true_incremental_cw2'] < 0) | 
            (df_true_incremental['true_incremental_cw3'] < 0) | 
            (df_true_incremental['true_incremental_cw4'] < 0), STR_MPN].unique().tolist()
        if wrong_true_incremental:
            raise ErrorExcept(ErrCode.System,
                              AllocationRunErrorMsg.SpecialSupplyCalculateError + ','.join(wrong_true_incremental))
        
        # 生成文件
        file_path_true_incremental = save_unique_file(df_true_incremental)
        logger.info(f'file_path_true_incremental {file_path_true_incremental}')
        
        save_dataframe_to_database_by_week(
            df_true_incremental,
            AllocationRunSpecialSupplyCalculateResultTrueIncremental,
            week_date
        )
        
        # 计算Demand with tag
        # 获取第一步上传的suplly data
        df_uploaded_supply_data = get_uploaded_supply(week_date)
        # 获取demand_with_tag数据，并进行转化
        df_demand_collection_all = OdsFastCpfDemandCollectionConfirmResult.get_demand_with_tag_data(
            fiscal_week_year)
        # sold-to-id字段类型统一为string
        df_demand_collection_all[STR_SOLD_TO_ID] = df_demand_collection_all[STR_SOLD_TO_ID].astype(str)
        
        pivot_columns = [
            STR_MPN,
            STR_SOLD_TO_ID,
            'priority',
            'sales_input_qty_cw1',
            'sales_input_qty_cw2',
            'sales_input_qty_cw3',
            'sales_input_qty_cw4',
        ]
        df_before_pivot = df_demand_collection_all[pivot_columns]
        
        df_pivot = df_before_pivot.pivot(
            index=[STR_MPN, STR_SOLD_TO_ID],
            columns='priority'
        )
        df_pivot = df_pivot.reset_index()
        # 重命名列
        df_pivot.columns = [
            STR_MPN,
            STR_SOLD_TO_ID,
            'cw1_p0_demand',
            'cw1_p1_demand',
            'cw1_p2_demand',
            'cw2_p0_demand',
            'cw2_p1_demand',
            'cw2_p2_demand',
            'cw3_p0_demand',
            'cw3_p1_demand',
            'cw3_p2_demand',
            'cw4_p0_demand',
            'cw4_p1_demand',
            'cw4_p2_demand',
        ]
        
        df_uploaded_supply_data = pd.merge(
            df_uploaded_supply_data, df_pivot,
            how='left', on=[STR_MPN, STR_SOLD_TO_ID]
        )
        
        # 不使用第二步分完货的数据，所以这些字段值都给0
        df_uploaded_supply_data[LIST_NEED_PROTECT_FEILD] = 0

        for _, row_special in df_special_supply.iterrows():
            current_special_mpn = row_special[STR_MPN]
            current_special_sold_to_id = row_special[STR_SOLD_TO_ID]
            if current_special_mpn not in df_uploaded_supply_data[STR_MPN].values:
                continue
            
            for j in range(4):
                special_supply_value = row_special[f"special_supply_cw{j+1}"]
                if special_supply_value == 0:
                    continue
                diff = special_supply_value
                for k in range(3):
                    current_demand_column = f"cw{j+1}_p{k}_demand"
                    condition_protect = (df_uploaded_supply_data[STR_MPN] == current_special_mpn) & (
                        df_uploaded_supply_data[STR_SOLD_TO_ID] == current_special_sold_to_id)
                    
                    diff -= df_uploaded_supply_data.loc[condition_protect, current_demand_column].fillna(0).iloc[0]
                    if diff >= 0:
                        df_uploaded_supply_data.loc[condition_protect,
                                                        current_demand_column] = 0
                        continue
                    else:
                        df_uploaded_supply_data.loc[condition_protect,
                                                        current_demand_column] = -diff
                        break
        # 生成文件
        file_path_demand_with_tag = save_unique_file(df_uploaded_supply_data)
        logger.info(f'file_path_demand_with_tag {file_path_demand_with_tag}')

        save_dataframe_to_database_by_week(
            df_uploaded_supply_data,
            AllocationRunSpecialSupplyCalculateResultDemandWithTag,
            week_date
        )
            
    except Exception as e:
        logger.error("allocation-run-special-supply-calculate.py-failed",
                     exc_info=True)
        calculate_status = AllocationRunCalculateStatus.CalculationFailed
        AllocationRunSupplyOperateResult.reset_operate_status(
            fiscal_week_year, 3
        )
    except ErrorExcept as e:
        logger.error("allocation-run-special-supply-calculate.py-failed",
                     exc_info=True)
        failure_reason = e.err_msg
        calculate_status = AllocationRunCalculateStatus.CalculationFailed
        AllocationRunSupplyOperateResult.reset_operate_status(
            fiscal_week_year, 3
        )
    finally:
        # 计算结束，更改计算状态
        SupplyAcquisitionCalculateRecord.update_by_id(
            calculate_id,
            {
                "calculate_status": calculate_status,
                "finish_time": datetime.now().strftime(DateTimeFormat),
                "failure_reason": failure_reason
            }
        )


def calculate_special_supply_service(week_date: str, operator_id: int):
    fiscal_week_year = FiscalYearWeek.get_fiscal_week_year_by_week_date(
        week_date)
    
    # 生成第三步计算记录
    calculate_record = SupplyAcquisitionCalculateRecord(
        week_date, fiscal_week_year, operator_id,
        AllocationRunCalculateStatus.InCalculation, 3)
    calculate_id = calculate_record.save()
    logger.info(
        f"{week_date} sepical supply start calculating, id is {calculate_id}")
    
    async_calculate_special_supply(week_date,
                                   fiscal_week_year,
                                   calculate_id)

    return calculate_id


def save_dataframe_to_database_by_week(df, table_class, week_date):
    # 处理数据存储
    table_fields = table_class.get_table_fields_list()
    save_df = df[table_fields].copy()
    save_df.replace({np.nan: None}, inplace=True)
    # 保存数据
    table_class.delete_by_week(week_date)
    table_class.bulk_save(save_df.to_dict("records"))
    