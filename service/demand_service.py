import os
import pandas as pd

from data.cascade_filter_data import DimFastBusinessSoldtoMapping
from util.conf import logging
from util.util import get_download_path, gen_dict_by_tuple, sort_by_list, gen_dict_by_tuple2, sort_by_list2
from util.const import RTM, MODEL
from data.fast_lite_demand_data import FastLiteAppFastDemandSummaryWa
from data.demand_data import AppFastDemandMonoSummaryWa
from data.fiscal_year_week import FiscalYearWeek


def get_demand_list_service(page_num: int, page_size: int) -> list:
    # ret, total = FastLiteAppFastDemandSummaryWa.get_fiscal_year_quarter_week_name(page_num, page_size)
    ret, total = AppFastDemandMonoSummaryWa.get_week_list(page_num, page_size)
    data_list = []
    for i in ret:
       data_list.append({
           "fiscalQtrWeekName": i[0],
           "fiscal_week_year": i[1]
       }) 
    
    res = {
        "dataList": {
            "records": data_list,
            "total": total,
            "size": page_size,
            "pages": page_num
        }
    }
    
    return res


def get_rtm_options_service() -> list:
    return RTM


def get_business_type_options_service(fiscal_week_year: int, rtm: list) -> list:
    if rtm is not None and len(rtm) == 0:
        rtm = RTM
    query_reult = DimFastBusinessSoldtoMapping.get_business_type(fiscal_week_year, rtm)
    temp = gen_dict_by_tuple(query_reult)
    ret = sort_by_list(rtm, temp)
    return ret


def get_sold_to_options_service(fiscal_week_year: int, rtm: list, business_type: list, fuzzy_filter: str) -> list:
    if rtm is not None and len(rtm) == 0:
        rtm = RTM
    if business_type is not None and len(business_type) == 0:
        business_type = get_business_type_options_service(fiscal_week_year, rtm)
    query_result = DimFastBusinessSoldtoMapping.get_sold_to(fiscal_week_year, rtm, business_type, fuzzy_filter)
    temp = gen_dict_by_tuple2(query_result)
    ret = sort_by_list2(business_type, temp)
    return ret


def get_demand_detail_service(fiscal_qtr_week_name: str, lob: str, model: list, sku: list, rtm: list, business_type: list, sold_to: list, orders: list, page_num: int, page_size: int) -> list:
    if rtm is not None and len(rtm) == 0:
        rtm = RTM

    ret_total, data_keys = FastLiteAppFastDemandSummaryWa.get_total(fiscal_qtr_week_name, lob, model, sku, rtm, business_type, sold_to, orders)
    ret, count = FastLiteAppFastDemandSummaryWa.get_sold_to_total(fiscal_qtr_week_name, lob, model, sku, rtm, business_type, sold_to, orders, page_num, page_size)
    titles = FastLiteAppFastDemandSummaryWa.get_title(fiscal_qtr_week_name)

    columns = []
    for i,v in enumerate(titles):
        if len(data_keys) > 0:
            columns.append({
                'key': list(data_keys)[i],
                'value': v
            })    

    res = {
            "columns": columns,
            "records": ret_total + ret,
            "total": count,
            "size": page_size,
            "pages": page_num
        }
    return res


def get_multi_version_demand_detail_service(fiscal_qtr_week_name: str, lob: str, model: list, sku: list, rtm: list, business_type: list, sold_to: list, orders: list, page_num: int, page_size: int) -> list:
    if lob is None or lob.strip() == '':
        lob = 'iPhone'
    if rtm is not None and len(rtm) == 0:
        rtm = RTM

    
    fiscal_week_year = FiscalYearWeek.get_fiscal_week_year_by_week_date(fiscal_qtr_week_name)
    ret_total, data_keys = AppFastDemandMonoSummaryWa.get_total_union(fiscal_week_year, fiscal_qtr_week_name, lob, model, sku, rtm, business_type, sold_to, orders)
    ret, count = AppFastDemandMonoSummaryWa.get_sold_to_total_union(fiscal_week_year, fiscal_qtr_week_name, lob, model, sku, rtm, business_type, sold_to, orders, page_num, page_size)
    titles = FastLiteAppFastDemandSummaryWa.get_title(fiscal_qtr_week_name)

    columns = []
    for i,v in enumerate(titles):
        if len(data_keys) > 0:
            columns.append({
                'key': list(data_keys)[i],
                'value': v
            })    

    res = {
            "columns": columns,
            "records": ret_total + ret,
            "total": count,
            "size": page_size,
            "pages": page_num
        }
    return res


def get_fast_demand_download_path(fiscal_qtr_week_name: str, demand_version: int = 0) -> str:
    path = get_download_path(fiscal_qtr_week_name, 3, demand_version)
    # # 如果存在文件, 直接进行下载, 不需要生成数据
    # if os.path.isfile(path):
    #     return path
    # 当前系统不存在下载文件, 则要新生成需要的数据
    gen_download_file(fiscal_qtr_week_name, path, demand_version)

    return path


def gen_download_file(fiscal_qtr_week_name: str, file_path: str, version: int =0):
    '''
    生成需要下载数据的csv文件
    按照下载类型, 查询对应的数据库, 生成数据
    '''
    try:
        data = []
        
        if version == 0:
            data = FastLiteAppFastDemandSummaryWa.get_download_data(fiscal_qtr_week_name)
        else:
            latest_version = AppFastDemandMonoSummaryWa.get_latest_version_by_week(fiscal_qtr_week_name)
            if version <= latest_version:
                fiscal_week_year = FiscalYearWeek.get_fiscal_week_year_by_week_date(fiscal_qtr_week_name)
                data = AppFastDemandMonoSummaryWa.get_download_data(fiscal_week_year, fiscal_qtr_week_name, version)
                
        columns = [
            "Week_Date",
            "RTM",
            "Business Type",
            "Customer Sold-to ID",
            "Sold-to Name",
            "Abbre.",
            "LOB",
            "Model",
            "FPH4",
            "Project Code",
            "SKU",
            "MPN",
            "Top Up Demand CW+1",
            "Top Up Demand CW+2",
            "Top Up Demand CW+3",
            "Top Up Demand CW+4",
            "Shipment Plan CW",
            "Shipment Plan CW+1",
            "Shipment Plan CW+2",
            "Shipment Plan CW+3",
            "PO Needed CW",
            "PO Needed CW+1",
            "PO Needed CW+2",
            "PO Needed CW+3"
        ]
        
        df = pd.DataFrame(data, columns=columns)
        df.to_csv(file_path, header=True, index=False)
        return True
    except Exception as e:
        logging.info(f"gen csv error is {e}.")
        return False