from hashlib import md5

from data.allocation_cpf_lob_data import OdsFastCpfSalesInputUpload
from data.allocation_prepare_data import *
from data.allocation_prepare_gc_dmp_data import AppFastAllocationDemandSubmissionTemplate
from data.allocation_submission_datasource_data import OdsFastCpfDemandSubmissionMultiOnlineUpload
from service.allocation_prepare_service import transfer_file_path, replenish_upload_dataframe
from util.file_util import save_df_to_md5_name
from util.util_operation import insert_operate_record


def upload_submission_file_by_rtm(rtm, file, fiscal_week_year, uploader, uploader_email, is_cpf_upload: bool):
    fiscal_obj = FiscalYearWeek.get_by_fiscal_week_year(fiscal_week_year)
    if fiscal_obj is None:
        raise ErrorExcept(ErrCode.DBQueryError, 'can not found fiscal day record')
    # file save
    path = os.path.dirname(os.path.dirname(os.path.abspath(__file__))) + f'/uploads/allocation/'
    if not os.path.exists(path):
        os.makedirs(path)
    file_md5 = md5(file.stream.read()).hexdigest()
    file.stream.seek(0)
    file.save(f'{path}/{file_md5}.xlsx')
    # 文件验证
    df = pd.read_excel(f'{path}/{file_md5}.xlsx')
    template_record = TblAllocationPrepareFile.find_by_phase_rtm_category(
        "2", rtm, PrepareSubmissionFileCategory.Template, fiscal_obj.fiscal_week_year
    )
    if len(template_record) == 0:
        raise ErrorExcept(ErrCode.DBQueryError, 'template file has not generated')
    tdf = pd.read_excel(transfer_file_path(template_record[0].upload_file_path))
    
    # 规则校验0: 表头校验, 必须与模版文件的表头一致
    if not list(df.columns) == list(tdf.columns):
        raise ErrorExcept(ErrCode.FileUploadError, FileUploadError.RTMUploadHeadError)

    # 校验规则1: 不允许上传空表, 除表头外无数据
    if len(df) == 0:
        raise ErrorExcept(ErrCode.FileUploadError, FileUploadError.RTMUploadEmptyError)
    
    compared_columns = list(PrepareSubmissionMonoHeaderDict.keys())[:17]
    # 校验规则2-1：每行数据的这些列的数据值应该包含在生成的Template表中
    merged_df = pd.merge(df[compared_columns], tdf[compared_columns],
                         how='outer', indicator=True)
    not_in_template = merged_df[merged_df['_merge'] == 'left_only']
    if not not_in_template.empty:
        raise ErrorExcept(ErrCode.FileUploadError, f"{FileUploadError.RTMUploadPreFilledError}")
    
    # 校验规则2-2：数据行数与模版表的只能少不能新增，且这些字段（同上述3字段：）值不能完全重复
    duplicate_rows = df[df.duplicated(compared_columns)]
    if len(df) > len(tdf) or not duplicate_rows.empty:
        raise ErrorExcept(ErrCode.FileUploadError, f"{FileUploadError.RTMUploadNewDataError}")

    # 校验规则2-3: Top Up Demand CW+1/+2/+3/+4 列下的各行内容仅能为0或正整数，如果是空则置为0
    non_positive_integer_and_zero_rows = []
    for i in range(1, 5):
        demand_col = f"Top Up Demand CW+{i}"
        df[demand_col].fillna(0, inplace=True)
        for row_idx, row in df.iterrows():
            if isinstance(row[demand_col], str) or not (isinstance(row[demand_col], (int, float)) and row[demand_col] >= 0 and row[demand_col] == int(row[demand_col])):
                non_positive_integer_and_zero_rows.append(row_idx+2)
        if len(non_positive_integer_and_zero_rows):
            raise ErrorExcept(ErrCode.FileUploadError,
                            f"{FileUploadError.RTMUploadRowsError} {','.join(map(str, non_positive_integer_and_zero_rows))}")
    
    # 校验规则3：by Sold-to by MPN的Top Up Demand CW+1/+2/+3/+4 需要大于等于对应周数的Sales Input中P0, P1, P2的总和
    sales_input_data = OdsFastCpfSalesInputUpload.find_by_rtm_and_lob(rtm, fiscal_obj.fiscal_week_year, "iPad")
    si_df = sales_input_data.groupby(["sold_to_id", "mpn"]).agg({
        "sales_input_qty_cw1": "sum",
        "sales_input_qty_cw2": "sum",
        "sales_input_qty_cw3": "sum",
        "sales_input_qty_cw4": "sum",
    })
    df2 = df[[
        "Customer Sold-to ID",
        "MPN / Apple Part #",
        "Top Up Demand CW+1",
        "Top Up Demand CW+2",
        "Top Up Demand CW+3",
        "Top Up Demand CW+4",
    ]]
    df2.columns = [
        "sold_to_id",
        "mpn",
        "top_up_demand_cw1",
        "top_up_demand_cw2",
        "top_up_demand_cw3",
        "top_up_demand_cw4",
    ]
    df3 = pd.merge(df2, si_df, how="left", left_on=["sold_to_id", "mpn"], right_on=["sold_to_id", "mpn"])
    error_rows = list(df3[df3["top_up_demand_cw1"] < df3["sales_input_qty_cw1"]].T.columns)
    error_rows.extend(list(df3[df3["top_up_demand_cw2"] < df3["sales_input_qty_cw2"]].T.columns))
    error_rows.extend(list(df3[df3["top_up_demand_cw3"] < df3["sales_input_qty_cw3"]].T.columns))
    error_rows.extend(list(df3[df3["top_up_demand_cw4"] < df3["sales_input_qty_cw4"]].T.columns))
    if len(error_rows) != 0:
        err_list = sorted(set(error_rows))
        raise ErrorExcept(ErrCode.ValidationError, FileUploadError.SubmissionDemandLowerThanSalesInput + ','.join(map(str, [i+2 for i in err_list])))
    # 如果某行的Top Up Demand+1/+2/+3/+4全部为空或者全部为0，此类数据依然需要保留
    # # remove all zero
    # df = df[df[[
    #     "Top Up Demand CW+1",
    #     "Top Up Demand CW+2",
    #     "Top Up Demand CW+3",
    #     "Top Up Demand CW+4"
    # ]].ne(0).any(axis=1)]
    # divide odq then multi odq
    df["Top Up Demand CW+1"] = (df["Top Up Demand CW+1"] / df["ODQ"]).apply(int) * df["ODQ"]
    df["Top Up Demand CW+2"] = (df["Top Up Demand CW+2"] / df["ODQ"]).apply(int) * df["ODQ"]
    df["Top Up Demand CW+3"] = (df["Top Up Demand CW+3"] / df["ODQ"]).apply(int) * df["ODQ"]
    df["Top Up Demand CW+4"] = (df["Top Up Demand CW+4"] / df["ODQ"]).apply(int) * df["ODQ"]
    
    # 如果某个HR的MPN在Multi/Online 上传的文件中已删除，需要将删除的数据再进行补充
    df = replenish_upload_dataframe(df, tdf,
                                    on=list(PrepareSubmissionMonoHeaderDict.keys())[:17],
                                    adjust_columns=["Top Up Demand CW+1",
                                                    "Top Up Demand CW+2",
                                                    "Top Up Demand CW+3",
                                                    "Top Up Demand CW+4",])
    if not is_cpf_upload:
        # save demand file - RTM上传之后显示在RTM页面的文件
        demand_records = TblAllocationPrepareFile.find_by_phase_rtm_category(
            "2", rtm, PrepareSubmissionFileCategory.GenerateAndUpload, fiscal_obj.fiscal_week_year
        )
        if len(demand_records) == 0:
            demand_record = TblAllocationPrepareFile()
            demand_record.upload_file_version = 0
            demand_record.fiscal_qtr_week_name = fiscal_obj.fiscal_qtr_week_name
            demand_record.fiscal_week_year = fiscal_obj.fiscal_week_year
            demand_record.rtm = rtm
            demand_record.lob = "iPad"
            demand_record.operate_phase = "2"
            demand_record.category = PrepareSubmissionFileCategory.GenerateAndUpload
        else:
            demand_record = demand_records[0]
            demand_record.upload_file_version += 1
        demand_record.upload_status = RTMSalesInputUploadStatus.Uploaded
        file_name = f"{rtm}_Demand_iPad_{fiscal_obj.fiscal_qtr_week_name}_{str(demand_record.upload_file_version+1)}.xlsx".replace('/', '_')
        local_file_name = save_df_to_md5_name(df, path, 'xlsx')
        demand_record.upload_file_name = file_name
        demand_record.upload_file_path = f"/file/storage/{local_file_name}"
        demand_record.upload_by = uploader
        demand_record.uploader_email = uploader_email
        demand_record.upload_at = datetime.now()
        demand_record.update_by = uploader
        demand_record.save()
    else:
        # 记录操作信息
        insert_operate_record(rtm, AllocationOperateCategory.SellInDemandCPFupload,
                              f'{file_md5}.xlsx', f'{path}/{file_md5}.xlsx',
                              uploader, uploader_email)
    
    # save hr only file
    submission_data = AppFastAllocationDemandSubmissionTemplate.get_by_rtm_lob(rtm, fiscal_obj.fiscal_week_year, "iPad")
    sub_df = submission_data[["mpn", "sold_to_id", "hr_lr"]]
    sub_df.columns = ["MPN / Apple Part #", "Customer Sold-to ID", "hr_lr"]
    hr_df = pd.merge(df, sub_df, how="left")
    hr_df = hr_df[hr_df["hr_lr"] == "HR"][list(PrepareSubmissionMonoHeaderDict.keys())]
    # 新增计算的四列数据
    hr_df.loc[:, 'Discrete CW+1'] = hr_df[['CW+1 Shipment Plan (Discrete)', 'Top Up Demand CW+1']].sum(axis=1)
    hr_df.loc[:, 'Discrete CW+2'] = hr_df[['CW+2 Shipment Plan (Discrete)', 'Top Up Demand CW+2']].sum(axis=1)
    hr_df.loc[:, 'Discrete CW+3'] = hr_df[['CW+3 Shipment Plan (Discrete)', 'Top Up Demand CW+3']].sum(axis=1)
    hr_df.loc[:, 'Discrete CW+4'] = hr_df['Top Up Demand CW+4']
    
    hr_only_records = TblAllocationPrepareFile.find_by_phase_rtm_category(
        "2", rtm, PrepareSubmissionFileCategory.HrOnly, fiscal_obj.fiscal_week_year
    )
    if len(hr_only_records) == 0:
        hr_only_records = TblAllocationPrepareFile()
        hr_only_records.upload_file_version = 0
        hr_only_records.fiscal_qtr_week_name = fiscal_obj.fiscal_qtr_week_name
        hr_only_records.fiscal_week_year = fiscal_obj.fiscal_week_year
        hr_only_records.rtm = rtm
        hr_only_records.lob = "iPad"
        hr_only_records.operate_phase = "2"
        hr_only_records.category = PrepareSubmissionFileCategory.HrOnly
    else:
        hr_only_records = hr_only_records[0]
        hr_only_records.upload_file_version += 1
    hr_only_records.upload_status = RTMSalesInputUploadStatus.Uploaded
    hr_name = f"{rtm}_Demand (HR Only)_iPad_{fiscal_obj.fiscal_qtr_week_name}.xlsx".replace('/', '_')
    hr_local_file_name = save_df_to_md5_name(hr_df, path, 'xlsx')
    hr_only_records.upload_file_name = hr_name
    hr_only_records.upload_file_path = f"/file/storage/{hr_local_file_name}"
    hr_only_records.upload_by = uploader
    hr_only_records.uploader_email = uploader_email
    hr_only_records.upload_at = datetime.now()
    hr_only_records.update_by = uploader
    hr_only_records.save()
    
    # save to datasource
    df.columns = list(PrepareSubmissionMonoHeaderDict.values())
    df["fiscal_qtr_week_name"] = fiscal_obj.fiscal_qtr_week_name
    df["fiscal_week_year"] = fiscal_obj.fiscal_week_year
    df["week_begin_dt"] = fiscal_obj.week_begin_dt
    df["week_end_dt"] = fiscal_obj.week_end_dt
    df["rtm"] = rtm
    # 先删除本周已有数据
    OdsFastCpfDemandSubmissionMultiOnlineUpload.delete_by_week_rtm(fiscal_obj.fiscal_qtr_week_name, rtm)
    OdsFastCpfDemandSubmissionMultiOnlineUpload.batch_save(df.to_dict("records"))
    # 更新状态为in progress
    TblAllocationPrepare.update_by_week_rtm_lob(
        fiscal_obj.fiscal_qtr_week_name, rtm, 'iPad',
        {'second_phase_status': RTMDemandSubmissionStatus.InProcess, 'update_by': uploader})
    return "ok"
