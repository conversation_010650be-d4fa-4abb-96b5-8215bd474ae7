from typing import List

from data.allocation_cpf_run_data import *
from util.const import Error<PERSON>xcept, ErrCode, ErrMsg, P<PERSON><PERSON><PERSON><PERSON><PERSON>


def get_preview_data(fiscal_week_year: str, project_code: List[str], mpn: List[str]):
    # 参数验证
    if fiscal_week_year is None:
        raise ErrorExcept(ErrCode.Param, "fiscal_week_year required")
    try:
        fiscal_week_year = int(fiscal_week_year)
    except Exception:
        raise ErrorExcept(ErrCode.Param, "fiscal_week_year must be integer")
    # 获取数据
    result = AllocationRunSupplyPreview.get_preview_data(fiscal_week_year, project_code, mpn)
    # 数据处理
    result = sorted(result, key=lambda x: x.project_code)
    result = sorted(result, key=lambda x: AllocationRunSupplyPreview.SALES_ORG_ORDER.index(x.sales_org))
    # 格式化输出
    columns = {
        'sales_org': 'Sales Org',
        'project_code': 'Project Code',
        'nand': 'Nand',
        'color': 'Color',
        'mpn': 'MPN / Apple Part #',
        'true_incremental_cw1': 'true Incremental CW+1',
        'true_incremental_cw2': 'true Incremental CW+2',
        'true_incremental_cw3': 'true Incremental CW+3',
        'true_incremental_cw4': 'true Incremental CW+4',
        'total_top_up_demand_cw1': 'Total Top up Demand CW+1',
        'total_top_up_demand_cw2': 'Total Top up Demand CW+2',
        'total_top_up_demand_cw3': 'Total Top up Demand CW+3',
        'total_top_up_demand_cw4': 'Total Top up Demand CW+4',
        'cum_gap_cw1': 'Cum Gap CW+1',
        'cum_gap_cw2': 'Cum Gap CW+2',
        'cum_gap_cw3': 'Cum Gap CW+3',
        'cum_gap_cw4': 'Cum Gap CW+4'
    }
    count_columns = [
        "true_incremental_cw1",
        "true_incremental_cw2",
        "true_incremental_cw3",
        "true_incremental_cw4",
        "total_top_up_demand_cw1",
        "total_top_up_demand_cw2",
        "total_top_up_demand_cw3",
        "total_top_up_demand_cw4",
        "cum_gap_cw1",
        "cum_gap_cw2",
        "cum_gap_cw3",
        "cum_gap_cw4",
    ]
    data = []
    current_project = {}
    for line in result:
        item = line._asdict()
        if item["project_code"] != current_project.get("project_code") or item["sales_org"] != current_project.get("sales_org"):
            if current_project.get("project_code") is not None:
                data.append(current_project)
            current_project = {"project_code": item["project_code"], "sales_org": item["sales_org"], "list": []}
        if current_project.get("total") is None:
            current_project["total"] = {
                "nand": "All",
                "color": "All",
                "mpn": "All",
            }
            for c in count_columns:
                current_project["total"][c] = item[c]
        else:
            for c in count_columns:
                current_project["total"][c] = (current_project["total"][c] or 0) + (item[c] or 0)
        current_project["list"].append(item)
    else:
        if current_project.get("project_code") is not None:
            data.append(current_project)

    return {"column": columns, "list": data}


def get_project_code(fiscal_week_year):
    if fiscal_week_year is None:
        raise ErrorExcept(ErrCode.Param, "fiscal_week_year required")
    try:
        fiscal_week_year = int(fiscal_week_year)
    except Exception:
        raise ErrorExcept(ErrCode.Param, "fiscal_week_year must be integer")
    result = AllocationRunSupplyPreview.get_project_code(fiscal_week_year)
    return {"list": [x.project_code for x in result]}


def get_mpn_id(fiscal_week_year, project_code):
    if fiscal_week_year is None:
        raise ErrorExcept(ErrCode.Param, "fiscal_week_year required")
    try:
        fiscal_week_year = int(fiscal_week_year)
    except Exception:
        raise ErrorExcept(ErrCode.Param, "fiscal_week_year must be integer")
    result = AllocationRunSupplyPreview.get_mpn_id(fiscal_week_year, project_code)
    return {"list": [x.mpn for x in result]}


def gen_allocation_run_preview_download_data(fiscal_week_year):
    fiscal_qtr = AllocationRunSupplyPreview.get_fiscal_qtr_by_fiscal_week_year(fiscal_week_year)
    path = os.path.dirname(os.path.dirname(os.path.abspath(__file__))) + f'/downloads/allocation_run_prepare/'
    if not os.path.exists(path):
        os.makedirs(path)
    path += f'Allocation Preview（By MPN）_IPAD_{fiscal_qtr}.xlsx'
    data = AllocationRunSupplyPreview.get_download_data(fiscal_week_year)
    columns = AllocationRunSupplyPreview.DOWNLOAD_COLUMN_NAME
    df = pd.DataFrame(data, columns=columns)
    df.to_excel(path, header=True, index=False)
    return path
