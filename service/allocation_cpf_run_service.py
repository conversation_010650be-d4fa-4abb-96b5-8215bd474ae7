import uuid
import pandas as pd
import re

from util.const import *
from util.util import _async
from util.cpf_util import cpf_merge_file_path
from util.cpf_const import AllocationRunErrorMsg
from data.allocation_cpf_run_data import *
from data.fiscal_year_week import FiscalYearWeek
from service.allocation_prepare_service import transfer_file_path
from data.allocation_demand_collection_overview_data import OdsFastCpfDemandCollectionConfirmResult


def get_allocation_run_detail_service(fiscal_qtr_week_name:str):
    record = AllocationRun.query_by_week(fiscal_qtr_week_name)
    if record:
        return {
            "fiscal_week_year": record[0].fiscal_week_year,
            "fiscal_qtr_week_name": record[0].fiscal_qtr_week_name,
            "lob": record[0].lob,
            "process_type": record[0].process_type,
            "latest_step": record[0].latest_step,
            "create_time": record[0].create_time.strftime(DateTimeFormat),
            "update_time": record[0].update_time.strftime(DateTimeFormat),
        }


def update_allocation_run_detail_service(fiscal_qtr_week_name: str,
                                         update_data: dict,
                                         operator_id):
    AllocationRun.update_by_week_lob(fiscal_qtr_week_name,
                                     "iPad", update_data)
    logger.info(f"{operator_id} update allocation run detail.")
    
    return "ok"


def get_supply_acquisition_detail_service(fiscal_qtr_week_name: str):
    
    record = AllocationRunSupplyAcquisition.query_by_week(fiscal_qtr_week_name)
    if record:
        supply_acquisition = {
            "fiscal_week_year": record[0].fiscal_week_year,
            "fiscal_qtr_week_name": record[0].fiscal_qtr_week_name,
            "supply_data_upload_status": record[0].supply_data_upload_status,
            "calculate_id": record[0].calculate_id,
            "create_time": record[0].create_time.strftime(DateTimeFormat),
            "update_time": record[0].update_time.strftime(DateTimeFormat),
        }
        # 查询上传过的文件信息
        supply_acquisition["file_list"] = get_supply_data_file(
            fiscal_qtr_week_name,
            AllocationRunFileType.Upload
        )
        # 查询当周是否有计算成功过的记录，如果有的话，则可以进行preview
        can_preview = SupplyAcquisitionCalculateRecord.check_calculate_success_by_week(
            fiscal_qtr_week_name
        )
        supply_acquisition["can_preview"] = can_preview
        
        return supply_acquisition


def get_supply_data_file(fiscal_qtr_week_name: str,
                         category: int):
    file_list = []
    file_info = AllocationRunFile.get_file_by_week_category_step(
        fiscal_qtr_week_name,
        category,
        AllocationRunStep.SupplyAcquisition
    )
    for f in file_info:
        if f.file_path:
            file_list.append({
                "fiscal_week_year": f.fiscal_week_year,
                "fiscal_qtr_week_name": f.fiscal_qtr_week_name,
                "file_name": f.file_name,
                "file_path": f.file_path,
                "category": f.category,
                "group": f.group,
            })
    
    return file_list


def get_supply_data_template_list_service(fiscal_qtr_week_name: str):
    template_file = get_supply_data_file(
        fiscal_qtr_week_name,
        AllocationRunFileType.Template
    )
    return template_file


def get_supply_data_template_service(supply_type: int, fiscal_qtr_week_name: str):
    file_path = ""
    file_name = ""
    file_info = AllocationRunFile.get_file_by_week_category_group_step(fiscal_qtr_week_name,
                                                                       AllocationRunFileType.Template,
                                                                       supply_type,
                                                                       AllocationRunStep.SupplyAcquisition)
    if file_info:
        file_path = file_info[0].file_path
        file_name = file_info[0].file_name
    return file_path, file_name


def generate_supply_data_template_file_service(template_type, fiscal_dt):
    week_info = FiscalYearWeek.get_week_by_date(fiscal_dt)
    fiscal_week_year = week_info[StrFiscalWeekYear]
    fiscal_qtr_week_name = week_info[StrFiscalQtrWeekName]
    # 查询模版内容

    file_name = ""
    if template_type == AllocationRunSupplyDataType.SupplyData:
        df_list = AppFastAllocationRunSupplyDataTemplateWi.find_by_week(fiscal_week_year)
        df = df_list[ALLOCATION_RUN_SUPPLY_DATA_HEADER_RAW]
        df.columns = ALLOCATION_RUN_SUPPLY_DATA_HEADER
        file_name = f"Supply_Data_Template_iPad_{fiscal_qtr_week_name}.xlsx"
    elif template_type == AllocationRunSupplyDataType.ExcessSupply:
        df_list = AppFastAllocationRunExcessSupplyDataTemplateWi.find_by_week(fiscal_week_year)
        df = df_list[ALLOCATION_RUN_EXCESS_SUPPLY_DATA_HEADER_RAW]
        df.columns = ALLOCATION_RUN_EXCESS_SUPPLY_DATA_HEADER
        file_name = f"Excess_Supply_Data_Template_iPad_{fiscal_qtr_week_name}.xlsx"
    elif template_type == AllocationRunSupplyDataType.StopSupply:
        # 查询上一周的内容
        last_fiscal_week_year = FiscalYearWeek.get_last_fiscal_week_year(fiscal_week_year)
        df_last_week = AllocationRunStopSupplyUpload.find_by_week(last_fiscal_week_year)
        if not df_last_week.empty:
            convert_columns = ['start_week', 'end_week']
            new_df = df_last_week.copy()
            week_dict = convert_week_date_to_fiscal_week_year(
                df_last_week, convert_columns)
            new_df[convert_columns] = new_df[convert_columns].replace(week_dict)
            # 每周模版表需自动更新，如current week 晚于End Week，则该条数据自动剔除
            filtered_df = new_df[new_df['end_week'] > fiscal_week_year]
            df_list = df_last_week.loc[filtered_df.index]
            df_list['current_week'] = fiscal_qtr_week_name
            df = df_list[ALLOCATION_RUN_STOP_SUPPLY_DATA_HEADER_RAW]
            df.columns = ALLOCATION_RUN_STOP_SUPPLY_DATA_HEADER
        else:
            df = pd.DataFrame(columns=ALLOCATION_RUN_STOP_SUPPLY_DATA_HEADER)
        file_name = f"Stop_Supply_Data_Template_iPad_{fiscal_qtr_week_name}.xlsx"

    # 生成文件
    unique_name = f"{uuid.uuid4().hex}.xlsx"
    file_path = cpf_merge_file_path(unique_name)
    df.to_excel(file_path, index=False)
    
    # 保存文件信息
    run_file = AllocationRunFile(
        fiscal_qtr_week_name,
        fiscal_week_year,
        AllocationRunFileType.Template,
        template_type,
        AllocationRunStep.SupplyAcquisition,
        f'/file/storage/{unique_name}',
        file_name, unique_name, None)
    run_file.save()


def upload_supply_data_file_service(upload_file, supply_type: int, fiscal_qtr_week_name: str, person_id: str):
    # 上传文件
    upload_df = pd.read_excel(upload_file)
    # 模版文件
    template_file_path, _ = get_supply_data_template_service(supply_type, fiscal_qtr_week_name)
    template_df = pd.read_excel(transfer_file_path(template_file_path))
    
    raw_columns = []
    file_name = ""
    db_clazz = None
    if supply_type == AllocationRunSupplyDataType.SupplyData:
        validate_supply_data(upload_df, template_df)
        # 空值/负数 置为0
        upload_df.fillna(0, inplace=True)
        replace_columns = ALLOCATION_RUN_SUPPLY_DATA_HEADER[11:]
        upload_df[replace_columns] = upload_df[replace_columns].clip(lower=0)
        raw_columns = ALLOCATION_RUN_SUPPLY_DATA_HEADER_RAW
        file_name = f"Supply_Data_iPad_{fiscal_qtr_week_name}.xlsx"
        db_clazz = AllocationRunSupplyDataUpload
    elif supply_type == AllocationRunSupplyDataType.ExcessSupply:
        validate_excess_supply(upload_df, template_df)
        raw_columns = ALLOCATION_RUN_EXCESS_SUPPLY_DATA_HEADER_RAW
        file_name = f"Excess_Supply_Data_iPad_{fiscal_qtr_week_name}.xlsx"
        db_clazz = AllocationRunExcessSupplyUpload
    elif supply_type == AllocationRunSupplyDataType.StopSupply:
        validate_stop_supply(upload_df, template_df)
        raw_columns = ALLOCATION_RUN_STOP_SUPPLY_DATA_HEADER_RAW
        file_name = f"Stop_Supply_Data_iPad_{fiscal_qtr_week_name}.xlsx"
        db_clazz = AllocationRunStopSupplyUpload
    
    # 生成文件
    unique_name = f"{uuid.uuid4().hex}.xlsx"
    file_path = cpf_merge_file_path(unique_name)
    upload_df.to_excel(file_path, index=False)
    
    fiscal_week_year = FiscalYearWeek.get_fiscal_week_year_by_week_date(fiscal_qtr_week_name)
    
    # 保存文件信息
    run_file = AllocationRunFile(
        fiscal_qtr_week_name,
        fiscal_week_year,
        AllocationRunFileType.Upload,
        supply_type,
        AllocationRunStep.SupplyAcquisition,
        f"/file/storage/{unique_name}",
        file_name, unique_name, person_id)
    file_recored_id = run_file.save()
    
    # 保存文件内容到数据库
    upload_df.columns = raw_columns
    
    # 需要特殊处理下stop supply中current_week的值
    if supply_type == AllocationRunSupplyDataType.StopSupply:
        upload_df["current_week"] = fiscal_qtr_week_name
    
    upload_df["fiscal_week_year"] = fiscal_week_year
    upload_df["fiscal_qtr_week_name"] = fiscal_qtr_week_name
    current_time = datetime.now().strftime(DateTimeFormat)
    upload_df["create_time"] = current_time
    upload_df["update_time"] = current_time
    upload_df.replace({np.nan: None}, inplace=True)
    db_clazz.delete_by_week(fiscal_qtr_week_name)
    db_clazz.bulk_save(upload_df.to_dict("records"))
    
    # 需要返回前端文件名称
    query_file_info = AllocationRunFile.query_by_id(file_recored_id)
    
    return query_file_info.file_name


def validate_supply_data(upload_df: pd.DataFrame, template_df: pd.DataFrame):
    # 规则校验0: 表头校验, 必须与模版文件的表头一致
    if not list(upload_df.columns) == list(template_df.columns):
        raise ErrorExcept(ErrCode.FileUploadError, FileUploadError.CPFAllocationRunSupplyDataHeaderError)
    
    # 校验规则1: 上传文件的Sales Org列至Customer Sold-to Name列下的各行的行数和内容需要表1保持一致
    if len(upload_df) != len(template_df):
        raise ErrorExcept(
            ErrCode.FileUploadError,
            FileUploadError.CPFAllocationRunSupplyDataRowColumnError)
    overlap_columns = ALLOCATION_RUN_SUPPLY_DATA_HEADER[:11]
    merged_df = pd.merge(upload_df[overlap_columns], template_df[overlap_columns],
                         how='outer', indicator=True)
    not_in_template = merged_df[merged_df['_merge'] != 'both']
    if not not_in_template.empty:
        raise ErrorExcept(ErrCode.FileUploadError, FileUploadError.CPFAllocationRunSupplyDataRowColumnError)
    
    # 校验规则2: 不允许上传没有任何修改的模版表（模版表下载后直接上传）
    exactly_equal = upload_df.equals(template_df)
    if exactly_equal:
        raise ErrorExcept(ErrCode.FileUploadError, FileUploadError.EmptyError)

    # 校验规则3: Max_CW_Shipment_Plan_and_Consumption_CQ_CW至Last_week_POR_discrete_CW4列内填写内容仅能为0或空置或正整数或负数，空置或负数自动补0
    validata_columns = ALLOCATION_RUN_SUPPLY_DATA_HEADER[11:]
    upload_df.fillna(0, inplace=True)
    upload_df[validata_columns] = upload_df[validata_columns].clip(lower=0)
    df = upload_df[upload_df[validata_columns].applymap(lambda x: isinstance(
        x, str) or not (isinstance(x, (int, float)) and x >= 0 and x == int(x))).any(axis=1)]
    non_positive_integer_and_zero_rows = df.index.tolist()
    if len(non_positive_integer_and_zero_rows):
        raise ErrorExcept(ErrCode.FileUploadError,
                          FileUploadError.InvalidRowsError
                          + ", ".join([str(idx+2) for idx in non_positive_integer_and_zero_rows]))
    
    # 校验规则4: Shipment_Plan_Solver_Landed_CWX+1减Shipment_Plan_Solver_Landed_CWX的值需要大于等于0
    columns_to_check = ["Shipment_Plan_Solver_Landed_CW","Shipment_Plan_Solver_Landed_CW1",
                        "Shipment_Plan_Solver_Landed_CW2","Shipment_Plan_Solver_Landed_CW3",
                        "Shipment_Plan_Solver_Landed_CW4"]
    # 计算每一列与前一列的差
    diff_df = upload_df[columns_to_check].diff(axis=1)
    
    # 判断是否差小于等于0，记录不满足条件的行号
    rows_with_negative_difference = upload_df[diff_df.lt(0).any(axis=1)].index.tolist()

    if len(rows_with_negative_difference) > 0:
        raise ErrorExcept(ErrCode.FileUploadError, 
                          FileUploadError.InvalidRowsError + 
                          f"{','.join(map(str,[i+2 for i in rows_with_negative_difference]))}")


def validate_stop_supply(upload_df: pd.DataFrame, template_df: pd.DataFrame):
    # 规则校验0: 表头校验, 必须与模版文件的表头一致
    if not list(upload_df.columns) == list(template_df.columns):
        raise ErrorExcept(ErrCode.FileUploadError, FileUploadError.CPFAllocationRunSupplyDataHeaderError)
    
    # 校验规则1: 不允许上传没有任何修改的模版表（模版表下载后直接上传）
    exactly_equal = upload_df.equals(template_df)
    if exactly_equal:
        raise ErrorExcept(ErrCode.FileUploadError, FileUploadError.EmptyError)
    
    # 校验规则2: Start Week至End Week列内填写内容格式仅为 FY[X]Q[X]W[X]
    # 定义正则表达式模式
    # Q:1-4, W:1-14, 但是只有Q1有W14
    pattern = r'^FY\d{2}Q([1-4]W(1[0-3]|[1-9])|1W14)$'

    # 判断两列是否符合指定格式，记录不满足条件的行号
    rows_with_invalid_format = []

    for index, row in upload_df.iterrows():
        start_week  = row["Start Week"]
        end_week = row["End Week"]
        if pd.isnull(start_week) or pd.isnull(end_week) \
            or not re.match(pattern, str(start_week)) \
            or not re.match(pattern, str(end_week)):
            rows_with_invalid_format.append(index+2)
            continue
        if start_week.endswith('14'):
            week_info = FiscalYearWeek.get_fiscal_week_year_by_week_date(start_week)
            if not week_info:
                rows_with_invalid_format.append(index+2)
                continue
        if end_week.endswith('14'):
            week_info = FiscalYearWeek.get_fiscal_week_year_by_week_date(end_week)
            if not week_info:
                rows_with_invalid_format.append(index+2)
    if rows_with_invalid_format:
            raise ErrorExcept(ErrCode.FileUploadError,
                              f"{FileUploadError.InvalidRowsError}{','.join(map(str, rows_with_invalid_format))}")


def validate_excess_supply(upload_df: pd.DataFrame, template_df: pd.DataFrame):
    # 规则校验0: 表头校验, 必须与模版文件的表头一致
    if not list(upload_df.columns) == list(template_df.columns):
        raise ErrorExcept(ErrCode.FileUploadError, FileUploadError.CPFAllocationRunSupplyDataHeaderError)
    
    # 校验规则1: 不允许上传没有任何修改的模版表（模版表下载后直接上传）
    exactly_equal = upload_df.equals(template_df)
    if exactly_equal:
        raise ErrorExcept(ErrCode.FileUploadError, FileUploadError.EmptyError)
    
    # 校验规则2:Excess Supply CW+1至Excess Supply CW+4列内填写内容仅能为0或空置或正整数，空置自动补0
    validata_columns = ALLOCATION_RUN_EXCESS_SUPPLY_DATA_HEADER[7:]
    upload_df.fillna(0, inplace=True)
    df = upload_df[upload_df[validata_columns].applymap(lambda x: isinstance(
        x, str) or not (isinstance(x, (int, float)) and x >= 0 and x == int(x))).any(axis=1)]
    non_positive_integer_and_zero_rows = df.index.tolist()
    if len(non_positive_integer_and_zero_rows):
        raise ErrorExcept(ErrCode.FileUploadError,
                          FileUploadError.InvalidRowsError
                          + ", ".join([str(idx+2) for idx in non_positive_integer_and_zero_rows]))


def supply_acquisition_calculate_service(fiscal_qtr_week_name: str, operator_id: str):
    fiscal_week_year = FiscalYearWeek.get_fiscal_week_year_by_week_date(fiscal_qtr_week_name)
    # 生成计算记录
    record = SupplyAcquisitionCalculateRecord(
        fiscal_qtr_week_name, fiscal_week_year, operator_id,
        AllocationRunCalculateStatus.InCalculation)
    record_id = record.save()
    
    # 更新supply acquisition记录
    AllocationRunSupplyAcquisition.update_by_week(fiscal_qtr_week_name, {
        "calculate_id": record_id
    })
    # 异步执行计算过程
    async_claculate_supply(
        fiscal_qtr_week_name,
        fiscal_week_year,
        record_id
    )
    
    return record_id


def get_supply_acquisition_calculate_status_service(calculate_id: int):
    # 根据calculate_id查询计算状态
    record = SupplyAcquisitionCalculateRecord.query_calculate_status(calculate_id)
    if not record:
        return None
    # 计算错误信息，限制500个英文字符，如果超出用省略号代替
    max_character = 500
    failure_reason = ''
    if record[1]:
        failure_reason = record[1] if len(record[1]) <= max_character else record[1][:max_character] + "..."
    return { "calculate_status": record[0], "failure_reason": failure_reason }


def delete_supply_data_file_service(supply_type: str, fiscal_qtr_week_name: str, operator_id: str):
    # 查询出对应的文件信息
    file_info = AllocationRunFile.get_file_by_week_category_group_step(
        fiscal_qtr_week_name,
        AllocationRunFileType.Upload,
        supply_type,
        AllocationRunStep.SupplyAcquisition
    )
    
    # 删除对应的文件
    if not file_info:
        raise ErrorExcept(
            ErrCode.System,
            "delete file error, please check it.")
    file_path = transfer_file_path(file_info[0].file_path)
    is_file_deleted = remove_file(file_path)
    if is_file_deleted:
        logger.info(f"person_id: {operator_id} has deleted {file_info[0].file_name}.")
    
    # 删除保存的文件信息,
    # AllocationRunFile.delete_by_id(file_info[0].id)
    AllocationRunFile.update_file_by_week_category_group_step(
        fiscal_qtr_week_name,
        AllocationRunFileType.Upload,
        supply_type,
        AllocationRunStep.SupplyAcquisition,
        {
            "file_name": None,
            "file_path": None,
            "unique_name": None,
            "person_id": operator_id 
        }
    )
    logger.info(f"{operator_id} delete file, file_type is {supply_type}")
    
    clazz = None
    update_supplu_acquisition = False
    if supply_type == AllocationRunSupplyDataType.SupplyData:
        update_supplu_acquisition = True
        clazz = AllocationRunSupplyDataUpload
    elif supply_type == AllocationRunSupplyDataType.ExcessSupply:
        clazz = AllocationRunExcessSupplyUpload
    elif supply_type == AllocationRunSupplyDataType.StopSupply:
        clazz = AllocationRunStopSupplyUpload
    
    # 删除保存在数据库的内容
    clazz.delete_by_week(fiscal_qtr_week_name)
    
    
    # 如果是删除了必填的Supply Data文件，还需要更新AllocationRunSupplyAcquisition
    if update_supplu_acquisition:
        AllocationRunSupplyAcquisition.update_by_week(
            fiscal_qtr_week_name,
            {
                "supply_data_upload_status": SupplyDataUploadStatus.Deleted
            }
        )
    
    return "ok"


def convert_week_date_to_fiscal_week_year(df_stop_supply: pd.DataFrame, specified_columns: list):
    '''将stop supply中的FY24Q1W2转化为202402，返回 {'FY24Q1W2': 202402}的结构'''
    
    # 使用 drop_duplicates() 方法获取指定列中的去重值
    unique_values = df_stop_supply[specified_columns].drop_duplicates()

    # 将去重值转化为一维数组
    unique_week_list = unique_values.values.flatten()
    
    week_dict = {}
    
    for w in unique_week_list:
        week_dict[w] = FiscalYearWeek.get_fiscal_week_year_by_week_date(w)
    
    return week_dict


def convert_stop_supply_week(df: pd.DataFrame,
                             fiscal_week_year: int):
    week_dict = convert_week_date_to_fiscal_week_year(
        df, ['Start Week', 'End Week'])
    
    # 计算end_week-current_week的差值
    # 根据差值确定CW+1,..,CW+4
    project_code_dict = {}
    for _, row in df.iterrows():
        sold_to_id = row["Customer Sold-to ID"]
        project_code = row["Project Code"]
        if pd.isnull(sold_to_id) or pd.isnull(project_code) or isinstance(sold_to_id, str):
            continue
        # 如果还未到停货起始周，则不进行停货
        if fiscal_week_year < week_dict.get(row["Start Week"]):
            continue
        d_value = week_dict.get(row["End Week"]) - fiscal_week_year
        new_key = "-".join([str(sold_to_id), str(project_code)])
        if d_value:
            new_value = d_value if d_value < 4 else 4
            project_code_dict[new_key] = new_value
        else:
            logger.info(f'{new_key} 本周无需停货')
    logger.info(f'project_code_dict>>> {fiscal_week_year} - {project_code_dict}')
    return project_code_dict


def calculate_supply_and_stop(fiscal_week_year: int,
                              df_supply_data: pd.DataFrame,
                              df_stop_supply: pd.DataFrame):
    """
    第一步计算 supply data 和 stop supply，按照sold-to/mpn 来进行停货。
    下一步中需要用到的数据依然是sold-to/mpn级别的数据，所以先不用计算Incremental
    """
    
    if not df_stop_supply.empty:
        # cwx 计算
        cwx = convert_stop_supply_week(df_stop_supply, fiscal_week_year)
        stop_project_code = {}
        for i, v in cwx.items():
            por_list = []
            for j in range(v):
                por_list.append(f"Last_week_POR_discrete_CW{j+1}")
            stop_project_code[i] = por_list
        
        # 先处理停货数据，把对应的Last_week_POR_discrete_CWx置为0
        for k, c in stop_project_code.items():
            split_key = k.split("-")
            df_supply_data.loc[
                ((df_supply_data['Customer Sold-to ID'] == int(split_key[0])) 
                 & (df_supply_data['Project Code'] == split_key[1])), c] = 0
        
    # 再计算Incremental，生成第一步的另一个输出结果，新的dataframe
    # group by mpn
    df = df_supply_data.copy()
    df_incremental = df[["MPN / Apple Part #"] +
                        ALLOCATION_RUN_SUPPLY_DATA_HEADER[11:]
                        ].groupby(["MPN / Apple Part #"]).sum().reset_index()
    
    for m in range(4):
        df_incremental[f"Incremental CW+{m+1}(V1)"] = df_incremental[f"Shipment_Plan_Solver_Landed_CW{m+1}"] - \
            df_incremental[f"Shipment_Plan_Solver_Landed_CW{m if m > 0 else ''}"] - \
                df_incremental[f"Last_week_POR_discrete_CW{m+1}"]
    file_path_1 = save_unique_file(df_supply_data)
    file_path_2 = save_unique_file(df_incremental)
    logger.info(f'first step: df_supply_data {file_path_1}, df_incremental {file_path_2}')
    return df_supply_data, df_incremental


def calculate_cw_pull_in(df_supply_data: pd.DataFrame, df_incr_group: pd.DataFrame):
    # 是否发生pull in 与CW_Pull_In_Qty相关
    
    # 先计算出新的字段 CW_Pull_In_Qty
    # CW_Pull_In_Qty = Max_CW_Shipment_Plan_and_Consumption_CQ_CW - Shipment_Plan_Solver_Landed_CW
    df_supply_data["CW_Pull_In_Qty"] = df_supply_data["Max_CW_Shipment_Plan_and_Consumption_CQ_CW"] - \
        df_supply_data["Shipment_Plan_Solver_Landed_CW"]
    
    df_supply_data["Cum_POR"] = df_supply_data[POR_CWX_COLUMNS].sum(axis=1)

    # 计算Need_Protect_CWx_CW_Pull_in 和 Last_week_POR_discrete_CWx
    '''
    diff = CW_Pull_In_Qty
    diff = diff - last_week_por_discrete_cwx
    if diff=<0: last_week_por_discrete_cwx = -diff, break
    if diff>0: last_week_por_discrete_cwx = 0
    last_week_por_discrete_cw4之后
    if diff>0:
    diff = diff - incremental_cwx
    if incremental_cwx<0: continue
    if diff<=0: need_protect_cwx_cw_pull_in = incremental_cwx + diff, incremental_cwx = -diff, break
    if diff>0: need_protect_cwx_cw_pull_in = incremental_cwx, incremental_cwx = 0
    incremental_cw4之后
    if diff>0: raise
    '''

    df_supply_data[NEED_PROTECT_CW_PULL_IN_COLUMNS] = 0
    
    # 每行都要重新计算
    for idx, row in df_supply_data.iterrows():
        cw_pull_in = row["CW_Pull_In_Qty"]
        row_mpn = row["MPN / Apple Part #"]
        if cw_pull_in == 0:
            # 不需要进行计算
            continue
        # 先计算 last_week_por_discrete_cwx
        diff = cw_pull_in
        for j in range(4):
            diff = diff - row[POR_CWX_COLUMNS[j]]
            if diff <= 0:
                df_supply_data.loc[idx, POR_CWX_COLUMNS[j]] = -diff
                # 如果在某一周够扣除了，则终止内循环
                break
            else:
                # 扣除POR时，不需要protect
                # df_supply_data.loc[idx, NEED_PROTECT_CW_PULL_IN_COLUMNS[j]] = row[POR_CWX_COLUMNS[j]]
                df_supply_data.loc[idx, POR_CWX_COLUMNS[j]] = 0
        
        if diff <= 0:
            continue
        
        # 计算该MPN的 incremental 总和
        # cum_incr = df_incr_group.loc[
        #     row_mpn, "Incremental CW+1(V1)":"Incremental CW+4(V1)"].sum()
        
        for k in range(4):
            df_incremental_cwx = df_incr_group.loc[df_incr_group['MPN / Apple Part #'] == row_mpn,
                                                f"Incremental CW+{k+1}(V1)"]
            incremental_cwx = df_incremental_cwx.iloc[0]
            if incremental_cwx < 0:
                continue
            
            diff = diff - incremental_cwx
            if diff <= 0:
                df_supply_data.loc[idx, NEED_PROTECT_CW_PULL_IN_COLUMNS[k]] = incremental_cwx + diff
                df_incr_group.loc[df_incr_group['MPN / Apple Part #'] == row_mpn,
                                  f"Incremental CW+{k+1}(V1)"] = -diff
                # 已经够分，跳出内循环
                break
            else:
                df_supply_data.loc[idx, NEED_PROTECT_CW_PULL_IN_COLUMNS[k]] = incremental_cwx
                df_incr_group.loc[df_incr_group['MPN / Apple Part #'] == row_mpn,
                                  f"Incremental CW+{k+1}(V1)"] = 0
        
        # 如果所有的量都不够扣除，则需要报错
        if diff > 0:
            raise ErrorExcept(
                ErrCode.System, SUPPLY_CALCULATE_ERROR
            )
    file_path_1 = save_unique_file(df_supply_data)
    file_path_2 = save_unique_file(df_incr_group)
    logger.info(f'pull in: df_supply_data {file_path_1}, df_incr_group {file_path_2}')
    return df_supply_data, df_incr_group


def calculate_de_commit(df_incr_group: pd.DataFrame):
    '''
    need_protect_cwx 默认0，如果incremental对应周有负数，则将负数赋值给当周
    true_incremental_cwx 默认0
    incremental_cwx 不变
    diff = sum(need_protect_cwx)
    for incremental_cwx:
        if incremental < 0:
            continue
        diff = diff+incremental
        if diff<=0:
            need = need+incremental
            true = 0
        else:
            need = need+incremental-diff
            true = diff
    四周都计算完后，diff<0: raise 
    '''
    # 添加新字段
    raw_incr_columns = [
        'Incremental CW+1(V1)',
        'Incremental CW+2(V1)',
        'Incremental CW+3(V1)',
        'Incremental CW+4(V1)',
    ]
    df_incr_group[NEED_PROTECT_CW_COLUMNS] = df_incr_group[
        raw_incr_columns].clip(upper=0)
    # .applymap(lambda x: 0 if x >= 0 else x)
    
    df_incr_group[TRUE_INCREMENTAL_CWX_COLUMNS] = 0
    
    new_value = []
    # 接下来是按照对应列进行扣减的逻辑
    for idx, row in df_incr_group.iterrows():
        diff = row[NEED_PROTECT_CW_COLUMNS].sum()
        for i in range(4):
            # 按照每个周来进行扣减
            incr = row[raw_incr_columns[i]]
            if incr < 0:
                continue
            diff = diff + incr
            if diff <= 0:
                row[NEED_PROTECT_CW_COLUMNS[i]] = incr
                row[TRUE_INCREMENTAL_CWX_COLUMNS[i]] = 0
            elif diff <= incr:
                row[NEED_PROTECT_CW_COLUMNS[i]] = incr - diff
                row[TRUE_INCREMENTAL_CWX_COLUMNS[i]] = diff
            else:
                row[NEED_PROTECT_CW_COLUMNS[i]] = 0
                row[TRUE_INCREMENTAL_CWX_COLUMNS[i]] = incr
                
        new_value.append(row)
        # 如果所有周都cover不住，则计算报错
        if diff < 0:
            raise ErrorExcept(
                ErrCode.System, SUPPLY_CALCULATE_ERROR
            )
    df_incr_group = pd.DataFrame(new_value)
    file_path_1 = save_unique_file(df_incr_group)
    logger.info(f'decommit: df_incr_group {file_path_1}')
    return df_incr_group


def calculate_excess_supply(df_incremental: pd.DataFrame, df_excess_supply: pd.DataFrame):
    '''
    将by country by mpn 维度的数据，将true incremental 和 excess supply对应周进行加和后生成最终的数据
    '''
    if df_excess_supply.empty:
        for i in range(4):
            df_incremental[TRUE_INCREMENTAL_CWX_COLUMNS[i]] = df_incremental[TRUE_INCREMENTAL_CWX_COLUMNS[i]]
            # 如果没有上传excess supply文件，默认字段为0
            df_incremental[EXCESS_SYPPLY_CWX_COLUMNS[i]] = 0
        return df_incremental
    
    df = pd.merge(
        df_incremental, df_excess_supply,
        how='left',on=['MPN / Apple Part #']
    )
    
    df[EXCESS_SYPPLY_CWX_COLUMNS].fillna(0, inplace=True)
    
    # 再计算加和
    for i in range(4):
        df[TRUE_INCREMENTAL_CWX_COLUMNS[i]] = df[TRUE_INCREMENTAL_CWX_COLUMNS[i]
                                                 ] + df[EXCESS_SYPPLY_CWX_COLUMNS[i]]
    file_path_1 = save_unique_file(df)
    logger.info(f'plus excess: df {file_path_1}')
    return df


@_async
def async_claculate_supply(fiscal_qtr_week_name: str,
                           fiscal_week_year: int,
                           calculate_id: int):
    '''异步执行supply的计算
    0. 读取上传文件的内容
    1. 计算上传的文件，生成MPN维度的数据
    2. 查询Demand Collection Overview中的数据， 并进行转换与计算
    3. 合并上面两个结果，生成最终的数据
    '''
    calculate_status = AllocationRunCalculateStatus.CalculationSuccessful
    failure_reason = ''
    try:
        # 读取上传的文件内容
        uploaded_list = get_supply_data_file(
            fiscal_qtr_week_name,
            AllocationRunFileType.Upload
        )
        df_supply_data = pd.DataFrame()
        df_stop_supply = pd.DataFrame()
        df_excess_supply = pd.DataFrame()
        for k in uploaded_list:
            if k.get('group') == AllocationRunSupplyDataType.SupplyData:
                df_supply_data = pd.read_excel(transfer_file_path(k.get('file_path')))
            elif k.get('group') == AllocationRunSupplyDataType.StopSupply:
                df_stop_supply = pd.read_excel(transfer_file_path(k.get('file_path')))
            elif k.get('group') == AllocationRunSupplyDataType.ExcessSupply:
                df_excess_supply = pd.read_excel(transfer_file_path(k.get('file_path')))
                df_excess_supply = df_excess_supply[
                    ['MPN / Apple Part #'] + EXCESS_SYPPLY_CWX_COLUMNS]
        
        if df_supply_data.empty:
            raise ErrorExcept(
                ErrCode.System,
                "please upload required file."
            )
        
        # 计算上传的文件内容
        df_supply_data, df_incremental_group = calculate_supply_and_stop(
            fiscal_week_year,
            df_supply_data, df_stop_supply)
        
        df_supply_data, df_incremental_group = calculate_cw_pull_in(
            df_supply_data, df_incremental_group)
        
        df_incremental = calculate_de_commit(
            df_incremental_group)
        
        # 计算已经变更过的supply data数据，进行by mpn汇总加和
        # 需要的字段
        need_count_fileds = ALLOCATION_RUN_SUPPLY_DATA_HEADER[:8] + \
            ALLOCATION_RUN_SUPPLY_DATA_HEADER[11:] + \
            ['CW_Pull_In_Qty'] + NEED_PROTECT_CW_PULL_IN_COLUMNS
        df_supply_data_part = df_supply_data[need_count_fileds]
        # 转换字段名称
        need_count_fileds_raw = ALLOCATION_RUN_SUPPLY_DATA_HEADER_RAW[:8] + \
            ALLOCATION_RUN_SUPPLY_DATA_HEADER_RAW[11:] + \
            ['cw_pull_in_qty'] + [
                'need_protect_cw1_cw_pull_in',
                'need_protect_cw2_cw_pull_in',
                'need_protect_cw3_cw_pull_in',
                'need_protect_cw4_cw_pull_in',
            ]
        df_supply_data_part.columns = need_count_fileds_raw
        df_supply_data_summary = df_supply_data_part[need_count_fileds_raw].groupby(
            ["sales_org", "fph1", "fph3", "project_code", "nand", "color", "mpn"]
        ).sum().reset_index()
        
        df_final_incremental_total = calculate_excess_supply(
            df_incremental, df_excess_supply)
        
        df_final_incremental = df_final_incremental_total[
            ['MPN / Apple Part #',
             'Incremental CW+1(V1)',
             'Incremental CW+2(V1)',
             'Incremental CW+3(V1)',
             'Incremental CW+4(V1)'
            ] + TRUE_INCREMENTAL_CWX_COLUMNS+NEED_PROTECT_CW_COLUMNS+EXCESS_SYPPLY_CWX_COLUMNS]
        df_final_incremental.columns = [
            'mpn',
            'incremental_cw1',
            'incremental_cw2',
            'incremental_cw3',
            'incremental_cw4',
            'true_incremental_cw1',
            'true_incremental_cw2',
            'true_incremental_cw3',
            'true_incremental_cw4',
            'need_protect_cw1',
            'need_protect_cw2',
            'need_protect_cw3',
            'need_protect_cw4',
            'excess_supply_cw1',
            'excess_supply_cw2',
            'excess_supply_cw3',
            'excess_supply_cw4',
        ]
        
        # 查询，计算并转化Demand Collection Overview的数据
        df_demand_collection_all = OdsFastCpfDemandCollectionConfirmResult.get_demand_with_tag_data(
            fiscal_week_year)
        agg_rules = {
            "sales_input_qty_cw1": "sum",
            "sales_input_qty_cw2": "sum",
            "sales_input_qty_cw3": "sum",
            "sales_input_qty_cw4": "sum",
        }
        df_demand_collection_total = df_demand_collection_all.groupby(['mpn']).agg(agg_rules)
        df_demand_collection_total.columns = [
            'total_top_up_demand_cw1',
            'total_top_up_demand_cw2',
            'total_top_up_demand_cw3',
            'total_top_up_demand_cw4',
        ]
        df_demand_collection_pivot  = df_demand_collection_all.groupby(['mpn', 'priority']).agg(agg_rules)
        # 行转列 priority
        df_demand_collection_pivot = df_demand_collection_pivot.reset_index()
        df_demand_collection_pivot = df_demand_collection_pivot.pivot(
            index='mpn', columns='priority')
        df_demand_collection_pivot.columns = [
            'p0_top_up_demand_cw1',
            'p1_top_up_demand_cw1',
            'p2_top_up_demand_cw1',
            'p0_top_up_demand_cw2',
            'p1_top_up_demand_cw2',
            'p2_top_up_demand_cw2',
            'p0_top_up_demand_cw3',
            'p1_top_up_demand_cw3',
            'p2_top_up_demand_cw3',
            'p0_top_up_demand_cw4',
            'p1_top_up_demand_cw4',
            'p2_top_up_demand_cw4',
        ]
        df_demand_collection = pd.merge(
            df_demand_collection_total,
            df_demand_collection_pivot,
            how='left', on=['mpn']
        )
        
        # 结果合并，生成最终的数据保存到数据库
        df1 = pd.merge(
            df_supply_data_summary,
            df_final_incremental,
            how='left', on=['mpn']
        )
        df = pd.merge(
            df1,
            df_demand_collection,
            how='left', on=['mpn']
        )
        df['fiscal_qtr_week_name'] = fiscal_qtr_week_name
        df['fiscal_week_year'] = fiscal_week_year
        
        current_time = datetime.now().strftime(DateTimeFormat)
        df['create_time'] = current_time
        df['update_time'] = current_time
        
        df['cum_gap_cw1'] = df['total_top_up_demand_cw1'].fillna(0) - df['true_incremental_cw1'].fillna(0)
        df['cum_gap_cw2'] = df['cum_gap_cw1'] + df['total_top_up_demand_cw2'].fillna(0) - df['true_incremental_cw2'].fillna(0)
        df['cum_gap_cw3'] = df['cum_gap_cw2'] + df['total_top_up_demand_cw3'].fillna(0) - df['true_incremental_cw3'].fillna(0)
        df['cum_gap_cw4'] = df['cum_gap_cw3'] + df['total_top_up_demand_cw4'].fillna(0) - df['true_incremental_cw4'].fillna(0)
        df.replace({np.nan: None}, inplace=True)
        
        AllocationRunSupplyPreview.delete_by_week(fiscal_qtr_week_name)
        AllocationRunSupplyPreview.bulk_save(df.to_dict("records"))
        
        logger.info(f"allocation-run-calculate.py-successfully.")
    except (Exception, ErrorExcept) as e:
        logger.error("allocation-run-calculate.py-failed",
                     exc_info=True)
        calculate_status = AllocationRunCalculateStatus.CalculationFailed
        failure_reason = AllocationRunErrorMsg.SupplyUploadCalculateError
    finally:
        # 计算结束，更改计算状态
        SupplyAcquisitionCalculateRecord.update_by_id(
            calculate_id,
            {
                "calculate_status": calculate_status,
                "finish_time": datetime.now().strftime(DateTimeFormat),
                "failure_reason": failure_reason
            }
        )


def save_unique_file(df: pd.DataFrame):
    unique_name = f"{uuid.uuid4().hex}.xlsx"
    file_path = cpf_merge_file_path(unique_name)
    df.to_excel(file_path, index=False)
    return f"/file/storage/{unique_name}"
