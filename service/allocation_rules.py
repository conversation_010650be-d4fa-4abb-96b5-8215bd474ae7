
from enum import Enum


class RuleEnum(Enum):
    PULL_IN = "pull_in"
    PUSH_OUT = "push_out"


def get_deduction_week_index(need_protect_week_values: list):
    return [i for i, v in enumerate(need_protect_week_values) if v < 0]


def rule_processing(rules_result: dict, process_list: list,
                    list_index: int, negative_index: int,
                    rule: RuleEnum):
    '''
    处理rules和对应list
    '''
    diff = process_list[list_index] + process_list[negative_index]
    
    if rule == RuleEnum.PULL_IN.value:
        rule_index = 0
    elif rule == RuleEnum.PUSH_OUT.value:
        rule_index = 1
    
    if diff >= 0:
        rules_result.get(negative_index)[rule_index].get("value").append(
            {list_index: -process_list[negative_index]})
        process_list[list_index] = diff
        process_list[negative_index] = 0
    else:
        rules_result.get(negative_index)[rule_index].get("value").append(
            {list_index: process_list[list_index]})
        process_list[list_index] = 0
        process_list[negative_index] = diff


def get_allcaotion_rules(allocation_week_indices: list, process_list: list):
    rules = {}
    for allocation_index in allocation_week_indices:
        
        rules[allocation_index] = [{"mode": RuleEnum.PULL_IN.value,
                                    "value": []},
                                   {"mode":RuleEnum.PUSH_OUT.value,
                                    "value": []}
                                ]
        
        for idx, value in enumerate(process_list):
            
            if idx == allocation_index or value <= 0:
                continue
            
            if idx < allocation_index:
                rule_processing(rules, process_list,
                                idx, allocation_index, RuleEnum.PULL_IN.value)
            else:
                rule_processing(rules, process_list,
                                idx,allocation_index, RuleEnum.PUSH_OUT.value)
            
            if process_list[allocation_index] == 0:
                break
    return rules


def test_case_1():
    l1 = [-10, 20, -15, 5]
    except_rules1 = {0: [{"mode":"pull_in",
                        "value": []}, {"mode":"push_out",
                                        "value": [{1: 10}]}],
                    2: [{"mode":"pull_in",
                        "value": [{1: 10}]}, {"mode":"push_out","value": [{3: 5}]}]}

    # 深拷贝，复制一份新数据
    process_list1 = l1[:]
    allocation_week_index = get_deduction_week_index(process_list1)
    rules1 = get_allcaotion_rules(allocation_week_index, process_list1)
    print(rules1 == except_rules1)


def test_case_2():
    l2 = [20, -10, -15, 5]
    except_rules2 = {1: [{"mode":"pull_in","value": [{0: 10}]}, {"mode":"push_out", "value": []}],
                    2: [{"mode":"pull_in","value": [{0: 10}]}, {"mode":"push_out", "value": [{3: 5}]}]}

    process_list2 = l2[:]
    allocation_week_index2 = get_deduction_week_index(process_list2)
    rules2 = get_allcaotion_rules(allocation_week_index2, process_list2)
    print(rules2 == except_rules2)
