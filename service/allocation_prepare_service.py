import uuid

from data.allocation_prepare_data import *
from data.allocation_prepare_gc_dmp_data import AppFastTaskStatusWi
from data.allocation_prepare_gc_dmp_data import AppFastDemandRtmSalesTemplateWi
from util.util import compare_datetime
from util.file_util import get_absolute_path, hash_file_md5, convert_file_path
from openpyxl.worksheet.datavalidation import DataValidation
from data.cpf_sell_in_demand_data import *
from util.template_email_sender import TemplateEmail
from util.cpf_util import get_cpf_mail_navigate_url, get_navigate_url_list_page
from data.allocation_cpf_lob_data import TblAllocationCpfLob
from service.common import get_current_time_with_mock, is_history_week_in_allocation


def get_allocation_prepare_list_service(rtm: str, fiscal_week_year: int):
    ret = TblAllocationPrepare.get_allocation_prepare_list(rtm, fiscal_week_year)
    return ret

def get_allocation_prepare_week_list_service(rtm: str) -> list:
    ret = TblAllocationPrepare.get_fiscal_week(rtm)
    return ret

def check_whether_ready_service(fiscal_week_year: int):
    # 首先查一下数据是否ready
    # 如果没有ready, 则需要弹出提示信息
    whether_ready = AppFastTaskStatusWi.get_whether_ready(fiscal_week_year)
    if not whether_ready:
        raise ErrorExcept(ErrCode.ValidationError, UnreadyDataError)
    return True


def get_allocation_prepare_detail_service(id: int, enter_phase: int, fiscal_week_year: int):
    ret = TblAllocationPrepare.get_detail(id)
    if not enter_phase:
        enter_phase = int(ret['operate_phase'])
    common_fields = {
        'id': ret['id'],
        'rtm': ret['rtm'],
        'lob': ret['lob'],
        StrFiscalQtrWeekName: ret[StrFiscalQtrWeekName],
        StrFiscalWeekYear: ret[StrFiscalWeekYear],
        'phase': ret['operate_phase'],
        'upload_status': ret['upload_status'],
        'update_by': ret['update_by'],
        'create_time': ret['create_time'],
        'update_time': ret['update_time'] if ret['update_by'] else None,
        'current_time': get_current_time_with_mock(),
        'is_history_week': is_history_week_in_allocation(ret[StrFiscalWeekYear])
    }

    # 根据不同的阶段，返回不同的字段
    if enter_phase == RTMAllocationPhase.SalesInput:
        rtm_upload_time = ret['upload_at']
        cpf_upload_time = ret['upload_at_cpf']
        is_show_rtm_upload = True \
        if (rtm_upload_time is None and cpf_upload_time is None) \
            or (rtm_upload_time is not None and cpf_upload_time is None) \
            or compare_datetime(rtm_upload_time, cpf_upload_time) \
        else False
        
        latest_upload = {}
        latest_upload['upload_file_name'] = ret[f"upload_file_name{'' if is_show_rtm_upload else '_cpf'}"]
        latest_upload['upload_file_path'] = ret[f"upload_file_path{'' if is_show_rtm_upload else '_cpf'}"]
        latest_upload['upload_file_version'] = ret[f"upload_file_version{'' if is_show_rtm_upload else '_cpf'}"]
        latest_upload['upload_by'] = ret[f"upload_by{'' if is_show_rtm_upload else '_cpf'}"]
        latest_upload['uploader_email'] = ret[f"uploader_email{'' if is_show_rtm_upload else '_cpf'}"]
        latest_upload['upload_at'] = ret[f"upload_at{'' if is_show_rtm_upload else '_cpf'}"]
        
        operate_fields = {
            'rtm_file_download_by_cpf': ret['rtm_file_download_by_cpf'],
            'rerun': ret['rerun'],
        }
        
        return {**common_fields, **latest_upload, **operate_fields}
    elif enter_phase == RTMAllocationPhase.DemandSubmission:
        # 第二阶段要区分不同的RTM
        # Multi/Online 是需要有下载模版，上传和下载文件
        current_file_info = {
            "template_file_name": None,
            "template_file_path": None
        }
        if ret['rtm'] == AllocationRTM.Multi or ret['rtm'] == AllocationRTM.Online:
            template_file = get_files_with_5_conditions(ret[StrFiscalWeekYear], enter_phase, ret['rtm'], 0)
            current_file_info["template_file_name"] = template_file["file_name"]
            current_file_info["template_file_path"] = template_file["file_path"]

        upload_file_info = get_files_with_5_conditions(ret[StrFiscalWeekYear], enter_phase, ret['rtm'], 3)
        current_file_info.update(upload_file_info)
        
        current_phase_status = {"upload_status": ret['second_phase_status']}
        
        return {**common_fields, **current_phase_status, **current_file_info}
    elif enter_phase == RTMAllocationPhase.DemandAdjustment:
        # 第三阶段中，都是下载模版文件，上传和下载文件
        current_file_info = {
            "template_file_name": None,
            "template_file_path": None
        }

        template_file = get_files_with_5_conditions(ret[StrFiscalWeekYear], enter_phase, ret['rtm'], 0)
        current_file_info["template_file_name"] = template_file["file_name"]
        current_file_info["template_file_path"] = template_file["file_path"]

        # 取最新上传的文件
        rtm_upload_file_info = get_files_with_5_conditions(ret[StrFiscalWeekYear], enter_phase, ret['rtm'], 1)
        cpf_upload_file_info = get_files_with_5_conditions(ret[StrFiscalWeekYear], enter_phase, ret['rtm'], 2)
        if rtm_upload_file_info["upload_at"] and cpf_upload_file_info["upload_at"]:
            latest_file = rtm_upload_file_info if rtm_upload_file_info[
                "upload_at"] > cpf_upload_file_info["upload_at"] else cpf_upload_file_info
        else:
            latest_file = rtm_upload_file_info if rtm_upload_file_info[
                "upload_at"] else cpf_upload_file_info
        current_file_info.update(latest_file)
        
        current_phase_status = {"upload_status": ret['third_phase_status']}
        
        return {**common_fields, **current_phase_status, **current_file_info}
        
    return common_fields

def get_files_with_5_conditions(filscal_week_year: int, phase: int, rtm: str, file_category: int, lob: str = 'iPad'):
    file_info = {
        "file_path": None,
        "file_name": None,
        "category": None,
        "file_version": None,
        "upload_by": None,
        "uploader_email": None,
        "upload_at": None,
    }
    ret = TblAllocationPrepareFile.get_allocation_prepare_file(filscal_week_year, str(phase), lob, file_category, rtm)
    if ret:
        file_info['file_path'] = ret.upload_file_path
        file_info['file_name'] = ret.upload_file_name
        file_info['category'] = ret.category
        file_info['file_version'] = ret.upload_file_version
        file_info['upload_by'] = ret.upload_by
        file_info['uploader_email'] = ret.uploader_email
        file_info['upload_at'] = ret.upload_at.strftime(DateTimeFormat) if ret.upload_at else None
    return file_info


def get_allocation_prepare_template_file_service(rtm: str, fiscal_week_year: int) -> tuple[str, str]:
    file_path, file_name = get_template_file(rtm, fiscal_week_year)
    return file_path, file_name


def get_template_file(rtm: str, fiscal_week_year: int) -> tuple[str, str]:
    # 查询模版文件
    template_record = TblAllocationPrepareFile.get_allocation_prepare_file(
        fiscal_week_year, str(RTMAllocationPhase.SalesInput), "iPad", 0, rtm)
    if template_record:
        file_path, file_name = convert_file_path(template_record.upload_file_path), template_record.upload_file_name
    else:
        # 兼容历史数据 - 历史数据直接从数据库查询生成固定文件，会有读写时序问题，EOFError
        file_path, file_name = generate_sales_input_template_file(rtm, fiscal_week_year, None)

    return file_path, file_name


def generate_sales_input_template_file(rtm: str, fiscal_week_year: int, fiscal_qtr_week_name: str):
    '''生成sales input阶段的模版文件，文件信息保存到数据库'''
    
    # 文件路径和唯一文件名
    folder_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__))) + "/uploads/allocation/"
    if not os.path.exists(folder_path):
        os.mkdir(folder_path)
    unique_filename = f"{uuid.uuid4().hex}.xlsx"
    file_name = f"{rtm.replace('/', '_')}_Sales_Input_Template_{fiscal_week_year}.xlsx"
    file_path = folder_path + unique_filename
    
    # 从数据库获取模版数据
    template_content_raw = AppFastDemandRtmSalesTemplateWi.get_template_by_rtm_lob(rtm, fiscal_week_year)
    
    # 取指定的列
    template_content = template_content_raw[TemplateFileRawHeader]
    
    # 更新列名
    template_content.columns = TemplateFileHeader
    
    # 保存 Excel 文件
    add_dropdown_and_save(file_path, template_content)
    
    # 保存模版文件信息到数据库
    current_time = datetime.now().strftime(DateTimeFormat)
    template_file_mapping = {
        'fiscal_qtr_week_name': fiscal_qtr_week_name,
        'fiscal_week_year': fiscal_week_year,
        'rtm': rtm,
        'lob': "iPad",
        "operate_phase": str(RTMAllocationPhase.SalesInput),
        'upload_by': "system",
        'upload_file_name': file_name,
        'upload_file_version': 0,
        'upload_file_path': f"/file/storage/{unique_filename}",
        'update_by': "system",
        'category': 0,
        'upload_at': current_time,
        'create_time': current_time,
        'update_time': current_time
    }
    TblAllocationPrepareFile.insert_and_update_unique(template_file_mapping)
    
    return file_path, file_name


def add_dropdown_and_save(absolute_file_path: str, content: pd.DataFrame):
    with pd.ExcelWriter(absolute_file_path, engine='openpyxl') as writer:
        # 写入数据
        content.to_excel(writer, sheet_name='Sheet1', index=False)
        worksheet = writer.sheets['Sheet1']
        # 筛选 Open Backlog over Published for CW+3 SP > 0 的数据
        filtered_content = content.loc[content['Open Backlog over Published for CW+3 SP'] > 0]
        # 添加下拉列表验证
        for idx, row in filtered_content.iterrows():
            value = row['Priority']
            if value == 'P0':
                add_validation_to_cell(worksheet, P0ReasonValidate, idx)
            elif value == 'P1':
                add_validation_to_cell(worksheet, P1ReasonValidate, idx)
            elif value == 'P2':
                add_validation_to_cell(worksheet, P2ReasonValidate, idx)

def add_validation_to_cell(worksheet, source, current_index, start_index = 2):
    dv = DataValidation(type='list', formula1=f"\"{','.join(source)}\"", allow_blank=True)
    # 对应Reason for CW+X，X=1,2,3,4
    columns = ['W', 'Y', 'AA', 'AC']
    for column in columns:
        dv.add(column + str(current_index + start_index))
    worksheet.add_data_validation(dv)

def get_template_dataframe(rtm: str, fiscal_week_year: int):
    template_file_path, _ = get_template_file(rtm, fiscal_week_year)
    template_df = pd.read_excel(template_file_path)
    return template_df

def validate_with_template(upload_df: pd.DataFrame, template_df: pd.DataFrame, empty_table: bool = True):

    # 规则校验0: 表头校验, 必须与模版文件的表头一致
    if not list(upload_df.columns) == list(template_df.columns):
        raise ErrorExcept(ErrCode.FileUploadError, FileUploadError.RTMUploadHeadError)

    # 校验规则1: 不允许上传空表, 除表头外无数据
    if len(upload_df) == 0:
        if not empty_table:
            raise ErrorExcept(ErrCode.FileUploadError, FileUploadError.RTMUploadEmptyError)
        else:
            # CP&F 可上传空表, 不再进行下面的校验规则
            return EMPTY_TABLE
    
    # 规则被拆分了，需要重新验证
    # 校验规则2: 每行数据的 Sales Org, RTML4, LOB/FPH L1, Prod/FPH L3, Project Code, Nand, Color, MPN/ Apple Part #, Customer Sold-to, Customer Name, Open Backlog over Published for CW+3 SP, Priority 列数据应该包含在生成的Template表中（数据行只能少不能新增，且不能重复） - 删除
    
    # 校验规则2-1：每行数据的这些列的数据值应该包含在生成的Template表中
    merged_df = pd.merge(upload_df[OverlapColumns], template_df[OverlapColumns],
                         how='outer', indicator=True)
    not_in_template = merged_df[merged_df['_merge'] == 'left_only']
    if not not_in_template.empty:
        raise ErrorExcept(ErrCode.FileUploadError, f"{FileUploadError.RTMUploadPreFilledError}")
    
    # 校验规则2-2：数据行数与模版表的只能少不能新增，且这些字段（同上述3字段：）值不能完全重复
    duplicate_rows = upload_df[upload_df.duplicated(OverlapColumns)]
    if len(upload_df) > len(template_df) or not duplicate_rows.empty:
        raise ErrorExcept(ErrCode.FileUploadError, f"{FileUploadError.RTMUploadNewDataError}")

    # 校验规则3: Sales input Qty for CW+X列如果有数且大于0，则与其对应的Reason for CW+X不能为空，而且内容是从预填的下拉选项中选择的。(X = 1-4)
    invalid_reason_rows = []
    reason_validation = []
    for i in range(1, 5):
        qty_col = f'Sales input Qty for CW+{i}'
        reason_col = f'Reason for CW+{i}'
        priority_col = 'Priority'
        for idx, row in upload_df.iterrows():
            qty = row[qty_col]
            reason = row[reason_col]
            priority = row[priority_col]
            if qty and qty > 0:
                if priority == 'P0':
                    if pd.isna(reason) or reason not in P0ReasonValidate:
                        invalid_reason_rows.append(idx+2)
                elif priority == 'P1':
                    if pd.isna(reason) or reason not in P1ReasonValidate:
                        invalid_reason_rows.append(idx+2)
                elif priority == 'P2':
                    if pd.isna(reason) or reason not in P2ReasonValidate:
                        invalid_reason_rows.append(idx+2)
        if len(invalid_reason_rows) > 0:
            reason_validation.append(f"The “Reason for CW+{i}” can not be empty for the following rows: {','.join(map(str,invalid_reason_rows))}.")
            invalid_reason_rows = []
    if reason_validation:
        raise ErrorExcept(ErrCode.FileUploadError, f"{''.join(reason_validation)}")

    # 校验规则4: 
    # by Sold-to，by MPN数据行第 Sales input Qty for CW+1， Sales input Qty for CW+2， Sales input Qty for CW+3， Sales input Qty for CW+4列的值加和
    # 不能大于Open Backlog over Published for CW+3 SP的值
    # 将数据按照 Sold-to 和 MPN 分组
    StrSoldToId = 'Customer Sold-to ID'
    StrMPN = 'MPN / Apple Part #'
    data_grouped = upload_df.groupby([StrSoldToId, StrMPN])
    invalid_qty = []
    for name, group in data_grouped:
        upload_row = upload_df[(upload_df[StrSoldToId] == name[0]) & (upload_df[StrMPN] == name[1])]
        total_sales_input_qty = group['Sales input Qty for CW+1'].sum() + group['Sales input Qty for CW+2'].sum() + group['Sales input Qty for CW+3'].sum() + group['Sales input Qty for CW+4'].sum()
        if total_sales_input_qty > upload_row['Open Backlog over Published for CW+3 SP'].iloc[0]:
            invalid_qty.append((name[0],name[1]))
    if len(invalid_qty) > 0:
        invalid_rows = []
        for item in invalid_qty:
            invalid_rows += upload_df[(upload_df[StrSoldToId] == item[0]) & (upload_df[StrMPN] == item[1])].index.tolist()
        raise ErrorExcept(ErrCode.FileUploadError, f"{FileUploadError.SalesInputExceedOpenBacklogError}{[row_idx+2 for row_idx in invalid_rows]}")

def rtm_update_file_info(id, update_file_mapping: dict):
    TblAllocationPrepare.update_data_by_id(id, update_file_mapping)
    
def upload_file_by_rtm_service(file, uploader: str, uploader_email: str, id: int):

    # 查询当前记录
    ap = TblAllocationPrepare.get_detail(id)

    # 获取模版DataFrame
    template_dataframe = get_template_dataframe(ap[RtmKey], ap[StrFiscalWeekYear])
    # 获取上传文件DataFrame
    upload_dataframe = pd.read_excel(file)
    # 与模版文件对比，校验文件
    validate_with_template(upload_dataframe, template_dataframe, False)
    
    file_md5 = hash_file_md5(file)
    absolute_path = get_absolute_path('/uploads/allocation/')
    # 校验通过后，修正上传文件的内容
    correct_df = correct_upload_file(upload_dataframe)
    
    # 补充数据
    correct_df = replenish_upload_dataframe(correct_df, template_dataframe,
                               on=TemplateFileHeader[:21],
                               adjust_columns=['Sales input Qty for CW+1','Sales input Qty for CW+2',
                                               'Sales input Qty for CW+3','Sales input Qty for CW+4'])
    
    add_dropdown_and_save(f"{absolute_path}{file_md5}.xlsx", correct_df)
    
    # 组合需要更新的文件信息
    if ap['upload_file_version'] is None:
        file_version = 1
    else:
        file_version = ap['upload_file_version'] + 1
    ret_datetime = datetime.now()
    current_time = ret_datetime.strftime(DateTimeFormat)
    file_name = f"{ap[RtmKey]}_Sales_Input_{ap['lob']}_{ap[StrFiscalQtrWeekName]}_{file_version}.xlsx".replace('/', '_')
    file_path = f"/file/storage/{file_md5}.xlsx"
    upload_file_mapping = {
        'upload_at': current_time,
        'upload_by': uploader,
        'uploader_email': uploader_email,
        'upload_file_name': file_name,
        'upload_file_path': file_path,
        'upload_file_version': file_version,
        'update_time': current_time,
        'update_by': uploader
    }
    # 更新文件上传状态
    if ap['upload_status'] != RTMSalesInputUploadStatus.Uploaded:
        upload_file_mapping['upload_status'] = RTMSalesInputUploadStatus.Uploaded
    
    # 校验成功后，保存文件信息
    rtm_update_file_info(id, upload_file_mapping)
    
    # 留存上传记录
    upload_record = TblUploadFileRecord(id, file_name, uploader)
    TblUploadFileRecord.save(upload_record)
    
    # RTM上传成功，通知CP&F查收，模版3
    # 查询对应cpf记录ID
    cpf_recored = TblAllocationCpfLob.get_by_week_lob(ap[StrFiscalWeekYear], 'iPad')
    
    if len(cpf_recored) > 0:
        navigate_url = get_cpf_mail_navigate_url(
            cpf_recored[0].id, ap[StrFiscalQtrWeekName], ap[StrFiscalWeekYear], 'iPad')
    else:
        navigate_url = get_navigate_url_list_page('cpf')
    TemplateEmail().sales_input_3(ap[StrFiscalQtrWeekName], ap[RtmKey], uploader, current_time, navigate_url)
    
    return {"file_path": file_path}

# 修正上传文件中的内容
def correct_upload_file(source_dataframe: pd.DataFrame) -> pd.DataFrame:
    # 将用户填入的Sales Input 数除以ODQ，然后向下取整，再乘以ODQ
    correct_rows = []
    for _, row in source_dataframe.iterrows():
        odq = row['ODQ']
        for num in range(1,5):
            qty = row[f'Sales input Qty for CW+{num}']
            if not pd.isna(qty) and qty > 0:
                row[f'Sales input Qty for CW+{num}'] = qty // odq * odq
        correct_rows.append(row)
    return pd.DataFrame(correct_rows)

def replenish_upload_dataframe(upload_dataframe: pd.DataFrame,
                               template_dataframe: pd.DataFrame,
                               on: list,
                               adjust_columns: list) -> pd.DataFrame:
    '''使用模版数据补充上传文件缺失内容'''
    target_dataframe = template_dataframe[on]
    target_dataframe = pd.merge(target_dataframe, upload_dataframe,
             on=on, how='left')
    # 将指定列空值置为0
    target_dataframe[adjust_columns] = target_dataframe[adjust_columns].fillna(0)
    return target_dataframe

def transfer_file_path(file_path: str):
    split_path = file_path.split('/file/storage/')
    if len(split_path) > 1:
        absolute_path = get_absolute_path('/uploads/allocation/')
        return f"{absolute_path}{split_path[1]}"
    else:
        return ""


def get_open_backlog_less_than_monday(rtm: str, fiscal_week_year: int):
    return AppFastDemandRtmSalesTemplateWi.query_open_backlog_decrease(rtm, fiscal_week_year)
