from hashlib import md5

from data.allocation_prepare_data import *
from data.allocation_submission_datasource_data import *
from util.file_util import save_df_to_md5_name
from util.rtm_demand_adjustment_const import *
from service.rtm_demand_adjustment_service import validate_with_template, correct_delta_data, transfer_file_path
from util.template_email_sender import TemplateEmail
from util.cpf_util import get_mail_navigate_url
from util.util_operation import insert_operate_record
from service.allocation_prepare_service import get_current_time_with_mock, is_history_week_in_allocation


def upload_collection_file_by_rtm(rtm, file, fiscal_week_year, uploader, uploader_email):
    fiscal_obj = FiscalYearWeek.get_by_fiscal_week_year(fiscal_week_year)
    if fiscal_obj is None:
        raise ErrorExcept(ErrCode.DBQueryError, 'can not found fiscal day record')
    # file save
    path = os.path.dirname(os.path.dirname(os.path.abspath(__file__))) + f'/uploads/allocation/'
    if not os.path.exists(path):
        os.makedirs(path)
    file_md5 = md5(file.stream.read()).hexdigest()
    file.stream.seek(0)
    file.save(f'{path}/{file_md5}.xlsx')
    # 文件验证
    df = pd.read_excel(f'{path}/{file_md5}.xlsx')
    template_record = TblAllocationPrepareFile.find_by_phase_rtm_category(
        "3", rtm, PrepareCollectionAdjustmentFileCategory.Template, fiscal_obj.fiscal_week_year
    )
    if len(template_record) == 0:
        raise ErrorExcept(ErrCode.DBQueryError, 'template file has not generated')
    # Delta for adjustment in CW+X(X=1-4)列下的各行内容需除ODQ向下取整后再乘ODQ，计算后的数据覆盖原数据
    # 文件校验
    validate_with_template(file, rtm, fiscal_week_year, 'iPad')
    # save with tags file
    upload_records = TblAllocationPrepareFile.find_by_phase_rtm_category(
        "3", rtm, PrepareCollectionAdjustmentFileCategory.CPF, fiscal_obj.fiscal_week_year
    )
    if len(upload_records) == 0:
        upload_record = TblAllocationPrepareFile()
        upload_record.upload_file_version = 0
        upload_record.fiscal_qtr_week_name = fiscal_obj.fiscal_qtr_week_name
        upload_record.fiscal_week_year = fiscal_obj.fiscal_week_year
        upload_record.rtm = rtm
        upload_record.lob = "iPad"
        upload_record.operate_phase = "3"
        upload_record.category = PrepareCollectionAdjustmentFileCategory.CPF
    else:
        upload_record = upload_records[0]
        upload_record.upload_file_version += 1
    upload_record.upload_status = PrepareCollectionAdjustmentFileCategory.CPF
    file_name = f"{rtm}_Demand Adjustment_iPad_{fiscal_obj.fiscal_qtr_week_name}_{str(upload_record.upload_file_version+1)}.xlsx".replace('/','_')
    local_file_name = save_df_to_md5_name(df, path, 'xlsx')
    upload_record.upload_file_name = file_name
    upload_record.upload_file_path = f"/file/storage/{local_file_name}"
    upload_record.upload_by = uploader
    upload_record.uploader_email = uploader_email
    upload_record.upload_at = datetime.now()
    upload_record.update_by = uploader
    upload_record.save()
    # save to datasource
    delta_df = df.copy(True)
    
    # Delta for adjustment in CW+X(X=1-4)列下的各行内容需除ODQ向下取整后再乘ODQ，计算后的数据覆盖原数据
    delta_df = correct_delta_data(delta_df)

    # 兼容 Qty for CW 名称不一致
    df.columns = list(PrepareCollectionUploadsHeaderDict.keys())
    
    # 兼容表名不一致问题
    delta_df.columns = ['sold_to_name_en' if x == 'customer_name' else x for x in list(PrepareCollectionUploadsHeaderDict.values())]
    delta_df["fiscal_qtr_week_name"] = fiscal_obj.fiscal_qtr_week_name
    delta_df["fiscal_week_year"] = fiscal_obj.fiscal_week_year
    delta_df["rtm"] = rtm
    delta_df.replace({np.nan: None}, inplace=True)
    OdsFastCpfDemandAdjustUploadDelta.delete_by_week_rtm(fiscal_obj.fiscal_qtr_week_name, rtm)
    OdsFastCpfDemandAdjustUploadDelta.batch_save(delta_df.to_dict("records"))
    # save with tags file
    for i in range(1, 5):
        # 保存的数据需要加和之后除ODQ，向下取整后再乘ODQ
        df[f"Qty for CW+{i}"] = df[f"Delta for adjustment in CW+{i}"].add(df[f"Qty for CW+{i}"], fill_value=0)//df['ODQ']*df['ODQ']
        df[f"Reason for CW+{i}"] = df[f"Reason for adjustment in CW+{i}"]
    df = df[list(PrepareCollectionWithTagsHeaderDict.keys())]
    with_tags_records = TblAllocationPrepareFile.find_by_phase_rtm_category(
        "3", rtm, PrepareCollectionAdjustmentFileCategory.WithTags, fiscal_obj.fiscal_week_year
    )
    if len(with_tags_records) == 0:
        with_tags_records = TblAllocationPrepareFile()
        with_tags_records.upload_file_version = 0
        with_tags_records.fiscal_qtr_week_name = fiscal_obj.fiscal_qtr_week_name
        with_tags_records.fiscal_week_year = fiscal_obj.fiscal_week_year
        with_tags_records.rtm = rtm
        with_tags_records.lob = "iPad"
        with_tags_records.operate_phase = "3"
        with_tags_records.category = PrepareCollectionAdjustmentFileCategory.WithTags
    else:
        with_tags_records = with_tags_records[0]
        with_tags_records.upload_file_version += 1
    with_tags_records.upload_status = RTMSalesInputUploadStatus.Uploaded
    hr_name = f"{rtm}_Demand with Tag_iPad_{fiscal_obj.fiscal_qtr_week_name}_{str(upload_record.upload_file_version + 1)}.xlsx".replace('/','_')
    hr_local_file_name = save_df_to_md5_name(df, path, 'xlsx')
    with_tags_records.upload_file_name = hr_name
    with_tags_records.upload_file_path = f"/file/storage/{hr_local_file_name}"
    with_tags_records.upload_by = uploader
    with_tags_records.uploader_email = uploader_email
    with_tags_records.upload_at = datetime.now()
    with_tags_records.update_by = uploader
    with_tags_records.save()
    # save upload record
    upload_record = TblUploadFileRecord(upload_record.id, file_name, uploader)
    upload_record.save()
    # save to datasource
    # 兼容表名不一致问题
    df.columns = ['sold_to_name_en' if x == 'customer_name' else x for x in list(PrepareCollectionWithTagsHeaderDict.values())]
    df["fiscal_qtr_week_name"] = fiscal_obj.fiscal_qtr_week_name
    df["fiscal_week_year"] = fiscal_obj.fiscal_week_year
    df["week_begin_dt"] = fiscal_obj.week_begin_dt
    df["week_end_dt"] = fiscal_obj.week_end_dt
    df["rtm"] = rtm
    df.replace({np.nan: None}, inplace=True)
    OdsFastCpfDemandAdjustResult.delete_by_week_rtm(fiscal_obj.fiscal_qtr_week_name, rtm)
    OdsFastCpfDemandAdjustResult.batch_save(df.to_dict("records"))
    
    # 更改上传状态
    record = TblAllocationPrepare.find_by_fiscal_week_year_and_rtm(fiscal_week_year, rtm)
    # 如果cpf已经有审核过的数据，需要讲状态重置为0
    if record.is_third_phase_approved:
        record.is_third_phase_approved = 0
    record.third_phase_status = RTMDemandAdjustmentUploadStatus.Completed
    record.third_phase_cpf_uploaded += 1
    record.update_by= uploader
    record.save()
    return "ok"


def cpf_collection_adjustment_rerun(rtm, fiscal_week_year):
    fiscal_obj = FiscalYearWeek.get_by_fiscal_week_year(fiscal_week_year)
    if fiscal_obj is None:
        raise ErrorExcept(ErrCode.DBQueryError, 'can not found fiscal day record')
    record = TblAllocationPrepare.find_by_fiscal_week_year_and_rtm(fiscal_week_year, rtm)
    if record.third_phase_status in [
        RTMDemandAdjustmentUploadStatus.NotStared,
        RTMDemandAdjustmentUploadStatus.WaitingToUpload,
        RTMDemandAdjustmentUploadStatus.NeedToReupload,
        RTMDemandAdjustmentUploadStatus.NotUpload,
    ]:
        raise ErrorExcept(ErrCode.Param, "this rtm can not rerun")
    record.third_phase_status = RTMDemandAdjustmentUploadStatus.NeedToReupload
    record.third_phase_rerun += 1
    record.save()
    # 邮件提醒RTM重新提交
    current_time = datetime.now().strftime(DateTimeFormat)
    navigate_url = get_mail_navigate_url(
        record.id, record.rtm, record.fiscal_qtr_week_name,record.fiscal_week_year, RTMAllocationPhase.DemandAdjustment)
    TemplateEmail().adjustment_17(
        record.fiscal_qtr_week_name, record.rtm, current_time, navigate_url)
    
    # 删除CPF上传后保存的delta/result DB中内容
    OdsFastCpfDemandAdjustUploadDelta.delete_by_week_rtm(record.fiscal_qtr_week_name, rtm)
    OdsFastCpfDemandAdjustResult.delete_by_week_rtm(record.fiscal_qtr_week_name, rtm)
    
    return "ok"


def get_demand_collection_list(fiscal_week_year, module):
    if module == str(CPFAllocationPhaseOneModule.Adjustment):
        return get_collection_adjustment_list(fiscal_week_year)
    else:
        return get_collection_overview_list(fiscal_week_year)
        

def get_collection_adjustment_list(fiscal_week_year):
    records = TblAllocationPrepare.find_by_fiscal_week_year(fiscal_week_year)
    files = TblAllocationPrepareFile.find_by_phase(CPFAllocationPhaseOneModule.Adjustment, fiscal_week_year)
    data = []
    for rtm in AllocationRTMList:
        try:
            record = filter(lambda x: x.rtm == rtm, records).__next__()
            data.append(get_data_list(record, files, lambda x: x.rtm == rtm and x.category != PrepareCollectionAdjustmentFileCategory.CPF))
        except StopIteration:
            data.append({
                "rtm": rtm
            })
    return {"list": data, "now": get_current_time_with_mock(), 
            "is_history_week": is_history_week_in_allocation(fiscal_week_year)}


def get_collection_overview_list(fiscal_week_year):
    records = TblAllocationPrepare.find_by_fiscal_week_year(fiscal_week_year)
    third_phase_withtags = TblAllocationPrepareFile.find_by_week_phase_category(
        fiscal_week_year, CPFAllocationPhaseOneModule.Adjustment, PrepareCollectionAdjustmentFileCategory.WithTags)
    second_phase_withtags = TblAllocationPrepareFile.find_by_week_phase_category(
        fiscal_week_year, CPFAllocationPhaseOneModule.SellinDemand, PrepareSubmissionFileCategory.WithTags)
    data = []
    for rtm in AllocationRTMList:
        try:
            record = filter(lambda x: x.rtm == rtm, records).__next__()
            file_list = []
            if record.third_phase_status == RTMDemandAdjustmentUploadStatus.Completed and not record.cpf_confirmed:
                # 返回表8数据
                file_list = third_phase_withtags
            else:
                # 返回初始版数据
                file_list = second_phase_withtags
            data.append(get_data_list(record, file_list, lambda x: x.rtm == rtm))
        except StopIteration:
            data.append({
                "rtm": rtm
            })
    return {"list": data, "now":get_current_time_with_mock(),
            "is_history_week": is_history_week_in_allocation(fiscal_week_year)}


def get_data_list(record, file_list, file_lambda):
    file = []
    for f in filter(file_lambda, file_list):
        file.append({
            "id": f.id,
            "phase": f.operate_phase,
            "file_name": f.upload_file_name,
            "file_path": f.upload_file_path,
            "upload_version": f.upload_file_version,
            "category": f.category,
            "upload_by": f.upload_by,
            "upload_at": f.upload_at and f.upload_at.strftime(DateTimeFormat),
            "update_by": f.update_by,
        })
    return {
        "id": record.id,
        "rtm": record.rtm,
        "status": record.third_phase_status,
        "rerun": record.third_phase_rerun,
        "upload_version": record.third_phase_upload_version,
        "cpf_approved": record.is_third_phase_approved,
        "fiscal_week_year": record.fiscal_week_year,
        "fiscal_qtr_week_name": record.fiscal_qtr_week_name,
        "cpf_uploaded": record.third_phase_cpf_uploaded,
        "cpf_confirmed": record.cpf_confirmed,
        "update_by": record.update_by,
        "create_time": record.create_time.strftime(DateTimeFormat),
        "update_time": record.update_time.strftime(DateTimeFormat),
        "file_list": file,
    }


def adjustment_approve_service(rtm, fiscal_week_year, uploader, uploader_email):
    # 获取rtm上传的文件路径
    rtm_record = TblAllocationPrepare.find_by_fiscal_week_year_and_rtm(fiscal_week_year, rtm)
    
    rtm_file_list = TblAllocationPrepareFile.find_by_phase_rtm_category(str(RTMAllocationPhase.DemandAdjustment),
                                                                          rtm, PrepareCollectionAdjustmentFileCategory.RTM,
                                                                          fiscal_week_year)
    if len(rtm_file_list) == 0:
        raise ErrorExcept(ErrCode.DBQueryNoData, f'{rtm} can not found file rtm uploaded.')
    file_path = rtm_file_list[0].upload_file_path
    file_name = rtm_file_list[0].upload_file_name
        
    # 读取文件内容
    rtm_upload_df = pd.read_excel(transfer_file_path(file_path))
    # 字段名称转换
    delta_df = rtm_upload_df.copy(True)
    
    # Delta for adjustment in CW+X(X=1-4)列下的各行内容需除ODQ向下取整后再乘ODQ，计算后的数据覆盖原数据
    delta_df = correct_delta_data(delta_df)

    # 兼容 Qty for CW 名称不一致
    rtm_upload_df.columns = list(PrepareCollectionUploadsHeaderDict.keys())
    
    # 兼容表名不一致问题
    delta_df.columns = ['sold_to_name_en' if x == 'customer_name' else x for x in list(PrepareCollectionUploadsHeaderDict.values())]
    delta_df["fiscal_qtr_week_name"] = rtm_record.fiscal_qtr_week_name
    delta_df["fiscal_week_year"] = rtm_record.fiscal_week_year
    delta_df["rtm"] = rtm
    delta_df["version"] = 1
    delta_df.replace({np.nan: None}, inplace=True)
    OdsFastCpfDemandAdjustUploadDelta.delete_by_week_rtm(rtm_record.fiscal_qtr_week_name, rtm)
    OdsFastCpfDemandAdjustUploadDelta.batch_save(delta_df.to_dict("records"))
    # save with tags file
    for i in range(1, 5):
        # 保存的数据需要加和之后除ODQ，向下取整后再乘ODQ
        rtm_upload_df[f"Qty for CW+{i}"] = rtm_upload_df[f"Delta for adjustment in CW+{i}"].add(rtm_upload_df[f"Qty for CW+{i}"], fill_value=0)//rtm_upload_df['ODQ']*rtm_upload_df['ODQ']
        rtm_upload_df[f"Reason for CW+{i}"] = rtm_upload_df[f"Reason for adjustment in CW+{i}"]
    rtm_upload_df = rtm_upload_df[list(PrepareCollectionWithTagsHeaderDict.keys())]
    with_tags_records = TblAllocationPrepareFile.find_by_phase_rtm_category(
        "3", rtm, PrepareCollectionAdjustmentFileCategory.WithTags, rtm_record.fiscal_week_year
    )
    if len(with_tags_records) == 0:
        with_tags_records = TblAllocationPrepareFile()
        with_tags_records.upload_file_version = 0
        with_tags_records.fiscal_qtr_week_name = rtm_record.fiscal_qtr_week_name
        with_tags_records.fiscal_week_year = rtm_record.fiscal_week_year
        with_tags_records.rtm = rtm
        with_tags_records.lob = "iPad"
        with_tags_records.operate_phase = "3"
        with_tags_records.category = PrepareCollectionAdjustmentFileCategory.WithTags
    else:
        with_tags_records = with_tags_records[0]
        with_tags_records.upload_file_version += 1
    with_tags_records.upload_status = RTMSalesInputUploadStatus.Uploaded
    hr_name = f"{rtm}_Demand with Tag_iPad_{rtm_record.fiscal_qtr_week_name}_{str(rtm_file_list[0].upload_file_version + 1)}.xlsx".replace('/','_')
    path = os.path.dirname(os.path.dirname(os.path.abspath(__file__))) + f'/uploads/allocation/'
    hr_local_file_name = save_df_to_md5_name(rtm_upload_df, path, 'xlsx')
    with_tags_records.upload_file_name = hr_name
    with_tags_records.upload_file_path = f"/file/storage/{hr_local_file_name}"
    with_tags_records.upload_by = uploader
    with_tags_records.uploader_email = uploader_email
    with_tags_records.upload_at = datetime.now()
    with_tags_records.update_by = uploader
    with_tags_records.save()
    
    # save to datasource
    fiscal_obj = FiscalYearWeek.get_by_fiscal_week_year(fiscal_week_year)
    # 兼容表名不一致问题
    rtm_upload_df.columns = ['sold_to_name_en' if x == 'customer_name' else x for x in list(PrepareCollectionWithTagsHeaderDict.values())]
    rtm_upload_df["fiscal_qtr_week_name"] = rtm_record.fiscal_qtr_week_name
    rtm_upload_df["fiscal_week_year"] = rtm_record.fiscal_week_year
    rtm_upload_df["week_begin_dt"] = fiscal_obj.week_begin_dt
    rtm_upload_df["week_end_dt"] = fiscal_obj.week_end_dt
    rtm_upload_df["rtm"] = rtm
    rtm_upload_df["version"] = 1
    rtm_upload_df.replace({np.nan: None}, inplace=True)
    OdsFastCpfDemandAdjustResult.delete_by_week_rtm(rtm_record.fiscal_qtr_week_name, rtm)
    OdsFastCpfDemandAdjustResult.batch_save(rtm_upload_df.to_dict("records"))
    
    # 更新rtm记录
    rtm_record.third_phase_status = RTMDemandAdjustmentUploadStatus.Completed
    rtm_record.is_third_phase_approved = 1
    rtm_record.update_by = uploader
    rtm_record.save()
    
    # 添加操作记录
    insert_operate_record(rtm, AllocationOperateCategory.AdjustmentApprove, file_name, file_path, uploader, uploader_email)

