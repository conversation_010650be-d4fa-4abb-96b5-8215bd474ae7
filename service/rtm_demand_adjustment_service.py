from data.allocation_prepare_data import *
from data.allocation_prepare_gc_dmp_data import AppFastTaskStatusWi
from data.allocation_prepare_gc_dmp_data import AppFastDemandRtmSalesTemplateWi
from data.allocation_prepare_gc_dmp_data import AppFastAllocationDemandAdjustmentTemplateWi
from data.allocation_prepare_data import TblAllocationPrepare
from util.util import compare_datetime
from util.file_util import get_absolute_path, hash_file_md5
from openpyxl.worksheet.datavalidation import DataValidation
from util.rtm_demand_adjustment_const import *
from data.cpf_sell_in_demand_data import *
from util.file_util import *
from util.template_email_sender import TemplateEmail
from util.cpf_util import get_navigate_url_list_page, get_cpf_mail_navigate_url
from data.allocation_cpf_lob_data import TblAllocationCpfLob


def check_wednesday_esr_service(fiscal_week_year: int):
    whether_ready = AppFastTaskStatusWi.check_wednesday_esr_status(
        fiscal_week_year)
    if not whether_ready:
        raise ErrorExcept(ErrCode.ValidationError, UnreadyDataError)
    return True


def get_demand_adjustment_template_file_service(rtm: str, fiscal_week_year: int, fiscal_qtr_week_name: str, lob: str):
    file_path, file_name, _ = get_template_file(rtm, fiscal_week_year, fiscal_qtr_week_name, lob)
    return file_path, file_name


def get_template_file(rtm: str, fiscal_week_year: int, fiscal_qtr_week_name: str, lob: str):
    '''
    从数据库中获取模版原始数据
    '''
    # 从数据库获取模版数据
    template_content_raw = AppFastAllocationDemandAdjustmentTemplateWi.get_template_by_week_rtm_lob(
        rtm, fiscal_week_year, lob)

    raw_header = ['customer_name' if x == 'sold_to_name_en' else x for x in AdjustmentTemplateFileRawHeader]
    # 取指定的列
    template_content = template_content_raw[raw_header]

    # 更新列名
    template_content.columns = AdjustmentTemplateFileHeader

    # 保存 Excel 文件
    file_path = os.path.dirname(os.path.dirname(
        os.path.abspath(__file__))) + f"/templates/"
    if not os.path.exists(file_path):
        os.mkdir(file_path)
    file_name = f"{rtm}_Demand Adjustment_Template_{fiscal_qtr_week_name}.xlsx".replace('/', '_')
    add_dropdown_and_save(file_path+file_name, template_content)
    hash_file_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__))) + f"/uploads/allocation/"
    hash_file_name = hash_file_md5_by_path(file_path+file_name, 'xlsx')
    shutil.copy(file_path+file_name, hash_file_path+hash_file_name)
    current_time = datetime.now().strftime(DateTimeFormat)
    # 存储模版文件信息
    upload_file_mapping = {
        'fiscal_qtr_week_name': fiscal_qtr_week_name,
        'fiscal_week_year': fiscal_week_year,
        'rtm': rtm,
        'lob': lob,
        "operate_phase": str(RTMAllocationPhase.DemandAdjustment),
        'upload_by': "system",
        'upload_file_name': file_name,
        'upload_file_version': 0,
        'upload_file_path': f"/file/storage/{hash_file_name}",
        'update_by': "system",
        'category': ThirdPhaseUploadFileCategory.Template,
        'upload_at': current_time,
        'create_time': current_time,
        'update_time': current_time
    }
    TblAllocationPrepareFile.insert_and_update_unique(upload_file_mapping)

    return file_path, file_name, template_content_raw


def add_dropdown_and_save(absolute_file_path: str, content: pd.DataFrame):
    with pd.ExcelWriter(absolute_file_path, engine='openpyxl') as writer:
        # 写入数据
        content.to_excel(writer, sheet_name='Sheet1', index=False)
        worksheet = writer.sheets['Sheet1']
        # 添加下拉列表验证
        for idx, value in enumerate(content['Priority']):
            if value == 'P0':
                add_validation_to_cell(worksheet, P0ReasonValidate, idx)
            elif value == 'P1':
                add_validation_to_cell(worksheet, P1ReasonValidate, idx)
            elif value == 'P2':
                add_validation_to_cell(worksheet, P2ReasonValidate, idx)


def add_validation_to_cell(worksheet, source, current_index, start_index=2):
    dv = DataValidation(type='list', formula1=f"\"{','.join(source)}\"", allow_blank=True)
    # 对应Reason for adjustment in CW+X，X=1,2,3,4
    columns = ['AF', 'AH', 'AJ', 'AL']
    for column in columns:
        dv.add(column + str(current_index + start_index))
    worksheet.add_data_validation(dv)

# demand_adjustment 上传文件校验规则


def validate_with_template(upload_file, rtm: str, fiscal_week_year: int, lob: str):
    tempate_file = TblAllocationPrepareFile.get_allocation_prepare_file(
        fiscal_week_year, RTMAllocationPhase.DemandAdjustment, lob, ThirdPhaseUploadFileCategory.Template, rtm)
    if not tempate_file:
        raise ErrorExcept(ErrCode.FileUploadError,
                          f"{rtm} no template file in third phase.")
    template_df = pd.read_excel(transfer_file_path(tempate_file.upload_file_path))
    upload_df = pd.read_excel(upload_file)

    # 规则校验0: 表头校验, 必须与模版文件的表头一致
    if not list(upload_df.columns) == list(template_df.columns):
        raise ErrorExcept(ErrCode.FileUploadError,
                          ErrorMessage.InconsistentHeader)

    # 校验规则1: 上传文件的Sales Org列至Comments列下的各行的行数和内容需要表4（demand with tag数据）保持一致
    # 查询 rtm/week demand with tag 数据，且全部相同
    # demand_with_tag = pd.DataFrame()
    # if rtm == AllocationRTM.Mono:
    #     demand_with_tag = AppFastAllocationDemandSellInMonoWi.get_demand_with_tag_data(
    #         fiscal_week_year, rtm)
    # elif rtm == AllocationRTM.Multi or rtm == AllocationRTM.Online:
    #     demand_with_tag = AppFastAllocationDemandSellInMultiOnlineWi.get_demand_with_tag_data(
    #         fiscal_week_year, rtm)
    # else:
    #     demand_with_tag = AppFastAllocationDemandSellInOtherWi.get_demand_with_tag_data(
    #         fiscal_week_year, rtm)

    # if not demand_with_tag.empty:
    #     # 数据表字段不一致，需要特殊处理
    #     compare_raw_header = ['shipment_plan_cw' if x == 'cw_shipment_plan' else x for x in CompareDataRawHeader]
    #     df_demand_with_tag = demand_with_tag[compare_raw_header]
    #     df_demand_with_tag.columns = CompareDataHeader
    #     df_upload_file = upload_df[CompareDataHeader]
        
    #     if len(df_upload_file) != len(df_demand_with_tag):
    #         raise ErrorExcept(ErrCode.FileUploadError,
    #                           ErrorMessage.InconsistentData)
            
    #     df_demand_with_tag = df_demand_with_tag.fillna(value=np.nan)
    #     df_merged = pd.merge(df_upload_file, df_demand_with_tag,
    #                         how='outer', indicator=True)
    #     if list(df_merged['_merge'].unique()) != ['both']:
    #         raise ErrorExcept(ErrCode.FileUploadError,
    #                           ErrorMessage.InconsistentData)
    # 该规则修改为-上传文件的Sales Org列至Comments列下的各行的行数和内容需要模版保持一致
    if len(upload_df) != len(template_df) or \
        not upload_df[CompareDataHeader].equals(template_df[CompareDataHeader]):
        raise ErrorExcept(ErrCode.FileUploadError,
                            ErrorMessage.InconsistentData)

    # 不允许上传没有任何修改的模版表（模版表下载后直接上传）
    # 上传文件的后面9列内容中不能全是空
    compare_delta = AdjustmentTemplateFileHeader[-9:]
    if upload_df[compare_delta].isnull().all().all():
        raise ErrorExcept(ErrCode.FileUploadError,
                            FileUploadError.RTMUploadEmptyError)

    # 校验规则2-1: Delta for adjustment in CW+X(X=1-4)列下的各行内容不能含有“+”
    invalid_plus_sign_rows = []
    for i in range(1, 5):
        delta_col = f"Delta for adjustment in CW+{i}"
        for row_idx, row in upload_df.iterrows():
            if row[delta_col] and "+" in str(row[delta_col]):
                invalid_plus_sign_rows.append(row_idx+2)
    if len(invalid_plus_sign_rows):
        raise ErrorExcept(ErrCode.FileUploadError,
                          f"{ErrorMessage.InvalidRows} {','.join(map(str, invalid_plus_sign_rows))}")

    # 接下来的校验，需要先合并表，来判断high runner or low runner
    template_raw_data = AppFastAllocationDemandAdjustmentTemplateWi.get_template_by_week_rtm_lob(
        rtm, fiscal_week_year, lob)
    hr_lr_df = template_raw_data[HRLRMergeRawHeader]
    hr_lr_df.columns = HRLRMergeHeader
    merged_df = pd.merge(upload_df, hr_lr_df, on=HRLRMergeHeader[:3])

    # 并且重新计算delta
    merged_df = correct_delta_data(merged_df)
    # 校验规则2-2: 对于Mono/Multi/Online对于HR MPN：by Sold-to，by MPN的数据行P0, P1的值总和
    # （“Delta for adjustment in CW+X(X=1-4)列下的by Sold-to，by MPN的数据行P0, P1行的数值”
    # 与 “Qty for CW+X(X=1-4)列下的by Sold-to，by MPN的数据行P0, P1行的数值” 进行求和）
    # 不能大于 Refreshed Open Backlog over Published for CW+3 SP 的值
    # 且不能小于0
    if rtm == AllocationRTM.Mono or rtm == AllocationRTM.Multi or rtm == AllocationRTM.Online:
        hr_df = merged_df[(merged_df["HR_LR"] == "HR") & ((
            merged_df["Priority"] == "P0") | (merged_df["Priority"] == "P1"))]
        invalid_rows = sum_multiple_cells_for_validation(hr_df)
        if len(invalid_rows):
            invalid_index = get_upload_file_group_invalid_index(merged_df, invalid_rows)
            raise ErrorExcept(
                ErrCode.FileUploadError, f"{ErrorMessage.InvalidRows} {','.join(invalid_index)}")

    # 校验规则2-3: 对于Mono/Multi/Online LR MPN 和 Other RTM的所有MPN：by Sold-to，by MPN的数据行P0,P1,P2的值总和
    # （“Delta for adjustment in CW+X(X=1-4)列下的by Sold-to，by MPN的数据行P0,P1,P2行的数值”
    # 与 “Qty for CW+X(X=1-4)列下的by Sold-to，by MPN的数据行P0,P1,P2行的数值” 进行求和）
    # 不能大于 Refreshed Open Backlog over Published for CW+3 SP 的值
    # 且不能小于0
    if rtm == AllocationRTM.Mono or rtm == AllocationRTM.Multi or rtm == AllocationRTM.Online:
        lr_df = merged_df[merged_df["HR_LR"] == "LR"]
        invalid_rows = sum_multiple_cells_for_validation(lr_df)
        if len(invalid_rows):
            invalid_index = get_upload_file_group_invalid_index(merged_df, invalid_rows)
            raise ErrorExcept(
                ErrCode.FileUploadError, f"{ErrorMessage.InvalidRows} {','.join(invalid_index)}")
    else:
        invalid_rows = sum_multiple_cells_for_validation(merged_df)
        if len(invalid_rows):
            invalid_index = get_upload_file_group_invalid_index(merged_df, invalid_rows)
            raise ErrorExcept(
                ErrCode.FileUploadError, f"{ErrorMessage.InvalidRows} {','.join(invalid_index)}")


def sum_multiple_cells_for_validation(df):
    data_grouped = df.groupby(
        ['Customer Sold-to ID', 'MPN / Apple Part #'])
    invalid_rows = []
    for name, group in data_grouped:
        upload_row = df[(df['Customer Sold-to ID'] == name[0])
                        & (df['MPN / Apple Part #'] == name[1])]
        total_qty_plus_delta = group['Qty for CW+1'].sum() \
            + group['Qty for CW+2'].sum() \
            + group['Qty for CW+3'].sum() \
            + group['Qty for CW+4'].sum() \
            + group['Delta for adjustment in CW+1'].sum() \
            + group['Delta for adjustment in CW+2'].sum() \
            + group['Delta for adjustment in CW+3'].sum() \
            + group['Delta for adjustment in CW+4'].sum()
        if not (total_qty_plus_delta <= upload_row['Open Backlog over Published for CW+3 SP'].iloc[0] and total_qty_plus_delta >= 0):
            for priority in group['Priority']:
                invalid_rows.append((name[0],name[1],priority))
    return invalid_rows


def get_upload_file_group_invalid_index(df, invalid_rows):
    # 根据'Customer Sold-to ID', 'MPN / Apple Part #', 'Priority'转换行号
    invalid_index = []
    for item in invalid_rows:
        if len(item) < 3:
            continue
        invalid_index += df[(df['Customer Sold-to ID'] == item[0])
                            & (df['MPN / Apple Part #'] == item[1])
                            & (df['Priority'] == item[2])
                            ].index.tolist()
    return list(map(lambda x: str(x + 2), invalid_index))


def rtm_update_file_info(id, update_file_mapping: dict):
    TblAllocationPrepare.update_data_by_id(id, update_file_mapping)


def upload_file_by_rtm_service(file, uploader: str, uploader_email: str, id: int, lob: str):

    # 查询当前记录
    ap = TblAllocationPrepare.get_detail(id)

    if ap["third_phase_status"] == RTMDemandAdjustmentUploadStatus.Completed:
        raise ErrorExcept(ErrCode.FileUploadError, ErrorMessage.UploadConflict)

    # 与模版文件对比，校验文件
    validate_with_template(file, ap[RtmKey], ap[StrFiscalWeekYear],lob)

    file_md5 = hash_file_md5(file)
    absolute_path = get_absolute_path('/uploads/allocation/')

    upload_file_df = pd.read_excel(file)
    upload_file_df = correct_delta_data(upload_file_df)
    add_dropdown_and_save(f"{absolute_path}{file_md5}.xlsx", upload_file_df)

    # 保存的文件信息
    current_time = datetime.now().strftime(DateTimeFormat)
    file_version = ap['third_phase_upload_version'] + 1
    upload_file_name = f"{ap[RtmKey]}_Demand_Adjustment_{ap['lob']}_{ap[StrFiscalQtrWeekName]}_{file_version}.xlsx"
    upload_file_mapping = {
        'fiscal_qtr_week_name': ap[StrFiscalQtrWeekName],
        'fiscal_week_year': ap[StrFiscalWeekYear],
        'rtm': ap[RtmKey],
        'lob': ap['lob'],
        "operate_phase": str(RTMAllocationPhase.DemandAdjustment),
        'upload_by': uploader,
        'upload_file_name': upload_file_name,
        'upload_file_version': file_version,
        'upload_file_path': f"/file/storage/{file_md5}.xlsx",
        'uploader_email': uploader_email,
        'update_by': uploader,
        'category': ThirdPhaseUploadFileCategory.RTM,
        'upload_at': current_time,
        'create_time': current_time,
        'update_time': current_time
    }
    TblAllocationPrepareFile.insert_and_update_unique(upload_file_mapping)
    update_main_table_mapping = {
        'third_phase_upload_version': file_version,
        'update_time': current_time,
        'update_by': uploader
    }
    # 更新文件上传状态
    if ap['third_phase_status'] != RTMSalesInputUploadStatus.Uploaded:
        update_main_table_mapping['third_phase_status'] = RTMSalesInputUploadStatus.Uploaded

    # 校验成功后，更新主表信息
    rtm_update_file_info(id, update_main_table_mapping)

    # 留存上传记录
    upload_record = TblUploadFileRecord(id, upload_file_name, uploader)
    TblUploadFileRecord.save(upload_record)

    # 发送邮件提醒CP&F进行审核
    cpf_recored = TblAllocationCpfLob.get_by_week_lob(ap[StrFiscalWeekYear], 'iPad')
    
    if len(cpf_recored) > 0:
        navigate_url = get_cpf_mail_navigate_url(
            cpf_recored[0].id, ap[StrFiscalQtrWeekName], ap[StrFiscalWeekYear], 'iPad')
    else:
        navigate_url = get_navigate_url_list_page('cpf')
    TemplateEmail().adjustment_16(
        ap[StrFiscalQtrWeekName], ap[RtmKey], uploader, current_time, navigate_url)

    return {"file_path": f"/file/storage/{file_md5}.xlsx"}

# 上传后校验前计算逻辑


def correct_delta_data(df):
    # 将用户填入的Delta for adjustment 数除以ODQ，然后向下取整，再乘以ODQ
    correct_rows = []
    for _, row in df.iterrows():
        odq = row['ODQ']
        for num in range(1, 5):
            delta = row[f'Delta for adjustment in CW+{num}']
            if not pd.isna(delta):
                if delta > 0:
                    row[f'Delta for adjustment in CW+{num}'] = delta // odq * odq
                elif delta < 0:
                    row[f'Delta for adjustment in CW+{num}'] = - \
                        ((-delta) // odq * odq)
        correct_rows.append(row)
    return pd.DataFrame(correct_rows)


def transfer_file_path(file_path: str):
    split_path = file_path.split('/file/storage/')
    if len(split_path) > 1:
        absolute_path = get_absolute_path('/uploads/allocation/')
        return f"{absolute_path}{split_path[1]}"
    else:
        return ""
