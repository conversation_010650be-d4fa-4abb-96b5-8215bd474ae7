import pandas as pd
from util.const import TemplateFileHeader

# 增加操作记录的函数, 可在任意需要添加的位置调用
# 设置记录的表结构
def operate_record():
    '''
    操作记录, 记录的内容
        1. 用户名称(昵称 + 姓氏)
        2. 操作成功时间
        3. 操作的文件名称
        4. 操作类型: a,b,c
        5. 操作的是哪个团队
    '''
    return



def upload_file_record():
    '''
    上传文件
        1. 用户名(Nick Name + Last Name)
        2. 上传成功时间
        3. 上传的后的文件名
    '''
    return

def validate_allocation_prepare_upload_file(upload_file, template_file_path: str):

    # 读取模板文件
    template_df = pd.read_excel(template_file_path)

    # 读取上传文件
    data_df = pd.read_excel(upload_file)
    
    # 规则校验0: 表头校验, 必须与模版文件的表头一致
    if len(data_df) >= 1 and all(data_df.columns == TemplateFileHeader):
        print("The header in the file is invalid. The header should be consistent with the header of the template")

    # 校验规则1: 不允许上传空表, 除表头外无数据

    if len(data_df) == 1 and all(data_df.columns == TemplateFileHeader):
        print('Please fill in data.')
    else:
        # 校验规则2：每行数据的 Sales Org, RTML4, LOB/FPH L1, Prod/FPH L3, Project Code, Nand, Color, MPN/ Apple Part #, Customer Sold-to, Customer Name, Open Backlog over Published for CW+3 SP, Priority 列数据应该包含在生成的Template表中（数据行只能少不能新增，且不能重复）
        template_columns = set(template_df.columns)
        data_columns = set(data_df.columns)
        if not template_columns.issubset(data_columns):
            invalid_columns = template_columns.difference(data_columns)
            print('The columns are invalid:', ', '.join(invalid_columns))

        # 将数据按照 Sold-to 和 MPN 分组
        data_grouped = data_df.groupby(['Customer Sold-to', 'MPN/ Apple Part #'])

        for name, group in data_grouped:
            # 校验规则3：by Sold-to, by MPN的数据行的Open Backlog over Published for CW+3 SP必须等于Template中的值
            template_row = template_df[(template_df['Customer Sold-to'] == name[0]) & (template_df['MPN/ Apple Part #'] == name[1])]
            if template_row.empty:
                print(f'The rows data is invalid: {", ".join(str(i) for i in group.index)}')
            elif not group['Open Backlog over Published for CW+3 SP'].equals(template_row['Open Backlog over Published for CW+3 SP']):
                print(f'The rows Open Backlog over Published for CW+3 SP is invalid: {", ".join(str(i) for i in group.index)}')

            # 校验规则4：by Sold-to，by MPN数据行第 Sales input Qty for CW+1， Sales input Qty for CW+2， Sales input Qty for CW+3， Sales input Qty for CW+4列的值加和不能大于Open Backlog over Published for CW+3 SP的值
            total_sales_input_qty = group['Sales input Qty for CW+1'].sum() + group['Sales input Qty for CW+2'].sum() + group['Sales input Qty for CW+3'].sum() + group['Sales input Qty for CW+4'].sum()
            if total_sales_input_qty > template_row['Open Backlog over Published for CW+3 SP'].iloc[0]:
                print(f'The total Sales Input Qty is too large: {total_sales_input_qty} & {name[1]}, {name[0]}')

        # 校验规则5：每行数据的Comment列不能为为空
        blank_comment_rows = data_df[data_df['Comment'].isnull()].index
        if len(blank_comment_rows) > 0:
            print('Please fill in the blank Comment column of the rows:', ', '.join(str(i) for i in blank_comment_rows))
