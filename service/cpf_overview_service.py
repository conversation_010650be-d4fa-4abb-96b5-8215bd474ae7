from data.allocation_prepare_data import *
from util.rtm_demand_adjustment_const import *
from data.allocation_demand_collection_overview_data import *
from data.cpf_sell_in_demand_data import AppFastAllocationDemandSellInMonoWi,\
    AppFastAllocationDemandSellInMultiOnlineWi,\
    AppFastAllocationDemandSellInOtherWi
from service.allocation_prepare_service import transfer_file_path
from util.file_util import get_absolute_path
from data.cpf_data_source import *
from data.allocation_esr_data import *
from service.allocation_collection_adjustment_service import get_collection_overview_list
from util.template_email_sender import TemplateEmail
from data.allocation_cpf_lob_data import TblAllocationCpfLob
from data.allocation_cpf_run_data import AllocationRun, AllocationRunSupplyAcquisition

IntConfirmed = 1


def confirm_all_rtm_service(fiscal_week_year: int, operator: str):
    # # 修改所有rtm的adjustment阶段状态
    query_all = TblAllocationPrepare.find_by_fiscal_week_year(fiscal_week_year)
    fiscal_qtr_week_name = ""
    merge_result = pd.DataFrame()
    diff_header = ["shipment_plan_cw" if x == "cw_shipment_plan" else x for x in TemplateFileRawHeader]
    current_time = datetime.now().strftime(DateTimeFormat)
    for item in query_all:
        if not fiscal_qtr_week_name:
            fiscal_qtr_week_name = item.fiscal_qtr_week_name
        update_data = {"cpf_confirmed": IntConfirmed}
        if item.third_phase_status == RTMDemandAdjustmentUploadStatus.WaitingToUpload or item.third_phase_status == RTMDemandAdjustmentUploadStatus.NeedToReupload:
            update_data["third_phase_status"] = RTMDemandAdjustmentUploadStatus.NotUpload
            TblAllocationPrepare.update_data_by_id(item.id, update_data)

        if item.third_phase_status == RTMDemandAdjustmentUploadStatus.Completed:
            ret = DemandWithTagsByUpload.get_demand_with_tag_data(
                fiscal_week_year, item.rtm)
            ret = ret[TemplateFileRawHeader]
        else:
            if item.rtm == AllocationRTM.Mono:
                ret = AppFastAllocationDemandSellInMonoWi.get_demand_with_tag_data(
                    fiscal_week_year, item.rtm)
            elif item.rtm == AllocationRTM.Multi or item.rtm == AllocationRTM.Online:
                ret = AppFastAllocationDemandSellInMultiOnlineWi.get_demand_with_tag_data(
                    fiscal_week_year, item.rtm)
            else:
                ret = AppFastAllocationDemandSellInOtherWi.get_demand_with_tag_data(
                    fiscal_week_year, item.rtm)
            ret = ret[diff_header]
            ret.columns = TemplateFileRawHeader
        ret["fiscal_qtr_week_name"] = item.fiscal_qtr_week_name
        ret["fiscal_week_year"] = item.fiscal_week_year
        ret["rtm"] = item.rtm
        ret.replace({np.nan: None}, inplace=True)
        merge_result = pd.concat([merge_result, ret], axis=0, ignore_index=True)
        # 邮件通知RTM
        TemplateEmail().overview_18(item.fiscal_qtr_week_name, item.rtm, current_time)
    # 保存所有的数据
    if len(merge_result):
        OdsFastCpfDemandCollectionConfirmResult.delete_by_week(fiscal_week_year)
        OdsFastCpfDemandCollectionConfirmResult.bulk_save(merge_result.to_dict("records"))
    else:
        logger.info(f"current week has no data, please check it.")
    
    # confirm后开启第二阶段
    cpf_record = TblAllocationCpfLob.get_by_week_lob(fiscal_week_year, 'iPad')
    if cpf_record and cpf_record[0].phase == CPFAllcationPhase.DemandCollection:
        cpf_lob_data = {
            'last_operator': operator,
            'last_updated': current_time,
            'phase': CPFAllcationPhase.AllocationRun
        }
        TblAllocationCpfLob.update_by_fiscal_week_year(fiscal_week_year, 'iPad', cpf_lob_data)
        logger.info("start second phase.")
    
        # 生成allocation run记录
        ar = AllocationRun(fiscal_qtr_week_name, fiscal_week_year, 'iPad',
                           AllocationRunProcessType.Automatic,
                           AllocationRunStep.SupplyAcquisition)
        ar_id = ar.save()
        logger.info(f"generate allocation run record {ar_id}")
        
        # 生成supply acquisition记录
        supple_acquisition = AllocationRunSupplyAcquisition(
            fiscal_qtr_week_name, fiscal_week_year
        )
        supple_acquisition_id = supple_acquisition.save()
        logger.info(f"generate supply acquisition record {supple_acquisition_id}")
    
    return 'ok'


def download_all_demand_with_tags_service(fiscal_week_year: int):
    '''
    下载最终版本的demand数据, 返回合并后的文件地址
    '''
    all_df = pd.DataFrame(columns=PrepareCollectionWithTagsHeaderDict.keys())
    
    overview_list = get_collection_overview_list(fiscal_week_year)
    
    fiscal_obj = FiscalYearWeek.get_by_fiscal_week_year(fiscal_week_year)
    if fiscal_obj is None:
        raise ErrorExcept(ErrCode.DBQueryError, 'can not found fiscal day record')
    
    file_data = overview_list.get('list')
    fiscal_qtr_week_name = fiscal_obj.fiscal_qtr_week_name
    
    # 进行排序，rtm 按照列表页顺序, MPN字符正序，Sold-to-ID正序，Priority [P0, P1, P2]
    priority_ordered_list = ['P0', 'P1', 'P2']
    for rtm in AllocationRTMList:
        record = filter(lambda x: x['rtm'] == rtm, file_data).__next__()
        file_list = record.get("file_list")
        if file_list and file_list[0]["file_path"]:
            df_file = pd.read_excel(transfer_file_path(file_list[0]["file_path"]))
            df_file['Priority'] = pd.Categorical(df_file['Priority'], categories=priority_ordered_list, ordered=True)
            df_file.sort_values(['MPN / Apple Part #', 'Customer Sold-to ID', 'Priority'], ascending=[True, True, True], inplace=True)
            all_df = pd.concat([all_df, df_file], axis=0, ignore_index=True)
    
    file_name = f"Overview_Demand_iPad_{fiscal_qtr_week_name}.xlsx"
    merged_file_path = get_absolute_path('/uploads/allocation/') + file_name
    with pd.ExcelWriter(merged_file_path, engine='openpyxl') as writer:
        all_df.to_excel(writer, index=False)

    return merged_file_path, file_name


def get_overview_data_source_by_week(fiscal_week_year: int):
    # active sku ipad snapshot
    sku_query = OdsFastCPFActiveSKUiPadSnapshot.query_data_by_week(fiscal_week_year)
    sku_data = [{
        "name": x.name,
        "last_upload_date": x.last_upload_date and x.last_upload_date.strftime(DateTimeFormat),
        "uploader_name": x.uploader_name,
        "fiscal_week_year": x.fiscal_week_year
    } for x in sku_query]
    
    # esr
    esr_query = AppFastESRMonWi.get_esr_record_list(fiscal_week_year)
    esr_data = [{
        "name": "ESR",
        "last_upload_date": x.update_time and x.update_time.strftime(DateTimeFormat),
        "uploader_name": "system",
        "fiscal_week_year": x.fiscal_week_year
    } for x in esr_query]
    
    return esr_data + sku_data