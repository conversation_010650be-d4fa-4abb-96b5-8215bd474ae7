import os
import pandas as pd

from data.cascade_filter_data import DimFastModelSkuList, DimFastModelMappingList
from data.fast_lite_forecast_data import LiteNewAppFastForecastTrmSoWa, FastCPFForecastWeekList, AppFastForecastMultiVersion, NewAppFastForecastTrmSoWa
from data.fast_lite_national_data import NationalForecastingResultDtl
from data.forecast_data import AppFastForecastTrmSoWa
from service.model_sku_service import get_model_list

from util.conf import logging
from util.util import get_download_path, gen_dict_by_tuple, sort_by_list
from util.const import MODEL, RTM, DownloadNational, DownloadRTM, FORECAST_FIRST_VERSION, FORECAST_SECOND_VERSION, \
    StrRTMCPF
from data.fiscal_year_week import FiscalYearWeek


def get_forecast_list_service(page_num: int, page_size: int) -> list:
    
    ret, total = FastCPFForecastWeekList.get_week_list(page_num, page_size)
    data_list = []
    for i in ret:
       data_list.append({
           "fiscalQtrWeekName": i[0],
           "fiscal_week_year": i[1]
       }) 
    
    res = {
        "dataList": {
            "records": data_list,
            "total": total,
            "size": page_size,
            "pages": page_num
        }
    }
    
    return res


def get_model_options_service(lob: str, fiscal_week_year: str) -> list:
    res = []
    if lob == 'iPhone':
        res = DimFastModelSkuList.get_model_list(StrRTMCPF,fiscal_week_year)
    return res


def get_sku_options_service(type:str,lob: str, model: list,fiscal_week_year: str) -> list:
    ret = []
    if model is not None and len(model) == 0:
        model = get_model_list(type, ["iphone"], StrRTMCPF, fiscal_week_year)
    query_result = DimFastModelSkuList.get_sku(lob, model)
    # temp = gen_dict_by_tuple(query_result)
    # ret = sort_by_list(model, temp)
    return [x[0] for x in query_result if x[0]]


def get_forecast_detail_service(fiscal_qtr_week_name: str, lob: str, model: list, sku: list, orders: list) -> list:
    ret = []
    

    
    national_data = NationalForecastingResultDtl.gen_data_by_week(fiscal_qtr_week_name, lob, model, sku)
    
    rtm_total, data_keys = LiteNewAppFastForecastTrmSoWa.get_details(fiscal_qtr_week_name, lob, model, sku, orders)
    
    # Model Delta = RTM Model - National Model
    delta_dynamic = {}
    if len(rtm_total) > 0 and len(national_data) > 0:
        rtm_data = rtm_total[0]
        nat_data = national_data[0]
        for j in rtm_data:
            if isinstance(rtm_data[j], int) and isinstance(nat_data[j], int):
                delta_dynamic[j] = rtm_data[j] - nat_data[j]
            else:
                delta_dynamic[j] = nat_data[j]

    fixed_delta = {
        "rtm": 'Model Delta',
        "business_type": '',
        "sold_to": '',
    }
    # 两者中都是int类型的数据相减, 其他值取national数据, 再用新的key覆盖
    delta_data = [{**delta_dynamic, **fixed_delta}]
    ret = delta_data+national_data+rtm_total
    
    # 处理数据, 树形
    columns = LiteNewAppFastForecastTrmSoWa.get_title(fiscal_qtr_week_name)
    col = []

    for i,v in enumerate(columns):
        col.append({
            'key': list(data_keys)[i],
            'value': v
        })
    by_rtm = LiteNewAppFastForecastTrmSoWa.get_details_by_rtm(fiscal_qtr_week_name, lob, model, sku, orders)
    by_business_type = LiteNewAppFastForecastTrmSoWa.get_details_by_business_type(fiscal_qtr_week_name, lob, model, sku, orders)
    by_sold_to = LiteNewAppFastForecastTrmSoWa.get_details_by_sold_to(fiscal_qtr_week_name, lob, model, sku, orders)

    for m in RTM:
        if m in by_rtm.keys():
            business_type_data = []
            for n in by_business_type[m]:
                n['children'] = by_sold_to[m].get(n['business_type'])
                business_type_data.append(n)
            by_rtm[m]['children'] = business_type_data
            ret.append(by_rtm[m])
    
    return {
        "columns": col,
        "data_list": ret
    }


def get_forecast_multi_version_detail_service(fiscal_qtr_week_name: str, lob: str, model: list, sku: list, orders: list) -> list:
    ret = []
    

    
    # national和rtm的总计的*cw-1直接添加在末尾, 统一计算
    national_data = NationalForecastingResultDtl.gen_data_by_week(fiscal_qtr_week_name, lob, model, sku)
    
    # 获取不同的version
    no_mono_version, mono_version = AppFastForecastMultiVersion.get_latest_version(fiscal_qtr_week_name)
    
    rtm_total, data_keys = AppFastForecastMultiVersion.get_details_total(fiscal_qtr_week_name, lob, model, sku, orders, no_mono_version, mono_version)
    
    # Model Delta = RTM Model - National Model
    delta_dynamic = {}
    if len(rtm_total) > 0 and len(national_data) > 0:
        rtm_data = rtm_total[0]
        nat_data = national_data[0]
        for j in rtm_data:
            if isinstance(rtm_data[j], int) and isinstance(nat_data[j], int):
                delta_dynamic[j] = rtm_data[j] - nat_data[j]
            else:
                delta_dynamic[j] = nat_data[j]

    fixed_delta = {
        "rtm": 'Model Delta',
        "business_type": '',
        "sold_to": '',
    }
    # 两者中都是int类型的数据相减, 其他值取national数据, 再用新的key覆盖
    delta_data = [{**delta_dynamic, **fixed_delta}]
    ret = delta_data+national_data+rtm_total
    
    # 处理数据, 树形
    columns = AppFastForecastMultiVersion.get_title(fiscal_qtr_week_name)
    col = []
    boundary = False
    for index,column in enumerate(columns):
        if column.startswith('*CW-1'):
            boundary = True
            col.append({
                'key': '*CW-1',
                'value': column
            })
            continue
        if boundary:
            col.append({
                'key': list(data_keys)[index-1],
                'value': column
            })
            continue
        col.append({
            'key': list(data_keys)[index],
            'value': column
        })
    
    fiscal_week_year = FiscalYearWeek.get_fiscal_week_year_by_week_date(fiscal_qtr_week_name)
    by_rtm = AppFastForecastMultiVersion.get_details_by_rtm(fiscal_qtr_week_name, lob, model, sku, orders, no_mono_version, mono_version)
    by_business_type = AppFastForecastMultiVersion.get_details_by_business_type(fiscal_week_year, fiscal_qtr_week_name, lob, model, sku, orders, no_mono_version, mono_version)
    by_sold_to = AppFastForecastMultiVersion.get_details_by_sold_to(fiscal_week_year, fiscal_qtr_week_name, lob, model, sku, orders, no_mono_version, mono_version)

    for m in RTM:
        if m in by_rtm.keys():
            business_type_data = []
            for n in by_business_type[m]:
                n['children'] = by_sold_to[m].get(n['business_type'])
                business_type_data.append(n)
            by_rtm[m]['children'] = business_type_data
            ret.append(by_rtm[m])
    
    return {
        "columns": col,
        "data_list": ret
    }


def get_fast_forecast_download_path(fiscal_qtr_week_name: str, model:list,download_type: int, forecast_version: int = 0) -> str:
    path = get_download_path(fiscal_qtr_week_name, download_type, forecast_version)
    # # 如果存在文件, 直接进行下载, 不需要生成数据
    # if os.path.isfile(path):
    #     return path
    # 当前系统不存在下载文件, 则要新生成需要的数据
    gen_download_file(fiscal_qtr_week_name,model, download_type, path, forecast_version)

    return path


def gen_download_file(fiscal_qtr_week_name: str, model:list, download_type: int, file_path: str, version: int = 0):
    '''
    生成需要下载数据的csv文件
    按照下载类型, 查询对应的数据库, 生成数据
    '''
    try:
        
        data = []
        columns = []
        
        if download_type == DownloadNational:
            national_data = NationalForecastingResultDtl.get_download_national_data(fiscal_qtr_week_name)
            project_code = DimFastModelMappingList.get_mpn_projcet_code()
            data = national_data.merge(project_code, how='left', on=['MPN'])
            columns = [
                "Week_Date",
                "LOB",
                "Model",
                "FPH4",
                "Project Code",
                "SKU",
                "MPN",
                "Fiscal_Quarter",
                "Fiscal_Week",
                "Fcst"
            ]
        elif download_type == DownloadRTM:
            fiscal_week_year = FiscalYearWeek.get_fiscal_week_year_by_week_date(fiscal_qtr_week_name)
            # 判断页面上显示的是第几周的数据, 
            # 如果是第二周的数据, 那么就可以下载第二周, 
            # 如果是第一周的数据,只能下载第一周, 第二下载为空表头.
            no_mono_version, mono_version = AppFastForecastMultiVersion.get_latest_version(fiscal_qtr_week_name)
            if version == FORECAST_FIRST_VERSION:
                data = NewAppFastForecastTrmSoWa.download_first_version_data(fiscal_week_year, fiscal_qtr_week_name,model)
            elif version == FORECAST_SECOND_VERSION and mono_version == FORECAST_SECOND_VERSION:
                data = NewAppFastForecastTrmSoWa.download_second_version_data(fiscal_week_year, fiscal_qtr_week_name,model)
            elif version == 0:
                data = AppFastForecastTrmSoWa.get_download_data(fiscal_qtr_week_name,model)
            columns = [
                "Week_Date",
                "RTM",
                "Business Type",
                "Sold-to ID",
                "Sold-to Name",
                "Abbre.",
                "LOB",
                "Model",
                "FPH4",
                "Project Code",
                "SKU",
                "MPN",
                "Fiscal_Quarter",
                "Fiscal_Week",
                "Fcst"
            ]
        # 对data进行整合
        
        df = pd.DataFrame(data, columns=columns)
        df.to_csv(file_path, header=True, index=False)
        return True
    except Exception as e:
        logging.info(f"gen csv error is {e}.")
        return False
    
