import threading
import types
from datetime import datetime

from data.fiscal_year_week import FiscalYearWeek
from util.conf import logger, cache
from util.const import *
from data.allocation_prepare_data import TblAllocationMock


class xthread(threading.Thread):
    def __init__(self, func, args=None, name=''):
        threading.Thread.__init__(self)
        assert isinstance(func, types.FunctionType)
        self.func = func
        self.args = args
        self.name = name

    def run(self):
        begin_time = datetime.now()
        logger.info(
            f'{begin_time.strftime("%Y-%m-%d %H:%M:%S")}, begin run func: {self.name}.{self.func.__name__}, args: {self.args}')
        # threadLock.acquire()
        try:
            if self.args == None:
                self.func()
                return
            self.func(*self.args)
        except Exception as e:
            logger.exception(e)
        end_time = datetime.now()
        logger.info(
            f'{end_time.strftime("%Y-%m-%d %H:%M:%S")}, func: {self.name}.{self.func.__name__} end. cost:{(end_time-begin_time).seconds}s')


strName = 'name'
strStartDate = 'start_date'
strEndDate = 'end_date'


@cache.memoize(TimeoutDay)
def get_default_dates(today: datetime) -> list:
    date = today.strftime('%Y-%m-%d')
    fis = FiscalYearWeek.get_fis_by_date(date)
    if not isinstance(fis, FiscalYearWeek):
        raise ErrorExcept(ErrCode.DBQueryError, 'get fiscal class error')

    dates = []

    begin, _ = FiscalYearWeek.get_boundary_date_for_mystore(
        fis.fiscal_year, week=fis.fiscal_week)
    dates.append({
        strName: strWTD,
        strStartDate: begin,
        strEndDate: date
    })

    y, w = FiscalYearWeek.get_last_week_by_date(date)
    begin, end = FiscalYearWeek.get_boundary_date_for_mystore(y, week=w)
    dates.append({
        strName: strLW,
        strStartDate: begin,
        strEndDate: end
    })

    begin, _ = FiscalYearWeek.get_boundary_date_for_mystore(
        fis.fiscal_year, quarter=fis.fiscal_quarter)
    dates.append({
        strName: strQTD,
        strStartDate: begin,
        strEndDate: date
    })

    y, q = FiscalYearWeek.get_last_quarter(date)
    begin, end = FiscalYearWeek.get_boundary_date_for_mystore(y, quarter=q)
    dates.append({
        strName: strLQ,
        strStartDate: begin,
        strEndDate: end
    })

    begin, _ = FiscalYearWeek.get_boundary_date_for_mystore(fis.fiscal_year)
    dates.append({
        strName: strYTD,
        strStartDate: begin,
        strEndDate: date
    })
    
    begin, end = FiscalYearWeek.get_boundary_date_for_mystore(fis.fiscal_year - 1)
    dates.append({
        strName: strLY,
        strStartDate: begin,
        strEndDate: end
    })

    return dates


def get_current_time_with_mock():
    ret = TblAllocationMock.get_info_by_mock_type('mock_current_time')
    if len(ret) > 0:
        return ret[0]['mock_value']
    return datetime.now().strftime(DateTimeFormat)


def get_fiscal_date_with_mock():
    ret = TblAllocationMock.get_info_by_mock_type('mock_fiscal_date')
    if len(ret) > 0:
        return ret[0]['mock_value']
    return datetime.now().strftime(DateFormat)


def is_history_week_in_allocation(fiscal_week_year: int):
    fiscal_dt = get_fiscal_date_with_mock()
    fiscal_week_year_info = FiscalYearWeek.get_week_by_date(fiscal_dt)
    return True if int(fiscal_week_year) < int(fiscal_week_year_info['fiscal_week_year']) else False
