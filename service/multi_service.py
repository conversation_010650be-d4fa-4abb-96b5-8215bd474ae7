from data.fast_forecast_week_data import FastForecastWeek
from data.multi_data import *
from data.fiscal_year_week import FiscalYearWeek
from util.const import ErrorExcept, ErrCode, ErrMsg, PrsId<PERSON><PERSON>


def divide_1000_then_keep_1(data: float):
    if data is None:
        return 0
    return f"{round(data/1000, 1):,.9g}"


def get_fiscal_year_from_fiscal_week_year(fiscal_week_year: int):
    if not fiscal_week_year:
        raise ErrorExcept(ErrCode.Param, "不合法的fiscal_week_year")
    return int(str(fiscal_week_year)[:4])


def get_same_sold_to_and_model_data(data: list, sold_to_id: str, model: str) -> int:
    try:
        forecast = filter(lambda x: x.sold_to_id == sold_to_id and x.model == model, data).__next__().fcst
        return forecast and int(forecast)
    except StopIteration:
        return None


def get_order_map(clazz):
    return {
        "fiscal_qtr_week_name": clazz.fiscal_qtr_week_name,
        "business_type": DimFastSoldToMappingMulti.business_type,
        "sold_to_id": clazz.sold_to_id,
        "sold_to_name": DimFastSoldToMappingMulti.sold_to_name,
        "lob": clazz.lob,
        "model": clazz.sub_lob,
        "shipment_plan_cw": func.sum(clazz.shipment_plan_cw),
        "shipment_plan_cw1": func.sum(clazz.shipment_plan_cw1),
        "shipment_plan_cw2": func.sum(clazz.shipment_plan_cw2),
        "shipment_plan_cw3": func.sum(clazz.shipment_plan_cw3),
        "top_up_demand_cw1": func.sum(clazz.top_up_demand_cw1),
        "top_up_demand_cw2": func.sum(clazz.top_up_demand_cw2),
        "top_up_demand_cw3": func.sum(clazz.top_up_demand_cw3),
        "top_up_demand_cw4": func.sum(clazz.top_up_demand_cw4),
        "po_needed_cw": func.sum(clazz.po_needed_cw),
        "po_needed_cw1": func.sum(clazz.po_needed_cw1),
        "po_needed_cw2": func.sum(clazz.po_needed_cw2),
        "po_needed_cw3": func.sum(clazz.po_needed_cw3),
        "demand_cw": func.sum(clazz.demand_cw),
        "demand_cw1": func.sum(clazz.demand_cw1),
        "demand_cw2": func.sum(clazz.demand_cw2),
        "demand_cw3": func.sum(clazz.demand_cw3),
        "shippable_backlog": func.sum(clazz.shippable_backlog),
        "gross_billing_units_cw": func.sum(clazz.gross_billing_units_cw),
        "sp_remaining_cw1": func.sum(clazz.sp_remaining_cw1),
        "sp_remaining_cw2": func.sum(clazz.sp_remaining_cw2),
    }


def get_multi_fiscal_qtr_week_year(page_num: str, page_size: str):
    try:
        page_num = int(page_num)
        page_size = int(page_size)
    except ValueError:
        raise ErrorExcept(ErrCode.Param, "page & pagesize must be number")
    res, count = AppFastDemandMultiSummaryAmWa.get_fiscal_qtr_week_data(page_num, page_size)
    data = [{"fiscal_week_year": x.fiscal_week_year, "fiscal_qtr_week_name": x.fiscal_qtr_week_name} for x in res]
    return {"list": data, "count": count}


def get_multi_demand_data(
        fiscal_week_year: str, business_type: list, sold_to_id: list,
        sold_to_name: list, lob: list, model: list, sku: list,
        order_by: list, page_num: str, page_size: str
):
    if fiscal_week_year is None:
        raise ErrorExcept(ErrCode.Param, "fiscal_week_year required")
    try:
        fiscal_week_year = int(fiscal_week_year)
    except Exception as e:
        raise ErrorExcept(ErrCode.Param, "fiscal_week_year must be integer")
    try:
        page_num = int(page_num)
        page_size = int(page_size)
    except ValueError:
        raise ErrorExcept(ErrCode.Param, "page & pagesize must be number")
    try:
        sold_to_id = list(map(int, sold_to_id))
    except ValueError:
        raise ErrorExcept(ErrCode.Param, "each element of sold_to_id must be number")

    # 查询取哪一版数据
    list_res, _ = AppFastDemandMultiSummaryWa.get_fiscal_qtr_week_data(1, 500)
    for week_data in list_res:
        if week_data.fiscal_week_year == fiscal_week_year:
            version = 2
            clazz = AppFastDemandMultiSummaryWa
            break
    else:
        version = 1
        clazz = AppFastDemandMultiSummaryAmWa

    order_map = get_order_map(clazz)
    # 排序预加工
    order_by_list = []
    for item in order_by:
        try:
            order_dict = json.loads(item)
            if order_dict.get("name", None) is None or order_dict.get("name", None) is None:
                raise ErrorExcept(ErrCode.Param, "每个order_by元素都应存在name与type。")
            if order_map.get(order_dict.get("name"), None) is None:
                raise ErrorExcept(ErrCode.Param, "不存在的列名：" + str(order_dict.get("name")))
            if order_dict.get("type") not in [0, 1]:
                raise ErrorExcept(ErrCode.Param, "order_by中的type只能为int：0->asc或1->desc")
            if order_dict.get("name") != "model" and order_dict.get("name") != "sold_to_id":
                order_by_list.append(
                    order_map.get(order_dict.get("name")).asc()
                    if order_dict.get("type") == 0
                    else order_map.get(order_dict.get("name")).desc()
                )
            elif order_dict.get("name") != "model":
                order_by_list.append(
                    cast(DimFastSoldToMappingMulti.sold_to_id, Integer)
                    if order_dict.get("type") == 0
                    else cast(DimFastSoldToMappingMulti.sold_to_id, Integer).desc()
                )
            else:
                order_by_list.append(
                    case(
                        value=clazz.sub_lob,
                        whens={x: (model
                                   if order_dict.get("type") == 0
                                   else model[::-1]).index(x)+1
                               for x in model}
                    )
                )
        except json.JSONDecodeError:
            raise ErrorExcept(ErrCode.Param, "每个order_by元素都应为合法的json字符串。")

    weeks = FiscalYearWeek.get_cw_next_4_week(fiscal_week_year)
    if len(weeks) < 5:
        raise ErrorExcept(ErrCode.DBQueryError, "fiscal_year_week table error: not enough data")
    columns = {
        "business_type": "Business Type",
        "sold_to_id": "Customer Sold-to ID",
        "sold_to_name": "Sold-to Name",
        "lob": "LOB",
        "model": "Model",
        "top_up_demand_cw1": f"Top Up Demand CW+1\n{weeks[1]}",
        "top_up_demand_cw2": f"Top Up Demand CW+2\n{weeks[2]}",
        "top_up_demand_cw3": f"Top Up Demand CW+3\n{weeks[3]}",
        "top_up_demand_cw4": f"Top Up Demand CW+4\n{weeks[4]}",
        "shipment_plan_cw": f"Shipment Plan CW\n{weeks[0]}",
        "shipment_plan_cw1": f"Shipment Plan CW+1\n{weeks[1]}",
        "shipment_plan_cw2": f"Shipment Plan CW+2\n{weeks[2]}",
        "shipment_plan_cw3": f"Shipment Plan CW+3\n{weeks[3]}",
        "po_needed_cw": f"PO Needed CW\n{weeks[0]}",
        "po_needed_cw1": f"PO Needed CW+1\n{weeks[1]}",
        "po_needed_cw2": f"PO Needed CW+2\n{weeks[2]}",
        "po_needed_cw3": f"PO Needed CW+3\n{weeks[3]}",
    }
    columns = [{"key": x, "value": columns[x]} for x in columns.keys()]

    # 处理结果
    result, total, count = clazz.get_multi_data(
        fiscal_week_year, business_type, sold_to_id, sold_to_name, lob, model, sku, order_by_list, page_num, page_size
    )
    if len(result) == 0:
        raise ErrorExcept(ErrCode.DBQueryNoData, ErrMsg.get(ErrCode.DBQueryNoData))
    main_data = []
    for item in result:
        res = {}
        item_dict = item._asdict()
        for key in item_dict:
            if key in [
                "po_needed_cw",
                "po_needed_cw1",
                "po_needed_cw2",
                "po_needed_cw3",
                "shipment_plan_cw",
                "shipment_plan_cw1",
                "shipment_plan_cw2",
                "shipment_plan_cw3",
                "top_up_demand_cw1",
                "top_up_demand_cw2",
                "top_up_demand_cw3",
                "top_up_demand_cw4",
            ]:
                res[key] = item_dict[key] and int(item_dict[key])
            else:
                res[key] = item_dict[key]
        main_data.append(res)
    total_dict = total._asdict()
    total_data = {}
    for key in total_dict:
        total_data[key] = total_dict[key]
    return {
        "main": main_data,
        "total": total_data,
        "count": count,
        "columns": columns,
        "version": version,
    }


def gen_multi_demand_download_data(fiscal_week_year, version,model:list):
    if version == "1":
        clazz = AppFastDemandMultiSummaryAmWa
        version_info = "first_version"
    else:
        clazz = AppFastDemandMultiSummaryWa
        version_info = "second_version"
    fiscal_qtr = clazz.get_fiscal_qtr_by_fiscal_week_year(fiscal_week_year)
    path = os.path.dirname(os.path.dirname(os.path.abspath(__file__))) + f'/downloads/{g.get(PrsIdKey)}/multi/'
    dir_name = os.path.dirname(path)
    if not os.path.exists(dir_name):
        os.makedirs(dir_name)
    if not os.path.exists(path):
        os.mkdir(path)
    path += f'Demand__{fiscal_qtr}_{version_info}.csv'
    # if os.path.isfile(path):
    #     return path
    data = clazz.get_download_data(fiscal_week_year,model)
    columns = clazz.DOWNLOAD_COLUMN_NAME
    df = pd.DataFrame(data, columns=columns)
    df.to_csv(path, header=True, index=False)
    return path


def get_multi_forecast_data_list(page_num: str, page_size: str):
    try:
        page_num = int(page_num)
        page_size = int(page_size)
    except ValueError:
        raise ErrorExcept(ErrCode.Param, "page & pagesize must be number")
    res, count = FastForecastWeek.get_by_rtm(StrRTMMulti, page_num, page_size)
    data = [{"fiscal_week_year": x.fiscal_week_year, "fiscal_qtr_week_name": x.week_date} for x in res]
    return {"list": data, "count": count}


def get_multi_forecast_data_new(
    fiscal_week_year: str, business_type: list, sold_to_id: list, sold_to_name: list, lob: list, model: list,
):
    # 排序部分放在model层，对数据库服务器压力偏大，将压力转移至view层
    if fiscal_week_year is None:
        raise ErrorExcept(ErrCode.Param, "fiscal_week_year required")
    try:
        fiscal_week_year = int(fiscal_week_year)
    except Exception:
        raise ErrorExcept(ErrCode.Param, "fiscal_week_year must be integer")
    try:
        map(int, sold_to_id)
    except ValueError:
        raise ErrorExcept(ErrCode.Param, "each element of sold_to_id must be number")
    # 获取business type mapping 数据
    sold_to_list = DimFastSoldToMappingMulti.get_by_role(business_type, sold_to_id, sold_to_name, fiscal_week_year)
    # 获取数据
    last_week_year = FiscalYearWeek.get_last_fiscal_week_year(fiscal_week_year)
    result, last_result = AppFastForecastMultiSoWa.get_data_by_fiscal_week_year_new(
        fiscal_week_year, last_week_year,
        [x.sold_to_id for x in sold_to_list], StrRTMMulti, lob, model
    )
    try:
        current_week_data = filter(lambda x: x.fiscal_week_year == x.show_week, result).__next__()
    except StopIteration:
        raise ErrorExcept(ErrCode.DBQueryNoData, ErrMsg.get(ErrCode.DBQueryNoData))
    current_fiscal_year = get_fiscal_year_from_fiscal_week_year(current_week_data.fiscal_week_year)
    current_fiscal_qtr = current_week_data.fiscal_quarter
    fiscal_weeks_dict, lq_year_name, cq_year_name = FiscalYearWeek.get_last3_and_current_and_next5_quarter_week(
        current_fiscal_year,
        current_fiscal_qtr,
        current_week_data.fiscal_week_year
    )
    # 表头信息整理
    old_columns = {
          "lq_total": f"LQ Total\n{lq_year_name}",
          "cq_total": f"CQ Total\n{cq_year_name}",
          **{str(x): fiscal_weeks_dict[x] for x in fiscal_weeks_dict.keys()}
    }
    cw_m1_index = list(old_columns.keys()).index(str(last_week_year)) + 1
    columns = [{"key": x, "value": old_columns[x]} for x in old_columns.keys()]
    columns.insert(cw_m1_index, {"key": f"*{last_week_year}", "value": f"*{old_columns[str(last_week_year)]}"})
    data = []
    total = {
        "lq_total": 0,
        "cq_total": 0,
        f"*{last_week_year}": 0,
        **{str(x): 0 for x in fiscal_weeks_dict.keys()}
    }
    # 数据处理
    sold_to_map = {x.sold_to_id: x for x in sold_to_list}
    result = sorted(result, key=lambda x: model.index(x.model))
    result = sorted(result, key=lambda x: int(x.sold_to_id))
    result = sorted(result, key=lambda x: MultiBusinessTypeOrder.index(sold_to_map.get(x.sold_to_id).business_type))
    # 格式化输出
    current_sold_to = {}
    current_record = {}
    lq = int(lq_year_name[-1:])
    cq = int(cq_year_name[-1:])
    lq_total = 0
    cq_total = 0
    for item in result:
        if item.sold_to_id == current_sold_to.get("sold_to_id"):
            if item.model != current_record.get("model"):
                if current_record:
                    current_record["data"]["lq_total"] = lq_total
                    current_record["data"]["cq_total"] = cq_total
                    lq_total = 0
                    cq_total = 0
                    current_record["data"][f"*{last_week_year}"] = get_same_sold_to_and_model_data(
                        last_result, current_sold_to.get("sold_to_id"),
                        current_record.get("model")
                    )
                    for key in current_sold_to["total"].keys():
                        current_sold_to["total"][str(key)] += (current_record["data"][str(key)] or 0)
                    current_sold_to["data"].append(current_record)
                current_record = {
                    "model": item.model,
                    "data": {
                        "lq_total": 0,
                        "cq_total": 0,
                        **{str(x): None for x in fiscal_weeks_dict.keys()}
                    }
                }
        else:
            if current_sold_to:
                current_record["data"]["lq_total"] = lq_total
                current_record["data"]["cq_total"] = cq_total
                lq_total = 0
                cq_total = 0
                current_record["data"][f"*{last_week_year}"] = get_same_sold_to_and_model_data(
                    last_result, current_sold_to.get("sold_to_id"),
                    current_record.get("model")
                )
                for key in current_sold_to["total"].keys():
                    current_sold_to["total"][str(key)] += (current_record["data"][str(key)] or 0)
                current_sold_to["data"].append(current_record)
                for key in total.keys():
                    total[str(key)] += current_sold_to["total"][str(key)]
                current_sold_to["total"] = current_sold_to["total"]
                data.append(current_sold_to)
            current_sold_to = {
                "business_type": sold_to_map.get(item.sold_to_id).business_type,
                "sold_to_name": sold_to_map.get(item.sold_to_id).sold_to_name,
                "sold_to_id": item.sold_to_id,
                "lob": item.lob,
                "total": {
                    "lq_total": 0,
                    "cq_total": 0,
                    f"*{last_week_year}": 0,
                    **{str(x): 0 for x in fiscal_weeks_dict.keys()}
                },
                "data": [],
            }
            current_record = {
                "model": item.model,
                "data": {
                    "lq_total": 0,
                    "cq_total": 0,
                    **{str(x): None for x in fiscal_weeks_dict.keys()}
                }
            }
        if item.fiscal_quarter == lq:
            lq_total += int(item.fcst or 0)
        elif item.fiscal_quarter == cq:
            cq_total += int(item.fcst or 0)
        if item.show_week in fiscal_weeks_dict.keys():
            current_record["data"][str(item.show_week)] = item.fcst and int(item.fcst)
    else:
        if current_sold_to:
            current_record["data"]["lq_total"] = lq_total
            current_record["data"]["cq_total"] = cq_total
            current_record["data"][f"*{last_week_year}"] = get_same_sold_to_and_model_data(
                last_result, current_sold_to.get("sold_to_id"),
                current_record.get("model")
            )
            for key in current_sold_to["total"].keys():
                current_sold_to["total"][str(key)] += (current_record["data"][str(key)] or 0)
            current_sold_to["data"].append(current_record)
            for key in total.keys():
                total[str(key)] += current_sold_to["total"][str(key)]
            data.append(current_sold_to)
    return {"total": total, "list": data, "columns": columns}


def get_multi_forecast_data(
    fiscal_week_year: str, business_type: list, sold_to_id: list, sold_to_name: list, lob: list, model: list,
):
    # 排序部分放在model层，对数据库服务器压力偏大
    if fiscal_week_year is None:
        raise ErrorExcept(ErrCode.Param, "fiscal_week_year required")
    try:
        fiscal_week_year = int(fiscal_week_year)
    except Exception:
        raise ErrorExcept(ErrCode.Param, "fiscal_week_year must be integer")
    try:
        map(int, sold_to_id)
    except ValueError:
        raise ErrorExcept(ErrCode.Param, "each element of sold_to_id must be number")
    # 数据处理
    last_week_year = FiscalYearWeek.get_last_fiscal_week_year(fiscal_week_year)
    result, last_result = AppFastForecastMultiSoWa.get_data_by_fiscal_week_year(
        fiscal_week_year, last_week_year, StrRTMMulti, business_type, sold_to_id, sold_to_name, lob, model
    )
    try:
        current_week_data = filter(lambda x: x.fiscal_week_year == x.show_week, result).__next__()
    except StopIteration:
        raise ErrorExcept(ErrCode.DBQueryNoData, ErrMsg.get(ErrCode.DBQueryNoData))
    current_fiscal_year = get_fiscal_year_from_fiscal_week_year(current_week_data.fiscal_week_year)
    current_fiscal_qtr = current_week_data.fiscal_quarter
    fiscal_weeks_dict, lq_year_name, cq_year_name = FiscalYearWeek.get_last3_and_current_and_next5_quarter_week(
        current_fiscal_year,
        current_fiscal_qtr,
        current_week_data.fiscal_week_year
    )
    old_columns = {
          "lq_total": f"LQ Total\n{lq_year_name}",
          "cq_total": f"CQ Total\n{cq_year_name}",
          **{str(x): fiscal_weeks_dict[x] for x in fiscal_weeks_dict.keys()}
    }
    cw_m1_index = list(old_columns.keys()).index(str(last_week_year)) + 1
    columns = [{"key": x, "value": old_columns[x]} for x in old_columns.keys()]
    columns.insert(cw_m1_index, {"key": f"*{last_week_year}", "value": f"*{old_columns[str(last_week_year)]}"})
    data = []
    total = {
        "lq_total": 0,
        "cq_total": 0,
        f"*{last_week_year}": 0,
        **{str(x): 0 for x in fiscal_weeks_dict.keys()}
    }
    current_sold_to = {}
    current_record = {}
    lq = int(lq_year_name[-1:])
    cq = int(cq_year_name[-1:])
    lq_total = 0
    cq_total = 0
    for item in result:
        if item.sold_to_id == current_sold_to.get("sold_to_id"):
            if item.model != current_record.get("model"):
                if current_record:
                    current_record["data"]["lq_total"] = lq_total
                    current_record["data"]["cq_total"] = cq_total
                    lq_total = 0
                    cq_total = 0
                    current_record["data"][f"*{last_week_year}"] = get_same_sold_to_and_model_data(
                        last_result, current_sold_to.get("sold_to_id"),
                        current_record.get("model")
                    )
                    for key in current_sold_to["total"].keys():
                        current_sold_to["total"][str(key)] += (current_record["data"][str(key)] or 0)
                    current_sold_to["data"].append(current_record)
                current_record = {
                    "model": item.model,
                    "data": {
                        "lq_total": 0,
                        "cq_total": 0,
                        **{str(x): None for x in fiscal_weeks_dict.keys()}
                    }
                }
        else:
            if current_sold_to:
                current_record["data"]["lq_total"] = lq_total
                current_record["data"]["cq_total"] = cq_total
                lq_total = 0
                cq_total = 0
                current_record["data"][f"*{last_week_year}"] = get_same_sold_to_and_model_data(
                    last_result, current_sold_to.get("sold_to_id"),
                    current_record.get("model")
                )
                for key in current_sold_to["total"].keys():
                    current_sold_to["total"][str(key)] += (current_record["data"][str(key)] or 0)
                current_sold_to["data"].append(current_record)
                for key in total.keys():
                    total[str(key)] += current_sold_to["total"][str(key)]
                current_sold_to["total"] = current_sold_to["total"]
                data.append(current_sold_to)
            current_sold_to = {
                "business_type": item.business_type,
                "sold_to_name": item.sold_to_name,
                "sold_to_id": item.sold_to_id,
                "lob": item.lob,
                "total": {
                    "lq_total": 0,
                    "cq_total": 0,
                    f"*{last_week_year}": 0,
                    **{str(x): 0 for x in fiscal_weeks_dict.keys()}
                },
                "data": [],
            }
            current_record = {
                "model": item.model,
                "data": {
                    "lq_total": 0,
                    "cq_total": 0,
                    **{str(x): None for x in fiscal_weeks_dict.keys()}
                }
            }
        if item.fiscal_quarter == lq:
            lq_total += int(item.fcst or 0)
        elif item.fiscal_quarter == cq:
            cq_total += int(item.fcst or 0)
        if item.show_week in fiscal_weeks_dict.keys():
            current_record["data"][str(item.show_week)] = item.fcst and int(item.fcst)
    else:
        if current_sold_to:
            current_record["data"]["lq_total"] = lq_total
            current_record["data"]["cq_total"] = cq_total
            current_record["data"][f"*{last_week_year}"] = get_same_sold_to_and_model_data(
                last_result, current_sold_to.get("sold_to_id"),
                current_record.get("model")
            )
            for key in current_sold_to["total"].keys():
                current_sold_to["total"][str(key)] += (current_record["data"][str(key)] or 0)
            current_sold_to["data"].append(current_record)
            for key in total.keys():
                total[str(key)] += current_sold_to["total"][str(key)]
            data.append(current_sold_to)
    return {"total": total, "list": data, "columns": columns}


def download_multi_forecast_data(fiscal_week_year: int,model:list):

    fiscal_qtr = AppFastForecastMultiSoWa.get_fiscal_qtr_by_fiscal_week_year(fiscal_week_year)
    path = os.path.dirname(os.path.dirname(os.path.abspath(__file__))) + f'/downloads/{g.get(PrsIdKey)}/multi/'
    dir_name = os.path.dirname(path)
    if not os.path.exists(dir_name):
        os.makedirs(dir_name)
    if not os.path.exists(path):
        os.mkdir(path)
    path += f'Forecast_Multi_Model_{fiscal_qtr}.csv'
    # if os.path.isfile(path):
    #     return path

    data = []
    result = AppFastForecastMultiSoWa.get_download_data_by_fiscal_week_year(fiscal_week_year, StrRTMMulti,model)
    current_record = {}
    week_index = 0
    for item in result:
        # 以cust-id和mpn为维度，序列化数据
        if item.cust_id == current_record.get("Sold-to ID") and item.mpn_id == current_record.get("MPN"):
            current_record[f"CW+{week_index}"] = item.fcst
        else:
            if current_record:
                # 数据不足13周的补None
                if week_index < 12:
                    for week_index in range(week_index, 13):
                        current_record[f"CW+{week_index}"] = None
                data.append(current_record)
                week_index = 0
            current_record = {
                AppFastForecastMultiSoWa.DOWNLOAD_COLUMN_NAME[0]: item.week_date,
                AppFastForecastMultiSoWa.DOWNLOAD_COLUMN_NAME[1]: item.business_type,
                AppFastForecastMultiSoWa.DOWNLOAD_COLUMN_NAME[2]: item.cust_id,
                AppFastForecastMultiSoWa.DOWNLOAD_COLUMN_NAME[3]: item.sold_to_name,
                AppFastForecastMultiSoWa.DOWNLOAD_COLUMN_NAME[4]: item.lob,
                AppFastForecastMultiSoWa.DOWNLOAD_COLUMN_NAME[5]: item.model,
                AppFastForecastMultiSoWa.DOWNLOAD_COLUMN_NAME[6]: item.fph4,
                AppFastForecastMultiSoWa.DOWNLOAD_COLUMN_NAME[7]: item.project_cd,
                AppFastForecastMultiSoWa.DOWNLOAD_COLUMN_NAME[8]: item.sku,
                AppFastForecastMultiSoWa.DOWNLOAD_COLUMN_NAME[9]: item.mpn_id,
                "CW": item.fcst,
            }
        week_index += 1
    else:
        if week_index < 12:
            for week_index in range(week_index, 13):
                current_record[f"CW+{week_index}"] = None
        data.append(current_record)

    df = pd.DataFrame.from_records(data)
    df.to_csv(path, header=True, index=False)
    return path


def get_multi_demand_sold_to_list_data():
    data = {}
    res = AppFastDemandMultiSummaryWa.get_sold_to_data()
    for item in res:
        if data.get(item.business_type) is None:
            data[item.business_type] = {
                "business_type": item.business_type,
                "children": []
            }
        data[item.business_type]["children"].append({
            "sold_to_name": item.sold_to_name,
            "sold_to_id": item.sold_to_id,
        })
    return list(data.values())


def get_multi_demand_sku_list_data():
    data = {}
    res = AppFastDemandMultiSummaryWa.get_sku_data()
    for item in res:
        if data.get(item.lob) is None:
            data[item.lob] = {
                "lob": item.lob,
                "children": {}
            }
        if data[item.lob]["children"].get(item.sub_lob) is None:
            data[item.lob]["children"][item.sub_lob] = {
                "model": item.sub_lob,
                "children": []
            }
        data[item.lob]["children"][item.sub_lob]["children"].append({"sku": item.sku})
    return [{"lob": x["lob"], "children": [y for y in x["children"].values()]} for x in data.values()]


def get_multi_forecast_sold_to_list_data():
    data = {}
    res = AppFastForecastMultiSoWa.get_sold_to_data()
    for item in res:
        if data.get(item.business_type) is None:
            data[item.business_type] = {
                "business_type": item.business_type,
                "children": []
            }
        data[item.business_type]["children"].append({
            "sold_to_name": item.sold_to_name,
            "sold_to_id": item.sold_to_id,
        })
    return list(data.values())


def get_multi_forecast_model_list_data():
    data = {}
    res = AppFastForecastMultiSoWa.get_model_data()
    for item in res:
        if data.get(item.lob) is None:
            data[item.lob] = {
                "lob": item.lob,
                "children": []
            }
        data[item.lob]["children"].append({
            "model": item.model,
        })
    return list(data.values())


def get_multi_raw_business_type_data(fiscal_week_year: str):
    if fiscal_week_year is None:
        fiscal_week_year = FiscalYearWeek.get_fiscal_by_date(datetime.now().strftime("%Y-%m-%d"))[0].fiscal_week_year
    try:
        fiscal_week_year = int(fiscal_week_year)
    except Exception as e:
        raise ErrorExcept(ErrCode.Param, "fiscal_week_year must be integer")
    res = DimFastSoldToMappingMulti.get_business_type_list(fiscal_week_year)
    return [x.business_type for x in res]


def get_multi_raw_sold_to_data(business_type: list, sold_to: str, fiscal_week_year: str):
    if fiscal_week_year is None:
        fiscal_week_year = FiscalYearWeek.get_fiscal_by_date(datetime.now().strftime("%Y-%m-%d"))[0].fiscal_week_year
    try:
        fiscal_week_year = int(fiscal_week_year)
    except Exception as e:
        raise ErrorExcept(ErrCode.Param, "fiscal_week_year must be integer")
    res = []
    if sold_to is None:
        res = DimFastSoldToMappingMulti.get_sold_to_list(business_type, fiscal_week_year=fiscal_week_year)
    elif sold_to.isdecimal():
        res = DimFastSoldToMappingMulti.get_sold_to_list(business_type,
                                                         sold_to_id=sold_to,
                                                         fiscal_week_year=fiscal_week_year)
    else:
        res = DimFastSoldToMappingMulti.get_sold_to_list(business_type,
                                                         sold_to_name=sold_to,
                                                         fiscal_week_year=fiscal_week_year)
    return [{"business_type": x.business_type, "sold_to_id": x.sold_to_id, "sold_to_name": x.sold_to_name} for x in res]
