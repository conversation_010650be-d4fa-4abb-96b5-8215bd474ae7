from data.carrier_data import AppFastDemandCarrierSummaryWa, AppFastDemandCarrierSummaryAmWa
from data.demand_data import AppFastDemandMonoSummaryWa
from data.fast_lite_forecast_data import NewAppFastForecastTrmSoWa
from data.forecast_data import AppFastForecastTrmSoWa
from data.model_sku_data import *
from data.multi_data import AppFastDemandMultiSummaryWa, AppFastDemandMultiSummaryAmWa
from data.online_data import AppFastDemandOnlineSummaryWa, AppFastDemandOnlineSummaryAmWa


def get_lob_list():
    res = DimFastModelSkuList.get_lob_list()
    return [x.lob for x in res]


def get_model_list(type: str, lob: list, rtm: str, fiscal_week_year):
    req = None
    if type == FORECAST:
        if rtm == StrRTMCPF:
            req = NewAppFastForecastTrmSoWa.get_model_list(lob,rtm, fiscal_week_year)
        else:
            req = AppFastForecastTrmSoWa.get_model_list(lob, rtm, fiscal_week_year)
    elif type == DEMAND:
        # 上下午的model 数据一致所以只查一板
        if rtm == StrRTMCPF:
            # 查询取哪一版数据
            req = AppFastDemandMonoSummaryWa.get_model_list(lob, rtm, fiscal_week_year)
        elif rtm == StrRTMMulti:
            req = AppFastDemandMultiSummaryAmWa.get_model_list(lob, rtm, fiscal_week_year)
        elif rtm == StrRTMOnline or rtm == StrRTMOnlineFull:
            req = AppFastDemandOnlineSummaryAmWa.get_model_list(lob, StrRTMOnlineFull, fiscal_week_year)
        elif rtm == StrRTMCarrier:
            # 查询取哪一版数据
            req = AppFastDemandCarrierSummaryAmWa.get_model_list(lob, rtm, fiscal_week_year)
        else:
            req = None
    else:
        req = None
    return [x[0] for x in req if x[0]]


def get_sku_list(model: list, rtm: str):
    res = DimFastModelSkuList.get_sku_list(model)
    return [x.sku for x in res]
