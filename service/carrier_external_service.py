from data.fiscal_year_week import FiscalYearWeek
from data.carrier_data import *
from data.fast_forecast_week_data import FastForecastWeek
from data.model_sku_data import DimFastModelSkuList
from util.const import Prs<PERSON>d<PERSON><PERSON>, Err<PERSON>g, User<PERSON><PERSON><PERSON><PERSON>, UserType, ExternalViewDemandVersion1Week


def get_order_map(clazz):
    return {
        "fiscal_qtr_week_name": clazz.fiscal_qtr_week_name,
        "lob": clazz.lob,
        "model": clazz.sub_lob,
        "shipment_plan_cw": func.sum(clazz.shipment_plan_cw),
        "shipment_plan_cw1": func.sum(clazz.shipment_plan_cw1),
        "demand_cw1": func.sum(clazz.top_up_demand_cw1),
        "demand_cw2": func.sum(clazz.top_up_demand_cw2),
        "demand_cw3": func.sum(clazz.top_up_demand_cw3),
        "demand_cw4": func.sum(clazz.top_up_demand_cw4),
        "po_needed_cw": func.sum(clazz.po_needed_cw),
        "po_needed_cw1": func.sum(clazz.po_needed_cw1),
    }


def get_carrier_fiscal_qtr_week_year_external(page_num: str, page_size: str):
    try:
        page_num = int(page_num)
        page_size = int(page_size)
    except ValueError:
        raise ErrorExcept(ErrCode.Param, "page & pagesize must be number")
    res, count = AppFastDemandCarrierSummaryAmWa.get_fiscal_qtr_week_data_external(page_num, page_size)
    data = [{"fiscal_week_year": x.fiscal_week_year, "fiscal_qtr_week_name": x.fiscal_qtr_week_name} for x in res]
    return {"list": data, "count": count}


def get_carrier_demand_data_external(
        fiscal_week_year: str, lob: list, model: list, sku: list, order_by: list
):
    if fiscal_week_year is None:
        raise ErrorExcept(ErrCode.Param, "fiscal_week_year required")
    try:
        fiscal_week_year = int(fiscal_week_year)
    except Exception as e:
        raise ErrorExcept(ErrCode.Param, "fiscal_week_year must be integer")

    # 排序预加工
    order_by_list = []
    if fiscal_week_year < ExternalViewDemandVersion1Week:
        clazz = AppFastDemandCarrierSummaryWa
    else:
        clazz = AppFastDemandCarrierSummaryAmWa
    order_map = get_order_map(clazz)
    for item in order_by:
        try:
            order_dict = json.loads(item)
            if order_dict.get("name", None) is None or order_dict.get("name", None) is None:
                raise ErrorExcept(ErrCode.Param, "每个order_by元素都应存在name与type。")
            if order_map.get(order_dict.get("name"), None) is None:
                raise ErrorExcept(ErrCode.Param, "不存在的列名：" + str(order_dict.get("name")))
            if order_dict.get("type") not in [0, 1]:
                raise ErrorExcept(ErrCode.Param, "order_by中的type只能为int：0->asc或1->desc")
            order_by_list.append({"key": order_dict.get("name"), "value": order_dict.get("type") != 0})
        except json.JSONDecodeError:
            raise ErrorExcept(ErrCode.Param, "每个order_by元素都应为合法的json字符串。")

    weeks = FiscalYearWeek.get_cw_next_4_week(fiscal_week_year)
    if len(weeks) < 5:
        raise ErrorExcept(ErrCode.DBQueryError, "fiscal_year_week table error: not enough data")
    columns = {
        "lob": "LOB",
        "model": "Model",
        "demand_cw1": f"Demand CW+1\n{weeks[1]}",
        "demand_cw2": f"Demand CW+2\n{weeks[2]}",
        "demand_cw3": f"Demand CW+3\n{weeks[3]}",
        "demand_cw4": f"Demand CW+4\n{weeks[4]}",
        "shipment_plan_cw": f"Shipment Plan CW\n{weeks[0]}",
        "shipment_plan_cw1": f"Shipment Plan CW+1\n{weeks[1]}",\
        "po_needed_cw": f"PO Needed CW\n{weeks[0]}",
        "po_needed_cw1": f"PO Needed CW+1\n{weeks[1]}",
    }
    columns = [{"key": x, "value": columns[x]} for x in columns.keys()]

    # 处理结果
    result, total, count = clazz.get_carrier_data_external(fiscal_week_year, lob, model, sku)
    if g.get(UserTypeKey) == UserType.Expert and len(result) == 0:
        raise ErrorExcept(ErrCode.DBQueryNoData, ErrMsg.get(ErrCode.DBQueryNoData))
    main_data = []
    data_key = [
        "po_needed_cw",
        "po_needed_cw1",
        "shipment_plan_cw",
        "shipment_plan_cw1",
        "demand_cw1",
        "demand_cw2",
        "demand_cw3",
        "demand_cw4",
    ]
    final_model = model
    if model is not None and len(model) != 0:
        final_model = [x for x in model if (True if len(model) == 0 else x in model)]
    if sku is not None and len(sku) != 0:
        models = [x.model for x in DimFastModelSkuList.get_models_by_skus(StrRTMCarrier, lob, sku,model)]
        final_model = [x for x in model if (True if len(models) == 0 else x in models)]
    for m in final_model:
        main_data.append({
            "lob": "iPhone",
            "model": m,
            **{x: None for x in data_key},
        })
    for item in result:
        res = {}
        item_dict = item._asdict()
        for key in item_dict:
            if key in data_key:
                res[key] = item_dict[key] and int(item_dict[key])
        main_data[final_model.index(item.model)].update(res)
    for order in order_by_list[::-1]:
        main_data = sorted(
            main_data,
            key=lambda x: (
                (x.get(order.get("key")) is not None) if order.get("value") else (x.get(order.get("key")) is None),
                x.get(order.get("key")) and float(x.get(order.get("key")))
            ),
            reverse=order.get("value")
        )
    total_dict = total._asdict()
    total_data = {x: 0 for x in data_key}
    for key in total_dict:
        if total_dict[key] is not None:
            total_data[key] = total_dict[key]
    return {
        "main": main_data,
        "total": total_data,
        "count": count,
        "columns": columns,
    }


def gen_carrier_demand_download_data_external(fiscal_week_year,model):
    if fiscal_week_year < ExternalViewDemandVersion1Week:
        clazz = AppFastDemandCarrierSummaryWa
    else:
        clazz = AppFastDemandCarrierSummaryAmWa
    fiscal_qtr = clazz.get_fiscal_qtr_by_fiscal_week_year(fiscal_week_year)
    reseller = DimFastSoldToMappingCarrier.get_cn_name()
    if reseller is None:
        raise ErrorExcept(ErrCode.DBQueryError, "no reseller")
    path = os.path.dirname(os.path.dirname(os.path.abspath(__file__))) + f'/downloads/{g.get(PrsIdKey)}/carrier/'
    dir_name = os.path.dirname(path)
    if not os.path.exists(dir_name):
        os.makedirs(dir_name)
    if not os.path.exists(path):
        os.mkdir(path)
    path += f'需求信息_{reseller.sold_to_name_cn}_{fiscal_qtr}.csv'
    # if os.path.isfile(path):
    #     return path
    data = clazz.get_download_data_external(fiscal_week_year, model)
    columns = clazz.EXTERNAL_DOWNLOAD_NAME
    df = pd.DataFrame(data, columns=columns)
    df.to_csv(path, header=True, index=False, encoding='utf_8_sig')
    return path


def get_carrier_forecast_data_list_external(page_num: str, page_size: str):
    try:
        page_num = int(page_num)
        page_size = int(page_size)
    except ValueError:
        raise ErrorExcept(ErrCode.Param, "page & pagesize must be number")
    res, count = FastForecastWeek.get_by_rtm_external(StrRTMCarrier, page_num, page_size)
    data = [{"fiscal_week_year": x.fiscal_week_year, "fiscal_qtr_week_name": x.week_date} for x in res]
    return {"list": data, "count": count}


def get_carrier_forecast_data_external(
    fiscal_week_year: str, lob: list, model: list,
):
    if fiscal_week_year is None:
        raise ErrorExcept(ErrCode.Param, "fiscal_week_year required")
    try:
        fiscal_week_year = int(fiscal_week_year)
    except Exception:
        raise ErrorExcept(ErrCode.Param, "fiscal_week_year must be integer")
    # 获取数据
    result = AppFastForecastCarrierSoWa.get_data_by_fiscal_week_year_external(
        fiscal_week_year, StrRTMCarrier, lob, model
    )
    week_date_dict = FiscalYearWeek.get_cw_next_12_week(fiscal_week_year)
    # 表头信息整理
    cross_model =model
    if model is not None and len(model) != 0:
        cross_model = [x for x in model if x in model]
    columns = [{"key": x, "value": week_date_dict[x]} for x in week_date_dict.keys()]
    data = {x: {
        "lob": "iPhone",
        "model": x,
        **{str(x): None for x in week_date_dict.keys()}
    } for x in cross_model}
    total = {str(x): 0 for x in week_date_dict.keys()}
    # 格式化输出
    current_record = {}
    for item in result:
        # 以mpn为维度，序列化数据
        total[str(item.show_week)] += item.fcst or 0
        if item.model == current_record.get("model"):
            current_record[str(item.show_week)] = item.fcst and int(item.fcst)
        else:
            if current_record:
                data[current_record.get("model")].update(current_record)
            current_record = {
                "lob": item.lob,
                "model": item.model,
                **{x: None for x in week_date_dict.keys()},
                str(item.show_week): item.fcst and int(item.fcst),
            }
    else:
        data[current_record.get("model")].update(current_record)
    for key in total.keys():
        total[key] = int(total[key])
    return {"total": total, "list": list(data.values()), "columns": columns}


def download_carrier_forecast_data_external(fiscal_week_year: int,model:list):
    fiscal_qtr = AppFastForecastCarrierSoWa.get_fiscal_qtr_by_fiscal_week_year(fiscal_week_year)
    reseller = DimFastSoldToMappingCarrier.get_cn_name()
    if reseller is None:
        raise ErrorExcept(ErrCode.DBQueryError, "no reseller")
    path = os.path.dirname(os.path.dirname(os.path.abspath(__file__))) + f'/downloads/{g.get(PrsIdKey)}/carrier/'
    dir_name = os.path.dirname(path)
    if not os.path.exists(dir_name):
        os.makedirs(dir_name)
    if not os.path.exists(path):
        os.mkdir(path)
    path += f'预测信息_{reseller.sold_to_name_cn}_{fiscal_qtr}.csv'
    # if os.path.isfile(path):
    #     return path

    data = []
    result = AppFastForecastCarrierSoWa.get_download_data_by_fiscal_week_year_external(fiscal_week_year, StrRTMCarrier,model)
    current_record = {}
    week_index = 0
    for item in result:
        # 以cust-id和mpn为维度，序列化数据
        if item.mpn_id == current_record.get("MPN"):
            current_record[f"CW+{week_index}"] = item.fcst
        else:
            if current_record:
                # 数据不足13周的补None
                if week_index < 12:
                    for week_index in range(week_index, 13):
                        current_record[f"CW+{week_index}"] = None
                data.append(current_record)
                week_index = 0
            current_record = {
                AppFastForecastCarrierSoWa.EXTERNAL_COLUMN_NAME[0]: item.week_date,
                AppFastForecastCarrierSoWa.EXTERNAL_COLUMN_NAME[1]: item.lob,
                AppFastForecastCarrierSoWa.EXTERNAL_COLUMN_NAME[2]: item.model,
                AppFastForecastCarrierSoWa.EXTERNAL_COLUMN_NAME[3]: item.mpn_id,
                AppFastForecastCarrierSoWa.EXTERNAL_COLUMN_NAME[4]: item.sku,
                "CW": item.fcst,
            }
        week_index += 1
    else:
        if week_index < 12:
            for week_index in range(week_index, 13):
                current_record[f"CW+{week_index}"] = None
        data.append(current_record)

    df = pd.DataFrame.from_records(data)
    df.to_csv(path, header=True, index=False, encoding='utf_8_sig')
    return path
