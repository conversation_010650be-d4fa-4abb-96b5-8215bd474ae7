import uuid
from hashlib import md5

from data.allocation_cpf_run_data import *
from service.allocation_prepare_service import transfer_file_path
from util.const import *
from util.cpf_util import cpf_merge_file_path


def supply_protection_get_data_list(fiscal_week_year, strategy, project_code=None, mpn=None):
    res, count = AllocationRunSupplyProtection.get_protection_data(fiscal_week_year, strategy, project_code, mpn)
    res = sorted(res, key=lambda x: x.mpn)
    res = sorted(res, key=lambda x: x.project_code)
    res = sorted(res, key=lambda x: AllocationRunSupplyPreview.SALES_ORG_ORDER.index(x.sales_org))
    data = {"list": [get_row_dict(x) for x in res], "not_add": count}
    return data


def supply_protection_add_data_list(fiscal_week_year, strategy, add_ids):
    datas = AllocationRunSupplyPreview.get_by_ids(fiscal_week_year, add_ids)
    add_data = []
    for line in datas:
        add_data.append({
            "fiscal_week_year": fiscal_week_year,
            "strategy": strategy,
            "preview_id": line.id
        })
    if len(add_data) == 0:
        return ErrorExcept(ErrCode.Param, "invalid ids")
    AllocationRunSupplyProtection.bulk_save(add_data)
    AllocationRunSupplyOperateResult.reset_operate_status(fiscal_week_year, 2)
    return supply_protection_get_data_list(fiscal_week_year, strategy)


def supply_protection_delete_data_list(fiscal_week_year, strategy, remove_ids):
    AllocationRunSupplyProtection.delete_by_ids(fiscal_week_year, strategy, remove_ids)
    AllocationRunSupplyOperateResult.reset_operate_status(fiscal_week_year, 2)
    return supply_protection_get_data_list(fiscal_week_year, strategy)


def supply_protection_to_add_data_list(fiscal_week_year, project_code=None, mpn=None):
    res = AllocationRunSupplyProtection.get_to_add_data(fiscal_week_year, project_code, mpn)
    res = sorted(res, key=lambda x: x.mpn)
    res = sorted(res, key=lambda x: x.project_code)
    res = sorted(res, key=lambda x: AllocationRunSupplyPreview.SALES_ORG_ORDER.index(x.sales_org))
    return [get_row_dict(x) for x in res]


def get_special_supply_uploaded_file(fiscal_qtr_week_name: str):
    data = {}
    res = AllocationRunFile.get_file_by_week_category_group_step(
        fiscal_qtr_week_name,
        AllocationRunFileType.Upload,
        0,
        AllocationRunStep.SpecialSupply
    )
    if len(res) == 0:
        return data
    last_file = sorted(res, key=lambda x: x.create_time)[-1]
    return {"file_path": last_file.file_path, "file_name": last_file.file_name}


def delete_special_supply_uploaded_file(fiscal_qtr_week_name: str):
    fiscal_week_year = FiscalYearWeek.get_fiscal_week_year_by_week_date(fiscal_qtr_week_name)
    res = AllocationRunFile.get_file_by_week_category_group_step(
        fiscal_qtr_week_name,
        AllocationRunFileType.Upload,
        0,
        AllocationRunStep.SpecialSupply
    )
    last_file = sorted(res, key=lambda x: x.create_time)[-1]
    AllocationRunFile.delete_by_id(last_file.id)
    AllocationRunSpecialSupplyUpload.delete_by_week(fiscal_week_year)
    AllocationRunSupplyOperateResult.reset_operate_status(fiscal_week_year, 3)
    return "OK"


def get_run_supply_calculate_result(fiscal_week_year: str, step: int):
    data = {}
    res = AllocationRunSupplyOperateResult.get_by_week(fiscal_week_year)
    if step == 2:
        data["next"] = res.calculate_step2_id is not None
    elif step == 3:
        data["next"] = res.calculate_step3_id is not None
    return data


def upload_special_supply_file_service(upload_file, fiscal_qtr_week_name: str, person_id: str):
    fiscal_week_year = FiscalYearWeek.get_fiscal_week_year_by_week_date(fiscal_qtr_week_name)
    file_md5 = md5(upload_file.stream.read()).hexdigest()
    upload_file.stream.seek(0)
    # 上传文件
    upload_df = pd.read_excel(upload_file)
    # 模版文件
    template_file_path, _ = get_special_supply_template_service(fiscal_week_year)
    template_df = pd.read_excel(transfer_file_path(template_file_path))

    validate_special_supply(upload_df, template_df)
    # 空值/负数 置为0
    upload_df.fillna(0, inplace=True)
    replace_columns = AllocationRunSpecialSupplyColumns[11:]
    upload_df[replace_columns] = upload_df[replace_columns].clip(lower=0)
    raw_columns = AllocationRunSpecialSupplyDataColumns
    file_name = f"Special Supply_iPad_{fiscal_qtr_week_name}.xlsx"

    # 生成文件
    unique_name = f"{file_md5}.xlsx"
    file_path = cpf_merge_file_path(unique_name)
    upload_df.to_excel(file_path, index=False)

    # 保存文件信息
    run_file = AllocationRunFile(
        fiscal_qtr_week_name,
        fiscal_week_year,
        AllocationRunFileType.Upload,
        0,
        AllocationRunStep.SpecialSupply,
        f"/file/storage/{unique_name}",
        file_name, unique_name, person_id)
    file_recored_id = run_file.save()

    # 保存文件内容到数据库
    upload_df.columns = raw_columns

    upload_df["fiscal_week_year"] = fiscal_week_year
    upload_df["fiscal_qtr_week_name"] = fiscal_qtr_week_name
    current_time = datetime.now().strftime(DateTimeFormat)
    upload_df["create_time"] = current_time
    upload_df["update_time"] = current_time
    upload_df.replace({np.nan: None}, inplace=True)
    AllocationRunSpecialSupplyUpload.delete_by_week(fiscal_qtr_week_name)
    AllocationRunSpecialSupplyUpload.bulk_save(upload_df.to_dict("records"))

    AllocationRunSupplyOperateResult.reset_operate_status(fiscal_week_year, 3)
    # 需要返回前端文件名称
    query_file_info = AllocationRunFile.query_by_id(file_recored_id)
    return query_file_info.file_name


def validate_special_supply(upload_df: pd.DataFrame, template_df: pd.DataFrame):
    # 规则校验0: 表头校验, 必须与模版文件的表头一致
    if not list(upload_df.columns) == list(template_df.columns):
        raise ErrorExcept(ErrCode.FileUploadError, FileUploadError.CPFAllocationRunSupplyDataHeaderError)

    # 校验规则1: 上传文件的LOB / FPH L1列至ODQ列下的各行的行数和内容需要表2保持一致
    if len(upload_df) != len(template_df):
        raise ErrorExcept(
            ErrCode.FileUploadError,
            FileUploadError.CPFAllocationRunSupplyDataRowColumnError)
    overlap_columns = AllocationRunSpecialSupplyColumns[2:9]
    merged_df = pd.merge(upload_df[overlap_columns], template_df[overlap_columns],
                         how='outer', indicator=True)
    not_in_template = merged_df[merged_df['_merge'] != 'both']
    if not not_in_template.empty:
        raise ErrorExcept(ErrCode.FileUploadError, FileUploadError.CPFAllocationRunSupplyDataRowColumnError)

    # 校验规则2: 不允许上传没有任何修改的模版表（模版表下载后直接上传）
    exactly_equal = upload_df.equals(template_df)
    if exactly_equal:
        raise ErrorExcept(ErrCode.FileUploadError, FileUploadError.EmptyError)

    # 校验规则3: Special Supply CW+1至Special Supply CW+4列内填写内容仅能为0或空置或正整数，空置自动补0
    validata_columns = AllocationRunSpecialSupplyColumns[11:]
    upload_df.fillna(0, inplace=True)
    df = upload_df[upload_df[validata_columns].applymap(lambda x: isinstance(
        x, str) or not (isinstance(x, (int, float)) and x >= 0 and x == int(x))).any(axis=1)]
    non_positive_integer_and_zero_rows = df.index.tolist()
    if len(non_positive_integer_and_zero_rows):
        raise ErrorExcept(ErrCode.FileUploadError,
                          FileUploadError.InvalidRowsError
                          + ", ".join([str(idx + 2) for idx in non_positive_integer_and_zero_rows]))


def get_special_supply_template_service(fiscal_week_year: str):
    week_info = FiscalYearWeek.get_by_fiscal_week_year(fiscal_week_year)
    fiscal_week_year = week_info.fiscal_week_year
    fiscal_qtr_week_name = week_info.fiscal_qtr_week_name
    file_info = AllocationRunFile.get_file_by_week_category_group_step(
        fiscal_qtr_week_name,
        AllocationRunFileType.Template,
        0,
        AllocationRunStep.SpecialSupply
    )
    if file_info:
        file_path = file_info[0].file_path
        file_name = file_info[0].file_name
        return file_path, file_name

    # 若未生成过template，生成本周template
    # 查询模版内容
    df_list = AppFastAllocationRunSpecialSupplyTemplateWi.find_by_week(fiscal_week_year)
    df = df_list[AllocationRunSpecialSupplyDataColumns]
    df.columns = AllocationRunSpecialSupplyColumns
    file_name = f"Special Supply_Template_iPad_{fiscal_qtr_week_name}.xlsx"

    # 生成文件
    unique_name = f"{uuid.uuid4().hex}.xlsx"
    file_path = cpf_merge_file_path(unique_name)
    df.to_excel(file_path, index=False)

    # 保存文件信息
    run_file = AllocationRunFile(
        fiscal_qtr_week_name,
        fiscal_week_year,
        AllocationRunFileType.Template,
        0,
        AllocationRunStep.SpecialSupply,
        f'/file/storage/{unique_name}',
        file_name, unique_name, None)
    run_file.save()
    return f'/file/storage/{unique_name}', file_name


def allcation_run_go_next_service(fiscal_qtr_week_name: str,
                                  current_step: int):
    run_record = AllocationRun.query_by_week(fiscal_qtr_week_name)
    if not run_record:
        raise ErrorExcept(
            ErrCode.DBQueryNoData, 'No data'
        )
    
    latest_step = run_record[0].latest_step
    if  latest_step != current_step:
        raise ErrorExcept(
            ErrCode.System, 'can not go next'
        )
    process_step = run_record[0].process_type
    if process_step == AllocationRunProcessType.Manual:
        # 手动是从第一步跳到第五步
        AllocationRun.update_by_week_lob(
            fiscal_qtr_week_name, 'iPad',
            {'latest_step': AllocationRunStep.ManualAdjustment}
        )
    else:
        AllocationRun.update_by_week_lob(
            fiscal_qtr_week_name, 'iPad',
            {'latest_step': current_step + 1}
        )
    return 'ok'
