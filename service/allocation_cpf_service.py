import copy
import uuid
from shutil import copyfile

from util.cpf_util import *
from util.util import remove_file, _async
from util.rtm_demand_adjustment_const import RTMDemandAdjustmentUploadStatus
from util.util_operation import insert_operate_record
from service.allocation_prepare_service import *
from service.common import is_history_week_in_allocation
from data.allocation_cpf_lob_data import TblAllocationCpfLob, TblAllocationCpfConfig, OdsFastCpfSalesInputUpload


NOT_UPLOAD_OPERATE = 'not_upload_operate'
WAITING_TO_RE_UPLOAD_OPERATE = 'waiting_to_re_upload_operate'
SALES_INPUT_PUBLISH_START = 'sales_input_publish_start'
BTN_STATUS_ON = 1
BTN_STATUS_OFF = 0
BTN_ORIGIN_STATUS = {
    'rtm_file_download': BTN_STATUS_OFF,
    'operation_upload': BTN_STATUS_OFF,
    'operation_download': BTN_STATUS_OFF,
    'operation_re_run': BTN_STATUS_OFF,
    'operation_approve': BTN_STATUS_OFF,
}
ODS_FAST_CPF_SALES_INPUT_UPLOAD_COLUMN_DIC = {
    'RTML4': 'rtml4',
    'Sales Org': 'sales_org',
    'LOB / FPH L1': 'lob',
    'Prod / FPH L3': 'prod',
    'Project Code': 'project_code',
    'Nand': 'nand',
    'MPN / Apple Part #': 'mpn', 'ODQ': 'odq',
    'Customer Sold-to ID': 'sold_to_id',
    'Customer Sold-to Name': 'sold_to_name_en',
    'QTW Shipment Plan': 'qtw_shipment_plan',
    'CW Shipment Plan (Discrete)': 'cw_shipment_plan',
    'Sni (Cw-1)': 'sni_cw_minus_1',
    'CW+1 Shipment Plan (Discrete)': 'shipment_plan_cw1',
    'CW+2 Shipment Plan (Discrete)': 'shipment_plan_cw2',
    'EOH': 'eoh',
    'St/Ub Cw-1': 'st_ub_qty_cw_minus1',
    'ST/UB 5 Wk Bwd Avg': 'st_ub_5wk_bwd_avg',
    'Open Backlog over Published for CW+3 SP': 'open_backlog_over_published_sp_cw3',
    'Priority': 'priority',
    'Sales input Qty for CW+1': 'sales_input_qty_cw1',
    'Sales input Qty for CW+2': 'sales_input_qty_cw2',
    'Sales input Qty for CW+3': 'sales_input_qty_cw3',
    'Sales input Qty for CW+4': 'sales_input_qty_cw4',
    'Reason for CW+1': 'reason_cw1',
    'Reason for CW+2': 'reason_cw2',
    'Reason for CW+3': 'reason_cw3',
    'Reason for CW+4': 'reason_cw4',
    'Color': 'color', 
    'Comments': 'comments'
}


def get_allocation_cpf_lob_list_service(fiscal_qtr_week_name: str) -> list:
    cpf_lob_list = TblAllocationCpfLob.get_allocation_cpf_lob_list(fiscal_qtr_week_name)
    ret = []
    for cpf_lob in cpf_lob_list:
        data = {
            'id': cpf_lob.id,
            'fiscal_qtr_week_name': cpf_lob.fiscal_qtr_week_name,
            'fiscal_week_year': cpf_lob.fiscal_week_year,
            'phase': cpf_lob.phase,
            'last_operator': cpf_lob.last_operator,
            'last_updated': cpf_lob.last_updated.strftime(DateFormat) if cpf_lob.last_updated else None,
            'lob': cpf_lob.lob
        }
        ret.append(data)
    return ret


def get_allocation_prepare_cpf_list_service(cpf_lob_id: int) -> list:
    detail = TblAllocationCpfLob.get_by_id(cpf_lob_id)
    ret = []
    if not isinstance(detail, TblAllocationCpfLob):
        return ret

    not_upload_operate_cfg = TblAllocationCpfConfig.get_by_config_type(NOT_UPLOAD_OPERATE)
    waiting_to_re_upload_cfg = TblAllocationCpfConfig.get_by_config_type(WAITING_TO_RE_UPLOAD_OPERATE)
    if (not isinstance(not_upload_operate_cfg, TblAllocationCpfConfig)
        or not isinstance(waiting_to_re_upload_cfg, TblAllocationCpfConfig)):
        return ret
    
    prepare_list = TblAllocationPrepare.get_allocation_prepare_cpf_list(detail.fiscal_qtr_week_name, detail.lob)
    is_history_week = is_history_week_in_allocation(detail.fiscal_week_year)
    for item in prepare_list:
        btn_status = copy.copy(BTN_ORIGIN_STATUS)
        if is_history_week:
            btn_status['rtm_file_download'] = BTN_STATUS_ON
            btn_status['operation_download'] = BTN_STATUS_ON
        else:
            if (int(item['upload_status']) == RTMSalesInputUploadStatus.Uploaded 
                or int(item['upload_status']) == RTMSalesInputUploadStatus.Received):
                btn_status['rtm_file_download'] = BTN_STATUS_ON
                if int(item['rtm_file_download_by_cpf']) > 0:
                    btn_status['operation_upload'] = BTN_STATUS_ON
                    btn_status['operation_re_run'] = BTN_STATUS_ON
                    # 只有非error或者处理了error才启用approve
                    if (not item['first_phase_error']
                        or (item['first_phase_error'] and item['first_phase_error_process'])):
                        btn_status['operation_approve'] = BTN_STATUS_ON
            elif int(item['upload_status']) == RTMSalesInputUploadStatus.NotUpload:
                if not is_after_config_week_time(not_upload_operate_cfg.week, not_upload_operate_cfg.time):
                    btn_status['operation_re_run'] = BTN_STATUS_ON
            elif int(item['upload_status']) == RTMSalesInputUploadStatus.Completed:
                if not is_after_config_week_time(not_upload_operate_cfg.week, not_upload_operate_cfg.time):
                    btn_status['rtm_file_download'] = BTN_STATUS_ON
                    btn_status['operation_upload'] = BTN_STATUS_ON
                    btn_status['operation_download'] = BTN_STATUS_ON
                    btn_status['operation_re_run'] = BTN_STATUS_ON
                else:
                    btn_status['rtm_file_download'] = BTN_STATUS_ON
                    btn_status['operation_download'] = BTN_STATUS_ON
            elif int(item['upload_status']) == RTMSalesInputUploadStatus.NeedToReupload:
                if is_after_config_week_time(waiting_to_re_upload_cfg.week, waiting_to_re_upload_cfg.time):
                    btn_status['operation_upload'] = BTN_STATUS_ON
        res = {
            'data': item,
            'btn_status': btn_status
        }
        ret.append(res)
    return ret


def download_rtm_file_update_status_service(prepare_id, phase, upload_status):
    detail = TblAllocationPrepare.get_detail(prepare_id)
    upload_file_mapping = {}
    if phase == str(RTMAllocationPhase.SalesInput):
        upload_file_mapping = {
            'rtm_file_download_by_cpf': detail['rtm_file_download_by_cpf'] + 1
        }
        if detail['upload_status'] == str(RTMSalesInputUploadStatus.Uploaded):
            upload_file_mapping['upload_status'] = upload_status
    elif phase == str(RTMAllocationPhase.DemandAdjustment):
        if detail['third_phase_status'] == RTMDemandAdjustmentUploadStatus.Uploaded:
            upload_file_mapping['third_phase_status'] = upload_status
    TblAllocationPrepare.update_data_by_id(prepare_id, upload_file_mapping)


def update_prepare_status_re_run_service(prepare_id, upload_status, uploader):
    detail = TblAllocationPrepare.get_detail(prepare_id)
    upload_file_mapping = {
        'upload_status': upload_status,
        'rerun': detail['rerun'] + 1,
        'update_by': uploader,
        'rtm_file_download_by_cpf': 0
    }
    # 如果有Error状态且没有进行其他操作（confirm, upload），则记录该操作
    if detail['first_phase_error'] and not detail['first_phase_error_process']:
        upload_file_mapping['first_phase_error_process'] = FirstPhaseErrorProcess.Rerun
    TblAllocationPrepare.update_data_by_id(prepare_id, upload_file_mapping)
    # Re-run后需要删除CPF上传确认的数据
    OdsFastCpfSalesInputUpload.delete_by_week(detail.get(StrFiscalQtrWeekName), detail.get('rtm'), detail.get('lob'))
    
    insert_operate_record(detail[RtmKey], AllocationOperateCategory.SalesInputRerun, None, None, uploader, None)
    # 发送邮件通知
    re_run_send_mail(detail)


def cpf_upload_file_service(prepare_id, file, uploader):
    # 保存上传的文件
    origin_file_path, final_file_path, final_file_name = save_file_and_create_file_cpf(file)

    # 查询当前记录
    ap = TblAllocationPrepare.get_detail(prepare_id)
    try:
        # 获取模版DataFrame
        template_dataframe = get_template_dataframe(ap[RtmKey], ap[StrFiscalWeekYear])
        # 获取上传文件DataFrame
        upload_dataframe = pd.read_excel(file)
        # 与模版文件对比，校验文件
        check_flag = validate_with_template(upload_dataframe, template_dataframe)
        version = 0
        if ap['upload_file_version_cpf']:
            version = ap['upload_file_version_cpf']

        file_name = f"CP&F_{ap['rtm']}_Sales Input_{ap['lob']}_{ap['fiscal_qtr_week_name']}_" \
                    f"{version + 1}.xlsx"
        # 记录record表
        current_time = datetime.now().strftime(DateTimeFormat)
        record = TblUploadFileRecord(allocation_prepare_id=prepare_id,
                                     upload_by=uploader, upload_file_name=file_name)
        TblUploadFileRecord.save(record)
        # 根据规则重新生成的文件 记录record表
        if check_flag == 0:
            copyfile(origin_file_path, final_file_path)
        else:
            cpf_upload_final_file_generate(origin_file_path, final_file_path, template_dataframe)
    except ErrorExcept as e:
        remove_cpf_upload_file_data(origin_file_path=origin_file_path, final_file_path=final_file_path)
        raise e
    except Exception as e:
        remove_cpf_upload_file_data(origin_file_path=origin_file_path, final_file_path=final_file_path)
        raise e
    # 校验成功后，保存文件信息
    upload_file_mapping = {
        'upload_file_name_cpf': file_name,
        'upload_file_path_cpf': f'/file/storage/{final_file_name}',
        'upload_file_version_cpf': version + 1,
        'upload_by_cpf': uploader,
        'upload_at_cpf': current_time,
        'upload_status': RTMSalesInputUploadStatus.Completed,
        'update_by': uploader
    }
    # 如果已有approved数据，需要重置approve状态
    if ap['is_first_phase_approved']:
        upload_file_mapping['is_first_phase_approved'] = 0
    # 如果有Error状态且没有进行其他操作（confirm, rerun），则记录该操作
    if ap['first_phase_error'] and not ap['first_phase_error_process']:
        upload_file_mapping['first_phase_error_process'] = FirstPhaseErrorProcess.Upload
    TblAllocationPrepare.update_data_by_id(prepare_id, upload_file_mapping)
    # 修改tbl_allocation_cpf_lob 数据
    cpf_lob_data = {
        'last_operator': uploader,
        'last_updated': current_time
    }
    TblAllocationCpfLob.update_data(ap['fiscal_qtr_week_name'], ap['lob'], cpf_lob_data)
    # 数据保存到算法侧
    save_to_data_source(final_file_path, ap['fiscal_week_year'], ap['fiscal_qtr_week_name'], ap['rtm'], ap['lob'])


def remove_cpf_upload_file_data(origin_file_path, final_file_path):
    # 删除记录表 record
    remove_file(origin_file_path)
    remove_file(final_file_path)


def save_to_data_source(file_url, fiscal_week_year, fiscal_qtr_week_name, rtm, lob):
    try:
        df = pd.read_excel(file_url)
        # df = df.where(df.notnull(), None)
        df.replace({np.nan: None}, inplace=True)
        data = {}
        for i in df.columns:
            if i in ODS_FAST_CPF_SALES_INPUT_UPLOAD_COLUMN_DIC:
                data[i] = ODS_FAST_CPF_SALES_INPUT_UPLOAD_COLUMN_DIC[i]
        df.rename(columns=data, inplace=True)
        current_time = datetime.now().strftime(DateTimeFormat)
        keys = ODS_FAST_CPF_SALES_INPUT_UPLOAD_COLUMN_DIC.values()
        res = []
        for j, row in df.iterrows():
            final_data = {'fiscal_week_year': fiscal_week_year, 'fiscal_qtr_week_name': fiscal_qtr_week_name,
                          "create_time": current_time, "update_time": current_time, 'rtm': rtm}
            for key in keys:
                final_data[key] = None if row[key] == 'None' else row[key]
            res.append(final_data)
        # 删除当前周的数据
        OdsFastCpfSalesInputUpload.delete_by_week(fiscal_qtr_week_name, rtm, lob)
        OdsFastCpfSalesInputUpload.bulk_save(res)
    except Exception as e:
        logger.error(e, 'save to data source error:' + file_url)
        raise ErrorExcept(ErrCode.FileUploadError, '[system] save to data source error')


def cpf_upload_final_file_generate(origin_file_path, final_file_path, template_dataframe: pd.DataFrame):
    try:
        final_rows = []
        df = pd.read_excel(origin_file_path)
        
        # 修正上传文件的内容
        correct_df = correct_upload_file(df)
        
        # 补充数据
        correct_df = replenish_upload_dataframe(correct_df, template_dataframe,
                                on=TemplateFileHeader[:21],
                                adjust_columns=['Sales input Qty for CW+1','Sales input Qty for CW+2',
                                                'Sales input Qty for CW+3','Sales input Qty for CW+4'])
        
        add_dropdown_and_save(final_file_path, correct_df)
    except Exception as e:
        logger.error(e, 'cpf_upload_final_file_generate path error:' + origin_file_path)
        raise ErrorExcept(ErrCode.FileUploadError, '[system] cpf upload final file generate error')


def get_prepare_detail_by_id(prepare_id):
    return TblAllocationPrepare.get_detail(prepare_id)


def add_allocation_cpf_lob_service():
    phases = [1]
    lobs = ['iPad']
    cpf_lob_list = []
    fiscal_week_year = FiscalYearWeek.get_week_by_date(datetime.now().strftime(DateFormat))
    for phase in phases:
        for lob in lobs:
            cpf_lob = TblAllocationCpfLob(fiscal_qtr_week_name=fiscal_week_year.get('fiscal_qtr_week_name'),
                                          phase=phase, lob=lob,
                                          fiscal_week_year=fiscal_week_year.get('fiscal_week_year'))
            cpf_lob_list.append(cpf_lob)
    TblAllocationCpfLob.bulk_save(cpf_lob_list)


def download_merge_file_service(cpf_lob_id):
    detail = TblAllocationCpfLob.get_by_id(cpf_lob_id)
    if isinstance(detail, TblAllocationCpfLob):
        prepare_list = TblAllocationPrepare.get_allocation_prepare_cpf_list(detail.fiscal_qtr_week_name, detail.lob)
        file_name = f"Sales Input_{detail.lob}_{detail.fiscal_week_year}.xlsx"
        df = []  # 创建一个空的列表
        for item in prepare_list:
            url = item['upload_file_path_cpf']
            # 只能是completed状态且没有error标识
            state_flag = (item['upload_status'] == str(RTMSalesInputUploadStatus.Completed)) and \
                not (item['first_phase_error'] and not item['first_phase_error_process'])
            if not (url and state_flag):
                logger.info(f'SalesInput阶段不进行Download CP&F上传过的文件是{item["rtm"]}, Status是{item["upload_status"]}, ErrorStatus是{item["first_phase_error"]}, ErrorProcess是{item["first_phase_error_process"]}')
                continue
            upload_df = pd.read_excel(transfer_file_path(url))
            df.append(upload_df)
        if not df:
            # 下载空文件
            merge_x = pd.DataFrame([], columns=TemplateFileHeader)
        else:
            merge_x = pd.concat(df, ignore_index=True)
        merge_url = cpf_merge_file_path(file_name)
        # merge_x.to_excel(merge_url, index=False)
        add_dropdown_and_save(merge_url, merge_x)
        return merge_url
    return None


def re_run_send_mail(detail):
    current_time = datetime.now().strftime(DateTimeFormat)
    navigate_url = get_mail_navigate_url(
        detail['id'], detail[RtmKey], detail[StrFiscalQtrWeekName], detail[StrFiscalWeekYear], RTMAllocationPhase.SalesInput)
    TemplateEmail().sales_input_4(detail[StrFiscalQtrWeekName], detail[RtmKey], current_time, navigate_url)


def publish_service(cpf_lob_id, description, current_user):
    detail = TblAllocationCpfLob.get_by_id(cpf_lob_id)
    if isinstance(detail, TblAllocationCpfLob):
        fiscal_qtr_week_name = detail.fiscal_qtr_week_name
        lob = detail.lob
        # 合并cpf提交的文件
        prepare_list = TblAllocationPrepare.get_allocation_prepare_cpf_list(fiscal_qtr_week_name, lob)
        df = []  # 创建一个空的列表
        for item in prepare_list:
            url = item['upload_file_path_cpf']
            # 只能是completed状态且没有error标识
            state_flag = (item['upload_status'] == str(RTMSalesInputUploadStatus.Completed)) and \
                not (item['first_phase_error'] and not item['first_phase_error_process'])
            if not (url and state_flag):
                logger.info(f'SalesInput阶段未被Publish的CP&F上传过的文件是{item["rtm"]}, Status是{item["upload_status"]},ErrorStatus是{item["first_phase_error"]}, ErrorProcess是{item["first_phase_error_process"]}')
                continue
            upload_df = pd.read_excel(transfer_file_path(url))
            df.append(upload_df)
        file_name = f"GC {lob} Sales Input - {fiscal_qtr_week_name}.xlsx"
        merge_url = cpf_merge_file_path(file_name)
        if not df:
            # 下载空文件
            merge_x = pd.DataFrame([], columns=TemplateFileHeader)
        else:
            merge_x = pd.concat(df, ignore_index=True)
        add_dropdown_and_save(merge_url, merge_x)
        TemplateEmail().sales_input_5(fiscal_qtr_week_name, description, current_user, [merge_url])
        insert_operate_record(None, AllocationOperateCategory.SalesInputPublish, None, None, current_user, None)


def fiscal_qtr_week_name_list_service(lob):
    return TblAllocationCpfLob.get_cpf_lob_list(lob)


def publish_status_service(cpf_lob_id):
    detail = TblAllocationCpfLob.get_by_id(cpf_lob_id)
    if isinstance(detail, TblAllocationCpfLob):
        # 判断是否超出当前时间周
        is_history_week = is_history_week_in_allocation(detail.fiscal_week_year)
        if is_history_week:
            return False
        get_sales_input_publish_start = TblAllocationCpfConfig.get_by_config_type(SALES_INPUT_PUBLISH_START)
        if isinstance(get_sales_input_publish_start, TblAllocationCpfConfig):
            if is_after_config_week_time(get_sales_input_publish_start.week, get_sales_input_publish_start.time):
                return True
    return False


def sales_input_approve_service(rtm, fiscal_week_year, uploader, uploader_email):
    # 版本迭代之后，所有上传的文件都需要根据template进行补齐，所以rtm上传后的文件和approve后的内容一致
    # 所以可进行approve的优化
    logger.info(f'start to approve >>> {uploader} at {datetime.now().strftime(DateTimeFormat)}')
    
    # 获取rtm上传的文件路径
    rtm_record = TblAllocationPrepare.find_by_fiscal_week_year_and_rtm(fiscal_week_year, rtm)
    
    new_file_name = f'{uuid.uuid4().hex}.xlsx'
    new_file_path = cpf_merge_file_path(new_file_name)
    version = 0
    if rtm_record.upload_file_version_cpf:
        version = rtm_record.upload_file_version_cpf

    file_name = f"CP&F_{rtm_record.rtm}_Sales Input_{rtm_record.lob}_{rtm_record.fiscal_qtr_week_name}_" \
                f"{version + 1}.xlsx"
    
    # 记录record表
    record = TblUploadFileRecord(rtm_record.id, file_name,uploader)
    TblUploadFileRecord.save(record)
    
    # 拷贝rtm上传后的文件为cpf的文件
    origin_file_path = transfer_file_path(rtm_record.upload_file_path)
    copyfile(origin_file_path, new_file_path)
    
    # 读取文件内容
    df_approved = pd.read_excel(new_file_path)
    # 字段名称转换
    file_header = list(ODS_FAST_CPF_SALES_INPUT_UPLOAD_COLUMN_DIC.keys())
    raw_header = list(ODS_FAST_CPF_SALES_INPUT_UPLOAD_COLUMN_DIC.values())
    df_approved = df_approved[file_header]
    df_approved.columns = raw_header
    current_time = datetime.now().strftime(DateTimeFormat)
    df_approved['fiscal_week_year'] = fiscal_week_year
    df_approved['fiscal_qtr_week_name'] = rtm_record.fiscal_qtr_week_name
    df_approved['create_time'] = current_time
    df_approved['update_time'] = current_time
    df_approved['rtm'] = rtm
    df_approved['version'] = 1

    # 上传至数据库，数据保存到算法侧
    df_approved.replace({np.nan: None}, inplace=True)
    OdsFastCpfSalesInputUpload.delete_by_week(rtm_record.fiscal_qtr_week_name, rtm, 'iPad')
    OdsFastCpfSalesInputUpload.bulk_save(df_approved.to_dict("records"))

    # 校验成功后，保存文件信息
    upload_file_mapping = {
        'upload_file_name_cpf': file_name,
        'upload_file_path_cpf': f'/file/storage/{new_file_name}',
        'upload_file_version_cpf': version + 1,
        'upload_by_cpf': uploader,
        'upload_at_cpf': current_time,
        'upload_status': RTMSalesInputUploadStatus.Completed,
        'update_by': uploader,
        'is_first_phase_approved': 1
    }
    TblAllocationPrepare.update_data_by_id(rtm_record.id, upload_file_mapping)
    
    # 修改tbl_allocation_cpf_lob 数据
    cpf_lob_data = {
        'last_operator': uploader,
        'last_updated': current_time
    }
    TblAllocationCpfLob.update_data(rtm_record.fiscal_qtr_week_name, rtm_record.lob, cpf_lob_data)
    
    # 添加操作记录
    insert_operate_record(rtm, AllocationOperateCategory.SalesInputApprove, rtm_record.upload_file_name, rtm_record.upload_file_path, uploader, uploader_email)
    logger.info(f'approve successfully >>> {uploader} at {datetime.now().strftime(DateTimeFormat)}, file_name {new_file_name}')
    return "ok"


def sales_input_error_confirm_service(rtm, fiscal_week_year, uploader, uploader_email):
    # 添加操作记录-点击即记录，如果有对应的文件信息，则confirm成功
    insert_operate_record(rtm, AllocationOperateCategory.SalesInputErrorConfirm, None, None, uploader, uploader_email)
    
    rtm_record = TblAllocationPrepare.find_by_fiscal_week_year_and_rtm(fiscal_week_year, rtm)
    # 判断当前RTM第一阶段的状态是否需要confirm
    if not rtm_record.first_phase_error:
        logger.info(f'{rtm} no error in {fiscal_week_year}')
        return None
    
    if rtm_record.first_phase_error and rtm_record.first_phase_error_process:
        logger.info(f'{rtm} error has been processed {rtm_record.first_phase_error_process} in {fiscal_week_year}')
        return None

    if rtm_record.upload_status \
        and int(rtm_record.upload_status) not in [RTMSalesInputUploadStatus.Uploaded,
                                                  RTMSalesInputUploadStatus.Received,
                                                  RTMSalesInputUploadStatus.Completed]:
        logger.info(f'{rtm} {rtm_record.upload_status} invalid state in {fiscal_week_year}')
        return None

    # 先查询有哪些是小于周一的open backlog
    less_data = get_open_backlog_less_than_monday(rtm, fiscal_week_year)

    '''
    - 如果CPF已经approve/upload，则直接将cpf文件中的内容进行扣减（
      因为存在cpf上传内容与rtm不通的情况，所以不能直接处理rtm文件）
    - 如果CPF还未处理，则需要将rtm的文件副本扣减之后保存为cpf的文件并存储数据库
    '''
    ColumnSoldToID = 'Customer Sold-to ID'
    ColumnMPN = 'MPN / Apple Part #'
    ColumnPriority = 'Priority'
    source_file_path = ''
    if rtm_record.upload_file_path_cpf \
        and rtm_record.upload_status == str(RTMSalesInputUploadStatus.Completed):
        source_file_path = rtm_record.upload_file_path_cpf
    else:
        if rtm_record.upload_file_path:
            source_file_path = rtm_record.upload_file_path
    if not source_file_path:
        return None
     
    need_deduct_data = pd.read_excel(transfer_file_path(source_file_path))
    for item in less_data:
        # 指定需要加和的列
        cols_to_sum = ['Sales input Qty for CW+4',
                       'Sales input Qty for CW+3',
                       'Sales input Qty for CW+2',
                       'Sales input Qty for CW+1']
        df = need_deduct_data[(need_deduct_data[ColumnSoldToID] == item[0]) & (need_deduct_data[ColumnMPN] == item[1])]
        # # 3行4列加和 - 新open backlog
        difference = df[cols_to_sum].sum().sum() - item[2]
        
        for week in cols_to_sum:
            for priority in ['P2', 'P1', 'P0']:
                cell_value = df.loc[df[ColumnPriority] == priority, week].values[0]
                if cell_value > 0:
                    row_condition = (need_deduct_data[ColumnSoldToID] == item[0]) & \
                                    (need_deduct_data[ColumnMPN] == item[1]) & \
                                    (need_deduct_data[ColumnPriority] == priority)
                    # 进行扣减
                    if cell_value - difference <= 0:
                        need_deduct_data.loc[row_condition, week] = 0
                        difference -= cell_value
                    else:
                        # 取该行的ODQ值
                        odq = need_deduct_data[row_condition]['ODQ'].values[0]
                        # 这里需要将扣减完的结果除以ODQ，然后向下取整，再乘以ODQ
                        need_deduct_data.loc[row_condition, week] = (cell_value - difference) // odq * odq
                        difference = 0
                if not difference:
                    break  # 终止内层循环
            else:
                continue  # 内层循环未被终止时，继续外层循环
            break  # 内层循环被终止时，终止外层循环

    new_file_name = f'{uuid.uuid4().hex}.xlsx'
    new_file_path = cpf_merge_file_path(new_file_name)
    version = 0
    if rtm_record.upload_file_version_cpf:
        version = rtm_record.upload_file_version_cpf

    file_name = f"CP&F_{rtm_record.rtm}_Sales Input_{rtm_record.lob}_{rtm_record.fiscal_qtr_week_name}_" \
                f"{version + 1}.xlsx"
    # 记录record表
    current_time = datetime.now().strftime(DateTimeFormat)
    record = TblUploadFileRecord(allocation_prepare_id=rtm_record.id,
                                 upload_by=uploader, upload_file_name=file_name)
    TblUploadFileRecord.save(record)
    
    # 保存新文件
    add_dropdown_and_save(new_file_path, need_deduct_data)
    
    # 更新记录
    upload_file_mapping = {
        'upload_file_name_cpf': file_name,
        'upload_file_path_cpf': f'/file/storage/{new_file_name}',
        'upload_file_version_cpf': version + 1,
        'upload_by_cpf': uploader,
        'upload_at_cpf': current_time,
        'upload_status': RTMSalesInputUploadStatus.Completed,
        'update_by': uploader,
        'first_phase_error_process': FirstPhaseErrorProcess.Confirm
    }
    TblAllocationPrepare.update_data_by_id(rtm_record.id, upload_file_mapping)
    
    # 修改tbl_allocation_cpf_lob 数据
    cpf_lob_data = {
        'last_operator': uploader,
        'last_updated': current_time
    }
    TblAllocationCpfLob.update_data(rtm_record.fiscal_qtr_week_name, rtm_record.lob, cpf_lob_data)
    
    # 数据保存到算法侧
    save_to_data_source(new_file_path, rtm_record.fiscal_week_year, rtm_record.fiscal_qtr_week_name, rtm_record.rtm, rtm_record.lob)
    
    # 添加操作记录
    insert_operate_record(rtm, AllocationOperateCategory.SalesInputErrorConfirm, file_name, new_file_path, uploader, uploader_email)


@_async
def cpf_latest_update(operator, fiscal_qtr_week_name: str, lob: str = 'iPad'):
    '''更新最新的操作人和操作时间'''
    current_time = datetime.now().strftime(DateTimeFormat)
    update_data = {
        'last_operator': operator,
        'last_updated': current_time
    }
    TblAllocationCpfLob.update_data(fiscal_qtr_week_name, lob, update_data)
