import uuid
from itertools import groupby

from data.allocation_cpf_lob_data import TblAllocationCpfLob, TblAllocationCpfConfig
from data.cpf_sell_in_demand_data import TblAllocationPrepareFile, AppFastAllocationDemandSellInMonoWi, \
    AppFastAllocationDemandSellInMultiOnlineWi, AppFastAllocationDemandSellInOtherWi, \
    AppFastaAllocationDemandSubmissionMonoWi
from data.allocation_prepare_data import TblAllocationPrepare
from data.fiscal_year_week import FiscalYearWeek
from data.allocation_prepare_gc_dmp_data import AppFastAllocationDemandSubmissionMono
from util.const import *
from util.cpf_const import *
from util.cpf_util import *
from util.conf import logger
from util.file_util import save_df_to_md5_name
from util.util_operation import insert_operate_record
from util.template_email_sender import TemplateEmail
from service.allocation_prepare_service import get_current_time_with_mock,\
    is_history_week_in_allocation, transfer_file_path


DEMAND_WITH_TAGS_CATEGORY = 1
DEMAND_HR_ONLY_CATEGORY = 2
DEMAND_HR_ONLY_TEMPLATE_CATEGORY = 0
SELL_IN_DEMAND_COMPLETED = 3
SELL_IN_DEMAND_ERROR = 4


def get_sell_in_demand_list_service(cpf_lob_id: int) -> list:
    detail = TblAllocationCpfLob.get_by_id(cpf_lob_id)
    ret = []
    is_history_week = False
    if isinstance(detail, TblAllocationCpfLob):
        fiscal_qtr_week_name = detail.fiscal_qtr_week_name
        lob = detail.lob
        cpf_list = TblAllocationPrepare.get_allocation_prepare_cpf_list(fiscal_qtr_week_name, lob)
        file_list = TblAllocationPrepareFile.get_allocation_prepare_file_list(detail.fiscal_week_year, str(CPFAllocationPhaseOneModule.SellinDemand), lob)
        file_sort = sorted(file_list, key=lambda x: x["rtm"])
        file_group = groupby(file_sort, key=lambda x: (x["rtm"]))
        file_dict = {key: list(group) for key, group in file_group}
        is_history_week = is_history_week_in_allocation(detail.fiscal_week_year)
        for item in cpf_list:
            data = {
                "id": item['id'],
                "rtm": item['rtm'],
                "status": item['second_phase_status'],
                "fiscal_week_year": item['fiscal_week_year'],
                "fiscal_qtr_week_name": item['fiscal_qtr_week_name'],
                'demand_with_tags_file_path': '',
                'demand_with_tags_file_name': '',
                'demand_with_tags_update_time': '',
                'demand_hr_only_file_path': '',
                'demand_hr_only_file_name': '',
                'demand_hr_only_update_time': '',
            }
            if item['rtm'] in file_dict:
                file_data_list = file_dict[item['rtm']]
                filter_tags = list(filter_set(file_data_list, DEMAND_WITH_TAGS_CATEGORY))
                filter_hr_only = list(filter_set(file_data_list, DEMAND_HR_ONLY_CATEGORY))
                if filter_tags and len(filter_tags) == 1:
                    data['demand_with_tags_file_path'] = filter_tags[0]['upload_file_path']
                    data['demand_with_tags_file_name'] = filter_tags[0]['upload_file_name']
                    data['demand_with_tags_update_time'] = filter_tags[0]['upload_at'].strftime(DateFormat)
                if filter_hr_only and len(filter_hr_only) == 1:
                    data['demand_hr_only_file_path'] = filter_hr_only[0]['upload_file_path']
                    data['demand_hr_only_file_name'] = filter_hr_only[0]['upload_file_name']
                    data['demand_hr_only_update_time'] = filter_hr_only[0]['upload_at'].strftime(DateFormat)
            ret.append(data)
    return {"list": ret, "now": get_current_time_with_mock(),
            "is_history_week": is_history_week}


def filter_set(aquarium_creatures, search_string):
    def iterator_func(x):
        if x['category'] == search_string:
            return True
        return False

    return filter(iterator_func, aquarium_creatures)


def sell_in_demand_with_tags_only_name_service():
    env = os.environ.get('DB_ENV')
    if not env:
        env = os.environ.get('ENV')
    try:
        fiscal_week_year = FiscalYearWeek.get_week_by_date(datetime.now().strftime(DateFormat))
        fiscal_qtr_week_name = fiscal_week_year['fiscal_qtr_week_name']
        fiscal_week_year = fiscal_week_year['fiscal_week_year']
        demand_rtm_list = ['Mono', 'Multi', 'Online', 'Carrier', 'ENT', 'EDU', 'HK/TW Carrier', 'HK/TW RP']
        data_list = []
        for lob in cpf_lobs:
            for rtm in demand_rtm_list:
                rtm_name = rtm.replace('/', '_')
                file_name = f'{rtm_name}_Demand with Tags_{lob}_{fiscal_qtr_week_name}.xlsx'
                data = {
                    'upload_file_name': file_name,
                    'fiscal_qtr_week_name': fiscal_qtr_week_name,
                    'fiscal_week_year': fiscal_week_year,
                    'operate_phase': str(CPFAllocationPhaseOneModule.SellinDemand),
                    'rtm': rtm,
                    'lob': lob,
                    'category': DEMAND_WITH_TAGS_CATEGORY,
                    'upload_at': datetime.now().strftime(DateTimeFormat),
                    'create_time': datetime.now().strftime(DateTimeFormat),
                    'update_time': datetime.now().strftime(DateTimeFormat)
                }
                data_list.append(data)
            # 删除with tags 数据
            TblAllocationPrepareFile.delete(fiscal_week_year, str(CPFAllocationPhaseOneModule.SellinDemand), None, lob, DEMAND_WITH_TAGS_CATEGORY)
            TblAllocationPrepareFile.bulk_save(data_list)
            sell_in_mono_hr_only(fiscal_qtr_week_name, fiscal_week_year, lob)
            # 修改second_phase_status 状态
            update_data = {
                'second_phase_status': SELL_IN_DEMAND_COMPLETED
            }
            TblAllocationPrepare.update_by_week_lob(fiscal_qtr_week_name, lob, update_data)
            num = AppFastaAllocationDemandSubmissionMonoWi.get_sell_in_demand_count(fiscal_qtr_week_name,
                                                                                    lob, None,
                                                                                    exceed_sales_input='N')
            if num:
                # 存在exceed_sales_input = n 的数据 状态改成Error
                update_mono_data = {
                    'second_phase_status': SELL_IN_DEMAND_ERROR
                }
                TblAllocationPrepare.update_by_week_rtm_lob(fiscal_qtr_week_name, 'Mono', lob, update_mono_data)
        send_file_email(f'sell in demand, env:{env}', f'success', dev_email_receivers, [], '')
    except Exception as e:
        send_file_email(f'sell in demand, env:{env}', f'error:{e}', dev_email_receivers, [], '')


def sell_in_demand_with_tags(fiscal_dt):
    env = os.environ.get('ENV')
    
    try:
        if not fiscal_dt:
            fiscal_dt = datetime.now().strftime(DateFormat)
        fiscal_week_year = FiscalYearWeek.get_week_by_date(fiscal_dt)
        fiscal_qtr_week_name = fiscal_week_year['fiscal_qtr_week_name']
        fiscal_week_year = fiscal_week_year['fiscal_week_year']
        for lob in cpf_lobs:
            sell_in_mono_with_tags(fiscal_qtr_week_name, fiscal_week_year, lob)
            # sell_in_mono_hr_only(fiscal_qtr_week_name, fiscal_week_year, lob)
            sell_in_multi_online_with_tags(fiscal_qtr_week_name, fiscal_week_year, lob)
            sell_in_other_with_tags(fiscal_qtr_week_name, fiscal_week_year, lob)
            
            all_rtm_record = TblAllocationPrepare.find_by_week_and_rtm_list(fiscal_week_year, AllocationRTM.get_all_rtms())
            # 修改second_phase_status 状态
            update_data = {
                'second_phase_status': SELL_IN_DEMAND_COMPLETED
            }
            for item in all_rtm_record:
                if item.rtm == AllocationRTM.Mono:
                    # 需要判断status和HR Only文件
                    hr_only_file_info = TblAllocationPrepareFile.get_allocation_prepare_file(item.fiscal_week_year, str(RTMAllocationPhase.DemandSubmission),
                                                                       'iPad', PrepareSubmissionFileCategory.HrOnly, item.rtm)
                    # 如果状态是InProgress, 并且生成了HR Only文件，才要更新为Completed，其他状态则不需要更新
                    if item.second_phase_status == RTMDemandSubmissionStatus.InProcess \
                        and hr_only_file_info:
                        TblAllocationPrepare.update_by_week_rtm_lob(item.fiscal_qtr_week_name, item.rtm, item.lob, update_data)
                elif item.rtm in [AllocationRTM.Multi, AllocationRTM.Online]:
                    hr_only_file_info = TblAllocationPrepareFile.get_allocation_prepare_file(item.fiscal_week_year, str(RTMAllocationPhase.DemandSubmission),
                                                                       'iPad', PrepareSubmissionFileCategory.HrOnly, item.rtm)
                    if item.second_phase_status == RTMDemandSubmissionStatus.InProcess and hr_only_file_info:
                        TblAllocationPrepare.update_by_week_rtm_lob(item.fiscal_qtr_week_name, item.rtm, item.lob, update_data)
                    elif item.second_phase_status == RTMDemandSubmissionStatus.NotGenerated and not hr_only_file_info:
                        TblAllocationPrepare.update_by_week_rtm_lob(item.fiscal_qtr_week_name, item.rtm, item.lob, 
                                                                    {"second_phase_status": RTMDemandSubmissionStatus.Error})
                else:
                    if item.second_phase_status != SELL_IN_DEMAND_COMPLETED:
                        TblAllocationPrepare.update_by_week_rtm_lob(item.fiscal_qtr_week_name, item.rtm, item.lob, update_data)
                
        send_file_email(f'sell in demand, env:{env}', f'success', dev_email_receivers, [], '')
    except Exception as e:
        send_file_email(f'sell in demand, env:{env}', f'error:{e}', dev_email_receivers, [], '')


def refresh_sell_in_demand_with_tags_mono_hr_only(fiscal_dt):
    '''
    仅重刷数据，不更新状态
    '''
    env = os.environ.get('ENV')
    
    try:
        if not fiscal_dt:
            fiscal_dt = datetime.now().strftime(DateFormat)
        fiscal_week_year = FiscalYearWeek.get_week_by_date(fiscal_dt)
        fiscal_qtr_week_name = fiscal_week_year['fiscal_qtr_week_name']
        fiscal_week_year = fiscal_week_year['fiscal_week_year']
        for lob in cpf_lobs:
            sell_in_mono_with_tags(fiscal_qtr_week_name, fiscal_week_year, lob)
            sell_in_mono_hr_only(fiscal_qtr_week_name, fiscal_week_year, lob)
            sell_in_multi_online_with_tags(fiscal_qtr_week_name, fiscal_week_year, lob)
            sell_in_other_with_tags(fiscal_qtr_week_name, fiscal_week_year, lob)
        send_file_email(f'refresh sell in demand with tags and mono hr only, env:{env}', f'success', dev_email_receivers, [], '')
    except Exception as e:
        send_file_email(f'refresh sell in demand with tags and mono hr only, env:{env}', f'error:{e}', dev_email_receivers, [], '')


def sell_in_mono_with_tags(fiscal_qtr_week_name, fiscal_week_year, lob):
    pd_list = AppFastAllocationDemandSellInMonoWi.get_sell_in_demand_list(fiscal_qtr_week_name, lob, hr_lr=None)
    df_list = sell_in_field(pd_list, AppFastAllocationDemandSellInMonoWi)
    df = pd.DataFrame(columns=PrepareCollectionWithTagsHeaderDict, data=df_list)
    file_name = f'Mono_Demand with Tags_{lob}_{fiscal_qtr_week_name}.xlsx'
    hash_file_name = save_df_to_md5_name(df, create_path(cpf_file_url_prefix['uploads']), 'xlsx')
    file_path = cpf_merge_file_path(hash_file_name)
    with pd.ExcelWriter(file_path) as writer:
        df.to_excel(writer, index=False)
    save_prepare_file(fiscal_qtr_week_name=fiscal_qtr_week_name, fiscal_week_year=fiscal_week_year,
                      rtm='Mono', lob=lob, upload_file_name=file_name, upload_file_path=file_path,
                      category=DEMAND_WITH_TAGS_CATEGORY)


def generate_sell_in_demand_mono_hr_only(fiscal_dt):
    if not fiscal_dt:
        fiscal_dt = datetime.now().strftime(DateFormat)
    fiscal_week_year_dict = FiscalYearWeek.get_week_by_date(fiscal_dt)
    fiscal_qtr_week_name = fiscal_week_year_dict['fiscal_qtr_week_name']
    fiscal_week_year = fiscal_week_year_dict['fiscal_week_year']
    sell_in_mono_hr_only(fiscal_qtr_week_name, fiscal_week_year, 'iPad')
    change_mono_status_after_demand_hr_only_generated(fiscal_qtr_week_name, fiscal_week_year)


def change_mono_status_after_demand_hr_only_generated(fiscal_qtr_week_name: str, fiscal_week_year: int):
    submission_data = AppFastAllocationDemandSubmissionMono.get_by_rtm_lob(AllocationRTM.Mono, fiscal_week_year, 'iPad')
    mono_record = TblAllocationPrepare.find_by_fiscal_week_year_and_rtm(fiscal_week_year, AllocationRTM.Mono)
    if submission_data['exceed_sales_input'].str.contains('N').any():
        # 如果是Completed或者是Error状态，则不更新与发送邮件
        if mono_record.second_phase_status != RTMDemandSubmissionStatus.Completed and \
            mono_record.second_phase_status != RTMDemandSubmissionStatus.Error:
            # 如果有任意一个N，则更改状态为Error
            TblAllocationPrepare.update_by_week_rtm_lob(
                fiscal_qtr_week_name, str(AllocationRTM.Mono), 'iPad',
                {"second_phase_status": RTMDemandSubmissionStatus.Error})
            # 邮件通知CPF业务方 / Mono
            cpf_recored = TblAllocationCpfLob.get_by_week_lob(fiscal_week_year, 'iPad')
            if len(cpf_recored) > 0:
                navigate_url = get_cpf_mail_navigate_url(
                    cpf_recored[0].id, fiscal_qtr_week_name, fiscal_week_year, 'iPad')
            else:
                navigate_url = get_navigate_url_list_page('cpf')
            TemplateEmail().sell_in_demand_11(fiscal_qtr_week_name, navigate_url)
            TemplateEmail().sell_in_demand_12(fiscal_qtr_week_name)
    else:
        if mono_record.second_phase_status == RTMDemandSubmissionStatus.NotGenerated:
            TblAllocationPrepare.update_by_week_rtm_lob(
                fiscal_qtr_week_name, str(AllocationRTM.Mono), 'iPad',
                {"second_phase_status": RTMDemandSubmissionStatus.InProcess})


def sell_in_mono_hr_only(fiscal_qtr_week_name, fiscal_week_year, lob):
    pd_list = AppFastaAllocationDemandSubmissionMonoWi.get_sell_in_demand_list(fiscal_qtr_week_name, lob, hr_lr='HR')
    df = pd_list[SellInDemandHrOnlyTemplateFileRawHeader]
    df.columns = SellInDemandHrOnlyTemplateFileHeader
    file_name = f'Mono_Demand(HR Only)_{lob}_{fiscal_qtr_week_name}.xlsx'
    hash_file_name = save_df_to_md5_name(df, create_path(cpf_file_url_prefix['uploads']), 'xlsx')
    file_path = cpf_merge_file_path(hash_file_name)
    with pd.ExcelWriter(file_path) as writer:
        df.to_excel(writer, index=False)
    save_prepare_file(fiscal_qtr_week_name=fiscal_qtr_week_name, fiscal_week_year=fiscal_week_year,
                      rtm='Mono', lob=lob, upload_file_name=file_name, upload_file_path=file_path,
                      category=DEMAND_HR_ONLY_CATEGORY)


def sell_in_multi_online_with_tags(fiscal_qtr_week_name, fiscal_week_year, lob):
    rtm_list = [AllocationRTM.Multi, AllocationRTM.Online]
    for rtm in rtm_list:
        pd_list = AppFastAllocationDemandSellInMultiOnlineWi.get_sell_in_demand_list(fiscal_qtr_week_name, lob, rtm)
        df_list = sell_in_field(pd_list, AppFastAllocationDemandSellInMultiOnlineWi)
        df = pd.DataFrame(columns=PrepareCollectionWithTagsHeaderDict, data=df_list)
        file_name = f'{rtm}_Demand with Tags_{lob}_{fiscal_qtr_week_name}.xlsx'.replace('/', '_')
        hash_file_name = save_df_to_md5_name(df, create_path(cpf_file_url_prefix['uploads']), 'xlsx')
        file_path = cpf_merge_file_path(hash_file_name)
        with pd.ExcelWriter(file_path) as writer:
            df.to_excel(writer, index=False)
        save_prepare_file(fiscal_qtr_week_name=fiscal_qtr_week_name, fiscal_week_year=fiscal_week_year,
                          rtm=rtm, lob=lob, upload_file_name=file_name, upload_file_path=file_path,
                          category=DEMAND_WITH_TAGS_CATEGORY)


def sell_in_other_with_tags(fiscal_qtr_week_name, fiscal_week_year, lob):
    rtm_list = [AllocationRTM.Carrier, AllocationRTM.ENT, AllocationRTM.EDU,
                AllocationRTM.HKTWCarrier, AllocationRTM.HKTWRP, 
                AllocationRTM.HKTWEDU, AllocationRTM.HKTWENT]
    for rtm in rtm_list:
        pd_list = AppFastAllocationDemandSellInOtherWi.get_sell_in_demand_list(fiscal_qtr_week_name, lob, rtm)
        df_list = sell_in_field(pd_list, AppFastAllocationDemandSellInOtherWi)
        df = pd.DataFrame(columns=PrepareCollectionWithTagsHeaderDict, data=df_list)
        file_name = f'{rtm}_Demand with Tags_{lob}_{fiscal_qtr_week_name}.xlsx'.replace('/', '_')
        hash_file_name = save_df_to_md5_name(df, create_path(cpf_file_url_prefix['uploads']), 'xlsx')
        file_path = cpf_merge_file_path(hash_file_name)
        with pd.ExcelWriter(file_path) as writer:
            df.to_excel(writer, index=False)
        save_prepare_file(fiscal_qtr_week_name=fiscal_qtr_week_name, fiscal_week_year=fiscal_week_year,
                          rtm=rtm, lob=lob,
                          upload_file_name=file_name,
                          upload_file_path=file_path,
                          category=DEMAND_WITH_TAGS_CATEGORY)


def save_prepare_file(fiscal_qtr_week_name, fiscal_week_year, lob, rtm, upload_file_path, upload_file_name, category):
    detail = TblAllocationPrepareFile.get_allocation_prepare_file(fiscal_week_year, str(CPFAllocationPhaseOneModule.SellinDemand), lob, category, rtm)
    upload_file_path = f'{cpf_file_url_prefix["file_storage"]}/{os.path.split(upload_file_path)[-1]}'
    if detail:
        update_data = {
            'upload_file_path': upload_file_path,
            'upload_file_name': upload_file_name,
            'upload_by': 'system',
            'update_by': 'system',
            'update_time': datetime.now().strftime(DateTimeFormat)
        }
        TblAllocationPrepareFile.update_data(fiscal_week_year, str(CPFAllocationPhaseOneModule.SellinDemand), lob, rtm, category, update_data)
    else:
        data_list = []
        data = {
            'upload_file_path': upload_file_path,
            'upload_file_name': upload_file_name,
            'fiscal_qtr_week_name': fiscal_qtr_week_name,
            'fiscal_week_year': fiscal_week_year,
            'operate_phase': str(CPFAllocationPhaseOneModule.SellinDemand),
            'rtm': rtm,
            'lob': lob,
            'category': category,
            'upload_by': 'system',
            'update_by': 'system',
            'upload_at': datetime.now().strftime(DateTimeFormat),
            'create_time': datetime.now().strftime(DateTimeFormat),
            'update_time': datetime.now().strftime(DateTimeFormat)
        }
        data_list.append(data)
        TblAllocationPrepareFile.bulk_save(data_list)


def sell_in_field(data_list, obj):
    ret = []
    if len(data_list) == 0:
        logger.error(f'gc dmp fast data is empty, error: {obj.__name__}')
        return ret
    for item in data_list:
        if isinstance(item, obj):
            ret.append({
                PrepareCollectionWithTagsHeaderList[0]: item.sales_org,
                PrepareCollectionWithTagsHeaderList[1]: item.rtml4,
                PrepareCollectionWithTagsHeaderList[2]: item.lob,
                PrepareCollectionWithTagsHeaderList[3]: item.prod,
                PrepareCollectionWithTagsHeaderList[4]: item.project_code,
                PrepareCollectionWithTagsHeaderList[5]: item.nand,
                PrepareCollectionWithTagsHeaderList[6]: item.color,
                PrepareCollectionWithTagsHeaderList[7]: item.mpn,
                PrepareCollectionWithTagsHeaderList[8]: item.odq,
                PrepareCollectionWithTagsHeaderList[9]: item.sold_to_id,
                PrepareCollectionWithTagsHeaderList[10]: item.sold_to_name_en,
                PrepareCollectionWithTagsHeaderList[11]: item.qtw_shipment_plan,
                PrepareCollectionWithTagsHeaderList[12]: item.shipment_plan_cw,
                PrepareCollectionWithTagsHeaderList[13]: item.sni_cw_minus_1,
                PrepareCollectionWithTagsHeaderList[14]: item.shipment_plan_cw1,
                PrepareCollectionWithTagsHeaderList[15]: item.shipment_plan_cw2,
                PrepareCollectionWithTagsHeaderList[16]: item.eoh,
                PrepareCollectionWithTagsHeaderList[17]: item.st_ub_qty_cw_minus1,
                PrepareCollectionWithTagsHeaderList[18]: item.st_ub_5wk_bwd_avg,
                PrepareCollectionWithTagsHeaderList[19]: item.open_backlog_over_published_sp_cw3,
                PrepareCollectionWithTagsHeaderList[20]: item.priority,
                PrepareCollectionWithTagsHeaderList[21]: item.sales_input_qty_cw1,
                PrepareCollectionWithTagsHeaderList[22]: item.reason_cw1,
                PrepareCollectionWithTagsHeaderList[23]: item.sales_input_qty_cw2,
                PrepareCollectionWithTagsHeaderList[24]: item.reason_cw2,
                PrepareCollectionWithTagsHeaderList[25]: item.sales_input_qty_cw3,
                PrepareCollectionWithTagsHeaderList[26]: item.reason_cw3,
                PrepareCollectionWithTagsHeaderList[27]: item.sales_input_qty_cw4,
                PrepareCollectionWithTagsHeaderList[28]: item.reason_cw4,
                PrepareCollectionWithTagsHeaderList[29]: item.comments,
            })
    return ret


def sell_with_tags_in_field(data_list, not_download_category_list):
    ret = []
    if len(data_list) == 0:
        logger.error(f'gc dmp fast data is empty')
        return ret
    for item in data_list:
        if item.rtm in not_download_category_list:
            continue
        ret.append({
            PrepareCollectionWithTagsHeaderList[0]: item.sales_org,
            PrepareCollectionWithTagsHeaderList[1]: item.rtml4,
            PrepareCollectionWithTagsHeaderList[2]: item.lob,
            PrepareCollectionWithTagsHeaderList[3]: item.prod,
            PrepareCollectionWithTagsHeaderList[4]: item.project_code,
            PrepareCollectionWithTagsHeaderList[5]: item.nand,
            PrepareCollectionWithTagsHeaderList[6]: item.color,
            PrepareCollectionWithTagsHeaderList[7]: item.mpn,
            PrepareCollectionWithTagsHeaderList[8]: item.odq,
            PrepareCollectionWithTagsHeaderList[9]: item.sold_to_id,
            PrepareCollectionWithTagsHeaderList[10]: item.sold_to_name_en,
            PrepareCollectionWithTagsHeaderList[11]: item.qtw_shipment_plan,
            PrepareCollectionWithTagsHeaderList[12]: item.shipment_plan_cw,
            PrepareCollectionWithTagsHeaderList[13]: item.sni_cw_minus_1,
            PrepareCollectionWithTagsHeaderList[14]: item.shipment_plan_cw1,
            PrepareCollectionWithTagsHeaderList[15]: item.shipment_plan_cw2,
            PrepareCollectionWithTagsHeaderList[16]: item.eoh,
            PrepareCollectionWithTagsHeaderList[17]: item.st_ub_qty_cw_minus1,
            PrepareCollectionWithTagsHeaderList[18]: item.st_ub_5wk_bwd_avg,
            PrepareCollectionWithTagsHeaderList[19]: item.open_backlog_over_published_sp_cw3,
            PrepareCollectionWithTagsHeaderList[20]: item.priority,
            PrepareCollectionWithTagsHeaderList[21]: item.sales_input_qty_cw1,
            PrepareCollectionWithTagsHeaderList[22]: item.reason_cw1,
            PrepareCollectionWithTagsHeaderList[23]: item.sales_input_qty_cw2,
            PrepareCollectionWithTagsHeaderList[24]: item.reason_cw2,
            PrepareCollectionWithTagsHeaderList[25]: item.sales_input_qty_cw3,
            PrepareCollectionWithTagsHeaderList[26]: item.reason_cw3,
            PrepareCollectionWithTagsHeaderList[27]: item.sales_input_qty_cw4,
            PrepareCollectionWithTagsHeaderList[28]: item.reason_cw4,
            PrepareCollectionWithTagsHeaderList[29]: item.comments,
        })
    return ret


def download_service(cpf_lob_id, category):
    detail = TblAllocationCpfLob.get_by_id(cpf_lob_id)
    if isinstance(detail, TblAllocationCpfLob):
        fiscal_qtr_week_name = detail.fiscal_qtr_week_name
        lob = detail.lob
        df = []  # 创建一个空的列表
        if int(category) == DEMAND_WITH_TAGS_CATEGORY:
            # 下载sell_in_demand_completed状态的数据
            cpf_list = TblAllocationPrepare.get_allocation_prepare_cpf_list(fiscal_qtr_week_name, lob)
            not_download_category_list_dict = list(filter(lambda x: int(x['second_phase_status']) != SELL_IN_DEMAND_COMPLETED, cpf_list))
            not_download_category_list = [item[RtmKey] for item in not_download_category_list_dict]
            # 直接查询数据库
            pd_mono_list = AppFastAllocationDemandSellInMonoWi.get_sell_in_demand_list(fiscal_qtr_week_name, lob, hr_lr=None)
            pd_other_list = AppFastAllocationDemandSellInOtherWi.get_sell_in_demand_list(fiscal_qtr_week_name, lob, rtm=None)
            pd_multi_online_list = AppFastAllocationDemandSellInMultiOnlineWi.get_sell_in_demand_list(fiscal_qtr_week_name, lob, rtm=None)
            df.extend(pd_mono_list)
            df.extend(pd_other_list)
            df.extend(pd_multi_online_list)
            data_list = sell_with_tags_in_field(df, not_download_category_list)
            merge_x = pd.DataFrame(columns=PrepareCollectionWithTagsHeaderDict, data=data_list)
            file_name = f'Demand with Tags_{lob}_{fiscal_qtr_week_name}.xlsx'
        else:
            detail_list = TblAllocationPrepareFile.get_allocation_prepare_file_by_rtm_list(detail.fiscal_week_year,
                                                                                           str(CPFAllocationPhaseOneModule.SellinDemand),
                                                                                           lob, DEMAND_HR_ONLY_CATEGORY,
                                                                                           SellInDemandHrOnlyRtmList)
            for item in detail_list:
                if isinstance(item, TblAllocationPrepareFile):
                    if item.rtm in SellInDemandHrOnlyRtmList:
                        rtm_record = TblAllocationPrepare.find_by_fiscal_week_year_and_rtm(item.fiscal_week_year, item.rtm)
                        if rtm_record.second_phase_status != RTMDemandSubmissionStatus.Completed \
                            and rtm_record.second_phase_status != RTMDemandSubmissionStatus.InProcess:
                            continue
                        if item.upload_file_path:
                            upload_df = pd.read_excel(transfer_file_path(item.upload_file_path))
                            df.append(upload_df)
                        else:
                            detail = TblAllocationPrepareFile.get_allocation_prepare_file(
                                detail.fiscal_week_year, str(CPFAllocationPhaseOneModule.SellinDemand), lob, DEMAND_HR_ONLY_TEMPLATE_CATEGORY, item.rtm)
                            # 模版文件
                            if detail.upload_file_path:
                                upload_df = pd.read_excel(transfer_file_path(detail.upload_file_path))
                                df.append(upload_df)
            if not df:
                # 下载空文件
                merge_x = pd.DataFrame([], columns=SellInDemandHrOnlyTemplateFileHeader)
            else:
                merge_x = pd.concat(df, ignore_index=True)
            file_name = f'Demand (HR Only)_{lob}_{fiscal_qtr_week_name}.xlsx'
        merge_url = sell_in_demand_file_path_customs(file_name, 'sell_in_demand_merge')
        with pd.ExcelWriter(merge_url) as writer:
            merge_x.to_excel(writer, index=False)
        # return f'/file/storage/{file_name}'
        return merge_url, file_name


def publish_summary_list_to_dict(data_list):
    # '<' not supported between instances of 'str' and 'NoneType', 对可能存在的None值，使用空字符串替代
    data_sort = sorted(data_list, key=lambda x: (x["prod"] or "", x["project_code"] or ""))
    data_group = groupby(data_sort, key=lambda x: (x["prod"] or "", x["project_code"] or ""))
    data_dict = {key: list(group) for key, group in data_group}
    return data_dict


def publish_service(cpf_lob_id, current_user, description):
    can_publish = []
    detail = TblAllocationCpfLob.get_by_id(cpf_lob_id)
    if isinstance(detail, TblAllocationCpfLob):
        fiscal_qtr_week_name = detail.fiscal_qtr_week_name
        lob = detail.lob
        # 查询
        prepare_list = TblAllocationPrepare.find_by_week_and_rtm_list(detail.fiscal_week_year, SellInDemandHrOnlyRtmList)
        for item in prepare_list:
            if item.second_phase_status == RTMDemandSubmissionStatus.Completed \
                or item.second_phase_status == RTMDemandSubmissionStatus.InProcess:
                can_publish.append(item.rtm)
        detail_list = TblAllocationPrepareFile.get_allocation_prepare_file_by_rtm_list(detail.fiscal_week_year,
                                                                                       str(CPFAllocationPhaseOneModule.SellinDemand),
                                                                                       lob, DEMAND_HR_ONLY_CATEGORY,
                                                                                       can_publish)
        df = []  # 创建一个空的列表
        for item in detail_list:
            if isinstance(item, TblAllocationPrepareFile):
                if item.upload_file_path:
                    # 略过不能读取的文件
                    # ValueError: Excel file format cannot be determined, you must specify an engine manually.
                    # 指定后engine后还会报错
                    # BadZipFile: File is not a zip file
                    try:
                        upload_df = pd.read_excel(transfer_file_path(item.upload_file_path))
                    except ValueError as e:
                        logger.error(f"{item.upload_file_path}, {e}")
                        continue
                    df.append(upload_df)
        merge_x = pd.concat(df, ignore_index=True)
        file_name = f'Demand (HR Only)_{lob}_{fiscal_qtr_week_name}.xlsx'
        merge_url = sell_in_demand_file_path_customs(file_name, 'sell_in_demand_merge_publish')
        # merge_x.to_excel(merge_url, index=False)
        with pd.ExcelWriter(merge_url) as writer:
            merge_x.to_excel(writer, index=False)
        data_grouped = merge_x.groupby(['Prod / FPH L3', 'Project Code'])
        rows = []
        # 查询qtw_shipment_plan shipment_plan_cw1 shipment_plan_cw2数据
        mono_shipment = AppFastAllocationDemandSellInMonoWi.get_publish_summary(fiscal_qtr_week_name, lob)
        multi_online_shipment = AppFastAllocationDemandSellInMultiOnlineWi.get_publish_summary(fiscal_qtr_week_name, lob)
        mono_dict = publish_summary_list_to_dict(mono_shipment)
        multi_online_dict = publish_summary_list_to_dict(multi_online_shipment)
        for name, group in data_grouped:
            qtw_shipment_plan = 0
            shipment_plan_cw = 0
            shipment_plan_cw1 = 0
            shipment_plan_cw2 = 0
            if (name[0], name[1]) in mono_dict:
                mono_value = mono_dict[(name[0], name[1])]
                qtw_shipment_plan += mono_value[0]['qtw_shipment_plan']
                shipment_plan_cw += mono_value[0]['shipment_plan_cw']
                shipment_plan_cw1 += mono_value[0]['shipment_plan_cw1']
                shipment_plan_cw2 += mono_value[0]['shipment_plan_cw2']
            if (name[0], name[1]) in multi_online_dict:
                multi_online_value = multi_online_dict[(name[0], name[1])]
                qtw_shipment_plan += multi_online_value[0]['qtw_shipment_plan']
                shipment_plan_cw += multi_online_value[0]['shipment_plan_cw']
                shipment_plan_cw1 += multi_online_value[0]['shipment_plan_cw1']
                shipment_plan_cw2 += multi_online_value[0]['shipment_plan_cw2']
            row = [
                name[0], name[1],
                qtw_shipment_plan, shipment_plan_cw,
                shipment_plan_cw1, shipment_plan_cw2,
                group['Top Up Demand CW+1'].sum(), group['Top Up Demand CW+2'].sum(),
                group['Top Up Demand CW+3'].sum(), group['Top Up Demand CW+4'].sum(),
            ]
            rows.append(row)

        # 生成summary
        table_content = ''
        table_content += f'''<tr>
                        <td>{HrOnlySummaryHeader[0]}</td>
                        <td>{HrOnlySummaryHeader[1]}</td>
                        <td>{HrOnlySummaryHeader[2]}</td>
                        <td>{HrOnlySummaryHeader[3]}</td>
                        <td>{HrOnlySummaryHeader[4]}</td>
                        <td>{HrOnlySummaryHeader[5]}</td>
                        <td>{HrOnlySummaryHeader[6]}</td>
                        <td>{HrOnlySummaryHeader[7]}</td>
                        <td>{HrOnlySummaryHeader[8]}</td>
                        <td>{HrOnlySummaryHeader[9]}</td>
                    </tr>'''
        total_top_up_demand_cw1 = 0
        total_top_up_demand_cw2 = 0
        total_top_up_demand_cw3 = 0
        total_top_up_demand_cw4 = 0
        total_shipment_plan_cw = 0
        total_shipment_plan_cw1 = 0
        total_shipment_plan_cw2 = 0
        total_qtw_shipment_plan = 0
        for item in rows:
            total_qtw_shipment_plan += item[2]
            total_shipment_plan_cw += item[3]
            total_shipment_plan_cw1 += item[4]
            total_shipment_plan_cw2 += item[5]
            total_top_up_demand_cw1 += item[6]
            total_top_up_demand_cw2 += item[7]
            total_top_up_demand_cw3 += item[8]
            total_top_up_demand_cw4 += item[9]
            table_content += f'''<tr>
                        <td>{item[0]}</td>
                        <td>{item[1]}</td>
                        <td>{item[2]}</td>
                        <td>{item[3]}</td>
                        <td>{item[4]}</td>
                        <td>{item[5]}</td>
                        <td>{item[6]}</td>
                        <td>{item[7]}</td>
                        <td>{item[8]}</td>
                        <td>{item[9]}</td>
                    </tr>'''
        table_content += f'''
                            <tr>
                                <td colspan="2" style="height:25px"><strong>Total</strong></td>
                                <td><strong>{total_qtw_shipment_plan}</strong></td>
                                <td><strong>{total_shipment_plan_cw}</strong></td>
                                <td><strong>{total_shipment_plan_cw1}</strong></td>
                                <td><strong>{total_shipment_plan_cw2}</strong></td>
                                <td><strong>{total_top_up_demand_cw1}</strong></td>
                                <td><strong>{total_top_up_demand_cw2}</strong></td>
                                <td><strong>{total_top_up_demand_cw3}</strong></td>
                                <td><strong>{total_top_up_demand_cw4}</strong></td>
                            </tr>
                            '''
        table = f'<html><body><p>Summary:</p>' \
                f'<table style="text-align:center" border="1" width="500" bordercolor="black" cellspacing=0>{table_content}</html></body>'
        receivers = mail_config['cpf_sell_in_demand_publish_receivers'].replace(' ', '').strip(',').split(',')
        content = f'Dear All,\n\rAttached is iPad partner SIF{description}\n\rSending by {current_user}'
        subject = f'GC iPad Partner SIF - {fiscal_qtr_week_name}'
        TemplateEmail().sell_in_demand_13(fiscal_qtr_week_name, [merge_url], description, current_user, table, 'html')
        # send_html_file_email(subject, content, receivers, [merge_url], file_name, table)
        insert_operate_record(None, AllocationOperateCategory.SellInDemandPublish, None, None, current_user, None)


def sell_in_demand_confirm_service(prepare_id: int, current_user: str):
    # 根据文件是否生成判断是什么状态
    # Demand HR Only and Demand With Tags -> Completed
    # Demand HR Only, no Demand With Tags -> In Progress
    rtm_record = TblAllocationPrepare.get_detail(prepare_id)
    fiscal_week_year = rtm_record[StrFiscalWeekYear]
    hr_only_file_info = TblAllocationPrepareFile.get_allocation_prepare_file(fiscal_week_year, str(RTMAllocationPhase.DemandSubmission),
                                                                       'iPad', PrepareSubmissionFileCategory.HrOnly, AllocationRTM.Mono)
    with_tags_file_info = TblAllocationPrepareFile.get_allocation_prepare_file(fiscal_week_year, str(RTMAllocationPhase.DemandSubmission),
                                                                       'iPad', PrepareSubmissionFileCategory.WithTags, AllocationRTM.Mono)
    second_phase_status = RTMDemandSubmissionStatus.Completed
    if hr_only_file_info and not with_tags_file_info:
        second_phase_status = RTMDemandSubmissionStatus.InProcess
    TblAllocationPrepare.update_data_by_id(prepare_id, {
        "second_phase_status": second_phase_status,
        "update_time": datetime.now(),
        "update_by": current_user
    })
    insert_operate_record(None, AllocationOperateCategory.SellInDemandConfirm, None, None, current_user, None)
    return 'ok'


def get_error_page_info(rtm, fiscal_week_year):
    page_info = {
        "file_name": "",
        "file_path": "",
        "can_upload": False
    }
    # 获取模版文件
    template_file_info = TblAllocationPrepareFile.get_allocation_prepare_file(
        fiscal_week_year, str(RTMAllocationPhase.DemandSubmission),
        'iPad', PrepareSubmissionFileCategory.Template, rtm)
    if template_file_info:
        page_info['file_name'] = template_file_info.upload_file_name
        page_info['file_path'] = template_file_info.upload_file_path
    
    # 校验是否可上传，error状态下的周三10:00之前可upload
    is_history_week = is_history_week_in_allocation(fiscal_week_year)
    if not is_history_week:
        query_upload_deadline = TblAllocationCpfConfig.get_by_config_type("sell_in_demand_cpf_upload_end")
        if isinstance(query_upload_deadline, TblAllocationCpfConfig):
            if is_after_config_week_time(query_upload_deadline.week, query_upload_deadline.time):
                page_info['can_upload'] = True

    return page_info


def regenerate_multi_online_demand_with_tags_file(fiscal_week_year: int, rtm: str):
    rtm_record = TblAllocationPrepare.find_by_fiscal_week_year_and_rtm(fiscal_week_year, rtm)
    # 只有状态为InProgress的数据才能重刷，在这个时间依然是该状态，只能是cpf上传了文件
    if not rtm_record.second_phase_status == RTMDemandSubmissionStatus.InProcess:
        return ''
    
    lob = cpf_lobs[0]
    df_list = AppFastAllocationDemandSellInMultiOnlineWi.get_demand_with_tag_data(fiscal_week_year, rtm)
    df = pd.DataFrame(columns=PrepareCollectionWithTagsHeaderDict, data=df_list)
    file_name = f'{rtm}_Demand with Tags_{lob}_{rtm_record.fiscal_qtr_week_name}.xlsx'
    unique_file_name = f'{uuid.uuid4().hex}.xlsx'
    file_path = cpf_merge_file_path(unique_file_name)
    with pd.ExcelWriter(file_path) as writer:
        df.to_excel(writer, index=False)
    save_prepare_file(rtm_record.fiscal_qtr_week_name, fiscal_week_year,
                      lob, rtm, file_path, file_name,
                      DEMAND_WITH_TAGS_CATEGORY)
    # 生成完with tags数据后，修改状态为Completed
    TblAllocationPrepare.update_data_by_id(rtm_record.id, {
        "second_phase_status": SELL_IN_DEMAND_COMPLETED
    })
    return f'{rtm} regenerate demand with tags file successfully {DOUBLE_LINE_FEED}'
