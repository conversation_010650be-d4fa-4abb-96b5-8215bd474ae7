import io

from data.cpf_data_source import  OdsFastCPFSoldToMappingIPhone
from data.ideal_demand_data import *
from data.dmp_dw_common_data import DimPubProdInfo
from util.file_util import convert_file_path
from util.ideal_demand_template_email_sender import *
from util.cpf_util import get_list_page_url


DISPLAY_FINAL_FORECAST= 'display_final_forecast'
DISPLAY_IDEAL_DEMAND= 'display_ideal_demand'
DISPLAY_SO_EOH= 'display_so_eoh'
IDEAL_DEMAND_LOB = 'iPhone'

def get_ideal_demand_list_by_rtm(rtm, page, size):
    # 转化
    limit = size
    offset = (page - 1) * size
    ret, total = FastLiteIdealDemandRTMWeekRecord.get_list_by_rtm(rtm, limit, offset)

    return {
        "list": [{"fiscal_week_year": i.fiscal_week_year,
                  "week_date": i.week_date,
                  "rtm": i.rtm,
                  "id": i.id} for i in ret],
        "total": total
    }


def get_ideal_demand_detail(rtm, week_date):
    # 查询记录中的周信息
    record = FastLiteIdealDemandRTMWeekRecord.get_detail_by_rtm_week(rtm, week_date)
    if len(record) == 0:
        return {}
    
    file_list = []
    
    # 固定模版信息
    file_list.append({
        "file_name": RTM_FORECAST_TEMPLATE_NAME,
        "category": IdealDemandRTMCategory.Template
    })
    
    # 查询上传的文件
    upload_record = FastLiteIdealDemandRTMForecastUpload.count_by_rtm_week(rtm, week_date)
    if upload_record > 0:
        file_list.append({
            "file_name": f"{rtm}_Forecast_{week_date}.xlsx",
            "category": IdealDemandRTMCategory.Uploaded
        })
    
    # rtm Mono/ENT/EDU 需要下载 Final Forecast / Ideal Demand
    if rtm in ['Mono', 'ENT', 'EDU']:
        fiscal_week_year = FiscalYearWeek.get_fiscal_week_year_by_week_date(week_date)
        if is_time_to_display_file(fiscal_week_year, DISPLAY_FINAL_FORECAST):
            final_forecast = FastLiteIdealDemandFinalForecastRTM.count_by_rtm_week(rtm, week_date)
            if final_forecast > 0:
                file_list.append({
                    "file_name": f"{rtm}_Final_Forecast_{week_date}.xlsx",
                    "category": IdealDemandRTMCategory.FinalForecast
                })
        if is_time_to_display_file(fiscal_week_year, DISPLAY_IDEAL_DEMAND):
            ideal_demand = AppFastIdealDemandTemplateWi.count_by_rtm_week(rtm, week_date)
            if ideal_demand > 0:
                file_list.append({
                    "file_name": f"{rtm}_Ideal_Demand_{week_date}.csv", 
                    "category": IdealDemandRTMCategory.IdealDemand
                })

    return {
        "fiscal_week_year": record[0].fiscal_week_year,
        "week_date": record[0].week_date,
        "rtm": record[0].rtm,
        "id": record[0].id,
        "file_list": file_list,
        "is_exceed_deadline": is_upload_file_exceed_deadline(record[0].fiscal_week_year, 'upload_file')
    }


def get_ideal_demand_template(template_name, replace=False):
    '''
    固定的模版，则自动生成
    '''
    try:
        file_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__))) + f'/uploads/allocation/{template_name}'
        if not os.path.exists(file_path) or replace:
            # 如果不存在会生成对应的文件
            columns = []
            sheet_name = 'Sheet1'
            
            if template_name == RTM_FORECAST_TEMPLATE_NAME:
                columns = RTM_FORECAST_TEMPLATE_HEADER
                sheet_name = RTM_FORECAST_TEMPLATE_SHEET_NAME
            elif template_name == IPHONE_TWOS_ADJUSTMNET_X_TEMPLATE_NAME:
                columns = IPHONE_TWOS_ADJUSTMNET_X_TEMPLATE_HEADER
                sheet_name = IPHONE_TWOS_ADJUSTMNET_X_TEMPLATE_SHEET_NAME
            elif template_name == IPHONE_TWOS_ADJUSTMNET_Y_TEMPLATE_NAME:
                columns = IPHONE_TWOS_ADJUSTMNET_Y_TEMPLATE_HEADER
                sheet_name = IPHONE_TWOS_ADJUSTMNET_Y_TEMPLATE_SHEET_NAME
            
            df = pd.DataFrame(columns=columns)
            df.to_excel(file_path, sheet_name=sheet_name, index=False)
            
        return f'/file/storage/{template_name}'
    except Exception as e:
        logger.error(f'ideal demand template error: {e}')
        return ''
        

def upload_ideal_demand_file_by_rtm(file, rtm, week_date, uploader, uploader_email):
    upload_df = pd.read_excel(file)
    fiscal_week_year = FiscalYearWeek.get_fiscal_week_year_by_week_date(week_date)
    # 校验数据
    rtm_upload_file_validation(rtm, fiscal_week_year, upload_df)
    
    # 校验通过后保存数据
    save_rtm_uploaded_ideal_demand_data(rtm, week_date, fiscal_week_year, upload_df)


def validate_integer_except_negative_null(value):
    if isinstance(value, str):
        return False
    if pd.isna(value): # 如果为空值，返回 False
        return False
    elif value < 0: # 如果为负数，返回 False
        return False
    else:
        # 当列中有numpy.nan时会把整列的数据类型改为float
        # return isinstance(value, int) # 需要类型是int
        return round(value, 0) == value


def rtm_upload_file_validation(rtm, fiscal_week_year, upload_df):
    # 表头: 应与模板完全一致，不能修改。
    if not list(upload_df.columns) == list(RTM_FORECAST_TEMPLATE_HEADER):
        raise ErrorExcept(ErrCode.FileUploadError, FileUploadError.CPFTWOSiPhoneHeaderError)
    
    # Week_Date: 限制格式为{FYxxQxxWxx}，例如：FY23Q3W7
    # pattern = r'^FY\d{2}Q\dW\d{1,2}$'
    # invalid_week_date = ~upload_df['Week_Date'].str.contains(pattern)
    # invalid_week_date_rows = upload_df.loc[invalid_week_date].index.tolist()
    # if len(invalid_week_date_rows) != 0:
    #     raise ErrorExcept(ErrCode.FileUploadError,
    #                       FileUploadError.CPFTWOSiPhoneInvalidRowsPrefix
    #                       + ", ".join([str(idx+2) for idx in invalid_week_date_rows])
    #                       + FileUploadError.CPFTIdealDemandWeekDateInvalidSuffix)
    
    # Customer Sold-to ID: 通过Sold-to mapping表校验数据合法性
    valid_sold_to_id_list = OdsFastCPFSoldToMappingIPhone.get_sold_to_id(rtm)
    invalid_sold_to_id = ~upload_df['Customer Sold-to ID'].isin(list(map(int, valid_sold_to_id_list)))
    invalid_sold_to_id_rows = upload_df.loc[invalid_sold_to_id].index.tolist()
    if len(invalid_sold_to_id_rows) != 0:
        raise ErrorExcept(ErrCode.FileUploadError,
                          FileUploadError.CPFTWOSiPhoneInvalidRowsPrefix
                          + ", ".join([str(idx+2) for idx in invalid_sold_to_id_rows])
                          + FileUploadError.CPFTIdealDemandSoldToIDInvalidSuffix)
    
    # MPN: 通过产品信息底表校验数据合法性。
    mpn_list = DimPubProdInfo.get_valid_mpn_list(IDEAL_DEMAND_LOB)
    mpn_condition = ~upload_df['MPN'].isin(mpn_list)
    invalid_mpn_rows = upload_df.loc[mpn_condition].index.tolist()
    if len(invalid_mpn_rows) != 0:
        raise ErrorExcept(ErrCode.FileUploadError, 
                          FileUploadError.CPFTWOSiPhoneInvalidRowsPrefix
                          + ", ".join([str(idx+2) for idx in invalid_mpn_rows])
                          + FileUploadError.CPFTIdealDemandMPNInvalidSuffix)
    
    # CW ～ CW+7数值: 数据应为整数，且不可以为负、不可以为空。改为cw+8, 又增加了cw+9
    for week in range(10):
        if week:
            column_name = f"CW+{week}"
        else:
            column_name = 'CW'
        invalid_cwx = ~upload_df[column_name].apply(validate_integer_except_negative_null)
        invalid_cwx_rows = upload_df.loc[invalid_cwx].index.tolist()
        if len(invalid_cwx_rows) != 0:
            raise ErrorExcept(ErrCode.FileUploadError,
                            FileUploadError.CPFTWOSiPhoneInvalidRowsPrefix
                            + ", ".join([str(idx+2) for idx in invalid_cwx_rows])
                            + FileUploadError.CPFTIdealDemandCWXInvalidSuffix)

    # Type: 只能为“UB”或“SO”，且匹配对应RTM的数据口径。
    type_list = RTM_TYPE_MAPPING.get(rtm)
    invalid_type = ~upload_df['Type'].isin(type_list)
    invalid_type_rows = upload_df.loc[invalid_type].index.tolist()
    if len(invalid_type_rows) != 0:
        raise ErrorExcept(ErrCode.FileUploadError, 
                          FileUploadError.CPFTWOSiPhoneInvalidRowsPrefix
                          + ", ".join([str(idx+2) for idx in invalid_type_rows])
                          + FileUploadError.CPFTWOSiPhoneTypeInvalidSuffix)

    # 重复行
    # + ", ".join(str(x+2) for x in upload_df[duplicated].T.columns)
    duplicated = upload_df.duplicated(subset=['MPN','Customer Sold-to ID'], keep=False)
    if duplicated.any():
        raise ErrorExcept(ErrCode.FileUploadError,
                          FileUploadError.CPFTIdealDemandDuplicateInvalid)


def save_rtm_uploaded_ideal_demand_data(rtm, week_date, fiscal_week_year, upload_df):
    save_df = upload_df
    # 替换表头
    save_df.columns = RTM_FORECAST_TEMPLATE_HEADER_RAW
    save_df['rtm'] = rtm
    save_df['week_date'] = week_date
    save_df['fiscal_week_year'] = fiscal_week_year
    save_df.replace({np.nan: None}, inplace=True)
    # 保存数据
    FastLiteIdealDemandRTMForecastUpload.delete_by_rtm_week(rtm, fiscal_week_year)
    FastLiteIdealDemandRTMForecastUpload.bulk_save(save_df.to_dict("records"))
    

def get_rtm_file_binary_service(rtm, week_date, category):
    sheet_name = DEFAULT_SHEET_NAME
    if category == IdealDemandRTMCategory.Template:
        file_name = RTM_FORECAST_TEMPLATE_NAME
        excel_file_path = convert_file_path(get_ideal_demand_template(file_name, True))
        return file_name, excel_file_path
    elif category == IdealDemandRTMCategory.Uploaded:
        query_df = FastLiteIdealDemandRTMForecastUpload.query_by_rtm_week(rtm, week_date)
        df = query_df[RTM_FORECAST_TEMPLATE_HEADER_RAW]
        df.columns = RTM_FORECAST_TEMPLATE_HEADER
        sheet_name = RTM_FORECAST_TEMPLATE_SHEET_NAME
        file_name = f"{rtm}_Forecast_{week_date}.xlsx"
    elif category == IdealDemandRTMCategory.FinalForecast:
        fiscal_week_year = FiscalYearWeek.get_fiscal_week_year_by_week_date(week_date)
        if is_time_to_display_file(fiscal_week_year, DISPLAY_FINAL_FORECAST):
            query_df = FastLiteIdealDemandFinalForecastRTM.query_by_rtm_week(rtm, week_date)
        else:
            query_df = pd.DataFrame([],columns=IDEAL_DEMAND_FINAL_FORECAST_HEADER_RAW)
        df = query_df[IDEAL_DEMAND_FINAL_FORECAST_HEADER_RAW]
        df.columns = IDEAL_DEMAND_FINAL_FORECAST_HEADER
        sheet_name = IDEAL_DEMAND_FINAL_FORECAST_SHEET_NAME
        file_name = f"{rtm}_Final_Forecast_{week_date}.xlsx"
    elif category == IdealDemandRTMCategory.IdealDemand:
        fiscal_week_year = FiscalYearWeek.get_fiscal_week_year_by_week_date(week_date)
        ideal_demand_header = RTM_IDEAL_DEMAND_HEADER
        ideal_demand_header_raw = RTM_IDEAL_DEMAND_HEADER_RAW

        if is_time_to_display_file(fiscal_week_year, DISPLAY_IDEAL_DEMAND):
            query_df = AppFastIdealDemandTemplateWi.query_by_rtm_week(rtm, week_date)
        else:
            query_df = pd.DataFrame([], columns=ideal_demand_header_raw)
        df = query_df[ideal_demand_header_raw]
        df.columns = ideal_demand_header
        file_name = f"{rtm}_Ideal_Demand_{week_date}.csv"
        csv_file_bytes = io.BytesIO()
        df.to_csv(csv_file_bytes, index=False)
        csv_file_bytes.seek(0)
        return file_name, csv_file_bytes
    else:
        return '', None

    # 返回文件流，不在系统中创建文件
    excel_file_bytes = io.BytesIO()
    df.to_excel(excel_file_bytes, sheet_name=sheet_name, index=False)
    excel_file_bytes.seek(0)
    
    return file_name, excel_file_bytes


def get_ideal_demand_list_by_cpf(page, size):
    # 转化
    limit = size
    offset = (page - 1) * size
    ret, total = FastLiteIdealDemandCPFWeekRecord.get_list(limit, offset)

    return {
        "list": [{"fiscal_week_year": i.fiscal_week_year,
                  "week_date": i.week_date,
                  "id": i.id} for i in ret],
        "total": total
    }


def get_ideal_demand_cpf_detail(week_date):
    # 查询记录中的周信息
    record = FastLiteIdealDemandCPFWeekRecord.get_detail_by_week(week_date)
    if len(record) == 0:
        return {}
    
    file_list = {}
    file_list_x = []
    file_list_y = []
    
    # 固定模版信息
    # x模版
    file_list_x.append({
        "file_name": IPHONE_TWOS_ADJUSTMNET_X_TEMPLATE_NAME,
        "category": IdealDemandCPFCategory.XTemplate
    })
    # y模版
    file_list_y.append({
        "file_name": IPHONE_TWOS_ADJUSTMNET_Y_TEMPLATE_NAME,
        "category": IdealDemandCPFCategory.YTemplate
    })
    
    x_record = FastLiteIdealDemandTWOSAdjustmentX.count_by_week(week_date)
    if x_record > 0:
        file_list_x.append({
            "file_name": f"TWOS_Adjustment_(X)_{week_date}.xlsx",
            "category": IdealDemandCPFCategory.XAdjustment
        })
    y_record = FastLiteIdealDemandTWOSAdjustmentY.count_by_week(week_date)
    if y_record > 0:
        file_list_y.append({
            "file_name": f"TWOS_Adjustment_(Y)_{week_date}.xlsx",
            "category": IdealDemandCPFCategory.YAdjustment
        })
    file_list['file_list_x'] = file_list_x
    file_list['file_list_y'] = file_list_y
    
    # 增加 so_eoh 文件显示, 查询Week-1的数据
    file_list_so_eoh = []
    if is_time_to_display_file(record[0].fiscal_week_year, DISPLAY_SO_EOH):
        last_week = FiscalYearWeek.get_last_week_by_qtr_week_name(week_date)
        file_list_so_eoh.append({
            "file_name": f"{AllocationRTM.Mono}_SO_EOH_{last_week.fiscal_qtr_week_name}.xlsx",
            "category": IdealDemandCPFCategory.MonoSoEoh,
            "is_ready": is_so_eoh_data_ready(AllocationRTM.Mono, last_week.fiscal_week_year)
        })
        file_list_so_eoh.append({
            "file_name": f"{AllocationRTM.Online}_SO_EOH_{last_week.fiscal_qtr_week_name}.xlsx",
            "category": IdealDemandCPFCategory.OnlineSoEoh,
            "is_ready": is_so_eoh_data_ready(AllocationRTM.Online, last_week.fiscal_week_year)
        })
    
    file_list['so_eoh'] = file_list_so_eoh
    
    return {
        "fiscal_week_year": record[0].fiscal_week_year,
        "week_date": record[0].week_date,
        "id": record[0].id,
        "file_list": file_list,
        "is_exceed_deadline": is_upload_file_exceed_deadline(record[0].fiscal_week_year, 'upload_file_adjustment')
    }


def upload_ideal_demand_file_by_cpf(file, category, week_date, uploader, uploader_email):
    upload_df = pd.read_excel(file)
    fiscal_week_year = FiscalYearWeek.get_fiscal_week_year_by_week_date(week_date)
    # 校验数据
    cpf_upload_file_validation(category, fiscal_week_year, upload_df)
    
    # 校验通过后保存数据
    save_cpf_uploaded_ideal_demand_data(category, week_date, fiscal_week_year, upload_df)


def cpf_upload_file_validation(category: str, fiscal_week_year, upload_df):
    # 表头: 应与模板完全一致，不能修改。
    template_columns = IPHONE_TWOS_ADJUSTMNET_X_TEMPLATE_HEADER if category == 'x' else IPHONE_TWOS_ADJUSTMNET_Y_TEMPLATE_HEADER
    if not list(upload_df.columns) == list(template_columns):
        raise ErrorExcept(ErrCode.FileUploadError, FileUploadError.CPFTWOSiPhoneHeaderError)
    
    # Model validate
    model_list = DimPubProdInfo.get_valid_model_list(IDEAL_DEMAND_LOB)
    condition = ~upload_df['Model'].isin(model_list)
    invalid_model_rows = upload_df.loc[condition].index.tolist()
    if len(invalid_model_rows) != 0:
        raise ErrorExcept(ErrCode.FileUploadError, 
                          FileUploadError.CPFTWOSiPhoneInvalidRowsPrefix
                          + ", ".join([str(idx+2) for idx in invalid_model_rows])
                          + FileUploadError.CPFTWOSiPhoneModelInvalidSuffix)

    # RTM/ Business Type validate
    if category == 'y':
        valid_rtm_business_type = OdsFastCPFSoldToMappingIPhone.get_business_type_relation()
        valid_rtm_business_type = valid_rtm_business_type[['rtm', 'business_type']].to_records(index=False)
        valid_rtm_business_type = [tuple(row) for row in valid_rtm_business_type]
        not_in_tuple = ~upload_df[['RTM', 'Business Type']].apply(lambda x: tuple(x) in valid_rtm_business_type, axis=1)
        invalid_rtm_business_type_rows = upload_df.loc[not_in_tuple].index.tolist()
        if len(invalid_rtm_business_type_rows) != 0:
            raise ErrorExcept(ErrCode.FileUploadError,
                            FileUploadError.CPFTWOSiPhoneInvalidRowsPrefix
                            + ", ".join([str(idx+2) for idx in invalid_rtm_business_type_rows])
                            + FileUploadError.CPFTWOSiPhoneRTMBusinessTypeInvalidSuffix)
    elif category == 'x':
        valid_rtm_business_type = OdsFastCPFSoldToMappingIPhone.get_business_type_relation()
        valid_rtm_business_type = valid_rtm_business_type[['rtm', 'business_type', 'sold_to_id']].to_records(index=False)
        valid_rtm_business_type = [tuple(row) for row in valid_rtm_business_type]
        # 文件上传来的这列是数值，需要转换为str来比对
        upload_df['Customer Sold-to ID'] = upload_df['Customer Sold-to ID'].astype(str)
        not_in_tuple = ~upload_df[['RTM', 'Business Type', 'Customer Sold-to ID']].apply(lambda x: tuple(x) in valid_rtm_business_type, axis=1)
        invalid_rtm_business_type_rows = upload_df.loc[not_in_tuple].index.tolist()
        if len(invalid_rtm_business_type_rows) != 0:
            raise ErrorExcept(ErrCode.FileUploadError,
                            FileUploadError.CPFTWOSiPhoneInvalidRowsPrefix
                            + ", ".join([str(idx+2) for idx in invalid_rtm_business_type_rows])
                            + FileUploadError.CPFTWOSiPhoneRTMBusinessTypeSoldtoInvalidSuffix)

    # X/Y in CW+1 ~ CW+4数值校验, 数值允许最多一位小数，正数/负数/0均可接受，不可以为空值
    for week in range(4):
        column_name = f"{category.upper()} in CW+{week+1}"
        invalid_cwx = ~upload_df[column_name].apply(validate_float_except_null)
        invalid_cwx_rows = upload_df.loc[invalid_cwx].index.tolist()
        if len(invalid_cwx_rows) != 0:
            raise ErrorExcept(ErrCode.FileUploadError,
                            FileUploadError.CPFTWOSiPhoneInvalidRowsPrefix
                            + ", ".join([str(idx+2) for idx in invalid_cwx_rows])
                            + FileUploadError.CPFTIdealDemandCPFXYInvalidSuffix)

    # Type: 只能为“UB”或“SO”，需要跟RTM对应
    # type_list = ['UB', 'SO']
    # invalid_type = ~upload_df['Type'].isin(type_list)
    rtm_type_tuple = [('Mono', 'SO'), ('Multi', 'UB'), ('Online', 'SO'), ('Carrier', 'UB'), ('ENT', 'UB'), ('EDU', 'UB')]
    invalid_type = ~upload_df[['RTM', 'Type']].apply(lambda x: tuple(x) in rtm_type_tuple, axis=1)
    invalid_type_rows = upload_df.loc[invalid_type].index.tolist()
    if len(invalid_type_rows) != 0:
        raise ErrorExcept(ErrCode.FileUploadError, 
                          FileUploadError.CPFTWOSiPhoneInvalidRowsPrefix
                          + ", ".join([str(idx+2) for idx in invalid_type_rows])
                          + FileUploadError.CPFTWOSiPhoneTypeInvalidSuffix)

    # 重复行
    # + ", ".join(str(x+2) for x in upload_df[duplicated].T.columns)
    check_subset = []
    if category == 'y':
        check_subset = ['Model', 'RTM', 'Business Type']
    elif category == 'x':
        check_subset = ['Model', 'Customer Sold-to ID']
    if len(check_subset) > 0:
        duplicated = upload_df.duplicated(subset=check_subset, keep=False)
        if duplicated.any():
            raise ErrorExcept(ErrCode.FileUploadError,
                            FileUploadError.CPFTIdealDemandDuplicateInvalid)


def validate_float_except_null(value):
    if isinstance(value, str):
        return False
    if pd.isna(value): # 如果为空值，返回 False
        return False
    else:
        return round(value, 1) == value # 如果小数位数超过 1，返回 False；否则返回 True


def save_cpf_uploaded_ideal_demand_data(category, week_date, fiscal_week_year, upload_df):
    save_df = upload_df
    # 替换表头
    if category == 'x':
        save_df.columns = IPHONE_TWOS_ADJUSTMNET_X_TEMPLATE_HEADER_RAW
        save_df['week_date'] = week_date
        save_df['fiscal_week_year'] = fiscal_week_year
        save_df.replace({np.nan: None}, inplace=True)
        # 保存数据
        FastLiteIdealDemandTWOSAdjustmentX.delete_by_week(fiscal_week_year)
        FastLiteIdealDemandTWOSAdjustmentX.bulk_save(save_df.to_dict("records"))
    elif category == 'y':
        save_df.columns = IPHONE_TWOS_ADJUSTMNET_Y_TEMPLATE_HEADER_RAW
        save_df['week_date'] = week_date
        save_df['fiscal_week_year'] = fiscal_week_year
        save_df.replace({np.nan: None}, inplace=True)
        # 保存数据
        FastLiteIdealDemandTWOSAdjustmentY.delete_by_week(fiscal_week_year)
        FastLiteIdealDemandTWOSAdjustmentY.bulk_save(save_df.to_dict("records"))


def get_cpf_file_binary_service(week_date, category):
    sheet_name = DEFAULT_SHEET_NAME
    if category == IdealDemandCPFCategory.XTemplate:
        file_name = IPHONE_TWOS_ADJUSTMNET_X_TEMPLATE_NAME
        excel_file_path = convert_file_path(get_ideal_demand_template(file_name, True))
        return file_name, excel_file_path
    elif category == IdealDemandCPFCategory.YTemplate:
        file_name = IPHONE_TWOS_ADJUSTMNET_Y_TEMPLATE_NAME
        excel_file_path = convert_file_path(get_ideal_demand_template(file_name, True))
        return file_name, excel_file_path
    elif category == IdealDemandCPFCategory.XAdjustment:
        query_df = FastLiteIdealDemandTWOSAdjustmentX.query_by_week(week_date)
        df = query_df[IPHONE_TWOS_ADJUSTMNET_X_TEMPLATE_HEADER_RAW]
        df.columns = IPHONE_TWOS_ADJUSTMNET_X_TEMPLATE_HEADER
        sheet_name = IPHONE_TWOS_ADJUSTMNET_X_TEMPLATE_SHEET_NAME
        file_name = f"TWOS_Adjustment_(X)_{week_date}.xlsx"
    elif category == IdealDemandCPFCategory.YAdjustment:
        query_df = FastLiteIdealDemandTWOSAdjustmentY.query_by_week(week_date)
        df = query_df[IPHONE_TWOS_ADJUSTMNET_Y_TEMPLATE_HEADER_RAW]
        df.columns = IPHONE_TWOS_ADJUSTMNET_Y_TEMPLATE_HEADER
        sheet_name = IPHONE_TWOS_ADJUSTMNET_Y_TEMPLATE_SHEET_NAME
        file_name = f"TWOS_Adjustment_(Y)_{week_date}.xlsx"
    elif category == IdealDemandCPFCategory.FinalForecast:
        fiscal_week_year = FiscalYearWeek.get_fiscal_week_year_by_week_date(week_date)
        if is_time_to_display_file(fiscal_week_year, DISPLAY_FINAL_FORECAST):
            file_name, file_path = get_cpf_final_forecast(week_date)
            return file_name, file_path
        else:
            query_df = pd.DataFrame([], columns=CPF_IDEAL_DEMAND_FINAL_FORECAST_HEADER_RAW)
        df = query_df[CPF_IDEAL_DEMAND_FINAL_FORECAST_HEADER_RAW]
        df.columns = CPF_IDEAL_DEMAND_FINAL_FORECAST_HEADER
        sheet_name = CPF_IDEAL_DEMAND_FINAL_FORECAST_SHEET_NAME
        file_name = f"CP&F_Final_Forecast_{week_date}.xlsx"
    elif category == IdealDemandCPFCategory.IdealDemand:
        fiscal_week_year = FiscalYearWeek.get_fiscal_week_year_by_week_date(week_date)
        if is_time_to_display_file(fiscal_week_year, DISPLAY_IDEAL_DEMAND):
            query_df = AppFastIdealDemandTemplateWi.query_by_week_order_rtm(week_date, AllocationRTM.get_all_rtms())
        else:
            query_df = pd.DataFrame([], columns=CPF_IDEAL_DEMAND_HEADER_RAW)
        df = query_df[CPF_IDEAL_DEMAND_HEADER_RAW]
        df.columns = CPF_IDEAL_DEMAND_HEADER
        file_name = f"Ideal_Demand_{week_date}.csv"
        csv_file_bytes = io.BytesIO()
        df.to_csv(csv_file_bytes, index=False)
        csv_file_bytes.seek(0)
        return file_name, csv_file_bytes
    elif category == IdealDemandCPFCategory.MonoSoEoh:
        fiscal_week_year = FiscalYearWeek.get_fiscal_week_year_by_week_date(week_date)
        last_week = FiscalYearWeek.get_last_week_by_qtr_week_name(week_date)
        if is_time_to_display_file(fiscal_week_year, DISPLAY_SO_EOH):
            query_df = AppFastIdealDemandMonoSoEohWi.query_by_lob_week(last_week.fiscal_week_year, IDEAL_DEMAND_LOB)
        else:
            query_df = pd.DataFrame([], columns=CPF_SO_EOH_HEADER_RAW)
        df = query_df[CPF_SO_EOH_HEADER_RAW]
        # 将so_eoh中小于0的数置为0
        df.loc[df['so_eoh'] < 0, 'so_eoh'] = 0
        df.columns = CPF_SO_EOH_HEADER
        sheet_name = CPF_SO_EOH_SHEET_NAME
        file_name = f"{AllocationRTM.Mono}_SO_EOH_{last_week.fiscal_qtr_week_name}.xlsx"
    elif category == IdealDemandCPFCategory.OnlineSoEoh:
        fiscal_week_year = FiscalYearWeek.get_fiscal_week_year_by_week_date(week_date)
        last_week = FiscalYearWeek.get_last_week_by_qtr_week_name(week_date)
        if is_time_to_display_file(fiscal_week_year, DISPLAY_SO_EOH):
            query_df = AppFastIdealDemandOnlineSoEohWi.query_by_lob_week(last_week.fiscal_week_year, IDEAL_DEMAND_LOB)
        else:
            query_df = pd.DataFrame([], columns=CPF_SO_EOH_HEADER_RAW)
        df = query_df[CPF_SO_EOH_HEADER_RAW]
        # 将so_eoh中小于0的数置为0
        df.loc[df['so_eoh'] < 0, 'so_eoh'] = 0
        df.columns = CPF_SO_EOH_HEADER
        sheet_name = CPF_SO_EOH_SHEET_NAME
        file_name = f"{AllocationRTM.Online}_SO_EOH_{last_week.fiscal_qtr_week_name}.xlsx"
    else:
        return '', None

    # 返回文件流，不在系统中创建文件
    excel_file_bytes = io.BytesIO()
    df.to_excel(excel_file_bytes, sheet_name=sheet_name, index=False)
    excel_file_bytes.seek(0)
    
    return file_name, excel_file_bytes


def generate_week_record(fiscal_dt):
    fiscal_obj = FiscalYearWeek.get_fis_by_date(fiscal_dt)
    week_date = fiscal_obj.fiscal_qtr_week_name
    fiscal_week_year = fiscal_obj.fiscal_week_year
    record_rtm = []
    for rtm in RTM_TYPE_MAPPING.keys():
        record_rtm.append({
            "rtm": rtm,
            "week_date": week_date,
            "fiscal_week_year": fiscal_week_year
        })
    FastLiteIdealDemandRTMWeekRecord.bulk_save(record_rtm)
    
    record_cpf = [{
        "week_date": week_date,
        "fiscal_week_year": fiscal_week_year
    }]
    FastLiteIdealDemandCPFWeekRecord.bulk_save(record_cpf)
    return f'{week_date} done'


def generate_final_forecast(fiscal_dt):
    '''
    1. ENT / EDU / Online：CW ~ CW+7的Final Forecast数值均直接取自RTM Forecast
    2. Mono / Multi / Carrier：CW ~ CW+1的Final Forecast数值取自AI Model Forecast，CW2 ~ CW+7的Final Forecast数值取自RTM Forecast
    '''
    fiscal_obj = FiscalYearWeek.get_fis_by_date(fiscal_dt)
    week_date = fiscal_obj.fiscal_qtr_week_name
    fiscal_week_year = fiscal_obj.fiscal_week_year
    # 需要查询AI Forecast cw/cw1 数据的三个RTM
    mono_ai = MonoForecastingResultDtl.query_forecast_by_rtm_week_latest(AllocationRTM.Mono, week_date)
    multi_ai = MultiForecastingResultDtl.query_forecast_by_rtm_week_latest(AllocationRTM.Multi, week_date)
    carrier_ai = CarrierForecastingResultDtl.query_forecast_by_rtm_week_latest(AllocationRTM.Carrier, week_date)
    ai_df = pd.concat([mono_ai, multi_ai, carrier_ai], axis=0, ignore_index=True)
    
    # 查询所有用户上传的Forecast数据
    fetch_upload_forecast_df = FastLiteIdealDemandRTMForecastUpload.query_by_week(week_date)
    
    # 处理cw/cw1数据
    # 合并两个表，相同字段会在left的加_x后缀，right的加_y后缀
    merged = pd.merge(fetch_upload_forecast_df, ai_df, on=['rtm', 'sold_to_id', 'mpn'], how='left')
    # 替换forecast_cw和forecast_cw1中的NaN值
    merged['forecast_cw'] = merged['forecast_cw_y'].fillna(merged['forecast_cw_x'])
    merged['forecast_cw1'] = merged['forecast_cw1_y'].fillna(merged['forecast_cw1_x'])
    # 删除多余的列
    dealed_df = merged.drop(['forecast_cw_x', 'forecast_cw_y', 'forecast_cw1_x', 'forecast_cw1_y'], axis=1)
    
    # final forecast表中需要加 business_type,fph4,project_code
    # 其中 business_type 来自 gc_dmp_fast.dim_fast_business_soldto_mapping 中的 business_type
    # fph4,project_code,sku 来自 dmp_dw_common.dim_pub_prod_info 中的 hier_node_level_4_name, project_short_desc, sku
    product_df = DimPubProdInfo.get_mpn_fph4_project_code(IDEAL_DEMAND_LOB)
    business_type_df = OdsFastCPFSoldToMappingIPhone.get_business_type_relation()
    dealed_df['sold_to_id'] = dealed_df['sold_to_id'].astype(str)
    merge_business_type = pd.merge(dealed_df, business_type_df,
                                   on=['rtm', 'sold_to_id'],
                                   how='left')
    merge_product = pd.merge(merge_business_type, product_df, on=['mpn'], how='left')
    final_forecast_header = IDEAL_DEMAND_FINAL_FORECAST_HEADER_RAW.copy()
    final_forecast_header.append('rtm')
    final_forecast_header.append('fiscal_week_year')
    final_forecast_df = merge_product[final_forecast_header]
    # 保存数据
    final_forecast_df.replace({np.nan: None}, inplace=True)
    FastLiteIdealDemandFinalForecastRTM.delete_by_week(fiscal_week_year)
    FastLiteIdealDemandFinalForecastRTM.bulk_save(final_forecast_df.to_dict("records"))
    
    # 生成CP&F的 final forecast 文件
    generate_cpf_final_forecast(week_date, fiscal_week_year)
    
    return f'{week_date} done'


def is_upload_file_exceed_deadline(fiscal_week_year, config_name):
    upload_file_config = FastLiteIdealDemandDeadlineConfig.query_config_by_name(config_name)
    if len(upload_file_config) > 0:
        now = datetime.now()
        # 如果不应用到历史周，则需要判断是否是历史周
        if not upload_file_config[0].is_apply_history:
            week_info = FiscalYearWeek.get_week_by_date(now.strftime(DateFormat))
            if fiscal_week_year < week_info.get(StrFiscalWeekYear):
                return True
        
        # 周日是第一天, 周一: 1, 周日: 7
        current_weekday = now.weekday() + 1
        if current_weekday == 7 or current_weekday < upload_file_config[0].week:
            return False
        elif current_weekday == upload_file_config[0].week \
            and now.strftime(DateTimeFormat) < now.strftime(DateFormat) + ' ' + upload_file_config[0].time:
            return False
        else:
            return True
    else:
        # 没有设置Deadline就不会超过
        return False


def get_cpf_final_forecast(week_date):
    file_name = f"CP&F_Final_Forecast_{week_date}.xlsx"
    file_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__))) + f'/uploads/allocation/{file_name}'
    return file_name, file_path


def generate_cpf_final_forecast(week_date, fiscal_week_year):
    # 查询cw到cw+7的fiscal_week_year 改成cw到cw+8, 又增加了cw+9
    fiscal_info = FiscalYearWeek.get_cw_next_n_week(fiscal_week_year, 9)
    fiscal_result = {}
    fiscal_week_mapping = {}
    for index in range(len(fiscal_info)):
        result_key = 'forecast_cw' if index == 0 else f'forecast_cw{index}'
        fiscal_result[result_key] = {
            "fiscal_week": fiscal_info[index].fiscal_week_year,
            "fiscal_quarter": fiscal_info[index].fiscal_quarter,
            "fiscal_year": fiscal_info[index].fiscal_year
        }
        fiscal_week_mapping[result_key] = fiscal_info[index].fiscal_week_year
    
    # 查询rtm上传的原始forecast数据
    rtm_origin_header = ['rtm','sold_to_id','mpn','forecast_cw','forecast_cw1','forecast_cw2','forecast_cw3','forecast_cw4','forecast_cw5','forecast_cw6','forecast_cw7','forecast_cw8', 'forecast_cw9']
    rtm_origin_forecast_df = FastLiteIdealDemandRTMForecastUpload.query_by_week(week_date)
    rtm_origin_forecast_df = rtm_origin_forecast_df[rtm_origin_header]
    # 进行列转换为行
    rtm_fcst_df_melted = pd.melt(rtm_origin_forecast_df, id_vars=rtm_origin_header[:3],
                    value_vars=rtm_origin_header[3:],
                    var_name='fiscal_week', value_name='rtm_fcst')

    # 查询rtm生成的FinalForecast数据
    final_forcast_header = ['rtm'] + IDEAL_DEMAND_FINAL_FORECAST_HEADER_RAW
    query_rtm_final_forecast_df = FastLiteIdealDemandFinalForecastRTM.query_by_week(week_date)
    combine_fcst_df = query_rtm_final_forecast_df[final_forcast_header]
    # 进行列转换为行
    # 前11列不动，将11列之后的做转换
    combine_fcst_df_melted = pd.melt(combine_fcst_df, id_vars=final_forcast_header[:11],
                                     value_vars=final_forcast_header[11:],
                                     var_name='fiscal_week', value_name='final_fcst')
    
    # 查询AI Forecast数据
    mono_ai = MonoForecastingResultDtl.query_all_forecast_by_rtm_week_latest(AllocationRTM.Mono, week_date)
    multi_ai = MultiForecastingResultDtl.query_all_forecast_by_rtm_week_latest(AllocationRTM.Multi, week_date)
    carrier_ai = CarrierForecastingResultDtl.query_all_forecast_by_rtm_week_latest(AllocationRTM.Carrier, week_date)
    ai_df = pd.concat([mono_ai, multi_ai, carrier_ai], axis=0, ignore_index=True)

    # 进行列转换为行
    # 前3列不动，跟rtm相同的列做转换
    ai_fcst_df_melted = pd.melt(ai_df, id_vars=['rtm', 'sold_to_id', 'mpn'],
                                value_vars=IDEAL_DEMAND_FINAL_FORECAST_HEADER_RAW[10:],
                                var_name='fiscal_week', value_name='ai_fcst')
    
    merged_melted_df = pd.merge(combine_fcst_df_melted, ai_fcst_df_melted,
                                on=['rtm', 'sold_to_id', 'mpn', 'fiscal_week'],
                                how='left')
    merged_melted_df = pd.merge(merged_melted_df, rtm_fcst_df_melted,
                                on=['rtm', 'sold_to_id', 'mpn', 'fiscal_week'],
                                how='left')

    # 可以统一做替换
    # 将转换后的fiscal_week列替换为真实的周
    merged_melted_df['fiscal_week'] = merged_melted_df['fiscal_week'].replace(fiscal_week_mapping)
    
    # fiscal_quarter dataframe
    # orient='index'参数来确保将外层键作为索引
    fiscal_quarter_df = pd.DataFrame.from_dict(fiscal_result, orient='index')
    
    # 去掉abbre
    # 查询sold_to_id 与abbre 关系的dataframe
    # sold_to_abbre_df = DimIdealRtmSoldtoMappingA.get_business_type_relation(fiscal_week_year)
    # sold_to_abbre_df = sold_to_abbre_df[['sold_to_id', 'abbre']]
    
    # 需要做类型转化，不然会匹配不到abbre
    # sold_to_abbre_df['sold_to_id'] = sold_to_abbre_df['sold_to_id'].astype(int)

    # 增加根据mpn查询产品表中的nand/color
    mpn_nand_color_df = DimPubProdInfo.get_mpn_nand_color(IDEAL_DEMAND_LOB)
    
    
    merged_quarter_df = pd.merge(merged_melted_df, fiscal_quarter_df,
                                 on=['fiscal_week'], how='left')
    # merged_abbre_df = pd.merge(merged_quarter_df, sold_to_abbre_df,
    #                            on=['sold_to_id'], how='left')
    merged_df = pd.merge(merged_quarter_df, mpn_nand_color_df, 
                         on=['mpn'], how='left')
    # 将 ai_fcst 列的空值替换为 -
    merged_df['ai_fcst'].replace({np.nan: '-'}, inplace=True)
    
    # 保存文件
    df = merged_df[CPF_IDEAL_DEMAND_FINAL_FORECAST_HEADER_RAW]
    df.columns = CPF_IDEAL_DEMAND_FINAL_FORECAST_HEADER
    sheet_name = CPF_IDEAL_DEMAND_FINAL_FORECAST_SHEET_NAME
    file_name = f"CP&F_Final_Forecast_{week_date}.xlsx"
    file_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__))) + f'/uploads/allocation/{file_name}'
    df.to_excel(file_path, sheet_name=sheet_name, index=False)


def is_time_to_display_file(fiscal_week_year, config_name):
        
    display_file_config = FastLiteIdealDemandDeadlineConfig.query_config_by_name(config_name)
    if len(display_file_config) > 0:
        now = datetime.now()
        # 如果不应用到历史周，则需要判断是否是历史周
        if not display_file_config[0].is_apply_history:
            week_info = FiscalYearWeek.get_week_by_date(now.strftime(DateFormat))
            if fiscal_week_year < week_info.get(StrFiscalWeekYear):
                return True
        
        # 周日是第一天, 周一: 1, 周日: 7
        current_weekday = now.weekday() + 1
        if current_weekday == 7 or current_weekday < display_file_config[0].week:
            return False
        elif current_weekday == display_file_config[0].week \
            and now.strftime(DateTimeFormat) < now.strftime(DateFormat) + ' ' + display_file_config[0].time:
            return False
        else:
            return True
    else:
        # 没有设置Deadline就不会限制
        return True


def rtm_not_upload_forecast_reminder(fiscal_dt: str, rtm: str):
    if not fiscal_dt:
        fiscal_dt = datetime.now().strftime(DateFormat)
    
    week_info = FiscalYearWeek.get_week_by_date(fiscal_dt)
    week_date = week_info.get(StrFiscalQtrWeekName)
    # 查看是否上传
    upload_record = FastLiteIdealDemandRTMForecastUpload.count_by_rtm_week(rtm, week_date)    
    if not upload_record:
        ideal_demand_list_link = get_list_page_url('ideal-demand')
        IdealDemandTemplateEmail().ideal_demand_rtm_forecast_reminder(week_date, rtm, ideal_demand_list_link)
    return f'{week_date} {rtm} ' + ('uploaded.' if upload_record else 'not uploaded.')


def cpf_not_upload_adjustment_reminder(fiscal_dt: str, adjustment_type: str):
    if not fiscal_dt:
        fiscal_dt = datetime.now().strftime(DateFormat)
    
    week_info = FiscalYearWeek.get_week_by_date(fiscal_dt)
    week_date = week_info.get(StrFiscalQtrWeekName)
    # 查看是否上传
    if adjustment_type == 'x':
        upload_record = FastLiteIdealDemandTWOSAdjustmentX.count_by_week(week_date)
    elif adjustment_type == 'y':
        upload_record = FastLiteIdealDemandTWOSAdjustmentY.count_by_week(week_date)
    else:
       exit()
    if not upload_record:
        ideal_demand_list_link = get_list_page_url('ideal-demand')
        IdealDemandTemplateEmail().ideal_demand_adjustment_x_y_reminder(week_date, adjustment_type.upper(), ideal_demand_list_link)
    return f'{week_date} Adjustment ({adjustment_type.upper()}) ' + ('uploaded.' if upload_record else 'not uploaded.')


def rtm_not_upload_forecast_expired(fiscal_dt: str, rtm: str):
    if not fiscal_dt:
        fiscal_dt = datetime.now().strftime(DateFormat)
    
    week_info = FiscalYearWeek.get_week_by_date(fiscal_dt)
    week_date = week_info.get(StrFiscalQtrWeekName)
    # 查看是否上传
    upload_record = FastLiteIdealDemandRTMForecastUpload.count_by_rtm_week(rtm, week_date)
    if not upload_record:
        ideal_demand_list_link = get_list_page_url('ideal-demand')
        IdealDemandTemplateEmail().ideal_demand_rtm_forecast_expired(week_date, rtm, ideal_demand_list_link)
    return f'{week_date} {rtm} ' + ('uploaded.' if upload_record else 'not uploaded.')


def cpf_not_upload_adjustment_expired(fiscal_dt: str, adjustment_type: str):
    if not fiscal_dt:
        fiscal_dt = datetime.now().strftime(DateFormat)
    
    week_info = FiscalYearWeek.get_week_by_date(fiscal_dt)
    week_date = week_info.get(StrFiscalQtrWeekName)
    # 查看是否上传
    if adjustment_type == 'x':
        upload_record = FastLiteIdealDemandTWOSAdjustmentX.count_by_week(week_date)
    elif adjustment_type == 'y':
        upload_record = FastLiteIdealDemandTWOSAdjustmentY.count_by_week(week_date)
    else:
       exit()
    if not upload_record:
        ideal_demand_list_link = get_list_page_url('ideal-demand')
        IdealDemandTemplateEmail().ideal_demand_adjustment_x_y_expired(week_date, adjustment_type.upper(), ideal_demand_list_link)
    return f'{week_date} Adjustment ({adjustment_type.upper()}) ' + ('uploaded.' if upload_record else 'not uploaded.')


def is_so_eoh_data_ready(rtm: str, fiscal_week_year: int):
    count_so_eoh = 0
    if rtm == AllocationRTM.Mono:
        count_so_eoh = AppFastIdealDemandMonoSoEohWi.count_by_lob_week(fiscal_week_year, IDEAL_DEMAND_LOB)
    elif rtm == AllocationRTM.Online:
        count_so_eoh = AppFastIdealDemandOnlineSoEohWi.count_by_lob_week(fiscal_week_year, IDEAL_DEMAND_LOB)
    return True if count_so_eoh > 0 else False


def is_so_eoh_data_ready_service(fiscal_dt: str, rtm: str):
    if not fiscal_dt:
        fiscal_dt = datetime.now().strftime(DateFormat)
    last_week = datetime.strptime(fiscal_dt, DateFormat) - timedelta(days=7)
    week_info = FiscalYearWeek.get_week_by_date(last_week.strftime(DateFormat))
    fiscal_week_year = week_info.get(StrFiscalWeekYear)
    so_eoh_status = is_so_eoh_data_ready(rtm, fiscal_week_year)
    if not so_eoh_status:
        # 如果数据没有ready，发送告警邮件
        IdealDemandTemplateEmail().ideal_demand_so_eoh_not_ready(rtm)
    return f'{rtm} is ' + ('ready' if so_eoh_status else 'not ready') + f' in {week_info.get(StrFiscalQtrWeekName)}.'


def is_cpf_ideald_demand_data_ready(fiscal_qtr_week_name: str):
    count_ideald_demand = AppFastIdealDemandTemplateWi.count_by_week(fiscal_qtr_week_name)
    return True if count_ideald_demand > 0 else False


def is_cpf_ideal_demand_data_ready_service(fiscal_dt: str):
    if not fiscal_dt:
        fiscal_dt = datetime.now().strftime(DateFormat)
    week_info = FiscalYearWeek.get_week_by_date(fiscal_dt)
    fiscal_qtr_week_name = week_info.get(StrFiscalQtrWeekName)
    cpf_ideal_demand_status = is_cpf_ideald_demand_data_ready(fiscal_qtr_week_name)
    if not cpf_ideal_demand_status:
        # 如果数据没有ready，发送告警邮件
        IdealDemandTemplateEmail().ideal_demand_cpf_ideal_demand_not_ready()
    return 'CP&F ideal demand is ' + ('ready' if cpf_ideal_demand_status else 'not ready') + f' in {fiscal_qtr_week_name}.'

