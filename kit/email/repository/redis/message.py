from util.const import FAST_EMAIL_SUFFIX
from util.redis_pool import GetRedis


class EmailMessageRepo:
    def __init__(self, message_id: str):
        self.message_id = message_id

    def key(self):
        return f'{FAST_EMAIL_SUFFIX}{self.message_id}'

    def is_duplicated(self, ttl) -> bool:
        cli = GetRedis()
        res = cli.setnx(self.key(), 1)
        if not res:
            return True
        # 设置过期
        cli.expire(self.key(), ttl)
        return False

    def delete_duplicated(self) -> int:
        cli = GetRedis()
        return cli.delete(self.key())
