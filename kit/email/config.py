import threading
from typing import Optional
import json


class EmailConfig:
    def __init__(self, cmd: str,
                 content_type: str, subject: str,
                 content: str, params: str, attachments: str,
                 recipients: str, cc: str, bcc: str,
                 is_base_template: bool, frequency: Optional[str],
                 from_email: str = '') -> None:
        self.cmd = cmd
        self.content_type = content_type
        self.subject = subject
        self.content = content
        self.params = params
        self.attachments = attachments

        self.to_recipients = []
        self.cc_recipients = []
        self.bcc_recipients = []
        if recipients:
            self.to_recipients = replace_invalid_chars(recipients)
        if cc:
            self.cc_recipients = replace_invalid_chars(cc)
        if bcc:
            self.bcc_recipients = replace_invalid_chars(bcc)

        self.is_base_template = is_base_template
        self.frequency = frequency

        self.from_email = from_email

    def __repr__(self) -> str:
        return f'cmd: {self.cmd}, content-type:{self.content_type}, subject:{self.subject}, params:{self.params}, attachments:{self.attachments}, recipients:{self.to_recipients}, cc:{self.cc_recipients}, bcc:{self.bcc_recipients}, is_base_template:{self.is_base_template}, frequency:{self.frequency}'


def replace_invalid_chars(text: str):
    return text.replace(' ', '').strip(',').split(',')
