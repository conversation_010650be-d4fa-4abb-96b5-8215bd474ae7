from concurrent.futures import ThreadPoolExecutor
from datetime import datetime, timed<PERSON>ta
from jinja2 import Template
from data.databend.dashboard.daily_ub_tracking_summary import DailyUbTrackingSummaryDi
from data.databend.dashboard.weekly_ub_tracking_summary import WeeklyUbTrackingSummaryDi
from domain.dashboard.entity.anti_fraud.anti_fraud_so_ub import RTM_Mono, RTM_CARRIER, RTM_MULTI
from domain.dashboard.entity.anti_fraud.eoh_aging_by_nand import EOHAgingByNand
from domain.dashboard.entity.fiscal_week import FiscalWeek
from kit.email.sender import CustomizedData
from task_kit.worker.antifraud_so_ub_email_worker import convert_daily, convert_sub_lob_weekly, convert_week, \
    convert_sub_lob, convert_platform_daily, convert_platform_week
from util.const import ALL

html_content = '''
<!doctype html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">

<head>
    <title>
    </title>
    <!--[if !mso]><!-->
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!--<![endif]-->
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        #outlook a {
            padding: 0;
        }

        body {
            margin: 0;
            padding: 0;
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }

        table,
        td {
            border-collapse: collapse;
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
        }

        img {
            border: 0;
            height: auto;
            line-height: 100%;
            outline: none;
            text-decoration: none;
            -ms-interpolation-mode: bicubic;
        }

        p {
            display: block;
            margin: 13px 0;
        }

    </style>
    <!--[if mso]>
    <noscript>
        <xml>
            <o:OfficeDocumentSettings>
                <o:AllowPNG/>
                <o:PixelsPerInch>96</o:PixelsPerInch>
            </o:OfficeDocumentSettings>
        </xml>
    </noscript>
    <![endif]-->
    <!--[if lte mso 11]>
    <style>
        .mj-outlook-group-fix { width:100% !important; }
    </style>
    <![endif]-->
    <style>
        @media only screen and (min-width:480px) {
            .mj-column-per-100 {
                width: 100% !important;
                max-width: 100%;
            }
        }

    </style>
    <style media="screen and (min-width:480px)">
        .moz-text-html .mj-column-per-100 {
            width: 100% !important;
            max-width: 100%;
        }

    </style>
    <style>
    </style>
    <style>
        @media (prefers-color-scheme: dark) {
            .body {
                background: #232425;
            }

            .container {
                padding: 16px;
                background: #232425;
            }

            .container>table {
                background: #232425;
            }

            .header {
                background-color: rgba(254, 254, 254, 1);
                color: #1C1C1E;
                border-radius: 12px;
                position: relative;
            }

            .header-text {
                padding: 0 6px;
            }

            .header .apple-icon {
                background-image: url(data:image/png;base64,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);
            }

            .header .apple-icon {
                position: absolute;
                right: 0;
                bottom: 0;
                width: 90px;
                height: 60px;
                display: block;
                z-index: 0;
            }

            .sub-title {
                display: flex;
                justify-content: space-between;
                font-size: 12px;
            }

            .sub-title p {
                margin: 6px 0 0;
            }

            .apple-icon,
            .title,
            .sub-title {
                color: #1C1C1E;
            }

            .icon {
                color: #1C1C1E;
            }

            .title {
                position: relative;
                left: 2px;
            }

            .date {
                font-size: 10px;
                color: #6E6E73;
                padding: 0 2px;
                line-height: 12px;
            }

            .type-btn-title {
                display: flex;
                justify-content: space-between;
                align-items: center;
                font-size: 14px;
                color: #1C1C1E;
            }

            .type-btn-title p:first-child {
                font-weight: 500;
                margin: 0;
                font-size: 16px;
                color: #fff;
            }

            .type-btn-title p:last-child {
                font-size: 10px;
                color: #aeaeb2;
            }

            .wrap-box-div {
                padding: 24px 0;
            }

            .wrap-box-div .timeTip {
                color: #aeaeb2;
                margin-top: 10px;
                font-size: 10px;
            }

            .wrap-box-div .lightTip {
                color: rgba(255, 149, 0);
                margin-top: 2px;
                margin-bottom: 0;
                font-size: 10px;
                font-weight: 400;
            }

            table {
                border-collapse: separate;
            }

            .tableTitle {
                font-size: 14px;
                font-weight: 500;
                color: #fff;
            }

            .first-tr,
            .two-tr {
                background: #313136;
            }

            .first-tr th {
                border-top: 1px solid #6e6e73;
            }

            .first-tr th:first-child {
                border-bottom: none;
                border-top-left-radius: 12px;
                border-left: 1px solid #6e6e73;
            }

            .first-tr th:last-child.totalSeven {
                width: 50px;
            }

            .qtd-perf .two-tr th:last-child,
            .qtd-perf tr td:last-child {
                border-right: 1px solid #6e6e73;
            }

            .first-tr th:last-child {
                border-top-right-radius: 12px;
                border-right: 1px solid #6e6e73;
            }

            .qtd-perf tr td:first-child {
                border-left: 1px solid #6e6e73;
            }

            .qtd-perf tr:last-child td {
                border-bottom: 1px solid #6e6e73;
            }

            .qtd-perf tr:last-child td:first-child {
                border-left: 1px solid #6e6e73;
                border-bottom: 1px solid #6e6e73;
                border-bottom-left-radius: 12px;
            }

            .qtd-perf tr:last-child td:last-child {
                border-right: 1px solid #6e6e73;
                border-bottom: 1px solid #6e6e73;
                border-bottom-right-radius: 12px;
            }

            .qtd-perf th,
            .qtd-perf td {
                font-weight: 400;
                border-bottom: 1px solid #6e6e73;
                border-right: 1px solid #6e6e73;
                line-height: 10px;
                padding: 10px 8px;
            }

            .qtd-perf .two-tr th:nth-child(2),
            .qtd-perf .two-tr th:nth-child(4),
            .qtd-perf .two-tr th:nth-child(5),
            .qtd-perf .two-tr th:nth-child(6),
            .qtd-perf .two-tr th:nth-child(7),
            .qtd-perf .two-tr th:nth-child(9),
            .qtd-perf tr td:nth-child(2),
            .qtd-perf tr td:nth-child(4),
            .qtd-perf tr td:nth-child(5),
            .qtd-perf tr td:nth-child(6),
            .qtd-perf tr td:nth-child(7),
            .qtd-perf tr td:nth-child(9) {
                border-right: none;
            }

            .qtd-perf .two-tr th:last-child,
            .qtd-perf tr td:last-child {
                border-right: 1px solid #6e6e73;
                color: rgba(255, 149, 0);
            }

            .qtd-perf .two-tr th:nth-child(7),
            .qtd-perf .two-tr th:nth-child(8),
            .qtd-perf .two-tr th:nth-child(9),
            .qtd-perf .two-tr th:nth-child(10),
            .qtd-perf tr td:nth-child(7),
            .qtd-perf tr td:nth-child(8),
            .qtd-perf tr td:nth-child(9),
            .qtd-perf tr td:nth-child(10) {
                color: rgba(255, 149, 0);
            }

            .qtd-perf td {
                color: #fff;
            }

            .qtd-perf tr td:first-child {
                white-space: nowrap;
            }

            .qtd-perf th {
                font-size: 8px;
                color: #aeaeb2;
                white-space: nowrap;
                background: #313136;
                padding: 8px;
            }

            .two-tr th {
                text-align: left;
            }

            .two-tr th:first-child {
                border-left: 1px solid #6e6e73;
            }

            .qtd-perf .one td {
                font-size: 10px;
                font-weight: 500;
                line-height: 12px;
            }

            .qtd-perf .two td {
                color: #fff;
                font-size: 9px;
            }

            .qtd-perf .three td {
                color: #fff;
                font-size: 9px;
                padding: 10px 0 0 8px;
            }

            .subLob .qtd-perf .three td,
            .overall .qtd-perf .three td {
                color: #aeaeb2;
            }
            .subLob .qtd-perf .three td:nth-child(7),
            .subLob .qtd-perf .three td:nth-child(8),
            .subLob .qtd-perf .three td:nth-child(9),
            .subLob .qtd-perf .three td:nth-child(10),
            .overall .qtd-perf .three td:nth-child(7),
            .overall .qtd-perf .three td:nth-child(8),
            .overall .qtd-perf .three td:nth-child(9),
            .overall .qtd-perf .three td:nth-child(10) {
                color: rgba(255, 149, 0);
            }

            .qtd-perf .four td {
                color: #aeaeb2;
                font-size: 9px;
                padding: 10px 0 0 8px;
                border-bottom: none;
            }

            .qtd-perf .three:not(:has(+ .three)):not(:has(+ .four)) td {
                border-bottom: 1px solid #6e6e73;
                padding-bottom: 10px;
            }

            .qtd-perf .four:not(:has(+ .four)):not(:has(+ .three)) td {
                border-bottom: 1px solid #6e6e73;
                padding-bottom: 10px;
            }

            .qtd-perf .four td:nth-child(1) {
                padding-left: 18px;
            }

            .qtd-perf .two:not(:has(+ .two)):has(+ .three) td,
            .qtd-perf .two:not(:has(+ .two)):has(+ .four) td {
                padding-bottom: 0;
            }

            .qtd-perf .two:not(:has(+ .two)):has(+ .one) td {
                border-bottom: 1px solid #6e6e73;
            }

            .qtd-perf .three:not(:has(+ .three)):has(+ .two) td {
                border-bottom: 1px solid #6e6e73;
            }

            .qtd-perf .two:has(+ .two) td {
                border-bottom: 1px solid #6e6e73;
            }

            .qtd-perf .three td:nth-child(1) {
                padding-left: 13px;
            }

            .qtd-perf .two td,
            .qtd-perf .three td {
                border-bottom: none;
            }

            .qtd-perf .two:first-child td,
            .qtd-perf .three:first-child td,
            .qtd-perf .four:first-child td {
                border-bottom: 1px solid #6e6e73;
            }

            .footer {
                background-color: #34343b;
                border-radius: 12px;
            }

            .footer-hint {
                color: #d1d1d6;
                font-size: 10px;
                line-height: 16px;
            }

            .footer-hint-text {
                color: #4F78E3;
                text-decoration: none;
            }

            .hint-text {
                color: #8e8e93;
                text-align: center;
                font-size: 10px;
                font-weight: 400;
                line-height: 14px;
            }
        }

        @media (prefers-color-scheme: light) {
            .body {
                background: #fff;
            }

            .container {
                padding: 16px;
                background: #fff;
            }

            .container>table {
                background: #fff;
            }

            .header {
                background-color: #1C1C1E;
                border-radius: 12px;
                position: relative;
            }

            .header-text {
                padding: 0 6px;
            }

            .header .apple-icon {
                background-image: url(data:image/png;base64,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);
                position: absolute;
                right: 0;
                bottom: 0;
                width: 90px;
                height: 60px;
                display: block;
                z-index: 0;
            }

            .icon {
                color: #fff;
            }

            .apple-icon {
                color: #fff;
            }

            .title {
                position: relative;
                left: 2px;
                color: #fff;
            }

            .sub-title {
                display: flex;
                justify-content: space-between;
                font-size: 12px;
                color: #fff;
            }

            .sub-title p {
                margin: 6px 0 0;
            }

            .date {
                font-size: 10px;
                color: #6E6E73;
                padding: 0 2px;
                line-height: 12px;
            }

            table {
                border-collapse: separate;
            }

            .tableTitle {
                font-size: 14px;
                font-weight: 500;
            }

            .wrap-box-div {
                padding: 24px 0;
            }

            .wrap-box-div .timeTip {
                color: #6e6e73;
                margin-top: 10px;
                font-size: 10px;
            }

            .wrap-box-div .lightTip {
                color: #ff9500;
                margin-top: 2px;
                margin-bottom: 0;
                font-size: 10px;
                font-weight: 400;
            }

            .type-btn-title {
                display: flex;
                justify-content: space-between;
                align-items: center;
                font-size: 14px;
                color: #1C1C1E;
            }

            .type-btn-title p:first-child {
                font-weight: 500;
                margin: 0;
                font-size: 16px;
            }

            .type-btn-title p:last-child {
                font-size: 10px;
                color: #6e6e73;
            }

            .first-tr,
            .two-tr {
                background: #f5f5f7;
            }

            .first-tr th {
                border-top: 1px solid #E5E5EA;
            }

            .first-tr th:last-child.totalSeven {
                width: 50px;
            }

            .first-tr th:first-child {
                border-bottom: none;
                border-top-left-radius: 12px;
                border-left: 1px solid #E5E5EA;
            }

            .first-tr th:last-child {
                border-top-right-radius: 12px;
                border-right: 1px solid #E5E5EA;
            }

            .qtd-perf tr td:first-child {
                border-left: 1px solid #E5E5EA;
            }

            .qtd-perf tr:last-child td {
                border-bottom: 1px solid #E5E5EA;
            }

            .qtd-perf tr:last-child td:first-child {
                border-left: 1px solid #E5E5EA;
                border-bottom: 1px solid #E5E5EA;
                border-bottom-left-radius: 12px;
            }

            .qtd-perf tr:last-child td:last-child {
                border-right: 1px solid #E5E5EA;
                border-bottom: 1px solid #E5E5EA;
                border-bottom-right-radius: 12px;
            }

            .qtd-perf th,
            .qtd-perf td {
                border-bottom: 1px solid #E5E5EA;
                border-right: 1px solid #E5E5EA;
                font-weight: 400;
                line-height: 10px;
                padding: 10px 8px;
            }

            .qtd-perf .two-tr th:nth-child(2),
            .qtd-perf .two-tr th:nth-child(4),
            .qtd-perf .two-tr th:nth-child(5),
            .qtd-perf .two-tr th:nth-child(6),
            .qtd-perf .two-tr th:nth-child(7),
            .qtd-perf .two-tr th:nth-child(9),
            .qtd-perf tr td:nth-child(2),
            .qtd-perf tr td:nth-child(4),
            .qtd-perf tr td:nth-child(5),
            .qtd-perf tr td:nth-child(6),
            .qtd-perf tr td:nth-child(7),
            .qtd-perf tr td:nth-child(9) {
                border-right: none;
            }

            .qtd-perf .two-tr th:last-child,
            .qtd-perf tr td:last-child {
                border-right: 1px solid #E5E5EA;
                color: #ff9500;
            }

            .qtd-perf .two-tr th:nth-child(7),
            .qtd-perf .two-tr th:nth-child(8),
            .qtd-perf .two-tr th:nth-child(9),
            .qtd-perf .two-tr th:nth-child(10),
            .qtd-perf tr td:nth-child(7),
            .qtd-perf tr td:nth-child(8),
            .qtd-perf tr td:nth-child(9),
            .qtd-perf tr td:nth-child(10) {
                color: #ff9500;
            }

            .qtd-perf td {
                color: #3a3a3c;
            }

            .qtd-perf tr td:first-child {
                white-space: nowrap;
            }

            .qtd-perf th {
                font-size: 8px;
                color: #6e6e73;
                white-space: nowrap;
                background: #f5f5f7;
                padding: 8px;
            }

            .two-tr th {
                text-align: left;
            }

            .two-tr th:first-child {
                border-left: 1px solid #E5E5EA;
            }

            .qtd-perf .one td {
                font-size: 10px;
                font-weight: 500;
                line-height: 12px;
            }

            .qtd-perf .two td {
                font-size: 9px;
                color: #3a3a3c;
            }

            .qtd-perf .three td {
                color: #3a3a3c;
                font-size: 9px;
                padding: 10px 0 0 8px;
            }

            .qtd-perf .four td {
                color: #6e6e73;
                font-size: 9px;
                padding: 10px 0 0 8px;
                border-bottom: none;
            }

            .qtd-perf .three:not(:has(+ .three)):not(:has(+ .four)) td {
                border-bottom: 1px solid #e5e5ea;
                padding-bottom: 10px;
            }

            .qtd-perf .four:not(:has(+ .four)):not(:has(+ .three)) td {
                border-bottom: 1px solid #e5e5ea;
                padding-bottom: 10px;
            }

            .qtd-perf .four td:nth-child(1) {
                padding-left: 18px;
            }

            .qtd-perf .two:not(:has(+ .two)):has(+ .one) td {
                border-bottom: 1px solid #e5e5ea;
            }

            .qtd-perf .two:not(:has(+ .two)):has(+ .three) td,
            .qtd-perf .two:not(:has(+ .two)):has(+ .four) td {
                padding-bottom: 0;
            }

            .qtd-perf .three:not(:has(+ .three)):has(+ .two) td {
                border-bottom: 1px solid #e5e5ea;
            }

            .qtd-perf .three td:nth-child(1) {
                padding-left: 13px;
            }

            .qtd-perf .two td,
            .qtd-perf .three td {
                border-bottom: none;
            }

            .qtd-perf .two:first-child td,
            .qtd-perf .three:first-child td,
            .qtd-perf .four:first-child td {
                border-bottom: 1px solid #e5e5ea;
            }

            .qtd-perf .two:has(+ .two) td {
                border-bottom: 1px solid #e5e5ea;
            }

            .footer {
                background-color: #f5f5f7;
                border-radius: 12px;
            }

            .footer-hint {
                color: #3a3a3c;
                font-size: 10px;
                line-height: 16px;
            }

            .footer-hint-text {
                color: #4F78E3;
                text-decoration: none;
            }

            .hint-text {
                color: #6e6e73;
                text-align: center;
                font-size: 10px;
                line-height: 14px;
                font-weight: 400;
            }
        }
    </style>
    <meta name="color-scheme" content="light dark">
    <meta name="supported-color-schemes" content="light dark">
</head>

<body style="word-spacing:normal;">
<div class="body" style="">
    <!--[if mso | IE]><table align="center" border="0" cellpadding="0" cellspacing="0" class="container-outlook" role="presentation" style="width:600px;" width="600" ><tr><td style="line-height:0px;font-size:0px;mso-line-height-rule:exactly;"><![endif]-->
    <div class="container" style="margin:0px auto;max-width:600px;">
        <table align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="width:100%;">
            <tbody>
            <tr>
                <td style="direction:ltr;font-size:0px;padding:0;text-align:center;">
                    <!--[if mso | IE]><table role="presentation" border="0" cellpadding="0" cellspacing="0"><![endif]-->
                    <!-- 页眉区域 -->
                    <!--[if mso | IE]><tr><td class="" width="600px" ><table align="center" border="0" cellpadding="0" cellspacing="0" class="" role="presentation" style="width:600px;" width="600" ><tr><td style="line-height:0px;font-size:0px;mso-line-height-rule:exactly;"><![endif]-->
                    <div style="margin:0px auto;max-width:600px;">
                        <table align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="width:100%;">
                            <tbody>
                            <tr>
                                <td style="direction:ltr;font-size:0px;padding:0;text-align:center;">
                                    <!--[if mso | IE]><table role="presentation" border="0" cellpadding="0" cellspacing="0"><tr><td class="header-outlook" style="vertical-align:top;width:600px;" ><![endif]-->
                                    <div class="mj-column-per-100 mj-outlook-group-fix header" style="font-size:0px;text-align:left;direction:ltr;display:inline-block;vertical-align:top;width:100%;">
                                        <table border="0" cellpadding="0" cellspacing="0" role="presentation" style="vertical-align:top;" width="100%">
                                            <tbody>
                                            <tr>
                                                <td align="left" style="font-size:0px;padding:16px 24px;word-break:break-word;">
                                                    <div style="font-family:'SF Pro','PingFang SC';font-size:13px;line-height:1;text-align:left;color:#000000;">
                                                        <div class="header-text">
                                                            <div>
                                                                <span class="icon"></span>
                                                                <span class="title">Expert</span>
                                                            </div>
                                                            <div class="sub-title">
                                                                <p style="font-size: 17px; font-weight: 600;">iPhone UB Summary</p>
                                                                <p>{{ send_date }}</p>
                                                            </div>
                                                        </div>
                                                        <div class="apple-icon"></div>
                                                    </div>
                                                </td>
                                            </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                    <!--[if mso | IE]></td></tr></table><![endif]-->
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <!--[if mso | IE]></td></tr></table></td></tr><tr><td class="" width="600px" ><table align="center" border="0" cellpadding="0" cellspacing="0" class="" role="presentation" style="width:600px;" width="600" ><tr><td style="line-height:0px;font-size:0px;mso-line-height-rule:exactly;"><![endif]-->
                    <div style="margin:0px auto;max-width:600px;">
                        <table align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="width:100%;">
                            <tbody>
                            <tr>
                                <td style="direction:ltr;font-size:0px;padding:0;text-align:center;">
                                    <!--[if mso | IE]><table role="presentation" border="0" cellpadding="0" cellspacing="0"><tr><td class="" style="vertical-align:top;width:600px;" ><![endif]-->
                                    <div class="mj-column-per-100 mj-outlook-group-fix" style="font-size:0px;text-align:left;direction:ltr;display:inline-block;vertical-align:top;width:100%;">
                                        <table border="0" cellpadding="0" cellspacing="0" role="presentation" style="vertical-align:top;" width="100%">
                                            <tbody>
                                            <tr>
                                                <td align="left" style="font-size:0px;padding:0 2px 0 8px;word-break:break-word;">
                                                    <div style="font-family:'SF Pro','PingFang SC';font-size:13px;line-height:1;text-align:left;color:#000000;">
                                                        <div class="wrap-box-div">
                                                            <div class="type-btn-title">
                                                                <p>iPhone UB Summary</p>
                                                                <p style="margin: 0">Updated to:{{ data_as_of_date }}</p>
                                                            </div>
                                                            <div class="timeTip">The indicator starts to accumulate from 2024/08/18.</div>
                                                            <h4 class="lightTip">The weekly indicators marked in orange will change over time.</h4>
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                    <!--[if mso | IE]></td></tr></table><![endif]-->
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <!--[if mso | IE]></td></tr></table></td></tr><![endif]-->
                    
                                        <!--[if mso | IE]></td></tr></table></td></tr><tr><td class="overall-outlook" width="600px" ><table align="center" border="0" cellpadding="0" cellspacing="0" class="overall-outlook" role="presentation" style="width:600px;" width="600" ><tr><td style="line-height:0px;font-size:0px;mso-line-height-rule:exactly;"><![endif]-->
                    <div class="overall" style="margin:0px auto;max-width:600px;">
                        <table align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="width:100%;">
                            <tbody>
                            <tr>
                                <td style="direction:ltr;font-size:0px;padding:0 0 16px 0;text-align:center;">
                                    <!--[if mso | IE]><table role="presentation" border="0" cellpadding="0" cellspacing="0"><tr><td class="" style="vertical-align:top;width:600px;" ><![endif]-->
                                    <div class="mj-column-per-100 mj-outlook-group-fix" style="font-size:0px;text-align:left;direction:ltr;display:inline-block;vertical-align:top;width:100%;">
                                        <table border="0" cellpadding="0" cellspacing="0" role="presentation" style="vertical-align:top;" width="100%">
                                            <tbody>
                                            <tr>
                                                <td align="left" style="font-size:0px;padding:0 0 8px 8px;word-break:break-word;">
                                                    <div style="font-family:'SF Pro','PingFang SC';font-size:13px;line-height:1;text-align:left;color:#000000;">
                                                        <div class="tableTitle">Overall</div>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td align="left" class="qtd-perf" style="font-size:0px;padding:0;word-break:break-word;">
                                                    <table cellpadding="0" cellspacing="0" width="100%" border="0" style="color:#000000;font-family:'SF Pro','PingFang SC';font-size:13px;line-height:22px;table-layout:auto;width:100%;border:none;">
                                                        <tr class="first-tr">
                                                            <th></th>
                                                            <th colspan="2">QTD</th>
                                                            <th colspan="5">Weekly Trend<br />(≤ 7 days %)</th>
                                                            {% if customized_data.get('day_weeks') | length  > 1 %}
                                                                <th colspan="2">D-1 <br />(≤ 7 days %)</th>
                                                            {% else %}
                                                                <th class="totalSeven" colspan="1">D-1 <br />(≤ 7 days %)</th>
                                                            {% endif %}
                                                        </tr>
                                                        <tr class="two-tr">
                                                            <th></th>
                                                            <th>≤ 3<br /> days %</th>
                                                            <th>≤ 7<br /> days %</th>
                                                            {% for week in customized_data.get('rolling_weeks') %}
                                                                {% if customized_data.get('day_weeks') | length  > 1 %}
                                                                    <th>{{ week.quarter_name() }}<br />{{ week.week_name() }}</th>
                                                                {% else %}
                                                                    <th class="noLight">{{ week.quarter_name() }}<br />{{ week.week_name() }}</th>
                                                                {% endif %}
                                                            {% endfor %}
                                                            {% for day_week in customized_data.get('day_weeks') %}
                                                                <th>{{ day_week.quarter_name() }}<br />{{ day_week.week_name() }}</th>
                                                            {% endfor %}
                                                        </tr>
                                                        {% for row in customized_data.get('data') %}
                                                            {% if customized_data.get('rtm_version') == 'All' or customized_data.get('rtm_version') == row.get('rtm') %}
                                                                <tr class="{{ row.get('level') }}">
                                                                    {% set index = loop.index-1 %}
                                                                    <td>
                                                                        {% if row.get('level') == 'two' %}
                                                                            -{{ row.get('field1') }}
                                                                        {% else %}
                                                                            {{ row.get('field1') }}
                                                                        {% endif %}
                                                                    </td>
                                                                    <td>
                                                                        {{ row.get('lte_3_days', '-') }}
                                                                    </td>
                                                                    <td>
                                                                        {{ row.get('lte_7_days', '-') }}
                                                                    </td>
                                                                    {% set weekly_data_cw1 = customized_data.get('weekly_data_cw1')[index] %}
                                                                    <td>
                                                                        {{  weekly_data_cw1.get('lte_7_days', '-') }}
                                                                    </td>
                                                                    {% set weekly_data_cw2 = customized_data.get('weekly_data_cw2')[index] %}
                                                                    <td>
                                                                        {{  weekly_data_cw2.get('lte_7_days', '-') }}
                                                                    </td>
                                                                    {% if customized_data.get('day_weeks') | length  > 1 %}
                                                                        {% set weekly_data_cw3 = customized_data.get('weekly_data_cw3')[index] %}
                                                                        <td>
                                                                            {{  weekly_data_cw3.get('lte_7_days', '-') }}
                                                                        </td>
                                                                    {% else %}
                                                                        {% set weekly_data_cw3 = customized_data.get('weekly_data_cw3')[index] %}
                                                                        <td class="noLight">
                                                                            {{  weekly_data_cw3.get('lte_7_days', '-') }}
                                                                        </td>
                                                                    {% endif %}
                                                                    {% set weekly_data_cw4 = customized_data.get('weekly_data_cw4')[index] %}
                                                                    <td>
                                                                        {{  weekly_data_cw4.get('lte_7_days', '-') }}
                                                                    </td>
                                                                    {% set weekly_data_cw5 = customized_data.get('weekly_data_cw5')[index] %}
                                                                    <td>
                                                                        {{  weekly_data_cw5.get('lte_7_days', '-') }}
                                                                    </td>
                                                                    {% set weekly_data_d1_cw1 = customized_data.get('weekly_data_d1_cw1')[index] %}
                                                                    <td>
                                                                        {{  weekly_data_d1_cw1.get('lte_7_days', '-') }}
                                                                    </td>
                                                                    {% if customized_data.get('weekly_data_d1_cw2') %}
                                                                        {% set weekly_data_d1_cw2 = customized_data.get('weekly_data_d1_cw2')[index] %}
                                                                        <td>
                                                                            {{  weekly_data_d1_cw2.get('lte_7_days', '-') }}
                                                                        </td>
                                                                    {% endif %}
                                                                </tr>
                                                            {% endif %}
                                                        {% endfor %}
                                                    </table>
                                                </td>
                                            </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                    <!--[if mso | IE]></td></tr></table><![endif]-->
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    
                    <!-- 页脚区域 -->
                    <!--[if mso | IE]><tr><td class="" width="600px" ><table align="center" border="0" cellpadding="0" cellspacing="0" class="" role="presentation" style="width:600px;" width="600" ><tr><td style="line-height:0px;font-size:0px;mso-line-height-rule:exactly;"><![endif]-->
                    <div style="margin:0px auto;max-width:600px;">
                        <table align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="width:100%;">
                            <tbody>
                            <tr>
                                <td style="direction:ltr;font-size:0px;padding:8px 0 0 0;text-align:center;">
                                    <!--[if mso | IE]><table role="presentation" border="0" cellpadding="0" cellspacing="0"><tr><td class="" style="vertical-align:top;width:600px;" ><![endif]-->
                                    <div class="mj-column-per-100 mj-outlook-group-fix" style="font-size:0px;text-align:left;direction:ltr;display:inline-block;vertical-align:top;width:100%;">
                                        <table border="0" cellpadding="0" cellspacing="0" role="presentation" style="vertical-align:top;" width="100%">
                                            <tbody>
                                            <tr>
                                                <td align="left" class="footer" style="font-size:0px;padding:18px;word-break:break-word;">
                                                    <div style="font-family:'SF Pro','PingFang SC';font-size:13px;line-height:1;text-align:left;color:#000000;">
                                                        <div class="footer-hint">
                                                            <div style="text-align:center">If you have any questions,</div>
                                                            <div style="text-align:center">please contact <a href="mailto:<EMAIL>" class="footer-hint-text"><EMAIL></a>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td align="left" style="font-size:0px;padding:16px 0 0;word-break:break-word;">
                                                    <div style="font-family:'SF Pro','PingFang SC';font-size:13px;line-height:1;text-align:left;color:#000000;">
                                                        <div class="hint-text"> For internal use only. Sent from Expert Technical Support. </div>
                                                    </div>
                                                </td>
                                            </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                    <!--[if mso | IE]></td></tr></table><![endif]-->
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <!--[if mso | IE]></td></tr></table></td></tr></table><![endif]-->
                </td>
            </tr>
            </tbody>
        </table>
    </div>
    <!--[if mso | IE]></td></tr></table><![endif]-->
</div>
</body>

</html>
'''


# def test_sender_render():
#     refresh_time = datetime.now()

#     data = [
#         DailyItem('2024-09-11', RTM_Mono, 'Lifestyle', 'iPhone 15', 1000, 100, 300),
#         DailyItem('2024-09-11', RTM_Mono, 'MONO', 'iPhone 15', 1000, 100, 300),
#         DailyItem('2024-09-11', RTM_MULTI, 'Duty Free', 'iPhone 15', 1000, 100, 300),
#         DailyItem('2024-09-11', RTM_MULTI, 'Mass Merchant', 'iPhone 15', 1000, 100, 300),
#         DailyItem('2024-09-11', RTM_MULTI, 'OTC', 'iPhone 15', 1000, 100, 300),
#         DailyItem('2024-09-11', RTM_MULTI, 'Township', 'iPhone 15', 1000, 100, 300),
#         DailyItem('2024-09-11', RTM_CARRIER, 'CB', 'iPhone 15', 1000, 100, 300),
#         DailyItem('2024-09-11', RTM_CARRIER, 'CM', 'iPhone 15', 1000, 100, 300),
#         DailyItem('2024-09-11', RTM_CARRIER, 'CM', 'iPhone 15', 1000, 100, 300),
#         DailyItem('2024-09-11', RTM_CARRIER, 'CU', 'iPhone 15', 1000, 100, 300),
#     ]

#     weekly_data = [
#         WeeklyItem('2024-09-11', 202450, 'FY24Q4W10', RTM_Mono, 'Lifestyle', 'iPhone 15', 1000, 300),
#         WeeklyItem('2024-09-11',  202450, 'FY24Q4W10', RTM_Mono, 'MONO', 'iPhone 15', 1000, 300),
#         WeeklyItem('2024-09-11',  202450, 'FY24Q4W10', RTM_MULTI, 'Duty Free', 'iPhone 15', 1000, 300),
#         WeeklyItem('2024-09-11',  202450, 'FY24Q4W10', RTM_MULTI, 'Mass Merchant', 'iPhone 15', 1000, 300),
#         WeeklyItem('2024-09-11',  202450, 'FY24Q4W10', RTM_MULTI, 'OTC', 'iPhone 15', 1000, 300),
#         WeeklyItem('2024-09-11',  202450, 'FY24Q4W10', RTM_MULTI, 'Township', 'iPhone 15', 1000, 300),
#         WeeklyItem('2024-09-11',  202450, 'FY24Q4W10', RTM_CARRIER, 'CB', 'iPhone 15', 1000, 300),
#         WeeklyItem('2024-09-11',  202450, 'FY24Q4W10', RTM_CARRIER, 'CM', 'iPhone 15', 1000, 300),
#         WeeklyItem('2024-09-11',  202450, 'FY24Q4W10', RTM_CARRIER, 'CM', 'iPhone 15', 1000, 300),
#         WeeklyItem('2024-09-11',  202450, 'FY24Q4W10', RTM_CARRIER, 'CU', 'iPhone 15', 1000, 300),
#     ]

#     weekly_data1 = WeeklyUbTrackingSummaryDi.query_week_ub_summary_records('2024-09-11','FY24Q4W10')

#     render_data = {
#         "fiscal_week": FiscalWeek('FY24Q1W4'),
#         "data": convert_daily(data),
#         "weekly_data": convert_week(weekly_data),
#         "weekly_data1": convert_week(weekly_data1),
#     }
#     customized_data = CustomizedData(refresh_time, render_data)
#     print(customized_data.as_dict())
#     content = Template(html_content).render(**customized_data.as_dict())

#     file_path = './rendered_table.html'
#     # 使用with语句打开文件，确保文件正确关闭
#     with open(file_path, 'w', encoding='utf-8') as file:
#         file.write(content)

def test_sender_send():
    now = datetime.now()
    # now = datetime.now() - timedelta(days=1) # 调试使用
    current_date = now.strftime('%Y-%m-%d')
    t_1_date = (now - timedelta(days=1)).strftime('%Y-%m-%d')
    # 数据刷新时间固定就是t-1, 如果没有数据就需要报错，且不要发邮件
    refresh_time = datetime.strptime(t_1_date, '%Y-%m-%d')
    # 调试的时候手动把这里改成需要的周
    rolling_weeks, d_minus1_weeks = ['FY24Q4W8', 'FY24Q4W9', 'FY24Q4W10', 'FY24Q4W11', 'FY24Q4W12'], ['FY24Q4W11']
    # 2\加载数据组数据

    rtm_version = ALL
    # ----------overall-------------------------
    # 根据rtm-sub_rtm-sub_lob构造的 daily 数据
    data = DailyUbTrackingSummaryDi.query_daily_ub_summary_records(t_1_date)
    futures_weekly_summary = []
    pool_weekly_summary = ThreadPoolExecutor(max_workers=len(rolling_weeks))
    for week in rolling_weeks:
        futures_weekly_summary.append(
            pool_weekly_summary.submit(WeeklyUbTrackingSummaryDi.query_week_ub_summary_records, current_date, week)
        )
    results_weekly_summary = [future.result() for future in futures_weekly_summary]
    pool_weekly_summary.shutdown()
    weekly_data_cw1 = results_weekly_summary[0]
    weekly_data_cw2 = results_weekly_summary[1]
    weekly_data_cw3 = results_weekly_summary[2]
    weekly_data_cw4 = results_weekly_summary[3]
    weekly_data_cw5 = results_weekly_summary[4]

    futures_weekly_summary_d_minus_1 = []
    pool_weekly_summary_d_minus_1 = ThreadPoolExecutor(max_workers=len(d_minus1_weeks))
    for week in d_minus1_weeks:
        futures_weekly_summary_d_minus_1.append(
            pool_weekly_summary_d_minus_1.submit(WeeklyUbTrackingSummaryDi.query_week_ub_summary_records, t_1_date, week)
        )
    results_weekly_summary_d_minus_1 = [future.result() for future in futures_weekly_summary_d_minus_1]
    pool_weekly_summary_d_minus_1.shutdown()
    weekly_data_d1_cw1 = results_weekly_summary_d_minus_1[0]
    weekly_data_d1_cw2 = None
    if len(d_minus1_weeks) == 2:
        weekly_data_d1_cw2 = results_weekly_summary_d_minus_1[1]

    # ----------sublob------------------------
    daily_data1 = DailyUbTrackingSummaryDi.query_region_daily_ub_summary_records(fiscal_dt=t_1_date)
    futures_region_week_summarys = []
    pool_weekly_summary = ThreadPoolExecutor(max_workers=len(rolling_weeks))
    for week in rolling_weeks:
        futures_region_week_summarys.append(
            pool_weekly_summary.submit(WeeklyUbTrackingSummaryDi.query_region_week_ub_summary_records, current_date, week)
        )
    results_region_weekly_summary = [future.result() for future in futures_region_week_summarys]
    pool_weekly_summary.shutdown()
    # 查询rtm=all，按照sublob聚合的数据
    week_data1 = results_region_weekly_summary[0]
    week_data2 = results_region_weekly_summary[1]
    week_data3 = results_region_weekly_summary[2]
    week_data4 = results_region_weekly_summary[3]
    week_data5 = results_region_weekly_summary[4]

    futures_region_d1_summarys = []
    pool_region_d1 = ThreadPoolExecutor(max_workers=len(d_minus1_weeks))
    for week in d_minus1_weeks:
        futures_region_d1_summarys.append(
            pool_region_d1.submit(WeeklyUbTrackingSummaryDi.query_region_week_ub_summary_records, t_1_date, week)
        )
    results_region_d1_summary = [future.result() for future in futures_region_d1_summarys]
    pool_region_d1.shutdown()
    week_data_d1_w1 = results_region_d1_summary[0]
    week_data_d1_w2 = None
    if len(d_minus1_weeks) == 2:
        week_data_d1_w2 = results_region_d1_summary[1]

    #  # ----------platform------------------------
    platform_data = DailyUbTrackingSummaryDi.query_platform_daily_ub_summary_records(t_1_date)
    platform_rolling_pool = ThreadPoolExecutor(max_workers=len(rolling_weeks))
    platform_futures = []
    for week in rolling_weeks:
        platform_futures.append(
            platform_rolling_pool.submit(WeeklyUbTrackingSummaryDi.query_platform_week_ub_summary_records,current_date,week)
        )
    platform_week_results = [future.result() for future in platform_futures]
    platform_rolling_pool.shutdown()
    platform_weekly_data_cw1 = platform_week_results[0]
    platform_weekly_data_cw2 = platform_week_results[1]
    platform_weekly_data_cw3 = platform_week_results[2]
    platform_weekly_data_cw4 = platform_week_results[3]
    platform_weekly_data_cw5 = platform_week_results[4]

    platform_d1_pool = ThreadPoolExecutor(max_workers=len(d_minus1_weeks))
    platform_d1_futures = []
    for d_minus1_week in d_minus1_weeks:
        platform_d1_futures.append(
            platform_d1_pool.submit(
                WeeklyUbTrackingSummaryDi.query_platform_week_ub_summary_records, t_1_date, d_minus1_week
            )
        )
    platform_d1_results = [future.result() for future in platform_d1_futures]
    platform_d1_pool.shutdown()
    platform_weekly_data_d1_cw1 = platform_d1_results[0]
    platform_weekly_data_d1_cw2 = None
    if len(d_minus1_weeks) == 2:
        platform_weekly_data_d1_cw2 = platform_d1_results[1]

    convert_daily_data = convert_daily(data, rtm_version)
    convert_week_cw1_data = convert_week(weekly_data_cw1)

    if len(convert_daily_data) == 0 or len(convert_week_cw1_data) == 0:
        raise Exception(f"convert daily data or covert week cw1 data is empty")

    render_data = {
        # "fiscal_week": FiscalWeek('FY24Q1W4'),
        "data": convert_daily_data,
        "weekly_data_cw1": convert_week_cw1_data,
        "weekly_data_cw2": convert_week(weekly_data_cw2),
        "weekly_data_cw3": convert_week(weekly_data_cw3),
        "weekly_data_cw4": convert_week(weekly_data_cw4),
        "weekly_data_cw5": convert_week(weekly_data_cw5),
        "weekly_data_d1_cw1": convert_week(weekly_data_d1_cw1),
        "weekly_data_d1_cw2": convert_week(weekly_data_d1_cw2),
        "sub_lob_data": convert_sub_lob(data),
        "rtm_version": rtm_version,
        "sub_lob_weekly_data_cw1": convert_sub_lob_weekly(weekly_data_cw1),
        "sub_lob_weekly_data_cw2": convert_sub_lob_weekly(weekly_data_cw2),
        "sub_lob_weekly_data_cw3": convert_sub_lob_weekly(weekly_data_cw3),
        "sub_lob_weekly_data_cw4": convert_sub_lob_weekly(weekly_data_cw4),
        "sub_lob_weekly_data_cw5": convert_sub_lob_weekly(weekly_data_cw5),
        "sub_lob_weekly_data_d1_cw1": convert_sub_lob_weekly(weekly_data_d1_cw1),
        "sub_lob_weekly_data_d1_cw2": convert_sub_lob_weekly(weekly_data_d1_cw2),
        "daily_data1": daily_data1,
        "week_data1": week_data1,
        "week_data2": week_data2,
        "week_data3": week_data3,
        "week_data4": week_data4,
        "week_data5": week_data5,
        "week_data_d1_w1": week_data_d1_w1,
        "week_data_d1_w2": week_data_d1_w2,

        "platform_data": convert_platform_daily(platform_data, rtm_version),
        "platform_weekly_data_cw1": convert_platform_week(platform_weekly_data_cw1),
        "platform_weekly_data_cw2": convert_platform_week(platform_weekly_data_cw2),
        "platform_weekly_data_cw3": convert_platform_week(platform_weekly_data_cw3),
        "platform_weekly_data_cw4": convert_platform_week(platform_weekly_data_cw4),
        "platform_weekly_data_cw5": convert_platform_week(platform_weekly_data_cw5),
        "platform_weekly_data_d1_cw1": convert_platform_week(platform_weekly_data_d1_cw1),
        "platform_weekly_data_d1_cw2": convert_platform_week(platform_weekly_data_d1_cw2),
        "rolling_weeks": [FiscalWeek(fiscal_week_name=fiscal_week_name) for fiscal_week_name in rolling_weeks],
        "day_weeks": [FiscalWeek(fiscal_week_name=fiscal_week_name) for fiscal_week_name in d_minus1_weeks]
    }
    customized_data = CustomizedData(refresh_time, render_data)
    content = Template(html_content).render(**customized_data.as_dict())
    
    file_path = './rendered_table.html'
    # 使用with语句打开文件，确保文件正确关闭
    with open(file_path, 'w', encoding='utf-8') as file:
        file.write(content)


def test_pos_ub_sender():
    rtm_version = ALL
    # 打开文件
    with open('./html_templete/aaa.html', 'r') as file:
        #     读取文件内容并存储为字符串
        html_content = file.read()

    now = datetime.now()
    # now = datetime.now() - timedelta(days=1) # 调试使用
    current_date = now.strftime('%Y-%m-%d')
    t_1_date = (now - timedelta(days=1)).strftime('%Y-%m-%d')
    # 数据刷新时间固定就是t-1, 如果没有数据就需要报错，且不要发邮件
    refresh_time = datetime.strptime(t_1_date, '%Y-%m-%d')
    # 调试的时候手动把这里改成需要的周
    rolling_weeks, d_minus1_weeks = ['FY24Q4W8', 'FY24Q4W9', 'FY24Q4W10', 'FY24Q4W11', 'FY24Q4W12'], ['FY24Q4W11']
    data = DailyUbTrackingSummaryDi.query_daily_ub_summary_records(t_1_date)
    futures_weekly_summary = []
    pool_weekly_summary = ThreadPoolExecutor(max_workers=len(rolling_weeks))
    for week in rolling_weeks:
        futures_weekly_summary.append(
            pool_weekly_summary.submit(WeeklyUbTrackingSummaryDi.query_week_ub_summary_records, current_date, week)
        )
    results_weekly_summary = [future.result() for future in futures_weekly_summary]
    pool_weekly_summary.shutdown()
    weekly_data_cw1 = results_weekly_summary[0]
    weekly_data_cw2 = results_weekly_summary[1]
    weekly_data_cw3 = results_weekly_summary[2]
    weekly_data_cw4 = results_weekly_summary[3]
    weekly_data_cw5 = results_weekly_summary[4]

    render_data = {
        # "fiscal_week": FiscalWeek('FY24Q1W4'),
        "over_all_ub_qtd": convert_daily(data, rtm_version),
        "over_all_ub_cw1": convert_week(weekly_data_cw1),
        "over_all_ub_cw2": convert_week(weekly_data_cw2),
        "over_all_ub_cw3": convert_week(weekly_data_cw3),
        "over_all_ub_cw4": convert_week(weekly_data_cw4),
        "over_all_ub_cw5": convert_week(weekly_data_cw5),
        # "sub_lob_ub_qtd": [],
        # "sub_lob_ub_cw1": [],
        # "sub_lob_ub_cw2": [],
        # "sub_lob_ub_cw3": [],
        # "sub_lob_ub_cw4": [],
        # "sub_lob_ub_cw5": [],
        "rtm_version": rtm_version,
        "rolling_weeks": [FiscalWeek(fiscal_week_name=week) for week in rolling_weeks],
        "day_weeks": [FiscalWeek(fiscal_week_name=fiscal_week_name) for fiscal_week_name in d_minus1_weeks]
    }

    customized_data = CustomizedData(refresh_time, render_data)
    content = Template(html_content).render(**customized_data.as_dict())

    file_path = './pos_ub_rendered_table.html'
    # 使用with语句打开文件，确保文件正确关闭
    with open(file_path, 'w', encoding='utf-8') as file:
        file.write(content)





def test_send_eog_aging_email():
    nand_list = [
        EOHAgingByNand(ALL, ALL, ALL,1,2,3,4,5,6,7,8),
        EOHAgingByNand(RTM_Mono, ALL, ALL, 1, 2, 3, 4, 5, 6, 7, 8),
        EOHAgingByNand(RTM_Mono, "LifeStyle", ALL, 1, 2, 3, 4, 5, 6, 7, 8),
        EOHAgingByNand(RTM_Mono, "LifeStyle", "hq1", 1, 2, 3, 4, 5, 6, 7, 8),
        EOHAgingByNand(RTM_Mono, "LifeStyle", "hq2", 1, 2, 3, 4, 5, 6, 7, 8),
        EOHAgingByNand(RTM_MULTI, ALL, ALL, 2, 2, 3, 4, 5, 6, 7, 8),
        EOHAgingByNand(RTM_MULTI, "OTC", "hq3", 2, 2, 3, 4, 5, 6, 7, 8),
        EOHAgingByNand(RTM_MULTI, "OTC", "hq4", 2, 2, 3, 4, 5, 6, 7, 8),
    ]

    # 打开文件
    with open('./html_templete/mail6(2).html', 'r') as file:
        #     读取文件内容并存储为字符串
        html_content = file.read()
        render_data = {
            # 这是ub without ds模块的overall
            "over_all_ub_qtd": [],
            "over_all_ub_cw1": [],
            "over_all_ub_cw2": [],
            "over_all_ub_cw3": [],
            "over_all_ub_cw4": [],
            "over_all_ub_cw5": [],

            # 这是ub without ds模块的sublob view
            "sub_lob_ub_qtd": [],
            "sub_lob_ub_cw1": [],
            "sub_lob_ub_cw2": [],
            "sub_lob_ub_cw3": [],
            "sub_lob_ub_cw4": [],
            "sub_lob_ub_cw5": [],

            # 这是ub without ds模块的sublob view(china channel)
            "region_sub_lob_ub_qtd": 1,
            "region_sub_lob_ub_cw1": 1,
            "region_sub_lob_ub_cw2": 1,
            "region_sub_lob_ub_cw3": 1,
            "region_sub_lob_ub_cw4": 1,
            "region_sub_lob_ub_cw5": 1,

            "rtm_version": ALL,
            "rolling_weeks": [FiscalWeek(fiscal_week_name=week) for week in ['FY24Q1W1']],

            # 这是eoh aging模块的overall
            "eoh_aging_overall": [],
            # 这是eoh aging模块的sublob
            "eoh_aging_sublob":[],
            # 这是eoh aging模块的nand view
            "eoh_aging_nand": nand_list,
        }
        customized_data = CustomizedData(datetime.strptime('2024-09-19', '%Y-%m-%d'), render_data)
        content = Template(html_content).render(**customized_data.as_dict())
        file_path = './eoh_aging_rendered_table.html'
        # 使用with语句打开文件，确保文件正确关闭
        with open(file_path, 'w', encoding='utf-8') as file:
            file.write(content)