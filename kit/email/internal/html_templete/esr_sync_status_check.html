<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <!--[if !mso]><!-->
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <!--<![endif]-->
  <meta name="viewport" content="width=device-width">
  <meta name="color-scheme" content="light dark"/>
  <meta name="supported-color-schemes" content="light dark"/>
  <style>
    * {
      margin: 0;
      padding: 0;
    }
    table,
    tr,
    td {
      border-collapse: collapse;
    }
    .container {
      box-sizing: border-box;
      margin: 0px auto;
    }
    .header {
      width: 100%;
      border-radius: 12px;
      position: relative;
    }
    .header-t1 {
      font-size: 14px;
      line-height: 22px;
    }
    .header-t1 div {
      margin-top: -0.18em;
    }
    .header-t2 {
      font-size: 13px;
      line-height: 22px;
      padding-left: 4px;
      padding-bottom: 2px;
    }
    .header-st1 {
      font-weight: 500;
    }
    .header-st2 {
      text-align: right;
    }
    .apple-icon {
      position: absolute;
      right: 0;
      bottom: 0;
      width: 70px;
      height: 80px;
      display: block;
      z-index: 0;
    }

    .body .section {
      padding: 0 8px;
      font-size: 16px;
      line-height: 22px;
      word-break: keep-all;
      color: #6e6e73;
    }

    .contentHeader {
      padding-bottom: 20px;
    }

    .sincerely {
      padding-top: 20px;
    }
    .body .button {
      text-align: center;
    }
    .section-btn {
      display: inline-block;
      width: 140px;
      height: 48px;
      text-align: center;
      border-radius: 24px;
      line-height: 48px;
      font-size: 17px;
      font-weight: 500;
      cursor: pointer;
      margin: 0 auto;
      text-decoration: none;
      overflow: hidden;
      outline: none;
      background-color: rgba(254, 254, 254, 1);
      color: #1C1C1E;
    }
    .section-btn:focus {
      outline: none;
    }

    .footer {
      height: 78px;
      border-radius: 12px;
      text-align: center;
      width: 100%;
    }
    .footer .footer-text {
      color: #3a3a3c;
      text-align: center;
    }
    .footer .footer-link {
      color: #4F78E3;
      text-decoration: none;
    }
    .copyright {
      line-height: 16px;
      padding-top: 16px;
      text-align: center;
      color: #3A3A3C;
    }


    @media (prefers-color-scheme: dark) {
      .padding {
        background-color: #232425;
      }
      .header {
        background-color: #fefefe;
      }
      .header-color {
        color: #1C1C1E;
      }
      .apple-icon {
        background-image: url(data:image/png;base64,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);
      }

      .body {
        color: #FFFFFF;
      }
      .body .section {
        color: #aeaeb2;
      }
      .section-btn {
        background-color: #fefefe;
        color: #1C1C1E;
      }

      .footer {
        background-color: #3a3a3c;
      }
      .footer .footer-text {
        color: #aeaeb2;
      }
      .copyright {
        color: #6e6e73;
      }
    }

    @media (prefers-color-scheme: light) {
      .padding {
        background-color: rgba(255,255,254,1);
      }
      .header {
        background-color: #1C1C1E;
      }
      .header-color {
        color: #fff;
      }
      .apple-icon {
        background-image: url(data:image/png;base64,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);
      }

      .body {
        color: #1C1C1E;
      }
      .section-btn {
        background-color: #1C1C1E;
        color: #ffffff;
      }

      .footer {
        background-color: #F5F5F7;
      }

    }

    @media screen and (min-width: 500px) {
      .container {
        width: 100%;
        max-width: 750px;
      }
      .padding {
        padding: 40px;
      }
      .header {
        padding: 16px 30px;
      }
      .header-st1 {
        font-size: 17px;
        line-height: 22px;
      }
      .header-st2 {
        font-size: 12px;
        line-height: 22px;
      }

      .body {
        padding: 24px 0 36px;
      }

      .body .button {
        padding-top: 24px;
      }
      .footer-text {
        font-size: 14px;
        line-height: 20px;
      }
      .copyright {
        font-size: 12px;
      }
    }

    @media screen and (max-width: 500px ) {
      .container {
        width: 100%;
        max-width: 750px;
      }
      .padding {
        padding: 12px;
      }
      .header {
        padding: 16px;
      }
      .header-st1 {
        font-size: 14px;
        line-height: 22px;
      }
      .header-st2 {
        font-size: 10px;
        line-height: 22px;
      }

      .body {
        padding: 16px 0 36px;
      }
      .body .button {
        padding-top: 16px;
      }
      .section-btn {
        width: 120px;
        height: 40px;
        line-height: 40px;
        font-size: 14px;
      }

      .footer-text {
        font-size: 12px;
        line-height: 14px;
      }
      .copyright {
        font-size: 10px;
      }
    }
  </style>
</head>
<body style="margin: 0 auto; padding: 0; width: 100%; max-width: 750px;">
<table class="container" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation">
  <tr>
    <td class="padding">
      <table align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="width: 100%;">
        <tr>
          <td class="header">
            <table align="center" border="0" cellpadding="0" cellspacing="0" role="presentation">
              <tr>
                <td class="header-color header-t1"><div></div></td>
                <td class="header-color header-t2">Expert</td>
              </tr>
            </table>
            <table align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="width: 100%;">
              <tr>
                <td class="header-color header-st1">{% if customized_data.get("sync_status") == "Refresh" %} Data Source - {% endif %} ESR Data Report {{ customized_data.get("sync_status") }} ({{ customized_data.get("esr_title_version") }})</td>
                <td class="header-color header-st2">{{ send_date }}</td>
              </tr>
            </table>
            <div class="apple-icon"></div>
          </td>
        </tr>
        <tr>
          <td class="body">
            <table align="center" border="0" cellpadding="0" cellspacing="0" role="presentation">
              <tr>
                <td class=""><div class="section">
                  <div class="contentHeader">Dear Planner,</div>
                  <div class="content">The {{ customized_data.get("esr_content_version") }} data as of {{ customized_data.get("snapshot_dt") }} {{ customized_data.get("refresh_version") }} {% if customized_data.get("sync_status") == "Refresh" %} has been updated. You can access full report via FAST - Data Source module. {% else %} has not been generated yet. We will keep you updated for further data update. Please contact DMP team for more information. {% endif %} </div>
                  <div class="sincerely">Sincerely,<br /> Expert Technical Support</div>
                </div></td>
              </tr>
            </table>

          {% if customized_data.get("sync_status") == "Refresh" %}
             <table align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="width: 100%;">
              <tr>
                <td class="button">
                  <a href="{{ customized_data.get('jump_workspace_link') }}" class="section-btn">
                    View
                  </a>
                </td>
              </tr>
            </table>
          {% endif %}
          </td>
        </tr>
        <tr>
          <td class="footer">
            <table align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="width: 100%;">
              <tr>
                <td class="footer-text">If you have any questions, please contact</td>
              </tr>
              <tr>
                <td class="footer-text"><a href="mailto:<EMAIL>" class="footer-link"><EMAIL></a>
                </td>
              </tr>
            </table>
          </td>
        </tr>
        <tr>
          <td class="copyright">For internal use only. Sent from Expert Technical Support.</td>
        </tr>
      </table>
    </td>
  </tr>
</table>
</body>
</html>
