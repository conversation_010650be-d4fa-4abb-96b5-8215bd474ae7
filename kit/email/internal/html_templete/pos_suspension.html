<!DOCTYPE html>
<html
  xmlns="http://www.w3.org/1999/xhtml"
  xmlns:v="urn:schemas-microsoft-com:vml"
  xmlns:o="urn:schemas-microsoft-com:office:office"
>
  <head>
    <title> </title>
    <!--[if !mso]><!-->
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <!--<![endif]-->
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style>
      #outlook a {
        padding: 0;
      }

      body {
        margin: 0;
        padding: 0;
        -webkit-text-size-adjust: 100%;
        -ms-text-size-adjust: 100%;
      }

      table,
      td {
        border-collapse: collapse;
        mso-table-lspace: 0pt;
        mso-table-rspace: 0pt;
      }

      img {
        border: 0;
        height: auto;
        line-height: 100%;
        outline: none;
        text-decoration: none;
        -ms-interpolation-mode: bicubic;
      }

      p {
        display: block;
        margin: 13px 0;
      }
    </style>
    <!--[if mso]>
      <noscript>
        <xml>
          <o:OfficeDocumentSettings>
            <o:AllowPNG />
            <o:PixelsPerInch>96</o:PixelsPerInch>
          </o:OfficeDocumentSettings>
        </xml>
      </noscript>
    <![endif]-->
    <!--[if lte mso 11]>
      <style>
        .mj-outlook-group-fix {
          width: 100% !important;
        }
      </style>
    <![endif]-->
    <style>
      @media only screen and (min-width: 480px) {
        .mj-column-per-100 {
          width: 100% !important;
          max-width: 100%;
        }
      }
      @media only screen and (min-width: 600px) {
        .mj-column-per-100 {
          width: 100% !important;
          max-width: 100%;
        }
      }
    </style>
    <style media="screen and (min-width:480px)">
      .moz-text-html .mj-column-per-100 {
        width: 100% !important;
        max-width: 100%;
      }
    </style>
    <style media="screen and (min-width:600px)">
      .moz-text-html .mj-column-per-100 {
        width: 100% !important;
        max-width: 100%;
      }
    </style>
    <style></style>
    <style>
      .tableWrapper {
        min-width: 500px;
      }

      .tableWrapper > table {
        padding: 16px;
      }

      .header {
        border-radius: 12px;
        position: relative;
      }

      .header .apple-icon {
        position: absolute;
        right: 0;
        bottom: 0;
        width: 90px;
        height: 60px;
        display: block;
        z-index: 0;
      }

      .sub-title {
        display: flex;
        justify-content: space-between;
        font-size: 12px;
        align-items: baseline;
      }

      .sub-title p {
        margin: 6px 0 0;
      }

      .title {
        position: relative;
        left: 2px;
      }

      .date {
        font-size: 10px;
        padding: 0 2px;
        line-height: 12px;
      }

      .timeTip {
        font-size: 10px;
        font-weight: 400;
        line-height: 14px;
        color: #aeaeb2;
      }

      .lightTip {
        display: block;
        color: #ff9500;
      }

      .tableTitle {
        font-size: 14px;
        font-weight: 500;
        padding: 24px 8px 8px;
        line-height: 20px;
      }

      .tableTitle.tableOver {
        padding-top: 8px;
      }

      table {
        border-collapse: separate;
      }

      .wrap-box-div {
        padding: 24px 0 0 0;
      }

      .type-btn-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 4px;
      }

      .type-btn-title p:first-child {
        font-weight: 500;
        margin: 0;
        font-size: 16px;
        line-height: 20px;
      }

      .type-btn-title p:last-child {
        font-size: 10px;
      }

      .qtd-perf th,
      .qtd-perf td {
        font-weight: 400;
        border-bottom-width: 1px;
        border-bottom-style: solid;
        line-height: 10px;
        padding: 10px 8px;
      }

      .qtd-perf th {
        font-size: 8px;
        white-space: nowrap;
        padding-top: 5px;
        padding-bottom: 5px;
      }

      .qtd-perf td {
        font-size: 10px;
        line-height: 12px;
      }

      .qtd-perf td div {
        font-size: 9px;
      }

      .qtd-perf td:not(:nth-child(1)) div {
        font-size: 8px;
      }

      .first-tr th:nth-child(2),
      .two-tr th {
        text-align: left;
      }

      .first-tr th {
        border-top-width: 1px;
        border-top-style: solid;
        border-right-width: 1px;
        border-right-style: solid;
      }

      .first-tr th:first-child {
        border-top-left-radius: 12px;
        width: 30%;
      }

      .first-tr th:last-child {
        border-top-right-radius: 12px;
        border-right-width: 1px;
        border-right-style: solid;
        width: 50%;
      }

      .two-tr th {
        width: 25%;
      }

      .qtd-perf .two-tr th,
      .qtd-perf tr td {
        border-right-width: 1px;
        border-right-style: solid;
        white-space: nowrap;
      }

      .qtd-perf .first-tr th:first-child,
      .qtd-perf tr td:first-child {
        border-left-width: 1px;
        border-left-style: solid;
      }

      .qtd-perf tr td:first-child {
        white-space: nowrap;
      }

      .qtd-perf tr:last-child td {
        border-bottom-width: 1px;
        border-bottom-style: solid;
      }

      .qtd-perf tr:last-child td:first-child {
        border-bottom-left-radius: 12px;
      }

      .qtd-perf tr:last-child td:last-child {
        border-bottom-right-radius: 12px;
      }

      .qtd-perf tr td.highlightRed div {
        color: #f63f54;
      }

      .qtd-perf tr td.highlightGreen div {
        color: #68d58f;
      }

      .qtd-perf .one td {
        font-weight: 500;
      }

      .qtd-perf .one td div {
        font-weight: 400;
      }

      .qtd-perf td:last-child span {
        display: inline-block;
        border-radius: 7px;
        padding: 0 4px;
        font-size: 8px;
        font-weight: 400;
        margin-left: 4px;
        color: #ffae3d;
        vertical-align: text-bottom;
      }

      .qtd-perf .two td {
        font-size: 9px;
      }

      .qtd-perf .two td:first-child div {
        padding-left: 5px;
      }

      .qtd-perf tr.two td {
        padding-top: 8px;
      }

      .qtd-perf.NDT1View .two td {
        border-bottom: none;
        padding-bottom: 0;
      }

      .qtd-perf.NDT1View .two:not(:has(+ .two)) td {
        border-bottom-width: 1px;
        border-bottom-style: solid;
        padding-bottom: 10px;
      }

      .footer {
        border-radius: 12px;
      }

      .footer-hint {
        font-size: 10px;
        line-height: 16px;
      }

      .footer-hint-text {
        text-decoration: none;
      }

      .hint-text {
        text-align: center;
        font-size: 10px;
        line-height: 14px;
        font-weight: 400;
      }

      .qtd-perf tr.two-tr th:nth-child(1), .qtd-perf tr.two-tr th:nth-child(2),.qtd-perf tr.two-tr th:nth-child(4){
        border-right: none;
      }
      .qtd-perf tr td:nth-child(3),
      .qtd-perf tr td:nth-child(4),
      .qtd-perf tr td:nth-child(6) {
        border-right: none;
      }


      @media (prefers-color-scheme: dark) {
        .tableWrapper > table {
          background: #232425;
        }

        .header {
          background-color: rgba(254, 254, 254, 1);
          color: #1c1c1e;
        }

        .header .apple-icon {
          background-image: url(data:image/png;base64,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);
        }

        .apple-icon,
        .title,
        .sub-title {
          color: #1c1c1e;
        }

        .icon {
          color: #1c1c1e;
        }

        .date {
          color: #6e6e73;
        }

        .type-btn-title p:first-child {
          color: #fff;
        }

        .type-btn-title p:last-child {
          color: #aeaeb2;
        }

        .tableTitle {
          color: #fff;
        }

        .first-tr,
        .two-tr {
          background: #313136;
        }

        .qtd-perf th,
        .qtd-perf td,
        .qtd-perf.NDT1View .two:not(:has(+ .two)) td {
          border-color: #6e6e73;
        }

        .qtd-perf td:last-child span {
          background-color: #393227;
        }

        .qtd-perf .bold td {
          color: #fff;
        }
        .qtd-perf  td {
          color: #AEAEB2;
        }

        .qtd-perf th {
          background: #313136;
        }

        .qtd-perf th,
        .qtd-perf.overAll .two td {
          color: #e5e5ea;
        }

        .qtd-perf.NDT1View .two td {
          color: #aeaeb2;
        }

        .qtd-perf td div {
          color: #6e6e73;
        }

        .footer {
          background-color: #34343b;
        }

        .footer-hint {
          color: #d1d1d6;
        }

        .footer-hint-text {
          color: #4f78e3;
        }

        .hint-text {
          color: #8e8e93;
        }
        .qtd-perf .three-tr td{
        color:  #AEAEB2;
      }
      .qtd-perf .three-tr td div{
        color:  #6E6E73;
      }
      }

      @media (prefers-color-scheme: light) {
        .header {
          background-color: #1c1c1e;
        }

        .header .apple-icon {
          background-image: url(data:image/png;base64,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);
        }

        .icon {
          color: #fff;
        }

        .apple-icon {
          color: #fff;
        }

        .title {
          color: #fff;
        }

        .date {
          color: #6e6e73;
        }

        .apple-icon,
        .title,
        .sub-title {
          color: #fff;
        }

        .type-btn-title p:first-child {
          color: #1c1c1e;
        }

        .type-btn-title p:last-child {
          color: #6e6e73;
        }

        .first-tr,
        .two-tr {
          background: #f5f5f7;
        }

        .qtd-perf th,
        .qtd-perf td,
        .qtd-perf.NDT1View .two:not(:has(+ .two)) td {
          border-color: #e5e5ea;
        }

        .qtd-perf td {
          color: #3A3A3C;
        }

        .qtd-perf td div {
          color: #aeaeb2;
        }
        .qtd-perf .bold td div {
          color: #6E6E73;
        }

        .qtd-perf th {
          background: #f5f5f7;
        }

        .qtd-perf td:last-child span {
          background-color: #fdf6ed;
        }

        .qtd-perf .two td,
        .qtd-perf.overAll .two td {
          color: #3a3a3c;
        }

        .qtd-perf th,
        .qtd-perf.NDT1View .two td {
          color: #6e6e73;
        }

        .footer {
          background-color: #f5f5f7;
        }

        .footer-hint {
          color: #3a3a3c;
        }

        .footer-hint-text {
          color: #4f78e3;
        }

        .hint-text {
          color: #6e6e73;
        }
        .qtd-perf .three-tr td{
        color:  #6E6E73;
      }
      .qtd-perf .three-tr td div{
        color:  #AEAEB2;
      }
      }
    </style>
    <style>
      @media screen and (min-width: 600px) {
        .tableWrapper {
          width: 100%;
          max-width: 800px !important;
        }
        .tableWrapper > table {
          padding: 32px 40px !important;
        }
        .tableWrapper .header {
          padding: 16px 30px !important;
        }
        .tableWrapper .header .icon {
          font-size: 17px;
        }
        .tableWrapper .sub-title p:first-child {
          font-size: 16px !important;
        }
        .type-btn-title p:first-child {
          font-size: 16px;
          font-weight: normal !important;
        }
        .type-btn-title p:last-child {
          font-size: 14px !important;
        }
        .wrap-box-div .timeTip {
          padding-top: 8px;
          font-size: 14px;
          line-height: 20px;
        }
        .tableTitle {
          padding: 24px 0 12px 8px !important;
          line-height: 20px;
          font-size: 16px;
        }
        .tableTitle span {
          font-size: 16px;
        }
        .tableTitle div {
          font-size: 16px;
        }
        .tableTitle .timeTip {
          font-size: 14px;
        }
        .footer-hint {
          font-size: 14px !important;
        }
        .hint-text {
          font-size: 12px !important;
        }
        .qtd-perf td {
          font-size: 14px !important;
          line-height: normal !important;
        }
        .qtd-perf .two td:first-child div {
          padding-left: 8px;
        }
        .qtd-perf td div {
          font-size: 14px;
        }
        .qtd-perf th {
          font-size: 13px !important;
          padding: 12px !important;
          line-height: 16px !important;
        }
        .qtd-perf .one td {
          font-size: 14px !important;
          line-height: 20px !important;
          padding: 16px !important;
        }
        .qtd-perf .two td {
          font-size: 14px !important;
          line-height: 20px !important;
          padding: 12px 16px !important;
        }
        .NDT1View .two td {
          padding-bottom: 0 !important;
        }
        .NDT1View .two:not(:has(+ .two)) td {
          padding: 12px 16px !important;
        }
        .qtd-perf-padding {
          padding-top: 16px !important;
        }
        .qtd-perf td:not(:nth-child(1)) div {
          font-size: 13px;
        }
        .qtd-perf td:last-child span {
          font-size: 13px;
        }
        .two-right{
        padding-left: 10px;
      }
      }
    </style>
    <meta name="color-scheme" content="light dark" />
    <meta name="supported-color-schemes" content="light dark" />
  </head>

  <body style="word-spacing: normal">
    <div style="">
      <!--[if mso | IE]><table align="center" border="0" cellpadding="0" cellspacing="0" class="tableWrapper-outlook" role="presentation" style="width:600px;" width="600" ><tr><td style="line-height:0px;font-size:0px;mso-line-height-rule:exactly;"><![endif]-->
      <div class="tableWrapper" style="margin: 0px auto; max-width: 600px">
        <table
          align="center"
          border="0"
          cellpadding="0"
          cellspacing="0"
          role="presentation"
          style="width: 100%"
        >
          <tbody>
            <tr>
              <td
                style="
                  direction: ltr;
                  font-size: 0px;
                  padding: 0 0 16px 0;
                  text-align: center;
                "
              >
                <!--[if mso | IE]><table role="presentation" border="0" cellpadding="0" cellspacing="0"><tr><td class="" style="vertical-align:top;width:600px;" ><![endif]-->
                <div
                  class="mj-column-per-100 mj-outlook-group-fix"
                  style="
                    font-size: 0px;
                    text-align: left;
                    direction: ltr;
                    display: inline-block;
                    vertical-align: top;
                    width: 100%;
                  "
                >
                  <table
                    border="0"
                    cellpadding="0"
                    cellspacing="0"
                    role="presentation"
                    style="vertical-align: top"
                    width="100%"
                  >
                    <tbody>
                      <!-- 页眉区域 -->
                      <tr>
                        <td
                          align="left"
                          class="header"
                          style="
                            font-size: 0px;
                            padding: 16px 24px;
                            word-break: break-word;
                          "
                        >
                          <div
                            style="
                              font-family: 'SF Pro', 'PingFang SC';
                              font-size: 13px;
                              line-height: 1;
                              text-align: left;
                              color: #000000;
                            "
                          >
                            <div class="header-text">
                              <div>
                                <span class="icon"></span>
                                <span class="title">Expert</span>
                              </div>
                              <div class="sub-title">
                                <p style="font-size: 17px; font-weight: 600">
                                  iPhone UB POS Suspension
                                </p>
                                <p>{{ customized_data.get('fiscal_week') }}</p>
                              </div>
                            </div>
                            <div class="apple-icon"></div>
                          </div>
                        </td>
                      </tr>
                      <tr>
                        <td
                          align="left"
                          style="
                            font-size: 0px;
                            padding: 0 2px 0 8px;
                            word-break: break-word;
                          "
                        >
                          <div
                            style="
                              font-family: 'SF Pro', 'PingFang SC';
                              font-size: 13px;
                              line-height: 1;
                              text-align: left;
                              color: #000000;
                            "
                          >
                            <div class="wrap-box-div">
                              <div class="type-btn-title">
                                <p>iPhone UB POS Suspension</p>
                                <p style="margin: 0">
                                  Updated to:{{ data_as_of_date }}
                                </p>
                              </div>
                              <div class="timeTip">
                                Please refer to the attachment at the bottom of
                                the email for the detailed POS list. <br />The
                                CW POS suspension list is based on the 7D UB
                                results from the CW-2's sell-out.
<!--                                <div class="lightTip">-->
<!--                                  The indicator marked in orange represents the-->
<!--                                  threshold "7D UB rate" for the bottom 5% POS.-->
<!--                                </div>-->
                              </div>
                            </div>
                          </div>
                        </td>
                      </tr>
                      <tr>
                        <td
                          align="left"
                          style="
                            font-size: 0px;
                            padding: 0;
                            word-break: break-word;
                          "
                        >
                          <div
                            style="
                              font-family: 'SF Pro', 'PingFang SC';
                              font-size: 13px;
                              line-height: 1;
                              text-align: left;
                              color: #000000;
                            "
                          >
                            <div class="tableTitle tableOver">Overall</div>
                          </div>
                        </td>
                      </tr>
                      <tr>
                        <td
                          align="left"
                          class="qtd-perf overAll"
                          style="
                            font-size: 0px;
                            padding: 0;
                            word-break: break-word;
                          "
                        >
                          <table
                            cellpadding="0"
                            cellspacing="0"
                            width="100%"
                            border="0"
                            style="
                              color: #000000;
                              font-family: 'SF Pro', 'PingFang SC';
                              font-size: 13px;
                              line-height: 22px;
                              table-layout: auto;
                              width: 100%;
                              border: none;
                            "
                          >
                            <tr class="first-tr">
                              <th rowspan="2"></th>
                              <th rowspan="2">
                                New Suspension
                                #POS
                              </th>
                              <th colspan="3">Rolling 26 wks</th>
<!--                              <th colspan="2">Reason Code</th>-->
                            </tr>
                            <tr class="two-tr">
                              <th >1 time</th>
                              <th >2 times</th>
                              <th>3 times</th>
<!--                              <th >7D 0% UB</th>-->
<!--                              <th>Bottom POS</th>-->
                            </tr>
                            {% for row in customized_data.get('overall_data') %}
                              {% if customized_data.get('rtm_version') == 'All' or customized_data.get('rtm_version') == row.rtm %}
                                <tr class="{{ row.level_by_nand(customized_data.get('rtm_version')) }}">
                                {% if row.level_by_nand(customized_data.get('rtm_version')) == 'two' %}
                                  <td>
                                    -{{ row.first_column() }}
                                    <div>WoW</div>
                                  </td>
                                {% else %}
                                  <td>
                                    {{ row.first_column() }}
                                    <div>WoW</div>
                                  </td>
                                {% endif %}
                                {% set total_pos_wow = row.default_percent(row.get_total_pos_wow()) %}
                                <td class="{{ row.get_pos_wow_class_name(total_pos_wow) }}">
                                  {{ row.convert_to_thousands(row.get_total_pos()) }}
                                  <div>
                                    {{ row.get_pos_wow_symbol(total_pos_wow) }}{{ total_pos_wow }}
                                  </div>
                                </td>

                                {% set suspend_1_time_pos_wow = row.default_percent(row.get_suspend_1_time_wow()) %}
                                <td class="{{ row.get_pos_wow_class_name(suspend_1_time_pos_wow) }}">
                                  {{ row.convert_to_thousands(row.suspend_1_time_pos) }}
                                  <div>
                                    {{ row.get_pos_wow_symbol(suspend_1_time_pos_wow) }}{{ suspend_1_time_pos_wow }}
                                  </div>
                                </td>
                               {% set suspend_2_time_pos_wow = row.default_percent(row.get_suspend_2_time_wow()) %}
                                <td class="{{ row.get_pos_wow_class_name(suspend_2_time_pos_wow) }}">
                                  {{ row.convert_to_thousands(row.suspend_2_time_pos) }}
                                  <div>
                                    {{ row.get_pos_wow_symbol(suspend_2_time_pos_wow) }}{{ suspend_2_time_pos_wow }}
                                  </div>
                                </td>
                               {% set suspend_3_time_pos_wow = row.default_percent(row.get_suspend_3_time_wow()) %}
                                <td class="{{ row.get_pos_wow_class_name(suspend_3_time_pos_wow) }}">
                                  {{ row.convert_to_thousands(row.suspend_3_time_pos) }}
                                  <div>
                                    {{ row.get_pos_wow_symbol(suspend_3_time_pos_wow) }}{{ suspend_3_time_pos_wow }}
                                  </div>
                                </td>

<!--                                {% set no_ub_pos_wow = row.default_percent(row.get_no_ub_pos_wow()) %}-->
<!--                                <td class="{{ row.get_pos_wow_class_name(no_ub_pos_wow) }}">-->
<!--                                  {{ row.convert_to_thousands(row.no_ub_pos) }}-->
<!--                                  <div>-->
<!--                                    {{ row.get_pos_wow_symbol(no_ub_pos_wow) }}{{ no_ub_pos_wow }}-->
<!--                                  </div>-->
<!--                                </td>-->
<!--                                {% set bottom_pos_wow = row.default_percent(row.get_bottom_pos_wow()) %}-->
<!--                                <td class="{{ row.get_pos_wow_class_name(bottom_pos_wow) }}">-->
<!--                                  {{ row.convert_to_thousands(row.bottom_pos) }}-->
<!--                                  {# Overall 模块下，sub rtm 级别的行展示 ub7_rate #}-->
<!--                                  {% if row.rtm != 'All' and row.sub_rtm == 'All' and row.default_percent(row.ub7_rate) != '0%' %}-->
<!--                                    <span>≤{{ row.default_percent(row.ub7_rate)}}</span>-->
<!--                                  {% endif %}-->
<!--                                  <div>-->
<!--                                    {{ row.get_pos_wow_symbol(bottom_pos_wow) }}{{bottom_pos_wow }}-->
<!--                                  </div>-->
<!--                                </td>-->
                              </tr>
                              {% endif %}
                            {% endfor %}
                          </table>
                        </td>
                      </tr>
                      <tr>
                        <td
                          align="left"
                          style="
                            font-size: 0px;
                            padding: 0;
                            word-break: break-word;
                          "
                        >
                          <div
                            style="
                              font-family: 'SF Pro', 'PingFang SC';
                              font-size: 13px;
                              line-height: 1;
                              text-align: left;
                              color: #000000;
                            "
                          >
                            <div class="tableTitle">
                              <div>ND/T1 View</div>
                            </div>
                          </div>
                        </td>
                      </tr>
                      <tr>
                        <td
                          align="left"
                          class="qtd-perf NDT1View"
                          style="
                            font-size: 0px;
                            padding: 0 0 24px;
                            word-break: break-word;
                          "
                        >
                          <table
                            cellpadding="0"
                            cellspacing="0"
                            width="100%"
                            border="0"
                            style="
                              color: #000000;
                              font-family: 'SF Pro', 'PingFang SC';
                              font-size: 13px;
                              line-height: 22px;
                              table-layout: auto;
                              width: 100%;
                              border: none;
                            "
                          >
                            <tr class="first-tr">
                              <th rowspan="2"></th>
                              <th rowspan="2">
                                New Suspension
                                #POS
                              </th>
                              <th colspan="3">Rolling 26 wks</th>
<!--                              <th colspan="2">Reason Code</th>-->
                            </tr>
                            <tr class="two-tr">
                              <th >1 time</th>
                              <th >2 times</th>
                              <th>3 times</th>
<!--                              <th >7D 0% UB</th>-->
<!--                              <th>Bottom POS</th>-->
                            </tr>

                            {% for row in customized_data.get('nand_data') %}
                              {% if customized_data.get('rtm_version') == 'All' or customized_data.get('rtm_version') == row.rtm %}
                                {% set total_pos_wow = row.default_percent(row.get_total_pos_wow()) %}
                                {% set no_ub_pos_wow = row.default_percent(row.get_no_ub_pos_wow()) %}
                                {% set bottom_pos_wow = row.default_percent(row.get_bottom_pos_wow()) %}
                                {# 在ND那个层级，如果本周六个数量都为0 那么在这周不显示#}
                                {# 在ND那个层级，如果本周六个数量都为横杠 那么在这周不显示 #}
                                {# 在ND那个层级，特殊情况，分子为0，如果本周三个原因数量都为0, 并且wow都是横杠的 那么在这周不显示 #}
                                {% if row.is_display_item() %}
                                  <tr class="{{ row.level_by_nand(customized_data.get('rtm_version')) }}">
                                    {% if row.level_by_nand(customized_data.get('rtm_version')) == 'two' %}
                                      <td>
                                        -{{ row.first_column() }}
                                        <div>WoW</div>
                                      </td>
                                    {% else %}
                                      <td>
                                        {{ row.first_column() }}
                                        <div>WoW</div>
                                      </td>
                                    {% endif %}

                                    <td class="{{ row.get_pos_wow_class_name(total_pos_wow) }}">
                                      {{ row.convert_to_thousands(row.get_total_pos()) }}
                                      <div>
                                        {{ row.get_pos_wow_symbol(total_pos_wow) }}{{ total_pos_wow }}
                                      </div>
                                    </td>

                                 {% set suspend_1_time_pos_wow = row.default_percent(row.get_suspend_1_time_wow()) %}
                                  <td class="{{ row.get_pos_wow_class_name(suspend_1_time_pos_wow) }}">
                                    {{ row.convert_to_thousands(row.suspend_1_time_pos) }}
                                    <div>
                                      {{ row.get_pos_wow_symbol(suspend_1_time_pos_wow) }}{{ suspend_1_time_pos_wow }}
                                    </div>
                                  </td>

                                 {% set suspend_2_time_pos_wow = row.default_percent(row.get_suspend_2_time_wow()) %}
                                  <td class="{{ row.get_pos_wow_class_name(suspend_2_time_pos_wow) }}">
                                    {{ row.convert_to_thousands(row.suspend_2_time_pos) }}
                                    <div>
                                      {{ row.get_pos_wow_symbol(suspend_2_time_pos_wow) }}{{ suspend_2_time_pos_wow }}
                                    </div>
                                  </td>

                                 {% set suspend_3_time_pos_wow = row.default_percent(row.get_suspend_3_time_wow()) %}
                                  <td class="{{ row.get_pos_wow_class_name(suspend_3_time_pos_wow) }}">
                                    {{ row.convert_to_thousands(row.suspend_3_time_pos) }}
                                    <div>
                                      {{ row.get_pos_wow_symbol(suspend_3_time_pos_wow) }}{{ suspend_3_time_pos_wow }}
                                    </div>
                                  </td>

<!--                                    <td class="{{ row.get_pos_wow_class_name(no_ub_pos_wow) }}">-->
<!--                                      {{ row.convert_to_thousands(row.no_ub_pos) }}-->
<!--                                      <div>-->
<!--                                        {{ row.get_pos_wow_symbol(no_ub_pos_wow) }}{{ no_ub_pos_wow }}-->
<!--                                      </div>-->
<!--                                    </td>-->

<!--                                    <td class="{{ row.get_pos_wow_class_name(bottom_pos_wow) }}">-->
<!--                                      {{ row.convert_to_thousands(row.bottom_pos) }}-->
<!--                                      {# 这里是处理 nd 给sub rtm 级别的行展示ub7_rate #}-->
<!--                                      {% if row.sub_rtm != 'All' and row.disti_name == 'All' and row.default_percent(row.ub7_rate) != '0%' %}-->
<!--                                        <span>≤{{ row.default_percent(row.ub7_rate) }}</span>-->
<!--                                      {% endif %}-->
<!--                                      <div>-->
<!--                                        {{ row.get_pos_wow_symbol(bottom_pos_wow) }}{{ bottom_pos_wow }}-->
<!--                                      </div>-->
<!--                                    </td>-->
                                  </tr>
                                {% endif %}
                              {% endif %}
                            {% endfor %}
                          </table>
                        </td>
                      </tr>
                      <!-- 页脚区域 -->
                      <tr>
                        <td
                          align="left"
                          class="footer"
                          style="
                            font-size: 0px;
                            padding: 18px;
                            word-break: break-word;
                          "
                        >
                          <div
                            style="
                              font-family: 'SF Pro', 'PingFang SC';
                              font-size: 13px;
                              line-height: 1;
                              text-align: left;
                              color: #000000;
                            "
                          >
                            <div class="footer-hint">
                              <div style="text-align: center">
                                If you have any questions, please contact
                              </div>
                              <div style="text-align: center">
                                <a
                                  href="mailto:<EMAIL>"
                                  class="footer-hint-text"
                                  ><EMAIL></a
                                >
                              </div>
                            </div>
                          </div>
                        </td>
                      </tr>
                      <tr>
                        <td
                          align="left"
                          style="
                            font-size: 0px;
                            padding: 16px 0 0;
                            word-break: break-word;
                          "
                        >
                          <div
                            style="
                              font-family: 'SF Pro', 'PingFang SC';
                              font-size: 13px;
                              line-height: 1;
                              text-align: left;
                              color: #000000;
                            "
                          >
                            <div class="hint-text">
                              For internal use only. Sent from Expert Technical
                              Support.
                            </div>
                          </div>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
                <!--[if mso | IE]></td></tr></table><![endif]-->
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <!--[if mso | IE]></td></tr></table><![endif]-->
    </div>
  </body>
</html>
