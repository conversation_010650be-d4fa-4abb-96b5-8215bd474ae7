import json
import os
import traceback
from datetime import datetime
from jinja2 import Template
from data.email_config import EmailConfigRepository
from kit.email.config import EmailConfig
from kit.email.internal.mail_sender import MailSender
from kit.email.is_rtm_version import is_rtm_version
from kit.email.repository.redis.message import EmailMessageRepo
from util.conf import logger
from util.file_util import get_absolute_path
from util.mail_conf import get_main_config_by_from_email


class CustomizedData:
    def __init__(self, refresh_time: datetime, data: dict, send_time_style="simple"):
        self.refresh_time = refresh_time
        self._data = data

    def as_dict(self):
        return {
            "customized_data": self._data,
            "data_as_of_date": self.refresh_time.strftime("%Y/%m/%d"),  # 格式化输出字符串 2024/09/03
            "send_date": get_send_time(),
            "util": is_rtm_version,
        }


class Sender:
    def __init__(self, cmd: str,
                 config: EmailConfig = None,
                 subject_date: str = '',
                 need_check_duplication=True,
                 custom_format_subject=False):
        if config is None:  # 留一个单测和调试的入口
            # 获取数据库中配置的邮件信息，模版内容已经配置到content字段中，不需要额外的params
            query_config = EmailConfigRepository.query_email_config_with_template(cmd)
            # 上面数据库中查询出来的内容，需要转成新的EmailConfig对象
            if query_config is not None:
                config = EmailConfig(
                    query_config.cmd, query_config.content_type,
                    query_config.subject, query_config.content,
                    query_config.params, query_config.attachments,
                    query_config.recipients,query_config.cc,
                    query_config.bcc, query_config.is_base_template,
                    query_config.frequency, query_config.from_email
                )
        self.config = config
        self.custom_format_subject = custom_format_subject

        if not self.custom_format_subject:
            self.__format_subject(subject_date)  # 格式化邮件主题 添加日期

        self.need_check_duplication = need_check_duplication    # 是否需要邮件去重检查

    def __format_subject(self, subject_date: str):
        if not subject_date:
            subject_date = datetime.today().strftime("%Y-%m-%d")
        self.config.subject = self.config.subject.format(
            **{"subject_date": subject_date}
        )

    def __custom_format_subject(self, custom_params: dict):
        self.config.subject = self.config.subject.format(
            **custom_params
        )

    def get_message_id(self):
        return f"{self.config.subject}+{self.config.to_recipients}+{self.config.cc_recipients}"

    def check_duplication(self):
        if not self.need_check_duplication:
            return
        message_id = self.get_message_id()
        # ttl = 24 * 60 * 60  # 默认24小时不能发送同样 主题+收件人+抄送人邮件
        ttl = 5 * 60  # 5分钟不能发送同样 主题+收件人+抄送人邮件
        if EmailMessageRepo(message_id).is_duplicated(ttl):
            logger.info(f"Duplicated message: {message_id} will not be sent. config:{self.config}")
            raise Exception("Duplicate message found.")

    def delete_duplication(self):
        message_id = self.get_message_id()
        return EmailMessageRepo(message_id).delete_duplicated()

    def send(self, customized_data: CustomizedData):
        # 1、check
        # todo 白名单、黑名单
        # todo 频控
        # todo 去重

        # 去重，不允许发送重复消息
        self.check_duplication()

        # 2、实际执行发送
        self._send(customized_data)

    def __get_mail_sender(self):
        # 默认from_email为空时, 走***********************发件人
        mail_config = get_main_config_by_from_email(self.config.from_email)
        return MailSender(mail_config['smtp_host'], mail_config['smtp_port'], mail_config['login'],
                          mail_config['password'])

    def _send(self, content_data: CustomizedData):
        content = ""
        try:
            # 格式转换
            content_data = content_data.as_dict()
            # 校验参数
            if check_render_params(content_data.get('customized_data')):
                raise Exception("content data not found.")

            # 自定义邮件主题
            if self.custom_format_subject:
                self.__custom_format_subject(content_data.get("customized_data"))

            content = Template(self.config.content).render(**content_data)
            # file_path = './pos_ub_rendered_table.html'
            # # 使用with语句打开文件，确保文件正确关闭
            # with open(file_path, 'w', encoding='utf-8') as file:
            #     file.write(content)
            sender = self.__get_mail_sender()

            attachments_json_str = self.config.attachments
            attachments_json_list = []
            if attachments_json_str:
                attachments_json_list = json.loads(attachments_json_str)

            new_attachments_json_list = []
            for tmp_file_path in attachments_json_list:
                directory_name = os.path.dirname(tmp_file_path)
                # 获取文件名（包括扩展名）
                file_name = os.path.basename(tmp_file_path)
                absolute_path = get_absolute_path(directory_name)
                new_file_path = absolute_path + file_name
                new_attachments_json_list.append(new_file_path)

            sender.send(
                self.config.to_recipients,
                self.config.cc_recipients,
                self.config.subject,
                content,
                self.config.content_type,
                new_attachments_json_list,
                None,
                None,
                bcc_reveivers=self.config.bcc_recipients)
        except Exception as e:
            logger.error(f'send email fail. error: {traceback.format_exc()}, '
                         f'config:{self.config.__dict__}, content:{content}')
            raise e

    def set_recipients(self, recipient_list: list[str]):
        self.config.to_recipients = recipient_list

    def set_cc(self, cc_list: list[str]):
        self.config.cc_recipients = cc_list

    def set_bcc(self, bcc_list: list[str]):
        self.config.bcc_recipients = bcc_list


def get_send_time(style="simple"):
    # 格式化日期Sept. 10, 2024
    if style == "simple":
        return datetime.today().strftime("%b. %d, %Y")
    return datetime.today().strftime("%B %d, %Y")


def check_render_params(content_data: (list, dict)):
    # 校验参数
    ret = True
    # 参数空
    if not content_data:
        return ret

    for item in content_data:
        # 存在dict内 参数值
        children = None
        if isinstance(content_data, list):
            if not item:
                continue
            children = item
        elif isinstance(content_data, dict):
            if not content_data.get(item):
                continue
            children = content_data.get(item)

        # 递归校验
        if isinstance(children, (list, dict)):
            # 赋值 递归有效值
            if not check_render_params(children):
                ret = False
                return ret
        else:
            # 存在 有效值
            return False

    return ret
