import json
from dataclasses import dataclass, field
from typing import List, Dict, Optional, Callable, Any

from dataclasses_json import dataclass_json


@dataclass_json
@dataclass
class TreeNode:
    """树节点类"""
    data: Any  # 节点数据
    children: List['TreeNode'] = field(default_factory=list)  # 子节点列表

    def add_child(self, child: 'TreeNode'):
        """添加子节点"""
        self.children.append(child)


class TreeBuilder:
    """树结构构建工具类"""

    @staticmethod
    def build_tree(
            data_list: List[Any],
            id_getter: Callable[[Any], Any],
            parent_id_getter: Callable[[Any], Optional[Any]]
    ) -> List[TreeNode]:
        """
        构建树形结构

        Args:
            data_list: 数据对象列表
            id_getter: 获取节点ID的函数
            parent_id_getter: 获取父节点ID的函数
        eg:
            roots = TreeBuilder.build_tree(
                data_list=departments,
                id_getter=lambda x: x.id,
                parent_id_getter=lambda x: x.parent_id
            )
        Returns:
            根节点列表
        """
        node_map: Dict[Any, TreeNode] = {}
        roots: List[TreeNode] = []

        # 第一遍遍历：创建所有节点
        for data in data_list:
            node_id = id_getter(data)
            if node_id in node_map:
                raise ValueError(f"Duplicate node ID detected: {node_id}")
            node_map[node_id] = TreeNode(data=data)

        # 第二遍遍历：建立父子关系
        for data in data_list:
            node_id = id_getter(data)
            parent_id = parent_id_getter(data)
            current_node = node_map[node_id]

            if parent_id is None or parent_id == "Total":
                roots.append(current_node)
            elif parent_id in node_map:
                parent_node = node_map[parent_id]
                parent_node.add_child(current_node)
            # 可选：处理孤儿节点（没有找到父节点）
            else:
                roots.append(current_node)

        return roots
