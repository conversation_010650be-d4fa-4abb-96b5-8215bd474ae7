import re
from util.conf import logger


class CustomSort:
    def __init__(self):
        pass

    # sublob列表的排序规则：
    # "All",  todo
    # "iPhone 15 Series",
    # "iPhone 15 Pro Tier",
    # "iPhone 15 Consumer Tier",
    # "iPhone 15 Pro Max",
    # "iPhone 15 Pro",
    # "iPhone 15 Plus",
    # "iPhone 15",
    # "iPhone 14 Plus",
    # "iPhone 14",
    # "iPhone 13",
    # "iPhone SE (3rd Gen)"，暂无
    @classmethod
    def sort_iphone_sublobs(cls, sub_lobs):
        return sorted(sub_lobs, key=cls.__sort_by_suffix, reverse=True)

    @classmethod
    def sort_iphone_sublobs_item(cls, sub_lobs):
        return sorted(sub_lobs, key=lambda x: cls.__sort_by_suffix(x["sub_lob"]), reverse=True)

    @classmethod
    def get_sub_lob_order(cls, sub_lob):
        return cls.__sort_by_suffix(sub_lob)

    @classmethod
    def __sort_by_suffix(cls, sub_lob):
        pattern = re.compile(r'iPhone (\d+)(.*)')
        match = pattern.match(sub_lob)
        # 定义已知后缀的排序规则
        suffixes = ["Series", "Pro Tier", "Consumer Tier", "Pro Max", "Pro", "Plus"]
        seris_no = 0
        suffix_no = 0
        if match and match.group(1):
            seris_no = int(match.group(1))
            suffix_part = match.group(2).strip()  # 去除空白
            if suffix_part:
                # 如果后缀为"e"（不区分大小写），赋值为 -1，使其排序键更小
                if suffix_part.lower() == "e":
                    suffix_no = -1
                else:
                    pattern2 = re.compile(r'(Pro Tier|Series|Pro Max|Plus|Consumer Tier|Pro)', re.IGNORECASE)
                    match2 = pattern2.match(suffix_part)
                    try:
                        if match2 and match2.group(1):
                            # 采用不区分大小写的方式查找匹配值在列表中的位置
                            suffix_matched = match2.group(1)
                            suffixes_lower = [s.lower() for s in suffixes]
                            idx = suffixes_lower.index(suffix_matched.lower())
                            suffix_no = len(suffixes) - idx
                        else:
                            # 未匹配到已知后缀，则当作无后缀处理（0）
                            suffix_no = 0
                    except IndexError:
                        suffix_no = 0
                    except Exception as e:
                        logger.exception(e)
            # 若没有后缀，则 suffix_no 维持为 0
        else:
            if sub_lob == 'All':
                seris_no = (len(suffixes) + 1) * 100
            else:
                logger.debug("badcase:", sub_lob)
        return seris_no * 100 + suffix_no
