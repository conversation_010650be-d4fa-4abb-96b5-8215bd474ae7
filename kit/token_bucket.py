from util.redis_pool import GetRedis

EMAIL_BUCKET_KEY_PREFIX = 'email_report-bucket-'


class TokenBucket:
    def __init__(self, key: str, capacity: int, expire_time: int):
        self.capacity = capacity
        self.expire_time = expire_time
        self.bucket_key = key

    def consume(self) -> bool:
        redis_cli = GetRedis()
        # 尝试获取现有的令牌数量
        tokens = redis_cli.get(self.bucket_key)
        if tokens is None:
            # 如果key不存在，初始化令牌数量并设置过期时间
            redis_cli.set(self.bucket_key, self.capacity - 1, ex=self.expire_time)
            return True
        else:
            tokens = int(tokens)
            if tokens > 0:
                # 如果令牌数量大于0，则消耗一个令牌
                redis_cli.decr(self.bucket_key)
                return True
            else:
                # 如果令牌数量不足，返回False
                return False
