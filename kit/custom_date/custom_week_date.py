import json
from datetime import datetime, timedelta

from data.mysqls.demand.module_switch import ModuleSwitch


def week_date_standardize(week_date):
    #       Sunday Monday Tuesday Wednesday Thursday Friday Saturday
    # input:  6       0      1      2         3        4       5
    # want:   0       1      2      3         4        5       6
    week_date = (week_date + 1) % 7
    return week_date


class CustomWeekDate:
    def __init__(self, module: str):
        self.module = module
        week_date_by_module = ModuleSwitch.query_date_by_module(module)
        if not week_date_by_module:
            raise Exception("week_date_by_module not found")
        self.week_date = json.loads(week_date_by_module)
        if self.week_date and 'start_week' in self.week_date:
            self.start_date = {
                "week": self.week_date["start_week"],
                "hour": self.week_date["start_hour"],
                "minute": self.week_date["start_minute"],
                "second": self.week_date["start_second"]
            }
            self.end_date = {
                "week": self.week_date["week"],
                "hour": self.week_date["hour"],
                "minute": self.week_date["minute"],
                "second": self.week_date["second"]
            }


    def is_before_now(self):
        now = datetime.now()

        if self.week_date['week'] > week_date_standardize(now.weekday()):
            return True
        if self.week_date['week'] == week_date_standardize(now.weekday()):
            if self.week_date['hour'] > now.hour:
                return True
            if self.week_date['hour'] == now.hour:
                if self.week_date['minute'] > now.minute:
                    return True
                if self.week_date['minute'] == now.minute:
                    if self.week_date['second'] > now.second:
                        return True
        return False

    def is_after_now(self):
        now = datetime.now()
        if self.week_date['week'] < week_date_standardize(now.weekday()):
            return True
        if self.week_date['week'] == week_date_standardize(now.weekday()):
            if self.week_date['hour'] < now.hour:
                return True
            if self.week_date['hour'] == now.hour:
                if self.week_date['minute'] < now.minute:
                    return True
                if self.week_date['minute'] == now.minute:
                    if self.week_date['second'] < now.second:
                        return True
        return False
    
    def is_before_ddl(self, default_current_date: datetime = None):
        """
        是否在配置的截止时间之前
        """
        if default_current_date is None:
            current_time = datetime.now()
        else:
            current_time = default_current_date
        current_week, current_hour = week_date_standardize(current_time.weekday()), current_time.hour
        current_minute, current_second = current_time.minute, current_time.second
        ddl_week, ddl_hour = self.week_date['week'], self.week_date['hour']
        ddl_minute, ddl_second = self.week_date['minute'], self.week_date['second']
        if current_week < ddl_week:
            return True
        if current_week == ddl_week:
            if current_hour < ddl_hour:
                return True
            if current_hour == ddl_hour:
                if current_minute < ddl_minute:
                    return True
                if current_minute == ddl_minute:
                    if current_second < ddl_second:
                        return True
        return False

    # 将{'hour': 14, 'minute': 59, 'second': 0, 'week': 6}转成当前周年月日时分秒
    def get_current_week_date(self):
        now = datetime.now()
        current_weekday = now.weekday()
        # 将给定的星期几转换成代码通用格式
        target_weekday = self.week_date['week'] - 1  # 将星期一到星期日从1到7调整为0到6

        # 计算日期差
        days_difference = target_weekday - current_weekday
        if days_difference < 0:
            days_difference += 7

        target_date = now + timedelta(days=days_difference)

        return datetime(target_date.year, target_date.month, target_date.day, self.week_date['hour'], self.week_date['minute'], self.week_date['second'])

    def _compare_time(self, current_time, target_date):
        """比较当前时间与目标时间（返回-1/0/1）"""
        current_week = week_date_standardize(current_time.weekday())
        target_week = target_date["week"]

        if current_week < target_week:
            return -1
        elif current_week > target_week:
            return 1
        else:  # 周相同，比较时分秒
            if current_time.hour < target_date["hour"]:
                return -1
            elif current_time.hour > target_date["hour"]:
                return 1
            else:
                if current_time.minute < target_date["minute"]:
                    return -1
                elif current_time.minute > target_date["minute"]:
                    return 1
                else:
                    if current_time.second < target_date["second"]:
                        return -1
                    elif current_time.second > target_date["second"]:
                        return 1
                    else:
                        return 0

    def is_started(self):
        """当前时间是否在开始时间之前"""
        return self._compare_time(datetime.now(), self.start_date) == 1

    def is_not_started(self):
        return not self.is_started()

    def is_end(self):
        """当前时间是否在截止时间之后"""
        return self._compare_time(datetime.now(), self.end_date) == 1

    def is_on(self):
        """当前时间是否在活动期间"""
        return self.is_started() and not self.is_end()
