import pandas as pd
from validator import validate
from util.const import ErrorExcept, ErrCode


class FileValidator:
    def __init__(self, df: pd.DataFrame,
                 rules: dict[str: list] = None,
                 header_rules: dict[str: list] = None,
                 group_rules: dict[str: list] = None):
        self.df = df
        self.rules = rules  # 元素
        self.header_rules = header_rules  # 表头规则
        self.group_rules = group_rules  # 聚合规则，适用于需要聚合的校验

    def standardize(self) -> list[dict]:
        ret = []
        for index, row in self.df.iterrows():
            row_data = row.to_dict()
            # 给每一行加上行号
            row_data['row_no'] = index + 2
            ret.append(row_data)
        return ret

    def group_standardize(self, group_by_row, group_func) -> list[dict]:
        ret = []
        # 将self.df按照group_by_row进行分组,sum_row进行求和,让行号作为索引
        group_df = self.df.groupby(group_by_row, as_index=False).agg(group_func)
        for index, row in group_df.iterrows():
            row_data = row.to_dict()
            ret.append(row_data)
        return ret

    def do_validate(self):
        results, validated_datas, errors = True, [], []

        if self.header_rules:
            header = self.header_rules.get("header")
            col_header = self.df.columns.tolist()
            if header != col_header:
                results = False
                errors = {"errors": {"header": [{"Invalid data fields in the table, please follow the template and do not change any field.": []}]}}
        if not results:
            # 数据处理
            return results, validated_datas, errors

        # 单元校验
        if self.rules:
            dicts = self.standardize()
            for d in dicts:
                result, validated_data, error = validate(d, self.rules, return_info=True)
                if not result:
                    error['error_row'] = d['row_no']
                    results = False
                    validated_datas.append(validated_data)
                    errors.append(error)
        if not results:
            # 数据处理
            return results, validated_datas, parse_error_message(errors)

        # 聚合校验
        if self.group_rules:
            # group_rules = [
            #   {"rule":
            #       {"Mix%": [AllowedRule([1], "The Mix% uploaded is incomplete or invalid on the following models:")],
            #       "Sum": [AllowedRule([3], "The Sum uploaded is incomplete or invalid on the following models:")]},
            #   "group_by": ["Region", "Sub-LOB"],
            #   "group_func": ["sum"]},
            #   {"rule":
            #       {"Mix%": [AllowedRule([1], "The Mix% uploaded is incomplete or invalid on the following models:")],
            #        "Sum": [AllowedRule([3],"The Sum uploaded is incomplete or invalid on the following models:")]},
            #   "group_by": ["LOB"],
            #   "group_func": ["mean"]}]
            for group_rule in self.group_rules:
                rules = group_rule.get("rule")
                group_by_rows = group_rule.get("group_by")
                group_func = group_rule.get("group_func")

                dicts = self.group_standardize(group_by_rows, group_func)
                for d in dicts:
                    result, validated_data, error = validate(d, rules, return_info=True)
                    if not result:
                        results = False
                        validated_datas.append(validated_data)
                        error_row = ''
                        for group_by_row in group_by_rows:
                            if group_by_row in d:
                                error_row = error_row + d[group_by_row] + " "
                        error['error_row'] = error_row
                        errors.append(error)

        if not results:
            # 数据处理
            return results, validated_datas, parse_error_message(errors)

        return True, None, {"errors": {}}

    @classmethod
    def handle_error_msg(cls, has_pass_validation: bool, errors: dict, priority_validation: list):
        # 先判断header是否正确
        if not has_pass_validation:
            error_info = errors.get("errors").get("header")
            if error_info:
                for error_msg, idx in error_info[0].items():
                    raise ErrorExcept(
                        ErrCode.FileUploadError, { "message": error_msg, "row_index": idx }
                    )
        # 需要按照列的优先级来返回对应的错误，越靠前越先返回
        if not has_pass_validation:
            # 处理errors返回格式
            error = {
                "message": "",
                "row_index": []
            }
            error_info = errors.get("errors")
            
            if not isinstance(error_info, dict):
                raise ErrorExcept(ErrCode.FileUploadError, error)
            
            for item in priority_validation:
                error_value = error_info.get(item)
                if isinstance(error_value, list) and len(error_value) > 0:
                    message_and_row_index = error_value[0]
                    if isinstance(message_and_row_index, dict):
                        message, row_index = next(iter(message_and_row_index.items()))
                        all_strings = all(isinstance(item, str) for item in row_index)
                        if all_strings:
                            error["message"] = message + ", ".join(row_index)
                        else:
                            error["message"], error["row_index"] = next(iter(message_and_row_index.items()))
                        break
            raise ErrorExcept(ErrCode.FileUploadError, error)


def parse_error_message(errors):
    # errors:
    # [{'Region': {'Required': 'Field was empty', 'AllowedRule': None},
    # 'LOB': {'AllowedRule': None},
    # 'Color': {'Required': 'Field was empty'},
    # 'error_row': 1},
    # {'Region': {'AllowedRule': None},
    # 'error_row': 2},
    # {'Region': {'AllowedRule': None},
    # 'error_row': 3}]

    ret_errors = {}
    for error in errors:
        for key, value in error.items():
            if key == 'error_row':
                continue
            if key not in ret_errors:
                ret_errors[key] = []
            for k, v in value.items():
                key_errors = {}
                error_message = v if v is not None else get_messages(key, k)
                ret = ret_errors[key]

                # 判断ret中所有的key是否包含error_message
                keys = [list(i.keys())[0] for i in ret]
                if error_message not in keys:
                    key_errors[error_message] = []
                    ret_errors[key].append(key_errors)

                # 为ret_errors中的key_errors 添加error_row
                for i in ret_errors[key]:
                    if error_message in i.keys():
                        i[error_message].append(error['error_row'])
    return {"errors": ret_errors}


def get_messages(row_name, func_name):
    validate_messages = {
        "AllowedRule": f"The following data row(s) are invalid for wrong {row_name} values:",
        "RangeRule": f"The following data row(s) are invalid for wrong {row_name} values:",
        "Required": "The following data row(s) are invalid for missing required fields:",
        "PositiveIntegerRangeRule": f"The following data row(s) are invalid for wrong {row_name} values:",
    }
    if validate_messages.get(func_name):
        return validate_messages.get(func_name)


def raise_upload_error(message: str, rows: list):
    raise ErrorExcept(
        ErrCode.FileUploadError,
        {
            "message": message,
            "row_index": [idx + 2 for idx in rows]
        }
    )
