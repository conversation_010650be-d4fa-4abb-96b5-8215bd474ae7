from validator.rules import Rule


class AllowedRule(Rule):
    def __init__(self, arg, error_message=None):
        Rule.__init__(self)
        self.arg = arg
        self.error_message = error_message

    def check(self, value):
        return value in self.arg


class RangeRule(Rule):
    def __init__(self, begin_range, end_range, error_message=None):
        Rule.__init__(self)
        self.begin_range = begin_range
        self.end_range = end_range
        self.error_message = error_message

    def check(self, value):
        if not isinstance(value, (int, float)):
            return False
        return self.begin_range <= value <= self.end_range


class PositiveIntegerRangeRule(Rule):
    def __init__(self, begin_range, end_range, error_message=None):
        Rule.__init__(self)
        self.begin_range = begin_range
        self.end_range = end_range
        self.error_message = error_message

    def check(self, value):
        # 将value转换为int类型，如果转换失败则返回False
        try:
            value = int(value)
        except ValueError:
            return False
        if self.begin_range is not None and self.end_range is not None:
            return self.begin_range <= value <= self.end_range
        if self.begin_range is not None:
            return self.begin_range <= value
        if self.end_range is not None:
            return value <= self.end_range
