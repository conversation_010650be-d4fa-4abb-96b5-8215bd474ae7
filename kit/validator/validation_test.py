from kit.validator.customer_rule import AllowedRule, RangeRule
from kit.validator.file import FileValidator
import pandas as pd


def test_validator():
    data = {'Region': ['HK', 'HK', ''],
            'LOB': ['iPad', 'iPhone', 'iPhone'],
            'Sub-LOB': ['iPhone 14', 'iPhone 14', 'iPhone 14 Pro'],
            'Color': ['', 'Black', 'Pink'],
            'Mix%': [1, 2, 1],
            'Sum': [3, 2, 1]}
    data_pd = pd.DataFrame(data)

    rules = {"Region": ["required", AllowedRule(['China Mainland'])],
             "LOB": ["required", AllowedRule(['iPhone'])],
             "Sub-LOB": ["required"],
             "Color": ["required"],
             "Mix%": ["required", RangeRule(0, 1)],}
    val = FileValidator(data_pd, rules=rules)
    result, row, errors = val.do_validate()
    assert errors == {'errors': {
        'Region': [{'The following data row(s) are invalid for wrong Region values:': [1, 2, 3]},
                   {'Field was empty': [3]}],
        'LOB': [{'The following data row(s) are invalid for wrong LOB values:': [1]}],
        'Color': [{'Field was empty': [1]}],
        'Mix%': [{'The following data row(s) are invalid for wrong Mix% values:': [2]}]}}

    group_rules = [{"rule": {"Mix%": [AllowedRule([1], "The Mix% uploaded is incomplete or invalid on the following models:")],
                             "Sum": [AllowedRule([3], "The Sum uploaded is incomplete or invalid on the following models:")]},
                    "group_by": ["Region", "Sub-LOB"],
                    "group_func": {"Mix%": "sum", "Sum": "sum"}},
                   {"rule": {"Mix%": [AllowedRule([1], "The Mix% uploaded is incomplete or invalid on the following models:")],
                             "Sum": [AllowedRule([3],"The Sum uploaded is incomplete or invalid on the following models:")]},
                    "group_by": ["LOB"],
                    "group_func": {"Mix%": "sum", "Sum": "sum"}}]
    val = FileValidator(data_pd, group_rules=group_rules)
    result, row, errors = val.do_validate()
    assert errors == {"errors": {'Sum': [
        {'The Sum uploaded is incomplete or invalid on the following models:': [' iPhone 14 Pro ', 'HK iPhone 14 ']}],
                                 'Mix%': [{'The Mix% uploaded is incomplete or invalid on the following models:': [
                                     'HK iPhone 14 ', 'iPhone ']}]}}

    header_rules = {"header": ["Region", "LOB", "Sub-LOB", "Color", "Mix%", "Sum", "New"]}
    val = FileValidator(data_pd, header_rules=header_rules)
    result, row, errors = val.do_validate()
    assert errors == {
        'errors': 'Invalid data fields in the table, please follow the template and do not change any field.'}


