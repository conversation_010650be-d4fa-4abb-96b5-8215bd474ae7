import math

import numpy as np
import pandas


def set_target_if_less(new_df:pandas.DataFrame, column_name:str, target:int) -> pandas.DataFrame:
    new_df.loc[new_df[column_name] < target, column_name] = target
    return new_df


def set_target_if_less_by_column(new_df: pandas.DataFrame, column_name: str, target_column: str) -> pandas.DataFrame:
    new_df.loc[new_df[column_name] < new_df[target_column], column_name] = new_df[target_column]
    return new_df


def set_target_by_none_column(new_df: pandas.DataFrame, column_name: str, target_column: str) -> pandas.DataFrame:
    # 将df中为None或者NaN的列替换成对应target_column列的值
    new_df[column_name] = new_df.apply(lambda x: x[target_column] if x[column_name] is None or pandas.np.isnan(x[column_name]) else x[column_name], axis=1)
    return new_df


def fill_nan_to_none(new_df: pandas.DataFrame) -> pandas.DataFrame:
    new_df.replace({np.nan: None}, inplace=True)
    return new_df


def replace_none_from_nan(by_soldto_to_dict, columns):
    for demand in by_soldto_to_dict:
        for column in columns:
            if demand[column] is None:
                continue
            if math.isnan(demand[column]):
                demand[column] = None
