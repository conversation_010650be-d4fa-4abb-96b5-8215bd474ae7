from util.util import traditional_round


def format_number_with_thousands_separator(number):
    # 输出千分位分隔的结果
    # 如果number的绝对值小于1，则需要保留一位小数
    ndigits = 0 if abs(number) >= 1 or number == 0 else 1  
    return "{:,.{}f}".format(number, ndigits)

def round_with_dividing_by_thousand(number, ndigits=0):
    # 特殊处理，如果0< number绝对值 < 950, 则保留1位小数
    if 0 < abs(number) < 950: 
        ndigits = 1
    # 除以 1000 并四舍五入
    # result = round(number / 1000, ndigits)
    '''
    round() 函数使用的是 “银行家舍入”（Banker's Rounding）规则，也称为 “四舍六入五成双” 规则。
    这意味着当需要舍入的数字是 5 时，会将结果舍入到最接近的偶数。所以 round(0.5) 结果为 0，round(1.5) 结果为 2
    '''
    result = traditional_round(number / 1000, ndigits)
    if result == -0:
        return 0
    return result
