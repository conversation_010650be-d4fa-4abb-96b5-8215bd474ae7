import os
import zipfile

import pandas as pd


def save_as_csv(df: pd.DataFrame, dir_path: str, file_name: str):
    base_dir = os.path.dirname(os.path.abspath(__file__))
    path = os.path.dirname(base_dir) + dir_path
    dir_name = os.path.dirname(path)
    if not os.path.exists(dir_name):
        os.makedirs(dir_name)
    if not os.path.exists(path):
        os.mkdir(path)
    csv_path = path + file_name
    # if os.path.isfile(path):
    #     return path

    df.to_csv(csv_path, header=True, index=False, encoding='utf_8_sig', line_terminator="\n")
    return csv_path, path


def save_as_zip(csv_path: str, csv_name: str, path: str, zip_name: str):
    zip_path = path + zip_name
    with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
        zipf.write(csv_path, arcname=csv_name)
    return zip_path


def save_as_excel(df: pd.DataFrame, dir_path: str, file_name: str):
    base_dir = os.path.dirname(os.path.abspath(__file__))
    path = os.path.dirname(base_dir) + dir_path
    dir_name = os.path.dirname(path)
    if not os.path.exists(dir_name):
        os.makedirs(dir_name)
    if not os.path.exists(path):
        os.mkdir(path)
    excel_path = path + file_name
    # if os.path.isfile(path):
    #     return path

    df.to_excel(excel_path, index=False)
    return excel_path, path

