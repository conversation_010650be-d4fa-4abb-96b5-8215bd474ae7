from kit.container.global_var_container import run_once


def test_run_once():
    class MyClass:
        @run_once
        def my_method(self, arg):
            print(f"Method called with argument: {arg}")
            return arg * 2
        
    # 创建类的实例
    obj = MyClass()

    # 第一次调用方法
    result1 = obj.my_method(5)
    assert result1 == 10
    print(f"Result 1: {result1}")

    # 第二次调用方法（不会实际执行，而是返回第一次的结果）
    result2 = obj.my_method(100)  # 注意：这里传递的参数被忽略了
    assert result2 == 10
    print(f"Result 2: {result2}")

    # 第三次调用方法（不会实际执行，而是返回第一次的结果）
    result3 = obj.my_method(200)  # 注意：这里传递的参数被忽略了
    assert result3 == 10
    print(f"Result 3: {result3}")

    # 创建类的实例
    obj1 = MyClass()

    # 第一次调用方法
    result1 = obj1.my_method(1000)
    assert result1 == 2000
    print(f"Result 1: {result1}")

    # 第二次调用方法（不会实际执行，而是返回第一次的结果）
    result2 = obj1.my_method(10)  # 注意：这里传递的参数被忽略了
    assert result2 == 2000
    print(f"Result 2: {result2}")
