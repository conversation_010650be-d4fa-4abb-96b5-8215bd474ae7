import time
import threading
from util.conf import logger


def run_once(func):
    def wrapper(self, *args, **kwargs):
        once_key = func.__name__ + "_ONCE"
        if not hasattr(self, once_key):
            setattr(self, once_key, func(self, *args, **kwargs))
        else:
            print("函数已执行")
        return getattr(self, once_key)
    return wrapper


class GlobalVarContainer:
    def __init__(self, loader, refresh_interval):
        self.loader = loader
        self.refresh_interval = refresh_interval

        self._cache = Cache()
        self.__lock = threading.Lock()

    def run(self):
        if self.loader is None:
            raise Exception("No loader function defined")

        # 立即执行一次,保证服务启动起来的时候一定是有数据的
        self._refresh()

        self._async_loop_refresh()

    def load(self):
        return self._cache.get()

    def _refresh(self):
        try:
            v = self.loader()
            self._cache.set(v)
        except Exception as e:
            logger.error(e)


    @run_once
    def _async_loop_refresh(self):
        # 循环更新数据
        if self.refresh_interval == 0:
            return

        def _loop_refresh():
            while True:
                time.sleep(self.refresh_interval)
                self._refresh()
                print(f'refreshed.')

        timer = threading.Thread(target=_loop_refresh)
        timer.start()

class Cache:
    def __init__(self):
        self.__cache = None
        self.__lock = threading.Lock()

    def set(self, value):
        with self.__lock:
            self.__cache = value

    def get(self):
        with self.__lock:
            return self.__cache


# 定时数据刷新只执行一次
def singleton(cls):
    instances = {}

    def get_instance(*args, **kwargs):
        if cls not in instances:
            instances[cls] = cls(*args, **kwargs)
        return instances[cls]

    return get_instance



