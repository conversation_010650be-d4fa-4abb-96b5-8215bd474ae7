from kit.custom_sort import CustomSort


def test_sort_sublobs():
    sublobs = ["iPhone 14", "iPhone 15", "iPhone 14 Series", "iPhone 15 Pro Tier", "iPhone 14 Plus",
               "iPhone 15 Consumer Tier", "All"]
    expected = [
        "All",
        "iPhone 15 Pro Tier",
        "iPhone 15 Consumer Tier",
        "iPhone 15",
        "iPhone 14 Series",
        "iPhone 14 Plus",
        "iPhone 14"
    ]
    sorted_sublobs = CustomSort.sort_iphone_sublobs(sublobs)
    print(sorted_sublobs)
    assert sorted_sublobs == expected
