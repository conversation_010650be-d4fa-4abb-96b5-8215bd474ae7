import concurrent
from concurrent.futures import Thread<PERSON>oolExecutor

from util.conf import logger


class AbstractHandler:
    def __init__(self, name):
        self.name = name

    def handle(self):
        pass


class ParallelExecutor:

    def __init__(self, source: list[AbstractHandler], max_workers: int = 10):
        self.source = source
        self.future = {}
        self.max_workers = max_workers

    def execute(self):
        max_workers = min(self.max_workers, len(self.source))
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            for handler in self.source:
                tmp_future = executor.submit(handler.handle)
                self.future[tmp_future] = handler.name

            res = {}
            for future in concurrent.futures.as_completed(self.future):
                handler_name = self.future[future]
                try:
                    result = future.result()
                    res[handler_name] = result
                except Exception as exc:
                    logger.error(f'execute {handler_name} failed, error: {str(exc)}')
                    raise exc
        return res
