# gunicorn.py
from logging.handlers import WatchedFileHandler
import os
import multiprocessing
bind = '0.0.0.0:8081'      #绑定ip和端口号
timeout = 300     #超时

workers = 4  #进程数
#loglevel = 'info' #日志级别，这个日志级别指的是错误日志的级别，而访问日志的级别无法设置
#access_log_format = '%(t)s %(p)s %(h)s "%(r)s" %(s)s %(L)s %(b)s %(f)s" "%(a)s"'    #设置gunicorn访问日志格式，错误日志无法设置

limit_request_line=8190
limit_request_field_size=65520

"""
其每个选项的含义如下:
h          remote address
l          '-'
u          currently '-', may be user name in future releases
t          date of the request
r          status line (e.g. ``GET / HTTP/1.1``)
s          status
b          response length or '-'
f          referer
a          user agent
T          request time in seconds
D          request time in microseconds
L          request time in decimal seconds
p          process ID
"""
#accesslog = "/home/<USER>/server/log/gunicorn_access.log"      #访问日志文件
#errorlog = "/home/<USER>/server/log/gunicorn_error.log"        #错误日志文件