########## must at head ############
import sys
import os

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(SCRIPT_DIR))
##########      end     ############

from datetime import datetime
from data.allocation_prepare_data import TblAllocationPrepare
from data.allocation_cpf_lob_data import TblAllocationCpfLob
from data.fiscal_year_week import FiscalYearWeek
from data.allocation_prepare_gc_dmp_data import AppFastTaskStatusWi
from util.send_email import async_send_email
from util.const import *
from util.conf import task_logger

# 说明
# 时间限制周一17:30
# 周一17:00～17:29之间每隔3分钟执行一次，如果本周数据没有生成且数据侧ready，则生成一条数据
# 周一17:30分执行一次，如果本周数据没有生成，生成一条数据

# usage
# 0-30/3 17 * * 1 ENV=dev python3 task/task_allocation_prepare_record.py
# 0-30/3 17 * * 1 ENV=prod python3 task/task_allocation_prepare_record.py


def generate_allocation_prepare(fiscal_week: int,
                                fiscal_qtr_week_name: str,
                                rtm: str, lob: str = "iPad",
                                operate_phase: str = str(RTMAllocationPhase.SalesInput)):
    return {
        "fiscal_qtr_week_name": fiscal_qtr_week_name,
        "fiscal_week_year": fiscal_week,
        "rtm": rtm,
        "lob": lob,
        "operate_phase": operate_phase,
        "upload_status": str(RTMSalesInputUploadStatus.WaitingToUpload)
    }


def get_week_info_by_date(date: str) -> dict:
    if date is None or date.strip() == "":
        return {}
    return FiscalYearWeek.get_week_by_date(date)


def insert_rtms_record(fiscal_week_year: int,
                       fiscal_qtr_week_name: str,
                       rtm_list: list):
    mail_content = ''
    new_data = []
    for rtm in rtm_list:
        record = generate_allocation_prepare(fiscal_week_year,
                                             fiscal_qtr_week_name,
                                             rtm)
        new_data.append(record)
        mail_content += f"rtm: {rtm}, has input 1 record.{DOUBLE_LINE_FEED}"
    TblAllocationPrepare.bulk_save(new_data)
    return mail_content


def check_data_status(fiscal_week_year: int) -> bool:
    return AppFastTaskStatusWi.get_whether_ready(fiscal_week_year)


def check_current_week_record(fiscal_week_year: int) -> bool:
    current_count = TblAllocationPrepare.count_current_week_record(
        fiscal_week_year)
    return True if current_count > 0 else False


def check_within_deadline() -> bool:
    now = datetime.now()
    # 截止时间为17:30:00
    target = now.replace(hour=17, minute=30, second=0, microsecond=0)
    return True if now < target else False


def insert_cpf_record(fiscal_week_year: int, fiscal_qtr_week_name: str,
                      lob: str = 'iPad', phase: int = 1):
    cpf_lob_list = []
    cpf_lob = TblAllocationCpfLob(fiscal_qtr_week_name, lob,
                                  phase, fiscal_week_year)
    cpf_lob_list.append(cpf_lob)
    TblAllocationCpfLob.bulk_save(cpf_lob_list)
    return f"CP&F: has input 1 record.{DOUBLE_LINE_FEED}"


def generate_allocation_record(fiscal_dt):
    '''
    如果在截止日期之前，数据侧没有ready，不生成新的数据；
    如果在截止日期之前，数据侧ready，但是已经生成了数据，不再生成数据；
    如果在截止日期之后，但是已经生成了数据，不再生成数据；
    其他情况，生成各个RTM对应的数据
    '''
    if not fiscal_dt:
        return ''
    week_info = get_week_info_by_date(fiscal_dt)
    content = f"{week_info.get('fiscal_qtr_week_name')}:{DOUBLE_LINE_FEED}"
    within_deadline = check_within_deadline()
    data_status = check_data_status(week_info.get(StrFiscalWeekYear))
    current_week_record = check_current_week_record(
        week_info.get(StrFiscalWeekYear))

    task_logger.info(f"within_deadline is {within_deadline} and \
        data_status is {data_status} and \
        current_week_record is {current_week_record}.")

    if within_deadline and not data_status:
        return ''
    if within_deadline and current_week_record:
        return ''
    if not within_deadline and current_week_record:
        return ''
    rtm_list = AllocationRTM.get_all_rtms()
    rtm_content = insert_rtms_record(week_info.get(StrFiscalWeekYear),
                                        week_info.get(StrFiscalQtrWeekName),
                                        rtm_list)
    content += rtm_content
    cpf_content = insert_cpf_record(week_info.get(StrFiscalWeekYear),
                                    week_info.get(StrFiscalQtrWeekName))
    content += cpf_content
    return content


if __name__ == "__main__":
    # 获取时间
    if len(sys.argv) >= 2:
        exec_date = sys.argv[1]
    else:
        exec_date = datetime.now().strftime(DateFormat)

    subject = f"{os.environ.get('ENV')} Allocation Prepare & Summition Automatically Generate Record."
    try:
        content = generate_allocation_record(exec_date)
        if not content:
            exit(1)
        async_send_email(subject, content)
        task_logger.info(f"{subject}{DOUBLE_LINE_FEED}{content}")
    except ErrorExcept as e:
        async_send_email(subject, "生成数据失败")
        task_logger.error(f"{subject}{DOUBLE_LINE_FEED}{e}")
