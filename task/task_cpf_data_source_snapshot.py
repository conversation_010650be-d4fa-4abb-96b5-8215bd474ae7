########## must at head ############
import sys
import os

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(SCRIPT_DIR))
##########      end     ############

# usage:
# ENV=dev python3 task/task_cpf_data_source_snapshot.py


from data.cpf_data_source import *
from data.fiscal_year_week import FiscalYearWeek
from util.const import *


def generate_active_sku_snapshot(fiscal_dt):
    if not fiscal_dt:
        return ""
    week_info = FiscalYearWeek.get_week_by_date(fiscal_dt)
    
    # 获取数据
    snapshot_data = OdsFastCPFActiveSKUiPad.query_snapshot_data(week_info.get(StrFiscalWeekYear),week_info.get(StrFiscalQtrWeekName))
    # 插入数据
    OdsFastCPFActiveSKUiPadSnapshot.delete_by_week(week_info.get(StrFiscalWeekYear))
    OdsFastCPFActiveSKUiPadSnapshot.bulk_save(snapshot_data)
    
    return f"you generate {week_info.get(StrFiscalQtrWeekName)} snapshot."


if __name__ == "__main__":
    if len(sys.argv) >= 2:
        fiscal_dt = sys.argv[1]
    if not fiscal_dt:
        fiscal_dt = datetime.now().strftime(DateFormat)

    generate_active_sku_snapshot(fiscal_dt)
    