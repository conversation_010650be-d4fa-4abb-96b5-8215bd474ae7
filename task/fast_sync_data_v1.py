########## must at head ############
import sys
import os

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(SCRIPT_DIR))
##########      end     ############

from sqlalchemy import exc
from util.gc_dmp_base import *
from util.conf import task_logger
from util.const import *
from util.send_email import send_email
from util.fast_lite_base import *
from data.fast_lite_forecast_data import LiteNewAppFastForecastTrmSoWa, NewFiscalWeekYear, NewAppFastForecastTrmSoWa, NewDimFastBusinessSoldtoMapping
from data.fast_lite_demand_data import FastLiteAppFastDemandSummaryWa
from data.fiscal_year_week import FiscalYearWeek
from data.forecast_data import AppFastForecastTrmSoWa
from data.cascade_filter_data import DimFastBusinessSoldtoMapping
from data.demand_data import AppFastDemandOnlineSummaryWa, AppFastDemandCarrierSummaryWa, AppFastDemandMonoSummaryWa,AppFastDemandMultiSummaryWa, gen_ent_edu_sold_to_data


### 说明
# 初始化
# ENV=dev py3 task/fast_sync_data_v1.py init
# ENV=prod py3 task/fast_sync_data_v1.py init
# 
# 定时任务 每周一20:00
# 0 20 * * 1 ENV=dev py3 task/fast_sync_data_v1.py
# 0 20 * * 1 ENV=prod py3 task/fast_sync_data_v1.py
# 
# 单独同步某周(指定所在周)某张表
# ENV=dev py3 task/fast_sync_data_v1.py weekly 'FY23Q2W1' All
# ENV=dev py3 task/fast_sync_data_v1.py weekly 'FY23Q2W1' demand
# ENV=prod py3 task/fast_sync_data_v1.py weekly 'FY23Q2W1' All
# ENV=prod py3 task/fast_sync_data_v1.py weekly 'FY23Q2W1' demand

def init_tables():
    try:
        # 初始化表
        NewFiscalWeekYear.__table__.create(fast_lite_engine)
        NewDimFastBusinessSoldtoMapping.__table__.create(fast_lite_engine)
        NewAppFastForecastTrmSoWa.__table__.create(fast_lite_engine)
        LiteNewAppFastForecastTrmSoWa.__table__.create(fast_lite_engine)
        FastLiteAppFastDemandSummaryWa.__table__.create(fast_lite_engine)
    except exc.OperationalError:
        pass


# 同步财年周表,只取了一周中第一天的数据, 全表同步
def sync_fiscal_week_year():
    # 查询来源表数据
    data_list = FiscalYearWeek.get_week_data()
    # 整合数据
    results = [dict(item) for item in data_list]
    # 向新表插入数据
    NewFiscalWeekYear.bulk_save(results)
    return f"All table: insert new data [ {len(results)} ] to [ {NewFiscalWeekYear.__tablename__} ].{DOUBLE_LINE_FEED}"


# 同步business_type_sold_to映射表, 全表同步
def sync_sold_to_data():
    # 查询来源表数据
    data_list = DimFastBusinessSoldtoMapping.get_all_data()
    # 整理数据
    results = [dict(item) for item in data_list]
    # 向新表插入数据
    NewDimFastBusinessSoldtoMapping.bulk_save(results)
    return f"All table: insert new data [ {len(results)} ] to [ {NewDimFastBusinessSoldtoMapping.__tablename__} ].{DOUBLE_LINE_FEED}"


# 同步数据侧的forecast表, 按周维度来同步
def sync_cpf_forecast_data(fiscal_qtr_week_name: str):
    forecast_source_content = ''
    if fiscal_qtr_week_name is not None:
        # 查询来源表数据
        data_list = AppFastForecastTrmSoWa.get_data_by_week(fiscal_qtr_week_name)
        # 整合数据
        results = [dict(item) for item in data_list]
        # 向新表插入数据
        NewAppFastForecastTrmSoWa.bulk_save(results)
        forecast_source_content = f"{fiscal_qtr_week_name}: insert new data [ {len(results)} ] to [ {NewAppFastForecastTrmSoWa.__tablename__} ].{DOUBLE_LINE_FEED}"
    return forecast_source_content


# 将同步过来的forecast相关数据, 整合成一张新表的数据, 按周维度
def sync_final_table_by_week(fiscal_qtr_week_name: str):
    forecast_content = ''
    if fiscal_qtr_week_name is not None:
        # 向新表插入数据
        forecast_content = LiteNewAppFastForecastTrmSoWa.gen_data_by_week(fiscal_qtr_week_name)
    return forecast_content


# 同步数据侧4个demand表, 并生成ent/edu sold-to/sku 维度的数据, 按周维度
def gen_demand_data(fiscal_qtr_week_name: str):
    demand_content = ''
    if fiscal_qtr_week_name is not None:
        online_content = AppFastDemandOnlineSummaryWa.gen_data_by_week(fiscal_qtr_week_name)
        carrier_content = AppFastDemandCarrierSummaryWa.gen_data_by_week(fiscal_qtr_week_name)
        mono_content = AppFastDemandMonoSummaryWa.gen_data_by_week(fiscal_qtr_week_name)
        multi_content = AppFastDemandMultiSummaryWa.gen_data_by_week(fiscal_qtr_week_name)
        other_rtm_content = gen_ent_edu_sold_to_data(fiscal_qtr_week_name)
        demand_content = online_content+carrier_content+mono_content+multi_content+other_rtm_content
    return demand_content


def sync_cpf_forecast_rtm_data(sync_week: str, init: bool, table_type: str = 'All' or 'forecast' or 'demand'):
    if sync_week is None or sync_week == '':
        ret_datetime = datetime.now()
        kabob_date = ret_datetime.strftime('%Y-%m-%d')
        fiscal_qtr_week_name = FiscalYearWeek.get_fiscal_qtr_week_name_by_date(kabob_date)
    else:
        fiscal_qtr_week_name = sync_week
    
    content1 = '' 
    content2 = '' 
    content3 = '' 
    content4 = '' 
    content5 = '' 
    if init:
        # 新建所需表
        init_tables()
        # 只需要同步一次
        content1 = sync_fiscal_week_year()
        content2 = sync_sold_to_data()
    # 需要每周同步一次
    if table_type == 'All' or table_type == 'forecast':
        content3 = sync_cpf_forecast_data(fiscal_qtr_week_name)
        content4 = sync_final_table_by_week(fiscal_qtr_week_name)
    if table_type == 'All' or table_type == 'demand':
        content5 = gen_demand_data(fiscal_qtr_week_name)
    return content1+content2+content3+content4+content5


if __name__ == "__main__":
    task_logger.info('starting...')
    env_info = f"{StrENV}: {os.getenv(StrENV)}."
    mail_content = ''
    task_name_list = ['init']
    init_flag = False
    exec_week = ''
    table_type = 'All'

    if len(sys.argv) >= 2:
        if sys.argv[1] in task_name_list:
            init_flag = True
    if len(sys.argv) >= 3:
        exec_week = sys.argv[2]
    if len(sys.argv) >= 4:
        table_type = sys.argv[3]
    mail_content = sync_cpf_forecast_rtm_data(exec_week, init_flag, table_type)
    task_logger.info(mail_content)
    # 所有的结果发送邮件
    send_email(f"{env_info} Forecast Lite CP&F Data Sync Report.", mail_content)
    task_logger.info('done.')
