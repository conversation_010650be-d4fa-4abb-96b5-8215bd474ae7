########## must at head ############
import sys
import os

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(SCRIPT_DIR))
##########      end     ############

from datetime import datetime
from util.const import DateFormat, RTMAllocationPhase, PrepareSubmissionFileCategory, StrFiscalQtrWeekName,StrFiscalWeekYear, AllocationRTM, RTMDemandSubmissionStatus
from data.allocation_prepare_data import TblAllocationPrepare
from util.conf import *
from data.cpf_sell_in_demand_data import TblAllocationPrepareFile
from data.fiscal_year_week import FiscalYearWeek
from util.template_email_sender import TemplateEmail
from util.cpf_util import get_mail_navigate_url

'''
第二步, sell-in demand
task_type:
1: 周二18:30, Multi/Online 未提交Demand数据, 通知Multi/Online
2: 周二19:00, Multi/Online 未提交Demand数据, 通知Multi/CP&F,Online/CP&F
'''

def check_rtm_upload_status(rtm, fiscal_week_year):
    '''
    判断rtm已经上传了demand文件, 第二步只有rtm会上传, CP&F只下载
    '''
    ret = TblAllocationPrepareFile.get_allocation_prepare_file(fiscal_week_year, str(RTMAllocationPhase.DemandSubmission), 'iPad', PrepareSubmissionFileCategory.GenerateAndUpload, rtm)
    return True if ret else False


def timed_reminder_email(task_type, rtm, fiscal_dt):
    week_info = FiscalYearWeek.get_week_by_date(fiscal_dt)
    record = TblAllocationPrepare.find_by_fiscal_week_year_and_rtm(week_info[StrFiscalWeekYear], rtm)
    navigate_url = get_mail_navigate_url(
        record.id, record.rtm, record.fiscal_qtr_week_name, record.fiscal_week_year, RTMAllocationPhase.DemandSubmission)
    upload_status = check_rtm_upload_status(rtm, week_info[StrFiscalWeekYear])
    if not upload_status:
        if task_type == str(1):
            TemplateEmail().sell_in_demand_6(rtm, week_info[StrFiscalQtrWeekName], navigate_url)
        elif task_type == str(2):
            # 更改状态为Error
            TblAllocationPrepare.update_data_by_id(record.id, {'second_phase_status': RTMDemandSubmissionStatus.Error})
            # 邮件通知RTM
            TemplateEmail().sell_in_demand_8(week_info[StrFiscalQtrWeekName], rtm)
            # 邮件通知CP&F
            TemplateEmail().sell_in_demand_10(week_info[StrFiscalQtrWeekName], rtm)
    return upload_status


if __name__ == "__main__":
    task_type = ''
    if len(sys.argv) >= 2:
        task_type = sys.argv[1]
    if not task_type:
        task_logger.info('please specify task type.')
        exit(1)
    fiscal_dt = ''
    if len(sys.argv) >= 3:
        fiscal_dt = sys.argv[2]
    if not fiscal_dt:
        fiscal_dt =  datetime.now().strftime(DateFormat)
    for rtm in [AllocationRTM.Multi, AllocationRTM.Online]:
        timed_reminder_email(task_type, rtm, fiscal_dt)
