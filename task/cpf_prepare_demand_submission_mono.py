# ========== must at head ==========
import sys
import os

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(SCRIPT_DIR))
# ========== end ==========

from util.const import *
from util.file_util import save_df_to_md5_name
from data.fiscal_year_week import FiscalYearWeek
from data.allocation_prepare_gc_dmp_data import *
from data.allocation_prepare_data import TblAllocationPrepareFile


def get_data_by_lob(fiscal_obj: FiscalYearWeek, lob):
    fiscal_week_year = fiscal_obj.fiscal_week_year
    submission_data = AppFastAllocationDemandSubmissionMono.get_by_rtm_lob("Mono", fiscal_week_year, lob)
    
    new_discrete_fields = {
        "Discrete CW+1": "discrete_cw1",
        "Discrete CW+2": "discrete_cw2",
        "Discrete CW+3": "discrete_cw3",
        "Discrete CW+4": "discrete_cw4"
    }
    sub_df = submission_data[list(PrepareSubmissionMonoHeaderDict.values())+list(new_discrete_fields.values())]
    sub_df.columns = list(PrepareSubmissionMonoHeaderDict.keys())+list(new_discrete_fields.keys())
    # 保存 Excel 文件
    file_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__))) + f"/uploads/allocation/"
    local_file_name = save_df_to_md5_name(sub_df, file_path, 'xlsx')
    file_name = f"Mono_Demand_{lob}_{fiscal_obj.fiscal_qtr_week_name}.xlsx"
    file_records = TblAllocationPrepareFile.find_by_phase_rtm_category(
        str(RTMAllocationPhase.DemandSubmission), AllocationRTM.Mono, PrepareSubmissionFileCategory.GenerateAndUpload, fiscal_obj.fiscal_week_year
    )
    if len(file_records) == 0:
        file_record = TblAllocationPrepareFile()
        file_record.fiscal_qtr_week_name = fiscal_obj.fiscal_qtr_week_name
        file_record.fiscal_week_year = fiscal_obj.fiscal_week_year
        file_record.rtm = AllocationRTM.Mono
        file_record.lob = lob
        file_record.operate_phase = str(RTMAllocationPhase.DemandSubmission)
        file_record.upload_status = RTMSalesInputUploadStatus.Received
        file_record.upload_file_version = 0
    else:
        file_record = file_records[0]
        file_record.upload_file_version += 1
    file_record.upload_file_name = file_name
    file_record.upload_file_path = f"/file/storage/{local_file_name}"
    file_record.upload_by = "system"
    file_record.upload_at = datetime.now()
    file_record.update_by = "system"
    file_record.category = PrepareSubmissionFileCategory.GenerateAndUpload
    file_record.save()


def main(fiscal_date: str, lob_list: list):
    fiscal_obj = FiscalYearWeek.get_fis_by_date(fiscal_date)
    if fiscal_obj is None:
        raise ErrorExcept(ErrCode.Param, "can't find fiscal day")
    for lob in lob_list:
        get_data_by_lob(fiscal_obj, lob)


if __name__ == '__main__':
    task_logger.info('starting...')
    logger = logging.getLogger("myapp.sqltime")
    logger.setLevel(logging.INFO)
    env_info = f"{StrENV}: {os.getenv(StrENV)}."
    exec_week = ''
    fiscal_dt = ''
    execute_lobs = SubmissionExecuteLobs

    if len(sys.argv) >= 2:
        fiscal_dt = sys.argv[1]
    else:
        task_logger.error('Please input fiscal_date parameter!')
        sys.exit()
    try:
        datetime.strptime(fiscal_dt, DateStrfDay)
    except ValueError:
        task_logger.error('fiscal_date parameter error!')
        sys.exit()
    try:
        main(fiscal_dt, execute_lobs)
    except ErrorExcept as e:
        task_logger.error(e)
        sys.exit(0)
    except Exception as e:
        task_logger.error(e)
        sys.exit(0)
