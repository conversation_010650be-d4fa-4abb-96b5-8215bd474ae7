########## must at head ############
import sys
import os

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(SCRIPT_DIR))
##########      end     ############

from datetime import datetime
from util.const import DateFormat
from service.cpf_sell_in_demand_service import sell_in_demand_with_tags

# 0 0 22 ? * 2 ENV=dev python3 task/task_sell_in_demand_with_tags.py
# 0 0 22 ? * 2 ENV=prod python3 task/task_sell_in_demand_with_tags.py

if __name__ == "__main__":
    if len(sys.argv) >= 2:
        kabob_date = sys.argv[1]
    else:
        kabob_date = datetime.now().strftime(DateFormat)
    sell_in_demand_with_tags(kabob_date)
