########## must at head ############
import sys
import os

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(SCRIPT_DIR))
##########      end     ############

from datetime import datetime
from util.const import DateFormat, StrFiscalQtrWeekName
from data.fiscal_year_week import FiscalYearWeek
from util.template_email_sender import TemplateEmail
from data.cpf_sell_in_demand_data import AppFastaAllocationDemandSubmissionMonoWi



def check_mono_not_exceed_sales_input(fiscal_dt):
    week_info = FiscalYearWeek.get_week_by_date(fiscal_dt)
    num = AppFastaAllocationDemandSubmissionMonoWi.get_sell_in_demand_count(week_info[StrFiscalQtrWeekName],
                                                                        'iPad', None,
                                                                        exceed_sales_input='N')
    if num > 0:
        TemplateEmail().sell_in_demand_12(week_info[StrFiscalQtrWeekName])
        return True
    else:
        return False


if __name__ == "__main__":
    if len(sys.argv) >= 2:
        kabob_date = sys.argv[1]
    else:
        kabob_date = datetime.now().strftime(DateFormat)
    check_mono_not_exceed_sales_input(kabob_date)
