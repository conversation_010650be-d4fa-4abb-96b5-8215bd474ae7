########## must at head ############
import sys
import os

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(SCRIPT_DIR))
##########      end     ############

from util.conf import task_logger
from service.common import xthread
from util.const import *
from util.send_email import send_email
from util.fast_lite_base import *
from data.fast_lite_forecast_data import AppFastForecastMonoAdvanceWa, NewAppFastForecastTrmSoWa,AppFastForecastMultiVersion, FastCPFForecastWeekList
from data.forecast_data import AppFastForecastMonoAdvanceWaRaw, AppFastForecastTrmSoWa


### 说明
# ENV=dev py3 task/fast_cpf_generate_forecast_multi_version.py 1 'FY23Q2W1'
# ENV=prod py3 task/fast_cpf_generate_forecast_multi_version.py 1 'FY23Q2W1'


# 同步原表数据
def sync_raw_table_data(week_date: str, version: int):
    if version == FORECAST_FIRST_VERSION:
        # 同步第一版本原始数据
        first_count_raw = NewAppFastForecastTrmSoWa.check_current_week_data(week_date)
        if first_count_raw == 0:
            first_version_raw_data = AppFastForecastTrmSoWa.get_data_by_week(week_date)
            first_version_data = [dict(item) for item in first_version_raw_data]
            NewAppFastForecastTrmSoWa.bulk_save(first_version_data)
    
    elif version == FORECAST_SECOND_VERSION:
        # 同步第二版本原始数据
        count_raw = AppFastForecastMonoAdvanceWa.check_current_week_data(week_date)
        if count_raw == 0:
            week_raw_data = AppFastForecastMonoAdvanceWaRaw.get_version2_data_by_week(week_date)
            AppFastForecastMonoAdvanceWa.bulk_save(week_raw_data)


# 同步转换表数据
def sync_data_by_week_version(week_date: str, version: int):
    if version == FORECAST_FIRST_VERSION:
        NewAppFastForecastTrmSoWa.gen_version1_data_by_week(week_date, version)
    elif version == FORECAST_SECOND_VERSION:
        AppFastForecastMonoAdvanceWa.gen_version2_data_by_week(week_date, version)


# 插入列表页中对应的周信息
def insert_into_forecast_week_list(week_date: str, version: int):
    if version == FORECAST_FIRST_VERSION:
        fiscal_year_week = FiscalYearWeek.get_fiscal_week_year_by_week_date(week_date)
        if fiscal_year_week != 0:
            fcst = FastCPFForecastWeekList(week_date, fiscal_year_week)
            fcst.insert_week_data()
        

# 检查数据插入情况
def check_insert_data(week_date: str, version: int) -> tuple[int, str]:
    if version == FORECAST_FIRST_VERSION:
        first_raw_count = NewAppFastForecastTrmSoWa.check_current_week_data(week_date)
        first_count = AppFastForecastMultiVersion.check_data_by_week_version(week_date, version)
        return first_raw_count, f"insert {first_raw_count} into {NewAppFastForecastTrmSoWa.__tablename__}. {DOUBLE_LINE_FEED}" + \
            f"insert {first_count} into {AppFastForecastMultiVersion.__tablename__} version {version}. {DOUBLE_LINE_FEED}"
    elif version == FORECAST_SECOND_VERSION:
        second_raw_count = AppFastForecastMonoAdvanceWa.check_current_week_data(week_date)
        second_count = AppFastForecastMultiVersion.check_data_by_week_version(week_date, version)
        return second_raw_count, f"insert {second_raw_count} into {AppFastForecastMonoAdvanceWa.__tablename__}. {DOUBLE_LINE_FEED}" + \
            f"insert {second_count} into {AppFastForecastMultiVersion.__tablename__} version {version}. {DOUBLE_LINE_FEED}"


# 异步发送正常/异常邮件
def async_send_email(subject: str, content: str, file_paths: list=None):
    t = xthread(send_email, [subject, content, file_paths])
    t.start()


# 设置指定log的level
def set_logger_level(logger_name: str, logger_level: int):
    logger = logging.getLogger(logger_name)
    logger.setLevel(logger_level)


if __name__ == "__main__":
    task_logger.info('starting...')
    set_logger_level("myapp.sqltime", logging.INFO)
    env_info = f"{StrENV}: {os.getenv(StrENV)}."
    exec_week = ''
    sync_version = 0

    if len(sys.argv) >= 2:
        sync_version = int(sys.argv[1])
    else:
        task_logger.error('Please input sync version parameter!')
        sys.exit()
        
    if len(sys.argv) >= 3:
        exec_week = sys.argv[2]
    else:
        ret_datetime = datetime.now()
        kabob_date = ret_datetime.strftime('%Y-%m-%d')
        exec_week = FiscalYearWeek.get_fiscal_qtr_week_name_by_date(kabob_date)
    
    sync_raw_table_data(exec_week, sync_version)
    sync_data_by_week_version(exec_week, sync_version)
    count, content = check_insert_data(exec_week, sync_version)
    
    # 同步数据成功后,向列表页插入数据
    if count > 0:
        insert_into_forecast_week_list(exec_week, sync_version)

    if count == 0:
        subject = f"FATAL!!! {env_info} Forecast Lite CP&F Multi Version Data Sync Report."
    else:
        subject = f"SUCCESS! {env_info} Forecast Lite CP&F Multi Version Data Sync Report."
    async_send_email(subject, f"{exec_week}:{DOUBLE_LINE_FEED}"+content)
    
    task_logger.info(content)
    task_logger.info('done.')
