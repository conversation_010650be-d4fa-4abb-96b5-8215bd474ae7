########## must at head ############
import sys
import os

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(SCRIPT_DIR))
##########      end     ############

from datetime import datetime
from data.fiscal_year_week import FiscalYearWeek
from util.const import *
from util.conf import task_logger
from service.rtm_demand_adjustment_service import get_demand_adjustment_template_file_service


def generate_demand_adjustment_template_file(fiscal_dt, rtm, lob='iPad'):
    if not fiscal_dt or not rtm:
        return ''
    week_info = FiscalYearWeek.get_week_by_date(fiscal_dt)
    _, file_name = get_demand_adjustment_template_file_service(rtm, week_info[StrFiscalWeekYear], week_info[StrFiscalQtrWeekName], lob)
    return f'generate {file_name}.'


if __name__ == "__main__":
    rtm = ''
    if len(sys.argv) >= 2:
        rtm = sys.argv[1]
    if not rtm:
        task_logger.info(f"please specify rtm.")
        exit(1)
    
    if len(sys.argv) >= 3:
        fiscal_dt = sys.argv[2]
    if not fiscal_dt:
        fiscal_dt = datetime.now().strftime(DateFormat)
    
    generate_demand_adjustment_template_file(fiscal_dt, rtm)
