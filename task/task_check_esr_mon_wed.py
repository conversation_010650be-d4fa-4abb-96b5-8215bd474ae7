########## must at head ############
import sys
import os

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(SCRIPT_DIR))
##########      end     ############

from datetime import datetime
from data.fiscal_year_week import FiscalYearWeek
from util.template_email_sender import TemplateEmail
from data.allocation_prepare_gc_dmp_data import AppFastTaskStatusWi
from util.const import *
from util.conf import *

'''
周一/三 17:30 检查esr状态
周二08:30 检查esr状态
'''

def check_esr_status(task_type, fiscal_dt):
    week_info = FiscalYearWeek.get_week_by_date(fiscal_dt)
    ret = False
    if task_type == '1':
        ret = AppFastTaskStatusWi.check_task_status(week_info[StrFiscalWeekYear], 'app_fast_esr_wi')
        if not ret:
            TemplateEmail().sales_input_1(week_info[StrFiscalQtrWeekName])
            for rtm in AllocationRTMList:
                TemplateEmail().sales_input_2(week_info[StrFiscalQtrWeekName], rtm)
    elif task_type == '2':
        '''周二09:15ESR未ready，通知CP&F'''
        ret = AppFastTaskStatusWi.check_task_status(week_info[StrFiscalWeekYear], 'app_fast_esr_tue_am_wi')
        if not ret:
            TemplateEmail().sales_input_25(week_info[StrFiscalQtrWeekName])
    elif task_type == '3':
        ret = AppFastTaskStatusWi.check_task_status(week_info[StrFiscalWeekYear], 'app_fast_esr_wed_wi')
        if not ret:
            TemplateEmail().adjustment_14(week_info[StrFiscalQtrWeekName])
            for rtm in AllocationRTMList:
                TemplateEmail().adjustment_15(week_info[StrFiscalQtrWeekName], rtm)
        
    return ret
        

if __name__ == "__main__":
    task_type = ''
    if len(sys.argv) >= 2:
        task_type = sys.argv[1]
    if not task_type:
        task_logger.info('please specify task type.')
        exit(1)

    if len(sys.argv) >= 3:
        fiscal_dt = sys.argv[2]
    if not fiscal_dt:
        fiscal_dt =  datetime.now().strftime(DateFormat)
    
    check_esr_status(task_type, fiscal_dt)

