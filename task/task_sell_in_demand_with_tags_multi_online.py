########## must at head ############
import sys
import os

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(SCRIPT_DIR))
##########      end     ############

from datetime import datetime
from util.const import *
from data.fiscal_year_week import FiscalYearWeek
from service.cpf_sell_in_demand_service import regenerate_multi_online_demand_with_tags_file


def task_regenerate_multi_online_demand_with_tags_file(fiscal_dt: str):
    if not fiscal_dt:
        raise ErrorExcept(ErrCode.Param, 'please specify fiscal_dt')
    week_info = FiscalYearWeek.get_week_by_date(fiscal_dt)
    fiscal_qtr_week_name = week_info[StrFiscalQtrWeekName]
    fiscal_week_year = week_info[StrFiscalWeekYear]
    content = ''
    for rtm in [AllocationRTM.Multi, AllocationRTM.Online]:
        content += regenerate_multi_online_demand_with_tags_file(fiscal_week_year, rtm)
    if not content:
        content = 'No need to regenerate files'
    return f'{fiscal_qtr_week_name}: {DOUBLE_LINE_FEED}{content}'


if __name__ == "__main__":
    if len(sys.argv) >= 2:
        kabob_date = sys.argv[1]
    else:
        kabob_date = datetime.now().strftime(DateFormat)
    task_regenerate_multi_online_demand_with_tags_file(kabob_date)
