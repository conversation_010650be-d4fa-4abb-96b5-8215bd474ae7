########## must at head ############
import sys
import os

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(SCRIPT_DIR))
##########      end     ############

from datetime import datetime
from data.allocation_prepare_data import TblAllocationPrepare
from data.fiscal_year_week import FiscalYearWeek
from util.const import *
from util.conf import task_logger
from util.cpf_const import FirstPhaseErrorProcess


def change_status_by_week_at_10_clock(fiscal_dt):
    if not fiscal_dt:
        return ''
    week_info = FiscalYearWeek.get_week_by_date(fiscal_dt)
    ret = TblAllocationPrepare.find_by_fiscal_week_year(week_info[StrFiscalWeekYear])
    changed_rtm = []
    for item in ret:
        if item.upload_status == str(RTMSalesInputUploadStatus.WaitingToUpload):
            TblAllocationPrepare.update_data_by_id(item.id, {"upload_status": RTMSalesInputUploadStatus.NotUpload})
            changed_rtm.append(item.rtm)
    return changed_rtm


def change_status_by_week_at_16_clock(fiscal_dt):
    if not fiscal_dt:
        return ''
    week_info = FiscalYearWeek.get_week_by_date(fiscal_dt)
    ret = TblAllocationPrepare.find_by_fiscal_week_year(week_info[StrFiscalWeekYear])
    changed_rtm = []
    for item in ret:
        if item.upload_status in [str(RTMSalesInputUploadStatus.WaitingToUpload),
                                  str(RTMSalesInputUploadStatus.Uploaded),
                                  str(RTMSalesInputUploadStatus.NeedToReupload),
                                  str(RTMSalesInputUploadStatus.Received)]:
            TblAllocationPrepare.update_data_by_id(item.id, {"upload_status": RTMSalesInputUploadStatus.Uncompleted})
            changed_rtm.append(item.rtm)
        elif item.upload_status == str(RTMSalesInputUploadStatus.Completed) and (item.first_phase_error and not item.first_phase_error_process):
            TblAllocationPrepare.update_data_by_id(item.id, {"upload_status": RTMSalesInputUploadStatus.Uncompleted,
                                                             "first_phase_error_process": FirstPhaseErrorProcess.Timeout})
            changed_rtm.append(item.rtm)
    return changed_rtm
    

if __name__ == "__main__":
    task_type = ''
    if len(sys.argv) >= 2:
        task_type = sys.argv[1]
    if not task_type:
        task_logger.info(f"please specify task_type: 10,16.")
        exit(1)
    
    if len(sys.argv) >= 3:
        fiscal_dt = sys.argv[2]
    if not fiscal_dt:
        fiscal_dt = datetime.now().strftime(DateFormat)
    
    if task_type == '10':
        change_status_by_week_at_10_clock(fiscal_dt)
    elif task_type == '16':
        change_status_by_week_at_16_clock(fiscal_dt)
