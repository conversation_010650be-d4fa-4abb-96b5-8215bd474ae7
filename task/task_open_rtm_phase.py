########## must at head ############
import sys
import os

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(SCRIPT_DIR))
##########      end     ############

from datetime import datetime
from data.allocation_prepare_data import TblAllocationPrepare
from data.fiscal_year_week import FiscalYearWeek
from util.const import *
from util.rtm_demand_adjustment_const import *
from util.conf import task_logger


def open_phase_by_week_rtm(fiscal_dt, rtm, phase):
    if not fiscal_dt or not rtm or not phase:
        return ''
    week_info = FiscalYearWeek.get_week_by_date(fiscal_dt)
    ret = TblAllocationPrepare.find_by_fiscal_week_year_and_rtm(week_info[StrFiscalWeekYear], rtm)
    if phase == str(RTMAllocationPhase.DemandSubmission) and ret.operate_phase == str(RTMAllocationPhase.SalesInput):
        # 开启第二阶段, 不需要更改状态
        update_data = {"operate_phase": str(RTMAllocationPhase.DemandSubmission)}
        TblAllocationPrepare.update_data_by_id(ret.id, update_data)
        return f'{rtm} open second phase. update_data: {update_data}'
    elif phase == str(RTMAllocationPhase.DemandAdjustment) and ret.operate_phase == str(RTMAllocationPhase.DemandSubmission):
        # 开启第三阶段
        update_data = {"operate_phase": str(RTMAllocationPhase.DemandAdjustment),
                       "third_phase_status": RTMDemandAdjustmentUploadStatus.WaitingToUpload}
        TblAllocationPrepare.update_data_by_id(ret.id, update_data)
        return f'{rtm} open third phase. update_data: {update_data}'
    return f'{rtm} no phase to open.'


if __name__ == "__main__":
    rtm = ''
    if len(sys.argv) >= 2:
        rtm = sys.argv[1]
    if not rtm:
        task_logger.info(f"please specify rtm.")
        exit(1)
    phase = ''
    if len(sys.argv) >= 3:
        phase = sys.argv[2]
    if not phase:
        task_logger.info(f"please specify phase.")
        exit(1)
    
    if len(sys.argv) >= 4:
        fiscal_dt = sys.argv[3]
    if not fiscal_dt:
        fiscal_dt = datetime.now().strftime(DateFormat)
    
    open_phase_by_week_rtm(fiscal_dt, rtm, phase)
