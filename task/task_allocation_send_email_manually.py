########## must at head ############
import sys
import os

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(SCRIPT_DIR))
##########      end     ############

from datetime import datetime
from util.const import *
from util.conf import *
from data.fiscal_year_week import FiscalYearWeek
from util.template_email_sender import TemplateEmail
from util.cpf_util import get_mail_navigate_url, get_cpf_mail_navigate_url, get_navigate_url_list_page
from data.allocation_prepare_data import TblAllocationPrepare
from data.allocation_cpf_lob_data import TblAllocationCpfLob


class EmailType:
    SalesInput21 = 1
    SalesInput22 = 2
    Adjustment23 = 3
    Adjustment24 = 4


def send_email_manually(email_type, fiscal_dt):
    week_info = FiscalYearWeek.get_week_by_date(fiscal_dt)
    if email_type == str(EmailType.SalesInput21):
        navigate_url = get_navigate_url_list_page('cpf')
        TemplateEmail().sales_input_21(week_info[StrFiscalQtrWeekName], navigate_url)
    elif email_type == str(EmailType.SalesInput22):
        for rtm in AllocationRTMList:
            rtm_record = TblAllocationPrepare.find_by_fiscal_week_year_and_rtm(week_info[StrFiscalWeekYear], rtm)
            navigate_url = get_navigate_url_list_page('rtm')
            TemplateEmail().sales_input_22(week_info[StrFiscalQtrWeekName], rtm, navigate_url)
    elif email_type == str(EmailType.Adjustment23):
        cpf_record = TblAllocationCpfLob.get_by_week_lob(week_info[StrFiscalWeekYear], 'iPad')
        if len(cpf_record) == 0:
            return 'cpf no record'
        navigate_url = get_cpf_mail_navigate_url(
            cpf_record[0].id, week_info[StrFiscalQtrWeekName], week_info[StrFiscalWeekYear], 'iPad')
        TemplateEmail().adjustment_23(week_info[StrFiscalQtrWeekName], navigate_url)
    elif email_type == str(EmailType.Adjustment24):
        for rtm in AllocationRTMList:
            rtm_record = TblAllocationPrepare.find_by_fiscal_week_year_and_rtm(week_info[StrFiscalWeekYear], rtm)
            navigate_url = get_mail_navigate_url(rtm_record.id, rtm, 
                                                 week_info[StrFiscalQtrWeekName], week_info[StrFiscalWeekYear],
                                                 RTMAllocationPhase.SalesInput)
            TemplateEmail().adjustment_24(week_info[StrFiscalQtrWeekName], rtm, navigate_url)
    else:
        return 'please correct email_report type, eg. 1,2,3,4'
    return 'successfully send email_report.'


if __name__ == "__main__":
    email_type = ''
    if len(sys.argv) >= 2:
        email_type = sys.argv[1]
    if not email_type:
        task_logger.info('please specify email_report type.')
        exit(1)
    fiscal_dt = ''
    if len(sys.argv) >= 3:
        fiscal_dt = sys.argv[2]
    if not fiscal_dt:
        fiscal_dt =  datetime.now().strftime(DateFormat)
