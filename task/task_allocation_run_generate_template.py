########## must at head ############
import sys
import os

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(SCRIPT_DIR))
##########      end     ############

from datetime import datetime
from util.const import DateFormat
from service.allocation_cpf_run_service import generate_supply_data_template_file_service


def generate_allocation_run_template(fiscal_dt):
    for i in range(3):
        generate_supply_data_template_file_service(i, fiscal_dt)
    

if __name__ == "__main__":
    if len(sys.argv) >= 2:
        kabob_date = sys.argv[1]
    else:
        kabob_date = datetime.now().strftime(DateFormat)
    generate_allocation_run_template(kabob_date)
