########## must at head ############
import sys
import os

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(SCRIPT_DIR))
##########      end     ############

import pandas as pd
from datetime import datetime
import uuid
from data.fiscal_year_week import FiscalYearWeek
from util.const import *
from util.conf import task_logger
from service.allocation_prepare_service import generate_sales_input_template_file,\
    get_open_backlog_less_than_monday, transfer_file_path, get_template_dataframe
from util.template_email_sender import TemplateEmail
from util.cpf_util import get_mail_navigate_url, get_cpf_mail_navigate_url, cpf_merge_file_path,\
    get_navigate_url_list_page
from data.allocation_prepare_data import TblAllocationPrepare
from data.allocation_cpf_lob_data import TblAllocationCpfLob


def check_open_backlog_less_than_monday(fiscal_dt):
    '''检查周二重新刷新ESR后，template是否有open_backlog小的情况'''
    if not fiscal_dt:
        return ''
    content = ""
    week_info = FiscalYearWeek.get_week_by_date(fiscal_dt)
    less_rtm_list = []
    # 检查各个RTM的状态，分别给对应RTM发邮件
    for rtm in AllocationRTMList:
        ret_less = get_open_backlog_less_than_monday(rtm, week_info[StrFiscalWeekYear])
        if ret_less:
            rtm_record = TblAllocationPrepare.find_by_fiscal_week_year_and_rtm(week_info[StrFiscalWeekYear], rtm)
            if rtm_record.upload_status \
                and int(rtm_record.upload_status) in [RTMSalesInputUploadStatus.Uploaded,
                                                      RTMSalesInputUploadStatus.Received,
                                                      RTMSalesInputUploadStatus.Completed]:
                is_less = False
                # 需要比对Sales input Qty Total = 该Sold-to该MPN CW+1～+4下所有P0/P1/P2的Sales input Qty之和，空值按0处理
                checked_file_path = ""
                if rtm_record.upload_file_path_cpf \
                    and rtm_record.upload_status == str(RTMSalesInputUploadStatus.Completed):
                    checked_file_path = rtm_record.upload_file_path_cpf
                elif rtm_record.upload_file_path:
                    checked_file_path = rtm_record.upload_file_path
                
                if checked_file_path:
                    upload_data = pd.read_excel(transfer_file_path(checked_file_path))
                    for item in ret_less:
                        # 指定需要加和的列
                        cols_to_sum = ['Sales input Qty for CW+1',
                                       'Sales input Qty for CW+2',
                                       'Sales input Qty for CW+3',
                                       'Sales input Qty for CW+4']
                        df = upload_data[(upload_data['Customer Sold-to ID'] == item[0]) & (upload_data['MPN / Apple Part #'] == item[1])]
                        # 3行4列加和
                        if item[2] < df[cols_to_sum].sum().sum():
                            is_less = True
                            break
                    
                if is_less:
                    less_rtm_list.append(is_less)
                    if not rtm_record.first_phase_error:
                        # 记录error状态
                        TblAllocationPrepare.update_data_by_id(rtm_record.id, {'first_phase_error': 1})
                
                    # 发送邮件
                    navigate_url = get_mail_navigate_url(rtm_record.id, rtm, 
                                                        week_info[StrFiscalQtrWeekName], week_info[StrFiscalWeekYear],
                                                        RTMAllocationPhase.SalesInput)
                    content += f"rtm: {rtm}, open_backlog less than monday in {week_info[StrFiscalQtrWeekName]}{DOUBLE_LINE_FEED}"
                    TemplateEmail().sales_input_26(week_info[StrFiscalQtrWeekName], rtm, navigate_url)
    
    # 如果有任一个RTM出现少的情况，给CP&F发邮件
    if less_rtm_list:
        cpf_record = TblAllocationCpfLob.get_by_week_lob(week_info[StrFiscalWeekYear], 'iPad')
        if len(cpf_record) == 0:
            return 'cpf no record'
        navigate_url = get_cpf_mail_navigate_url(
            cpf_record[0].id, week_info[StrFiscalQtrWeekName], week_info[StrFiscalWeekYear], 'iPad')
        TemplateEmail().sales_input_26(week_info[StrFiscalQtrWeekName], StrRTMCPF, navigate_url)
    if not content:
        content = f'open_backlog data is normal in {week_info[StrFiscalQtrWeekName]}{DOUBLE_LINE_FEED}'
    return content


def merge_file_left(df_template_fixed: pd.DataFrame,
                    df_source: pd.DataFrame,
                    merge_on: list,
                    customize_columns: list) -> pd.DataFrame:
    '''使用模版数据将上传的RTM文件/CPF文件按照模版中不变的部分进行覆盖，如果有增加的行，需要将指定列置为0'''
    target_dataframe = pd.merge(df_template_fixed,
                                df_source,
                                on=merge_on,
                                how='left')
    # 将指定列空值置为0
    target_dataframe[customize_columns] = target_dataframe[customize_columns].fillna(0)
    return target_dataframe


def generate_new_file(df_template, df_upload):
    """"按照最新模版的内容来刷新文件中固定的部分"""
    df_template_fixed = df_template[TemplateFileHeader[:21]]
    # by sold-to by mpn by priority
    merge_on = ['MPN / Apple Part #', 'Customer Sold-to ID', 'Priority']
    customize_columns = ['Sales input Qty for CW+1',
                         'Sales input Qty for CW+2',
                         'Sales input Qty for CW+3',
                         'Sales input Qty for CW+4']
    # 上传文件中按照merge_on字段加上用户自定义填写的列内容
    df_source = df_upload[merge_on+TemplateFileHeader[21:]]
    df = merge_file_left(df_template_fixed, df_source, merge_on, customize_columns)
    # 生成新的文件
    new_file_name = f'{uuid.uuid4().hex}.xlsx'
    new_file_path = cpf_merge_file_path(new_file_name)
    df.to_excel(new_file_path, index=False)
    return f'/file/storage/{new_file_name}'
    

def refresh_upload_file(rtm: str, fiscal_week_year: int):
    rtm_record = TblAllocationPrepare.find_by_fiscal_week_year_and_rtm(fiscal_week_year, rtm)
    
    # 还未上传则没有文件需要刷新
    if rtm_record.upload_status == str(RTMSalesInputUploadStatus.NotUpload):
        return ''
    
    template_dataframe = get_template_dataframe(rtm, fiscal_week_year)
    update_mapping = {}
    # 刷新rtm文件
    if rtm_record.upload_file_path:
        rtm_dataframe = pd.read_excel(transfer_file_path(rtm_record.upload_file_path))
        update_mapping['upload_file_path'] = generate_new_file(template_dataframe, rtm_dataframe)
    
    # 刷新cpf文件
    if rtm_record.upload_file_path_cpf:
        cpf_dataframe = pd.read_excel(transfer_file_path(rtm_record.upload_file_path_cpf))
        update_mapping['upload_file_path_cpf'] = generate_new_file(template_dataframe, cpf_dataframe)
    
    if update_mapping:
        TblAllocationPrepare.update_data_by_id(rtm_record.id, update_mapping)
        return f'{rtm_record.rtm} update {update_mapping} in {rtm_record.fiscal_qtr_week_name}{DOUBLE_LINE_FEED}'
    else:
        return ''


def cover_upload_file_by_template_file_all_rtm(fiscal_dt):
    '''使用新模版覆盖所有RTM中上传的文件'''
    if not fiscal_dt:
        return ''
    content = ""
    week_info = FiscalYearWeek.get_week_by_date(fiscal_dt)
    for rtm in AllocationRTM.get_all_rtms():
        content += refresh_upload_file(rtm, week_info[StrFiscalWeekYear])
    return content


def generate_sales_input_template_file_all_rtm(fiscal_dt):
    '''生成所有RTM的Sales Input模版文件'''
    if not fiscal_dt:
        return ''
    content = ""
    week_info = FiscalYearWeek.get_week_by_date(fiscal_dt)
    for rtm in AllocationRTM.get_all_rtms():
        _, file_name = generate_sales_input_template_file(rtm, week_info[StrFiscalWeekYear], week_info[StrFiscalQtrWeekName])
        content += f"rtm: {rtm}, generate {file_name} in {week_info[StrFiscalQtrWeekName]}.{DOUBLE_LINE_FEED}"
    return content
        

def generate_sales_input_template_file_function(fiscal_dt, rtm):
    '''生成指定RTM的Sales Input模版文件'''
    if not fiscal_dt or not rtm:
        return ''
    week_info = FiscalYearWeek.get_week_by_date(fiscal_dt)
    _, file_name = generate_sales_input_template_file(rtm, week_info[StrFiscalWeekYear], week_info[StrFiscalQtrWeekName])
    return f'generate {file_name} in {week_info[StrFiscalQtrWeekName]}.'


def send_email_after_system_open(fiscal_dt):
    if not fiscal_dt:
        return ''
    content = ""
    week_info = FiscalYearWeek.get_week_by_date(fiscal_dt)
    rtm_navigate_url = get_navigate_url_list_page('rtm')
    for rtm in AllocationRTM.get_all_rtms():
        # 通知对应RTM入口已打开
        TemplateEmail().sales_input_27(week_info[StrFiscalQtrWeekName], rtm, rtm_navigate_url)
        
        content += f"notice {rtm} in {week_info[StrFiscalQtrWeekName]}.{DOUBLE_LINE_FEED}"
    
    # 通知CPF入口已打开
    cpf_record = TblAllocationCpfLob.get_by_week_lob(week_info[StrFiscalWeekYear], 'iPad')
    if len(cpf_record) == 0:
        content += 'cpf no record, notice error'
    else:
        cpf_navigate_url = get_cpf_mail_navigate_url(
            cpf_record[0].id, week_info[StrFiscalQtrWeekName], week_info[StrFiscalWeekYear], 'iPad')
        TemplateEmail().sales_input_29(week_info[StrFiscalQtrWeekName], StrRTMCPF, cpf_navigate_url)
        content += f"notice {StrRTMCPF} in {week_info[StrFiscalQtrWeekName]}.{DOUBLE_LINE_FEED}"
    
    return content


def send_email_after_refresh_esr_on_tuesday(fiscal_dt):
    if not fiscal_dt:
        return ''
    content = ""
    week_info = FiscalYearWeek.get_week_by_date(fiscal_dt)
    rtm_navigate_url = get_navigate_url_list_page('rtm')
    for rtm in AllocationRTM.get_all_rtms():
        # 通知各个RTM周二ESR已重新刷新完毕
        TemplateEmail().sales_input_28(week_info[StrFiscalQtrWeekName], rtm, rtm_navigate_url)
        
        content += f"notice {rtm} in {week_info[StrFiscalQtrWeekName]}.{DOUBLE_LINE_FEED}"
    
    # 通知CPF周二ESR已重新刷新完毕
    cpf_record = TblAllocationCpfLob.get_by_week_lob(week_info[StrFiscalWeekYear], 'iPad')
    if len(cpf_record) == 0:
        content += 'cpf no record, notice error'
    else:
        cpf_navigate_url = get_cpf_mail_navigate_url(
            cpf_record[0].id, week_info[StrFiscalQtrWeekName], week_info[StrFiscalWeekYear], 'iPad')
        TemplateEmail().sales_input_30(week_info[StrFiscalQtrWeekName], StrRTMCPF, cpf_navigate_url)
        content += f"notice {StrRTMCPF} in {week_info[StrFiscalQtrWeekName]}.{DOUBLE_LINE_FEED}"

    return content


if __name__ == "__main__":
    rtm = ''
    if len(sys.argv) >= 2:
        rtm = sys.argv[1]
    if not rtm:
        task_logger.info(f"please specify rtm.")
        exit(1)
    
    if len(sys.argv) >= 3:
        fiscal_dt = sys.argv[2]
    if not fiscal_dt:
        fiscal_dt = datetime.now().strftime(DateFormat)
    
    generate_sales_input_template_file_function(fiscal_dt, rtm)
