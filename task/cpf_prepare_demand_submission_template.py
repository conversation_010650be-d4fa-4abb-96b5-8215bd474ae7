# ========== must at head ==========
import sys
import os

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(SCRIPT_DIR))
# ========== end ==========

from util.const import *
from util.file_util import save_df_to_md5_name
from data.fiscal_year_week import FiscalYearWeek
from data.allocation_prepare_gc_dmp_data import *
from data.allocation_prepare_data import TblAllocationPrepareFile


def get_data_by_lob(rtm, fiscal_obj: FiscalYearWeek, lob):
    fiscal_week_year = fiscal_obj.fiscal_week_year
    submission_data = AppFastAllocationDemandSubmissionTemplate.get_by_rtm_lob(rtm, fiscal_week_year, lob)
    sub_df = submission_data[list(PrepareSubmissionMonoHeaderDict.values())]
    sub_df.columns = list(PrepareSubmissionMonoHeaderDict.keys())
    # 保存 Excel 文件
    file_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__))) + f"/uploads/allocation/"
    local_file_name = save_df_to_md5_name(sub_df, file_path, 'xlsx')
    file_name = f"{rtm}_Demand_{lob}_{fiscal_obj.fiscal_qtr_week_name}.xlsx".replace('/', '_')
    file_records = TblAllocationPrepareFile.find_by_phase_rtm_category(
        str(RTMAllocationPhase.DemandSubmission), rtm, PrepareSubmissionFileCategory.Template, fiscal_obj.fiscal_week_year
    )
    if len(file_records) == 0:
        file_record = TblAllocationPrepareFile()
        file_record.fiscal_qtr_week_name = fiscal_obj.fiscal_qtr_week_name
        file_record.fiscal_week_year = fiscal_obj.fiscal_week_year
        file_record.rtm = rtm
        file_record.lob = lob
        file_record.operate_phase = str(RTMAllocationPhase.DemandSubmission)
        file_record.upload_status = RTMSalesInputUploadStatus.Received
        file_record.upload_file_version = 0
    else:
        file_record = file_records[0]
        file_record.upload_file_version += 1
    file_record.upload_file_name = file_name
    file_record.upload_file_path = f"/file/storage/{local_file_name}"
    file_record.upload_by = "system"
    file_record.upload_at = datetime.now()
    file_record.update_by = "system"
    file_record.category = PrepareSubmissionFileCategory.Template
    file_record.save()


def main(fiscal_date: str, lob_list: list):
    fiscal_obj = FiscalYearWeek.get_fis_by_date(fiscal_date)
    if fiscal_obj is None:
        raise ErrorExcept(ErrCode.Param, "can't find fiscal day")
    for lob in lob_list:
        for rtm in [
            AllocationRTM.Multi,
            AllocationRTM.Online,
        ]:
            get_data_by_lob(rtm, fiscal_obj, lob)


if __name__ == '__main__':
    task_logger.info('starting...')
    logger = logging.getLogger("myapp.sqltime")
    logger.setLevel(logging.INFO)
    env_info = f"{StrENV}: {os.getenv(StrENV)}."
    exec_week = ''
    fiscal_dt = ''
    execute_lobs = SubmissionExecuteLobs

    if len(sys.argv) >= 2:
        fiscal_dt = sys.argv[1]
    else:
        task_logger.error('Please input fiscal_date parameter!')
        sys.exit()
    try:
        datetime.strptime(fiscal_dt, DateStrfDay)
    except ValueError:
        task_logger.error('fiscal_date parameter error!')
        sys.exit()
    try:
        main(fiscal_dt, execute_lobs)
    except ErrorExcept as e:
        task_logger.error(e)
        sys.exit(0)
    except Exception as e:
        task_logger.error(e)
        sys.exit(0)
