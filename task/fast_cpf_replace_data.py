########## must at head ############
import sys
import os

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.dirname(SCRIPT_DIR))
##########      end     ############

from sqlalchemy import exc
from util.gc_dmp_base import *
from util.conf import task_logger
from util.const import *
from util.send_email import send_email
from util.fast_lite_base import *
from data.fast_lite_data_replace_all import NewAppFastForecastTrmSoWaTemp, LiteNewAppFastForecastTrmSoWaTemp, FastLiteAppFastDemandSummaryWaTemp, DimFastBusinessSoldtoMappingTemp
from data.fiscal_year_week import FiscalYearWeek
from data.forecast_data import AppFastForecastTrmSoWa
from data.cascade_filter_data import DimFastBusinessSoldtoMapping


### 使用说明
# ENV=dev python3 task/fast_cpf_replace_data.py 'FY23Q2W1,FY23Q2W2,FY23Q2W3,FY23Q2W4' All
# ENV=prod python3 task/fast_cpf_replace_data.py 'FY23Q2W1,FY23Q2W2,FY23Q2W3,FY23Q2W4' All
### 重新生成sold-to-mapping数据
# ENV=dev python3 task/fast_cpf_replace_data.py '' sold_to_mapping
# ENV=prod python3 task/fast_cpf_replace_data.py '' sold_to_mapping


def init_temp_tables(table_type: str):
    try:
        # 初始化表
        if table_type == 'sold_to_mapping':
            DimFastBusinessSoldtoMappingTemp.__table__.create(fast_lite_engine)
        if table_type == 'All' or table_type == 'forecast':
            NewAppFastForecastTrmSoWaTemp.__table__.create(fast_lite_engine)
            LiteNewAppFastForecastTrmSoWaTemp.__table__.create(fast_lite_engine)
        if table_type == 'All' or table_type == 'demand':
            FastLiteAppFastDemandSummaryWaTemp.__table__.create(fast_lite_engine)
    except exc.OperationalError:
        pass
    
def rename_table(table_type: str):
    ret_datetime = datetime.now()
    current_time = ret_datetime.strftime('%Y%m%d%H%M%S')
    table_list = []
    
    if table_type == 'sold_to_mapping':
        table_list += ['dim_fast_business_soldto_mapping']
    if table_type == 'All' or table_type == 'forecast':
        table_list += ['app_fast_forecast_rtm_so_wa','new_app_fast_forecast_rtm_so_wa']
    if table_type == 'All' or table_type == 'demand':
        table_list += ['app_fast_demand_summary_wa']
    content = ''
    for table_name in table_list:
        bak_table = f'rename table {table_name} to {table_name}_bak_{current_time}'
        new_table = f'rename table {table_name}_temp to {table_name}'
        fast_lite_engine.execute(bak_table)
        task_logger.info(bak_table)
        content += f'{bak_table}{DOUBLE_LINE_FEED}'
        fast_lite_engine.execute(new_table)
        task_logger.info(new_table)
        content += f'{new_table}{DOUBLE_LINE_FEED}'
    return content


def generate_sold_to_mapping():
    # 查询来源表数据
    data_list = DimFastBusinessSoldtoMapping.get_all_data()
    # 整理数据
    results = [dict(item) for item in data_list]
    # 向新表插入数据
    DimFastBusinessSoldtoMappingTemp.bulk_save(results)
    return f"All table: insert new data [ {len(results)} ] to [ {DimFastBusinessSoldtoMappingTemp.__tablename__} ].{DOUBLE_LINE_FEED}"    


# 同步数据侧的forecast表, 按周维度来同步
def sync_cpf_forecast_data(fiscal_qtr_week_name: str):
    forecast_source_content = ''
    if fiscal_qtr_week_name is not None:
        # 查询来源表数据
        data_list = AppFastForecastTrmSoWa.get_data_by_week(fiscal_qtr_week_name)
        # 整合数据
        results = [dict(item) for item in data_list]
        # 向新表插入数据
        NewAppFastForecastTrmSoWaTemp.bulk_save(results)
        forecast_source_content = f"{fiscal_qtr_week_name}: insert new data [ {len(results)} ] to [ {NewAppFastForecastTrmSoWaTemp.__tablename__} ].{DOUBLE_LINE_FEED}"
    return forecast_source_content


# 将同步过来的forecast相关数据, 整合成一张新表的数据, 按周维度
def sync_final_table_by_week(fiscal_qtr_week_name: str):
    forecast_content = ''
    if fiscal_qtr_week_name is not None:
        # 向新表插入数据
        forecast_content = LiteNewAppFastForecastTrmSoWaTemp.gen_data_by_week(fiscal_qtr_week_name)
    return forecast_content


# 同步数据侧4个demand表, 并生成ent/edu sold-to/sku 维度的数据, 按周维度
def gen_demand_data(fiscal_qtr_week_name: str):
    demand_content = ''
    if fiscal_qtr_week_name is not None:
        online_content = FastLiteAppFastDemandSummaryWaTemp.demand_query_and_insert_data('app_fast_demand_online_summary_wa', fiscal_qtr_week_name)
        carrier_content = FastLiteAppFastDemandSummaryWaTemp.demand_query_and_insert_data('app_fast_demand_carrier_summary_wa', fiscal_qtr_week_name)
        mono_content = FastLiteAppFastDemandSummaryWaTemp.demand_query_and_insert_data('app_fast_demand_mono_summary_wa', fiscal_qtr_week_name)
        multi_content = FastLiteAppFastDemandSummaryWaTemp.demand_query_and_insert_data('app_fast_demand_multi_summary_wa', fiscal_qtr_week_name)
        other_rtm_content = FastLiteAppFastDemandSummaryWaTemp.demand_gen_ent_edu_sold_to_data(fiscal_qtr_week_name)
        demand_content = online_content+carrier_content+mono_content+multi_content+other_rtm_content
    return demand_content


def sync_cpf_forecast_rtm_data(sync_week: str, table_type: str = 'All' or 'forecast' or 'demand' or 'sold_to_mapping'):
    if sync_week is None or sync_week == '':
        ret_datetime = datetime.now()
        kabob_date = ret_datetime.strftime('%Y-%m-%d')
        fiscal_qtr_week_name = FiscalYearWeek.get_fiscal_qtr_week_name_by_date(kabob_date)
    else:
        fiscal_qtr_week_name = sync_week
    
    content = '' 
    content1 = '' 
    content2 = '' 
    content3 = '' 

    if table_type == 'sold_to_mapping':
        content = generate_sold_to_mapping()
        task_logger.info(content)
    if table_type == 'All' or table_type == 'forecast':
        content1 = sync_cpf_forecast_data(fiscal_qtr_week_name)
        task_logger.info(content1)
        content2 = sync_final_table_by_week(fiscal_qtr_week_name)
        task_logger.info(content2)
    if table_type == 'All' or table_type == 'demand':
        content3 = gen_demand_data(fiscal_qtr_week_name)
        task_logger.info(content3)
    return content+content1+content2+content3


if __name__ == "__main__":
    task_logger.info('starting...')
    env_info = f"{StrENV}: {os.getenv(StrENV)}."
    mail_content = ''
    exec_week = ''
    table_type = 'All'

    if len(sys.argv) >= 2:
        exec_week = sys.argv[1]
    if len(sys.argv) >= 3:
        table_type = sys.argv[2]
    
    exec_week_list = exec_week.split(',')

    # 初始化表
    init_temp_tables(table_type)
    
    # 插入数据
    for week in exec_week_list:
        mail_content += sync_cpf_forecast_rtm_data(week, table_type)
    
    # 重命名表
    mail_content += rename_table(table_type)
    
    # 所有的结果发送邮件
    send_email(f"{env_info} Forecast Lite CP&F Data Replace Data Report.", mail_content)
    task_logger.info('done.')
    