
from util.conf import logger

from werkzeug import Request

from util.util import env_dev


class AppleConnectMiddleware:
    '''
    Apple Connect middleware
    '''

    def __init__(self, app):
        self.app = app

    def __call__(self, environ, start_response):
        req = Request(environ)
        self._process_request(req)
        return self.app(environ, start_response)

    @staticmethod
    def _process_request(request):
        # logger.info(request.__dict__)
        # Get user info from apple connect related headers
        '''
        prs_id = request.headers.get('Shield-Ds-Prsid')
        if prs_id is None:
            # Not Shield environment
            return
        employee_id = request.headers.get('Shield-Ds-Employeeid')
        first_name = request.headers.get('Shield-Ds-Firstname')
        last_name = request.headers.get('Shield-Ds-Lastname')
        #name = first_name + " " + last_name
        nick_name = request.headers.get('Shield-Ds-Nickname')
        email_report = request.headers.get('Shield-Ds-Emailaddress')

        groupstr = request.headers.get('Shield-Ds-Allgroups')
        groups = str(groupstr).split(';')
        admin_group = GroupExpertAdmin
        if env_dev():
            admin_group = GroupExpertAdminDev
        # admin
        is_admin = 0
        if admin_group in groups:
            is_admin = 1
        # Create a user info record if there is no record in the database
        '''


class InvalidAPIUsage(Exception):
    status_code = 400

    def __init__(self, message, status_code=None, payload=None):
        super().__init__()
        self.message = message
        if status_code is not None:
            self.status_code = status_code
        self.payload = payload

    def to_dict(self):
        rv = dict(self.payload or ())
        rv['message'] = self.message
        return rv